<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>1.5.22.RELEASE</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>

    <groupId>com.dragonsoft.cicadas</groupId>
    <artifactId>cicada-dataCenter-web</artifactId>
    <version>3.6.1.RELEASE</version>
    <packaging>jar</packaging>

    <profiles>
        <profile>
            <id>local</id>
            <properties>
                <profile.active>local</profile.active>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>dev</id>
            <properties>
                <profile.active>dev</profile.active>
            </properties>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <profile.active>test</profile.active>
            </properties>
        </profile>
        <profile>
            <id>pro</id>
            <properties>
                <profile.active>pro</profile.active>
            </properties>
        </profile>
    </profiles>


    <properties>
        <java.version>1.8</java.version>

        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <okhttp.version>4.2.2</okhttp.version>
        <java.version>1.8</java.version>
        <metadata.version>2.5.0-SNAPSHOT</metadata.version>
        <meta.handler.version>1.0.0-SNAPSHOT</meta.handler.version>
        <common.version>1.0</common.version>
        <swagger.version>2.7.0</swagger.version>
        <hibernate.version>4.2.3.Final</hibernate.version>
        <springboot.dubbo.version>2.0.0</springboot.dubbo.version>
        <dfw.version>1.0.0-SNAPSHOT</dfw.version>
        <metadata.res.ddl.version>3.1.1-SNAPSHOT-DC-CD</metadata.res.ddl.version>
        <!--        <spring-framework.version>4.3.16.RELEASE</spring-framework.version>-->
        <itextpdf.version>5.5.13.3</itextpdf.version>
        <freemarker.version>2.3.31</freemarker.version>
    </properties>

    <dependencies>

        <dependency>
            <groupId>com.code.mist-service</groupId>
            <artifactId>published-service-interface</artifactId>
            <version>1.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>meta-res-dml</artifactId>
                    <groupId>com.code.meta</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-netflix-eureka-client</artifactId>
            <version>1.4.7.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
            <version>1.4.7.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.code.meta</groupId>
            <artifactId>metadata-kernel-core</artifactId>
            <version>3.4.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.code.meta</groupId>
            <artifactId>metadata-kernel-provider</artifactId>
            <version>3.4.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-lang3</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.code.meta</groupId>
            <artifactId>metadata-ga-log-security-audit-platform-provider</artifactId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>
        <!--  logback kafka 依赖-->
        <dependency>
            <groupId>com.github.danielwegener</groupId>
            <artifactId>logback-kafka-appender</artifactId>
            <version>0.1.0</version>
        </dependency>

        <dependency>
            <groupId>com.fw.tenon</groupId>
            <artifactId>tenon-fw-util-core</artifactId>
            <version>0.1-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.squareup.retrofit2</groupId>
            <artifactId>retrofit</artifactId>
            <version>2.8.1</version>
        </dependency>

        <dependency>
            <groupId>com.squareup.retrofit2</groupId>
            <artifactId>converter-gson</artifactId>
            <version>2.8.1</version>
        </dependency>

        <dependency>
            <groupId>com.code.common</groupId>
            <artifactId>mist-errcode</artifactId>
            <version>1.0</version>
            <exclusions>
                <exclusion>
                    <artifactId>data-gov-common</artifactId>
                    <groupId>com.dragoninfo</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.github.jsqlparser</groupId>
            <artifactId>jsqlparser</artifactId>
            <version>4.7</version>
        </dependency>

        <dependency>
            <groupId>com.code.mlsql</groupId>
            <artifactId>mlsql-etl-adaptor</artifactId>
            <version>3.3.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.code.common</groupId>
                    <artifactId>logic-dataset-build-interface</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>mist-metadata-interface</artifactId>
                    <groupId>com.code.meta</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mist-metadata-core</artifactId>
                    <groupId>com.code.meta</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mist-context</artifactId>
                    <groupId>com.code.common</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mist-plugin</artifactId>
                    <groupId>com.code.common</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>metadata-res-ddl-interface</artifactId>
                    <groupId>com.code.meta</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>metadata-server-manage-interface</artifactId>
                    <groupId>com.code.meta</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>metadata-server-manage-core</artifactId>
                    <groupId>com.code.meta</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>metadata-datacenter-core</artifactId>
                    <groupId>com.code.meta</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>metadata-datacenter-interface</artifactId>
                    <groupId>com.code.meta</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.code.meta</groupId>
                    <artifactId>metadata-res-dml-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.code.meta</groupId>
                    <artifactId>metadata-res-dml-interface</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>metadata-server-manage-provider</artifactId>
                    <groupId>com.code.meta</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.apache.parquet</groupId>
            <artifactId>parquet-hadoop</artifactId>
            <version>1.9.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.hadoop</groupId>
            <artifactId>hadoop-common</artifactId>
            <version>2.8.5</version>
            <exclusions>
                <exclusion>
                    <artifactId>commons-compress</artifactId>
                    <groupId>org.apache.commons</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>guava</artifactId>
                    <groupId>com.google.guava</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jsp-api</artifactId>
                    <groupId>javax.servlet.jsp</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>snappy-java</artifactId>
                    <groupId>org.xerial.snappy</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>servlet-api</artifactId>
                    <groupId>javax.servlet</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>curator-client</artifactId>
                    <groupId>org.apache.curator</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>curator-framework</artifactId>
                    <groupId>org.apache.curator</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>curator-recipes</artifactId>
                    <groupId>org.apache.curator</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>4.1.2</version>
        </dependency>

        <dependency>
            <groupId>com.code.common</groupId>
            <artifactId>mist-spark-client-interface</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.code.common</groupId>
            <artifactId>logic-dataset-build-provider</artifactId>
            <version>3.1.1-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>metadata-res-dml-core</artifactId>
                    <groupId>com.code.meta</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.68</version>
        </dependency>


        <!--swagger start-->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
            <version>2.9.2</version>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
            <version>2.9.2</version>
        </dependency>
        <!--swagger end-->
        <dependency>
            <groupId>e-iceblue</groupId>
            <artifactId>spire.xls.free</artifactId>
            <version>3.9.1</version>
        </dependency>
        <dependency>
            <groupId>com.dragonsoft.cicadas</groupId>
            <artifactId>dc-plugin-page-pom</artifactId>
            <version>3.0.0-SNAPSHOT</version>
            <type>pom</type>
            <exclusions>
                <exclusion>
                    <groupId>com.code.meta</groupId>
                    <artifactId>mist-metadata-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba.spring.boot</groupId>
                    <artifactId>dubbo-spring-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.code.meta</groupId>
                    <artifactId>metadata-res-dml-interface</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>metadata-provider</artifactId>
                    <groupId>com.code.meta</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>etl-plugin</artifactId>
                    <groupId>com.code.etl</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>edk-pom</artifactId>
                    <groupId>com.code.etl.open.edk</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mist-metadata-interface</artifactId>
                    <groupId>com.code.meta</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mist-plugin</artifactId>
                    <groupId>com.code.common</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>metadata-server-manage-interface</artifactId>
                    <groupId>com.code.meta</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>metadata-datacenter-core</artifactId>
                    <groupId>com.code.meta</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>metadata-datacenter-provider</artifactId>
                    <groupId>com.code.meta</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>metadata-datacenter-interface</artifactId>
                    <groupId>com.code.meta</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>meta-res-dml</artifactId>
                    <groupId>com.code.meta</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>hutool-all</artifactId>
                    <groupId>cn.hutool</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>metadata-server-manage-provider</artifactId>
                    <groupId>com.code.meta</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.code.etl.plugin</groupId>
                    <artifactId>plugin-field-filtering-page</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>tenon-fw-business-combinationsql-util</artifactId>
                    <groupId>com.fw.tenon</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>tenon-fw-business-conversion-interface</artifactId>
                    <groupId>com.fw.tenon</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>tenon-fw-business-function-util</artifactId>
                    <groupId>com.fw.tenon</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>tenon-fw-business-conversion-provider</artifactId>
                    <groupId>com.fw.tenon</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>tenon-fw-business-unified-operator-util</artifactId>
                    <groupId>com.fw.tenon</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>tenon-fw-business-tree-util</artifactId>
                    <groupId>com.fw.tenon</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>dc-ms-business-define-interface</artifactId>
                    <groupId>com.dragonsoft.dc</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>dc-ms-business-define-provider</artifactId>
                    <groupId>com.dragonsoft.dc</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>cicada-plugin-many-join-page</artifactId>
                    <groupId>com.code.etl.plugin</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fw-hibernate5-adapter</artifactId>
                    <groupId>com.fw</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.code.reaper</groupId>
            <artifactId>reaper-base</artifactId>
            <version>1.2</version>
            <exclusions>
                <exclusion>
                    <groupId>com.code.meta</groupId>
                    <artifactId>metadata</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>metadir</artifactId>
                    <groupId>com.code.meta</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>aspectjweaver</artifactId>
                    <groupId>org.aspectj</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>metadata-provider</artifactId>
                    <groupId>com.code.meta</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--   springboot dependency start     -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-cache</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>log4j-over-slf4j</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>logback-core</artifactId>
                    <groupId>ch.qos.logback</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>logback-classic</artifactId>
                    <groupId>ch.qos.logback</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <version>1.2.3</version>
        </dependency>


        <!--<dependency>
            <groupId>com.alibaba.spring.boot</groupId>
            <artifactId>dubbo-spring-boot-starter</artifactId>
            <version>${springboot.dubbo.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>io.searchbox</groupId>
                    <artifactId>jest</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>dubbo</artifactId>
                </exclusion>
            </exclusions>
        </dependency>-->
        <dependency>
            <groupId>com.code.cicadas.datacenter</groupId>
            <artifactId>mist-data-operation-provider</artifactId>
            <version>1.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>guava</artifactId>
                    <groupId>com.google.guava</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mist-schedule-interface</artifactId>
                    <groupId>com.code.mist.schedule</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fw-admin</artifactId>
                    <groupId>com.fw</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mist-context</artifactId>
                    <groupId>com.code.common</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mist-plugin</artifactId>
                    <groupId>com.code.common</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>dc-service-interface</artifactId>
                    <groupId>com.code.cicadas.datacenter</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>metadata-datacenter-provider</artifactId>
                    <groupId>com.code.meta</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>common-schedule</artifactId>
                    <groupId>com.code.common</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>meta-res-dml</artifactId>
                    <groupId>com.code.meta</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>etl-plugin</artifactId>
                    <groupId>com.code.etl</groupId>
                </exclusion>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
            <version>1.2.8</version>
        </dependency>

        <dependency>
            <groupId>com.dragonsoft</groupId>
            <artifactId>author-dragon</artifactId>
            <version>3.0.1-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-api</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-simple</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-lang</artifactId>
                    <groupId>commons-lang</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>ojdbc6</artifactId>
                    <groupId>com.oracle</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>guava</artifactId>
                    <groupId>com.google.guava</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.12</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.code.common</groupId>
            <artifactId>rule-engine-provider</artifactId>
            <version>1.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>metadata-provider</artifactId>
                    <groupId>com.code.meta</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fw-service</artifactId>
                    <groupId>com.fw</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jsp-api</artifactId>
                    <groupId>javax.servlet.jsp</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mist-graph</artifactId>
                    <groupId>com.code.common</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.code.meta</groupId>
            <artifactId>mist-metadata-interface</artifactId>
            <version>3.4.1-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.code.meta</groupId>
            <artifactId>mist-metadata-provider</artifactId>
            <version>3.4.1-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>mist-metadata-core</artifactId>
                    <groupId>com.code.meta</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>metadata-core</artifactId>
                    <groupId>com.code.meta</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>metadata-kernel-core</artifactId>
                    <groupId>com.code.meta</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>metadata-kernel-interface</artifactId>
                    <groupId>com.code.meta</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.code.meta</groupId>
            <artifactId>mist-metadata-core</artifactId>
            <version>3.4.1-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>javax.servlet.jsp</groupId>
            <artifactId>jsp-api</artifactId>
            <version>2.2</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
            <version>3.1.0</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.code.common</groupId>
            <artifactId>mist-service-interface</artifactId>
            <version>3.5.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>metadata-interface</artifactId>
                    <groupId>com.code.meta</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <artifactId>objenesis</artifactId>
                    <groupId>org.objenesis</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--   springboot dependency end     -->

        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-entitymanager</artifactId>
            <version>${hibernate.version}</version>
        </dependency>

        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-core</artifactId>
            <version>${hibernate.version}</version>
        </dependency>

        <dependency>
            <groupId>com.code.mist.builder</groupId>
            <artifactId>mist-scheme-builder-provider</artifactId>
            <version>2.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>metadata-provider</artifactId>
                    <groupId>com.code.meta</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mist-schedule-interface</artifactId>
                    <groupId>com.code.mist.schedule</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mist-plugin</artifactId>
                    <groupId>com.code.common</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>common-schedule</artifactId>
                    <groupId>com.code.common</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjrt</artifactId>
            <version>1.8.5</version>
        </dependency>

        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjweaver</artifactId>
            <version>1.8.5</version>
        </dependency>
        <!--    fw dependency start    -->

        <dependency>
            <groupId>com.fw</groupId>
            <artifactId>fw-dao</artifactId>
            <version>2.0</version>
            <exclusions>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>aspectjweaver</artifactId>
                    <groupId>org.aspectj</groupId>
                </exclusion>
                <exclusion>
                    <groupId>org.hibernate</groupId>
                    <artifactId>hibernate-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.code.meta</groupId>
            <artifactId>metadata-server-manage-provider</artifactId>
            <version>3.5.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.code.meta</groupId>
            <artifactId>metadata-datacenter-provider</artifactId>
            <version>4.0.0-DC-CD-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.code.meta</groupId>
                    <artifactId>metadata-res-dml-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.code.meta</groupId>
                    <artifactId>metadata-res-dml-provider</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>mist-metadata-interface</artifactId>
                    <groupId>com.code.meta</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fw-service</artifactId>
                    <groupId>com.fw</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>guava</artifactId>
                    <groupId>com.google.guava</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>metadata-res-dml-interface</artifactId>
                    <groupId>com.code.meta</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mist-metadata-provider</artifactId>
                    <groupId>com.code.meta</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>metadata-server-manage-provider</artifactId>
                    <groupId>com.code.meta</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.code.common</groupId>
                    <artifactId>common-dbscript</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>metadata-server-manage-interface</artifactId>
                    <groupId>com.code.meta</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>metadata-server-manage-core</artifactId>
                    <groupId>com.code.meta</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.code.meta</groupId>
            <artifactId>metadata-business-provider</artifactId>
            <version>${metadata.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.code.common</groupId>
                    <artifactId>common-dbscript</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.code.meta</groupId>
                    <artifactId>metadata-res-dml-provider</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>mist-metadata-interface</artifactId>
                    <groupId>com.code.meta</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fw-service</artifactId>
                    <groupId>com.fw</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>guava</artifactId>
                    <groupId>com.google.guava</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--    metadata dependency end    -->

        <!--    common dependency start    -->
        <dependency>
            <groupId>com.code.common</groupId>
            <artifactId>common-base</artifactId>
            <version>${common.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>commons-lang</artifactId>
                    <groupId>commons-lang</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-io</artifactId>
                    <groupId>commons-io</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-compress</artifactId>
                    <groupId>org.apache.commons</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>guava</artifactId>
                    <groupId>com.google.guava</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jedis</artifactId>
                    <groupId>redis.clients</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.quartz-scheduler</groupId>
            <artifactId>quartz</artifactId>
            <version>2.3.2</version>
        </dependency>

        <dependency>
            <groupId>com.fw</groupId>
            <artifactId>fw-service</artifactId>
            <version>2.0</version>
            <exclusions>
                <exclusion>
                    <artifactId>aspectjrt</artifactId>
                    <groupId>org.aspectj</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>aspectjweaver</artifactId>
                    <groupId>org.aspectj</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--    common dependency end    -->

        <!--    jdbc dependency start    -->
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <version>9.4.1212.jre7</version>
        </dependency>
        <!--    jdbc dependency end    -->

        <!-- ZK客户端 etl-core -->
        <dependency>
            <groupId>org.apache.curator</groupId>
            <artifactId>curator-framework</artifactId>
            <version>4.2.0</version>
            <type>jar</type>
            <exclusions>
                <exclusion>
                    <artifactId>guava</artifactId>
                    <groupId>com.google.guava</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.curator</groupId>
            <artifactId>curator-recipes</artifactId>
            <version>4.2.0</version>
            <type>jar</type>
        </dependency>

        <dependency>
            <groupId>com.code.meta</groupId>
            <artifactId>meta-res-dml</artifactId>
            <version>3.3.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>metadata-core</artifactId>
                    <groupId>com.code.meta</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.code.meta</groupId>
            <artifactId>meta-res-ddl</artifactId>
            <version>${meta.handler.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>meta-res-dml</artifactId>
                    <groupId>com.code.meta</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.code.meta</groupId>
            <artifactId>meta-sync</artifactId>
            <version>${meta.handler.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.code.meta</groupId>
                    <artifactId>metadata-res-dml-provider</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>metadata-provider</artifactId>
                    <groupId>com.code.meta</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>${okhttp.version}</version>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.7.16</version>
        </dependency>

        <dependency>
            <groupId>com.code.common</groupId>
            <artifactId>mist-service-loader</artifactId>
            <version>1.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>commons-compress</artifactId>
                    <groupId>org.apache.commons</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>guava</artifactId>
                    <groupId>com.google.guava</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>servlet-api</artifactId>
                    <groupId>javax.servlet</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>hadoop-client</artifactId>
                    <groupId>org.apache.hadoop</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.apache.hadoop</groupId>
            <artifactId>hadoop-client</artifactId>
            <version>2.8.5</version>
            <exclusions>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.dragoninfo</groupId>
            <artifactId>dfw-login</artifactId>
            <version>${dfw.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>spring-boot-starter-data-redis</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>springfox-swagger-ui</artifactId>
                    <groupId>io.springfox</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>springfox-swagger2</artifactId>
                    <groupId>io.springfox</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>javaee-api</artifactId>
                    <groupId>javax</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>dfw-auth-provider</artifactId>
                    <groupId>com.dragoninfo</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dragoninfo</groupId>
            <artifactId>dfw-auth-provider</artifactId>
            <version>3.3.1-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-data-redis</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.belerweb</groupId>
            <artifactId>pinyin4j</artifactId>
            <version>2.5.1</version>
        </dependency>
        <dependency>
            <groupId>com.code.cicadas.datacenter</groupId>
            <artifactId>dc-service-provider</artifactId>
            <version>3.5.0-SNAPSHOT</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <groupId>com.code.common</groupId>
                    <artifactId>common-dbscript</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.code.meta</groupId>
                    <artifactId>metadata-res-dml-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.code.meta</groupId>
                    <artifactId>metadata-res-dml-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.code.meta</groupId>
                    <artifactId>metadata-res-dml-interface</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>metadata-datacenter-core</artifactId>
                    <groupId>com.code.meta</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mist-metadata-provider</artifactId>
                    <groupId>com.code.meta</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mist-metadata-interface</artifactId>
                    <groupId>com.code.meta</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mist-metadata-core</artifactId>
                    <groupId>com.code.meta</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>metadata-server-manage-provider</artifactId>
                    <groupId>com.code.meta</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>metadata-server-manage-interface</artifactId>
                    <groupId>com.code.meta</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>metadata-server-manage-core</artifactId>
                    <groupId>com.code.meta</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>metadata-datacenter-provider</artifactId>
                    <groupId>com.code.meta</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>meta-res-dml</artifactId>
                    <groupId>com.code.meta</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>etl-plugin</artifactId>
                    <groupId>com.code.etl</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.code.meta</groupId>
            <artifactId>metadata-res-dml-provider</artifactId>
            <version>3.5.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.code.meta</groupId>
            <artifactId>metadata-res-dml-interface</artifactId>
            <version>3.5.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.fw</groupId>
            <artifactId>fw-admin</artifactId>
            <version>2.0</version>
            <type>jar</type>
            <classifier>classes</classifier>
        </dependency>


        <dependency>
            <groupId>commons-discovery</groupId>
            <artifactId>commons-discovery</artifactId>
            <version>0.2</version>
        </dependency>

        <dependency>
            <groupId>axis</groupId>
            <artifactId>axis</artifactId>
            <version>1.4</version>
        </dependency>

        <dependency>
            <groupId>jaxrpc</groupId>
            <artifactId>jaxrpc</artifactId>
            <version>1.0</version>
        </dependency>

        <dependency>
            <groupId>jaxen</groupId>
            <artifactId>jaxen</artifactId>
            <version>1.1.1</version>
            <exclusions>
                <exclusion>
                    <artifactId>xercesImpl</artifactId>
                    <groupId>xerces</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>wsdl4j</groupId>
            <artifactId>wsdl4j</artifactId>
            <version>1.5.1</version>
        </dependency>

        <dependency>
            <groupId>xpp3</groupId>
            <artifactId>xpp3</artifactId>
            <version>1.1.4c</version>
        </dependency>

        <dependency>
            <groupId>xstream</groupId>
            <artifactId>xstream</artifactId>
            <version>1.3.1</version>
        </dependency>

        <dependency>
            <groupId>org.springside</groupId>
            <artifactId>springside-core</artifactId>
            <version>4.1.0.GA</version>
        </dependency>

        <dependency>
            <groupId>dids-client</groupId>
            <artifactId>dids-client</artifactId>
            <version>2.2.1</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/lib/dids-client-2.2.1.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>encrypt_dragon</groupId>
            <artifactId>encrypt_dragon</artifactId>
            <version>1.0</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/lib/encrypt_dragon.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>middleobject</groupId>
            <artifactId>middleobject</artifactId>
            <version>1.1</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/lib/middleobject-1.1.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>xerces</groupId>
            <artifactId>xercesImpl</artifactId>
            <version>2.6.2</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/lib/xercesImpl-2.6.2.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>com.code.common</groupId>
            <artifactId>cicada-mist-plugin</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
            <version>1.2.15</version>
        </dependency>
        <dependency>
            <groupId>com.code.etl.plugin</groupId>
            <artifactId>plugin-rule-executor-meta</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>


        <dependency>
            <groupId>com.code.meta</groupId>
            <artifactId>metadata-res-ddl-core</artifactId>
            <version>${metadata.res.ddl.version}</version>
        </dependency>
        <dependency>
            <groupId>com.code.meta</groupId>
            <artifactId>metadata-res-ddl-interface</artifactId>
            <version>${metadata.res.ddl.version}</version>
        </dependency>
        <dependency>
            <groupId>com.code.meta</groupId>
            <artifactId>metadata-res-ddl-provider</artifactId>
            <version>${metadata.res.ddl.version}</version>
        </dependency>

        <dependency>
            <groupId>org.freemarker</groupId>
            <artifactId>freemarker</artifactId>
            <version>${freemarker.version}</version>
        </dependency>

    </dependencies>

    <!-- 引入版本管理器 问题太多先不引 -->
    <!--   <dependencyManagement>
           <dependencies>
               <dependency>
                   <groupId>com.dragonsoft.dc</groupId>
                   <artifactId>cicada-datacenter-dependencies</artifactId>
                   <version>3.4.0-SNAPSHOT</version>
                   <type>pom</type>
                   <scope>import</scope>
               </dependency>
           </dependencies>
       </dependencyManagement>
   -->

    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <excludes>
                    <exclude>application.yml</exclude>
                    <exclude>config-*.yml</exclude>
                </excludes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <!-- 打包时包含文件 -->
                <includes>
                    <include>application.yml</include>
                    <include>config-${profile.active}.yml</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.sql</include>
                </includes>
            </resource>
        </resources>

        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.2.6.RELEASE</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <!--                    <executable>true</executable>-->
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.1</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-assembly-plugin</artifactId>
                <version>2.6</version>
                <configuration>
                    <descriptors>
                        <descriptor>src/main/assembly/assembly.xml</descriptor>
                    </descriptors>
                </configuration>
                <executions>
                    <execution>
                        <id>make-assembly</id> <!-- this is used for inheritance merges -->
                        <phase>package</phase> <!-- append to the packaging phase. -->
                        <goals>
                            <goal>single</goal> <!-- goals == mojos -->
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!--生成git-infos.properties 版本文件-->
            <plugin>
                <groupId>pl.project13.maven</groupId>
                <artifactId>git-commit-id-plugin</artifactId>
                <version>4.9.10</version>
                <executions>
                    <execution>
                        <id>get-the-git-infos</id>
                        <goals>
                            <goal>revision</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <abbrevLength>8</abbrevLength>
                    <dateFormat>yyyy-MM-dd HH:mm:ss</dateFormat>
                    <generateGitPropertiesFile>true</generateGitPropertiesFile>
                    <includeOnlyProperties>
                        <includeOnlyProperty>^git.branch</includeOnlyProperty>
                        <includeOnlyProperty>^git.build.(time|version)$</includeOnlyProperty>
                        <includeOnlyProperty>^git.commit.(id|id.abbrev|message.full)*</includeOnlyProperty>
                    </includeOnlyProperties>
                    <generateGitPropertiesFilename>${project.build.outputDirectory}/git-infos.properties
                    </generateGitPropertiesFilename>
                </configuration>
            </plugin>


        </plugins>
    </build>

</project>
