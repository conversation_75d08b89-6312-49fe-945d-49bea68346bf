<assembly xmlns="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.0
          http://maven.apache.org/xsd/assembly-1.1.0.xsd">
    <id>bin</id>
    <baseDirectory>/</baseDirectory>
    <formats>
        <format>tar.gz</format>
    </formats>
    <fileSets>
        <fileSet>
            <directory>${project.build.directory}</directory>
            <includes>
                <include>cicada-dataCenter-web-3.6.1.RELEASE.jar</include>
            </includes>
            <outputDirectory>/dc-web-api</outputDirectory>
        </fileSet>
        <fileSet>
            <directory>src/main/resources</directory>
            <includes>
                <include>*.sh</include>
<!--                <include>config.yml</include>-->
<!--                <include>prop_config.yml</include>-->
            </includes>
            <outputDirectory>/dc-web-api</outputDirectory>
        </fileSet>
    </fileSets>
</assembly>
