package com.dragonsoft.cicada.datacenter;

import com.dragonsoft.cicada.datacenter.common.config.PropertiesListener;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;
import org.springframework.cloud.netflix.feign.EnableFeignClients;
import org.springframework.context.annotation.ImportResource;

/**
 * <AUTHOR> Wu.D.J
 * @Create : 2019.7.8
 */
@SpringBootApplication
@EnableCaching
@EnableFeignClients
@EnableEurekaClient
@ImportResource("classpath:application-context.xml")
public class DataCenterWebApplication {

    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(DataCenterWebApplication.class);
        application.addListeners(new PropertiesListener("mlsql-engine-config.properties"));
        //SpringApplication.run(DataCenterWebApplication.class, args);
        application.run(args);
    }
}
