package com.dragonsoft.cicada.datacenter;

import com.dragoninfo.dfw.bean.Result;
import lombok.Data;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

// 填入注册中心中的应用名, 也就是要调用的微服务的应用名
// 在eureka页面中可以找到
@FeignClient("service-publish-worker")
public interface ServicePublishClient {

    @RequestMapping("/service/getServiceToken")
    Result getServiceToken(@RequestBody ServiceTokenVo serviceTokenVo);

    @RequestMapping("/serviceOutput/getServiceOutput")
    Result getServiceOutput();

    @Data
    class ServiceTokenVo {
        private String userId;
        private String userName;
        private Boolean rememberMe;
    }
}
