package com.dragonsoft.cicada.datacenter;

import com.dragonsoft.cicada.datacenter.modules.system.schedule.job.DropExternalTableStarter;
import com.fw.spring.SpringContainer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class DataCenterAppRunner implements ApplicationRunner {

    @Autowired
    private ApplicationContext applicationContext;


    @Override
    public void run(ApplicationArguments applicationArguments) throws Exception {
        SpringContainer container = SpringContainer.getInstance();
        container.setApplicationContext(applicationContext);
        if (applicationContext.containsBean(DropExternalTableStarter.BEAN_NAME)) {
            DropExternalTableStarter starter = applicationContext.getBean(DropExternalTableStarter.class);
            starter.start();
        }
    }

}

