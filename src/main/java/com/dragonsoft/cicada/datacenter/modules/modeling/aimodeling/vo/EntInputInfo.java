package com.dragonsoft.cicada.datacenter.modules.modeling.aimodeling.vo;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description：实体数据接入信息bean
 * @date ：2021/10/13 10:49
 */
public class EntInputInfo implements Serializable {

    public EntInputInfo() {
    }

    public EntInputInfo(String sourceDatabase, int totalColumn, int totalRow, List<SourceTable> sourceTables) {
        this.sourceDatabase = sourceDatabase;
        this.totalColumn = totalColumn;
        this.totalRow = totalRow;
        this.sourceTables = sourceTables;
    }

    /**
     * 来源库
     */
    private String sourceDatabase;

    /**
     * 列数
     */
    private int totalColumn;

    /**
     * 行数
     */
    private int totalRow;

    /**
     * 来源表列表
     */
    private List<SourceTable> sourceTables;


    @Override
    public String toString(){
        StringBuilder s = new StringBuilder(
                "sourceDatabase:" + sourceDatabase + ", totalColumn:" + totalColumn
                        + ", totalRow:" + totalRow + ", sourceTables:"
        );
        if(sourceTables != null) {
            for (SourceTable t : sourceTables) {
                if (t != null) {
                    s.append(t.chineseName).append("|").append(t.englishName).append("; ");
                }
            }
        }
        return s.toString();
    }

    public String getSourceDatabase() {
        return sourceDatabase;
    }

    public void setSourceDatabase(String sourceDatabase) {
        this.sourceDatabase = sourceDatabase;
    }

    public int getTotalColumn() {
        return totalColumn;
    }

    public void setTotalColumn(int totalColumn) {
        this.totalColumn = totalColumn;
    }

    public int getTotalRow() {
        return totalRow;
    }

    public void setTotalRow(int totalRow) {
        this.totalRow = totalRow;
    }

    public List<SourceTable> getSourceTables() {
        return sourceTables;
    }

    public void setSourceTables(List<SourceTable> sourceTables) {
        this.sourceTables = sourceTables;
    }

    /**
     * 来源表信息
     */
    public static class SourceTable implements Serializable{

        public SourceTable() {
        }

        public SourceTable(String chineseName, String englishName) {
            this.chineseName = chineseName;
            this.englishName = englishName;
        }

        /**
         * 中文名
         */
        private String chineseName;
        /**
         * 英文名
         */
        private String englishName;

        public String getChineseName() {
            return chineseName;
        }

        public void setChineseName(String chineseName) {
            this.chineseName = chineseName;
        }

        public String getEnglishName() {
            return englishName;
        }

        public void setEnglishName(String englishName) {
            this.englishName = englishName;
        }
    }


}
