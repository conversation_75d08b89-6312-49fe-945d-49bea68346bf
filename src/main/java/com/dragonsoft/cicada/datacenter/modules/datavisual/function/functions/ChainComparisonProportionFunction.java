package com.dragonsoft.cicada.datacenter.modules.datavisual.function.functions;

import com.code.dragonsoft.dataquery.service.querymodel.ColumnDataModel;
import com.dragonsoft.cicada.datacenter.modules.datavisual.function.ChartConfig;
import com.dragonsoft.cicada.datacenter.modules.datavisual.function.ColumnMeta;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Assert;

import java.util.List;
import java.util.concurrent.ThreadLocalRandom;

/**
 * <AUTHOR>
 * @date 2021/1/4
 */
public class ChainComparisonProportionFunction extends AbstractFunction<ChainComparisonProportionFunction.ChainComparisonProportionMeta> {

    private final Logger logger = LoggerFactory.getLogger(ChainComparisonFunction.class);

    @Override
    protected ChainComparisonProportionMeta buildMeta(ChartConfig config,ColumnDataModel dates) {
        List<ColumnMeta> dimensions = config.getDimensions();
        Assert.notEmpty(dimensions, "chart dimension cannot be empty!");
        /*List<ColumnMeta> timeColumns = dimensions.stream()
                .filter(dimension -> timeType.contains(dates.getFieldName().get(dimension.getCode())))
                .collect(Collectors.toList());
        Assert.notEmpty(timeColumns, "time field cannot be empty!");*/
        List<ColumnMeta> timeColumns = dimensions;
        if (timeColumns.size() > 1 && logger.isWarnEnabled()) {
            logger.warn("There is more than one time field, actual: {}", timeColumns.size());
        }
        ColumnMeta timeColumn = timeColumns.get(ThreadLocalRandom.current().nextInt(timeColumns.size()));
        for (ColumnMeta dimension : dimensions) {
            if(dimension.isQueryColumnl()){
                timeColumn = dimension;
            }
        }        if (logger.isDebugEnabled()) {
            logger.debug("get random time field: {}", timeColumn.getCode());
        }
        List<ColumnMeta> metrics = config.getMetrics();
        Assert.notEmpty(metrics, "chart metric cannot be empty!");
        ColumnMeta metricColumn = metrics.get(0);
        return new ChainComparisonProportionFunction.ChainComparisonProportionMeta(timeColumn.getCode(), metricColumn.getCode());
    }

    @Override
    protected ColumnDataModel doCalculate(ColumnDataModel dates, ChainComparisonProportionMeta chainComparisonProportionMeta) {

        String metricColumn = chainComparisonProportionMeta.metricColumn;

        if (2 <= dates.getFieldValue().size()) {
            Double previousValue = 0.0;
            for (int i = 0; i < dates.getFieldValue().size(); i++) {

                if (0 == i) {
                    previousValue = Double.valueOf(dates.getFieldValue().get(i).get(metricColumn).toString());
                    dates.getFieldValue().get(i).put(metricColumn, 0.0);
                }

                if (0 != i) {
                    Double currentValue = Double.valueOf(dates.getFieldValue().get(i).get(metricColumn).toString());

                    Double differenceValue = currentValue - previousValue;
                    //previousValue为0 增长率为百分之百
                    if (0.0 == previousValue) {
                        dates.getFieldValue().get(i).put(metricColumn, Double.NaN);
                    } else {
                        dates.getFieldValue().get(i).put(metricColumn, super.KeepDecimals(differenceValue / previousValue, 2));
                    }
                    previousValue = currentValue;
                }
            }
            return dates;
        }

        return dates;
    }

    @Override
    public String getName() {
        return "求环比(比例)";
    }

    @Override
    public String getCode() {
        return "chain-comparison-proportion";
    }

    protected static class ChainComparisonProportionMeta {
        private String timeColumn;
        private String metricColumn;

        public ChainComparisonProportionMeta(String timeColumn, String metricColumn) {
            this.timeColumn = timeColumn;
            this.metricColumn = metricColumn;
        }
    }
}
