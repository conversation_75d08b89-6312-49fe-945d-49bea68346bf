/*==============================================================*/
/* Table: T_RESOURCES_APPLY                                     */
/*==============================================================*/
CREATE TABLE T_RESOURCES_APPLY (
   ID                   VARCHAR(32)          NOT NULL,
   APPLY_TYPE           VARCHAR(32)          NULL,
   RESOURCE_TYPE        VARCHAR(32)          NULL,
   RESOURCE_ID          VARCHAR(32)          NULL,
   RESOURCE_NAME         VARCHAR(32)          NULL,
   AUDIT_USER_ID        VARCHAR(32)         NULL,
   APPLY_TIME           VARCHAR(32)          NULL,
   APPLY_RESON          VARCHAR(500)         NULL,
   APPLY_USER_ID        VARCHAR(32)          NULL,
   AUDIT_STATE          VARCHAR(32)          NULL,
   AUDIT_RESULT_STATE   VARCHAR(32)          NULL,
   CONSTRAINT PK_T_RESOURCES_APPLY PRIMARY KEY (ID)
);

COMMENT ON COLUMN T_RESOURCES_APPLY.ID IS
'主键';

COMMENT ON COLUMN T_RESOURCES_APPLY.APPLY_TYPE IS
'申请类型(模型发布申请，模型复用申请)';

COMMENT ON COLUMN T_RESOURCES_APPLY.RESOURCE_TYPE IS
'资源类型';

COMMENT ON COLUMN T_RESOURCES_APPLY.RESOURCE_ID IS
'资源ID';

COMMENT ON COLUMN T_RESOURCES_APPLY.RESOURCE_NAME IS
'资源名称';

COMMENT ON COLUMN T_RESOURCES_APPLY.AUDIT_USER_ID IS
'审批人ID';

COMMENT ON COLUMN T_RESOURCES_APPLY.APPLY_TIME IS
'申请时间';

COMMENT ON COLUMN T_RESOURCES_APPLY.APPLY_RESON IS
'申请原因';

COMMENT ON COLUMN T_RESOURCES_APPLY.APPLY_USER_ID IS
'申请人ID';

COMMENT ON COLUMN T_RESOURCES_APPLY.AUDIT_STATE IS
'审批状态(审批中，审批通过，审批未通过)';

COMMENT ON COLUMN T_RESOURCES_APPLY.AUDIT_RESULT_STATE IS
'审批结果确认状态(已确认，未确认)';




/*==============================================================*/
/* Table: T_RESOURCES_AUDIT                                     */
/*==============================================================*/
CREATE TABLE T_RESOURCES_AUDIT (
   ID                   VARCHAR(32)          NOT NULL,
   APPLY_ID             VARCHAR(32)          NULL,
   AUDIT_USER_ID        VARCHAR(32)          NULL,
   AUDIT_TIME           VARCHAR(32)          NULL,
   AUDIT_STATE          VARCHAR(32)          NULL,
   AUDIT_OPTION         VARCHAR(500)         NULL,
   CONSTRAINT PK_T_RESOURCES_AUDIT PRIMARY KEY (ID)
);

COMMENT ON COLUMN T_RESOURCES_AUDIT.ID IS
'主键';

COMMENT ON COLUMN T_RESOURCES_AUDIT.APPLY_ID IS
'申请信息ID';

COMMENT ON COLUMN T_RESOURCES_AUDIT.AUDIT_USER_ID IS
'审批人ID';

COMMENT ON COLUMN T_RESOURCES_AUDIT.AUDIT_TIME IS
'审批时间';

COMMENT ON COLUMN T_RESOURCES_AUDIT.AUDIT_STATE IS
'审批状态(审批中，审批通过，审批未通过)';

COMMENT ON COLUMN T_RESOURCES_AUDIT.AUDIT_OPTION IS
'审批意见';




ALTER TABLE  t_model_attached_information ADD CASE_TYPE_CODE VARCHAR(32);
COMMENT ON COLUMN t_model_attached_information.CASE_TYPE_CODE IS '案件类型代码';
ALTER TABLE  t_model_attached_information ADD CASE_TYPE_NAME VARCHAR(32);
COMMENT ON COLUMN t_model_attached_information.CASE_TYPE_NAME IS '案件类型名称';
ALTER TABLE  t_model_attached_information ADD AREA_TYPE_CODE VARCHAR(32);
COMMENT ON COLUMN t_model_attached_information.AREA_TYPE_CODE IS '区域类型代码';
ALTER TABLE  t_model_attached_information ADD AREA_TYPE_NAME VARCHAR(32);
COMMENT ON COLUMN t_model_attached_information.CASE_TYPE_NAME IS '区域类型名称';
ALTER TABLE  t_model_attached_information ADD CONTROL_TYPE_CODE VARCHAR(32);
COMMENT ON COLUMN t_model_attached_information.CONTROL_TYPE_CODE IS '打防管控类型代码';
ALTER TABLE  t_model_attached_information ADD CONTROL_TYPE_NAME VARCHAR(32);
COMMENT ON COLUMN t_model_attached_information.CONTROL_TYPE_NAME IS '打防管控类型名称';



