package com.dragonsoft.cicada.datacenter.modules.modeling.service.impl;

import com.dragonsoft.cicada.datacenter.modules.modeling.service.MlsqlTransScheduleService;
import com.fw.service.BaseService;
import com.fw.service.annotation.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by yecc on 2020/11/11 9:25
 */
@Service
public class MlsqlTransScheduleServiceImpl extends BaseService implements MlsqlTransScheduleService {
    @Override
    public String getTransTaskStatus(String transId) {
        String sql = "SELECT execute_status FROM t_trans_job t WHERE t .trans_id = '" + transId + "'\n" +
                "AND t.start_time IN (SELECT MAX(start_time) from t_trans_job a GROUP BY a.trans_id)";
        List list = this.baseDao.sqlQueryForList(sql);
        return list.size() == 0 ? "" : ((HashMap) list.get(0)).get("execute_status").toString();
    }

    @Override
    public Map getTransTask(String transId) {
        String sql = "SELECT execute_status,start_time,end_time FROM t_trans_job t WHERE t .trans_id = '" + transId + "'\n" +
                "AND t.start_time IN (SELECT MAX(start_time) from t_trans_job a GROUP BY a.trans_id)";
        List list = this.baseDao.sqlQueryForList(sql);
        return list.size() > 0 ?  (Map) list.get(0) : null;
    }

    @Override
    public void deleteByTaskId(String taskId) {
        String sql = "delete from t_trans_job  WHERE id =:taskId";
        this.baseDao.executeSqlUpdate(sql, addParam("taskId", taskId).param());
    }


}
