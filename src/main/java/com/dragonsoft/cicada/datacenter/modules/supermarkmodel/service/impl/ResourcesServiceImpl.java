package com.dragonsoft.cicada.datacenter.modules.supermarkmodel.service.impl;

import com.code.common.paging.PageInfo;
import com.code.common.utils.DateUtils;
import com.code.common.utils.StringUtils;
import com.code.common.utils.assertion.Assert;
import com.code.metadata.modelsupermark.ResourcesApply;
import com.code.metadata.modelsupermark.ResourcesAudit;
import com.code.metadata.modelsupermark.SupermarkModel;
import com.code.metadata.modelsupermark.enumtype.ModelStateEnum;
import com.code.metaservice.modelsupermark.IModelSearchService;
import com.dragoninfo.dfw.bean.Result;
import com.dragoninfo.dfw.entity.TSysAuthUser;
import com.dragoninfo.dfw.service.SysAuthUserService;
import com.dragonsoft.cicada.datacenter.modules.supermarkmodel.service.IMyModelService;
import com.dragonsoft.cicada.datacenter.modules.supermarkmodel.service.IResourcesService;
import com.dragonsoft.cicada.datacenter.modules.supermarkmodel.vo.ModelResourcesEnum;
import com.dragonsoft.cicada.datacenter.modules.supermarkmodel.vo.ResourcesApplyAuditVo;
import com.dragonsoft.cicada.datacenter.modules.supermarkmodel.vo.ResourcesQueryVo;
import com.fw.service.BaseService;
import com.fw.service.annotation.Service;
import com.google.common.collect.Maps;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.Map;

@Service
public class ResourcesServiceImpl extends BaseService implements IResourcesService {
    @Autowired
    private SysAuthUserService sysAuthUserService;

    @Autowired
    private IModelSearchService modelSearchServiceImpl;
    @Autowired
    private IMyModelService myModelService;


    public void addResourcesApply(ResourcesApply resourcesApply){
//        resourcesApply.setApplyType(ModelResourcesEnum.MODEL_PULTILEXING.getCode());
        resourcesApply.setApplyTime(DateUtils.getDateFormat("yyyy-MM-dd HH:mm:ss"));
        resourcesApply.setAuditResultState(ModelResourcesEnum.AUDIT_UNCONFIRM.getCode());
        resourcesApply.setAuditState(ModelResourcesEnum.RESOURCE_AUDIT_UNAUDIT.getCode());
        baseDao.save(resourcesApply);
    }

    public PageInfo getListByType(ResourcesQueryVo vo){
        Assert.notNull(vo.getType(), "类型不能为空");
        if(!ModelResourcesEnum.APPLY_LIST.getCode().equals(vo.getType())
                && !ModelResourcesEnum.AUDIT_LIST.getCode().equals(vo.getType()) ){
            Assert.fail("查询列表类型错误");
        }
        return queryList(vo);

    }

    private PageInfo queryList(ResourcesQueryVo vo){
        Map<String, Object> params = Maps.newHashMap();

        StringBuilder sb = new StringBuilder();
        sb.append(" select a.*,t1.apply_user_name,t2.audit_user_name,m.id as model_id from t_resources_apply a  ");
        sb.append(" left join t_supermark_model m on a.resource_id=m.trans_id ");
        sb.append(" left join (SELECT distinct(u.ID) as id,obj_name AS apply_user_name from t_sys_auth_obj u left join t_resources_apply on u.id=apply_user_id) t1 on a.apply_user_id=t1.id ");
        sb.append(" left join (SELECT distinct(u.ID) as id,obj_name AS audit_user_name from t_sys_auth_obj u left join t_resources_apply on u.id=audit_user_id) t2 on a.audit_user_id=t2.id ");
        sb.append(" where 1=1 ");
        if(!isSuperAdmin(vo.getUserId())){
            if(ModelResourcesEnum.APPLY_LIST.getCode().equals(vo.getType())){
                sb.append(" and apply_user_id=:userId");
            }else{
                sb.append(" and audit_user_id=:userId");
            }
            params.put("userId",vo.getUserId());
        }
        addQueryCondition(params,sb, vo);
        sb.append(" order by audit_state asc, ");
        if(ModelResourcesEnum.AUDIT_LIST.getCode().equals(vo.getType())){
            sb.append(" audit_result_state asc,");
        }
        sb.append(" apply_time desc");


        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageIndex(vo.getPageNum());
        pageInfo.setPageSize(vo.getPageSize());

        PageInfo pageResult = baseDao.sqlQueryForPage(sb.toString(), params, pageInfo);
        return pageResult;
    }

    private boolean isSuperAdmin(String userId){
        Map<String,String> params = new HashMap<>();
        params.put("id", userId);
        TSysAuthUser tSysAuthUser = (TSysAuthUser) sysAuthUserService.query(params);
        if("dc_super".equals(tSysAuthUser.getObjCode())){
            return true;
        }
        return false;
    }

    private void addQueryCondition(Map<String, Object> params,StringBuilder sb,ResourcesQueryVo vo){

        if(StringUtils.isNotBlank(vo.getApplyType())){
            sb.append(" and apply_type=:applyType");
            params.put("applyType",vo.getApplyType());
        }
        if(StringUtils.isNotBlank(vo.getResourceName())){
            sb.append(" and resource_name like :resourceName");
            params.put("resourceName","%" + vo.getResourceName() + "%");
        }
        if(StringUtils.isNotBlank(vo.getAuditState())){
            sb.append(" and audit_state=:auditState");
            params.put("auditState",vo.getAuditState());
        }

    }


    public Result auditDetail(ResourcesQueryVo vo){
        Assert.notNull(vo.getId(), "申请单id不能为空");
        ResourcesApply apply = (ResourcesApply) baseDao.get(ResourcesApply.class,vo.getId());
        ResourcesAudit audit = (ResourcesAudit) baseDao.queryForObject("from ResourcesAudit where applyId=:applyId",this.addParam("applyId",vo.getId()).param());
        ResourcesApplyAuditVo resultVo = new ResourcesApplyAuditVo();
        BeanUtils.copyProperties(apply,resultVo);
        resultVo.setAuditTime(audit.getAuditTime());
        resultVo.setAuditOption(audit.getAuditOption());

        resultVo.setAuditUserName(getUserNameByUserId(audit.getAuditUserId()));
        resultVo.setApplyUserName(getUserNameByUserId(apply.getApplyUserId()));

        return Result.success(resultVo);
    }

    private String getUserNameByUserId(String userId){
        Map<String,String> params = new HashMap<>();
        params.put("id", userId);
        TSysAuthUser tSysAuthUser = (TSysAuthUser) sysAuthUserService.query(params);
        if(tSysAuthUser != null){
            return tSysAuthUser.getObjName();
        }
        return "";
    }

    public void audit(ResourcesApplyAuditVo vo){
        Assert.notNull(vo.getId(), "申请单id不能为空");
        ResourcesApply apply = (ResourcesApply) baseDao.get(ResourcesApply.class,vo.getId());
        Assert.notNull(apply, "申请单不存在");
        apply.setAuditState(vo.getAuditState());
        ResourcesAudit resourcesAudit = new ResourcesAudit();
        BeanUtils.copyProperties(vo,resourcesAudit,"id");
        resourcesAudit.setApplyId(vo.getId());
        resourcesAudit.setAuditTime(DateUtils.getCurrTime(2));
        if(ModelResourcesEnum.MODEL_PUBLISH.getCode().equals(apply.getApplyType())){
            if(ModelResourcesEnum.RESOURCE_AUDTI_AGREE.getCode().equals(vo.getAuditState())){//审批通过上架
                SupermarkModel markModel = modelSearchServiceImpl.querySupermarkModelByTransId(apply.getResourceId());
                markModel.setState(ModelStateEnum.UP.getValue());
                baseDao.update(markModel);
            }else{//审批不通过删除model数据
                myModelService.deleteMarkModelByTransId(apply.getResourceId());
            }
        }

        baseDao.save(resourcesAudit);

    }

    public void auditResultConfirm(String id){
        Assert.notNull(id, "申请单id不能为空");
        ResourcesApply apply = (ResourcesApply) baseDao.get(ResourcesApply.class,id);
        Assert.notNull(apply, "申请单不存在");
        apply.setAuditResultState(ModelResourcesEnum.AUDIT_CONFIRM.getCode());
        baseDao.saveOrUpdate(apply);
    }
}
