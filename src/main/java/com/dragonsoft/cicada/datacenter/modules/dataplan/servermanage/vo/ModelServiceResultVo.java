package com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.vo;

import com.code.common.operator.model.SimpleDataSet;
import com.code.common.paging.PageInfo;
import lombok.Data;

@Data
public class ModelServiceResultVo {

    private boolean isFail;
    private int statusCode;
    private SimpleDataSet operatorDataSet;
    private String requestId;
    private String msg;
    private PageInfo pageInfo;


    /*private String requestId;
    private String messageCode;
    private String message;
    private ResponseParam responseParam;
    private SimpleDataSet responseData;

    @Data
    public static class ResponseParam {
        private String totalCount;
        private String pageNumber;
        private String pageSize;
    }*/
}
