package com.dragonsoft.cicada.datacenter.modules.dataplan.externaldatasources.service;



import com.dragonsoft.cicada.datacenter.modules.dataplan.externaldatasources.vo.datawarehouse.ReleaseOutParamVo;

import java.util.List;

public interface IDataFilterService {

    /**
     *
     * @param dbType 数据库类型
     * @param tableName 表名
     * @param filterJson 查询条件
     * @param selectModel 输出参数
     * @return
     */
    public String getQueryExp(String dbType, String tableName, String filterJson, List<ReleaseOutParamVo> selectModel);

    String getVercationQueryExp(String dbType, String tableName, String filterJson, List<ReleaseOutParamVo> selectModel);
}
