package com.dragonsoft.cicada.datacenter.modules.firstpage.share.service;

import com.dragoninfo.dfw.entity.TSysAuthObj;
import com.dragonsoft.cicada.datacenter.modules.firstpage.share.vo.ResultResource;
import com.fw.service.IService;

import java.util.List;
import java.util.Map;

public interface ShareManagementService extends IService {

    /**
     * 获取我拥有的资源
     * @param resourceType
     * @param dataTreeId
     * @param resourceName
     * @param myUserId
     * @return
     */
    List<ResultResource> getMyResources(String resourceType, String dataTreeId, String resourceName,String myUserId);

    /**
     * 添加分享
     * @param objType
     * @param objIds
     * @param resourceType
     * @param resourceIds
     * @param myUserId
     */
    void addShareByCondition(String objType,List<String> objIds,String resourceType,List<String> resourceIds,String myUserId);

    /**
     * 取消分享
     * @param resources
     */
    void cancelSharesByCondition(List<Map<String,String>> resources);

    List<TSysAuthObj> getObjsByResourceId(String resourceId);
}
