package com.dragonsoft.cicada.datacenter.modules.metadata.service;

import com.code.common.encrypt.DragonEncryptor;
import com.code.common.utils.StringUtils;
import com.code.common.utils.assertion.Assert;
import com.code.metadata.res.structured.rdb.RdbCatalog;
import com.code.metadata.res.structured.rdb.RdbCatalogCluster;
import com.code.metadata.res.structured.rdb.RdbSchema;
import com.code.metaservice.datawarehouse.model.DataSourceVO;
import com.code.metaservice.datawarehouse.model.SchemaVo;
import com.code.metaservice.res.DataSourceType;
import com.code.metaservice.res.request.datasource.DataSourceRequest;
import com.code.metaservice.res.response.datasource.InsertDataSourceResponse;
import com.code.metaservice.res.structured.rdb.IRdbCatalogService;
import com.fw.service.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> Avizii
 * @Create : 2021.03.29
 */
@Service(mappingName = "rdbConnectService")
public class RdbConnectService extends DataBaseConnectService {

    @Autowired
    private IRdbCatalogService rdbCatalogService;

    @Override
    public DataSourceVO getDataSourceInfo(String id) {
        DragonEncryptor dragonEncryptor = new DragonEncryptor();
        RdbSchema rdbSchema = (RdbSchema) this.baseDao.get(RdbSchema.class, id);
        DataSourceVO dataSourceVO = new DataSourceVO();
        dataSourceVO.setConnType("JDBC");
        dataSourceVO.setUsername(dragonEncryptor.decrypt(rdbSchema.getUserName()));
        dataSourceVO.setPassword(dragonEncryptor.decrypt(rdbSchema.getPassword()));
        dataSourceVO.setPort(String.valueOf(rdbSchema.getCatalog().getPort()));
        dataSourceVO.setDbCode(rdbSchema.getCatalog().getCode());
        dataSourceVO.setDbName(rdbSchema.getName());
        dataSourceVO.setClusterName("1");
        dataSourceVO.setSoftwareId(rdbSchema.getCatalog().getSoftware().getId());
        RdbCatalogCluster rdbCatalogCluster = getRdbCatalogCluster(rdbSchema.getCatalog().getId());
        dataSourceVO.setIp(rdbCatalogCluster.getIPAddress());
        return dataSourceVO;
    }

    private RdbCatalogCluster getRdbCatalogCluster(String id) {
        String sql = "FROM RdbCatalogCluster where owner_id = :id";
        return (RdbCatalogCluster) this.baseDao.queryForObject(sql, addParam("id", id).param());
    }

    @Override
    public boolean testConnection(DataSourceVO dataSourceVO) {
        if (DataSourceType.ORACLE.name().equalsIgnoreCase(dataSourceVO.getDbType())) {
            List<SchemaVo> schemaVoList = findUserByDataSourceId(dataSourceVO.getId(), DataSourceType.valueOf(DataSourceType.ORACLE.name()));
            if (schemaVoList.size() > 0) {
                return rdbCatalogService.connect2(schemaVoList.get(0).getSchemaId(),
                        dataSourceVO.getSoftwareId(),
                        dataSourceVO.getIp(),
                        dataSourceVO.getPort(),
                        dataSourceVO.getDbCode(),
                        dataSourceVO.getUsername(),
                        dataSourceVO.getPassword(),
                        dataSourceVO.getDbType(),
                        dataSourceVO.getJdbcUrl());
            }
        }
        return rdbCatalogService.connect(dataSourceVO.getSoftwareId(),
                dataSourceVO.getIp(),
                dataSourceVO.getPort(),
                dataSourceVO.getDbCode(),
                dataSourceVO.getUsername(),
                dataSourceVO.getPassword(),
                dataSourceVO.getDbType(),
                dataSourceVO.getJdbcUrl());
    }

    @Override
    public InsertDataSourceResponse insertDataSource(DataSourceVO dataSourceVO) {
        DataSourceRequest dataSourceRequest = toDataSourceRequest(dataSourceVO);
//        RdbCatalog catalog = rdbCatalogService.getOrCreateWithName(dataSourceRequest.getCode(), dataSourceRequest.getSoftwareId(),
//                dataSourceRequest.getPort(), dataSourceRequest.getIp(), dataSourceRequest.getName());
        RdbCatalog catalog = new RdbCatalog();
        if (StringUtils.isNotBlank(catalog.getId())) {
            if (judgeNameAndClassify(dataSourceVO)) {
                Assert.fail("存在完全相同数据源配置信息，请修改数据库名称！");
            }
        }
        return rdbCatalogService.insertRdbDataSource(dataSourceRequest);
    }

    private boolean judgeNameAndClassify(DataSourceVO dataSourceVO) {
        String sql = "select count(1) count from t_md_dw_db_instance " +
                "where dir_id = :dirId  " +
                "and code = :dbName " +
                "and operate_user_id = :userId";

        String count = this.baseDao.sqlQueryForValue(sql,
                addParam("dirId", dataSourceVO.getClassifyId())
                        .addParam("dbName", dataSourceVO.getDbName())
                        .addParam("userId", dataSourceVO.getUserId()).param());
        if (count == null) {
            return false;
        }
        return Integer.valueOf(count) > 0 ? true : false;
    }

    @Override
    public void updateResource(DataSourceVO dataSourceVO) {
        rdbCatalogService.updateResourceInfo(toDataSourceRequest(dataSourceVO));
    }

    private List<SchemaVo> findUserByDataSourceId(String dataSourceId, DataSourceType dataSourceType) {
        List<RdbSchema> rdbSchemaList = rdbCatalogService.querySchemaByCatalogId(dataSourceId);
        return toSchemaVoList(rdbSchemaList);
    }

    private List<SchemaVo> toSchemaVoList(List<RdbSchema> rdbSchemaList) {
        List<SchemaVo> schemaVoList = new ArrayList<>(rdbSchemaList.size());
        for (RdbSchema rdbSchema : rdbSchemaList) {
            SchemaVo schemaVo = new SchemaVo();
            schemaVo.setCatalogCode(rdbSchema.getCatalog().getCode());
            schemaVo.setCatalogId(rdbSchema.getCatalog().getId());
            schemaVo.setCatalogName(rdbSchema.getCatalog().getName());
            schemaVo.setMasterIp(rdbSchema.getCatalog().getMaster().getIPAddress());
            schemaVo.setPassword(rdbSchema.getPassword());
            schemaVo.setPort(String.valueOf(rdbSchema.getCatalog().getPort()));
            schemaVo.setSchemaCode(rdbSchema.getCode());
            schemaVo.setSchemaId(rdbSchema.getId());
            schemaVo.setSchemaName(rdbSchema.getName());
            schemaVo.setUserName(rdbSchema.getUserName());
            schemaVo.setVersionId(rdbSchema.getCatalog().getSoftware().getId());
            schemaVo.setVersionType(rdbSchema.getCatalog().getSoftware().getSoftVersion());
            schemaVoList.add(schemaVo);
        }
        return schemaVoList;
    }
}
