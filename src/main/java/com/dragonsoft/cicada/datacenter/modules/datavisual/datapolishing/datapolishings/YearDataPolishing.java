package com.dragonsoft.cicada.datacenter.modules.datavisual.datapolishing.datapolishings;


import java.util.Calendar;

/**
 * <AUTHOR>
 * @Date 2021/3/16 10:04
 */
public class YearDataPolishing extends AbsDataPolishing {

    private final String YEAR_FORMAT = "yyyy";
    private final String YEAR = "年";

    @Override
    public void calculateDate() {

        if (minDate.length() != 4) {
            granularituFail(YEAR, YEAR_FORMAT, minDate);
        }
        if (maxDate.length() != 4) {
            granularituFail(YEAR, YEAR_FORMAT, maxDate);
        }
        this.dataPolishingList = getAllDatesBetweenTwoDates(minDate, maxDate, Calendar.YEAR, YEAR_FORMAT, YEAR);
    }

    @Override
    public void checkData(String data) {
        parseInt(data, YEAR_FORMAT);
    }
}
