package com.dragonsoft.cicada.datacenter.modules.datavisual.model;

import com.code.common.utils.StringUtils;
import com.code.common.utils.assertion.Assert;
import com.code.dragonsoft.dataquery.service.querymodel.ColumnDataModel;
import com.code.metadata.datavisual.WidgetDatasetDims;
import com.code.metadata.datavisual.WidgetDatasetDimsDrill;
import com.code.metadata.datavisual.WidgetDatasetMeasures;
import com.dragonsoft.cicada.datacenter.modules.datavisual.model.annotation.WidgetLabel;
import com.dragonsoft.cicada.datacenter.modules.datavisual.model.enums.WidgetType;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by zhuangjp on 2020/11/25 13:55
 */
@WidgetLabel(name = "组合图", type = WidgetType.Combination, describe = "组合图")
@Slf4j
public class CombinationWidget extends BarChartWidget {

    private BarChartData barChartData = new BarChartData();

    @Override
    public Map<String, Object> builderResult(ColumnDataModel columns, String code) {
        //添加维度
        WidgetDatasetDims dim1 = this.widgetDataset.getFromListDims().iterator().next();

        WidgetDatasetDimsDrill drill = null;
        if (this.widgetDataset.getFromListDimsDrill().size() > 0) {

            drill = this.widgetDataset.getFromListDimsDrill().iterator().next();
        }
        String fieldName = drill == null ? dim1.getFiledName() : drill.getFiledName();
        String fieldCode = drill == null ? dim1.getFiledCode() : drill.getFiledCode();
        this.barChartData.addColumns(fieldName);
        //添加度量
        List<WidgetDatasetMeasures> meas = this.widgetDataset.getFromListMeasures();

        //封装返回的数据
        for (Map<String, String> map : columns.getFieldValue()) {
            Map<String, String> rowMap = Maps.newLinkedHashMap();
            String val = String.valueOf(map.get(fieldCode));
            if (StringUtils.isNotBlank(val)) {
                rowMap.put(fieldName, val);
                for (WidgetDatasetMeasures mea : meas) {
                    if (!"false".equals(mea.getIsDivisor())) {
                        this.barChartData.addColumns(mea.getFiledName() == null ? mea.getFiledAlias() : mea.getFiledName());
                        String resCode = StringUtils.isNotBlank(mea.getFiledAlias()) ? mea.getFiledAlias() : mea.getFiledCode();
                        rowMap.put(mea.getFiledName(), map.get(resCode));
                    }
                }
                this.barChartData.rows.add(rowMap);
            }
        }
        List<String> dimsCode = this.widgetDataset.getFromListDims().stream().map(s -> s.getFiledName()).collect(Collectors.toList());

        //时间格式转换
        timeFormatConversion(dim1, drill);

        Map re = new HashMap();
        this.barChartData.setDimsCodes(dimsCode);
        re.put("code", 1);
        re.put("data", this.barChartData);
        return re;
    }

    private void timeFormatConversion(WidgetDatasetDims dim, WidgetDatasetDimsDrill drill) {
        //可视化的时间格式
        String visualTimeFormat = dim.getVisualTimeFormat();
        if (StringUtils.isBlank(visualTimeFormat)) {
            return;
        }
        String filedName = drill == null ? dim.getFiledName() : drill.getFiledName();
        //数据集的时间格式
//        String dataSetTimeFormat = dim.getDataSetTimeFormat();
        String dataSetTimeFormat = "";
        //通过码表获取的颗粒度
        String dateGranularity = dim.getFormatDateGranularity();
        //todo 时间字段限制只能码表上有的才会做格式转换，主要是为了通过码表做周（季度）和年（月、日）的区分。
        if (StringUtils.isBlank(dateGranularity)) return;
        for (Map row : this.barChartData.rows) {
            Object rowValue = row.get(filedName);
            //通过数据长度获取所需的数据格式
            dataSetTimeFormat = chooseFormat((String) rowValue, dateGranularity);
            //周和季度无法通过Date进行转换，需要特殊处理
            if ("当年周".equalsIgnoreCase(dateGranularity) || "周".equalsIgnoreCase(dateGranularity) || "季度".equalsIgnoreCase(dateGranularity)) {
                timeFormatByWeekAndQuarter(row, filedName, visualTimeFormat, dataSetTimeFormat, dateGranularity);
            } else {
                String format = getVisualFormat(dateGranularity, visualTimeFormat);
                SimpleDateFormat formatter = new SimpleDateFormat(format);
                if (rowValue instanceof Date || rowValue instanceof java.sql.Date) {
                    String dateFormat = formatter.format(rowValue);
                    row.put(filedName, dateFormat);
                    this.barChartData.formatMapping.put(dateFormat, rowValue);
                } else {
                    Date parse = dateParse(dataSetTimeFormat, (String) rowValue);
                    String dateFormat = formatter.format(parse);
                    row.put(filedName, dateFormat);
                    this.barChartData.formatMapping.put(dateFormat, rowValue);
                }
            }
        }
    }

    private String chooseFormat(String rowValue, String dateGranularity) {

        String resFormat = "";
        switch (rowValue.length()) {
            case 4:
                resFormat = "yyyy";
                break;
            case 6:
            case 5:
                resFormat = "yyyyMM";
                break;
            case 8:
                resFormat = "yyyyMMdd";
        }
        return resFormat;
    }

    private void timeFormatByWeekAndQuarter(Map row, String filedName, String visualTimeFormat, String dataSetTimeFormat, String dateGranularity) {
        String value = (String) row.get(filedName);
        String weekAndQuarter = "周";
        if (!dataSetTimeFormat.equalsIgnoreCase("yyyyMM")) {
            Assert.fail("目前仅支持周和季度的原始数据格式为：yyyyMM");
        }
        if (value.length() < 4) {
            Assert.fail(String.format("格式化转换失败！数据：%s", value));
        }
        String prefix = value.substring(0, 4);
        String suffix = value.substring(4);
        String dateFormat = "";
        if ("季度".equalsIgnoreCase(dateGranularity)) {
            weekAndQuarter = "季度";
        }
        dateFormat = getDateFormatByWeekAndQuarter(weekAndQuarter, visualTimeFormat, prefix, suffix, dateFormat);
        row.put(filedName, dateFormat);
        this.barChartData.formatMapping.put(dateFormat, value);
    }

    private Date dateParse(String dataSetTimeFormat, String fieldValue) {
        SimpleDateFormat dataFormatter = new SimpleDateFormat(dataSetTimeFormat);
        Date parse = null;
        try {
            parse = dataFormatter.parse(fieldValue);
        } catch (Exception e) {
            String expStr = String.format("暂时不支持对数据[%s]进行格式转换，仅支持yyyyMMdd格式的数据！", fieldValue);
            log.error(expStr + "--->" + e.getMessage(), e);
            Assert.fail(expStr);

        }

        return parse;
    }

    @NotNull
    private String getDateFormatByWeekAndQuarter(String weekAndQuarter, String visualTimeFormat, String prefix, String suffix, String dateFormat) {
        if ("yyyy-MM-dd".equalsIgnoreCase(visualTimeFormat)) {
            dateFormat = prefix + "-" + suffix;
        } else if ("yyyy年MM月dd日".equalsIgnoreCase(visualTimeFormat)) {
            dateFormat = prefix + "年第" + suffix + weekAndQuarter;
        } else if ("yyyy/MM/dd".equalsIgnoreCase(visualTimeFormat)) {
            dateFormat = prefix + "/" + suffix;
        } else if ("yyyyMMdd".equalsIgnoreCase(visualTimeFormat)) {
            dateFormat = prefix + "" + suffix;
        } else {
            Assert.fail(String.format("暂不支持[%s]格式", visualTimeFormat));
        }
        return dateFormat;
    }

    private String getVisualFormat(String dateGranularity, String visualTimeFormat) {
        String resValue = "";
        if ("yyyy-MM-dd".equalsIgnoreCase(visualTimeFormat)) {
            switch (dateGranularity) {
                case "年":
                    resValue = "yyyy";
                    break;
                case "月":
                    resValue = "yyyy-MM";
                    break;
                case "日":
                    resValue = "yyyy-MM-dd";
                    break;
            }
        } else if ("yyyy年MM月dd日".equalsIgnoreCase(visualTimeFormat)) {
            switch (dateGranularity) {
                case "年":
                    resValue = "yyyy年";
                    break;
                case "月":
                    resValue = "yyyy年MM月";
                    break;
                case "日":
                    resValue = "yyyy年MM月dd日";
                    break;
            }
        } else {
            //yyyy/MM/dd
            switch (dateGranularity) {
                case "年":
                    resValue = "yyyy";
                    break;
                case "月":
                    resValue = "yyyy/MM";
                    break;
                case "日":
                    resValue = "yyyy/MM/dd";
                    break;
            }
        }

        return resValue;
    }
}
