CREATE TABLE IF NOT EXISTS public.t_md_publish_plugin_attribute (
id varchar(32) NOT NULL,
plugin_id varchar(32) NOT NULL,
param_id varchar(32) NOT NULL,
param_type varchar(20)  DEFAULT NULL::character varying
)
WITH (OIDS=FALSE)

;
COMMENT ON COLUMN public.t_md_publish_plugin_attribute.id IS 'ID';
COMMENT ON COLUMN public.t_md_publish_plugin_attribute.plugin_id IS '插件ID';
COMMENT ON COLUMN public.t_md_publish_plugin_attribute.param_id IS '参数ID';

/*
Navicat PGSQL Data Transfer

Source Server         : *************
Source Server Version : 90424
Source Host           : *************:5432
Source Database       : bjx_0114
Source Schema         : public

Target Server Type    : PGSQL
Target Server Version : 90424
File Encoding         : 65001

Date: 2021-02-04 16:05:09
*/


-- ----------------------------
-- Table structure for t_trans_schedule
-- ----------------------------
DROP TABLE IF EXISTS "public"."t_trans_schedule";
CREATE TABLE "public"."t_trans_schedule" (
"id" varchar(255) COLLATE "default" NOT NULL,
"cron_expression" varchar(64) COLLATE "default",
"schedule_declare" varchar(512) COLLATE "default",
"schedule_type" varchar(20) COLLATE "default",
"task_id" varchar(32) COLLATE "default" NOT NULL,
"trans_id" varchar(32) COLLATE "default" NOT NULL,
"trigger_last_time" int8,
"trigger_next_time" int8,
"trigger_status" int4,
"subtrans_id" varchar(32) COLLATE "default",
"subtrans_delay_time" int8,
"subtrans_isenforce" int2,
"subtrans_enforce_num" int8
)
WITH (OIDS=FALSE)

;
COMMENT ON COLUMN "public"."t_trans_schedule"."subtrans_id" IS '子方案id';
COMMENT ON COLUMN "public"."t_trans_schedule"."subtrans_delay_time" IS '子方案延迟执行时间';
COMMENT ON COLUMN "public"."t_trans_schedule"."subtrans_isenforce" IS '子方案是否始终执行，1代表始终执行，0代表执行一次就不再执行';
COMMENT ON COLUMN "public"."t_trans_schedule"."subtrans_enforce_num" IS '子方案执行次数';

-- ----------------------------
-- Alter Sequences Owned By
-- ----------------------------

-- ----------------------------
-- Primary Key structure for table t_trans_schedule
-- ----------------------------
ALTER TABLE "public"."t_trans_schedule" ADD PRIMARY KEY ("id");


/*
Navicat PGSQL Data Transfer

Source Server         : *************
Source Server Version : 90424
Source Host           : *************:5432
Source Database       : bjx_0114
Source Schema         : public

Target Server Type    : PGSQL
Target Server Version : 90424
File Encoding         : 65001

Date: 2021-02-04 16:05:41
*/


-- ----------------------------
-- Table structure for t_trans_subtrans_relation
-- ----------------------------
DROP TABLE IF EXISTS "public"."t_trans_subtrans_relation";
CREATE TABLE "public"."t_trans_subtrans_relation" (
"id" varchar(255) COLLATE "default" NOT NULL,
"subtrans_delay_time" int4,
"subtrans_enforce_num" int8,
"subtrans_id" varchar(255) COLLATE "default",
"subtrans_isenforce" int4,
"trans_id" varchar(255) COLLATE "default"
)
WITH (OIDS=FALSE)

;

-- ----------------------------
-- Alter Sequences Owned By
-- ----------------------------

-- ----------------------------
-- Primary Key structure for table t_trans_subtrans_relation
-- ----------------------------
ALTER TABLE "public"."t_trans_subtrans_relation" ADD PRIMARY KEY ("id");

/*
Navicat PGSQL Data Transfer

Source Server         : *************
Source Server Version : 90424
Source Host           : *************:5432
Source Database       : bjx_0114
Source Schema         : public

Target Server Type    : PGSQL
Target Server Version : 90424
File Encoding         : 65001

Date: 2021-02-04 16:05:50
*/


-- ----------------------------
-- Table structure for t_trans_task
-- ----------------------------
DROP TABLE IF EXISTS "public"."t_trans_task";
CREATE TABLE "public"."t_trans_task" (
"id" varchar(255) COLLATE "default" NOT NULL,
"run_param" text COLLATE "default",
"sub_task_id" varchar(32) COLLATE "default",
"trans_id" varchar(32) COLLATE "default" NOT NULL,
"trans_name" varchar(128) COLLATE "default"
)
WITH (OIDS=FALSE)

;

-- ----------------------------
-- Alter Sequences Owned By
-- ----------------------------

-- ----------------------------
-- Primary Key structure for table t_trans_task
-- ----------------------------
ALTER TABLE "public"."t_trans_task" ADD PRIMARY KEY ("id");


/*
Navicat PGSQL Data Transfer

Source Server         : *************
Source Server Version : 90424
Source Host           : *************:5432
Source Database       : bjx_0114
Source Schema         : public

Target Server Type    : PGSQL
Target Server Version : 90424
File Encoding         : 65001

Date: 2021-02-04 16:05:27
*/


-- ----------------------------
-- Table structure for t_trans_job
-- ----------------------------
DROP TABLE IF EXISTS "public"."t_trans_job";
CREATE TABLE "public"."t_trans_job" (
"id" varchar(255) COLLATE "default" NOT NULL,
"disabled" bool,
"end_time" timestamp(6),
"execute_status" varchar(255) COLLATE "default",
"job_detail" text COLLATE "default",
"schedule_id" varchar(32) COLLATE "default",
"start_time" timestamp(6),
"task_id" varchar(32) COLLATE "default" NOT NULL,
"trans_id" varchar(32) COLLATE "default" NOT NULL,
"trans_name" varchar(32) COLLATE "default"
)
WITH (OIDS=FALSE)

;

-- ----------------------------
-- Alter Sequences Owned By
-- ----------------------------

-- ----------------------------
-- Primary Key structure for table t_trans_job
-- ----------------------------
ALTER TABLE "public"."t_trans_job" ADD PRIMARY KEY ("id");
