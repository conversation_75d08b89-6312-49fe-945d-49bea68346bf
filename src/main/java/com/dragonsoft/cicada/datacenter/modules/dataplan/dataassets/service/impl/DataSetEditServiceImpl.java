package com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.code.common.AliasUtil;
import com.code.common.bean.BeanFactory;
import com.code.common.dataset.DataStepRelationOperator;
import com.code.common.dataset.parse.MergeSQLParse;
import com.code.common.dataset.parse.WhereSQLParse;
import com.code.common.dataset.sql.SQL;
import com.code.common.dataset.type.field.FieldInfoConverterEnum;
import com.code.common.dataset.type.field.IFieldInfoConverter;
import com.code.common.dataset.type.field.TableNameUtil;
import com.code.common.dataset.util.UnionUtil;
import com.code.common.model.step.BaseDataStep;
import com.code.common.paging.PageInfo;
import com.code.common.utils.BeanUtils;
import com.code.common.utils.StringUtils;
import com.code.common.utils.assertion.Assert;
import com.code.dataset.AbstractAtomDataSetOperator;
import com.code.dataset.BaseMergeOperator;
import com.code.dataset.BaseWhereOperator;
import com.code.dataset.IStepRelationService;
import com.code.dataset.operator.OperatorEnum;
import com.code.dataset.operator.column.addcolumn.AddColumnOperator;
import com.code.dataset.operator.column.addcolumn.AddColumnStep;
import com.code.dataset.operator.column.deletecolumn.DeleteColumnOperator;
import com.code.dataset.operator.column.deletecolumn.DeleteColumnStep;
import com.code.dataset.operator.column.editcolumn.EditColumnOperator;
import com.code.dataset.operator.column.editcolumn.EditColumnStep;
import com.code.dataset.operator.column.format.FormatColumnOperator;
import com.code.dataset.operator.column.format.FormatStep;
import com.code.dataset.operator.column.indextype.IndexTypeOperator;
import com.code.dataset.operator.column.indextype.IndexTypeStep;
import com.code.dataset.operator.column.numberformat.NumberFormatOperator;
import com.code.dataset.operator.column.numberformat.NumberFormatStep;
import com.code.dataset.operator.column.synccolumn.SyncColumnOperator;
import com.code.dataset.operator.column.synccolumn.SyncColumnStep;
import com.code.dataset.operator.column.synccolumn.vo.LogicColumnInfoVo;
import com.code.dataset.operator.column.synccolumn.vo.LogicSyncColumn;
import com.code.dataset.operator.filter.FilterConditionOperator;
import com.code.dataset.operator.filter.FilterConditionStep;
import com.code.dataset.operator.join.JoinTableOperator;
import com.code.dataset.operator.join.JoinTableStep;
import com.code.dataset.operator.join.UnionTableOperator;
import com.code.dataset.operator.join.UnionTableStep;
import com.code.dataset.operator.join.vo.MetaColumnInfo;
import com.code.dataset.operator.join.vo.UnionColumn;
import com.code.dataset.operator.join.vo.UnionParam;
import com.code.dragonsoft.dataquery.service.DDLOperationService;
import com.code.dragonsoft.dataquery.service.QueryDataService;
import com.code.dragonsoft.dataquery.service.querymodel.ColumnPageInfo;
import com.code.metadata.model.core.DataType;
import com.code.metadata.model.core.ModelElement;
import com.code.metadata.res.common.ClassifierStat;
import com.code.metadata.res.ddl.*;
import com.code.metadata.res.structured.rdb.RdbDataObj;
import com.code.metadata.variable.TransVariable;
import com.code.metaservice.base.typemapping.TypeSystemService;
import com.code.metaservice.datawarehouse.IDataWarehouseService;
import com.code.metaservice.ddl.*;
import com.code.metaservice.ddl.vo.LogicDataSetColumnVo;
import com.code.metaservice.ddl.vo.LogicHttpColumns;
import com.code.metaservice.res.common.ClassifierStatService;
import com.code.metaservice.util.LogicDataObjectUtil;
import com.code.metaservice.variable.ITransVariableService;
import com.code.plugin.db.TypeMapping;
import com.code.std.types.NonStandardType;
import com.code.std.types.StandardType;
import com.dragoninfo.dfw.bean.Result;
import com.dragonsoft.cicada.datacenter.common.utils.GetDataPropertiesUtil;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.common.DataSetStepContext;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.common.LogicDataSetConstant;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.common.UrlConfig;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.enums.EnumFunction;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.qo.LogicDataExportQo;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.service.IDataSetEditService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.util.DataSetUtils;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.vo.ElementUrlVO;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.vo.LogicDataSet.*;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.vo.StepVo;
import com.fw.service.BaseService;
import com.fw.service.annotation.Service;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;

import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@Scope("prototype")
public class DataSetEditServiceImpl extends BaseService implements IDataSetEditService {

    private static final List<String> NUMBERTYPE = Lists.newArrayList("INT2", "INT4", "INT8", "INT", "FLOAT4", "INT", "FLOAT8", "STD.BIGINTEGER");

    @Autowired
    BeanFactory beanFactory;

    @Autowired
    IStepRelationService stepRelationService;

    @Autowired
    ILogicDataColumnService logicDataColumnService;

    @Autowired
    IDataSetStepMetaService dataSetStepMetaService;

    @Autowired
    ILogicDataObjService logicDataObjService;
    @Autowired
    IDataSetStepService dataSetStepService;

    @Autowired
    IDataSetSyncService setSyncService;

    @Autowired
    IDataWarehouseService dataWarehouseService;
    @Autowired
    TypeSystemService typeSystemService;
    @Autowired
    DDLOperationService ddlOperationService;

    @Autowired
    QueryDataService queryDataService;

    @Autowired
    private ClassifierStatService classifierStatService;

    @Autowired
    private DataSetStepContext dataSetStepContext;

    @Autowired
    private ITransVariableService transVariableService;

    @Autowired
    private GetDataPropertiesUtil getDataPropertiesUtil;

    @Autowired
    private UrlConfig urlConfig;


    @Override
    public String createLogicDataSet(String id, String dsTypeId, String name, String userId) {
        String dataSetId = saveLogicDataSet(name, userId);
        //建立目录树和数据集的关系
        saveRelevancy(Lists.newArrayList(id, dsTypeId), dataSetId);
        return dataSetId;
    }

    private void saveRelevancy(List<String> ids, String dataSetId) {

        String sql = "insert into t_md_classify_element values (:dataSetId,:id)";
        Map<String, Object> map;
        for (String id : ids) {
            map = Maps.newHashMap();
            map.put("id", id);
            map.put("dataSetId", dataSetId);
            this.baseDao.executeSqlUpdate(sql, map);
        }

    }

    private String saveLogicDataSet(String name, String userId) {
        LogicDataObj logicDataObj = new LogicDataObj();
        logicDataObj.setName(name);
        logicDataObj.setCode(name);
        logicDataObj.setUserId(userId);
        logicDataObj.setOperateUserId(userId);
        logicDataObj.setType(LogicDataSetConstant.LOGIC_DATA_SET_TYPE);
        logicDataObj.setIsFast(LogicDataSetConstant.NOT_FAST);
        //自助数据集初始化创建的时候，不要塞belong_type,点击保存的时候再去塞，这时候列表显示会根据这个是否有值去过滤显示，所以在这边注释掉了。
        //logicDataObj.setBelongType(LogicDataSetConstant.BELONG_TYPE);
        this.baseDao.save(logicDataObj);
        return logicDataObj.getId();
    }

    @Override
    public LogicDataSetVo getLogicDataInfo(String id, String currentDataSetId) {
        if (StringUtils.isBlank(id)) {
            //编辑
            //获取数据集的字段
            return getDataColumnInfo(currentDataSetId);
        } else {
            List<LogicDataColumn> queryList = Lists.newArrayList();
            //新增
            queryList.addAll(getLogicDataColumns(id));
            //获取新增的字段
            return getLogicDataSetVo(queryList, id, currentDataSetId);
        }
    }

    private LogicDataSetVo getDataColumnInfo(String currentDataSetId) {
        TypeMapping typeMapping = (TypeMapping) beanFactory.getBean("typeMapping");
        Map<String, String> allDataType = getAllDataType();
        LogicDataObj dataObjById = logicDataObjService.findLogicDataObjById(currentDataSetId);
        LogicDataSetVo logicDataSetVo = new LogicDataSetVo();
        logicDataSetVo.setCode(dataObjById.getCode());
        logicDataSetVo.setDataSetId(dataObjById.getId());
        logicDataSetVo.setName(dataObjById.getName());
        logicDataSetVo.setRelationId(dataObjById.getStepRelationId());
        String belongType = dataObjById.getBelongType();
        if (belongType.equals(LogicDataObj.EnumDataBelongType.SELF_HELP.name())) {
            logicDataSetVo.setDwInstanceId(logicDataObjService.getDataSetCatalogId(currentDataSetId));
        }
        List<LogicDataColumn> columns = logicDataColumnService.findLogicDataColumnsByLogicDataObjId(currentDataSetId);
        Map<String, Map<String, List<LogicSyncColumn>>> maps = Maps.newHashMap();
        List<LogicSyncColumn> pks = new ArrayList<>();
        for (LogicDataColumn column : columns) {
            //获取获取字段来源的数据集
            Map<String, List<LogicSyncColumn>> parentMap = maps.get(column.getBelongParentId());
            if (parentMap == null) {
                parentMap = Maps.newHashMap();
            }
            if (column.getIsPk() != null && column.getIsPk()) {
                LogicSyncColumn pk = new LogicSyncColumn();
                pk.setCode(column.getCode());
                pk.setId(column.getId());
                pks.add(pk);
            }
            if (LogicDataObjectUtil.DIMENSION.equals(column.getIndexType())) {
                List<LogicSyncColumn> dimension = parentMap.get(LogicDataSetConstant.DIMENSION);
                if (dimension == null) {
                    dimension = Lists.newArrayList();
                }
                LogicSyncColumn logicColumnInfoVo = getLogicColumnInfoVo(column, column.getId());
                if (Objects.equals(dataObjById.getBelongType(), "QUICK_SQL")) {
                    NonStandardType nonStandardType = DataSetUtils.changType(dataObjById.getDbType(), column.getDataType().getCode());
                    StandardType trans = typeMapping.trans(nonStandardType);
                    String dataTypeId = allDataType.get(trans.getCode());
                    logicColumnInfoVo.setDataTypeId(dataTypeId);
                    logicColumnInfoVo.setDataType(trans.getCode());
                }
                dimension.add(logicColumnInfoVo);
                parentMap.put(LogicDataSetConstant.DIMENSION, dimension);
            } else {
                List<LogicSyncColumn> measure = parentMap.get(LogicDataSetConstant.MEASURE);
                if (measure == null) {
                    measure = Lists.newArrayList();
                }
                LogicSyncColumn logicColumnInfoVo = getLogicColumnInfoVo(column, column.getId());
                if (Objects.equals(dataObjById, "QUICK_SQL")) {
                    NonStandardType nonStandardType = DataSetUtils.changType(dataObjById.getDbType(), column.getDataType().getCode());
                    StandardType trans = typeMapping.trans(nonStandardType);
                    String dataTypeId = allDataType.get(trans.getCode());
                    logicColumnInfoVo.setDataTypeId(dataTypeId);
                    logicColumnInfoVo.setDataType(trans.getCode());
                }
                measure.add(logicColumnInfoVo);
                parentMap.put(LogicDataSetConstant.MEASURE, measure);
            }

            maps.put(column.getBelongParentId(), parentMap);
        }

        //封装vo
        List<ColumnDataSetVo> columnDataSetVos = Lists.newArrayList();
        for (String key : maps.keySet()) {
            if (StringUtils.isBlank(key)) {
                continue;
            }
            ClassifierStat objById = logicDataObjService.findClassifier(key);

            Assert.notNull(objById, "数据集来源被删除！");

            ColumnDataSetVo columnDataSetVo = new ColumnDataSetVo();
            columnDataSetVo.setName(objById.getName());
            columnDataSetVo.setCode(objById.getCode());
            columnDataSetVo.setId(objById.getId());
            //字段信息
            Map<String, List<LogicSyncColumn>> listMap = maps.get(key);
            columnDataSetVo.setDimension(listMap.get(LogicDataSetConstant.DIMENSION));
            columnDataSetVo.setMeasure(listMap.get(LogicDataSetConstant.MEASURE));
            columnDataSetVo.setPks(pks);
            columnDataSetVos.add(columnDataSetVo);
        }

        logicDataSetVo.setColumns(columnDataSetVos);
        return logicDataSetVo;
    }

    private List<LogicDataColumn> getLogicDataColumns(String id) {
        String hql = "FROM LogicDataColumn WHERE ownerId = :id";
        return this.baseDao.queryForList(hql, addParam("id", id).param());
    }

    private LogicDataSetVo getLogicDataSetVo(List<LogicDataColumn> queryList, String id, String currentDataSetId) {
        if (queryList.isEmpty()) {
            return null;
        }
        //获取当前数据集的信息
        LogicDataSetVo logicDataSetVo = getLogicDataSet(currentDataSetId);

        //维度
        List<LogicSyncColumn> dimension = Lists.newArrayList();
        //度量
        List<LogicSyncColumn> measure = Lists.newArrayList();


        List<ColumnDataSetVo> columnDataSetList = Lists.newArrayList();

        //封装数据集信息
        LogicDataObj dataObjById = logicDataObjService.findLogicDataObjById(id);
        ColumnDataSetVo columnDataSetVo = new ColumnDataSetVo();
        columnDataSetVo.setCode(dataObjById.getCode());
        columnDataSetVo.setId(dataObjById.getId());
        columnDataSetVo.setName(dataObjById.getName());

        for (LogicDataColumn logicDataColumn : queryList) {
            //保存添加的字段
            LogicDataColumn column = getNewColumn(logicDataColumn, currentDataSetId, id);
            String columnId = column.getId();

            if (LogicDataObjectUtil.DIMENSION.equals(logicDataColumn.getIndexType())) {
                dimension.add(getLogicColumnInfoVo(logicDataColumn, columnId));
            } else {
                measure.add(getLogicColumnInfoVo(logicDataColumn, columnId));
            }
        }
        columnDataSetVo.setDimension(dimension);
        columnDataSetVo.setMeasure(measure);

        columnDataSetList.add(columnDataSetVo);
        logicDataSetVo.setColumns(columnDataSetList);
        return logicDataSetVo;
    }

    private String changeType(LogicDataColumn column, TypeMapping typeMapping, LogicDataObj logicDataObj) {
        if (Objects.equals(logicDataObj.getBelongType(), "QUUICK_SQL")) {
            NonStandardType type = new NonStandardType(logicDataObj.getDbType().equals("hwmpp") ? "hwmpp." + column.getDataType().getCode() : logicDataObj.getDbType().toUpperCase() + "." + column.getDataType().getCode(), column.getDataType().getCode(), 0, 0);
            StandardType trans = typeMapping.trans(type);
            return trans.getCode();
        }
        return column.getCode();
    }

    private LogicSyncColumn getLogicColumnInfoVo(LogicDataColumn logicDataColumn, String columnId) {
        LogicSyncColumn columnInfoVo = new LogicSyncColumn();
        columnInfoVo.setId(columnId);
        columnInfoVo.setBelongParentId(logicDataColumn.getBelongParentId());
        columnInfoVo.setFuncExp(logicDataColumn.getFuncExp());
        columnInfoVo.setDisplayTypeId(logicDataColumn.getDisplayTypeId());
        columnInfoVo.setColumnAlias(StringUtils.isBlank(logicDataColumn.getAlias()) ? logicDataColumn.getCode() : logicDataColumn.getAlias());
        columnInfoVo.setDataTypeId(logicDataColumn.getDataType().getId());
        columnInfoVo.setName(logicDataColumn.getName());
        columnInfoVo.setNumberFormat(logicDataColumn.getNumberFormat());
        columnInfoVo.setFormat(logicDataColumn.getFormat());
        columnInfoVo.setCode(logicDataColumn.getCode());
        columnInfoVo.setIndexType(logicDataColumn.getIndexType());
        columnInfoVo.setMemo(logicDataColumn.getMemo());
        columnInfoVo.setDataType(logicDataColumn.getDataType().getCode());
        return columnInfoVo;
    }

    private LogicDataSetVo getLogicDataSet(String id) {
        LogicDataSetVo logicDataSetVo = new LogicDataSetVo();
        LogicDataObj logicDataObj = (LogicDataObj) this.baseDao.get(LogicDataObj.class, id);
        logicDataObj.setOperateTime(DateUtil.format(LocalDateTime.now(), LogicDataSetConstant.DATE_FORMAT));
        logicDataSetVo.setName(logicDataObj.getName());
        logicDataSetVo.setCode(logicDataObj.getCode());
        logicDataSetVo.setRelationId(logicDataObj.getStepRelationId());
        logicDataSetVo.setDataSetId(id);
        return logicDataSetVo;
    }

    @Override
    public void editDataSetColumn(EditColumnStep editColumnStep) {
        checkColumnName(editColumnStep.getShowColumnName(), editColumnStep.getDataSetId(), editColumnStep.getColumnId());
        checkAlias(editColumnStep);
        editColumnStep.setCode(LogicDataSetConstant.STEP_EDIT_COLUMN);//editColumn增加edit步驟
        EditColumnOperator editColumnOperator = (EditColumnOperator) OperatorEnum.getOperator(LogicDataSetConstant.STEP_EDIT_COLUMN, beanFactory);
        editColumnOperator.init(editColumnStep);

        //获取步骤
        List<AbstractAtomDataSetOperator> setOperators = getSetOperators(editColumnStep.getDataSetId());
        setOperators.add(editColumnOperator);

        editColumnOperator.exec(editColumnStep);
        saveOperatorsByRelation(editColumnOperator, editColumnStep.getDataSetId());


    }

    private void saveOperatorsByRelation(AbstractAtomDataSetOperator operator, String dataSetId) {
        LogicDataObj obj = logicDataObjService.findLogicDataObjById(dataSetId);
        if (StringUtils.isBlank(obj.getStepRelationId())) {
            DataSetStepRelation upsert = stepRelationService.upsert(new ArrayList<>(Arrays.asList(operator)), null);
            obj.setStepRelationId(upsert.getId());
        } else {
            stepRelationService.saveOperators(operator, obj.getStepRelationId());
        }
    }

    private void saveOperatorsByRelation(List<AbstractAtomDataSetOperator> operators, String dataSetId) {
        LogicDataObj obj = logicDataObjService.findLogicDataObjById(dataSetId);
        if (StringUtils.isBlank(obj.getStepRelationId())) {
            DataSetStepRelation upsert = stepRelationService.upsert(operators, null);
            obj.setStepRelationId(upsert.getId());
        } else {
            stepRelationService.saveOperators(operators, obj.getStepRelationId());
        }
    }

    private void checkAlias(EditColumnStep step) {
        String columnAlias = step.getColumnAlias();
        String dataSetId = step.getDataSetId();
        String columnId = step.getColumnId();
        if (StringUtils.isBlank(columnAlias)) {
            return;
        }
        List<LogicDataColumn> columns = logicDataColumnService.findLogicDataColumnsByLogicDataObjId(dataSetId);
        for (LogicDataColumn column : columns) {
            String alias = StringUtils.isNotBlank(column.getAlias()) ? column.getAlias() : column.getCode();
            if (columnAlias.equals(alias) && !columnId.equals(column.getId())) {
                Assert.fail("别名[" + columnAlias + "]重复");
            }
        }
    }

    public void stepRegisterTable(List<AbstractAtomDataSetOperator> setOperators, String dataSetId, boolean isCreate) {
        LogicDataObj dataObj = logicDataObjService.findLogicDataObjById(dataSetId);
        String sql = stepRelationService.getSQL(setOperators, null);
        try {
            registerTable(dataObj, StringUtils.isNotBlank(sql) ? sql : dataObj.getSql(), isCreate);
        } catch (Exception e) {
            log.error("视图创建失败！", e);
            Assert.fail("视图创建失败！");
        }
    }

    @Override
    public void indexType(IndexTypeStep indexTypeStep) {
        indexTypeStep.setCode(LogicDataSetConstant.STEP_INDEX_TYPE);
        IndexTypeOperator indexTypeOperator = (IndexTypeOperator) OperatorEnum.getOperator(LogicDataSetConstant.STEP_INDEX_TYPE, beanFactory);

        indexTypeOperator.init(indexTypeStep);

//        List<AbstractAtomDataSetOperator> setOperators = getSetOperators(indexTypeStep.getDataSetId());
//        setOperators.add(indexTypeOperator);

        indexTypeOperator.exec(indexTypeStep);
//        saveRelation(indexTypeStep.getDataSetId(), setOperators);
        saveOperatorsByRelation(indexTypeOperator, indexTypeStep.getDataSetId());

    }

    @Override
    public LogicSyncColumn addDataSetColumn(AddColumnStep addColumnStep) {

        checkColumnName(addColumnStep.getColumnName(), addColumnStep.getDataSetId(), "");

        checkColumnCode(addColumnStep);
//        String columnCode = getColumnCode();
        addColumnStep.setCode(LogicDataSetConstant.STEP_ADD_COLUMN);
        addColumnStep.setExp(changExp(addColumnStep));

        AddColumnOperator addColumnOperator = (AddColumnOperator) OperatorEnum.getOperator(LogicDataSetConstant.STEP_ADD_COLUMN, beanFactory);
        addColumnOperator.init(addColumnStep);

        //获取当前数据集的所有步骤
        List<AbstractAtomDataSetOperator> setOperators = getSetOperators(addColumnStep.getDataSetId());
        setOperators.add(addColumnOperator);

        LogicDataColumn logicDataColumn = addColumnOperator.getLogicDataColumn(addColumnStep);
        saveOperatorsByRelation(addColumnOperator, addColumnStep.getDataSetId());
//        saveRelation(addColumnStep.getDataSetId(), setOperators);

        String id = logicDataColumnService.saveLogicDataObjColumn(logicDataColumn);
        LogicDataObj obj = logicDataObjService.findLogicDataObjById(addColumnStep.getDataSetId());
        obj.setOperateTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern(LogicDataSetConstant.DATE_FORMAT)));
        obj.setBelongType(LogicDataSetConstant.BELONG_TYPE);
        //stepRegisterTable(setOperators, addColumnStep.getDataSetId(), false);

        return getLogicColumnInfoVo(logicDataColumn, id);
    }

    private void checkColumnCode(AddColumnStep addColumnStep) {
        String columnCode = addColumnStep.getColumnCode();
        String dataSetId = addColumnStep.getDataSetId();
        if (StringUtils.isBlank(columnCode)) Assert.fail("字段Code不能为空！");
        List<LogicDataColumn> columns = logicDataColumnService.findLogicDataColumnsByLogicDataObjId(dataSetId);
        for (LogicDataColumn column : columns) {
            String resCode = StringUtils.isBlank(column.getAlias()) ? column.getCode() : column.getAlias();
            if (resCode.equalsIgnoreCase(columnCode)) {
                Assert.fail("字段 Code[" + columnCode + "]已经存在，请重新输入！");
            }
        }
    }

    private String changExp(AddColumnStep addColumnStep) {
        List<String> list = JSON.parseObject(addColumnStep.getExpColumnIds(), List.class);
        String exp = addColumnStep.getExp();
        for (String s : list) {
            LogicDataColumn column = logicDataColumnService.findLogicDataColumnById(s);
            if (StringUtils.isNotBlank(column.getName())) {
                exp = exp.replaceAll(column.getName(), column.getCode());
            }
        }
        return exp;
    }


    private String getColumnCode() {
        String resCode = LogicDataSetConstant.ROW;
        return resCode + "_" + System.currentTimeMillis();
    }

    private void checkColumn(String columnName, String dataSetId, String sql, String columnId) {
        int count = Integer.parseInt(this.baseDao.sqlQueryForValue(sql, addParam("id", dataSetId).addParam("name", columnName).addParam("columnid", columnId).param()));
        checkResponse(columnName, count);
    }

    private void checkColumn(String columnName, String dataSetId, String sql) {
        int count = Integer.parseInt(this.baseDao.sqlQueryForValue(sql, addParam("id", dataSetId).addParam("name", columnName).param()));
        checkResponse(columnName, count);
    }

    private void checkResponse(String columnName, int count) {
        if (count > 0) {
            Assert.fail("字段[" + columnName + "]已经存在，请重新输入！");
        }
    }

    private void checkColumnCode(String columnName, String dataSetId) {
        String sql = "select count(1) from t_md_logic_data_column where owner_id = :id and code = :name";
        checkColumn(columnName, dataSetId, sql);
    }

    private void checkColumnName(String columnName, String dataSetId, String columnId) {
        List<LogicDataColumn> columns = logicDataColumnService.findLogicDataColumnsByLogicDataObjId(dataSetId);
        for (LogicDataColumn column : columns) {
            String resCode = StringUtils.isBlank(column.getAlias()) ? column.getName() : column.getAlias();
            if (StringUtils.isBlank(resCode)) {
                resCode = column.getCode();
            }
            boolean checkColumnId = true;
            if (StringUtils.isNotBlank(columnId)) {
                checkColumnId = (!columnId.equalsIgnoreCase(column.getId()));
            }
            if (checkColumnId && resCode.equalsIgnoreCase(columnName)) {
                Assert.fail("字段[" + columnName + "]已经存在，请重新输入！");
            }
        }
    }

    @Override
    public PageInfo preview(PreviewVo previewVo) {
        String searchSQL = getQuerySQL(previewVo.getDataSetId());
        searchSQL = transTransVar(searchSQL, previewVo.getUserId());
        return previewBySql(previewVo, searchSQL);
    }

    @Override
    public PageInfo previewBySql(PreviewVo previewVo) {

        ClassifierStat classifierStat = getClassifierStat(previewVo.getClassifyStatId());

        String sql = buildSql(previewVo.getTableName(), previewVo.getColumnRefs(), previewVo.getCondition(), previewVo.getDataSetJoinVo(), classifierStat);
        //转换变量参数
        sql = transTransVar(sql, previewVo.getUserId());
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageSize(previewVo.getPageSize());
        pageInfo.setPageIndex(previewVo.getPage());
        getSqlColumnsForPageInfoDbType(sql, pageInfo, null, classifierStat);
        //转换列名
        if (!Objects.equals("column", previewVo.getTitleType())) {
            List<Map<String, Object>> list = pageInfo.getDataList();
            transColumnName(list, previewVo.getColumnRefs());
        }
        //字段值统一改为String类型
        return pageInfo;
    }

    public ClassifierStat getClassifierStat(String classifierStatId) {
        ClassifierStat classifierStat = null;
        RdbDataObj obj = (RdbDataObj) this.baseDao.get(RdbDataObj.class, classifierStatId);
        if (obj != null) {
            classifierStat = obj;
        } else {
            classifierStat = (ClassifierStat) this.baseDao.get(ClassifierStat.class, classifierStatId);
        }
        if (classifierStat == null) {
            return null;
        }
        return classifierStat;
    }

    public String buildSql(String tableName, List<ColumnRefVO> columnRefs, String condition, List<JoinTableStep> dataSetJoinVo, ClassifierStat classifierStat) {
        IFieldInfoConverter codeConverter = FieldInfoConverterEnum.getConverter(classifierStat.getDbType());
        StringBuilder sb = new StringBuilder();
        String newExp = "";
        String columnCode = "";
        String objAlias;
        for (ColumnRefVO c : columnRefs) {
            //这边生成字段要考虑到别名
            LogicDataColumn column = new LogicDataColumn();
            BeanUtils.simpleCopyProperties(c, column);
            DataType dataType = new DataType();
            dataType.setId(c.getDataTypeId());
            column.setDataType(dataType);
            if (StringUtils.isBlank(c.getObjAlias())) {
                newExp = codeConverter.typeConversion(c.getCode(), column);
            } else {
                objAlias = TableNameUtil.splitScheme(c.getObjAlias());
                columnCode = codeConverter.buildFiledAlias(objAlias, column.getCode());
                newExp = codeConverter.typeConversion(columnCode, column);
            }
            sb.append(newExp).append(",");
        }
        sb.deleteCharAt(sb.lastIndexOf(","));
        //如何加入条件过滤的
        SQL sql = SQL.create();
        sql.select(sb.toString());
        String dbType = classifierStat.getDbType();
        AbstractAtomDataSetOperator op = null;
        if (CollectionUtils.isNotEmpty(dataSetJoinVo)) {
            MergeSQLParse mergeSQLParse = null;
            for (JoinTableStep joinTableStep : dataSetJoinVo) {
                mergeSQLParse = new MergeSQLParse();
                op = buildSqlOperator(classifierStat, joinTableStep, dbType, OperatorEnum.joinTable.name());
                mergeSQLParse.complied((BaseMergeOperator) op, sql);
            }
        }
        if (StringUtils.isNotBlank(condition)) {
            FilterConditionStep step = new FilterConditionStep();
            step.setCondition(condition);
            op = buildSqlOperator(classifierStat, step, classifierStat.getDbType(), OperatorEnum.filterCondition.name());
            WhereSQLParse whereSQLParse = new WhereSQLParse();
            whereSQLParse.complied((BaseWhereOperator) op, sql);
        }
        return sql.getCompiledSelect(tableName, new boolean[]{true});
    }

    @Override
    public String transTransVar(String sql, String userId) {
        List<TransVariable> transVariables = transVariableService.queryByUser(userId);
        //转换成map
        if (CollectionUtils.isNotEmpty(transVariables)) {
            for (TransVariable var : transVariables) {
                //把单引号给去掉
                sql = sql.replace("'".concat(var.getParamCode()).concat("'"), var.getParamCode());
                sql = sql.replace(var.getParamCode(), var.getParamValue());
            }
        }
        return sql;
    }


    public void transColumnName(List<Map<String, Object>> dataList, List<ColumnRefVO> columnRefs) {
        Map<String, String> ref = columnRefs.stream().collect(Collectors.toMap(k -> null == k.getCode() ? "" : k.getCode(),
                v -> null == v.getName() ? null == v.getCode() ? "" : v.getCode() : v.getName(), (o1, o2) -> o1));
        dataList.forEach(l -> {
            for (Map.Entry<String, Object> entry : l.entrySet()) {
                //与保存后的预览一致,把所有兼职都转换成String类型
                if (null != entry.getValue()) {
                    entry.setValue(String.valueOf(entry.getValue()));
                }
            }
            ref.forEach((k, v) -> {
                if (!Objects.equals(k, v) && l.containsKey(k)) {
                    l.put(v, l.get(k));
                    l.remove(k);
                }
            });

        });
    }


    @Override
    public PageInfo preview(SelfHelpDataSetPreviewByStepVo previewVo) {
        List<AbstractAtomDataSetOperator> setOperators = new ArrayList<>();
        //别名未设置时设置别名
        previewVo.getDataSetJoinVo().forEach(r -> {
            if (StringUtils.isBlank(r.getSourceDataSetAlias())) {
                r.setSourceDataSetAlias(r.getSourceDataSetCode());
            }
            if (StringUtils.isBlank(r.getTargetDataSetAlias())) {
                r.setTargetDataSetAlias(r.getTargetDataSetCode());
            }
        });
        AbstractAtomDataSetOperator operator = buildOperator(previewVo.getClassifyStatId(), previewVo.getFilterConditionStep(), previewVo.getDbType(), OperatorEnum.filterCondition.name());
        setOperators.add(operator);
        for (JoinTableStep joinTableStep : previewVo.getDataSetJoinVo()) {
            operator = buildOperator(previewVo.getClassifyStatId(), joinTableStep, previewVo.getDbType(), OperatorEnum.joinTable.name());
            setOperators.add(operator);
        }
        List<LogicDataColumn> collect = previewVo.getColumns().stream().map(r -> {
            LogicDataColumn logicDataColumn = JSON.parseObject(JSON.toJSONString(r), LogicDataColumn.class);
            logicDataColumn.setOwnerId(r.getBelongParentId());
            DataType dataType = new DataType();
            dataType.setId(logicDataColumn.getDisplayTypeId());
            logicDataColumn.setDataType(dataType);
            return logicDataColumn;
        }).collect(Collectors.toList());
        String sql = stepRelationService.getSQL(setOperators, collect);
        //转换变量参数ID
        String dataSetId = setOperators.get(0).getDataStep().getDataSetId();
        sql = transTransVar(sql, previewVo.getUserId());

        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageSize(previewVo.getPageSize());
        pageInfo.setPageIndex(previewVo.getPage());
        String classifyStatId = previewVo.getClassifyStatId();
        ClassifierStat classifierStat = this.get(ClassifierStat.class, classifyStatId);
        return getSqlColumnsForPageInfoDbType(sql, pageInfo, previewVo.getDbType(), classifierStat);
    }

    private AbstractAtomDataSetOperator buildOperator(String classifyStatId, BaseDataStep dataStep, String dbType, String operatorName) {
        AbstractAtomDataSetOperator operator = OperatorEnum.getOperator(operatorName, beanFactory);
        ClassifierStat classifierStat = get(ClassifierStat.class, classifyStatId);//get(ClassifierStat.class, classifyStatId)
        dataStep.setCode(operatorName);
        operator.init(dataStep);
        operator.setDbType(dbType);
        operator.setClassifierStat(classifierStat);
        return operator;
    }

    private AbstractAtomDataSetOperator buildSqlOperator(ClassifierStat classifierStat, BaseDataStep dataStep, String dbType, String operatorName) {
        AbstractAtomDataSetOperator operator = OperatorEnum.getOperator(operatorName, beanFactory);
        dataStep.setCode(operatorName);
        operator.init(dataStep);
        operator.setDbType(dbType);
        operator.setClassifierStat(classifierStat);
        return operator;
    }

    @Nullable
    private PageInfo previewBySql(PreviewVo previewVo, String searchSQL) {
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageSize(previewVo.getPageSize());
        pageInfo.setPageIndex(previewVo.getPage());
        PageInfo info = null;
        if (StringUtils.isBlank(searchSQL)) {
            return info;
        }
        try {
            info = getSqlColumnsForPageInfo(searchSQL, pageInfo, previewVo.getDataSetId());
        } catch (Exception e) {
            log.error("预览失败！", e);
            throw new RuntimeException("预览失败！", e);
        }
        if (!"column".equals(previewVo.getTitleType())) {
            List<Map<String, Object>> res = Lists.newArrayList();
            if (info.getDataList().size() > 0) {
                res = showColumnName(info.getDataList(), previewVo.getDataSetId());
            }
            info.setDataList(res);
        }
        return info;
    }


    @Override
    public PageInfo prviewDataWithColumn(PreviewVo previewVo) {
        Assert.isTrue(CollectionUtils.isNotEmpty(previewVo.getColumnList()), "字段列表不能为空");
        List<String> columnList = previewVo.getColumnList().stream().filter(column -> column != null).map(column -> column.toLowerCase()).collect(Collectors.toList());

        PageInfo pageInfo = preview(previewVo);

        if (pageInfo != null && CollectionUtils.isNotEmpty(pageInfo.getDataList())) {
            List<Map<String, Object>> dataList = pageInfo.getDataList();
            for (Map<String, Object> map : dataList) {
                Iterator<String> keyIter = map.keySet().iterator();
                while (keyIter.hasNext()) {
                    String key = keyIter.next();
                    if (!columnList.contains(key.toLowerCase())) {
                        keyIter.remove(); //过滤不存在的字段列
                    }
                }
            }
        }

        return pageInfo;
    }


    @Override
    public String getLogicSearchSQL(String dataSetId) {

        String table = "";
        LogicDataObj logicDataObj = logicDataObjService.findLogicDataObjById(dataSetId);
        //获取当前数据集的所有步骤
        List<AbstractAtomDataSetOperator> setOperators = getSetOperators(dataSetId);

        if (setOperators.size() <= 0) {
            String sql = logicDataObj.getSql();
//            for (int i = 0; i < sql.length(); i++) {
//                char c = sql.charAt(i);
//                if (Character.isUpperCase(c)) {
//                    String tableName = logicDataObj.getCode();
//                    if (tableName.contains(".")) {
//                        tableName = tableName.replace(".", "\".\"");
//                    }
//                    sql = "select * from " + tableName;
//                    break;
//                }
//            }
            if (StringUtils.isBlank(sql)) {
                String tableName = logicDataObj.getCode();
                if (tableName.contains(".")) {
                    tableName = tableName.replace(".", "\".\"");
                }
                sql = "select * from " + tableName;
            }
            table = sql;
        } else {
            table = stepRelationService.getSQL(setOperators, null);
        }
        return " ( " + table + " ) ";
    }

    @Override
    public void syncColumns(Map<String, List<LogicHttpColumns>> columns) {
        TypeMapping typeMapping = (TypeMapping) beanFactory.getBean("com.code.plugin.db.DefTypeMapping");
        logicDataColumnService.syncColumns(columns, typeMapping);
    }

    @Override
    public SyncColumnsVo getErrorColumns(String logicId) {
        Map<String, List<LogicHttpColumns>> errorColumn = setSyncService.getErrorColumn(logicId);
        SyncColumnsVo syncColumnsVo = new SyncColumnsVo();
        if (errorColumn != null && errorColumn.size() > 0) {
            syncColumnsVo.getDeleteColumns().addAll(errorColumn.get("DELETE"));
        }
        return syncColumnsVo;
    }


    @NotNull
    protected String getQuerySQL(String datasSetId) {
        //有步骤，预览
        String searchSQL = getSearchSQL(datasSetId);
        if (StringUtils.isBlank(searchSQL)) {
            LogicDataObj logicDataObj = logicDataObjService.findLogicDataObjById(datasSetId);
            searchSQL = logicDataObj.getSql();

            if (StringUtils.isBlank(searchSQL)) {
                Assert.fail("请添加数据集!");
            }
        }
        return searchSQL;
    }

    private List<Map<String, Object>> showColumnName(List<Map<String, Object>> res, String dataSetId) {

        List<Map<String, Object>> resList = Lists.newArrayList();

        List<LogicDataColumn> columns = logicDataColumnService.findLogicDataColumnsByLogicDataObjId(dataSetId);
        if (CollectionUtils.isEmpty(columns)) {
            return res;
        }
        for (Map<String, Object> resData : res) {
            Map<String, Object> newMap = Maps.newHashMap();
            Iterator<String> iterator = resData.keySet().iterator();
            while (iterator.hasNext()) {
                String key = iterator.next();
                for (LogicDataColumn column : columns) {
                    String checkCode = StringUtils.isBlank(column.getAlias()) ? column.getCode() : column.getAlias();
                    checkCode = TableNameUtil.splitScheme(checkCode);
                    if (checkCode.equalsIgnoreCase(key)) {
                        //修改字段名
                        String value = "";
                        value = resData.get(key) == null ? "" : String.valueOf(resData.get(key));

                        String name = StringUtils.isNotBlank(column.getName()) ? column.getName() : column.getCode();
//                        name = StringUtils.isNotBlank(column.getAlias()) ? column.getAlias() : name;
                        newMap.put(name, value);
                        break;
                    } else if (key.split("\\.").length > 1) {
                        if (checkCode.equalsIgnoreCase(key.split("\\.")[1])) {
                            String value = "";
                            value = resData.get(key) == null ? "" : String.valueOf(resData.get(key));

                            String name = StringUtils.isNotBlank(column.getName()) ? column.getName() : column.getCode();
                            name = StringUtils.isNotBlank(column.getAlias()) ? column.getAlias() : name;
                            newMap.put(name, value);
                            break;
                        }
                    }
                }
            }
            resList.add(newMap);
        }
        return resList;
    }

    private String getSearchSQL(String dataSetId) {

        //获取当前数据集的所有步骤
        List<AbstractAtomDataSetOperator> setOperators = getSetOperators(dataSetId);

        if (setOperators.size() <= 0) {
            return "";
        }

        return stepRelationService.getSQL(setOperators, null);
    }

    public DataSetStepRelation saveRelation(String id, List<AbstractAtomDataSetOperator> allSteps) {
        ClassifierStat classifierStatByLogic = logicDataObjService.findLogicDataObjById(id);
        allSteps.forEach(p -> p.setClassifierStat(classifierStatByLogic));


        DataSetStepRelation relation = null;
        if (allSteps != null) {
            relation = stepRelationService.upsert(allSteps, null);
            updateRelationId(id, relation.getId());
        }
        return relation;
    }

    private void updateName(SaveDataSetVo saveDataSetVo) {
        LogicDataObj dataObj = logicDataObjService.findLogicDataObjById(saveDataSetVo.getLogicDataSetId());
        dataObj.setName(saveDataSetVo.getName());
        logicDataObjService.saveLogicDataObj(dataObj);
    }

    private void updateRelationId(String logicDataSetId, String relationId) {
        LogicDataObj dataObj = logicDataObjService.findLogicDataObjById(logicDataSetId);
        //删除旧的步骤
        if (StringUtils.isNotBlank(dataObj.getStepRelationId())) {
            dataSetStepService.deleteByRelation(dataObj.getStepRelationId());
        }
        //更新新的步骤
        dataObj.setStepRelationId(relationId);
    }

    private void saveColumn(List<JSONObject> columns, String logicDataSetId) {
        for (JSONObject obj : columns) {
            LogicColumnInfoVo column = JSONObject.parseObject(JSONObject.toJSONString(obj), LogicColumnInfoVo.class);
            getLogicDataColumn(column, logicDataSetId);
        }
    }

    private void saveColumnDCThree(List<JSONObject> columns, String logicDataSetId) {
        for (JSONObject obj : columns) {
            LogicColumnInfoVo column = JSONObject.parseObject(JSONObject.toJSONString(obj), LogicColumnInfoVo.class);
            getLogicDataColumnDCThree(column, logicDataSetId);
        }
    }


    private void getLogicDataColumnDCThree(LogicColumnInfoVo column, String logicDataSetId) {
        String belongId = column.getParentId();
        List<LogicDataColumn> dataObjs = logicDataColumnService.findLogicDataColumnsByLogicDataObjId(logicDataSetId);
        TypeMapping typeMapping = (TypeMapping) beanFactory.getBean("com.code.plugin.db.DefTypeMapping");
        LogicDataObj logicDataObj = logicDataObjService.findLogicDataObjById(logicDataSetId);

        for (LogicSyncColumn syncColumn : column.getColumns()) {
            String name = StringUtils.isBlank(syncColumn.getName()) ? syncColumn.getCode() : syncColumn.getName();
            for (LogicDataColumn dataObj : dataObjs) {
                String objName = StringUtils.isBlank(dataObj.getName()) ? dataObj.getCode() : dataObj.getName();
                if (objName.equals(name)) {
                    name += "_1";
                }
            }
            LogicDataColumn dataColumn = new LogicDataColumn();
            dataColumn.setBelongParentId(belongId);
            dataColumn.setFuncExp(syncColumn.getFuncExp());
            dataColumn.setAlias(syncColumn.getColumnAlias());
            //标准格式设置
            DataType dataType = (DataType) this.baseDao.get(DataType.class, syncColumn.getDataTypeId());
            NonStandardType type = new NonStandardType(logicDataObj.getDbType().equals("hwmpp") ? "hwmpp." + dataType.getCode() : logicDataObj.getDbType().toUpperCase() + "." + dataType.getCode(), dataType.getCode(), 0, 0);
            StandardType stdType = typeMapping.trans(type);
            DataType standDataTypeByCode = getStandDataTypeByCode(stdType.getCode());
            dataColumn.setDataType(standDataTypeByCode);
            dataColumn.setName(name);
            dataColumn.setCode(syncColumn.getCode());
            dataColumn.setMemo(syncColumn.getMemo());
            dataColumn.setIndexType(syncColumn.getIndexType());
            dataColumn.setDisplayTypeId(syncColumn.getDataTypeId());
            dataColumn.setOwner(logicDataObj);
            dataColumn.setFormat(syncColumn.getFormat());
            dataColumn.setAlias(syncColumn.getCode());
            logicDataColumnService.saveLogicDataObjColumn(dataColumn);
        }
    }

    private Map<String, String> getAllDataType() {
        String sql = "select t2.id, t2.code from t_md_type_system t1 left join t_md_element t2 on t1.id = t2.owner_id where t1.code = 'STANDARD'";
        List<Map<String, String>> list = this.baseDao.sqlQueryForList(sql);
        if (list.size() > 0) {
            Map<String, String> map = new HashMap<>(list.size());
            for (Map<String, String> item : list) {
                map.put(item.get("code"), item.get("id"));
            }
            return map;
        }
        return null;
    }

    private DataType getStandDataTypeByCode(String code) {
        String sql = "select t2.* from t_md_type_system t1 left join t_md_element t2 on t1.id = t2.owner_id where t1.code = 'STANDARD' and  t2.code  = '" + code + "'";
        List<Map> list = this.baseDao.sqlQueryForList(sql);
        if (list.size() > 0) {
            DataType dataType = new DataType();
            dataType.setCode(list.get(0).get("code").toString());
            dataType.setId(list.get(0).get("id").toString());
            return dataType;
        }
        return null;
    }

    private void getLogicDataColumn(LogicColumnInfoVo column, String logicDataSetId) {
        String belongId = column.getParentId();
        List<LogicDataColumn> dataObjs = logicDataColumnService.findLogicDataColumnsByLogicDataObjId(logicDataSetId);

        for (LogicSyncColumn syncColumn : column.getColumns()) {
            String name = StringUtils.isBlank(syncColumn.getName()) ? syncColumn.getCode() : syncColumn.getName();
            for (LogicDataColumn dataObj : dataObjs) {
                String objName = StringUtils.isBlank(dataObj.getName()) ? dataObj.getCode() : dataObj.getName();
                if (objName.equals(name)) {
                    name += "_1";
                }
            }
            LogicDataColumn dataColumn = new LogicDataColumn();
            dataColumn.setBelongParentId(belongId);
            dataColumn.setFuncExp(syncColumn.getFuncExp());
            dataColumn.setAlias(syncColumn.getColumnAlias());
            dataColumn.setDataType(getDataType(syncColumn.getDataTypeId()));
            dataColumn.setName(name);
            dataColumn.setCode(syncColumn.getCode());
            dataColumn.setMemo(syncColumn.getMemo());
            dataColumn.setIndexType(syncColumn.getIndexType());
            LogicDataObj logicDataObj = getLogicDataObj(logicDataSetId);
            dataColumn.setOwner(logicDataObj);
            dataColumn.setFormat(syncColumn.getFormat());
            logicDataColumnService.saveLogicDataObjColumn(dataColumn);
        }
    }

    private LogicDataObj getLogicDataObj(String logicDataSetId) {
        return logicDataObjService.findLogicDataObjById(logicDataSetId);
    }

    private DataType getDataType(String dataTypeId) {
        return (DataType) this.baseDao.get(DataType.class, dataTypeId);
    }


    @Override
    public List<DataSetStepRelationVo> getDataSetStepRelation(String dataSetId) {
        List<DataSetStep> steps = getDataSetSteps(dataSetId);
        return getResult(steps);
    }

    private List<DataSetStep> getDataSetSteps(String dataSetId) {
        List<DataSetStep> steps = Lists.newArrayList();
        String relationId = getRelationId(dataSetId);
        Assert.notNull(dataSetId, "当前数据集未有操作步骤！");
        if (StringUtils.isNotBlank(relationId)) {
            steps = dataSetStepService.getDataSetStepByRelation(relationId);
            DataStepRelationOperator.stepsSort(steps);
        }
        return steps;
    }


    private List<DataSetStepRelationVo> getResult(List<DataSetStep> steps) {
        List<DataSetStepRelationVo> resList = Lists.newArrayList();
        for (DataSetStep step : steps) {
            DataSetStepRelationVo vo = new DataSetStepRelationVo();
            vo.setDataSetStepId(step.getId());
            vo.setName(step.getDataSetStepMeta().getName());
            vo.setCode(step.getDataSetStepMeta().getCode());
            vo.setStatus(step.getStatus());
            boolean mesAndDim = "维度和度量".equalsIgnoreCase(vo.getName());
            boolean dataFormat = "数据格式".equals(vo.getName());

            if (dataFormat || mesAndDim) {
                for (DataSetStepAttribute attribute : step.getDataSetStepAttributes()) {
                    if (LogicDataSetConstant.STEP_INDEX_TYPE.equals(attribute.getDataSetStepAttributeMeta().getCode())) {
                        if (LogicDataSetConstant.MEASURE.equals(attribute.getDataSetAttributeValue())) {
                            if (dataFormat) {
                                vo.setName("度量类型转换");
                            } else {
                                vo.setName("转度量");

                            }
                        } else {
                            if (dataFormat) {
                                vo.setName("维度类型转换");
                            } else {
                                vo.setName("转维度");
                            }
                        }
                        break;
                    }
                }
            }
            resList.add(vo);
        }
        return resList;
    }

    @Override
    public String getRelationId(String dataSetId) {
        String sql = "select step_relation_id from t_md_logic_dataobj where id = :id";
        return this.baseDao.sqlQueryForValue(sql, addParam("id", dataSetId).param());
    }

    @Override
    public LogicSyncColumn findLogicDataColumnById(String columnId) {
        LogicDataColumn logicDataColumn = logicDataColumnService.findLogicDataColumnById(columnId);
        return getLogicColumnInfoVo(logicDataColumn, logicDataColumn.getId());
    }

    @Override
    public void addLogicDataStepFromFather(String currentDataSetId, String addDataSetId) {
        List<AbstractAtomDataSetOperator> setOperators = getSetOperators(addDataSetId);
        saveRelation(currentDataSetId, setOperators);
    }

    @Override
    public BaseDataStep getDataSetStepInfo(String id) {
        Assert.notNull(id, "步骤id为空！");
        //存放步骤详细信息，不同的步骤信息不同
        DataSetStep dataSetStep = dataSetStepService.getDataStep(id);
        Set<DataSetStepAttribute> dataSetStepAttributes = dataSetStep.getDataSetStepAttributes();
        BaseDataStep baseDataStep = dataSetStepContext.buildDataStepByAttribute(dataSetStepAttributes.iterator().next().getDataSetStep().getDataSetStepMeta().getCode(), dataSetStepAttributes);

        if (baseDataStep instanceof AddColumnStep) {
            baseDataStep.setDataSetId(dataSetStep.getDataSet().getId());
            AddColumnStep addColumnStep = (AddColumnStep) baseDataStep;
            String exp = addColumnStep.getExp();
            List<String> list = JSON.parseObject(addColumnStep.getExpColumnIds(), List.class);
            for (String s : list) {
                LogicDataColumn column = logicDataColumnService.findLogicDataColumnById(s);
                if (StringUtils.isNotBlank(column.getName())) {
                    exp = exp.replaceAll(column.getCode(), column.getName());
                }
            }
            addColumnStep.setExp(exp);
        }
        return baseDataStep;
    }

    @Override
    public PageInfo getSqlColumnsForPageInfoDbType(String sql, PageInfo pageInfo, String dbType, ClassifierStat classifierStat) {
        ColumnPageInfo res = null;
        log.info("预览sql:{}", sql);
        log.info("dbType:{}", dbType);
        try {
            res = queryByClassifierStat(sql, pageInfo, classifierStat);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException(e);
        }
        pageInfo.setDataList(res.getColumnDataModel().getFieldValue());
        return pageInfo;
    }

    @Override
    public PageInfo getSqlColumnsForPageInfo(String sql, PageInfo pageInfo, String logicDataObjId) {
        ColumnPageInfo res = null;
        LogicDataObj obj = logicDataObjService.findLogicDataObjById(logicDataObjId);
        log.info("预览sql:{}", sql);
        log.info("dbType:{}", obj.getDbType());
        try {
            if (!"QUICK_SQL".equalsIgnoreCase(obj.getBelongType())) {
                ClassifierStat classifierStat = logicDataObjService.getClassifierStatByLogic(obj);
                res = queryByClassifierStat(sql, pageInfo, classifierStat);
            } else {
                LogicDataObj metaLogicDataObJ = logicDataObjService.getMetaLogicDataObJ(obj);
                res = queryDataService.queryDataBySchemaPageInfo(metaLogicDataObJ.getOwnerId(), sql, pageInfo);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException(e);
        }
        pageInfo.setDataList(res.getColumnDataModel().getFieldValue());
        return pageInfo;
    }

    private ColumnPageInfo queryByClassifierStat(String sql, PageInfo pageInfo, ClassifierStat classifierStat) throws Exception {
        return queryDataService.queryData(classifierStat.getId(), sql, pageInfo);
    }


    @Override
    public String getSqlByLogicDataObj(LogicDataObj logicDataObj, String addDataObjId) {
        String sql = "";
        if (StringUtils.isBlank(logicDataObj.getStepRelationId())) {
            sql = logicDataObj.getSql();

            if (StringUtils.isBlank(sql)) {
                Assert.fail("请先添加数据集！");
            }
        } else {
            sql = getRelationSql(logicDataObj);
        }
        return sql;
    }

    private String getRelationSql(LogicDataObj logicDataObj) {
        String sql;
        String querySQL = "select sql from t_dataset_step_relation where id  = :id";
        sql = this.baseDao.sqlQueryForValue(querySQL, addParam("id", logicDataObj.getStepRelationId()).param());
        return sql;
    }

    @Override
    public void format(FormatStep formatStep) {
        formatStep.setCode(LogicDataSetConstant.STEP_FORMAT_COLUMN);
        FormatColumnOperator formatColumnOperator = (FormatColumnOperator) OperatorEnum.getOperator(LogicDataSetConstant.STEP_FORMAT_COLUMN, beanFactory);

        formatColumnOperator.init(formatStep);

        //获取步骤
        List<AbstractAtomDataSetOperator> setOperators = getSetOperators(formatStep.getDataSetId());
        setOperators.add(formatColumnOperator);

        formatColumnOperator.exec(formatStep);
        saveOperatorsByRelation(formatColumnOperator, formatStep.getDataSetId());
//        saveRelation(formatStep.getDataSetId(), setOperators);
        //stepRegisterTable(setOperators, formatStep.getDataSetId(), false);
    }

    @Override
    public void deleteColumn(DeleteColumnStep deleteColumnStep) {
        deleteColumnStep.setCode(LogicDataSetConstant.STEP_DELETE_COLUMN);
        DeleteColumnOperator deleteColumnOperator = (DeleteColumnOperator) OperatorEnum.getOperator(LogicDataSetConstant.STEP_DELETE_COLUMN, beanFactory);

        deleteColumnOperator.init(deleteColumnStep);

        //获取步骤
        List<AbstractAtomDataSetOperator> setOperators = getSetOperators(deleteColumnStep.getDataSetId());
        setOperators.add(deleteColumnOperator);

        deleteColumnOperator.exec(deleteColumnStep);
        saveOperatorsByRelation(deleteColumnOperator, deleteColumnStep.getDataSetId());

        //stepRegisterTable(setOperators, deleteColumnStep.getDataSetId(), false);

        //获取与该字段相关的步骤，status置为1，表示异常
        setDataStepStatus(deleteColumnStep.getDataSetId(), deleteColumnStep.getColumnId(), deleteColumnStep.getId());
//        saveRelation(deleteColumnStep.getDataSetId(), setOperators);
    }

    private void setDataStepStatus(String dataSetId, String columnId, String stepId) {
        List<DataSetStep> steps = getDataSetSteps(dataSetId);
        for (int i = steps.size() - 2; i >= 0; i--) {
            DataSetStep step = steps.get(i);
            if (stepId.equalsIgnoreCase(step.getId())) continue;
            for (DataSetStepAttribute attribute : step.getDataSetStepAttributes()) {
                String code = attribute.getDataSetStepAttributeMeta().getCode();
                //普通步骤
                if (code.equalsIgnoreCase("columnId") && attribute.getDataSetAttributeValue().equals(columnId)) {
                    step.setStatus(1);
                }
            }
        }
    }

    @Override
    public void numberFormat(NumberFormatStep numberFormatStep) {
        numberFormatStep.setCode(LogicDataSetConstant.STEP_NUMBER_FORMAT);
        NumberFormatOperator numberFormatOperator = (NumberFormatOperator) OperatorEnum.getOperator(LogicDataSetConstant.STEP_NUMBER_FORMAT, beanFactory);
        numberFormatOperator.init(numberFormatStep);

        //获取步骤
        List<AbstractAtomDataSetOperator> setOperators = getSetOperators(numberFormatStep.getDataSetId());
        setOperators.add(numberFormatOperator);

        numberFormatOperator.exec(numberFormatStep);
//        saveRelation(numberFormatStep.getDataSetId(), setOperators);
        saveOperatorsByRelation(numberFormatOperator, numberFormatStep.getDataSetId());

        //stepRegisterTable(setOperators, numberFormatStep.getDataSetId(), false);
    }

    @Override
    public void filter(FilterConditionStep filterConditionStep) {
        LogicDataObj obj = logicDataObjService.findLogicDataObjById(filterConditionStep.getDataSetId());
        obj.setOperateTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern(LogicDataSetConstant.DATE_FORMAT)));
        FilterConditionOperator filterConditionOperator = null;
        filterConditionStep.setCode(LogicDataSetConstant.STEP_FILTER_CONDITION);
        BaseDataStep filter = getJoinAndFilter(LogicDataSetConstant.STEP_TYPE_FILTER, filterConditionStep.getDataSetId());
        if (filter != null) {
            //修改过滤条件步骤
            FilterConditionStep oldFilter = (FilterConditionStep) filter;
            //修改数据库里的数据
            DataSetStep dataStep = dataSetStepService.getDataStep(oldFilter.getId());
            Set<DataSetStepAttribute> attributes = dataStep.getDataSetStepAttributes();
            dataSetStepContext.updateAttribute(LogicDataSetConstant.STEP_FILTER_CONDITION, filterConditionStep, attributes);
        } else {
            //初始化过滤步骤
            filterConditionOperator = (FilterConditionOperator) OperatorEnum.getOperator(LogicDataSetConstant.STEP_FILTER_CONDITION, beanFactory);
            filterConditionOperator.init(filterConditionStep);
        }

        List<AbstractAtomDataSetOperator> setOperators = getSetOperators(filterConditionStep.getDataSetId());
        //获取步骤
        if (filterConditionOperator != null) {
            //用于注册spark
            setOperators.add(filterConditionOperator);
            //保存操作到数据库
            saveOperatorsByRelation(filterConditionOperator, filterConditionStep.getDataSetId());
        }
//        saveRelation(filterConditionStep.getDataSetId(), setOperators);
        //stepRegisterTable(setOperators, filterConditionStep.getDataSetId(), false);

    }

    @Override
    public void syncColumnStep(SyncColumnStep syncColumnStep) {

        LogicDataObj obj = logicDataObjService.findLogicDataObjById(syncColumnStep.getDataSetId());
        obj.setOperateTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern(LogicDataSetConstant.DATE_FORMAT)));

        syncColumnStep.setCode(LogicDataSetConstant.STEP_SYNC_COLUMN);
        SyncColumnOperator syncColumnOperator = (SyncColumnOperator) OperatorEnum.getOperator(LogicDataSetConstant.STEP_SYNC_COLUMN, beanFactory);
        syncColumnOperator.init(syncColumnStep);

        //获取步骤
        List<AbstractAtomDataSetOperator> setOperators = getSetOperators(syncColumnStep.getDataSetId());
        setOperators.add(syncColumnOperator);

//        saveRelation(syncColumnStep.getDataSetId(), setOperators);
        saveOperatorsByRelation(syncColumnOperator, syncColumnStep.getDataSetId());

        saveSyncColumn(syncColumnStep);

        //stepRegisterTable(setOperators, syncColumnStep.getDataSetId(), false);
    }

    @Override
    public void syncColumnStepDCThree(SyncColumnStep syncColumnStep) {
        LogicDataObj obj = logicDataObjService.findLogicDataObjById(syncColumnStep.getDataSetId()); //获取数据集
        obj.setOperateTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern(LogicDataSetConstant.DATE_FORMAT)));//更新操作时间

        syncColumnStep.setCode(LogicDataSetConstant.STEP_SYNC_COLUMN); //步骤为同步code
        SyncColumnOperator syncColumnOperator = (SyncColumnOperator) OperatorEnum.getOperator(LogicDataSetConstant.STEP_SYNC_COLUMN, beanFactory);
        syncColumnOperator.init(syncColumnStep);

        //获取步骤
        List<AbstractAtomDataSetOperator> setOperators = getSetOperators(syncColumnStep.getDataSetId());
        setOperators.add(syncColumnOperator);

        saveOperatorsByRelation(syncColumnOperator, syncColumnStep.getDataSetId());

        saveSyncColumnDCThree(syncColumnStep);

    }

    @Override
    public void saveSyncColumn(SyncColumnStep syncColumnStep) {
        String columnStr = syncColumnStep.getColumns();
        List<JSONObject> columnInfoVos = JSONObject.parseObject(columnStr, List.class);
        saveColumn(columnInfoVos, syncColumnStep.getDataSetId());
    }

    @Override
    public void saveOrUpdateDataSetColumn(List<LogicDataSetColumnVo> columns, String dataSetId) {
        logicDataObjService.saveOrUpdateDataSetColumn(columns, dataSetId);
    }

    @Override
    public void saveSyncColumnDCThree(SyncColumnStep syncColumnStep) {
        String columnStr = syncColumnStep.getColumns();
        List<JSONObject> columnInfoVos = JSONObject.parseObject(columnStr, List.class);
        saveColumnDCThree(columnInfoVos, syncColumnStep.getDataSetId());
    }

    @Override
    public LogicDataSetVo join(JoinTableStep joinTableStep) {
        LogicDataObj obj = logicDataObjService.findLogicDataObjById(joinTableStep.getDataSetId());
        obj.setOperateTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern(LogicDataSetConstant.DATE_FORMAT)));
        joinTableStep.setCode(LogicDataSetConstant.STEP_JOIN_TABLE);
//        checkJoinAlias(joinTableStep);
        JoinTableOperator joinTableOperator = null;
        BaseDataStep join = getJoinAndFilter(LogicDataSetConstant.STEP_TYPE_JOIN, joinTableStep.getDataSetId());

        if (join != null) {
            //修改过滤条件步骤
            JoinTableStep oldJoin = (JoinTableStep) join;
            //修改数据库里的数据
            DataSetStep dataStep = dataSetStepService.getDataStep(oldJoin.getId());
            Set<DataSetStepAttribute> attributes = dataStep.getDataSetStepAttributes();
            dataSetStepContext.updateAttribute(LogicDataSetConstant.STEP_JOIN_TABLE, joinTableStep, attributes);
        } else {
            joinTableStep.setTargetDataSetAlias(AliasUtil.getAliasMap(joinTableStep.getTargetDataSetId()));
            joinTableStep.setSourceDataSetAlias(AliasUtil.getAliasMap(joinTableStep.getSourceDataSetId()));
            joinTableOperator = (JoinTableOperator) OperatorEnum.getOperator(LogicDataSetConstant.STEP_JOIN_TABLE, beanFactory);
            LogicDataObj targetObj = logicDataObjService.findLogicDataObjById(joinTableStep.getTargetDataSetId());
            joinTableStep.setTargetDataSetCode(getLogicSearchSQL(targetObj.getId()));
            joinTableOperator.init(joinTableStep);
        }

        //获取步骤
        List<AbstractAtomDataSetOperator> setOperators = getSetOperators(joinTableStep.getDataSetId());
        if (joinTableOperator != null) {
            setOperators.add(joinTableOperator);
            saveOperatorsByRelation(joinTableOperator, joinTableStep.getDataSetId());
        }
        return getColumn(joinTableStep);
    }

    @Override
    public void multiJoinAndFilter(String dataSetId, List<JoinTableStep> joinVos, FilterConditionStep filterConditionStep) {
        LogicDataObj obj = logicDataObjService.findLogicDataObjById(dataSetId);
        obj.setOperateTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern(LogicDataSetConstant.DATE_FORMAT)));
        List<BaseDataStep> oldSteps = getJoinAndFilters(LogicDataSetConstant.STEP_TYPE_JOIN, dataSetId);
        oldSteps.addAll(getJoinAndFilters(LogicDataSetConstant.STEP_TYPE_FILTER, dataSetId));
        if (oldSteps != null && oldSteps.size() > 0) {
            //修改过滤条件步骤
            for (BaseDataStep oldStep : oldSteps) {
                dataSetStepService.deleteDataStepById(oldStep.getId());
            }
        }
        List<BaseDataStep> steps = new ArrayList<>();
        if (joinVos != null && joinVos.size() > 0) {
            steps.addAll(joinVos);
        }
        if (filterConditionStep != null) {
            filterConditionStep.setCode(LogicDataSetConstant.STEP_FILTER_CONDITION);
            steps.add(filterConditionStep);
        }
        //设置数据集id
        steps.forEach(r -> r.setDataSetId(dataSetId));
        //别名未设置时设置别名
        joinVos.forEach(r -> {
            if (StringUtils.isBlank(r.getSourceDataSetAlias())) {
                r.setSourceDataSetAlias(r.getSourceDataSetCode());
            }
            if (StringUtils.isBlank(r.getTargetDataSetAlias())) {
                r.setTargetDataSetAlias(r.getTargetDataSetCode());
            }
        });

        saveMultiJoin(dataSetId, joinVos, filterConditionStep, obj.getDbType());

    }

    @Override
    public void editSelfHelpDataSet(DataSetJoinVo dataSetJoinVo) {
        String dataSetId = dataSetJoinVo.getDataSetId();
        multiJoinAndFilter(dataSetId, dataSetJoinVo.getJoinTableSteps(), dataSetJoinVo.getFilterConditionStep());
        List<LogicDataSetColumnVo> columns = dataSetJoinVo.getColumns();
        saveOrUpdateDataSetColumn(columns, dataSetId);
        LogicDataObj obj = logicDataObjService.findLogicDataObjById(dataSetId);
        if (StringUtils.isNotBlank(dataSetJoinVo.getDataSetName())) {
            obj.setName(dataSetJoinVo.getDataSetName());
        }
        if (StringUtils.isNotBlank(dataSetJoinVo.getDataSetCode())) {
            obj.setCode(dataSetJoinVo.getDataSetCode());
        }
        List<String> dataObjIds = dataSetJoinVo.getDataObjIds();
        List<String> collect;
        if (dataObjIds != null && !dataObjIds.isEmpty()) {
            collect = dataObjIds;
        } else {
            collect = columns.stream().map(c -> c.getBelongParentId()).distinct().collect(Collectors.toList());
        }
        ClassifierStat classifierStat = get(ClassifierStat.class, collect.get(0));
        obj.setOwner(classifierStat);
        logicDataObjService.saveOrUpdateLogicDataObjRelation(dataSetId, collect);
        logicDataObjService.updateLogicDataObj(obj);
    }

    private void saveMultiJoin(String dataSetId, List<JoinTableStep> joinTableSteps, FilterConditionStep filterConditionStep, String dbType) {
        List<AbstractAtomDataSetOperator> tableOperatorList = new ArrayList<>();
        for (JoinTableStep tableStep : joinTableSteps) {
            JoinTableOperator joinTableOperator = new JoinTableOperator(beanFactory);
            joinTableOperator.init(tableStep);
            tableOperatorList.add(joinTableOperator);
        }
        if (filterConditionStep != null) {
            FilterConditionOperator filterConditionOperator = new FilterConditionOperator(beanFactory);
            filterConditionOperator.init(filterConditionStep);
            tableOperatorList.add(filterConditionOperator);
        }
        //保存操作到数据库
        saveOperatorsByRelation(tableOperatorList, dataSetId);
    }


    private void checkJoinAlias(JoinTableStep joinTableStep) {

        List<LogicDataColumn> columns = logicDataColumnService.findLogicDataColumnsByLogicDataObjId(joinTableStep.getDataSetId());
        List<LogicDataColumn> targetColumns = logicDataColumnService.findLogicDataColumnsByLogicDataObjId(joinTableStep.getTargetDataSetId());

        for (LogicDataColumn column : columns) {
            String checkCode = StringUtils.isBlank(column.getAlias()) ? column.getCode() : column.getAlias();
            for (LogicDataColumn targetColumn : targetColumns) {
                String checkTargetCode = StringUtils.isBlank(targetColumn.getAlias()) ? targetColumn.getCode() : targetColumn.getAlias();
                if (checkCode.equals(checkTargetCode) && !column.getBelongParentId().equals(joinTableStep.getTargetDataSetId())) {
                    String columnName = StringUtils.isNotBlank(column.getName()) ? column.getName() : column.getCode();
                    if (StringUtils.isBlank(column.getAlias())) {
                        Assert.fail("字段[" + columnName + "]的code重复,请取别名!");
                    } else {
                        Assert.fail("字段[" + columnName + "]的别名重复,请重新输入!");
                    }
                }
            }
        }
    }

    private LogicDataSetVo getColumn(JoinTableStep joinTableStep) {

        String targetDataSetId = joinTableStep.getTargetDataSetId();
        if (StringUtils.isNotBlank(joinTableStep.getMetaTargetDataSetId())) {
            if (!targetDataSetId.equals(joinTableStep.getMetaTargetDataSetId())) {
                //删除字段
                logicDataColumnService.deleteColumnByBelongParentId(joinTableStep.getMetaTargetDataSetId());
                //删除数据集之间的关系
                logicDataObjService.deleteLogicDataRelationByParentId(joinTableStep.getDataSetId(), joinTableStep.getMetaTargetDataSetId());
            } else {
                return null;
            }
        }
        //保存数据集之间的关系
        logicDataObjService.saveLogicDataObjRelation(joinTableStep.getDataSetId(), new ArrayList<String>(Arrays.asList(joinTableStep.getTargetDataSetId())));

        List<LogicDataColumn> targetColumns = getLogicDataColumns(targetDataSetId);
        List<LogicDataColumn> checkColumns = getLogicDataColumns(joinTableStep.getDataSetId());

        List<LogicDataColumn> addColumns = Lists.newArrayList();
        for (LogicDataColumn targetColumn : targetColumns) {
            String targetName = StringUtils.isNotBlank(targetColumn.getName()) ? targetColumn.getName() : targetColumn.getCode();
            LogicDataColumn addColumn = getAddNewColumn(targetColumn);
            for (LogicDataColumn checkColumn : checkColumns) {
                String checkName = StringUtils.isNotBlank(checkColumn.getName()) ? checkColumn.getName() : checkColumn.getCode();
                if (checkName.equals(targetName)) {
                    addColumn.setName(targetName + "_1");
                }
            }
            addColumns.add(addColumn);
        }
        return getLogicDataSetVo(addColumns, joinTableStep.getTargetDataSetId(), joinTableStep.getDataSetId());
    }

    private LogicDataColumn getAddNewColumn(LogicDataColumn targetColumn) {
        LogicDataColumn newColumn = new LogicDataColumn();
        newColumn.setName(targetColumn.getName());
        newColumn.setCode(targetColumn.getCode());
        newColumn.setMemo(targetColumn.getMemo());
        newColumn.setType(targetColumn.getType());
        newColumn.setDataType(targetColumn.getDataType());
        newColumn.setFormat(targetColumn.getFormat());
        newColumn.setIndexType(targetColumn.getIndexType());
        newColumn.setNumberFormat(targetColumn.getNumberFormat());
        return newColumn;
    }

    private LogicDataColumn getNewColumn(LogicDataColumn targetColumn, String dataSetId, String columnParentId) {
        LogicDataObj obj = logicDataObjService.findLogicDataObjById(dataSetId);
        LogicDataColumn logicDataColumn = new LogicDataColumn();
        String name = StringUtils.isNotBlank(targetColumn.getName()) ? targetColumn.getName() : targetColumn.getCode();
        logicDataColumn.setName(name);
        logicDataColumn.setCode(targetColumn.getCode());
        logicDataColumn.setMemo(targetColumn.getMemo());
        logicDataColumn.setType(targetColumn.getType());

        logicDataColumn.setBelongParentId(columnParentId);

        logicDataColumn.setDataType(targetColumn.getDataType());
        logicDataColumn.setFormat(targetColumn.getFormat());
        logicDataColumn.setIndexType(targetColumn.getIndexType());
        logicDataColumn.setNumberFormat(targetColumn.getNumberFormat());

        logicDataColumn.setOwner(obj);

        logicDataColumnService.saveLogicDataObjColumn(logicDataColumn);
        return logicDataColumn;
    }

    @Override
    public void union(UnionTableStep unionTableStep) {
        unionTableStep.setCode(LogicDataSetConstant.STEP_UNION_TABLE);

        unionTableStep = getUnionShowColumn(unionTableStep);
        UnionTableOperator unionTableOperator = null;
        BaseDataStep union = getJoinAndFilter(LogicDataSetConstant.STEP_TYPE_UNION, unionTableStep.getDataSetId());

        if (union != null) {
            //修改步骤
            UnionTableStep oldUnion = (UnionTableStep) union;

            //从旧的步骤获取当前数据集未合并前的所有字段
            UnionParam unionParam = JSON.parseObject(unionTableStep.getUnionInfoJson(), UnionParam.class);
            UnionParam oldUnionParam = JSON.parseObject(oldUnion.getUnionInfoJson(), UnionParam.class);
            unionParam.setMetaColumnInfo(oldUnionParam.getMetaColumnInfo());

            unionTableStep.setUnionInfoJson(JSON.toJSONString(unionParam));

            //修改数据库里的数据
            DataSetStep dataStep = dataSetStepService.getDataStep(oldUnion.getId());
            Set<DataSetStepAttribute> attributes = dataStep.getDataSetStepAttributes();
            dataSetStepContext.updateAttribute(LogicDataSetConstant.STEP_UNION_TABLE, unionTableStep, attributes);

            //修改字段信息
            updateDataSetColumn(unionTableStep);
        } else {
            unionTableStep = saveMetaColumn(unionTableStep);
            unionTableOperator = (UnionTableOperator) OperatorEnum.getOperator(LogicDataSetConstant.STEP_UNION_TABLE, beanFactory);
            unionTableOperator.init(unionTableStep);
            unionTableOperator.exec(unionTableStep);
        }

        List<AbstractAtomDataSetOperator> setOperators = getSetOperators(unionTableStep.getDataSetId());
        if (unionTableOperator != null) {
            setOperators.add(unionTableOperator);
            saveOperatorsByRelation(unionTableOperator, unionTableStep.getDataSetId());
//            saveRelation(unionTableStep.getDataSetId(), setOperators);
        }

        //stepRegisterTable(setOperators, unionTableStep.getDataSetId(), false);
    }

    private void updateDataSetColumn(UnionTableStep unionTableStep) {

        //获取数据集的所有数据
        List<LogicDataColumn> columns = logicDataColumnService.findLogicDataColumnsByLogicDataObjId(unionTableStep.getDataSetId());

        //修改数据库字段的name
        UnionParam unionParam = JSON.parseObject(unionTableStep.getUnionInfoJson(), UnionParam.class);
        for (LogicDataColumn column : columns) {
            for (UnionColumn unionColumn : unionParam.getUnionShowColumn()) {
                if (unionColumn.getId().equals(column.getId())) {
                    column.setName(unionColumn.getName());
                }
            }
        }

        List<MetaColumnInfo> columnInfo = unionParam.getMetaColumnInfo();
        //将在数据库里面有的字段过滤
        Iterator<MetaColumnInfo> iterator = columnInfo.iterator();
        while (iterator.hasNext()) {
            MetaColumnInfo next = iterator.next();
            for (LogicDataColumn column : columns) {
                if (column.getId().equals(next.getColumnId())) {
                    iterator.remove();
                    break;
                }
            }
        }

        //添加修改后添加的字段信息
        for (MetaColumnInfo metaColumnInfo : columnInfo) {
            for (UnionColumn unionColumn : unionParam.getUnionShowColumn()) {
                if (unionColumn.getId().equals(metaColumnInfo.getColumnId())) {
                    //添加字段信息
                    saveUnionAddColumn(metaColumnInfo, unionTableStep.getDataSetId());
                    break;
                }
            }
        }
    }

    private void saveUnionAddColumn(MetaColumnInfo info, String dataSetId) {
        String insertSQL = "INSERT INTO " + " T_MD_LOGIC_DATA_COLUMN " + " (id,name,type,memo,code,owner_id,operate_time,data_type_id,index_type,format,number_format,column_belong_parent_id,column_alias,column_expression) " + " VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
        List<Object[]> param = UnionUtil.getParam(info, dataSetId);
        logicDataColumnService.batchInsert(insertSQL, param);
    }

    private UnionTableStep saveMetaColumn(UnionTableStep step) {
        //保存原来的数据集字段
        UnionParam unionParam = JSON.parseObject(step.getUnionInfoJson(), UnionParam.class);

        List<LogicDataColumn> columns = logicDataColumnService.findLogicDataColumnsByLogicDataObjId(step.getDataSetId());
        unionParam.setMetaColumnInfo(getMetaColumn(columns));
        step.setUnionInfoJson(JSON.toJSONString(unionParam));
        return step;
    }

    private UnionTableStep getUnionShowColumn(UnionTableStep step) {

        UnionParam unionParam = JSON.parseObject(step.getUnionInfoJson(), UnionParam.class);

        List<UnionColumn> presentColumn = unionParam.getPresentColumn();
        List<UnionColumn> unionColumn = unionParam.getUnionColumn();
        List<UnionColumn> showColumn = unionParam.getUnionShowColumn();

        List<String> unionStr = Lists.newArrayList();
        for (int i = 0; i < presentColumn.size(); i++) {
            UnionColumn present = presentColumn.get(i);
            UnionColumn union = unionColumn.get(i);
            UnionColumn show = showColumn.get(i);
            //字段类型才能合并
            if (present.getTypeId().equals(union.getTypeId())) {
                show.setId(present.getId());
                show.setCode(present.getCode());
                unionStr.add(union.getCode());
            }
        }

        //获取合并的数据集sql
        StringBuffer unionSQL = getUnionSQL(step, unionStr);
        step.setUnionSql(unionSQL.toString());

        step.setUnionInfoJson(JSON.toJSONString(unionParam));

        return step;
    }

    @NotNull
    private StringBuffer getUnionSQL(UnionTableStep step, List<String> unionStr) {
        StringBuffer unionSQL = new StringBuffer();
        unionSQL.append("select ");
        for (int i = 0; i < unionStr.size(); i++) {
            unionSQL.append(unionStr.get(i));
            if (i != unionStr.size() - 1) {
                unionSQL.append(",");
            }
        }
        unionSQL.append(" from " + step.getTargetDataSetCode());
        return unionSQL;
    }

    private List<MetaColumnInfo> getMetaColumn(List<LogicDataColumn> columns) {
        List<MetaColumnInfo> res = Lists.newArrayList();
        for (LogicDataColumn column : columns) {
            MetaColumnInfo metaColumnInfo = new MetaColumnInfo();
            metaColumnInfo.setColumnId(column.getId());
            ;
            metaColumnInfo.setBelongParentId(column.getBelongParentId());
            metaColumnInfo.setColumnAlias(column.getAlias());
            metaColumnInfo.setColumnCode(column.getCode());
            metaColumnInfo.setColumnExpression(column.getFuncExp());
            metaColumnInfo.setDataTypeId(column.getDataType().getId());
            metaColumnInfo.setFormat(column.getFormat());
            metaColumnInfo.setIndexType(column.getIndexType());
            metaColumnInfo.setMemo(column.getMemo());
            metaColumnInfo.setName(column.getName());
            metaColumnInfo.setNumberFormat(column.getNumberFormat());
            res.add(metaColumnInfo);
        }
        return res;
    }

    @Override
    public List<FunctionVo> getFunction(String condition) {
        return EnumFunction.getFunctionByCondition(condition);
    }

    @Override
    public List<LogicSyncColumn> getLogicColumn(String id, String condition) {
        Assert.notNull(id, "数据集id不能为空！");
        String sql = "select id,code,name,format,data_type_id,index_type,memo,number_format,operate_time,column_belong_parent_id from t_md_logic_data_column where owner_id=:id";
        Map<String, String> param = Maps.newHashMap();
        param.put("id", id);
        if (StringUtils.isNotBlank(condition)) {
            sql += " and (name like '%" + condition + "%' or code like '%" + condition + "%')";
        }
        List<Map<String, String>> queryMap = this.baseDao.sqlQueryForList(sql, param);

        Map<String, String> currentDataSet = queryCurrentDataSet(id);
        //获取哪个是当前的数据集，operator_time小的是当前的数据集
        List<LogicSyncColumn> res = Lists.newArrayList();
        for (Map<String, String> map : queryMap) {
            LogicSyncColumn infoVo = new LogicSyncColumn();
            infoVo.setCode(map.get("code"));
            infoVo.setName(map.get("name"));
            infoVo.setFormat(map.get("format"));
            infoVo.setId(map.get("id"));
            infoVo.setIndexType(map.get("index_type"));
            infoVo.setMemo(map.get("memo"));
            infoVo.setNumberFormat(map.get("number_format"));
            infoVo.setDataTypeId(map.get("data_type_id"));

            String parentId = currentDataSet.get(map.get("operate_time")) == null ? map.get("column_belong_parent_id") : currentDataSet.get(map.get("operate_time"));
            infoVo.setBelongParentId(parentId);

            res.add(infoVo);
        }
        return res;
    }

    private Map<String, String> queryCurrentDataSet(String id) {
        String timeSQL = "select operate_time,owner_id from t_md_logic_data_column where owner_id = :id order by  operate_time limit 1 ";
        Map<String, String> query = this.baseDao.sqlQueryForMap(timeSQL, addParam("id", id).param());

        Map<String, String> resMap = Maps.newHashMap();
        resMap.put(query.get("operate_time"), query.get("owner_id"));
        return resMap;
    }

    @Override
    public void deleteDataStep(String id, String dataSetId) {
        List<DataSetStep> steps = getDataSetSteps(dataSetId);
        LogicDataObj obj = logicDataObjService.findLogicDataObjById(dataSetId);
        obj.setOperateTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern(LogicDataSetConstant.DATE_FORMAT)));
        deleteDataStepForDataBase(id);

        checkDataStep(id, dataSetId, steps);

        //stepRegisterTable(id, dataSetId);
    }

    private void checkDataStep(String id, String dataSetId, List<DataSetStep> steps) {
        setStepStatus(id, steps);
    }

    private String getSql(String dataSetId) {
        List<AbstractAtomDataSetOperator> setOperators = getSetOperators(dataSetId);
        return stepRelationService.getSQL(setOperators, null);
    }

    private boolean checkSqlStatus(String sql, String dataSetId) {
        LogicDataObj obj = logicDataObjService.findLogicDataObjById(dataSetId);
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageSize(10);
        pageInfo.setPageIndex(1);
        try {
            if (!"QUICK_SQL".equalsIgnoreCase(obj.getBelongType())) {
                ClassifierStat classifierStat = logicDataObjService.getClassifierStatByLogic(obj);
                queryDataService.queryData(classifierStat.getId(), sql, pageInfo);
            } else {
                LogicDataObj metaLogicDataObJ = logicDataObjService.getMetaLogicDataObJ(obj);
                queryDataService.queryDataBySchemaPageInfo(metaLogicDataObJ.getOwnerId(), sql, pageInfo);
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            return false;
        }
        return true;
    }

    private void setStepStatus(String id, List<DataSetStep> steps) {
        //删除删除字段步骤，将操作该步骤字段id的步骤status置为0
        changeStepStatus(id, steps);
    }

    private void changeStepStatus(List<DataSetStep> steps, String columnId, int status) {
        for (DataSetStep step : steps) {
            for (DataSetStepAttribute attribute : step.getDataSetStepAttributes()) {
                if (attribute.getDataSetStepAttributeMeta().getCode().equalsIgnoreCase("columnId") && columnId.equalsIgnoreCase((String) attribute.getDataSetAttributeValue())) {
                    step.setStatus(status);
                }
            }
        }
    }

    private void changeStepStatus(String id, List<DataSetStep> steps) {
        DataSetStep dataStep = dataSetStepService.getDataStep(id);
        String code = dataStep.getDataSetStepMeta().getCode();
        String columnId = "";
        if (LogicDataSetConstant.STEP_DELETE_COLUMN.equalsIgnoreCase(code)) {
            for (DataSetStepAttribute attribute : dataStep.getDataSetStepAttributes()) {
                if (attribute.getDataSetStepAttributeMeta().getCode().equalsIgnoreCase("columnId")) {
                    columnId = (String) attribute.getDataSetAttributeValue();
                }
            }
            changeStepStatus(steps, columnId, 0);
        } else if (LogicDataSetConstant.STEP_ADD_COLUMN.equalsIgnoreCase(code)) {
            for (DataSetStepAttribute attribute : dataStep.getDataSetStepAttributes()) {
                if (attribute.getDataSetStepAttributeMeta().getCode().equalsIgnoreCase("columnCode")) {
                    columnId = logicDataColumnService.getLogicDataColumnByCode((String) attribute.getDataSetAttributeValue(), dataStep.getDataSet().getId());
                }
            }
            changeStepStatus(steps, columnId, 1);
        }
    }

    private void stepRegisterTable(String id, String dataSetId) {
        List<AbstractAtomDataSetOperator> stepOperator = getSetOperators(dataSetId);

        Iterator<AbstractAtomDataSetOperator> iter = stepOperator.iterator();
        while (iter.hasNext()) {
            AbstractAtomDataSetOperator operator = iter.next();
            if (operator.getDataStep().getId().equals(id)) {
                iter.remove();
            }
        }
        //stepRegisterTable(stepOperator, dataSetId, false);
//        saveRelation(dataSetId, stepOperator);
    }

    private List<AbstractAtomDataSetOperator> getStepOperatorByDataStepStep(String dataSetId) {
        String relationId = getRelationId(dataSetId);
        return stepRelationService.flushStep(relationId, null);
    }

    private void deleteDataStepForDataBase(String id) {
        BaseDataStep dataStep = getDataSetStepInfo(id);
        if (dataStep == null) {
            Assert.fail("获取不到步骤！");
        }
        dataSetStepContext.revocationDataStep(dataStep.getCode(), dataStep);

        stepRelationService.deleteStepById(id);
    }

    @Override
    public void editDataStep(StepVo stepVo) {

        LogicDataObj obj = logicDataObjService.findLogicDataObjById(stepVo.getDataSetId());
        obj.setOperateTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern(LogicDataSetConstant.DATE_FORMAT)));

        updateAttribute(stepVo);

        List<AbstractAtomDataSetOperator> setOperators = getSetOperators(stepVo.getDataSetId());


        //stepRegisterTable(setOperators, stepVo.getDataSetId(), false);

    }

    private void updateAttribute(StepVo stepVo) {
        DataSetStep dataStep = dataSetStepService.getDataStep(stepVo.getStepId());
        Set<DataSetStepAttribute> attributes = dataStep.getDataSetStepAttributes();

        switch (dataStep.getDataSetStepMeta().getCode()) {
            case "editColumn":
                EditColumnStep editColumnStep = stepVo.getEditColumnStep();
                checkColumnName(editColumnStep.getShowColumnName(), editColumnStep.getDataSetId(), editColumnStep.getColumnId());
                dataSetStepContext.updateAttribute(dataStep.getDataSetStepMeta().getCode(), stepVo.getEditColumnStep(), attributes);
                break;
            case "addColumn":
                dataSetStepContext.updateAttribute(dataStep.getDataSetStepMeta().getCode(), stepVo.getAddColumnStep(), attributes);
                break;
            case "formatColumn":
                dataSetStepContext.updateAttribute(dataStep.getDataSetStepMeta().getCode(), stepVo.getFormatStep(), attributes);
                break;
            case "indexType":
                dataSetStepContext.updateAttribute(dataStep.getDataSetStepMeta().getCode(), stepVo.getIndexTypeStep(), attributes);
                break;
            case "filterCondition":
                dataSetStepContext.updateAttribute(dataStep.getDataSetStepMeta().getCode(), stepVo.getFilterConditionStep(), attributes);
                break;
            case "joinTable":
                dataSetStepContext.updateAttribute(dataStep.getDataSetStepMeta().getCode(), stepVo.getJoinTableStep(), attributes);
                break;
            case "numberFormat":
                dataSetStepContext.updateAttribute(dataStep.getDataSetStepMeta().getCode(), stepVo.getNumberFormatStep(), attributes);
                break;
            case "unionTable":
                //新的步骤
                UnionTableStep newUnionStep = stepVo.getUnionTableStep();
                //获取旧的union步骤
                BaseDataStep union = getJoinAndFilter(LogicDataSetConstant.STEP_TYPE_UNION, newUnionStep.getDataSetId());
                newUnionStep = getUnionShowColumn(newUnionStep);
                UnionTableStep oldUnion = (UnionTableStep) union;

                //从旧的步骤获取当前数据集未合并前的所有字段
                //获取新的步骤信息
                UnionParam unionParam = JSON.parseObject(newUnionStep.getUnionInfoJson(), UnionParam.class);
                //获取旧的步骤信息
                UnionParam oldUnionParam = JSON.parseObject(oldUnion.getUnionInfoJson(), UnionParam.class);
                unionParam.setMetaColumnInfo(oldUnionParam.getMetaColumnInfo());
                newUnionStep.setUnionInfoJson(JSON.toJSONString(unionParam));

                DataSetStep oldSataStep = dataSetStepService.getDataStep(oldUnion.getId());
                Set<DataSetStepAttribute> oldAttributes = oldSataStep.getDataSetStepAttributes();
                dataSetStepContext.updateAttribute(LogicDataSetConstant.STEP_UNION_TABLE, newUnionStep, oldAttributes);

                updateDataSetColumn(newUnionStep);
                break;
            default:
                Assert.fail("未找到对应步骤！");
        }
    }

    @Override
    public boolean checkHasParentId(String currentDataSetId) {
        String sql = "select count(1) from t_md_logic_data_relation where logic_data_obj_id = :id and (relation_type = '1' or relation_type is null) ";
        int count = Integer.valueOf(this.baseDao.sqlQueryForValue(sql, addParam("id", currentDataSetId).param()));
        if (count > 0) {
            return true;
        }
        return false;
    }

    @Override
    public BaseDataStep getJoinAndFilter(String type, String dataSetId) {
        List<AbstractAtomDataSetOperator> stepOperator = getStepOperatorByDataStepStep(dataSetId);

        for (AbstractAtomDataSetOperator operator : stepOperator) {
            String code = operator.getDataStepByOperator().getDataSetStepMeta().getCode();
            if (LogicDataSetConstant.STEP_JOIN_TABLE.equals(code) && LogicDataSetConstant.STEP_TYPE_JOIN.equals(type)) {
                return operator.getDataStep();
            } else if (LogicDataSetConstant.STEP_FILTER_CONDITION.equals(code) && LogicDataSetConstant.STEP_TYPE_FILTER.equals(type)) {
                return operator.getDataStep();
            } else if (LogicDataSetConstant.STEP_UNION_TABLE.equals(code) && LogicDataSetConstant.STEP_TYPE_UNION.equals(type)) {
                return operator.getDataStep();
            }
        }
        return null;

    }

    @Override
    public List<BaseDataStep> getJoinAndFilters(String type, String dataSetId) {
        List<AbstractAtomDataSetOperator> stepOperator = getStepOperatorByDataStepStep(dataSetId);
        List<BaseDataStep> result = new ArrayList<>();
        for (AbstractAtomDataSetOperator operator : stepOperator) {
            String code = operator.getDataStepByOperator().getDataSetStepMeta().getCode();
            if (LogicDataSetConstant.STEP_JOIN_TABLE.equals(code) && LogicDataSetConstant.STEP_TYPE_JOIN.equals(type)) {
                result.add(buildAddTableName((JoinTableStep) operator.getDataStep()));
            } else if ((LogicDataSetConstant.STEP_FILTER_CONDITION.equals(code) && LogicDataSetConstant.STEP_TYPE_FILTER.equals(type))
                    || (LogicDataSetConstant.STEP_UNION_TABLE.equals(code) && LogicDataSetConstant.STEP_TYPE_UNION.equals(type))) {
                result.add(operator.getDataStep());
            }
        }
        return result;
    }

    private JoinTableStepVo buildAddTableName(JoinTableStep dataStep) {
        JoinTableStepVo joinTableStepVo = JoinTableStepVo.fromJoinTableStep(dataStep);
        String sourceDataSetId = dataStep.getSourceDataSetId();
        String targetDataSetId = dataStep.getTargetDataSetId();
        Assert.isTrue(StringUtils.isNotBlank(sourceDataSetId), "join数据源表id不允许为空");
        Assert.isTrue(StringUtils.isNotBlank(targetDataSetId), "join数据目标表id不允许为空");
        ModelElement s = (ModelElement) this.baseDao.get(ModelElement.class, sourceDataSetId);
        ModelElement t = (ModelElement) this.baseDao.get(ModelElement.class, targetDataSetId);
        if (s != null) {
            joinTableStepVo.setSourceDataSetName(s.getName());
        }
        if (t != null) {
            joinTableStepVo.setTargetDataSetName(t.getName());
        }
        return joinTableStepVo;
    }

    @Override
    public List<BaseDataStep> getAllStep(String dataSetId) {
        List<AbstractAtomDataSetOperator> stepOperator = getStepOperatorByDataStepStep(dataSetId);
        List<BaseDataStep> collect = stepOperator.stream().map(r -> r.getDataStep()).collect(Collectors.toList());
        return collect;
    }

    @Override
    public void saveAs(SaveDataSetVo saveDataSetVo, String userId) {
        LogicDataObj logicDataObj = (LogicDataObj) this.baseDao.get(LogicDataObj.class, saveDataSetVo.getLogicDataSetId());
        LogicDataObj newLogicDataObj = copyLogicDataObj(saveDataSetVo.getSaveAsId(), userId, logicDataObj);
        newLogicDataObj.setOperateTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern(LogicDataSetConstant.DATE_FORMAT)));
        newLogicDataObj.setBelongType(logicDataObj.getBelongType());
        //复制当前数据集和父数据集的关系（t_md_logic_data_relation）
        List<String> logicDataRelation = logicDataObjService.getLogicDataRelation(logicDataObj.getId());
        logicDataObjService.saveLogicDataObjRelation(newLogicDataObj.getId(), logicDataRelation);
        //复制字段

        Map<String, String> dataColumn = logicDataColumnService.copyDataColumn(logicDataObj, newLogicDataObj);

        String sql = "";
        //复制步骤
        if (StringUtils.isNotBlank(logicDataObj.getStepRelationId())) {

            //获取步骤
            List<AbstractAtomDataSetOperator> setOperators = getSetOperators(saveDataSetVo.getLogicDataSetId());

            setOperators = updateOperator(setOperators, newLogicDataObj, dataColumn);

            DataSetStepRelation stepRelation = saveRelation(saveDataSetVo.getSaveAsId(), setOperators);
            if (stepRelation != null && StringUtils.isNotBlank(logicDataObj.getStepRelationId())) {
                DataSetStepRelation relation = this.dataSetStepService.get(DataSetStepRelation.class, logicDataObj.getStepRelationId());
                List<DataSetStep> oldSteps = new ArrayList<>(relation.getSteps());
                List<DataSetStep> newSteps = new ArrayList<>(stepRelation.getSteps());
                DataStepRelationOperator.stepsSort(oldSteps);
                DataStepRelationOperator.stepsSort(newSteps);
                for (int i = 0; i < newSteps.size(); i++) {
                    newSteps.get(i).setStatus(oldSteps.get(i).getStatus());
                }
            }
            sql = getQuerySQL(saveDataSetVo.getLogicDataSetId());
        }

        if (StringUtils.isBlank(sql)) {
            sql = logicDataObj.getSql();
        }

        if (checkLogicDataSetUsed(logicDataObj.getId()).size() > 0) return;
/*        try {
            registerTable(newLogicDataObj, sql, true);
        } catch (Exception e) {
          logger
            Assert.fail("视图创建失败！失败原因 ：" + e.getMessage());
        }*/
    }

    private List<AbstractAtomDataSetOperator> updateOperator(List<AbstractAtomDataSetOperator> setOperators, LogicDataObj logicDataObj, Map<String, String> dataColumn) {
        List<AbstractAtomDataSetOperator> newOperators = Lists.newArrayList();
        String dbType = logicDataObj.getDbType();
        for (AbstractAtomDataSetOperator operator : setOperators) {
            BaseDataStep dataStep = operator.getDataStep();
            dataStep.setDataSetId(logicDataObj.getId());
            switch (dataStep.getCode()) {
                case LogicDataSetConstant.STEP_ADD_COLUMN:
                    AddColumnStep addColumnStep = (AddColumnStep) operator.getDataStep();

                    AddColumnOperator addColumnOperator = (AddColumnOperator) OperatorEnum.getOperator(LogicDataSetConstant.STEP_ADD_COLUMN, beanFactory);
                    addColumnOperator.init(addColumnStep);
                    newOperators.add(addColumnOperator);
                    break;
                case LogicDataSetConstant.STEP_EDIT_COLUMN:

                    EditColumnStep editColumnStep = (EditColumnStep) operator.getDataStep();

                    String editColumn = StringUtils.isNotBlank(editColumnStep.getColumnAlias()) ? editColumnStep.getColumnAlias() : editColumnStep.getColumnCode();

                    String columnId = dataColumn.get(editColumn);
                    if (dataColumn.get(editColumn) == null) {
                        columnId = UUID.randomUUID().toString().replace("-", "");
                        dataColumn.put(editColumn, columnId);
                    }

                    editColumnStep.setColumnId(columnId);

                    EditColumnOperator editColumnOperator = (EditColumnOperator) OperatorEnum.getOperator(LogicDataSetConstant.STEP_EDIT_COLUMN, beanFactory);
                    editColumnOperator.init(editColumnStep);
                    newOperators.add(editColumnOperator);
                    break;

                case LogicDataSetConstant.STEP_DELETE_COLUMN:

                    DeleteColumnStep deleteColumnStep = (DeleteColumnStep) dataStep;

                    deleteColumnStep.setColumnId(UUID.randomUUID().toString().replace("-", ""));

                    DeleteColumnOperator deleteColumnOperator = (DeleteColumnOperator) OperatorEnum.getOperator(LogicDataSetConstant.STEP_DELETE_COLUMN, beanFactory);
                    deleteColumnOperator.init(deleteColumnStep);
                    newOperators.add(deleteColumnOperator);

                    break;
                case LogicDataSetConstant.STEP_FORMAT_COLUMN:
                    FormatStep formatStep = (FormatStep) dataStep;
                    LogicDataColumn columnFormat = logicDataColumnService.findLogicDataColumnById(formatStep.getColumnId());
                    formatStep.setColumnId(dataColumn.get(StringUtils.isNotBlank(columnFormat.getAlias()) ? columnFormat.getAlias() : columnFormat.getCode()));

                    FormatColumnOperator formatColumnOperator = (FormatColumnOperator) OperatorEnum.getOperator(LogicDataSetConstant.STEP_FORMAT_COLUMN, beanFactory);
                    formatColumnOperator.init(formatStep);
                    newOperators.add(formatColumnOperator);
                    break;
                case LogicDataSetConstant.STEP_INDEX_TYPE:
                    IndexTypeStep indexTypeStep = (IndexTypeStep) dataStep;
                    LogicDataColumn columnIndexType = logicDataColumnService.findLogicDataColumnById(indexTypeStep.getColumnId());
                    indexTypeStep.setColumnId(dataColumn.get(StringUtils.isNotBlank(columnIndexType.getAlias()) ? columnIndexType.getAlias() : columnIndexType.getCode()));

                    IndexTypeOperator indexTypeOperator = (IndexTypeOperator) OperatorEnum.getOperator(LogicDataSetConstant.STEP_INDEX_TYPE, beanFactory);
                    indexTypeOperator.init(indexTypeStep);
                    newOperators.add(indexTypeOperator);
                    break;
                case LogicDataSetConstant.STEP_NUMBER_FORMAT:
                    NumberFormatStep numberFormatStep = (NumberFormatStep) dataStep;
                    LogicDataColumn columnNumberFormat = logicDataColumnService.findLogicDataColumnById(numberFormatStep.getColumnId());
                    numberFormatStep.setColumnId(StringUtils.isNotBlank(columnNumberFormat.getAlias()) ? columnNumberFormat.getAlias() : columnNumberFormat.getCode());

                    NumberFormatOperator numberFormatOperator = (NumberFormatOperator) OperatorEnum.getOperator(LogicDataSetConstant.STEP_NUMBER_FORMAT, beanFactory);
                    numberFormatOperator.init(numberFormatStep);
                    newOperators.add(numberFormatOperator);
                    break;
                case LogicDataSetConstant.STEP_SYNC_COLUMN:
                    SyncColumnStep syncColumnStep = (SyncColumnStep) dataStep;
                    List<JSONObject> columnInfoVos = JSONObject.parseObject(syncColumnStep.getColumns(), List.class);

                    for (JSONObject infoVo : columnInfoVos) {
                        LogicColumnInfoVo column = JSONObject.parseObject(JSONObject.toJSONString(infoVo), LogicColumnInfoVo.class);
                        for (LogicSyncColumn syncColumn : column.getColumns()) {
                            LogicDataColumn dataColumnSync = getLogicDataColumnByName(logicDataObj, syncColumn.getId());
                            syncColumn.setId(dataColumnSync.getId());
                        }
                    }
                    syncColumnStep.setColumns(JSON.toJSONString(columnInfoVos));

                    SyncColumnOperator syncColumnOperator = (SyncColumnOperator) OperatorEnum.getOperator(LogicDataSetConstant.STEP_SYNC_COLUMN, beanFactory);
                    syncColumnOperator.init(syncColumnStep);

                    newOperators.add(syncColumnOperator);
                    break;
                case LogicDataSetConstant.STEP_JOIN_TABLE:

                    JoinTableStep joinTableStep = (JoinTableStep) dataStep;
//                    todo 老代码了，先注释掉看看，没影响的话后续删掉这段代码。
//                    List<JSONObject> targetFiled = JSONObject.parseObject(joinTableStep.getTargetFiled(), List.class);
//                    List<JSONObject> sourceField = JSONObject.parseObject(joinTableStep.getSourceField(), List.class);
//                    for (int i = 0; i < sourceField.size(); i++) {
//                        String source = sourceField.get(i).getString("fieldId");
//                        String target = targetFiled.get(i).getString("fieldId");
//                        LogicDataColumn sourceColumn = logicDataColumnService.findLogicDataColumnById(source);
//                        LogicDataColumn targetColumn = logicDataColumnService.findLogicDataColumnById(target);
//                        sourceField.get(i).put("fieldId", dataColumn.get(sourceColumn.getName()));
//                        targetFiled.get(i).put("fieldId", dataColumn.get(targetColumn.getName()));
//                    }
//                    joinTableStep.setSourceField(JSON.toJSONString(sourceField));
//                    joinTableStep.setTargetFiled(JSON.toJSONString(targetFiled));
                    JoinTableOperator joinTableOperator = (JoinTableOperator) OperatorEnum.getOperator(LogicDataSetConstant.STEP_JOIN_TABLE, beanFactory);
                    joinTableOperator.init(joinTableStep);

                    newOperators.add(joinTableOperator);

                    break;
                case LogicDataSetConstant.STEP_FILTER_CONDITION:
                    FilterConditionStep filterConditionStep = (FilterConditionStep) dataStep;

                    FilterConditionOperator filterConditionOperator = (FilterConditionOperator) OperatorEnum.getOperator(LogicDataSetConstant.STEP_FILTER_CONDITION, beanFactory);
                    filterConditionOperator.init(filterConditionStep);
                    newOperators.add(filterConditionOperator);

                    break;
            }
        }
        return newOperators;
    }

    private LogicDataColumn getLogicDataColumnByName(LogicDataObj logicDataObj, String columnId) {
        LogicDataColumn column = logicDataColumnService.findLogicDataColumnById(columnId);
        String name = column.getName();
        return logicDataColumnService.getLogicDataColumnByName(name, logicDataObj.getId());
    }

    @NotNull
    public LogicDataObj copyLogicDataObj(String saveAsId, String userId, LogicDataObj logicDataObj) {
        //复制一份logicDataObj，并设置新的globalCode
        LogicDataObj newLogicDataObj = logicDataObjService.findLogicDataObjById(saveAsId);
        newLogicDataObj.setGlobalCode("GB_LOGIC_" + System.currentTimeMillis());
        newLogicDataObj.setIsFast("0");
        newLogicDataObj.setSql(logicDataObj.getSql());
        newLogicDataObj.setOwner(logicDataObj.getOwner());
        newLogicDataObj.setBelongType(logicDataObj.getBelongType());
        newLogicDataObj.setOperateUserId(userId);
        newLogicDataObj.setCode(logicDataObj.getCode());
        newLogicDataObj.setOwner(logicDataObj.getOwner());
        newLogicDataObj.setDbType(logicDataObj.getDbType());
        logicDataObjService.saveLogicDataObj(newLogicDataObj);
        return newLogicDataObj;
    }

    @Override
    public void save(SaveDataSetVo saveDataSetVo) {
        LogicDataObj logicDataObj = (LogicDataObj) this.baseDao.get(LogicDataObj.class, saveDataSetVo.getLogicDataSetId());
        logicDataObj.setOperateTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern(LogicDataSetConstant.DATE_FORMAT)));
        //保存的时候再去设置belong_type 到时候自助数据集列表根据这个值是否有值才显示
        logicDataObj.setBelongType(LogicDataSetConstant.BELONG_TYPE);
        logicDataObj.setDataType(LogicDataObjectUtil.SELF_DATASET_TYPE);
        logicDataObj.setName(saveDataSetVo.getName());
        logicDataObjService.saveLogicDataObjRelation(saveDataSetVo.getLogicDataSetId(), saveDataSetVo.getMetaDataObjIds());
        logicDataObjService.saveLogicDataObj(logicDataObj);
        saveRelevancy(Lists.newArrayList(saveDataSetVo.getClassifyId()), saveDataSetVo.getLogicDataSetId());

        // saveRegisterSpark(logicDataObj, false);
    }

    private void saveRegisterSpark(LogicDataObj logicDataObj, boolean isCreate) {
        try {
            String sql = getQuerySQL(logicDataObj.getId());
            registerTable(logicDataObj, sql, false);
        } catch (Exception e) {
            log.error("视图创建失败！", e);
            Assert.fail("视图创建失败！错误信息：" + e.getMessage());
        }
    }

    @Override
    public List<AbstractAtomDataSetOperator> getSetOperators(String dataSetId) {
        String relationId = getRelationId(dataSetId);
        List<AbstractAtomDataSetOperator> setOperators = stepRelationService.flushStep(relationId, null);
        if (setOperators == null) {
            setOperators = Lists.newArrayList();
        }
        return setOperators;
    }

    @Override
    public List<AbstractAtomDataSetOperator> getSetOperators(String dataSetId, DataSetStepRelation relation) {
        List<AbstractAtomDataSetOperator> setOperators = stepRelationService.flushStep(relation.getId(), null);
        if (setOperators == null) {
            setOperators = Lists.newArrayList();
        }
        return setOperators;
    }

    @Override
    public List<ColumnDataSetVo> getSyncColumnVo(Map<String, List<LogicDataColumn>> res) {
        List<ColumnDataSetVo> columnDataSetVos = Lists.newArrayList();

        for (Map.Entry<String, List<LogicDataColumn>> entry : res.entrySet()) {

            ColumnDataSetVo columnDataSetVo = new ColumnDataSetVo();

            ClassifierStat objById = logicDataObjService.findClassifier(entry.getKey());
            columnDataSetVo.setId(objById.getId());
            columnDataSetVo.setName(objById.getName());
            columnDataSetVo.setCode(objById.getCode());

            List<LogicSyncColumn> logicColumnInfoVos = Lists.newArrayList();
            List<LogicDataColumn> columnList = entry.getValue();
            for (LogicDataColumn logicDataColumn : columnList) {
                LogicSyncColumn columnInfoVo = new LogicSyncColumn();
                columnInfoVo.setId(logicDataColumn.getId());
                columnInfoVo.setName(logicDataColumn.getName());
                columnInfoVo.setCode(logicDataColumn.getCode());
                columnInfoVo.setMemo(logicDataColumn.getMemo());
                columnInfoVo.setDataTypeId(logicDataColumn.getDataType().getId());
                columnInfoVo.setFormat(logicDataColumn.getFormat());
                columnInfoVo.setIndexType(logicDataColumn.getIndexType());
                columnInfoVo.setBelongParentId(logicDataColumn.getBelongParentId());
                columnInfoVo.setColumnAlias(StringUtils.isBlank(logicDataColumn.getAlias()) ? logicDataColumn.getCode() : logicDataColumn.getAlias());
                columnInfoVo.setFuncExp(logicDataColumn.getFuncExp());

                logicColumnInfoVos.add(columnInfoVo);
            }
            columnDataSetVo.setNewColumn(logicColumnInfoVos);
            columnDataSetVos.add(columnDataSetVo);
        }
        return columnDataSetVos;
    }

    @Override
    public boolean checkDataType(String dT1, String dT2) {
        String checkName1 = checkStandDataType(dT1);
        if (StringUtils.isBlank(checkName1)) {
            StandardType dt = ddlOperationService.getDataTypeInfo(dT1);
            checkName1 = dt.getName();
        }
        String checkName2 = checkStandDataType(dT2);
        if (StringUtils.isBlank(checkName2)) {
            StandardType dt = ddlOperationService.getDataTypeInfo(dT2);
            checkName2 = dt.getName();
        }

        if (NUMBERTYPE.contains(checkName1.toUpperCase()) && NUMBERTYPE.contains(checkName2.toUpperCase())) return true;
        if (checkName1.equalsIgnoreCase(checkName2)) {
            return true;
        } else {
            return false;
        }
    }

    @Override
    public void createOldView(String viewName) throws Exception {
        List<LogicDataObj> logicDataObjList = new ArrayList<>();
        //根据视图名查出logicid
        String logicId = getLogicId(viewName);
        LogicDataObj logicDataObj = logicDataObjService.getLogicDataObjInfo(logicId).getLogicDataObj();
        logicDataObjList.add(logicDataObj);
        //递归构建查询logDataObj,因为可能logDataObj是由logDataObj生出来的
        List<LogicDataObj> logicAndRdbView = createLogicAndRdbView(logicDataObj, logicDataObjList);
        for (int i = logicAndRdbView.size() - 1; i >= 0; i--) {
            registerTable(logicAndRdbView.get(i), logicAndRdbView.get(i).getSql(), true);
        }
    }

    @Override
    public void createLogicDatObjSearchSql(String datasetId) throws Exception {
        LogicDataObj logicDataObj = logicDataObjService.findLogicDataObjById(datasetId);
        updataLogicDataSearchSql(datasetId);
    }

    private void updataLogicDataSearchSql(String dataSetId) throws Exception {

        List<LogicDataObj> logicDataObjList = new ArrayList<>();
        //String logicDataId="ec97be686ce249699f844bd7a3474915";
        LogicDataObj logicDataObj = logicDataObjService.getLogicDataObjInfo(dataSetId).getLogicDataObj();
        logicDataObjList.add(logicDataObj);
        //递归构建查询logDataObj,因为可能logDataObj是由logDataObj生出来的
        List<LogicDataObj> fatherAndSonLogicDataObjs = getFatherAndSonLogicDataObjs(logicDataObj, logicDataObjList);
        String grandpaLogicDataObjCode = "";
        for (int i = fatherAndSonLogicDataObjs.size() - 1; i >= 0; i--) {
            //logicDataObj.getOwner()==null 脏数据 按道理来说不应该有这种情况，但是表里面就是有这样的数据，先记录，先跳过去不做处理。
            if (fatherAndSonLogicDataObjs.get(i).getOwner() == null || logicDataObjService.findLogicDataObjById(fatherAndSonLogicDataObjs.get(i).getOwnerId()) == null)
                continue;
            //从物理表派生出来的logicdataobj的处理
            if (i == fatherAndSonLogicDataObjs.size() - 1) {
                grandpaLogicDataObjCode = fatherAndSonLogicDataObjs.get(i).getOwner().getCode();
                fatherAndSonLogicDataObjs.get(i).setSql("select * from " + fatherAndSonLogicDataObjs.get(i).getOwner().getCode());

            } else {
                //从logicdataobj派生出来的logicdataobj的处理
                LogicDataObj logicDataObjFather = logicDataObjService.getLogicDataObjInfo(fatherAndSonLogicDataObjs.get(i).getOwnerId()).getLogicDataObj();
                fatherAndSonLogicDataObjs.get(i).setSql("select * from ( " + logicDataObjFather.getSql() + " ) as " + grandpaLogicDataObjCode + "_" + System.currentTimeMillis());
            }
            logicDataObjService.updateLogicDataObj(fatherAndSonLogicDataObjs.get(i));
        }
    }

    @Override
    public void replaceLogicDataObjSearchsql() throws Exception {
        List<HashMap> allLogicDataObj = logicDataObjService.getAllLogicDataObjs();
        for (HashMap logicDataIdMap : allLogicDataObj) {
            String id = logicDataIdMap.get("id").toString();
            updataLogicDataSearchSql(id);
        }

    }

    /**
     * 获取logicdataobj的所有父亲爷爷的logicdataobj
     *
     * @param logicDataObj
     * @param logicDataObjList
     * @return
     * @throws Exception
     */
    private List<LogicDataObj> getFatherAndSonLogicDataObjs(LogicDataObj logicDataObj, List<LogicDataObj> logicDataObjList) throws Exception {
        log.info("----logicDataObjID:" + logicDataObj.getId());
        //logicDataObj.getOwner()==null 脏数据 按道理来说不应该有这种情况，但是表里面就是有这样的数据，先记录，先跳过去不作处理。
        if (logicDataObj.getOwner() != null && logicDataObjService.findLogicDataObjById(logicDataObj.getOwnerId()) != null && "LogicDataObj".equalsIgnoreCase(logicDataObj.getOwner().getType())) {
            LogicDataObj logicDataObj2 = logicDataObjService.getLogicDataObjInfo(logicDataObj.getOwnerId()).getLogicDataObj();
            logicDataObjList.add(logicDataObj2);
            getFatherAndSonLogicDataObjs(logicDataObj2, logicDataObjList);
        }
        return logicDataObjList;
    }


    private List<LogicDataObj> createLogicAndRdbView(LogicDataObj logicDataObj, List<LogicDataObj> logicDataObjList) throws Exception {
        if ("LogicDataObj".equalsIgnoreCase(logicDataObj.getOwner().getType())) {
            //registerTable(logicDataObj,logicDataObj.getSql(),true);
            LogicDataObj logicDataObj2 = logicDataObjService.getLogicDataObjInfo(logicDataObj.getOwnerId()).getLogicDataObj();
            logicDataObjList.add(logicDataObj2);
            createLogicAndRdbView(logicDataObj2, logicDataObjList);
        }
        return logicDataObjList;
    }


    private String checkStandDataType(String dT1) {

        Map<String, String> dataType = getStandDataTypeInfo(dT1);

        String type = getStandType(dataType.get("id"));
        if ("STANDARD".equalsIgnoreCase(type)) return dataType.get("name");

        return "";

    }


    private String getStandType(String id) {
        String querySql = "select code from t_md_element where id = :id";
        return this.baseDao.sqlQueryForValue(querySql, addParam("id", id).param());
    }

    private String getLogicId(String globalCode) {
        String sql = "select id from t_md_logic_dataobj where global_code = :global_code";
        return this.baseDao.sqlQueryForValue(sql, addParam("global_code", globalCode).param());
    }

    private Map<String, String> getStandDataTypeInfo(String dT1) {
        String querySql = "select owner_id as id,name from t_md_element where id = :id";
        return this.baseDao.sqlQueryForMap(querySql, addParam("id", dT1).param());
    }

    @Override
    public void registerTable(LogicDataObj logicDataObj, String sql, boolean isCreate) throws Exception {
        String schemaId = getSchemaIdByLogicDataObj(logicDataObj);
        System.out.println(String.format("schemaId:%s-------创建视图sql:%s-------视图名称:%s", schemaId, sql, logicDataObj.getGlobalCode()));
        createView(schemaId, isCreate, logicDataObj.getGlobalCode(), sql);
    }

    @Override
    public void registerTable(String schemaId, LogicDataObj logicDataObj, boolean isCreate) throws Exception {
        createView(schemaId, isCreate, logicDataObj.getGlobalCode(), logicDataObj.getSql());
    }

    @Override
    public void registerTable(String viewName, LogicDataObj logicDataObj, String sql, boolean isCreate) throws Exception {
        String schemaId = getSchemaIdByLogicDataObj(logicDataObj);
        createView(schemaId, isCreate, viewName, sql);
    }

    private void createView(String schemaId, boolean isCreate, String globalCode, String sql) throws Exception {
        boolean exitsView = ddlOperationService.isExitsView(schemaId, globalCode);
        if (exitsView) ddlOperationService.dropView(schemaId, globalCode, false);
        ddlOperationService.createView(schemaId, sql, globalCode, true);
    }

    protected String getSchemaIdByLogicDataObj(LogicDataObj logicDataObj) {
        String schemaId = "";
        ClassifierStat classifierStat = logicDataObjService.getClassifierStatByLogic(logicDataObj);
        if (classifierStat != null) {
            schemaId = classifierStat.getOwnerId();
        }
        return schemaId;
    }

    @Override
    public void dropView(String schemaId, LogicDataObj logicDataObj) throws Exception {
        ddlOperationService.dropView(schemaId, logicDataObj.getGlobalCode(), true);
    }

    @Override
    public List<Map> checkLogicDataSetUsed(String logicDataSetId) {
        List<Map> rresList = Lists.newArrayList();
        rresList.addAll(checkQuickAnalyze(logicDataSetId));
        rresList.addAll(checkUnionAndJoinFlag(logicDataSetId));
        return rresList;
    }

    private List<Map> checkUnionAndJoinFlag(String logicDataSetId) {
        String checkSQL = "select  " + "  d.name  " + "from  " + "  t_dataset_step c  " + "left join t_md_logic_dataobj d on  " + "  c.dataset_id = d.id  " + "where  " + "  c.id =(  " + "    select  " + "      a.dataset_step_id  " + "    from  " + "      t_dataset_step_attribute a  " + "    left join t_md_dataset_step_attribute b on  " + "      a.dataset_attribute_id = b.id  " + "    where  " + "      b.code = 'targetDataSetId'  " + "      and a.dataset_attribute_value = :id  " + "  )";
        return this.baseDao.sqlQueryForList(checkSQL, addParam("id", logicDataSetId).param());
    }

    private List<Map> checkQuickAnalyze(String logicDataSetId) {
        String checkSQL = "select name from t_md_logic_dataobj where owner_id = :id ";
        return this.baseDao.sqlQueryForList(checkSQL, addParam("id", logicDataSetId).param());
    }

    @Override
    public void registerModelView(List<AbstractAtomDataSetOperator> setOperators, String dataSetId) throws Exception {
        LogicDataObj dataObj = logicDataObjService.findLogicDataObjById(dataSetId);
        String sql = stepRelationService.getSQL(setOperators, null);
        String schemaId = getSchemaIdByLogicDataObj(dataObj);
        boolean exitsView = ddlOperationService.isExitsView(schemaId, dataObj.getGlobalCode());
        if (exitsView && setOperators.isEmpty()) return;
        ddlOperationService.dropView(schemaId, dataObj.getGlobalCode(), false);
        ddlOperationService.createView(schemaId, sql, dataObj.getGlobalCode(), false);
    }

    /**
     * 拿到logicobj的字段去构建sql
     */
    public void getLogicSqlByColumns(String logicDataObjId) {
        logicDataColumnService.findLogicDataColumnsByLogicDataObjId(logicDataObjId);
    }

    /**
     * 模型预览结果下载
     *
     * @param qo
     * @param response
     */
    @Override
    public void logicDataExport(LogicDataExportQo qo, HttpServletResponse response) {
        Assert.isTrue(StrUtil.isNotBlank(qo.getExcelName()), "文件名称不能为空");
        Integer fileOutputLimitSize = getDataPropertiesUtil.getFileOutputLimitSize();
        Integer pageSize = Optional.ofNullable(qo.getExcelLength()).map(k -> {
            Assert.isTrue(k <= fileOutputLimitSize, String.format("导出预览条数不可大于%s条", fileOutputLimitSize));
            return k;
        }).orElse(fileOutputLimitSize);
        qo.setPage(1);
        qo.setPageSize(pageSize);
        PageInfo preview = StringUtils.isBlank(qo.getDataSetId()) ? previewBySql(qo) : preview(qo);
        List dataList = preview.getDataList();
        Assert.isTrue(!dataList.isEmpty(), "无数据");
        try {
            ExcelWriter bigWriter = ExcelUtil.getBigWriter();
            bigWriter.write(dataList);
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(qo.getExcelName(), "UTF-8") + ".xlsx");
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setCharacterEncoding("utf-8");
            bigWriter.flush(response.getOutputStream());
        } catch (Exception e) {
            log.error("模型预览结果下载，文件下载失败", e);
            Assert.fail("文件下载失败");
        }
    }


    /**
     * 获取下载文件最大条数限制
     *
     * @return
     */
    @Override
    public Result<Integer> getExportLimit() {
        return Result.success(getDataPropertiesUtil.getFileOutputLimitSize());
    }

    /**
     * dataSetId 数据源id为空 根据前端传输的字段集合查询转跳的url
     * dataSetId 数据源id不为空 根据数据源字段集合查询转跳的url
     *
     * @param previewVo
     * @return
     */
    @Override
    public Map<String, List<ElementUrlVO>> getElementByPreview(PreviewVo previewVo) {
        List<?> objectList = StringUtils.isBlank(previewVo.getDataSetId()) ? previewVo.getColumnList() :
                logicDataColumnService.findLogicDataColumnsByLogicDataObjId(previewVo.getDataSetId());
        return getElementByPreview(objectList, previewVo.getTitleType());
    }

    /**
     * 获取数据穿透字段的转跳地址
     *
     * @param columns
     * @param titleType
     * @param <T>
     * @return
     */
    private <T> Map<String, List<ElementUrlVO>> getElementByPreview(List<T> columns, String titleType) {
        Map<String, List<ElementUrlVO>> ref = MapUtil.newHashMap();
        for (T column : columns) {
            if (column instanceof LogicDataColumn) {
                String columnCode = getColumnCode(column);
                String columnName = getColumnName(column);
                List<ElementUrlVO> elementUrl = getElementUrl(columnCode);
                if (elementUrl != null) {
                    ref.put("column".equals(titleType) ? columnCode : columnName, elementUrl);
                }
            } else {
                ref.put(column.toString(), getElementUrl(column.toString()));
            }
        }
        return ref;
    }

    /**
     * 获取字段code
     *
     * @param columns
     * @return
     */
    private String getColumnCode(Object columns) {
        if (columns instanceof LogicDataColumn) {
            String alias = ((LogicDataColumn) columns).getAlias();
            String code = StringUtils.isBlank(alias) ? ((LogicDataColumn) columns).getCode() : alias;
            return TableNameUtil.splitScheme(code);
        }
        return "";
    }

    /**
     * 获取字段名称
     *
     * @param columns
     * @return
     */
    private String getColumnName(Object columns) {
        if (columns instanceof LogicDataColumn) {
            return StringUtils.isNotBlank(((LogicDataColumn) columns).getName()) ? ((LogicDataColumn) columns).getName() : ((LogicDataColumn) columns).getCode();
        }
        return "";
    }

    /**
     * 根据字段查找配置的url
     *
     * @param code
     * @return
     */
    private List<ElementUrlVO> getElementUrl(String code) {
        String columnCode = code.toUpperCase();
        List<UrlConfigMapping> urlConfigMappingList = Arrays.asList(
                new UrlConfigMapping(urlConfig.getPersonStrList(), urlConfig.getPerson()),
                new UrlConfigMapping(urlConfig.getCarStrList(), urlConfig.getCar()),
                new UrlConfigMapping(urlConfig.getAjStrList(), urlConfig.getAj())
        );
        return urlConfigMappingList.stream()
                .filter(v -> toUpperCase(v.getStrList()).contains(columnCode))
                .map(UrlConfigMapping::getElementUrlList)
                .findFirst().orElse(null);
    }

    @Data
    private static class UrlConfigMapping {
        public final List<String> strList;
        public final List<ElementUrlVO> elementUrlList;
    }


    private List<String> toUpperCase(List<String> strList) {
        return ObjectUtil.isNotEmpty(strList) ? strList.stream().map(String::toUpperCase).collect(Collectors.toList()) : strList;
    }


}
