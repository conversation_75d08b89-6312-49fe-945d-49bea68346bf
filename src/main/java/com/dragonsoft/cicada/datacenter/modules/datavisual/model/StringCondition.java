package com.dragonsoft.cicada.datacenter.modules.datavisual.model;

import com.code.common.utils.StringUtils;
import com.code.meta.dml.standard.IMultCdin;
import com.code.meta.dml.standard.cdins.IQueryCdin;
import com.code.meta.dml.standard.cdins.MultCdin;
import com.code.meta.dml.standard.cdins.QueryCdins;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public class StringCondition extends AbsConditionWidget {

    String value;
    String logicalVal;

    @Override
    IMultCdin builderCondition(QueryCdins queryCdins) {
        IMultCdin multCdin = new MultCdin();
        if (!StringUtils.isBlank(value)) {
            String fieldCode = StringUtils.isBlank(this.field.getFieldAlias()) ? this.field.getCode() : this.field.getFieldAlias();
            multCdin.addCdin(this.getIQueryCdin(fieldCode, queryCdins));
        }
        return multCdin;
    }

    private IQueryCdin getIQueryCdin(String filed, QueryCdins queryCdins) {
        if ("=".equals(logicalVal)) {
            return queryCdins.eq(filed, value);
        } else if ("LIKE".equals(logicalVal)) {
            String[] vs = null;
            if (value.contains(",")) {
                vs = value.split(",");
            }
            if (value.contains("，")) {
                vs = value.split("，");
            }
            if (null != vs && vs.length > 0) {
                IMultCdin m = new MultCdin();
                List<IQueryCdin> qc = Arrays.stream(vs).map(n -> queryCdins.like(filed, n)).collect(Collectors.toList());
                m.ors(qc.toArray(new IQueryCdin[qc.size()]));
                return m;
            }

            return queryCdins.like(filed, value);
        } else if ("IN".equals(logicalVal)) {
            if (value.contains(",")) {
                String[] vs = value.split(",");
                return queryCdins.in(filed, vs);
            }

            if (value.contains("，")) {
                String[] vs = value.split("，");
                return queryCdins.in(filed, vs);
            }
            return queryCdins.eq(filed, value);
        } else if ("!=".equals(logicalVal)) {
            return queryCdins.ne(filed, value);
        }
        return null;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getLogicalVal() {
        return logicalVal;
    }

    public void setLogicalVal(String logicalVal) {
        this.logicalVal = logicalVal;
    }
}
