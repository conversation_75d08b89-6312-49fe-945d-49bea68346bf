package com.dragonsoft.cicada.datacenter.modules.datavisual.controller;

import com.code.common.utils.R;
import com.code.metadata.datavisual.WidgetCategories;
import com.dragoninfo.dfw.bean.Result;
import com.dragonsoft.cicada.datacenter.modules.datavisual.widgetmeta.IWidgetCategoriesBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@CrossOrigin
@RestController
@RequestMapping("/widgetCategories")
public class WidgetCategoriesController {

    @Autowired
    IWidgetCategoriesBuilder widgetCategoriesBuilder;

    @GetMapping("/getChartGroup")
    public Result getChartGroup() {
        List<WidgetCategories> list= widgetCategoriesBuilder.getChartGroup();
        return Result.toResult(R.ok(list));
    }


}
