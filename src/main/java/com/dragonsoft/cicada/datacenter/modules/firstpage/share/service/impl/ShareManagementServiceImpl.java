package com.dragonsoft.cicada.datacenter.modules.firstpage.share.service.impl;

import cn.hutool.core.util.StrUtil;
import com.code.common.paging.PageInfo;
import com.code.metadata.sm.ServiceMeta;
import com.code.metadata.sm.ServicePublication;
import com.code.metaservice.ddl.ILogicDataObjService;
import com.code.metaservice.sm.IServiceMetaService;
import com.dragoninfo.dfw.entity.TSysAuthObj;
import com.dragoninfo.dfw.entity.TSysAuthObjFunc;
import com.dragoninfo.dfw.entity.TSysFuncBase;
import com.dragoninfo.dfw.service.SysAuthObjService;
import com.dragoninfo.dfw.service.SysAuthUserService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.service.IDataSetOperationService;
import com.dragonsoft.cicada.datacenter.modules.firstpage.share.service.ShareManagementService;
import com.dragonsoft.cicada.datacenter.modules.firstpage.share.vo.ResourceTypeEnum;
import com.dragonsoft.cicada.datacenter.modules.firstpage.share.vo.ResultResource;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.service.IUserService;
import com.fw.service.BaseService;
import com.fw.service.annotation.Service;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
@Slf4j
public class ShareManagementServiceImpl extends BaseService implements ShareManagementService {

    @Autowired
    private IUserService userService;

    @Autowired
    private IDataSetOperationService dataSetOperationService;

    @Autowired
    private ILogicDataObjService logicDataObjService;

    @Autowired
    private SysAuthUserService sysAuthUserService;

    @Autowired
    private SysAuthObjService sysAuthObjService;

    @Autowired
    private IServiceMetaService serviceMetaService;

    @Override
    public List<ResultResource> getMyResources(String resourceType, String dataTreeId, String resourceName,String myUserId) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        List<ResultResource> resources = new ArrayList<>();
        if ("dataSet".equalsIgnoreCase(resourceType)){
            PageInfo pageInfo = new PageInfo();
            pageInfo.setPageSize(10);
            pageInfo.setPageIndex(1);
            List<String> allAuthDatasetId = userService.getAllAuthDatasetId(myUserId);
            PageInfo logicDataObPageInfo = logicDataObjService.getLogicDataObPageInfo(dataTreeId, resourceName, pageInfo, myUserId,"",allAuthDatasetId);
            //long totalCount = logicDataObPageInfo.getTotalCount();
            pageInfo.setPageSize((int) logicDataObPageInfo.getTotalCount());
            logicDataObPageInfo = logicDataObjService.getLogicDataObPageInfo(dataTreeId, resourceName, pageInfo, myUserId,"",allAuthDatasetId);
            List dataList = logicDataObPageInfo.getDataList();
            try {
                for (Object o : dataList) {
                    Map map = (Map) o;
                    ResultResource resultResource = new ResultResource();
                    resultResource.setResourceName((String) map.get("name"));
                    resultResource.setCreateTime( sdf.parse((String) map.get("operate_time")));
                    resultResource.setResourceId((String) map.get("id"));
                    resources.add(resultResource);
                }
            }catch (Exception e){
             log.error(e.getMessage(),e);
            }
        }else if ("dataService".equalsIgnoreCase(resourceType)){
            List<ServiceMeta> list = this.baseDao.queryForList(" from ServiceMeta where operateUserId = :myUserId order by operateTime desc ", this.addParam("myUserId", myUserId).param());
            for (ServiceMeta serviceMeta : list) {
                ResultResource resultResource = new ResultResource();
                resultResource.setResourceId(serviceMeta.getId());
               /* try {
                    resultResource.setCreateTime(sdf.parse(serviceMeta.getOperateTime()));
                } catch (ParseException e) {
                 log.error(e.getMessage(),e);
                }*/
                ServicePublication servicePublication = serviceMeta.getServicePublication();
                if (null == servicePublication ||(!"4".equals(servicePublication.getServiceType()))){
                    continue;
                }
                String sql = "select element_id from t_md_classify_element where element_id = :id";
                String value = this.baseDao.sqlQueryForValue(sql, this.addParam("id", servicePublication.getId()).param());
                if (StrUtil.isEmpty(value)) {
                    continue;
                }
                resultResource.setResourceName(servicePublication.getName());
                resources.add(resultResource);
            }
        }

        return resources;
    }

    @Override
    public void addShareByCondition(String objType, List<String> objIds, String resourceType, List<String> resourceIds, String myUserId) {
       /* if ("2".equals(objType)){
            //角色
            String hql = " from TSysAuthObjRel tsr where tsr.toAuthObj.id in (:objIds) and tsr.relationType = '1' ";
            List<TSysAuthObjRel> tSysAuthObjRels = this.baseDao.queryForList(hql, this.addParam("objIds", objIds).param());
            for (TSysAuthObjRel tSysAuthObjRel : tSysAuthObjRels) {
                objIds.add(tSysAuthObjRel.getFromAuthObj().getId());
            }
        }*/
      /*  if ("2".equals(objType)){
            //角色
            for (String objId : objIds) {
                String hql = " from TSysAuthObjRel tsr where tsr.toAuthObj.id = :objId and tsr.relationType = '1' ";
                List<TSysAuthObjRel> tSysAuthObjRels = this.baseDao.queryForList(hql, this.addParam("objId", objId).param());
                for (TSysAuthObjRel tSysAuthObjRel : tSysAuthObjRels) {
                    String authId = tSysAuthObjRel.getFromAuthObj().getId();
                    for (String resourceId : resourceIds) {
                        Map<String,Object> map = new HashMap<>();
                        map.put("authId",authId);
                        map.put("resourceId",resourceId);
                        map.put("objId",objId);
                        String tsyshql = "from TSysAuthObjFunc tsf where tsf.tSysAuthObj.id = :authId and tsf.tSysFuncBase.funcCode = :resourceId " +
                                " and tsf.createObj.id = :objId ";
                        Object o = this.baseDao.queryForObject(tsyshql, map);
                        if (o == null){
                            TSysAuthObjFunc tSysAuthObjFunc = new TSysAuthObjFunc();
                            Map<String,String> mapObj = new HashMap<>();
                            mapObj.put("id",authId);
                            TSysAuthObj tSysAuthObj = (TSysAuthObj) sysAuthObjService.query(mapObj);
                            //TSysAuthObj tSysAuthObj = (TSysAuthObj) sysAuthUserService.query(mapObj);
                            String hqlFunc = " from TSysFuncBase where funcCode = :funcCode ";
                            TSysFuncBase tSysFuncBase = new TSysFuncBase();
                            tSysFuncBase = (TSysFuncBase) this.baseDao.queryForObject(hqlFunc, this.addParam("funcCode", resourceId).param());
                            if (tSysFuncBase == null){
                                tSysFuncBase = new TSysFuncBase();
                                tSysFuncBase.setCreateTime(new Timestamp(new Date().getTime()));
                                tSysFuncBase.setDescription(authId);
                                tSysFuncBase.setFuncCode(resourceId);
                                tSysFuncBase.setEnableState("1");
                                tSysFuncBase.setFuncName(resourceId);
                                if ("dataSet".equalsIgnoreCase(resourceType)){
                                    tSysFuncBase.setFuncType("1");
                                }else if ("dataService".equalsIgnoreCase(resourceType)){
                                    tSysFuncBase.setFuncType("4");
                                }
                                this.baseDao.save(tSysFuncBase);
                            }

                            tSysAuthObjFunc.settSysAuthObj(tSysAuthObj);
                            tSysAuthObjFunc.settSysFuncBase(tSysFuncBase);
                            tSysAuthObjFunc.setCreateTime(new Date());
                            tSysAuthObjFunc.setCreateObj(tSysAuthObjRel.getToAuthObj());
                            this.baseDao.save(tSysAuthObjFunc);
                        }
                    }
                }

            }

        }*/
        for (String objId : objIds) {
            for (String resourceId : resourceIds) {
                Map<String,Object> map = new HashMap<>();
                map.put("objId",objId);
                map.put("resourceId",resourceId);
                String hql = "from TSysAuthObjFunc tsf where tsf.tSysAuthObj.id = :objId and tsf.tSysFuncBase.funcCode = :resourceId ";
                        //" and tsf.createObj.id is null ";
                Object o = this.baseDao.queryForObject(hql, map);
                if (o == null){
                    TSysAuthObjFunc tSysAuthObjFunc = new TSysAuthObjFunc();
                    Map<String,String> mapObj = new HashMap<>();
                    mapObj.put("id",objId);
                    TSysAuthObj tSysAuthObj = (TSysAuthObj) sysAuthObjService.query(mapObj);
                    //TSysAuthObj tSysAuthObj = (TSysAuthObj) sysAuthUserService.query(mapObj);
                    String hqlFunc = " from TSysFuncBase where funcCode = :funcCode ";
                    TSysFuncBase tSysFuncBase = new TSysFuncBase();
                    tSysFuncBase = (TSysFuncBase) this.baseDao.queryForObject(hqlFunc, this.addParam("funcCode", resourceId).param());
                    if (tSysFuncBase == null){
                        tSysFuncBase = new TSysFuncBase();
                        tSysFuncBase.setCreateTime(new Timestamp(new Date().getTime()));
                        tSysFuncBase.setDescription(objId);
                        tSysFuncBase.setFuncCode(resourceId);
                        tSysFuncBase.setEnableState("1");
                        tSysFuncBase.setFuncName(resourceId);
                      /*  if ("dataSet".equalsIgnoreCase(resourceType)){
                            tSysFuncBase.setFuncType("1");
                        }else if ("dataService".equalsIgnoreCase(resourceType)){
                            tSysFuncBase.setFuncType("4");
                        }else if ("dashboard".equalsIgnoreCase(resourceType)){
                            tSysFuncBase.setFuncType("5");
                        }else if ("aiService".equalsIgnoreCase(resourceType)){
                            tSysFuncBase.setFuncType("6");
                        }*/
                        tSysFuncBase.setFuncType(ResourceTypeEnum.getCodeByIgnoreName(resourceType));
                        this.baseDao.save(tSysFuncBase);
                    }

                    tSysAuthObjFunc.settSysAuthObj(tSysAuthObj);
                    tSysAuthObjFunc.settSysFuncBase(tSysFuncBase);
                    tSysAuthObjFunc.setCreateTime(new Date());
                    this.baseDao.save(tSysAuthObjFunc);
                }
            }
        }
    }

    @Override
    public void cancelSharesByCondition(List<Map<String, String>> resources) {
        for (Map<String, String> resource : resources) {
            String objId = resource.get("objId");
            String objType = resource.get("objType");
            String funcCode = resource.get("funcCode");
            /*if ("2".equals(objType)){
                //角色
                String relHql = " from TSysAuthObjRel tsr where tsr.toAuthObj.id = :objId and tsr.relationType = '1' ";
                List<TSysAuthObjRel> tSysAuthObjRels = this.baseDao.queryForList(relHql, this.addParam("objId", objId).param());
                for (TSysAuthObjRel tSysAuthObjRel : tSysAuthObjRels) {
                    Map<String,Object> map = new HashMap<>();
                    map.put("objId",tSysAuthObjRel.getFromAuthObj().getId());
                    map.put("funcCode",funcCode);
                    String delSql = "delete from t_sys_auth_obj_func where obj_id = :objId and func_code = :funcCode";
                    this.baseDao.executeSqlUpdate(delSql,map);
                }
            }*/
           /* if ("2".equals(objType)) {
                //角色
                String relHql = " from TSysAuthObjRel tsr where tsr.toAuthObj.id = :objId and tsr.relationType = '1'  ";
                List<TSysAuthObjRel> tSysAuthObjRels = this.baseDao.queryForList(relHql, this.addParam("objId", objId).param());
                for (TSysAuthObjRel tSysAuthObjRel : tSysAuthObjRels) {
                    Map<String, Object> map = new HashMap<>();
                    map.put("objId", tSysAuthObjRel.getFromAuthObj().getId());
                    map.put("funcCode", funcCode);
                    map.put("roleId", objId);
                    String delSql = "delete from t_sys_auth_obj_func where obj_id = :objId and func_code = :funcCode " +
                            " and create_id = :roleId ";
                    this.baseDao.executeSqlUpdate(delSql, map);
                }
            }*/
            Map<String,Object> map = new HashMap<>();
            map.put("objId",objId);
            map.put("funcCode",funcCode);
            Object o = this.baseDao.queryForObject("from TSysAuthObjFunc tsf where tsf.tSysAuthObj.id = :objId and tsf.tSysFuncBase.funcCode = :funcCode",map);
            if (o != null){
                String delSql = "delete from t_sys_auth_obj_func where obj_id = :objId and func_code = :funcCode";
                this.baseDao.executeSqlUpdate(delSql,map);
            }
        }
    }

    @Override
    public List<TSysAuthObj> getObjsByResourceId(String resourceId) {
        Map<String,String> map = new HashMap<>();
        map.put("funcCode",resourceId);
        List<TSysAuthObjFunc> funcs = this.baseDao.queryForList("from TSysAuthObjFunc tf where tf.tSysFuncBase.funcCode = :funcCode ",map);
        List<TSysAuthObj> objs = new ArrayList<>();
        for (TSysAuthObjFunc tSysAuthObjFunc : funcs) {
            objs.add(tSysAuthObjFunc.gettSysAuthObj());
        }
        return objs;
    }


}
