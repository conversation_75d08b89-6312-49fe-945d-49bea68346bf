package com.dragonsoft.cicada.datacenter.modules.firstpage.share.service.impl;

import com.code.common.paging.PageInfo;
import com.code.common.utils.StringUtils;
import com.code.metadata.res.common.ClassifierStat;
import com.code.metadata.res.ddl.LogicDataObj;
import com.code.metaservice.dataSet.IDataSetTreeService;
import com.code.metaservice.datawarehouse.model.DatasetTreeModel;
import com.code.metaservice.ddl.ILogicDataObjService;
import com.dragoninfo.dfw.entity.TSysAuthObj;
import com.dragoninfo.dfw.entity.TSysAuthUser;
import com.dragoninfo.dfw.entity.TSysFunc;
import com.dragoninfo.dfw.service.SysFuncService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.service.IDataSetOperationService;
import com.dragonsoft.cicada.datacenter.modules.firstpage.share.service.DataShareAuthRelService;
import com.dragonsoft.cicada.datacenter.modules.firstpage.share.service.ShareManagementService;
import com.dragonsoft.cicada.datacenter.modules.firstpage.share.service.ShareService;
import com.dragonsoft.cicada.datacenter.modules.firstpage.share.vo.ResultResource;
import com.dragonsoft.cicada.datacenter.modules.firstpage.share.vo.ResultUserVo;
import com.dragonsoft.cicada.datacenter.modules.firstpage.share.vo.ShareVo;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Transactional
@PropertySource("classpath:case-config.properties")
public class ShareServiceImpl implements ShareService {

    @Autowired
    private DataShareAuthRelService dataShareAuthRelService;

    @Autowired
    private ShareManagementService shareManagementService;

    @Autowired
    private IDataSetOperationService dataSetOperationService;

    @Autowired
    private IDataSetOperationService operationService;

    @Autowired
    private IDataSetTreeService dataSetTreeService;

    @Autowired
    private ILogicDataObjService logicDataObjService;

    @Autowired
    private SysFuncService sysFuncService;


    @Value("${standModel}")
    private boolean standModel;

    @Value("${sceModel}")
    private boolean sceModel;

    @Override
    public PageInfo getSharesByCondition(ShareVo fromShareVo, String userId) {
        return dataShareAuthRelService.getSharesFromOthers(fromShareVo.getPageNum(),fromShareVo.getPageSize(),userId,fromShareVo.getResourceName(),
                fromShareVo.getResourceType(),fromShareVo.getFromUserIds());
    }

    @Override
    public List<ResultUserVo> getOtherUsers(String myUserId,String objType) {
        List<TSysAuthUser> otherUsers = dataShareAuthRelService.getOtherUsers(myUserId,objType);
        List<ResultUserVo> resultUserVos = new ArrayList<>();
        for (TSysAuthUser otherUser : otherUsers) {
            ResultUserVo resultUserVo = new ResultUserVo();
            resultUserVo.setUserId(otherUser.getId());
            resultUserVo.setUsername(otherUser.getObjName());
            resultUserVos.add(resultUserVo);
        }
        return resultUserVos;
    }

    @Override
    public PageInfo getMyShares(ShareVo shareVo, String myUserId) {
        return dataShareAuthRelService.getMySharesByPage(shareVo,myUserId);
    }

    @Override
    public void addShares(ShareVo shareVo, String myUserId) {
        if (shareVo.getSharedUserIds().size() <= 0 || shareVo.getResourceIds().size() <= 0){
            throw new RuntimeException("分享对象和资源不能为空！");
        }
        shareManagementService.addShareByCondition(shareVo.getUserType(),shareVo.getSharedUserIds(),shareVo.getResourceType(),shareVo.getResourceIds(),myUserId);
    }

    @Override
    public List<ResultResource> getResourceByCondition(ShareVo shareVo, String myUserId) {

        return shareManagementService.getMyResources(shareVo.getResourceType(),shareVo.getDataSetTreeId(),
                shareVo.getResourceName(),myUserId);
    }

    @Override
    public void cancelShare(ShareVo shareVo) {
        if (shareVo.getResources().size() <= 0){
            throw new RuntimeException("分享对象和资源不能为空！");
        }
        shareManagementService.cancelSharesByCondition(shareVo.getResources());
    }

    @Override
    public List<DatasetTreeModel> getMyDataSetResource(String userId) {
        //我的数据集
        List<DatasetTreeModel> datasetTreeModels = queryDataSetTree(userId, true, "");
        if (!standModel){
            DatasetTreeModel parent = datasetTreeModels.get(0);
            List<DatasetTreeModel> children = parent.getChildren();

            for (int i = 0; i < children.size(); i++) {
                if ("标准模型".equals(children.get(i).getName())){
                    children.remove(i);
                    break;
                }
            }
        }

        //数据仓库
        List<DatasetTreeModel> treeModelList = operationService.querySourceDatasetTree(userId, "",null);
        //去掉场景案例
        if (!sceModel){
            for (int i = 0; i < treeModelList.size(); i++) {
                if ("场景案例".equals(treeModelList.get(i).getName())){
                    treeModelList.remove(i);
                }
            }
        }
        datasetTreeModels.addAll(treeModelList);
        return datasetTreeModels;
    }

    @Override
    public List<TSysAuthObj> getObjsByResourceId(String resourceId) {
        List<TSysAuthObj> objsByResourceId = shareManagementService.getObjsByResourceId(resourceId);
        return objsByResourceId;
    }


    private List<DatasetTreeModel> queryDataSetTree(String userId, boolean hasLogicDataObject, String currentDataSetId) {


        //获取同库的数据集
        String catalogId = getCatalogId(currentDataSetId);

        List<DatasetTreeModel> datasetTreeModels = dataSetTreeService.queryDataSetTree(hasLogicDataObject, userId, catalogId);

        List<TSysFunc> tSysFuncs = gettSysFuncs(userId);
        List<String> funcIds = tSysFuncs.stream().map(s -> s.getFuncCode()).collect(Collectors.toList());

        List<String> newList = addFuncIds(datasetTreeModels, funcIds);

        datasetTreeModels = datasetTreeModels.stream().filter(s -> newList.indexOf(s.getId()) != -1).collect(Collectors.toList());
        return datasetTreeModels;
    }

    protected String getCatalogId(String currentDataSetId) {
        String schemaId = "";
        if (StringUtils.isNotBlank(currentDataSetId)) {
//            ClassifierStat obj = classifierStatService.getClassifierStat(ClassifierStat.class, currentDataSetId);
            LogicDataObj obj = logicDataObjService.findLogicDataObjById(currentDataSetId);
            ClassifierStat classifierStat = logicDataObjService.getClassifierStatByLogic(obj);

            if (classifierStat == null) return "";
            schemaId = classifierStat.getOwner().getOwnerId();
        }
        return schemaId;
    }

    private List<TSysFunc> gettSysFuncs(String userId) {
        Map<String, String> params = Maps.newHashMap();
        params.put("description", userId);
        params.put("func_type", "2");
        return sysFuncService.queryList(params);
    }

    private List<String> addFuncIds(List<DatasetTreeModel> datasetTreeModels, List<String> funcIds) {

        for (DatasetTreeModel datasetTreeModel : datasetTreeModels) {
            funcIds.add(datasetTreeModel.getId());
            for (DatasetTreeModel child : datasetTreeModel.getChildren()) {
                funcIds.add(child.getId());
            }
        }
        return funcIds;
    }
}
