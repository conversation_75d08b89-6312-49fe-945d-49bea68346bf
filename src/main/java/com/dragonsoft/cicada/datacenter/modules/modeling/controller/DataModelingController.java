package com.dragonsoft.cicada.datacenter.modules.modeling.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.parser.Feature;
import com.code.common.mist.plugin.config.service.impl.PluginConfigService;
import com.code.common.paging.PageInfo;
import com.code.common.sch.model.EnumJobStatus;
import com.code.common.schedulectr.scheduleclient.schemeconfig.SimpleJobInfo;
import com.code.common.utils.R;
import com.code.common.utils.StringUtils;
import com.code.common.utils.assertion.Assert;
import com.code.common.utils.io.SerializableMsgCodec;
import com.code.dataset.operator.column.synccolumn.vo.LogicSyncColumn;
import com.code.dragonsoft.dataquery.service.QueryDataService;
import com.code.dragonsoft.dataquery.service.querymodel.ColumnDataModel;
import com.code.metadata.etl.trans.*;
import com.code.metadata.res.structured.rdb.*;
import com.code.metaservice.business.directory.BusiClassifyService;
import com.code.metaservice.core.ClassifierService;
import com.code.metaservice.datawarehouse.model.DatasetTreeModel;
import com.code.metaservice.etl.trans.TransMetaService;
import com.code.metaservice.res.structured.rdb.IRdbDataObjService;
import com.code.mist.builder.model.DubboResult;
import com.code.mist.builder.model.TreeNode;
import com.code.mist.builder.model.trans.TransMetaVo;
import com.code.mist.builder.service.dubbo.ITransformApiService;
import com.code.ms.domain.application.productionline.dto.RefreshTransStatusDTO;
import com.code.thirdplugin.cicada.sql.meta.meta.input.CicadaStandardSqlInput;
import com.code.thirdplugin.cicada.sql.page.page.controller.CicadaStandardInputController;
import com.code.thirdplugin.cicada.sql.page.page.service.CicadaStandardSqlInputService;
import com.code.thirdplugin.cicada.sql.page.page.service.CicadaStandardSqlOutputService;
import com.dragoninfo.dfw.annotation.FuncScanAnnotation;
import com.dragoninfo.dfw.annotation.ValidateAndLogAnnotation;
import com.dragoninfo.dfw.bean.Result;
import com.dragonsoft.cicada.datacenter.common.token.GlobalConstant;
import com.dragonsoft.cicada.datacenter.common.utils.DatasetTreeModelUtils;
import com.dragonsoft.cicada.datacenter.common.utils.UserContextUtil;
import com.dragonsoft.cicada.datacenter.modules.common.service.CommonBusiClassifyService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.service.IDataSetEditService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.vo.LogicDataSet.ColumnDataSetVo;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.vo.LogicDataSet.LogicDataSetVo;
import com.dragonsoft.cicada.datacenter.modules.dataplan.externaldatasources.controller.DataWarehouseController;
import com.dragonsoft.cicada.datacenter.modules.dataplan.externaldatasources.vo.SQLModelVo;
import com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.service.IServiceManagementService;
import com.dragonsoft.cicada.datacenter.modules.modeling.aimodeling.vo.ModelTreeResult;
import com.dragonsoft.cicada.datacenter.modules.modeling.service.DataMiningService;
import com.dragonsoft.cicada.datacenter.modules.modeling.service.DataModelingService;
import com.dragonsoft.cicada.datacenter.modules.modeling.service.MLSQLService;
import com.dragonsoft.cicada.datacenter.modules.modeling.service.MlsqlLogManageService;
import com.dragonsoft.cicada.datacenter.modules.modeling.vo.ExcelDownloadVo;
import com.dragonsoft.cicada.datacenter.modules.modeling.vo.ModelTreeVo;
import com.dragonsoft.cicada.datacenter.modules.modeling.vo.TransClassifyVo;
import com.dragonsoft.cicada.datacenter.modules.supermarkmodel.service.IMyModelService;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.service.IFunctionService;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.service.IUserService;
import com.fw.tenon.excel.TenonExcelUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import net.sf.json.JsonConfig;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.StreamUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.net.URLEncoder;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@CrossOrigin
@RestController
@RequestMapping(value = "/dataModeling")
@FuncScanAnnotation(code = "processModeling", name = "数据建模", parentCode = "dataModeling")

public class DataModelingController {

    @Autowired
    private CommonBusiClassifyService commonBusiClassifyService;

    @Autowired
    private DataModelingService dataModelingService;

    @Autowired
    private TransMetaService transMetaService;

    @Autowired
    private ITransformApiService transformApiService;

    @Autowired
    private BusiClassifyService busiClassifyService;

    @Autowired
    PluginConfigService pluginConfigService;

    @Autowired
    MlsqlLogManageService mlsqlLogManageService;

    @Autowired
    private CicadaStandardSqlInputService standardSqlInputService;

    @Autowired
    private DataWarehouseController dataWarehouseController;

    @Autowired
    DataMiningService dataMiningService;

    @Autowired
    private IMyModelService myModelServiceImpl;

    @Autowired
    private IServiceManagementService serviceManagementService;

    @Autowired
    IDataSetEditService editService;

    @Autowired
    PreviewController previewController;

    @Autowired
    private ClassifierService classifierService;

    @Autowired
    private QueryDataService queryDataService;

    @Autowired
    MLSQLService mlsqlService;

    @Autowired
    CicadaStandardInputController cicadaStandardInputController;

    @Autowired
    private CicadaStandardSqlOutputService standardSqlOutputService;

    @Autowired
    private IRdbDataObjService rdbDataObjService;

    @Autowired
    private IUserService userService;

    @Value("${fileOutput.limit.size:0}")
    private String limitSize;

    @Autowired
    private IFunctionService functionService;

    @GetMapping(value = "/modifyColumn")
    public Result modifyColumn(String tranStepId,
                               String field,
                               String length,
                               String precsn,
                               String valType,
                               String filterExpress,
                               String uniqueValue) {
        this.dataModelingService.updateOutputColumn(tranStepId, field, Integer.parseInt(length),
                StringUtils.isNotBlank(precsn) ? Integer.parseInt(precsn) : 0, valType, filterExpress, uniqueValue);
        return Result.toResult(R.ok());
    }

    @GetMapping(value = "/queryTransTree")
    public Result queryTransTree(String parentId, String parentName, String dirType, HttpServletRequest request) {
        String userId = UserContextUtil.getUserIdByHttpRequest(request);
        List<Map<String, Object>> treeNodes = dataModelingService.getModelingTransTreeNodes("", "", userId, dirType);
        if (CollUtil.isEmpty(treeNodes)) {
            treeNodes = new ArrayList<>();
        }
        if (userService.isAdmin(userId)&&functionService.isAuthToAdmin()) {
            Map<String, String> userMap = dataModelingService.getEtlTransUsers(userId);
            Map<String, Object> treeModel = JSON.parseObject(JSON.toJSONString(DatasetTreeModelUtils.buildPersonalDataset("他人空间")));
            treeModel.put("children", new ArrayList<>());
            userMap.forEach((user, name) -> {
                List<Map<String, Object>> userNodeList = dataModelingService.getModelingTransTreeNodes("", "", user, dirType);
                for (Map<String, Object> map : userNodeList) {
                    if ("我的空间".equals(MapUtil.getStr(map, "name"))) {
                        map.put("name", name);
                        map.put("label", name);
                        map.put("pId", MapUtil.getStr(treeModel, "id"));
                    }
                }
                List<Map<String, Object>> others = MapUtil.get(treeModel, "children", new TypeReference<List<Map<String, Object>>>() {
                });
                others.addAll(userNodeList);
                treeModel.put("children", others);
            });
            treeNodes.add(treeModel);

        }
        //当parentId为-1是移动 或者新建获取目录树 不添加分享
/*
        if (!"-1".equals(parentId)) {
            List<BaseBusiClassify> dig_dir_standard = busiClassifyService.queryTransClassifyList("DIG_BASE_STANDARD");
            if(CollectionUtils.isNotEmpty(dig_dir_standard)){
                BaseBusiClassify baseBusiClassify = dig_dir_standard.get(0);
                if(baseBusiClassify.getOperateUserId().equals(userId)) {
                    DatasetTreeModel datasetTreeModel = new DatasetTreeModel();
                    datasetTreeModel.setId(baseBusiClassify.getId());
                    datasetTreeModel.setpId(baseBusiClassify.getId());
                    datasetTreeModel.setName(baseBusiClassify.getName());
                    datasetTreeModel.setIsParent(true);
                    datasetTreeModel.setOpen(true);
                    treeNodes.add(datasetTreeModel);
                }
            }

        }*/
        return Result.toResult(R.ok(treeNodes));
    }

    @GetMapping(value = "/moveDirectory")
    public Result moveDirectory(String curryClassifyId, String newParentClassifyId, String dirType) {

        return Result.toResult(dataModelingService.moveDirectory(curryClassifyId, newParentClassifyId, dirType));
    }

    @GetMapping(value = "/getDownloadSize")
    public Result getDownloadSize() {

        return Result.success(limitSize);
    }


    /**
     * 数据资源------原始数据:获取关系库的视图
     *
     * @return
     */
    @PostMapping(value = "/getOriginDataListByView")
    public Result getOriginDataListByView(@RequestBody Map<String, String> resMap, HttpServletRequest request) {
        String userId = (String) request.getSession().getAttribute("userId");
        String condition = resMap.get("condition");
        String pageSize = resMap.get("pageSize");
        String page = resMap.get("page");
//        boolean isStdlib = Boolean.valueOf(resMap.get("isStdlib"));
        List<Map<String, Object>> externalFile = dataModelingService.getOriginDataListByView(condition, pageSize, page, false, userId);
        return Result.toResult(R.ok(externalFile));
    }


    @GetMapping("/getTreeAllNode")
    public Result getTreeDir(String dirType, HttpServletRequest request) {
        String userId = (String) request.getSession().getAttribute("userId");
        List<ModelTreeVo> list = dataModelingService.getTreeNode(userId, dirType);
        return Result.toResult(R.ok(list));
    }

    @PostMapping(value = "/createTransClassify")
    public Result createTransClassify(@RequestBody Map reqMap, HttpServletRequest request) {
        String userId = (String) request.getSession().getAttribute("userId");
        String classifyId = reqMap.get("classifyId").toString();
        String classifyName = reqMap.get("classifyName").toString();
        String dirType = reqMap.get("dirType").toString();
        return Result.toResult(dataModelingService.createTransClassify(StringUtils.isBlank(classifyId) ? null : classifyId, classifyName, dirType, userId));
    }

    @GetMapping(value = "/updateTransClassify")
    public Result updateTransClassify(String currClassifyId, String transClassifyName) {
        return Result.toResult(dataModelingService.updateTransClassify(currClassifyId, transClassifyName));
    }

    @GetMapping(value = "/deleteTransClassify")
    public Result deleteTransClassify(String transClassifyId) {
        DubboResult<List<TreeNode>> dubboResult = this.transformApiService.getTreeNodes(transClassifyId, "xxx", null, "TRANS_DIR");
        List<TreeNode> list = dubboResult.getData();
        for (int i = 0; i < list.size(); i++) {
            if (StringUtils.isBlank(list.get(i).getRunMode())) {
                return Result.toResult(R.error("请按层级删除!"));
            }
        }
        myModelServiceImpl.deleteMarkModelByBusiClassifyId(transClassifyId); //删除模型市场数据

        DubboResult deleteResult = this.transformApiService.deleteTransClassify(transClassifyId);
        return Result.toResult(R.ok(deleteResult.getData()));
    }

    @GetMapping(value = "/isDeleteTransClassify")
    public Result isDeleteTransClassify(String transClassifyId) {
        List<String> allClassifyIds = commonBusiClassifyService.queryBusiClassifyIdList(transClassifyId);
        List<String> allTransIds = Lists.newArrayList();
        List<String> elementIdsByClassifyIds = commonBusiClassifyService.getElementIdsByClassifyIds(allClassifyIds);
        allTransIds.addAll(elementIdsByClassifyIds);

        return Result.toResult(R.ok(dataModelingService.isExistReference(allTransIds)));
    }

    @PostMapping(value = "/queryTransList")
    public Result queryTransList(@RequestBody Map queryTransMap, HttpServletRequest request) {
        String userId = (String) request.getSession().getAttribute("userId");
        String condition = (String) queryTransMap.get("condition");
        String state = (String) queryTransMap.get("state");
        String dirId = (String) queryTransMap.get("dirId");
        String dirType = (String) queryTransMap.get("dirType");
        Integer pageNum = (Integer) queryTransMap.get("pageNum");
        Integer pageSize = (Integer) queryTransMap.get("pageSize");
        String operatorId = (String) queryTransMap.get("operatorId");


        Map<String, Object> result;
        boolean isResetPath = false;

        Map<String, String> userMap = dataModelingService.getEtlTransUsers(userId);
        if (userId.equals(operatorId) || CharSequenceUtil.isBlank(operatorId)) {
            //我的空间
            operatorId = userId;
        } else {
            if (GlobalConstant.CommonProperties.FROM_PERSONAL_TOP_NODE_ID.equals(dirId)) {
                Assert.fail("请重新选择他人空间下的目录！");
            }
            //他人空间
            isResetPath = true;
        }
        result = dataModelingService.listTransInfo(condition, state, dirId, dirType, pageNum, pageSize, operatorId);
        buildTransResult(result, isResetPath, userMap.get(operatorId));
        return Result.toResult(R.ok(result));
    }


    private void buildTransResult(Map<String, Object> result, boolean isReSetPath, String userName) {
        List<Map> arrayList = (ArrayList) result.get("data");
        List<String> transMetaIds = arrayList.stream().map(s -> (String) s.get("id")).collect(Collectors.toList());
        Map<String, Object> stringObjectMap = new HashMap<>();
      /*  transMetaIds.forEach(s -> {
            Map<String, String> schedulePlan = dataMiningService.getSchedulePlan(s);
            stringObjectMap.put(s, schedulePlan);
        });*/
        arrayList.forEach(s -> {
            Map<String, String> schedulePlan = dataMiningService.getSchedulePlan(s.get("id").toString(), s.get("name").toString());
            stringObjectMap.put(s.get("id").toString(), schedulePlan);
        });

        //查询方案执行的状态
        Map<String, Object> taskLogListInfo = mlsqlLogManageService.getTaskLogListInfo(transMetaIds);
        arrayList.forEach(s -> {
            s.put("execute_status", null);
            s.put("end_time", null);
            s.put("scheduleType", null);
            if (taskLogListInfo.get(s.get("id")) != null) {
                Map map = (Map) taskLogListInfo.get(s.get("id"));
                s.put("execute_status", map.get("execute_status"));
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                Timestamp end_time = (Timestamp) map.get("end_time");
                if (null != end_time)
                    s.put("end_time", sdf.format(new Date(Long.valueOf(end_time.getTime()))));
            }
            if (stringObjectMap.get(s.get("id")) != null) {
                Map map = (Map) stringObjectMap.get(s.get("id"));
                s.put("scheduleType", map.get("scheduleType"));
            }
            if (isReSetPath) {
                String path = MapUtil.getStr(s, "path");
                path = path.replace("我的空间", String.format("他人空间/%s", userName));
                s.put("path", path);
            }

        });
    }


    @GetMapping(value = "/saveTempTrans")
    @FuncScanAnnotation(code = "processModelingSaveTempTrans", name = "流程建模", parentCode = "processModeling")
    @ValidateAndLogAnnotation
    public Result saveTempTrans(String transName, String transType, HttpServletRequest request) {
        String tempTransId = this.dataModelingService.saveTempTrans(transName, transType, request);
        return Result.toResult(R.ok(tempTransId));
    }

    @GetMapping(value = "/saveTempTransRapidAnalysis")
//    @FuncScanAnnotation(code = "processModelingRapidAnalysis", name = "快速分析", parentCode = "processModeling")
//    @ValidateAndLogAnnotation
    public Result saveTempTransRapidAnalysis(String transName, String transType, HttpServletRequest request) {
        String tempTransId = this.dataModelingService.saveTempTrans(transName, transType, request);
        return Result.toResult(R.ok(tempTransId));
    }

    @GetMapping(value = "/baseSettingPage")
//    @FuncScanAnnotation(code = "processModelingSaseSettingPage", name = "配置", parentCode = "processModeling")
//    @ValidateAndLogAnnotation
    public Result baseSetting(String transId) {

        Assert.notNull(transId, "转换过程ID为空！");
        JSONObject result = new JSONObject();

        JsonConfig config = new JsonConfig();
        config.setJsonPropertyFilter((source, name, value) -> {
            return value == null;//value为null时返回true，返回true的就是需要过滤调的
        });
        result.put("transId", transId);
        result.put("instanceList", this.dataModelingService.findAllInstanceList());
        SimpleJobInfo scheduleMeta = null;
        if (scheduleMeta != null) {
            result.put("weekMap", putWeekMap(scheduleMeta.getScheduleWeek()));
            result.put("scheduleMeta", JSONObject.fromObject(scheduleMeta, config));
            result.put("jobId", scheduleMeta.getId());
            result.put("active", scheduleMeta.isActive());
            if (scheduleMeta.getScheduleStatus() == null) {
                result.put("scheduleStatus", "暂无状态");
            } else {
                result.put("scheduleStatus", EnumJobStatus.convert(scheduleMeta.getScheduleStatus()).getMemo());
            }
        } else {
            result.put("jobName", transMetaService.getTransMetaById(transId).getName());
            result.put("jobType", "etlJob");

        }
        // 日志级别
        List<String> levels = new ArrayList<>();
        for (EnumLogLevel logLevel : EnumLogLevel.values()) {
            levels.add(logLevel.name());
        }
        result.put("logLevels", levels);
        DubboResult<String> transLogLevel = this.transformApiService.getTransLogLevel(transId);
        result.put("logLevel", transLogLevel.getData());
        // 处理模式
        TransMeta tranStep = this.transMetaService.getTransMetaById(transId);
        result.put("mode", tranStep.getExceptionMode());
        result.put("handleModes", TransMeta.EnumHandleMode.values());
        // 运行方式
        result.put("distributed", tranStep.getDistributed());
        return Result.toResult(R.ok(result));
    }


    /**
     * 设置weekMap
     *
     * @param temp week值
     */
    private Map putWeekMap(String temp) {
        Map weekMap = new HashMap();
        if (StringUtils.isNotBlank(temp)) {
            String[] tempweek = temp.split(",");
            for (int i = 0; i < tempweek.length; i++) {
                weekMap.put(tempweek[i], "checked");
            }
        }
        return weekMap;
    }


    /**
     * 打开过程编辑的画布页面
     *
     * @param transId  过程id
     * @param loadType 是否立即绘制插件
     * @return
     */
    @GetMapping(value = "/loadTransPage")
    public Result loadTransPage(String transId, String loadType) {
        Assert.notNull(transId, "转换步骤ID为空！");
        JSONObject result = new JSONObject();
        result.put("transId", transId);
        result.put("loadType", loadType);
        boolean hasRootTrans = this.dataModelingService.hasRootTrans(transId);
        // 是否临时过程
        if (hasRootTrans) {
            result.put("instanceList", this.dataModelingService.findAllInstanceList());
        }
        result.put("hasRootTrans", hasRootTrans);
        DubboResult<TransMetaVo> transform = this.transformApiService.findTransformById(transId);
        result.put("transMetaVo", JSON.toJSONString(transform.getData()));
        result.put("hopsHandleModes", TransHopMeta.EnumHandleMode.values());
        result.put("transMetaState", dataModelingService.getState(transId));
        result.put("firstTransId", dataModelingService.getFirstTransId(transId));
        result.put("transMetaTaskGroup", dataModelingService.getTransMetaTaskGroup(transId));
        this.dataModelingService.changeTime(transId);

        return Result.toResult(R.ok(result));
    }

   /* public Map<String, Object> getPlanState(String transId) {
        List<String> metaIds = Lists.newArrayList();
        DubboResult<TransMetaVo> transform = this.transformApiService.findTransformById(transId);
        if (null != transform.getData() && transform.getData().getChildren() != null) {
            transform.getData().getChildren().forEach(s -> metaIds.add("'" + s.getId() + "'"));
        }
        return dataModelingService.getState(metaIds);
    }*/

    private boolean getState(String tranStepId) {
        TransMeta step = pluginConfigService.get(TransMeta.class, tranStepId);
        if (null != step.getOutDataSetMeta()) {
            return true;
        }//字段过滤
        return false;
    }

    /**
     * 根据keyword去查询插件id
     *
     * @param keyword
     * @return
     */
    @GetMapping(value = "/queryDmcPluginTreeNodeIdByKeyword")
    public Result queryDmcPluginTreeNodeIdByKeyword(String keyword) {
        Map<String, String> map = dataModelingService.queryDmcPluginTreeNodeIdByKeyword(keyword);
        return Result.toResult(R.ok(map.get("id")));
    }

    /**
     * 数据资源---本地资源列表
     *
     * @param condition
     * @param dataTypeId
     * @return
     */
    @GetMapping(value = "/localDataObj")
    public Result localDataObj(String condition, String dataTypeId) {
        Map<String, List<Map<String, String>>> localData = dataModelingService.listLocalData(condition, dataTypeId);
        return Result.toResult(R.ok(localData));
    }

    /**
     * 数据资源------原始数据
     *
     * @return
     */
    @PostMapping(value = "/getOriginDataList")
    public Result getOriginDataList(@RequestBody Map<String, String> resMap, HttpServletRequest request) {
        String userId = (String) request.getSession().getAttribute("userId");
        String condition = resMap.get("condition");
        String pageSize = resMap.get("pageSize");
        String page = resMap.get("page");
        List<Map<String, Object>> externalFile = dataModelingService.getOriginDataList(condition, pageSize, page, false, userId);
        return Result.toResult(R.ok(externalFile));
    }

    /**
     * 数据资源---结果复用
     *
     * @param condition
     * @param dateType
     * @return
     */
    @GetMapping(value = "/resultReuseObj")
    @FuncScanAnnotation(code = "processModelingResultReuseObj", name = "编辑", parentCode = "processModeling")
//    @ValidateAndLogAnnotation
    public Result resultReuseObj(String condition, String dateType) {
        List<SQLModelVo> resultReuse = dataModelingService.listResultReuse(condition, dateType);
        return Result.toResult(R.ok(resultReuse));
    }

    /**
     * 下载过程模型
     *
     * @param response
     * @param transId
     * @return
     */
    @GetMapping(value = "/downloadModel")
    public Result downloadModel(HttpServletResponse response, String transId) {
        TransMeta transMeta = transMetaService.getTransMetaById(transId);
        ServletOutputStream out = null;
        try {
            String fileName = transMeta.getName() + ".model";
            response.setHeader("Content-Disposition", "attachment;filename=".concat(String.valueOf(URLEncoder.encode(fileName, "UTF-8"))));
            out = response.getOutputStream();
            StreamUtils.copy(SerializableMsgCodec.encode(transMeta), out);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            try {
                if (out != null) {
                    out.close();
                }
            } catch (Exception e) {
            }
        }
        return Result.toResult(R.ok());
    }

    /**
     * 通过插件code获取插件id
     *
     * @param pluginCode
     * @return
     */
    @GetMapping(value = "/getPluginId")
    public Result queryPluginId(String pluginCode) {
        Map plugin = dataModelingService.getPluginIdByCode(pluginCode);
        return Result.toResult(R.ok(plugin));
    }

    /**
     * 根据步骤id获取与之连线的来源步骤插件信息 todo 做什么
     *
     * @param stepId
     * @return
     */
    @GetMapping(value = "/getFromPluginInfo")
    public Result getFromPluginsByStepId(String stepId) {
        List<Map<String, String>> info = dataModelingService.getFromTransStepId(stepId);
        return Result.toResult(R.ok(info));
    }

    /**
     * 获取当前步骤的输入字段
     */
    @GetMapping(value = "/getInputColumn")
    public Result getInputColumn(String tranStepId) {
        Map inputColumns = dataModelingService.showInputColumns(tranStepId);
        return Result.toResult(R.ok(inputColumns));
    }

    /**
     * 展示当前步骤的输出字段
     *
     * @param tranStepId
     * @return
     */
    @GetMapping(value = "/showOutputColumn")
    public Result showOutputColumn(String tranStepId) {
        TransMeta transMetaById = transMetaService.getTransMetaById(tranStepId);

        if ("INPUT".equals(transMetaById.getUsedPlugin().getTransPluginType())) {
            //输入插件取数据集字段和类型
            CicadaStandardSqlInput pluginMete = (CicadaStandardSqlInput) this.pluginConfigService.getPluginInstance(CicadaStandardSqlInput.class, tranStepId);
            String logicObjId = pluginMete.getLogicObjId();
            if (StringUtils.isBlank(pluginMete.getSql())) {
                LogicDataSetVo info = editService.getLogicDataInfo(null, logicObjId);
                List<Map<String, Object>> columnObjects = new ArrayList<>();
                List<ColumnDataSetVo> columns = info.getColumns();
                List<LogicSyncColumn> allColumn = new ArrayList<>();
                for (ColumnDataSetVo column : columns) {
                    if (column.getDimension() != null && column.getDimension().size() > 0) {
                        allColumn.addAll(column.getDimension());
                    }
                    if (column.getMeasure() != null && column.getMeasure().size() > 0) {
                        allColumn.addAll(column.getMeasure());
                    }
                    if (column.getNewColumn() != null && column.getNewColumn().size() > 0) {
                        allColumn.addAll(column.getNewColumn());
                    }
                }
                for (LogicSyncColumn logicSyncColumn : allColumn) {
                    Map<String, Object> c = new HashMap<>();
                    c.put("id", logicSyncColumn.getId());
                    c.put("name", StringUtils.isNotBlank(logicSyncColumn.getColumnAlias()) ? logicSyncColumn.getColumnAlias() : logicSyncColumn.getCode());
                    c.put("columnZhName", logicSyncColumn.getName());
                    c.put("length", "256");
                    c.put("precsn", 0);
                    c.put("filterExpress", "");
                    c.put("type", logicSyncColumn.getDataType());
                    c.put("index", "");
                    columnObjects.add(c);
                }

                return Result.toResult(R.ok(columnObjects));

            }
        }
        Map<String, DataColumnMeta> outputColumns = dataModelingService.showOutputColumns(tranStepId);
        List<Map<String, Object>> columns = new ArrayList<>();
        for (Map.Entry<String, DataColumnMeta> e : outputColumns.entrySet()) {
            Map<String, Object> c = new HashMap<>();

            DataColumnMeta d = e.getValue();
            c.put("id", d.getDataColumnId());
            c.put("name", e.getKey());
            c.put("columnZhName", d.getColumnZhName());
            c.put("length", d.getDataLength());
            c.put("precsn", d.getPrecsn());
            c.put("filterExpress", d.getFilterExpress());
            if (d.getColumnType() != null) {
                c.put("type", d.getColumnType().getCode());
            } else {
                c.put("type", "");
            }
            c.put("index", d.getUniqueValue());

            columns.add(c);
        }
        if (CollectionUtil.isEmpty(columns)) {
            Map<String, String> transMetaAttribute = standardSqlOutputService.getTransMetaAttribute(tranStepId);
            String dataSetId = transMetaAttribute.get("dataSetId");
            Assert.hasText(dataSetId, "数据对象id不能为空!");
            RdbDataObj dataObj = (RdbDataObj) this.rdbDataObjService.get(RdbDataObj.class, dataSetId);
            Assert.notNull(dataObj, "所选数据对象查询为空，请检查数据对象是否存在!");
            Map<String, RdbDataColumn> features = dataObj.getFeatures();
            List<String> pks = new ArrayList<>();
            Set<RdbUniqueKey> uniqueKeys = dataObj.getRdbUniqueKeys();
            Iterator var7 = uniqueKeys.iterator();

            while (var7.hasNext()) {
                RdbUniqueKey uniqueKey = (RdbUniqueKey) var7.next();
                Iterator var9 = uniqueKey.getUkDatacolumns().iterator();

                while (var9.hasNext()) {
                    RdbUkDataColumn ukDatacolumn = (RdbUkDataColumn) var9.next();
                    RdbUkDataColumnId id = ukDatacolumn.getId();
                    RdbDataColumn datacolumn = id.getRdbDatacolumn();
                    pks.add(datacolumn.getCode());
                }
            }

            List<Map<String, Object>> cs = new ArrayList();
            Iterator var16 = features.entrySet().iterator();

            while (var16.hasNext()) {
                Map.Entry<String, RdbDataColumn> entry = (Map.Entry) var16.next();
                Map<String, Object> meta = new HashedMap();
                RdbDataColumn c = (RdbDataColumn) entry.getValue();
                meta.put("id", c.getId());
                meta.put("columnZhName", c.getName());
                meta.put("name", c.getCode());
                meta.put("length", c.getLength());
                meta.put("precsn", c.getPrecsn());
                meta.put("filterExpress", "");
                meta.put("type", c.getDataType().getCode());
                meta.put("index", "");
                cs.add(meta);
            }
            columns = cs;

        }
        return Result.toResult(R.ok(columns));
    }

    /**
     * 获取插件步骤详情
     *
     * @param tranStepId
     * @return
     */
    @GetMapping(value = "/getDetailInfo")
    public Result getDetailInfo(String tranStepId) {
        TransMeta tranStep = this.transMetaService.getTransMetaById(tranStepId);
        return Result.toResult(R.ok(tranStep));
    }

    /**
     * 获取高级配置详情
     *
     * @param tranStepId
     * @return
     */
    @GetMapping(value = "/advanceConfig")
    public Result advanceConfig(String tranStepId) {
        TransMeta tranStep = this.transMetaService.getTransMetaById(tranStepId);
        Map<String, Object> res = new HashMap<>();
        res.put("isCopy", tranStep.isDatasetCopy());
        res.put("threadCount", tranStep.getWorkThreadCount());
        res.put("exceptionMode", tranStep.getExceptionMode());

        DataSetMeta m = tranStep.getOutDataSetMeta();
        if (m != null) {
            res.put("sampleExpr", m.getFilterExpress());
            res.put("sampleDistinct", m.getDistinctExpress());
        }
        return Result.toResult(R.ok(res));
    }

    /**
     * 保存配置
     *
     * @param tranStepId
     * @param isCopy
     * @param threadCount
     * @param exceptionMode
     * @param sampleExpr
     * @param sampleDistinct
     * @return
     */
    @GetMapping(value = "/save/advanceConfig")
    public Result saveAdvanceConfig(String tranStepId, Boolean isCopy, int threadCount, String exceptionMode,
                                    String sampleExpr, String sampleDistinct) {
        TransMeta t = this.transMetaService.getTransMetaById(tranStepId);

        t.setDatasetCopy(isCopy);
        t.setWorkThreadCount(threadCount);
        t.setExceptionMode(exceptionMode);

        DataSetMeta m = t.getOutDataSetMeta();
        if (m != null) {
            m.setFilterExpress(sampleExpr);
            m.setDistinctExpress(sampleDistinct);
        }
        this.transMetaService.updateTransMeta(t);
        return Result.toResult(R.ok());
    }


    /**
     * 点执行按钮
     *
     * @param transId
     * @return
     */
    @GetMapping(value = "/updateSchedule")
    public Result updateSchedule(String transId) {
        return Result.toResult(R.ok());
    }
    //-------------------updateSchedule执行返回success后就执行transStart-------------------

    /**
     * 点配置按钮---》保存 -----start---------
     *
     * @param scheduleMeta
     * @param scheduleWeek
     * @return
     */
    @PostMapping(value = "/updateTransSchedule")
    public Result updateTransSchedule(@RequestBody SimpleJobInfo scheduleMeta, String[] scheduleWeek) {
//        mistTransformScheduleService.updateTransformSchedule(scheduleMeta, scheduleWeek);
        return Result.toResult(R.ok());
    }

    /**
     * 执行updateTransDistribute
     *
     * @param tranStepId
     * @param distributed
     * @return
     */
    @RequestMapping(value = "updateTransDistribute")
    public Result updateTransDistribute(String tranStepId, String distributed) {
        Assert.hasText(tranStepId, "转换步骤ID为空");
        transformApiService.updateTransDistribute(tranStepId, distributed);
        return Result.toResult(R.ok("更新成功"));

    }

    //还执行了updateHandleMode
    @RequestMapping(value = "updateHandleMode")
    public Result updateHandleMode(String tranStepId, String handleMode) {
        Assert.notNull(tranStepId, "转换步骤ID为空");
        transformApiService.updateHandleMode(tranStepId, handleMode);
        return Result.toResult(R.ok("更新成功"));
    }

    /**
     * 重命名时，判断名称是否存在
     *
     * @param classifyId
     * @param name
     * @return
     */
    @GetMapping(value = "/query/transExist")
    public Result checkExistByName(String classifyId, String name) {
        boolean result = dataModelingService.checkExistByName(classifyId, name);
        return Result.toResult(R.ok(result));
    }

    @GetMapping(value = "/dirCount")
    public Result dataRowsCount() {
        Map dataRowsCount = dataModelingService.dirCount("1", "2");
        return Result.toResult(R.ok(dataRowsCount));
    }

    @GetMapping(value = "/moveModel")
    @FuncScanAnnotation(code = "processModelingQueryTransTree", name = "移动", parentCode = "processModeling")
    @ValidateAndLogAnnotation
    public Result moveModel(String elementId, String classifyId) {
        String msg = dataModelingService.moveModel(elementId, classifyId);
        if ("success".equals(msg)) {
            return Result.toResult(R.ok());
        } else {
            return Result.toResult(R.error(msg));
        }
    }

    /**
     * 获取流程建模的数据集
     *
     * @param transId
     * @return
     */
    @GetMapping("/getDataSets")
    @FuncScanAnnotation(code = "processModelingGetDataSets", name = "预览结果集", parentCode = "processModeling")
    @ValidateAndLogAnnotation
    public Result getDataSets(String transId) {
        List dataSetByTrans = dataModelingService.getDataSetByTrans(transId);
        if (dataSetByTrans == null) {
            return Result.error("400", "该流程没有输出");
        }
        return Result.success(dataSetByTrans);
    }

    /**
     * 获取流程建模的分享列表
     *
     * @param
     * @return
     */
    @PostMapping("/getSharesByTran")
    public Result getSharesByTran(@RequestBody Map<String, Object> reqMap, HttpSession session) {
        String userId = (String) session.getAttribute("userId");
        //String userId = "40289754739d4e7e11729d4e682b2020";
        Assert.hasLength(userId, "请先登录！");
        PageInfo shares = dataModelingService.getShareResourceByTrans(reqMap, userId);
        return Result.success(shares);
    }

    /**
     * 获取流程建模的分享资源
     *
     * @param
     * @return
     */
    @GetMapping("/getResourceByTran")
    public Result getResourceByTran(String tranId) {
        Map<String, Object> resources = dataModelingService.getResourcesByTranId(tranId);
        return Result.success(resources);
    }

    @GetMapping("/getModelServiceDir")
    public Result getModelServiceDir(HttpSession session) {
        String userId = (String) session.getAttribute("userId");
        Assert.notNull(userId, "请先登录！");
        List<ModelTreeResult> modelService = dataModelingService.getModelService(userId);
        return Result.success(modelService);
    }

    @GetMapping("/getApiList")
    public Result getModelServiceDir(String modelId, String type) {
        Assert.notNull(modelId, "模型id不能为空！");
        List<Map<String, Object>> mapList = serviceManagementService.queryServicesByModelId(modelId, type);
        return Result.success(mapList);
    }

    @GetMapping("/getServiceDetailById")
    public Result getServiceDetailById(String serviceId) {
        Map<String, Object> map = serviceManagementService.queryServiceDetailsByPublishedId(serviceId);
        return Result.success(map);
    }

    //模型服务模型资源树
    @PostMapping("/queryModelServiceSourceTree")
    public Result queryModelServiceSourceTree(HttpServletRequest request, @RequestBody TransClassifyVo transClassify) {
        String userId = (String) request.getSession().getAttribute("userId");
        List<ModelTreeVo> modelTreeVoList = dataModelingService.queryModelServiceSourceTree(userId, transClassify.getTransClassify());
        return Result.success(modelTreeVoList);
    }

    //模型服务模型资源树
 /*   @PostMapping("/check")
    public Result queryModelServiceSourceTree(HttpServletRequest request,@RequestBody TransClassify transClassify) {
        String userId = (String) request.getSession().getAttribute("userId");
        List<ModelTreeVo> modelTreeVoList = dataModelingService.queryModelServiceSourceTree(userId,transClassify.getTransClassify());
        return Result.success(modelTreeVoList);
    }*/

    private Result previewFromRdbWithOutTrain(String transId, String tranStepId, String selectSize, String limitSize, String jobID, HttpSession session) {
        //完全复制  只在script 的mlsql做修改切割

        DataSetMeta outDataSetMeta = this.transMetaService.getTransMetaById(tranStepId).getOutDataSetMeta();
        Assert.notNull(outDataSetMeta, "预览数据前，请先保存");
        Map<String, DataColumnMeta> dataColumnMetas = outDataSetMeta.getDataColumnMetas();
        String userId = (String) session.getAttribute("userId");
        Map<String, String> afterTable = mlsqlService.getAfterTableName(transId, tranStepId, true, selectSize, userId);
        Map<String, Object> resMap = Maps.newHashMap();
        limitSize = limitSize == null ? "10" : limitSize;
        String mlsql = "select * from " + afterTable.get("tableName") + " limit " + limitSize + " as preview_" + afterTable.get("tableName") + ";";
        mlsql = afterTable.get("sql") + "\n" + mlsql;

        resMap.put("includeSchema", true);
        resMap.put("fetchType", "take");
        resMap.put("outputSize", limitSize);
        if (StringUtils.isNotBlank(jobID)) resMap.put("jobId", jobID);

        String[] strings = mlsql.split(";");
        StringBuilder mainSql = new StringBuilder();
        for (int i = 0; i < strings.length - 2; i++) {
            mainSql.append(strings[i] + ";");
        }
        String mainSqlStr = mainSql.toString();
        String lastTableName = mainSqlStr.substring(mainSql.lastIndexOf(" "), mainSqlStr.length() - 1);
      /*  String mainSql = mlsql.substring(0,mlsql.lastIndexOf(mlsql.lastIndexOf(",", mlsql.lastIndexOf(",") - 1) - 1));
        String limitSql = mlsql.substring(mlsql.lastIndexOf(",", mlsql.lastIndexOf(",") - 1));*/
        String limitSql = "select * from " + lastTableName + " limit " + limitSize + " as preview_outTable; ";
        mlsql = mainSqlStr + limitSql;
        resMap.put("sql", mlsql);
        log.info("{}{}", "预览sql:", mlsql);
        try {
            String runScript = mlsqlService.runScript(resMap);
            com.alibaba.fastjson.JSONObject jsonObject = com.alibaba.fastjson.JSONObject.parseObject(runScript);
            JSONArray datas = (JSONArray) jsonObject.get("data");
            com.alibaba.fastjson.JSONObject schema = (com.alibaba.fastjson.JSONObject) jsonObject.get("schema");
            JSONArray fields = (JSONArray) schema.get("fields");
            List<String> sortColumns = Lists.newArrayList();
            fields.forEach(s -> {
                com.alibaba.fastjson.JSONObject resField = (com.alibaba.fastjson.JSONObject) s;
                String name = String.valueOf(resField.get("name"));
                sortColumns.add(name);
            });
            JSONArray sortFields = new JSONArray();
            //统一排序
            sortColumns.sort(String::compareToIgnoreCase);
            for (String s : sortColumns) {
                for (Object field : fields) {
                    com.alibaba.fastjson.JSONObject resField = (com.alibaba.fastjson.JSONObject) field;
                    String name = String.valueOf(resField.get("name"));
                    if (s.equals(name)) {
                        DataColumnMeta dataColumnMeta = dataColumnMetas.get(name);
                        String columnZhName = "";
                        if (dataColumnMeta != null) {
                            columnZhName = dataColumnMeta.getColumnZhName();
                        }
                        String type = ((com.alibaba.fastjson.JSONObject) field).get("type").toString();
                        if (type.contains("{")) {
                            ((com.alibaba.fastjson.JSONObject) field).put("type", "object");
                        }
                        resField.put("name", StringUtils.isNotBlank(columnZhName) ? columnZhName : name);
                        resField.put("code", name);
                        sortFields.add(resField);
                    }
                }
            }
            fields = sortFields;


            JSONArray newArr = new JSONArray();
            for (Object data : datas) {
                com.alibaba.fastjson.JSONObject newObj = new com.alibaba.fastjson.JSONObject();
                com.alibaba.fastjson.JSONObject jsonData = (com.alibaba.fastjson.JSONObject) data;
                for (String s : sortColumns) {
                    if (jsonData.get(s) != null) {
                        String key = s;
                        Object value = jsonData.get(s);
                        DataColumnMeta dataColumnMeta = dataColumnMetas.get(key);
                        String columnZhName = "";
                        if (dataColumnMeta != null) {
                            columnZhName = dataColumnMetas.get(key).getColumnZhName();
                        }
                        newObj.put(StringUtils.isNotBlank(columnZhName) ? columnZhName : key, value);
                    }
                }
                newArr.add(newObj);
            }
            return Result.success(jsonObject.toJSONString());
        } catch (Exception e) {
            return Result.error("500", "建模方案不正确或者服务器异常，请联系管理员！", e.getMessage());
        }
    }


    //下载excel
    @GetMapping("/excelout")
    @FuncScanAnnotation(code = "dataExport", name = "数据导出", parentCode = "dataView")
    @ValidateAndLogAnnotation
    public Result excelout(HttpServletResponse response, String transId, String stepId, String selectSize, String limitSize, String downloadSize, HttpSession session, String transStepId, String excelname, String cnt, String limit) {
        ExcelDownloadVo excelDownloadVo = new ExcelDownloadVo();
        excelDownloadVo.setExcelname(excelname);
        if (StringUtils.isBlank(cnt)) {
            try {
                TransMeta thisStep = transMetaService.getTransMetaById(stepId);
                String pluginCode = thisStep.getUsedPlugin().getCode();
                if (StringUtils.isBlank(transId)) {
                    transId = thisStep.getParentId();
                }
                Result result;

                if ("cicadaStandardSqlOutput".equals(pluginCode)) {
                    //关系库输出不走常用的逻辑
                    Map resMap = new HashMap();
                    resMap.put("transStepId", stepId);
                    resMap.put("limit", limit);

                    Result result1 = dataWarehouseController.previewRdb(resMap);
                    ColumnDataModel columnDataModel = (ColumnDataModel) result1.getData();
                    List<Map> res = columnDataModel.getFieldValue();
                    if (StringUtils.isNotBlank(downloadSize)) {
                        int downSize = Integer.parseInt(downloadSize);
                        if (downSize <= res.size()) {
                            res = res.subList(0, downSize);
                        }
                    }
                    excelDownloadVo.setResult(res);

                } else {
                    result = (previewController.preview(transId, stepId, selectSize, downloadSize, null, session));
                    excelDownloadVo.setContent((String) result.getData());
                }


            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        } else {
            Result result = cicadaStandardInputController.previewRdb(stepId, transId, Integer.parseInt(downloadSize), session);
            ColumnDataModel columnDataModel = (ColumnDataModel) result.getData();
            List<Map> res = columnDataModel.getFieldValue();
            excelDownloadVo.setResult(res);

        }
        List<Map> data = null;

        String fileName = excelDownloadVo.getExcelname();
        if (StringUtils.isNotBlank(excelDownloadVo.getContent())) {
            //通过引擎获取的数据走以下逻辑
            List<Map> newData = new ArrayList<>();
            Map<Object, Object> result = jsonToMap(excelDownloadVo.getContent());
            Map<Object, Object> schema = (Map<Object, Object>) result.get("schema");
            net.sf.json.JSONArray fields = (net.sf.json.JSONArray) schema.get("fields");

            Map<String, String> codeNameMap = new HashMap<>();
            for (Object field : fields) {
                JSONObject fieldMap = (JSONObject) field;
                String name = (String) fieldMap.get("name");
                String code = (String) fieldMap.get("code");
                codeNameMap.put(StringUtils.isNotBlank(name) ? name : code, code);
            }

            data = (List<Map>) result.get("data");
            for (Map<String, Object> datum : data) {
                Map<String, Object> newDatum = new HashMap<>();
                for (String key : codeNameMap.keySet()) {
                    newDatum.put(key, datum.get(codeNameMap.get(key)));
                }
                newData.add(newDatum);
            }
            removeExcelData(newData, downloadSize);
            ExcelWriter excelWriter = TenonExcelUtil.getWriter(true);
            try {
                TenonExcelUtil.writeDataToExcel(response, fileName, newData, excelWriter);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        } else {
            ExcelWriter excelWriter = TenonExcelUtil.getWriter(true);
            try {
                removeExcelData(excelDownloadVo.getResult(), downloadSize);
                TenonExcelUtil.writeDataToExcel(response, fileName, excelDownloadVo.getResult(), excelWriter);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }

        return Result.success();
    }

    private void removeExcelData(List<Map> resultData, String downloadSize) {
        if (CollectionUtils.isNotEmpty(resultData) && StringUtils.isNotBlank(downloadSize)) {
            int downloadSizeInt = Integer.parseInt(downloadSize);
            if (resultData.size() >= downloadSizeInt) {
                resultData.subList(downloadSizeInt - 1, resultData.size() - 1);
            }
        }
    }

    /**
     * json string 转换为 map 对象
     *
     * @param jsonObj
     * @return
     */
    public static Map<Object, Object> jsonToMap(Object jsonObj) {
        JSONObject jsonObject = JSONObject.fromObject(jsonObj);
        Map<Object, Object> map = (Map) jsonObject;
        return map;
    }

    //获取List<Map>的key
    public List<String> GetMapKey(List<Map> listResult) {
        if ((listResult != null) && (!listResult.isEmpty())) {
            List listKey = new ArrayList();

            Map mapResult = (Map) listResult.get(0);

            Set mapKeySet = mapResult.keySet();

            String listHead = "";

            Iterator iteratorKey = mapKeySet.iterator();
            while (iteratorKey.hasNext()) {
                listHead = (String) iteratorKey.next();
                listKey.add(listHead);
            }

            return listKey;
        }
        return null;
    }

    @PostMapping("refreshTransStatus")
    public Result refreshTransStatus(@RequestBody RefreshTransStatusDTO dto) {
        return Result.success(dataModelingService.refreshTransStatus(dto));
    }

    @ApiOperation("获取运行详情日志信息")
    @GetMapping("/getTransJobDetail")
    public Result<String> getTransJobDetail(String detailId) {
        String detailMsg = dataModelingService.getTransJobDetail(detailId);
        return Result.success(detailMsg);
    }
}

