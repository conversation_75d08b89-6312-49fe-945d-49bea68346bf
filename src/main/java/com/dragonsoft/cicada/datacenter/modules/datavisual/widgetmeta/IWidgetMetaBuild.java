package com.dragonsoft.cicada.datacenter.modules.datavisual.widgetmeta;


import com.code.metadata.datavisual.WidgetMeta;

import java.util.List;
import java.util.Map;

public interface IWidgetMetaBuild {


     List<WidgetMeta> builderList();

     List<WidgetMeta> builderNoChartList();


    /**
     * 更新全部数据元信息
     *
     * @param bean
     */
     void updateWidgetMetaAll(Map<String, Object> bean);



}
