package com.dragonsoft.cicada.datacenter.modules.datavisual.model;

import cn.hutool.core.date.StopWatch;
import com.code.common.paging.PageInfo;
import com.code.dragonsoft.dataquery.service.querymodel.ColumnDataModel;
import com.code.dragonsoft.dataquery.service.querymodel.ColumnPageInfo;
import com.code.dragonsoft.dataquery.service.querymodel.ParamDataModel;
import com.code.meta.dml.standard.StandardQuery;
import com.code.meta.dml.standard.cdins.*;
import com.code.metadata.datavisual.WidgetDatasetDims;
import com.code.metadata.datavisual.WidgetDatasetDimsDrill;
import com.code.metadata.res.common.ClassifierStat;
import com.code.metadata.res.ddl.LogicDataObj;
import com.dragonsoft.cicada.datacenter.modules.datavisual.dataset.IDataSetBuilder;
import com.dragonsoft.cicada.datacenter.modules.datavisual.model.annotation.WidgetLabel;
import com.dragonsoft.cicada.datacenter.modules.datavisual.model.enums.WidgetType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;

@Slf4j
@Component
@WidgetLabel(name = "表格", type = WidgetType.TABLE_CHART, describe = "列表类型表格")
public class TableChartWidget extends AbsChartWidget {

    List<HeadData> headDatas = new LinkedList<>();
    List<Map<String, Object>> tableDatas = new LinkedList<>();
    int pageSize;
    int index;
    long count;

    @Override
    public Map<String, Object> loadingData(IDataSetBuilder dataSetBuilder, int mode,String code, int timers) {

        PageInfo pageInfo = new PageInfo(pageSize, index);
        pageInfo.setDefaultSortField(null);
        String newQuery = getSQL(dataSetBuilder,mode);
        StopWatch stopWatch = new StopWatch("可视化_表格查询");
        stopWatch.start("统一访问层查询");
        ColumnDataModel columns = null;
        if (this.getWidgetDataset().getDbType() != null && this.getWidgetDataset().getDbType().toLowerCase().equals(DB_TYPE_ES)) {
            try {
                ParamDataModel paramDataModel = new ParamDataModel();
                paramDataModel.setScript(newQuery);
//                ClassifierStat obj = classifierStatService.getClassifierStat(ClassifierStat.class, this.getWidgetDataset().getClassifierStatId());
                LogicDataObj obj = logicDataObjService.findLogicDataObjById(this.getWidgetDataset().getClassifierStatId());
                ClassifierStat classifierStat = logicDataObjService.getClassifierStatByLogic(obj);
                columns = queryDataService.queryData(classifierStat.getId(),paramDataModel);
            } catch (Exception e) {
                log.error(e.getMessage(),e);
            }
            this.count = columns.getFieldValue().size();
        } else {
            Order[] orders = this.getOrders(this.widgetDataset.getFromListDims(), null);

            if (orders != null ) {
                pageInfo.setSortField( new OrderCdin(orders).toExpression());
            }
            ColumnPageInfo result = null;
            try {
//                ClassifierStat obj = classifierStatService.getClassifierStat(ClassifierStat.class, this.getWidgetDataset().getClassifierStatId());
                LogicDataObj obj = logicDataObjService.findLogicDataObjById(this.getWidgetDataset().getClassifierStatId());
                if(!"QUICK_SQL".equalsIgnoreCase(obj.getBelongType())){
                    ClassifierStat classifierStat = logicDataObjService.getClassifierStatByLogic(obj);
                    result = queryDataService.queryData(classifierStat.getId(),newQuery,pageInfo);
                }else{
                    LogicDataObj metaLogicDataObJ = logicDataObjService.getMetaLogicDataObJ(obj);
                    result = queryDataService.queryDataBySchemaPageInfo(metaLogicDataObJ.getOwnerId(),newQuery,pageInfo);
                }

            } catch (Exception e) {
                log.error(e.getMessage(),e);
            }

            this.count = result.getTotalCount();
            columns = result.getColumnDataModel();
        }


        stopWatch.stop();

        addColumnType(columns.getFieldName());
        return buildResult(timers, stopWatch, columns);
    }

    protected Map buildResult(int timers, StopWatch stopWatch, ColumnDataModel columns) {
        stopWatch.start("表格前端对象封装");


        Set<WidgetDatasetDims> datasetDims = this.getWidgetDataset().sortWidgetDatasetDims();
        Set<WidgetDatasetDimsDrill> datasetDimsDrill = this.getWidgetDataset().sortWidgetDatasetDimsDrill();
        for (Map b : columns.getFieldValue()) {
            Map<String, Object> map = new LinkedHashMap();
            if(datasetDimsDrill.isEmpty()){
                for (WidgetDatasetDims d : datasetDims) {
                    map.put(d.getFiledCode(), this.getDimsFilterVal(d, null,b));
                }
            }else{
                for (WidgetDatasetDimsDrill d : datasetDimsDrill) {
                    map.put(d.getFiledCode(), this.getDimsFilterVal(new WidgetDatasetDims(), d,b));
                }
            }
            tableDatas.add(map);
        }
        Map rq = new HashMap();
        Map data = new HashMap();
        data.put("head", this.headDatas);
        data.put("data", this.tableDatas);
        data.put("count", count);
        rq.put("code", 1);
        rq.put("data", data);
        stopWatch.stop();
        if (stopWatch.getTotalTimeSeconds() >= timers) {
            log.info(stopWatch.prettyPrint());
        }
        return rq;
    }

    protected void addColumnType(Map<String, String> fileName){
      for(HeadData head : this.headDatas){
        if(fileName.get(head.getLabel()) != null || fileName.get(head.getProp()) != null){
            head.setColumnDataType(fileName.get(head.getLabel()) != null ?  fileName.get(head.getLabel()) :  fileName.get(head.getProp()));
        }
      }
    }

    @Override
    public String getSQL(IDataSetBuilder dataSetBuilder, int mode) {
        StandardQuery query = new StandardQuery(this.widgetDataset.getDbType());
        if (this.getWidgetDataset().getDbType() != null && this.getWidgetDataset().getDbType().toLowerCase().equals(DB_TYPE_ES)) {
            query.setSqlTYpe(StandardQuery.type.ES);
        }
        QueryCdins queryCdins = query.getQueryCdins();
        query.setTableName(getSearchSQL(this.widgetDataset.getDatasetId()));
        Set<WidgetDatasetDims> datasetDims = this.getWidgetDataset().sortWidgetDatasetDims();
        Set<WidgetDatasetDimsDrill> datasetDimsDrills = this.getWidgetDataset().sortWidgetDatasetDimsDrill();
        List<Select> selects = new LinkedList<>();
        //字段
        if(datasetDimsDrills.isEmpty()){

            for (WidgetDatasetDims d : datasetDims) {
                selects.add(new Select(d.getFiledCode(), d.getFiledAlias(), "1".equals(d.getDistinct())));
                HeadData headData = new HeadData(d.getFiledCode(), d.getFiledName());
                headDatas.add(headData);
            }
        }else{
            for (WidgetDatasetDimsDrill d : datasetDimsDrills) {
                selects.add(new Select(d.getFiledCode(), d.getFiledAlias(), "1".equals(d.getDistinct())));
                HeadData headData = new HeadData(d.getFiledCode(), d.getFiledName());
                headDatas.add(headData);
            }
        }
        query.setConditions(this.getCondition(queryCdins));

        Order[] orders = this.getOrders(this.widgetDataset.getFromListDims(), null);
        if (orders != null) {
            query.setOrderCdins(orders);
        }
        query.setSelectCdin(new SelectCdin(selects.toArray(new Select[0])));
        if (Objects.equals(this.widgetDataset.getDbType().toUpperCase(),"MYSQL")){
            return query.toExpression().getScript().replaceAll("`", "");
        }
        return query.toExpression().getScript().replaceAll("`","\"");
    }

    public static class HeadData {
        String prop;
        String label;
        String columnDataType;

        public HeadData(String prop, String label) {
            this.prop = prop;
            this.label = label;
        }

        public HeadData(String prop, String label,String columnDataType) {
            this.prop = prop;
            this.label = label;
            this.columnDataType = columnDataType;
        }

        public String getColumnDataType() {
            return columnDataType;
        }

        public void setColumnDataType(String columnDataType) {
            this.columnDataType = columnDataType;
        }

        public String getProp() {
            return prop;
        }

        public void setProp(String prop) {
            this.prop = prop;
        }

        public String getLabel() {
            return label;
        }

        public void setLabel(String label) {
            this.label = label;
        }
    }

    public List<HeadData> getHeadDatas() {
        return headDatas;
    }

    public void setHeadDatas(List<HeadData> headDatas) {
        this.headDatas = headDatas;
    }

    public List<Map<String, Object>> getTableDatas() {
        return tableDatas;
    }

    public void setTableDatas(List<Map<String, Object>> tableDatas) {
        this.tableDatas = tableDatas;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public long getCount() {
        return count;
    }

    public void setCount(long count) {
        this.count = count;
    }
}
