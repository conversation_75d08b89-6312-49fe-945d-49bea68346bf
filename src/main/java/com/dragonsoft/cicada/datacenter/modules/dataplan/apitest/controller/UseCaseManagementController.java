package com.dragonsoft.cicada.datacenter.modules.dataplan.apitest.controller;

import com.code.common.paging.PageInfo;
import com.code.common.utils.assertion.Assert;
import com.code.metadata.usecase.UseCase;
import com.code.metaservice.usecase.UseCaseService;
import com.dragoninfo.dfw.bean.Result;
import com.dragonsoft.cicada.datacenter.modules.common.service.CommonBusiClassifyService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.apitest.service.UseCaseManageService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.apitest.vo.ClassifyVo;
import com.dragonsoft.cicada.datacenter.modules.dataplan.apitest.vo.UseCaseListVo;
import com.dragonsoft.cicada.datacenter.modules.dataplan.apitest.vo.UseCaseVo;
import com.fw.tenon.tree.Tree;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpSession;
import java.util.List;

@CrossOrigin
@RestController
@RequestMapping("/useCaseManagement")
public class UseCaseManagementController {


    private static final String USE_CASE_DIR_CODE = "USE_CASE_API_DIR";
    private static final String USE_CASE_CLASSIFY_CODE = "USE_CASE_API_CLASSIFY";

    @Autowired
    private CommonBusiClassifyService commonBusiClassifyService;

    @Autowired
    private UseCaseManageService useCaseManageService;

    @Autowired
    private UseCaseService useCaseService;


    /**
     * 获取用例目录树
     * @param session
     * @return
     */
    @GetMapping("/getUseCaseClassify")
    public Result getUseCaseClassify(HttpSession session) {
        String userId = (String) session.getAttribute("userId");
        Assert.hasLength(userId,"用户id不能为空！");
        List<Tree> trees = commonBusiClassifyService.queryClassifyTree(userId, USE_CASE_DIR_CODE);
        return Result.success(trees);
    }

    @PostMapping("/newCaseClassify")
    public Result newCaseClassify(@RequestBody ClassifyVo classifyVo, HttpSession session) {
        String name = classifyVo.getName();
        String parentId = classifyVo.getParentId();
        Assert.hasLength(name,"目录名称不能为空！");
        Assert.hasLength(parentId,"上级目录不能为空！");
        String userId = (String) session.getAttribute("userId");
        Assert.hasLength(userId,"用户id不能为空！");

        String id = commonBusiClassifyService.newServiceClassify(parentId, name, USE_CASE_CLASSIFY_CODE, userId, USE_CASE_DIR_CODE);
        return Result.success(id);
    }

    @PostMapping("/editCaseClassify")
    public Result editCaseClassify(@RequestBody ClassifyVo classifyVo, HttpSession session) {
        String name = classifyVo.getName();
        String parentId = classifyVo.getParentId();
        String id = classifyVo.getId();
        Assert.hasLength(id,"目录id不能为空！");
        Assert.hasLength(name,"目录名称不能为空！");
        Assert.hasLength(parentId,"上级目录不能为空！");
        String userId = (String) session.getAttribute("userId");
        Assert.hasLength(userId,"用户id不能为空！");
        commonBusiClassifyService.editClassify(id,parentId,name,USE_CASE_CLASSIFY_CODE,userId,USE_CASE_DIR_CODE);
        return Result.success();
    }

    @GetMapping("/deleteCaseClassify")
    public Result deleteCaseClassify(String classifyId) {
        Assert.hasLength(classifyId,"目录id不能为空！");
        commonBusiClassifyService.deleteClassify(classifyId);
        return Result.success();
    }

    @GetMapping("/moveElementToClassify")
    public Result moveElementToClassify(String classifyId,String elementId) {
        Assert.hasLength(classifyId,"目录id不能为空！");
        Assert.hasLength(elementId,"元素id不能为空！");
        commonBusiClassifyService.moveElementToClassify(elementId,classifyId);
        return Result.success();
    }

    @PostMapping("/newUseCase")
    public Result newUseCase(@RequestBody UseCaseVo caseVo,HttpSession session) {

        String userId = (String) session.getAttribute("userId");
        useCaseManageService.saveUseCase(caseVo,userId);

        return Result.success();
    }

    @PostMapping("/editUseCase")
    public Result editUseCase(@RequestBody UseCaseVo caseVo,HttpSession session) {
        String userId = (String) session.getAttribute("userId");
        useCaseManageService.saveUseCase(caseVo,userId);
        return Result.success();
    }

    @GetMapping("/deleteUseCase")
    public Result deleteUseCase(String useCaseId) {
        Assert.hasLength(useCaseId,"用例id不能为空！");
        UseCase useCaseById = useCaseService.getUseCaseById(useCaseId);
        useCaseService.deleteUseCase(useCaseById);
        commonBusiClassifyService.deleteClassifyRelationByElementId(useCaseId);
        return Result.success();
    }

    @GetMapping("/getUseCaseDetail")
    public Result getUseCaseDetail(String useCaseId) {
        Assert.hasLength(useCaseId,"用例id不能为空！");

        UseCaseVo useCaseVo = useCaseManageService.getUseCaseDetailById(useCaseId);

        return Result.success(useCaseVo);
    }

    @PostMapping("/getUseCaseList")
    public Result getUseCaseList(@RequestBody UseCaseListVo vo) {
        String classifyId = vo.getClassifyId();
        String useCaseName = vo.getUseCaseName();
        Integer pageIndex = vo.getPageIndex();
        Integer pageSize = vo.getPageSize();
        PageInfo useCasePage = useCaseManageService.getUseCasePage(classifyId, useCaseName, pageSize, pageIndex);
        return Result.success(useCasePage);
    }

    @GetMapping("/getCaseTreeWithElement")
    public Result getCaseTreeWithElement(HttpSession session) {
        String userId = (String) session.getAttribute("userId");
        List<Tree> treeWithElement = commonBusiClassifyService.getTreeWithElement(userId, USE_CASE_DIR_CODE);
        return Result.success(treeWithElement);
    }
}
