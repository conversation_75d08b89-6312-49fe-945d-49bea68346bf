package com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.service;

import com.alibaba.fastjson.JSONArray;
import com.code.common.utils.R;
import com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.vo.ServicePublicationVo;

import java.util.Map;

public interface IAiServicePublishService {


    JSONArray getLogIdAndParams(String scriptId);

    R publishService(ServicePublicationVo vo, String userId);

    Map testAiService(String serviceId,Map params);

    Map<String,Object> publishTestService(ServicePublicationVo vo, String userId);

    R uninstall(String serviceMetaId);

    void offline(String serviceMetaId);

    void export(String serviceMetaId);

    Map updateRequestPath(String serviceMetaId,String code);

    void deleteServiceMetaById(String serviceMetaId);
}
