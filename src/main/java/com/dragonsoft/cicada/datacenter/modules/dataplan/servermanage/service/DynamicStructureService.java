package com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.service;

import com.code.common.mist.service.structure.model.ServiceClassMeta;
import com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.vo.ParamConfigVo;

public interface DynamicStructureService {
    public ServiceClassMeta dynamicCreateClassMetaByVo(ParamConfigVo paramConfigVo);

    ServiceClassMeta dynamicQueryCreateClassMetaByVo(ParamConfigVo paramConfigVo,String userId) throws Exception;

    ServiceClassMeta dynamicVerifcationCreateClassMetaByVo(ParamConfigVo paramConfigVo,String userId) throws Exception;
}
