/**
 * 版权所有：厦门市巨龙信息科技有限公司
 * Copyright 2024 Xiamen Dragoninfo Eng. Co. Ltd.
 * All right reserved.
 */
package com.dragonsoft.cicada.datacenter.modules.system.permissions.enums;

import com.dragoninfo.dfw.enums.IErrorCode;

/**
 * 类说明
 *
 * <AUTHOR>
 * @date 2024/10/24 10:11
 */
public enum PermissionErrorCode implements IErrorCode {
    ROLE_EXISTED("A0103", "角色已存在"),
    GROUP_EXISTED("A0104", "用户组已存在"),
    ;
    private final String errorCode;
    private final String msg;

    private PermissionErrorCode(String errorCode, String msg) {
        this.errorCode = errorCode;
        this.msg = msg;
    }

    @Override
    public String getErrorCode() {
        return errorCode;
    }

    @Override
    public String getMsg() {
        return msg;
    }
}
