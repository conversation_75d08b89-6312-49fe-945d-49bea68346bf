package com.dragonsoft.cicada.datacenter.modules.firstpage.scenariocase.controller;

import com.code.common.paging.PageInfo;
import com.code.common.utils.R;
import com.code.metadata.scenario.TScenarioCaseType;
import com.dragoninfo.dfw.bean.Result;
import com.dragonsoft.cicada.datacenter.modules.firstpage.scenariocase.service.ScenarioService;
import com.dragonsoft.cicada.datacenter.modules.firstpage.scenariocase.vo.ModelCaseVo;
import com.dragonsoft.cicada.datacenter.modules.firstpage.scenariocase.vo.ScenarioVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpSession;
import java.util.List;

@RestController
@CrossOrigin
@Slf4j
@RequestMapping("/scenarioCase")
@Api(tags = "场景案例相关接口",description = "提供查询场景案例，将模型转换成场景案例的接口")
public class ScenarioCaseController {


    @Autowired
    private ScenarioService scenarioService;

    @PostMapping("/queryScenarioCase")
    @ApiOperation("查找场景案例接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name="scenarioVo", dataType = "ScenarioVo", value = "参数和类型")
    })
    public Result queryScenarioCase(@RequestBody ScenarioVo scenarioVo, HttpSession session){
        try {
            String userId = (String) session.getAttribute("userId");
            scenarioVo.setUserId(userId);
            PageInfo scenarioPage = scenarioService.getScenarioByPage(scenarioVo);
            return Result.toResult(R.ok(scenarioPage));
        } catch (Exception e) {
            log.error(e.getMessage());
            log.error(e.getMessage(),e);
            return Result.toResult(R.error(e.getMessage()));
        }
    }

    @GetMapping("/queryParentCaseType")
    @ApiOperation("查找场景案例的所有类型")
    public Result queryParentCaseType(HttpSession session){
        try {
            String userId = (String) session.getAttribute("userId");
            List<TScenarioCaseType> allParentTypes = scenarioService.getAllParentTypes(userId);
            return Result.toResult(R.ok(allParentTypes));
        } catch (Exception e) {
            log.error(e.getMessage());
            log.error(e.getMessage(),e);
            return Result.toResult(R.error(e.getMessage()));
        }
    }

    /**
     * 转换为场景案例
     * @param
     * @return
     */
    @PostMapping("/changeToCase")
    @ApiOperation("将其它模型转为场景案例 modelId：模型、仪表盘、门户的方案ID，parentCaseType：场景案例父类型 数据模型、仪表盘、主题门户、AI建模" +
            "caseType:子类型 只有数据模型有数据碰撞和计数统计,desc:描述")
    public Result changeToCase(@RequestBody List<ModelCaseVo> modelCaseVos){
        try {
            for (ModelCaseVo modelCaseVo : modelCaseVos) {
                String modelId = modelCaseVo.getModelId();
                String parentCaseType = modelCaseVo.getParentCaseType();
                String caseType = modelCaseVo.getCaseType();
                String desc = modelCaseVo.getDesc();
                scenarioService.changeToScenarioCase(modelId,parentCaseType,caseType,desc);
            }
            return Result.toResult(R.ok());
        } catch (Exception e) {
            log.error(e.getMessage());
            log.error(e.getMessage(),e);
            return Result.toResult(R.error(e.getMessage()));
        }
    }

    @ApiOperation("删除场景案例 caseIds：案例id")
    @PostMapping("/deleteCases")
    public Result deleteCases(@RequestBody List<String> caseIds){
        try {
            scenarioService.deleteScenarioCases(caseIds);
            return Result.toResult(R.ok());
        } catch (Exception e) {
            log.error(e.getMessage());
            log.error(e.getMessage(),e);
            return Result.toResult(R.error(e.getMessage()));
        }
    }


}
