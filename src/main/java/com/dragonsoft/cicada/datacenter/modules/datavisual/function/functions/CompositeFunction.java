package com.dragonsoft.cicada.datacenter.modules.datavisual.function.functions;

import com.code.dragonsoft.dataquery.service.querymodel.ColumnDataModel;
import com.dragonsoft.cicada.datacenter.modules.datavisual.function.ChartConfig;
import com.dragonsoft.cicada.datacenter.modules.datavisual.function.MemoryFunction;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020-12-30 10:05
 */
public class CompositeFunction implements MemoryFunction {

    private List<MemoryFunction> functions;

    public CompositeFunction() {
    }

    public CompositeFunction(List<MemoryFunction> functions) {
        this.functions = functions;
    }

    @Override
    public String getName() {
        return "组合函数";
    }

    @Override
    public String getCode() {
        if (!functions.isEmpty()) {
            List<String> names = functions.stream().map(MemoryFunction::getName).collect(Collectors.toList());
            return String.format("CompositeFunction{%s}", String.join(",", names));
        }
        return "CompositeFunction{}";
    }

    @Override
    public ColumnDataModel calculate(ColumnDataModel dates, ChartConfig config) {
        ColumnDataModel result = dates;
        for (MemoryFunction function : functions) {
            result = function.calculate(result, config);
        }
        return result;
    }

    public void addFunction(MemoryFunction function) {
        if (this.functions == null) {
            this.functions = new ArrayList<>();
        }
        this.functions.add(function);
    }
}
