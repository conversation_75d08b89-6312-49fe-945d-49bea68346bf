package com.dragonsoft.cicada.datacenter.modules.supermarkmodel.controller;

import com.code.common.paging.PageInfo;
import com.code.common.utils.assertion.Assert;
import com.code.metadata.modelsupermark.ResourcesApply;
import com.dragoninfo.dfw.bean.Result;
import com.dragonsoft.cicada.datacenter.modules.supermarkmodel.service.IResourcesService;
import com.dragonsoft.cicada.datacenter.modules.supermarkmodel.vo.ResourcesApplyAuditVo;
import com.dragonsoft.cicada.datacenter.modules.supermarkmodel.vo.ResourcesQueryVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

@CrossOrigin
@RestController
@RequestMapping("/supermarkmodel/modelResource")
@Api(value = "MODELMARK模型市场申请审批")
@Slf4j
public class ResourcesController {


    @Resource
    private IResourcesService resourcesService;

    @PostMapping("/resourceApply")
    @ApiOperation(value = "申请")
    public Result addResourceApply(@RequestBody ResourcesApply apply) {
        Result result = null;
        try {
            resourcesService.addResourcesApply(apply);
            Result.success();
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            Assert.fail("申请失败：" + e.getMessage());
        }
        return result;
    }

    @PostMapping("/resourceApplyList")
    @ApiOperation(value = "申请列表")
    public Result resourceApplyList(HttpServletRequest request, @RequestBody ResourcesQueryVo vo) {
        PageInfo pageResult = null;
        String userId = (String) request.getSession().getAttribute("userId");
        vo.setUserId(userId);
        try {
            pageResult = resourcesService.getListByType(vo);
            Result.success();
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            Assert.fail("获取申请列表失败：" + e.getMessage());
        }
        return Result.success(pageResult);
    }

    @PostMapping("/viewAuditDetail")
    @ApiOperation(value = "审批详情")
    public Result viewMarkModelDetail(@RequestBody ResourcesQueryVo vo) {
        Result result = null;
        try {
            result = resourcesService.auditDetail(vo);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            Assert.fail("查看模型详情失败：" + e.getMessage());
        }
        return result;
    }


    @PostMapping("/audit")
    @ApiOperation(value = "审批")
    public Result audit(HttpServletRequest request,@RequestBody ResourcesApplyAuditVo vo) {
        String userId = (String) request.getSession().getAttribute("userId");
        vo.setAuditUserId(userId);
        try {
            resourcesService.audit(vo);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            Assert.fail("审批失败：" + e.getMessage());
        }
        return Result.success();
    }


    @PostMapping("/auditResultConfirm")
    @ApiOperation(value = "审批结果确认")
    public Result auditResultConfirm(@RequestBody ResourcesApplyAuditVo vo) {
        try {
            resourcesService.auditResultConfirm(vo.getId());
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            Assert.fail("结果确认失败：" + e.getMessage());
        }
        return Result.success();
    }





}
