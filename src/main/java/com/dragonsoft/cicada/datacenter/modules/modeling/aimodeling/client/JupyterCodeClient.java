package com.dragonsoft.cicada.datacenter.modules.modeling.aimodeling.client;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dragonsoft.cicada.datacenter.modules.modeling.aimodeling.vo.EntInputInfo;
import com.dragonsoft.cicada.datacenter.modules.modeling.aimodeling.vo.EvaluateRstItem;
import com.google.common.base.Preconditions;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringEscapeUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description：jupyter脚本相关操作客户端实现类
 * @date ：2021/10/9 11:21
 */
@Slf4j
public class JupyterCodeClient implements IJypyterCodeClient{



    public JupyterCodeClient(
            String initBuildServiceIp,
            String startAndSaveServiceIp,
            String runScriptServiceIp,
            String evaluateRstIp,
            String runOpServiceIp,
            String serviceReleaseIp
    ) {
        this.initBuildServiceIp = initBuildServiceIp;
        this.startAndSaveServiceIp = startAndSaveServiceIp;
        this.runScriptServiceIp = runScriptServiceIp;
        this.evaluateRstIp = evaluateRstIp;
        this.runOpServiceIp = runOpServiceIp;
        this.serviceReleaseIp = serviceReleaseIp;
    }

    public String initBuild(String modelName, String modelDescription) {
        log.info("initIp" + this.initBuildServiceIp);
        log.info("startIp" + this.startAndSaveServiceIp);
        log.info("runIp" + this.runScriptServiceIp);
        log.info("rstIp" + this.evaluateRstIp);
        String url = "http://" + this.initBuildServiceIp + "/build_ipynb/build";
        //String url = "http://**************:6002/build_ipynb/build";
        tmpPostData.clear();
        tmpPostData.put("model_name", modelName);
        tmpPostData.put("model_description", modelDescription);
        return this.httpInvokeHelper.postInvoke(url, tmpPostData);
    }

    public String startScript(String scriptId) {
        log.info("initIp" + this.initBuildServiceIp);
        log.info("startIp" + this.startAndSaveServiceIp);
        log.info("runIp" + this.runScriptServiceIp);
        log.info("rstIp" + this.evaluateRstIp);
        String url = "http://" + this.startAndSaveServiceIp + "/start_and_save/start";
        //String url = "http://**************:6007/start_and_save/start";
        tmpPostData.clear();
        tmpPostData.put("id", scriptId);
        return this.httpInvokeHelper.postInvoke(url, tmpPostData);
    }

    public void saveScript(String scriptId) {
        log.info("initIp" + this.initBuildServiceIp);
        log.info("startIp" + this.startAndSaveServiceIp);
        log.info("runIp" + this.runScriptServiceIp);
        log.info("rstIp" + this.evaluateRstIp);
        String url = "http://" + this.startAndSaveServiceIp + "/start_and_save/save";
        //String url = "http://**************:6007/start_and_save/save";
        tmpPostData.clear();
        tmpPostData.put("id", scriptId);
        this.httpInvokeHelper.postInvoke(url, tmpPostData);
    }

    public String runScript(String scriptId) {
        log.info("initIp" + this.initBuildServiceIp);
        log.info("startIp" + this.startAndSaveServiceIp);
        log.info("runIp" + this.runScriptServiceIp);
        log.info("rstIp" + this.evaluateRstIp);
        String url = "http://" + this.runScriptServiceIp + "/script_run/run";
        //String url = "http://**************:6008/script_run/run";
        tmpPostData.clear();
        tmpPostData.put("script_id", scriptId);
        String rspMsg = this.httpInvokeHelper.postInvoke(url, tmpPostData);
        Preconditions.checkNotNull(rspMsg, "执行脚本服务调用返回结果为空！");
        Map rspMap = (Map)JSONObject.parse(rspMsg);
        Object logId = rspMap.get("LogId");
        Preconditions.checkNotNull(logId, "执行脚本服务调用返回结果未包含日志Id !");
        return String.valueOf(logId);
    }

    public List<EvaluateRstItem> queryEvaluateRst(String logId) {
        log.info("initIp" + this.initBuildServiceIp);
        log.info("startIp" + this.startAndSaveServiceIp);
        log.info("runIp" + this.runScriptServiceIp);
        log.info("rstIp" + this.evaluateRstIp);
        String url = "http://" + this.evaluateRstIp + "/evaluate_rst/query";
        //String url = "http://**************:6009/evaluate_rst/query";
        tmpPostData.clear();
        tmpPostData.put("log_id", logId);
        String rspMsg = this.httpInvokeHelper.postInvoke(url, tmpPostData);
        return JSONObject.parseArray(rspMsg, EvaluateRstItem.class);
    }

    public EntInputInfo queryEntInputInfo(String logId) {
        log.info("initIp" + this.initBuildServiceIp);
        log.info("startIp" + this.startAndSaveServiceIp);
        log.info("runIp" + this.runScriptServiceIp);
        log.info("rstIp" + this.evaluateRstIp);
        String url = "http://" + this.evaluateRstIp + "/evaluate_rst/ent_acc_rst";
        //String url = "http://**************:6009/evaluate_rst/ent_acc_rst";
        tmpPostData.clear();
        tmpPostData.put("log_id", logId);
        String rspMsg = this.httpInvokeHelper.postInvoke(url, tmpPostData);
        return JSONObject.parseObject(rspMsg, EntInputInfo.class);
    }

    public JSONArray queryReleaseRst(String scriptId) {
        String url = "http://" + this.evaluateRstIp + "/evaluate_rst/release_rst";
        tmpPostData.clear();
        tmpPostData.put("script_id", scriptId);
        String rspMsg = this.httpInvokeHelper.postInvoke(url, tmpPostData);
        Preconditions.checkNotNull(rspMsg, "执行脚本服务调用返回结果为空！");
        JSONArray jsonArray = JSONArray.parseArray(rspMsg);
        return jsonArray;
    }

    @Override
    public JSONObject testAiService(String op_input,String url) {
        String testUrl = "http://" + this.runOpServiceIp + url;
        tmpPostData.clear();
        tmpPostData.put("op_input", op_input);
        String rspMsg = this.httpInvokeHelper.postInvoke(testUrl, tmpPostData);
        Preconditions.checkNotNull(rspMsg, "执行脚本服务调用返回结果为空！");
        rspMsg = StringEscapeUtils.unescapeJava(rspMsg);
        if (rspMsg.startsWith("\"")){
            rspMsg = rspMsg.substring(1, rspMsg.length() - 2);
        }
        JSONObject jsonObject = JSONObject.parseObject(rspMsg);
        return jsonObject;
    }

    @Override
    public String serviceRelease(String logId, String releasePath, String serviceCode) {
        if (releasePath.endsWith("/")){
            releasePath = releasePath.substring(0,releasePath.length() - 1);
        }
        String url = "http://" + this.serviceReleaseIp + "/service_release/service_release";
        tmpPostData.clear();
        tmpPostData.put("log_id", logId);
        tmpPostData.put("release_path", releasePath);
        tmpPostData.put("service_code", serviceCode);
        String rspMsg = this.httpInvokeHelper.postInvoke(url, tmpPostData);
        Preconditions.checkNotNull(rspMsg, "执行脚本服务调用返回结果为空！");
        //Map rspMap = (Map)JSONObject.parse(rspMsg);
        return rspMsg;
    }

    @Override
    public String serviceCancel(String releasePath, String serviceCode) {
        String url = "http://" + this.serviceReleaseIp + "/service_release/service_cancel";
        tmpPostData.clear();
        tmpPostData.put("release_path", releasePath);
        tmpPostData.put("service_code", serviceCode);
        String rspMsg = this.httpInvokeHelper.postInvoke(url, tmpPostData);
        Preconditions.checkNotNull(rspMsg, "执行脚本服务调用返回结果为空！");
        //Map rspMap = (Map)JSONObject.parse(rspMsg);
        return rspMsg;
    }

    private HttpInvokeHelper httpInvokeHelper = new HttpInvokeHelper();
    /**
     * 脚本初始化服务ip
     */
    private String initBuildServiceIp;
    /**
     * 脚本启动与脚本保存服务ip
     */
    private String startAndSaveServiceIp;
    /**
     * 脚本执行服务ip
     */
    private String runScriptServiceIp;
    /**
     * 指标评估结果查询服务ip
     */
    private String evaluateRstIp;

    private String runOpServiceIp;

    private String serviceReleaseIp;

    public String getServiceReleaseIp() {
        return serviceReleaseIp;
    }

    public void setServiceReleaseIp(String serviceReleaseIp) {
        this.serviceReleaseIp = serviceReleaseIp;
    }

    private Map<String,String> tmpPostData = new HashMap<String, String>();

    public String getInitBuildServiceIp() {
        return initBuildServiceIp;
    }

    public void setInitBuildServiceIp(String initBuildServiceIp) {
        this.initBuildServiceIp = initBuildServiceIp;
    }

    public String getStartAndSaveServiceIp() {
        return startAndSaveServiceIp;
    }

    public void setStartAndSaveServiceIp(String startAndSaveServiceIp) {
        this.startAndSaveServiceIp = startAndSaveServiceIp;
    }

    public String getRunScriptServiceIp() {
        return runScriptServiceIp;
    }

    public void setRunScriptServiceIp(String runScriptServiceIp) {
        this.runScriptServiceIp = runScriptServiceIp;
    }

    public String getEvaluateRstIp() {
        return evaluateRstIp;
    }

    public void setEvaluateRstIp(String evaluateRstIp) {
        this.evaluateRstIp = evaluateRstIp;
    }

    public String getRunOpServiceIp() {
        return runOpServiceIp;
    }

    public void setRunOpServiceIp(String runOpServiceIp) {
        this.runOpServiceIp = runOpServiceIp;
    }
}
