package com.dragonsoft.cicada.datacenter.modules.metadata.service;

import com.code.common.bean.BeanFactory;
import com.code.common.utils.assertion.Assert;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> Avizii
 * @Create : 2021.03.29
 */
@Component
public class DataBaseFactory {

    @Autowired
    private BeanFactory beanFactory;

    public static final List<String> RDB_LIST = Collections.unmodifiableList(
            Lists.newArrayList(
                    DataSourceEnum.ORACLE.name(),
                    DataSourceEnum.GBASE.name(),
                    DataSourceEnum.MYSQL.name(),
                    DataSourceEnum.POSTGRESQL.name(),
                    DataSourceEnum.HWMPP.name(),
                    DataSourceEnum.HIVE.name(),
                    DataSourceEnum.TBASE.name(),
                    DataSourceEnum.VERTICA.name(),
                    DataSourceEnum.GREENPLUM.name()));

    public DataBaseConnectService matchDb(String dbType) {
        DataSourceEnum dataSourceEnum = DataSourceEnum.valueOf(dbType.toUpperCase());
        Assert.notNull(dataSourceEnum, "获取类型[" + dbType + "]数据源为空!");
        return beanFactory.getBean(dataSourceEnum.getPrefix() + "ConnectService", DataBaseConnectService.class);
    }

    public enum DataSourceEnum {
        MYSQL("MySql", "MYSQL", "rdb"),
        ORACLE("Oracle", "ORACLE", "rdb"),
        GREENPLUM("Greenplum", "GREENPLUM", "rdb"),
        HIVE("Hive", "Hive", "rdb"),
        HWMPP("LibrA", "hwmpp", "rdb"),
        POSTGRESQL("PostgreSql", "POSTGRESQL", "rdb"),
        HBASE("HBase", "HBASE", "hbase"),
        ZOOKEEPER("ZooKeeper", "zookeeper", "zk"),
        REDIS("Redis", "redis", "redis"),
        KAFKA("Kafka", "kafka", "kafka"),
        TBASE("TBase","TBASE","rdb"),
        VERTICA("Vertica","vertica","rdb"),
        ELASTICSEARCH("Elasticsearch", "Elasticsearch", "es"),
        GBASE("Gbase","GBASE","rdb"),
        ;

        private final String label;

        private final String code;

        private final String prefix;

        DataSourceEnum(String label, String code, String prefix) {
            this.label = label;
            this.code = code;
            this.prefix = prefix;
        }

        public String getLabel() {
            return label;
        }

        public String getCode() {
            return code;
        }

        public String getPrefix() {
            return prefix;
        }

        public DataSourceEnum getByName(String name) {
            DataSourceEnum dataSourceEnum = DataSourceEnum.valueOf(name);
            Assert.notNull(dataSourceEnum);
            return dataSourceEnum;
        }
    }

}
