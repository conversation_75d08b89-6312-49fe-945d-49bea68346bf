package com.dragonsoft.cicada.datacenter.modules.datavisual.widget;


import java.util.Map;

public interface IChartWidgetBuilder {

    public Map<String,Object> loadingData(String code, String json, boolean type);

    public Map<String,Object> loadingCacheData(String code, String json, boolean type);

    void deleteCacheDashBoard(String taskCode,String taskJson,boolean taskType);

    String getSQL(String code, String data, boolean type);
}
