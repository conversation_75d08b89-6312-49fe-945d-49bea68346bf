package com.dragonsoft.cicada.datacenter.modules.modeling.client;

import com.dragoninfo.dfw.bean.Result;
import okhttp3.ResponseBody;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Query;

import java.util.Map;

public interface MLSQLScheduleClient {

    @POST("/api/startTask")
    Call<ResponseBody> startTask(@Body Map<String, Object> map);

    @GET("/api/stop")
    Call<ResponseBody> stopTask(@Query("transId") String transId);

    @POST("/serviceApi/subscribe")
    Call<Result> subscribe(@Body Map<String, Object> map);


    @POST("/serviceApi/removeSubscribe")
    Call<Result> removeSubscribe(@Body Map<String, Object> map);

    @POST("/serviceApi/updateSubscribe")
    Call<Result> updateSubscribe(@Body Map<String, Object> map);

}
