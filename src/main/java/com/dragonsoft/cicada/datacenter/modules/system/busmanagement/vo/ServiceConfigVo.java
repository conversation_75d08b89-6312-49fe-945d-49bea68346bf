package com.dragonsoft.cicada.datacenter.modules.system.busmanagement.vo;

import com.google.common.collect.Lists;
import lombok.Data;

import java.util.List;

@Data
public class ServiceConfigVo extends BusCommonVo {
    public static String REQUEST_TYPE = "0";
    public static String RESPONSE_TYPE = "1";
    public static String REQUEST_MODEL = "[{\"code\":\"sender_id\",\"columnType\":\"String\",\"configType\":\"0\",\"isRequired\":true,\"name\":\"请求方ID\"},{\"code\":\"service_id\",\"columnType\":\"String\",\"configType\":\"0\",\"isRequired\":true,\"name\":\"服务方ID\"},{\"code\":\"end_user.name\",\"columnType\":\"String\",\"configType\":\"0\",\"isRequired\":true,\"name\":\"用户姓名\"},{\"code\":\"end_user.id_card\",\"columnType\":\"String\",\"configType\":\"0\",\"isRequired\":true,\"name\":\"身份标识\"},{\"code\":\"end_user.department\",\"columnType\":\"String\",\"configType\":\"0\",\"isRequired\":true,\"name\":\"所在单位\"},{\"code\":\"end_user.certificate\",\"columnType\":\"String\",\"configType\":\"0\",\"isRequired\":true,\"name\":\"证书编号\"},{\"code\":\"end_user.device_id\",\"columnType\":\"String\",\"configType\":\"0\",\"isRequired\":true,\"name\":\"终端标识\"},{\"code\":\"timestamp\",\"columnType\":\"String\",\"configType\":\"0\",\"isRequired\":true,\"name\":\"时间戳\"},{\"code\":\"Content-Type\",\"columnType\":\"String\",\"configType\":\"0\",\"isRequired\":true,\"name\":\"请求头类型\",\"defaultValue\":\"application/json\"},{\"code\":\"Cookie\",\"columnType\":\"String\",\"configType\":\"0\",\"isRequired\":true,\"name\":\"标识\"}]";
    public static String RESPONSE_MODEL = "[{\"code\":\"status\",\"columnType\":\"String\",\"configType\":\"1\",\"isRequired\":true,\"name\":\"响应状态\"},{\"code\":\"status_message\",\"columnType\":\"String\",\"configType\":\"1\",\"isRequired\":true,\"name\":\"状态备注\"}]";

    private static List<String> requestList = Lists.newArrayList("sender_id", "end_user.name", "timestamp", "", "Cookie", "end_user.id_card", "service_id", "end_user.device_id", "Content-Type", "end_user.department", "end_user.certificate");
    private static List<String> responseList = Lists.newArrayList("status", "status_message");
    private Boolean isRequired = false;//是否必填

    private String defaultValue;

    private String configType;//0是request，1是response

    private Boolean isDelete = false;

    public static boolean judgeIsDelete(String code, String configType) {
        List<String> tmpList = configType.equals("0") ? requestList : responseList;
        if (tmpList.contains(code)) return false;
        return true;
    }
}
