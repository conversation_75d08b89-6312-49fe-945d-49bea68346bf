package com.dragonsoft.cicada.datacenter.modules.datavisual.model.enums;


/**
 * 字段类型枚举。。根据不同数据库枚举不同类型
 */
public enum FiledJSTypeEnum {

    NUMBER("number", "NUMERIC","INT2","INT4","INT8","DECIMAL","FLOAT4","FLOAT8","SERIAL4","SERIAL8","NUMBER","LONG","BINARY_FLOAT","BINARY_DOUBLE","BIGINT","BIGSERIAL","BYTE","INTEGER","INT","SERIAL","SERIAL8","SMALLFLOAT","SMALLINT","REAL","DEC","DECIMAL","DOUBLE PRECISION","FLOAT","INT","TINYINT","DOUBLE","INTEGER","LONG","SHORT","BYTE","HALF_FLOAT"),
    STRING("string", "VARCHAR","CHAR","TEXT","VARCHAR2","NVARCHAR2","NCHAR","CHARACTER","CHARACTER VARYING","NCHAR","NVARCHAR","TEXT","STRING","TEXT","KEYWORD","StringType"),
    TIME("time", "TIMESTAMP","DATE","DATETIME","TIME","INTERVAL","DateType");
    String code;
    String[] dataBaseTypes;

    FiledJSTypeEnum(String code, String... dataBaseTypes) {
        this.code = code;
        this.dataBaseTypes = dataBaseTypes;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String[] getDataBaseTypes() {
        return dataBaseTypes;
    }

    public void setDataBaseTypes(String[] dataBaseTypes) {
        this.dataBaseTypes = dataBaseTypes;
    }
}
