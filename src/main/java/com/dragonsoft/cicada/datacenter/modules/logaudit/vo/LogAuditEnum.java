package com.dragonsoft.cicada.datacenter.modules.logaudit.vo;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/10/11
 */
public enum LogAuditEnum {
    /**
     * 日志审计枚举
     * */
    DATACONNECTION("数据连接","dataConnection"),
    PROCESSMODELING("数据建模","processModeling"),
    SERVICEMANAGEMENT("服务管理","serviceManagement"),
    FASTANALYSIS("快速分析","fastAnalysis"),
    DASHBOARD("仪表盘","dashboard"),
    THEMEPORTAL("主题门户","themePortal"),
    DATASETOPERATION("数据准备","dataSetOperation"),
    ROLEMANAGEMENT("角色管理","roleManagement"),
    USERMANAGEMENT("用户管理","userManagement"),
    GROUPMANAGEMENT("用户组管理","groupManagement"),
    UDFOPERATORMANAGE("算子管理","udfOperatorManage");


    private String label;
    private String value;


    public String getLabel() {
        return label;
    }

    public String getValue() {
        return value;
    }

    LogAuditEnum(String label, String value) {
        this.label = label;
        this.value = value;
    }

    public static String getLogLableByValue(String value) {
        for (LogAuditEnum logAuditEnum : LogAuditEnum.values()) {
            if(Objects.equals(logAuditEnum.getValue(), value)) {
                return logAuditEnum.getLabel();
            }
        }
        return "";
    }

    public static String getLogValueByLable(String label) {
        for (LogAuditEnum logAuditEnum : LogAuditEnum.values()) {
            if(Objects.equals(logAuditEnum.getLabel(), label)) {
                return logAuditEnum.getValue();
            }
        }
        return "";
    }

    public static Map<String, String> getAllTypes() {
        Map<String, String> rtValue = new HashMap<>(LogAuditEnum.values().length);
        for (LogAuditEnum value : LogAuditEnum.values()) {
            rtValue.put(value.getLabel(), value.getValue());
        }
        return rtValue;
    }
}



