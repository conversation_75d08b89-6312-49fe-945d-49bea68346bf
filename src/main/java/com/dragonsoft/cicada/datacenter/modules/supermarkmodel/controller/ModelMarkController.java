package com.dragonsoft.cicada.datacenter.modules.supermarkmodel.controller;

import com.code.common.paging.PageInfo;
import com.code.common.utils.R;
import com.code.common.utils.assertion.Assert;
import com.dragoninfo.dfw.bean.Result;
import com.dragonsoft.cicada.datacenter.modules.supermarkmodel.service.IModelMarkService;
import com.dragonsoft.cicada.datacenter.modules.supermarkmodel.vo.ModelMarketQueryVo;
import com.dragonsoft.cicada.datacenter.modules.supermarkmodel.vo.SupermarkModelVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

@CrossOrigin
@RestController
@RequestMapping("/supermarkmodel/modelmark")
@Api(value = "MODELMARK模型市场")
@Slf4j
public class ModelMarkController {

    @Autowired
    private IModelMarkService modelMarkServiceImpl;

    @PostMapping("/viewMarkModelDetail")
    @ApiOperation(value = "MODELMARK模型详情")
    public Result viewMarkModelDetail(@RequestBody SupermarkModelVo modelVo, HttpServletRequest request) {
        Result result = null;
        try {
            String userId = (String) request.getSession().getAttribute("userId");
            modelVo.setUserId(userId);

            result = modelMarkServiceImpl.initViewMarkModelDetail(modelVo);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            Assert.fail("查看模型详情失败：" + e.getMessage());
        }

        return result;
    }

    @PostMapping("/sameTypeModelList")
    @ApiOperation(value = "MODELMARK同类型推荐查询")
    public Result querySameTypeMarkModelPage(@RequestBody Map<String, Object> queryModelMap) {
        Result result = null;
        try {
            result = modelMarkServiceImpl.querySameTypeMarkModelPage(queryModelMap);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            Assert.fail("同类型推荐查询失败：" + e.getMessage());
        }

        return result;
    }

    @PostMapping("/focusedUserlList")
    @ApiOperation(value = "MODELMARK已关注用户列表")
    public Result queryFocusedUserList(@RequestBody Map<String, Object> queryModelMap) {
        Result result = null;
        try {
            result = modelMarkServiceImpl.queryFocusedUserList(queryModelMap);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            Assert.fail("已关注用户列表查询失败：" + e.getMessage());
        }

        return result;
    }

    @PostMapping("/focusOrUnFocusModel")
    @ApiOperation(value = "MODELMARK关注或取消关注模型")
    public Result focusOrUnFocusModel(@RequestBody SupermarkModelVo modelVo, HttpServletRequest request) {
        Result result = null;
        try {
            String userId = (String) request.getSession().getAttribute("userId");
            modelVo.setUserId(userId);

            result = modelMarkServiceImpl.updateFocusOrUnFocusModel(modelVo);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            Assert.fail("注或取消关注模型失败：" + e.getMessage());
        }

        return result;
    }

    @PostMapping("/queryModelEvaluationList")
    @ApiOperation(value = "MODELMARK查询累计评价列表")
    public Result queryModelEvaluationList(@RequestBody Map<String, Object> queryModelMap) {
        Result result = null;
        try {

            result = modelMarkServiceImpl.queryModelEvaluationList(queryModelMap);

        } catch (Exception e) {
            log.error(e.getMessage(),e);
            Assert.fail("查询累计评价列表失败：" + e.getMessage());
        }

        return result;
    }

    @PostMapping("/evaluationModel")
    @ApiOperation(value = "MODELMARK评价模型")
    public Result evaluationModel(@RequestBody SupermarkModelVo modelVo, HttpServletRequest request) {
        Result result = null;
        try {
            String userId = (String) request.getSession().getAttribute("userId");
            modelVo.setUserId(userId);

            result = modelMarkServiceImpl.updateEvaluationModel(modelVo);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            Assert.fail("评价模型失败：" + e.getMessage());
        }

        return result;
    }

    @PostMapping("/evaluationUser")
    @ApiOperation(value = "MODELMARK评价人员")
    public Result evaluationUser(@RequestBody SupermarkModelVo modelVo, HttpServletRequest request) {
        Result result = null;
        try {
            String userId = (String) request.getSession().getAttribute("userId");
            modelVo.setUserId(userId);

            result = modelMarkServiceImpl.updateEvaluationUser(modelVo);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            Assert.fail("评价模型失败：" + e.getMessage());
        }

        return result;
    }

    @PostMapping("/modelEvaluationDetail")
    @ApiOperation(value = "MODELMARK查看模型评价详情")
    public Result viewModelEvaluationDetail(@RequestBody SupermarkModelVo modelVo, HttpServletRequest request) {
        Result result = null;
        try {
            String userId = (String) request.getSession().getAttribute("userId");
            modelVo.setUserId(userId);

            result = modelMarkServiceImpl.viewModelEvaluationDetail(modelVo);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            Assert.fail("查看模型评价详情失败：" + e.getMessage());
        }

        return result;
    }


    @PostMapping("/userEvaluationDetail")
    @ApiOperation(value = "MODELMARK查看人员评价详情")
    public Result userEvaluationDetail(@RequestBody SupermarkModelVo modelVo, HttpServletRequest request) {
        Result result = null;
        try {
            String userId = (String) request.getSession().getAttribute("userId");
            modelVo.setUserId(userId);

            result = modelMarkServiceImpl.viewUserEvaluationDetail(modelVo);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            Assert.fail("查看模型评价详情失败：" + e.getMessage());
        }

        return result;
    }


    @PostMapping("/userEvaluationBasicDetail")
    @ApiOperation(value = "MODELMARK查看人员评价基础详情")
    public Result userEvaluationBasicDetail(@RequestBody SupermarkModelVo modelVo, HttpServletRequest request) {
        Result result = null;
        try {
            String userId = (String) request.getSession().getAttribute("userId");
            modelVo.setUserId(userId);

            result = modelMarkServiceImpl.viewUserEvaluationBasicDetail(modelVo);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            Assert.fail("查看模型评价详情失败：" + e.getMessage());
        }

        return result;
    }




    @PostMapping("/queryModelMarketPage")
    @ApiOperation(value = "模型市场查询")
    public Result queryModelMarketPage(@RequestBody ModelMarketQueryVo queryVo) {
        try {
            PageInfo pageInfo = modelMarkServiceImpl.queryMarketPage(queryVo);
            return Result.toResult(R.ok(pageInfo));
        } catch (Exception e) {
            Assert.fail(e.getMessage());
            log.error(e.getMessage(),e);
            return Result.toResult(R.error(e.getMessage()));
        }
    }



}
