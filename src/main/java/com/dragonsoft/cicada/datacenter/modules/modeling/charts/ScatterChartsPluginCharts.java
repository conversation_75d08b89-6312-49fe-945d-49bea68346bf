package com.dragonsoft.cicada.datacenter.modules.modeling.charts;

import com.alibaba.fastjson.JSONArray;
import com.code.common.utils.assertion.Assert;

import java.util.List;

/**
 * <AUTHOR> Wu.D.J
 * @Create : 2020.10.28
 */
public class ScatterChartsPluginCharts extends BaseMoreDimension {
    @Override
    public PreviewChartsModel create(JSONArray json, List<String> columns) {
        Assert.isTrue(columns.size() >= 1, String.format("列数异常%d，检查sql是否正确", columns.size()));
        if (columns.size() == 1) {
            columns.add(columns.get(0));
        }
        return new PreviewChartsModel(columns, json);
    }
}
