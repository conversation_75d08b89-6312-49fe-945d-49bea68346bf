package com.dragonsoft.cicada.datacenter.modules.datavisual.controller;

import com.code.common.utils.R;
import com.code.metadata.datavisual.WidgetMeta;
import com.dragoninfo.dfw.annotation.FuncScanAnnotation;
import com.dragoninfo.dfw.annotation.ValidateAndLogAnnotation;
import com.dragoninfo.dfw.bean.Result;
import com.dragonsoft.cicada.datacenter.modules.datavisual.widgetmeta.IWidgetMetaBuild;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
@CrossOrigin
@RestController
@RequestMapping("/widgetMeta")
public class WidgetMetaController {
    @Autowired
    IWidgetMetaBuild iWidgetMetaBuild;

    @GetMapping("/getNoChart")
    @FuncScanAnnotation(code = "visualEditGetNoChart", name = "编辑", parentCode = "dashboard")
    @ValidateAndLogAnnotation
    public Result getList() {
        List<WidgetMeta> widgetMetas = iWidgetMetaBuild.builderNoChartList();
        return Result.toResult(R.ok(widgetMetas));
    }


}
