package com.dragonsoft.cicada.datacenter.modules.metadata.vo;

import lombok.Data;

import java.util.List;

@Data
public class DataObjectVO {

    private String directoryId;
    private String key;
    private String instanceId;
    private String dataSourceId;
    private String dataSourceName;
    private String dbType;
    private List<String> dataObjectTableList;
    private String industryClassify;
    private String businessClassify;
    private String level1ElementClassify;
    private String level2ElementClassify;
    private String resourceAttribute;
    private String level3ElementClassify;
    private String applicationSystemClassify;
    private String applicationSystemName;
    private String objUpdateType;
    private String objUpdateInterval;
    private String tableUseClassify;
    private String dictionaryClassify;
    private String id;
    private String zhName;
    private String businessTimestamp;
    private String odpsLifeInterval;
    private String dataObjectClassify;
    private String additionalRecordFlag;
    private String indexName;
    private String configPath; // 配置路径
    private String userId;
    private List<String> uniqueIdentificationList;
    private String registerScript;
    private String objectType;
    private String enName;

}
