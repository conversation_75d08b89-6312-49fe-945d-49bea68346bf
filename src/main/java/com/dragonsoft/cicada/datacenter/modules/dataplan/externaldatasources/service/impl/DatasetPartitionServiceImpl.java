package com.dragonsoft.cicada.datacenter.modules.dataplan.externaldatasources.service.impl;

import com.code.common.utils.StringUtils;
import com.code.common.utils.assertion.Assert;
import com.code.metadata.base.partition.ClassiferPartition;
import com.code.metadata.base.partition.EnumClassiferPartitionType;
import com.code.metadata.base.partition.PartitionRange;
import com.code.metadata.model.core.Classifier;
import com.code.metadata.model.core.StructuralFeature;
import com.code.metadata.res.common.ClassifierStat;
import com.code.metaservice.base.vo.ClassifierPartitionVo;
import com.code.metaservice.base.vo.PartitionInfoVo;
import com.code.metaservice.base.vo.PartitionRangeVo;
import com.dragonsoft.cicada.datacenter.modules.dataplan.externaldatasources.service.DatasetPartitionService;
import com.fw.service.BaseService;
import com.fw.service.annotation.Service;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Service
@Slf4j
public class DatasetPartitionServiceImpl extends BaseService implements DatasetPartitionService {

    @Override
    public List<ClassifierPartitionVo> queryPartitionList(String statId, String keyword) {
        Assert.notNull(statId, "未找到" + statId + "的分区方案");
        List<ClassiferPartition> list;
        this.setObjDefaultPartition(statId);
        String hql = "FROM ClassiferPartition WHERE classifier_id=:statId";
        if (StringUtils.isNotBlank(keyword)) {
            hql += " AND (name LIKE :name OR code LIKE :code) ORDER BY code";
            list = this.baseDao.queryForList(hql, addParam("statId", statId).addParam("name", "%" + keyword + "%").addParam("code", "%" + keyword + "%").param());
        } else {
            hql += " ORDER BY code";
            list = this.baseDao.queryForList(hql, addParam("statId", statId).param());
        }
        List<ClassifierPartitionVo> partition = Lists.newArrayList();
        for (ClassiferPartition cp : list) {
            ClassifierPartitionVo cpv = new ClassifierPartitionVo();
            cpv.setId(cp.getId());
            cpv.setClassifierId(statId);
            cpv.setName(cp.getName());
            cpv.setCode(cp.getCode());
            cpv.setPartitionType(getPartitionName(cp.getPartitionType()));
            cpv.setIsExecute(cp.getIsExecute());
            partition.add(cpv);
        }
        return partition;
    }

    private String getPartitionName(String code) {
        if (Strings.isNullOrEmpty(code)) {
            return code;
        }
        if (code.equals(EnumClassiferPartitionType.PHYSICAL.getName())) {
            return "物理分区";
        }
        if (code.equals(EnumClassiferPartitionType.BUSI_PHYSICAL.getName())) {
            return "业务分区";
        }
        if (code.equals(EnumClassiferPartitionType.LOGIC.getName())) {
            return "逻辑分区";
        }
        return null;
    }

    private void setObjDefaultPartition(String id) {
        ClassifierStat cs = (ClassifierStat) this.baseDao.get(ClassifierStat.class, id);
        if (StringUtils.isBlank(cs.getDefaultPhysical())) {
            cs.setDefaultPhysical("PHYSICAL");
            this.baseDao.update(cs);
        }
    }

    @Override
    public Map<String, String> deleteDatasetPartitionById(String id) {
        Map<String, String> map = Maps.newHashMap();
        Assert.notNull(id, "未找到" + id + "的分区方案");
        try {
            ClassiferPartition partition = (ClassiferPartition) this.baseDao.get(ClassiferPartition.class, id);
            String hql = "FROM PartitionRange WHERE PARTITION_ID=:partitionId";
            List<PartitionRange> ranges = this.baseDao.queryForList(hql, addParam("partitionId", id).param());
            for (PartitionRange range : ranges) {
                this.baseDao.delete(range);
            }
            this.baseDao.delete(partition);
            map.put("message", "删除成功");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            map.put("message", e.getMessage());
        }
        return map;
    }

    @Override
    public PartitionInfoVo queryPartitionRange(String partitionId) {
        PartitionInfoVo result = new PartitionInfoVo();
        ClassiferPartition cp = (ClassiferPartition) this.baseDao.get(ClassiferPartition.class, partitionId);
        result.setPartitionName(cp.getName());
        result.setPartitionCode(cp.getCode());
        result.setPartitionId(partitionId);
        result.setClassifierId(cp.getClassifier().getId());
        String hql = "FROM PartitionRange WHERE PARTITION_ID=:id";
        List<PartitionRange> list = this.baseDao.queryForList(hql, addParam("id", partitionId).param());
        List<PartitionRangeVo> vo = Lists.newArrayList();
        for (PartitionRange range : list) {
            PartitionRangeVo rvo = new PartitionRangeVo();
            rvo.setRangeId(range.getId());
            rvo.setCompareType(range.getCompareType());
            rvo.setRangeCode(range.getCode());
            rvo.setRangeIsExecute(range.getIsExecute());
            rvo.setPartitionVal(range.getPartitionVal());
            vo.add(rvo);
        }
        result.setRange(vo);
        return result;
    }

    @Override
    public Map<String, String> saveOrUpdatePartition(PartitionInfoVo partition) {
        Map<String, String> map = Maps.newHashMap();
        try {
            Assert.hasText(partition.getPartitionName(), "分区名称不能为空，请输入。");
            Assert.hasText(partition.getPartitionCode(), "分区中文名称不能为空，请输入。");
            Assert.notZero(partition.getRange().size(), "请至少输入一个分区RANGE。");
            checkNameAndCode(partition);
            if (StringUtils.isNotBlank(partition.getPartitionId())) {
                updatePartition(partition);
            } else {
                savePatition(partition);
            }
            map.put("message", "success");
            return map;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            map.put("message", e.getMessage());
            return map;
        }
    }

    /**
     * @param partition
     */
    private void checkNameAndCode(PartitionInfoVo partition) {
        /*判断中英文是否存在*/
        int countName = Integer.parseInt(this.baseDao.sqlQueryForValue( getSql(partition,true)));
        Assert.isZero(countName, "分区中文名称已存在，请重新输入。");
        int countCode = Integer.parseInt(this.baseDao.sqlQueryForValue(getSql(partition,false)));
        Assert.isZero(countCode, "分区英文名称已存在，请重新输入。");
    }

    private String getSql(PartitionInfoVo partition, boolean type) {
        String sql = "SELECT COUNT(*) FROM t_md_classifer_partition WHERE ";
        String typeStr;
        if (type) {
            typeStr = " name='" + partition.getPartitionName();
        } else {
            typeStr = " code='" + partition.getPartitionCode();
        }
        sql += typeStr;
        if (StringUtils.isNotBlank(partition.getPartitionId())) {
            String valSql = "SELECT classifier_id FROM t_md_classifer_partition WHERE id='" + partition.getPartitionId() + "'";
            String classifierId = this.baseDao.sqlQueryForValue(valSql);
            sql += "' AND classifier_id='" + classifierId + "' and id !='" + partition.getPartitionId() + "'";
        } else {
            sql += "' AND classifier_id='" + partition.getClassifierId() + "'";
        }
        return sql;
    }

    private void savePatition(PartitionInfoVo partition) {
        ClassiferPartition cp = new ClassiferPartition();
        cp.setName(partition.getPartitionName());
        cp.setCode(partition.getPartitionCode());
        cp.setPartitionType(partition.getPartitionType());
        cp.setType("ClassifierPartition");
        cp.setOwnerId(partition.getClassifierId());
        Classifier cf = new Classifier();
        cf.setId(partition.getClassifierId());
        cp.setClassifier(cf);
        cp.setOwner(cf);
        String hql = "FROM ClassiferPartition WHERE OWNER_ID=:ownerId";
        List<ClassiferPartition> oldPartition = this.baseDao.queryForList(hql, addParam("ownerId", cf.getId()).param());
        int count = 0;
        if (oldPartition != null) {
            for (ClassiferPartition oldCp : oldPartition) {
                if (oldCp.getIsExecute() != null && "1".equals(oldCp.getIsExecute())) {
                    count++;
                }
            }
        }
        cp.setIsExecute(count > 0 ? "0" : "1");
        ClassifierStat cs = (ClassifierStat) this.baseDao.get(ClassifierStat.class, partition.getClassifierId());
        if (count == 0) {
            cs.setDefaultPhysical(partition.getPartitionType());
        }
        this.baseDao.update(cs);
        this.baseDao.save(cp);
        for (PartitionRangeVo item : partition.getRange()) {
            Assert.hasText(item.getPartitionVal(), "表达式不能为空，请输入。");
            PartitionRange pr = new PartitionRange();
            pr.setIsExecute(item.getRangeIsExecute());
            pr.setPartitionVal(item.getPartitionVal());
            pr.setCompareType(item.getCompareType());
            pr.setIsExecute(StringUtils.isBlank(item.getRangeIsExecute()) ? "0" : item.getRangeIsExecute());
            pr.setOwner(cp);
            pr.setOwnerId(cp.getId());
            pr.setCode(item.getRangeCode());
            pr.setPartition(cp);
            this.baseDao.save(pr);
        }
    }

    private void updatePartition(PartitionInfoVo partition) {
        ClassiferPartition classiferPartition = (ClassiferPartition) this.baseDao.get(ClassiferPartition.class, partition.getPartitionId());
        classiferPartition.setName(partition.getPartitionName());
        classiferPartition.setCode(partition.getPartitionCode());
        classiferPartition.setType("ClassifierPartition");
        classiferPartition.setPartitionType(partition.getPartitionType());
        this.baseDao.update(classiferPartition);
        //删除页面去掉的range
        this.deletePartitionRange(partition.getPartitionId(), partition.getRange());
        for (PartitionRangeVo item : partition.getRange()) {
            if (StringUtils.isNotBlank(item.getRangeId())) {
                PartitionRange partitionRange = (PartitionRange) this.baseDao.get(PartitionRange.class, item.getRangeId());
                Assert.hasText(item.getPartitionVal(), "表达式不能为空，请输入。");
                partitionRange.setCode(item.getRangeCode());
                partitionRange.setCompareType(item.getCompareType());
                partitionRange.setOwner(classiferPartition);
                partitionRange.setOwnerId(partition.getPartitionId());
                partitionRange.setPartitionVal(item.getPartitionVal());
                partitionRange.setIsExecute(StringUtils.isBlank(item.getRangeIsExecute()) ? "0" : item.getRangeIsExecute());
                this.baseDao.update(partitionRange);
            } else {
                PartitionRange pr = new PartitionRange();
                Assert.hasText(item.getPartitionVal(), "表达式不能为空，请输入。");
                pr.setPartitionVal(item.getPartitionVal());
                pr.setIsExecute(StringUtils.isBlank(item.getRangeIsExecute()) ? "0" : item.getRangeIsExecute());
                pr.setCompareType(item.getCompareType());
                pr.setCode(item.getRangeCode());
                pr.setType("PartitionRange");
                pr.setOwner(classiferPartition);
                pr.setOwnerId(partition.getPartitionId());
                pr.setPartition(classiferPartition);
                this.baseDao.save(pr);
            }
        }
    }

    /*删除分区range*/
    @Override
    public void deletePartitionRange(String partitionId, List<PartitionRangeVo> range) {
        String hql = "FROM PartitionRange WHERE partition_id=:partitionId";
        List<PartitionRange> list = this.baseDao.queryForList(hql, addParam("partitionId", partitionId).param());
        List<String> oldList = Lists.newArrayList();
        for (PartitionRange it : list) {
            oldList.add(it.getId());
        }
        List<String> newList = Lists.newArrayList();
        for (PartitionRangeVo item : range) {
            if (StringUtils.isNotBlank(item.getRangeId())) {
                newList.add(item.getRangeId());
            }
        }
        boolean isExist = oldList.removeAll(newList);
        if (isExist) {
            for (String i : oldList) {
                PartitionRange partitionRange = (PartitionRange) this.baseDao.get(PartitionRange.class, i);
                this.baseDao.delete(partitionRange);
            }
        }
    }

    @Override
    public List<String> findColumnInfomationByClassifierId(String id) {
        String hql = "FROM StructuralFeature WHERE OWNER_ID=:ownerId";
        List<StructuralFeature> columns = this.baseDao.queryForList(hql, addParam("ownerId", id).param());
        List<String> column = Lists.newArrayList();
        for (StructuralFeature col : columns) {
            if (col.getDataType() != null) {
                column.add(col.getCode() + "(" + col.getDataType().getCode() + ")");
            } else {
                column.add(col.getCode());
            }
        }
        return column;
    }

    @Override
    public List<String> findColumnInfomationByPartitionId(String id) {
        ClassiferPartition cp = (ClassiferPartition) this.baseDao.get(ClassiferPartition.class, id);
        String classifierId = cp.getClassifier().getId();
        String hql = "FROM StructuralFeature WHERE OWNER_ID=:ownerId";
        List<StructuralFeature> columns = this.baseDao.queryForList(hql, addParam("ownerId", classifierId).param());
        List<String> column = Lists.newArrayList();
        for (StructuralFeature col : columns) {
            if (col.getDataType() != null) {
                column.add(col.getCode() + "(" + col.getDataType().getCode() + ")");
            } else {
                column.add(col.getCode());
            }
        }
        return column;
    }

    @Override
    public Map<String, String> updateExecutePartition(String id, String classifierId, String str) {
        Map<String, String> map = Maps.newHashMap();
        try {
            ClassiferPartition partition = (ClassiferPartition) this.baseDao.get(ClassiferPartition.class, id);
            if ("0".equals(str)) {
                partition.setIsExecute(str);
                this.baseDao.update(partition);
                Set<PartitionRange> ranges = partition.getRanges();
                for (PartitionRange range : ranges) {
                    range.setIsExecute("0");
                    this.baseDao.update(range);
                }
            } else {
                updateCpAndPartitionRange(id, classifierId);
            }
            map.put("message", "success");
            return map;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            map.put("message", e.getMessage());
            return map;
        }
    }

    private void updateCpAndPartitionRange(String id, String classifierId) {
        String sql = "FROM ClassiferPartition WHERE classifier_id = :classifierId";
        List<ClassiferPartition> partitions = this.baseDao.queryForList(sql, addParam("classifierId", classifierId).param());

        for (ClassiferPartition cp : partitions) {
            Set<PartitionRange> ranges = cp.getRanges();
            if (cp.getId().equals(id)) {
                cp.setIsExecute("1");
                for (PartitionRange range : ranges) {
                    range.setIsExecute("1");
                    this.baseDao.update(range);
                }
                ClassifierStat cs = (ClassifierStat) this.baseDao.get(ClassifierStat.class, cp.getClassifier().getId());
                cs.setDefaultPhysical(cp.getPartitionType());
                this.baseDao.update(cs);
            } else {
                cp.setIsExecute("0");
                for (PartitionRange range : ranges) {
                    range.setIsExecute("0");
                    this.baseDao.update(range);
                }
            }
            this.baseDao.update(cp);
        }
    }

}
