/*==============================================================*/
/* Table: T_SERVICE_RUN_LOG                                     */
/*==============================================================*/
CREATE TABLE T_SERVICE_RUN_LOG (
                                   ID                   VARCHAR(32)          NOT NULL,
                                   SERVICE_ID           VARCHAR(32)          NULL,
                                   START_TIME           TIMESTAMP            NULL,
                                   END_TIME             TIMESTAMP            NULL,
                                   REQUEST_ID           VARCHAR(32)          NULL,
                                   DATA_COUNT           INT8                 NULL,
                                   CONSTRAINT PK_T_SERVICE_RUN_LOG PRIMARY KEY (ID)
);

COMMENT ON COLUMN T_SERVICE_RUN_LOG.ID IS
'唯一标识';

COMMENT ON COLUMN T_SERVICE_RUN_LOG.SERVICE_ID IS
'服务id';

COMMENT ON COLUMN T_SERVICE_RUN_LOG.START_TIME IS
'请求时间';

COMMENT ON COLUMN T_SERVICE_RUN_LOG.END_TIME IS
'结束时间';

COMMENT ON COLUMN T_SERVICE_RUN_LOG.REQUEST_ID IS
'凭证id';

COMMENT ON COLUMN T_SERVICE_RUN_LOG.DATA_COUNT IS
'返回的数据量';
