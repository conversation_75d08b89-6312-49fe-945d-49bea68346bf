package com.dragonsoft.cicada.datacenter.modules.supermarkmodel.vo;

import lombok.Data;

import java.util.List;

@Data
public class SupermarkModelVo {

    private String id;
    private String transId;
    private String transName;
    private String type;
    private String logo;
    private String version;
    private String introduction;
    private String userId;
    private String state; //上下架状态 0上架 1下架
    private String enabledState;

    private String objectTypeCode;
    private String objectTypeName;
    private String applicationTypeCode;
    private String applicationTypeName;
    private String policeTypeCode;
    private String policeTypeName;
    private String caseTypeCode;
    private String caseTypeName;
    private String areaTypeCode;
    private String areaTypeName;
    private String controlTypeCode;
    private String controlTypeName;
    private String hyflSjTypeCode;
    private String hyflSjTypeName;
    private String yylxSjTypeCode;
    private String yylxSjTypeName;
    private String operateType;

    private List<ModelLabelVo> labels;

    private List<ModelPictureVo> pics;

    private String focusType; //关注操作类型 0关注 1取消关注

    private Float score; //评分
    private String operateContent; //评价内容
    private String appraisedUserId; //被评价人员
    private String evaluationType; //评价类型

    private boolean addView;

    private UserEvaluationVo userEvaluationVo;

}
