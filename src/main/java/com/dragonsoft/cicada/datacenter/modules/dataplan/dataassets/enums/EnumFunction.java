package com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.enums;


import com.code.common.utils.StringUtils;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.vo.LogicDataSet.FunctionVo;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/8/6 9:30
 */
public enum EnumFunction {

    ADDITION("加法","+","1+1=2"),
    SUBTRACTION("减法","-","2-1=1"),
    MULTIPLICATION("乘法","*","2*2=4"),
    DIVISION("除法","/","4/2=2"),
    ABS("绝对值","abs(X)","abs(2.1)=2"),
    CBRT("立方根","cbrt(DP)","cbrt(27.0)=3"),
    IF("判断","case when sex = 1 then '男' else '女' end","case when sex = 1 then '男' else '女' end"),

    //聚合函数
    SUM("求和","sum([NUMBER])","sum(AGE)"),
    COUNT("计数","count([NUMBER])","count(NAME)"),
    COUNT_DISTINCT("去重计数","count(distinct([FIELD]))","count(distinct(NAME))"),
    AVG("平均值","avg([NUMBER])","avg(AGE)"),
    MIN("最小值","min([NUMBER])","min(AGE)"),
    MAX("最大值","max([NUMBER])","max(AGE)");

    private String name;
    private String use;
    private String example;
    EnumFunction(String name, String use, String example) {
        this.name = name;
        this.use = use;
        this.example = example;
    }

    public static List<FunctionVo> getFunctionByCondition(String condition) {
        List<FunctionVo> enumFunctions = Lists.newArrayList();
        for (EnumFunction value : EnumFunction.values()) {
            if (StringUtils.isBlank(condition) || value.name.equals(condition) || value.use.equals(condition) || value.example.equals(condition)) {
                FunctionVo functionVo = new FunctionVo();
                functionVo.setName(value.name);
                functionVo.setUse(value.use);
                functionVo.setExample(value.example);
                enumFunctions.add(functionVo);

            }
        }
        return enumFunctions;
    }
}
