package com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.adapter;

import java.util.ArrayList;
import java.util.List;

public abstract class AbstractTypeAdapter<Adapter> implements TypesAdapter<Adapter>{

    List<Adapter> types = new ArrayList<>();

    @Override
    public boolean support(Adapter type) {
        return types.contains(String.valueOf(type).toUpperCase()) ? true : false;
    }

}
