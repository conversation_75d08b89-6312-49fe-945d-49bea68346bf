package com.dragonsoft.cicada.datacenter.modules.datavisual.dashboard;

import com.code.metadata.datavisual.DashboardGroup;

import java.util.List;

public interface IDashboardGroupBuilder {
    List<DashboardGroup> builderGroupNoMy(String userId);

    List<DashboardGroup> builderGroup(String userId);

    List<DashboardGroup> builderGroupByShare(String userId);

    DashboardGroup addOrUpdateGroup(String parentId, String name, String id, String userId);

    boolean checkName(String name,String id,String userId);

    void  deleteGroup(String id);

    List<DashboardGroup> builderGroupStandard();
    List<DashboardGroup> builderGroupShare(String userId);

    List<DashboardGroup> buliderGroupCase(String userId);

    /**
     *
     * @param userId
     * @return
     */
    List<DashboardGroup> builderPersonalGroup(String userId);
}
