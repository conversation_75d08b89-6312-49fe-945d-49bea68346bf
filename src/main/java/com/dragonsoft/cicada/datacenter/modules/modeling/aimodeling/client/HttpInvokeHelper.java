package com.dragonsoft.cicada.datacenter.modules.modeling.aimodeling.client;

import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import org.apache.http.HttpEntity;
import org.apache.http.HttpStatus;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.io.Serializable;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description：http接口调用工具类
 * @date ：2021/10/9 11:23
 */
public class HttpInvokeHelper {

    public HttpInvokeHelper() {
        this.config = RequestConfig
                .custom()
                .setConnectTimeout(this.timeOut)
                .setSocketTimeout(this.timeOut)
                .build();
    }

    public String postInvoke(String url, Map<String,String> data){
        HttpPost httpPost = new HttpPost(url);
        List<NameValuePair> list = new ArrayList();
        if(data != null) {
            for (Map.Entry<String, String> entry : data.entrySet()) {
                list.add(new BasicNameValuePair(entry.getKey(), entry.getValue()));
            }
            UrlEncodedFormEntity entity = new UrlEncodedFormEntity(list, Charset.forName("UTF-8"));
            httpPost.setEntity(entity);
        }
        CloseableHttpClient httpClient = HttpClientBuilder
                .create()
                .setDefaultRequestConfig(this.config)
                .build();
        CloseableHttpResponse httpResponse = null;
        try{
            httpResponse = httpClient.execute(httpPost);
            int statusCode = httpResponse.getStatusLine().getStatusCode();
            if(statusCode != HttpStatus.SC_OK){
                throw new RuntimeException(
                        "调用http接口发生异常，接口url:" + url
                                + ", 异常信息:" + httpResponse.getStatusLine().getReasonPhrase()
                );
            }
            HttpEntity responseEntity = httpResponse.getEntity();
            if(responseEntity == null){
                throw new RuntimeException("http接口未返回结果！");
            }
            String rstJson = EntityUtils.toString(responseEntity, "UTF-8");
          /*  if (url.contains("run_op")){
                return rstJson;
            }*/
            try {
                ResponseMsg rsp = JSONObject.parseObject(rstJson, ResponseMsg.class);
                if(!rsp.getSuccess()){
                    throw new RuntimeException("接口调用失败，返回异常信息:" + rsp.getMsg());
                }
                return rsp.getMsg();
            }catch (JSONException e){
                return rstJson;
            }
        }catch (IOException e) {
            System.out.println("调用http接口发生异常:" + e.getMessage());
            throw new RuntimeException(e);
        }finally {
            closeHttp(httpClient, httpResponse);
        }

    }


    private void closeHttp(CloseableHttpClient httpClient, CloseableHttpResponse httpResponse){
        try {
            if (httpResponse != null) {
                httpResponse.close();
            }
            if (httpClient != null) {
                httpClient.close();
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }


    /**
     * 超时时间
     */
    private int timeOut = 50000;
    private RequestConfig config;

    public int getTimeOut() {
        return timeOut;
    }

    public void setTimeOut(int timeOut) {
        this.timeOut = timeOut;
    }

    public RequestConfig getConfig() {
        return config;
    }

    public void setConfig(RequestConfig config) {
        this.config = config;
    }


    /**
     * 响应信息
     */
    public static class ResponseMsg implements Serializable {

        public ResponseMsg() {
        }

        public ResponseMsg(boolean success, String msg) {
            this.success = success;
            this.msg = msg;
        }

        /**
         * 是否成功
         */
        private boolean success;
        /**
         * 消息
         */
        private String msg;

        public boolean getSuccess() {
            return success;
        }

        public void setSuccess(boolean success) {
            this.success = success;
        }

        public String getMsg() {
            return msg;
        }

        public void setMsg(String msg) {
            this.msg = msg;
        }
    }
}
