package com.dragonsoft.cicada.datacenter.modules.datavisual.datapolishing.datapolishings;

import com.code.common.utils.assertion.Assert;

/**
 * <AUTHOR>
 * @Date 2021/3/16 10:05
 */
public class WeekDataPolishing extends AbsDataPolishing {

    private final String WEEK_FORMAT = "yyyyww";
    private final String WEEK = "周";

    @Override
    public void calculateDate() {

        if (minDate.length() != 6) {
            granularituFail(WEEK, WEEK_FORMAT, minDate);
        }
        if (maxDate.length() != 6) {
            granularituFail(WEEK, WEEK_FORMAT, maxDate);
        }

        //计算最小时间那年剩下的周
        String minYear = minDate.substring(0, 4);
        String minWeek = minDate.substring(4);

        String maxYear = maxDate.substring(0, 4);
        String maxWeek = maxDate.substring(4);
        Integer minMonthInt = parseInt(minWeek, WEEK_FORMAT);
        for (Integer i = minMonthInt; i <= 53; i++) {
            addFullDate(minYear, i);
        }
        //计算剩下的年的月
        int middleMinYearInt = parseInt(minYear, WEEK_FORMAT) + 1;
        int middleMaxYearInt = parseInt(maxYear, WEEK_FORMAT) - 1;

        for (int i = 0; i < (middleMaxYearInt - middleMinYearInt) + 1; i++) {
            for (int j = 1; j <= 53; j++) {
                String suffixMonth = String.valueOf(j);
                if (j < 10) {
                    suffixMonth = "0" + j;
                }
                dataPolishingList.add((middleMinYearInt + i) + suffixMonth);
            }
        }
        //计算最大时间那年前几个月
        Integer maxMonthInt = parseInt(maxWeek, WEEK_FORMAT);
        for (Integer i = 1; i <= maxMonthInt; i++) {
            addFullDate(maxYear, i);
        }
    }

    @Override
    public void checkData(String data) {
        int temData = parseInt(data, WEEK_FORMAT);
        int weekInt = temData % 100;
        if (weekInt > 53 || weekInt == 0) {
            Assert.fail(String.format("周数最大为53周，最小为1周！"));
        }
    }

    private void addFullDate(String year, Integer i) {
        String suffixMonth = String.valueOf(i);
        if (i < 10) {
            suffixMonth = "0" + i;
        }
        dataPolishingList.add(year + suffixMonth);
    }
}
