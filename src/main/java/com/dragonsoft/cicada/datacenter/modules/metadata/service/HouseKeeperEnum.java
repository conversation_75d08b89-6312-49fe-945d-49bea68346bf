package com.dragonsoft.cicada.datacenter.modules.metadata.service;

import java.util.HashMap;
import java.util.Map;

public enum HouseKeeperEnum {
    MYSQL("MYSQL", "MYSQL", "Rdb","Mysql"),
    ELASTICSEARCH("ELA<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>","Es","Elasticsearch"),
    GREENPLUM("GREENPLUM","GREENPLUM", "Rdb","Greenplum"),
    ORACLE("OR<PERSON>LE","OR<PERSON>LE","Rdb","Oracle"),
    POSTGRESQL("POSTGRESQL","POSTGRESQL","Rdb","Postgresql"),
    HWMPP("LibrA", "HWMP<PERSON>","Rdb","Libra"),
    HIVE("Hive","Hive","Rdb","Hive");


    private String name;
    private String value;
    private String pre;
    private String label;

    public String getName() {
        return name;
    }

    public String getValue() {
        return value;
    }

    public String getPre() {
        return pre;
    }

    public String getLabel() {
        return label;
    }

    HouseKeeperEnum(String name, String value, String pre, String label) {
        this.name = name;
        this.value = value;
        this.pre = pre;
        this.label = label;
    }

    public static HouseKeeperEnum getPreByType(String type) {
        return HouseKeeperEnum.valueOf(type.toUpperCase());
    }

    public static Map<String, String> getAllTypes() {
        Map<String, String> types = new HashMap<>(10);
        for (HouseKeeperEnum houseKeeperEnum : HouseKeeperEnum.values()) {
            types.put(houseKeeperEnum.getLabel(), houseKeeperEnum.getValue());
        }
        return types;
    }
}
