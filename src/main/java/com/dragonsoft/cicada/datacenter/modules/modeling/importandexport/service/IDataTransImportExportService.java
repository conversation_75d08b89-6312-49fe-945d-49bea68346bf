package com.dragonsoft.cicada.datacenter.modules.modeling.importandexport.service;

import com.code.metadata.business.directory.BaseBusiClassify;
import com.code.metadata.business.directory.BaseBusiDir;
import com.code.metadata.datawarehouse.DwDbInstance;
import com.code.metadata.etl.trans.TransMeta;
import com.code.metadata.res.structured.rdb.RdbCatalog;
import com.code.metadata.res.structured.rdb.RdbSchema;
import com.code.metaservice.ddl.vo.LogicDataObjInfo;
import com.dragonsoft.cicada.datacenter.modules.modeling.importandexport.controller.DataTransImportExportController;
import com.dragonsoft.cicada.datacenter.modules.modeling.importandexport.service.impl.DataTransImportExportServiceImpl;
import com.dragonsoft.cicada.datacenter.modules.modeling.importandexport.vo.ExportModelVo;
import com.dragonsoft.cicada.datacenter.modules.modeling.vo.DataExportVo;
import com.fw.service.IService;

import javax.servlet.http.HttpServletRequest;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date: 2022/07/13/下午2:03
 */
public interface IDataTransImportExportService extends IService {
    /**
     * get方法查询对象
     * @param catalogId
     * @return
     */
    RdbCatalog getRabCatalog(String catalogId);

    List<Map<String, Object>> getRabCatalogSql(String catalogId);

    /**
     * get方法查询对象
     * @param instancdId
     * @return
     */
    DwDbInstance getDbInstanceById(String instancdId);

    List<Map<String, Object>> getDbInstanceByIdSql(String instancdId);

    BaseBusiDir getBaseBusiDirById(String dirId);

    BaseBusiClassify getBaseBusiClassifiy(String instanceId);

    DataTransImportExportController.ClassifyElement getClassifyElement(String classifyId);

    TransMeta getTransMetaById(String transId);

    DataTransImportExportController.DwDbMapping getDwDbMappingbyInstanceId(String instanceId);

    RdbSchema getRdbSchema(String schemaId);

    List<Map<String, Object>> getRdbSchemaSql(String schemaId);

    List getLabelElement(String subjectId);

    List<Map<String, Object>> getRdbCatalogCluster(String catalogId);

    List<Map<String, Object>> getMachine(String machideId);

    List<String> getTransMetaListByClassifyId(String classifyId, String userId);

    DataTransImportExportController.DataCenterTransExportBean exportModel(List<String> transIds, List<String> visualIds, List<String> logicIds, List<String> portalIds, String dbType);

    List<String> getDatasourceIdByClassifyId(String classifyId);

    List<DataTransImportExportController.DataSourceBean> buildDataSourceBean(String datasourceId, String userId);

    DataTransImportExportController.LogicObjBean exportCustomDataset(String classifyId, String userId);


    List<BaseBusiClassify> findFatherAndSonClassify(String classifyId);

    void checkExportIsOk(String transDirId, String datasourceDirId, String datasetDirId, String userId);


    List<Map<String, Object>> listTransInfo(String dirId, String userId);


    void deleteDataByClassifyId(DataExportVo dataExportVo);

    /**
     * 同步导入后的用户信息
     *
     * @param
     * @param newUserId
     */
    void updateUserInfo(String newUserId,List<String> elementUserIds,List<String> stepUserIds);

    /**
     * 通过方案id删除调度信息
     *
     * @param transId 方案id
     */
    void deleteScheduleByTransId(String transId);


    /**
     * 根据当前目录 拿到对应的根目录
     *
     * @param classifyId
     * @return
     */
    BaseBusiDir getBaseBusiDirByClassify(String classifyId);



    /**
     * 目录树是否存在
     *
     * @param classifyId
     * @return
     */
    boolean isExistClassify(String classifyId);

    /**
     * 通过名称,用户，code查找目录节点
     * @param name
     * @param userId
     * @param code
     * @return
     */
    BaseBusiClassify findBaseClassifyByNameAndUserId(String name, String userId, String code);
    /**
     * 通过名称,用户，code查找目录根节点
     * @param name
     * @param userId
     * @param code
     * @return
     */
    BaseBusiDir findBaseDirByNameAndUserId(String name, String userId, String code);

    /**
     * 保存数据集
     * @param dataObjInfos
     */
    void saveLogicInfo(List<LogicDataObjInfo> dataObjInfos);


    /**
     * 通过logicId查询出要导出得LogicBean对象，里面会有多个数据集信息
     * 获取logicBean对象
     * @param logicIds
     * 跟getDataSourceAndLogic这个方法一起使用
     * @return
     */
    DataTransImportExportController.LogicObjBean exportLogicBeanById(List<String> logicIds);



    /**
     * 通过方案id查询数据源id和数据集相关id
     * @param transId
     * @param userId
     */
    DataTransImportExportServiceImpl.TransDatasetAndDatasource getDataSourceAndLogic(List<String> transId, String userId);

    /**
     * 通过数据源id获取数据源对象，导出对象
     * 配置getDataSourceAndLogic方法使用
     * @param dwInstanceIds
     * @param userId
     * @return
     */
    List<DataTransImportExportController.DataSourceBean> buildDataSourceBeanByInstanceIds(List<String> dwInstanceIds, String userId);
    /**
     * 通过rdbId集合获取要导出的Rdb对像
     * @param rdbIds
     * @param userId
     * @return
     */
    DataTransImportExportController.DataCenterRdbBean getRdbBeanByRdbIds(List<String> rdbIds,String userId);


    /**
     * 通过数据集id集合获取RdbId集合,通过transId获取输出插件的logic
     * @param logicIds
     * @return
     */
    List<Map> getRdbIdsByLogicIds(Collection<String> logicIds);

    public Set<String> queryLogicBySelf(List<String> logicIds);
    /**
     * 通过方案id查询目录树，方案目录树
     * @param transId
     */
    List<BaseBusiClassify> queryTransClassifyIdByTransId(List<String> transId);



    /**
     * 通过数据集id查询目录树
     * @param logicIds
     * @param userId
     */
    List<BaseBusiClassify> queryLogicClassifyIdByLogicId(List<String> logicIds,String userId);



    /**
     * 通过数据源id查询目录树，我的空间
     * @param dwInstanceIds
     * @param userId
     */
    List<BaseBusiClassify> queryMyHouseClassifyIdByDwInstancesId(List<String> dwInstanceIds,String userId);


    /**
     * 通过方案id查询目录树，数据仓库
     * @param dwInstanceIds
     * @param userId
     */
    List<BaseBusiClassify> queryDataWarehouseClassifyIdByDwInstancesId(List<String> dwInstanceIds,String userId);


    /**
     * 数据中心导入时删除方案，数据集，rdb表
     * @param transIds
     * @param logicIds
     * @param rdbIds
     */
    void deleteImportDataResource(Set<String> transIds, Set<String> logicIds, Set<String> rdbIds);

    List<String> queryServiceIdsByTransIds(List<String> transIds);


    /**
     * 查询多个服务关联的数据信息
     * @param serviceIds 服务id
     * @param queryTableName 要查询的表名
     * @param queryKey 查询的关键字
     * @return
     */
    List<Map<String,Object>> queryServiceApiByServiceIds(List<String> serviceIds,String queryTableName,String queryKey);


    List<String> queryServiceIdsByCaseIds(List<String> caseIds);

    /**
     * 通过服务id查询到目录树
     * @param serviceIds 服务ids
     * @return
     */
    List<BaseBusiClassify> queryServiceClassifyIdByServiceIds(List<String> serviceIds);


    /**
     * 通过服务ids找到资源id
     * @param serviceIds 服务id
     * @param  方案，数据集
     * @return
     */
    List<Map> queryResourceIdsServiceIds(List<String> serviceIds,String userId);


    Set<String> queryDwInstanceIds(List<String> logicIds);

    List<Map> queryServiceElement(List<String> classifyIds);

    List<Map> queryUseCaseByElement(List<String> elementIds);

    List<Map<String,Object>> queryTransVariableRelation(List<String> transIds);

    List<Map<String,Object>> queryTransVariable(List<String> transIds);

    void deleteVariable(List<String> variableIds);

    void deleteVariableRelations(List<String> variableRelationIds);

    List<String> querySchemaIdByDwInstanceIds(List<String> dwInstanceIds);

    /**
     * 保存导出日志
     * @param request
     * @param exportModelVo
     */
    void saveDataImportExportLog(HttpServletRequest request, ExportModelVo exportModelVo);
}
