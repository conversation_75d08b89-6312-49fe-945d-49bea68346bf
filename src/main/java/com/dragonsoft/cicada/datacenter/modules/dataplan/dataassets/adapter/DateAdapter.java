package com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.adapter;

import java.util.Arrays;

public class DateAdapter extends AbstractTypeAdapter<String> {

    {
        types.add("DATE");
        types.add("TIMESTAMP");
        types.add("TIME");
        String[] STRINGS_TYPE = {"BOOL","BOOLEAR","VARCHAR", "CHAR", "TEXT", "VARCHAR2", "NVARCHAR2", "NCHAR", "CHARACTER", "CHARACTER VARYING", "NCHAR", "NVARCHAR", "TEXT", "STRING", "TEXT", "KEYWORD", "STRINGTYPE"};
        types.addAll(Arrays.asList(STRINGS_TYPE));
    }

    @Override
    public String handler() {
        return "Date";
    }

    @Override
    public String getHanlderResult() {
        return "日期型";
    }
}
