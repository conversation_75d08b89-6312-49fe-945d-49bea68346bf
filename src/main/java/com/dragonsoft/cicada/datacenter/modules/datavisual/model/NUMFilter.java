package com.dragonsoft.cicada.datacenter.modules.datavisual.model;


import com.code.common.utils.StringUtils;
import com.code.meta.dml.standard.IMultCdin;
import com.code.meta.dml.standard.cdins.IQueryCdin;
import com.code.meta.dml.standard.cdins.MultCdin;
import com.code.meta.dml.standard.cdins.QueryCdins;

import java.text.NumberFormat;

public class NUMFilter extends AbsWidgetFilter {
    Condition leftV;
    Condition rightV;




    @Override
    public IMultCdin builderCondition(QueryCdins queryCdins) {
        IMultCdin cdin = new MultCdin();


        if(null != leftV && leftV.value!=null){
            String fieldCode = StringUtils.isBlank(this.field.getFieldAlias()) ? this.field.getCode() : this.field.getFieldAlias();
            cdin.addCdin(leftV.getIQueryCdin(fieldCode,queryCdins,this.type));
        }
        if(null != rightV && rightV.value!=null){
            String fieldCode = StringUtils.isBlank(this.field.getFieldAlias()) ? this.field.getCode() : this.field.getFieldAlias();
            cdin.addCdin(rightV.getIQueryCdin(fieldCode,queryCdins,this.type));
        }
        return cdin;
    }


    class Condition {
        String logical;
        Double value;

        public String getLogical() {
            return logical;
        }

        public void setLogical(String logical) {
            this.logical = logical;
        }

        public Double getValue() {
            return value;
        }

        public void setValue(Double value) {
            this.value = value;
        }

        IQueryCdin getIQueryCdin(String filed, QueryCdins queryCdins,String type) {

            if (null == value) {
                return null;
            }

            Object newValue ;
            if("text".equals(type)){
                NumberFormat nf = NumberFormat.getInstance();
                nf.setGroupingUsed(false);
                newValue = nf.format(value);
            }else {
                newValue = value;
            }

            if ("=".equals(logical)) {
                return queryCdins.eq(filed, newValue);
            } else if ("<".equals(logical)) {
                return queryCdins.lt(filed, newValue);
            } else if ("<=".equals(logical)) {
                return queryCdins.le(filed, newValue);
            } else if ("!=".equals(logical)) {
                return queryCdins.ne(filed, newValue);
            } else if (">".equals(logical)) {
                return queryCdins.gt(filed, newValue);
            } else if (">=".equals(logical)) {
                return queryCdins.ge(filed, newValue);
            }
            return null;
        }
    }

    public Condition getLeftV() {
        return leftV;
    }

    public void setLeftV(Condition leftV) {
        this.leftV = leftV;
    }

    public Condition getRightV() {
        return rightV;
    }

    public void setRightV(Condition rightV) {
        this.rightV = rightV;
    }
}
