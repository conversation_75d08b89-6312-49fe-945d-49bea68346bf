package com.dragonsoft.cicada.datacenter.modules.firstpage.scenariocase.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "用于将模型等转换为场景案例")
public class ModelCaseVo {

    @ApiModelProperty(value = "模型、仪表盘、门户的方案ID")
    private String modelId;
    @ApiModelProperty(value = "场景案例父类型 数据碰撞 或 计数统计")
    private String parentCaseType;
    @ApiModelProperty(value = "方案类型 为：数据模型、可视化仪表盘、主题门户")
    private String caseType;
    @ApiModelProperty(value = "描述")
    private String desc;
}
