package com.dragonsoft.cicada.datacenter.modules.system.dataportal.controller;

import com.dragoninfo.dfw.bean.Result;
import com.dragonsoft.cicada.datacenter.modules.system.common.vo.TreeVo;
import com.dragonsoft.cicada.datacenter.modules.system.dataportal.service.IDataMenuConfigService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/9/2
 */
@RestController
@CrossOrigin
@RequestMapping("/menuConfig")
@Api(value = "MenuConfigControl|门户菜单配置")
public class MenuConfigController {

    @Autowired
    private IDataMenuConfigService dataMenuConfigService;

    @ResponseBody
    @RequestMapping("/queryTree")
    public Result queryTree(String portalId, HttpServletRequest request) {
        String userId = (String) request.getSession().getAttribute("userId");
        List<TreeVo> menuAuthTrees = dataMenuConfigService.getAuthMenuTree(portalId, userId);
        return Result.success(menuAuthTrees);
    }
}
