package com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.service.impl;

import com.code.common.mist.service.structure.model.*;
import com.code.metadata.sm.EnumServiceType;
import com.code.metaservice.ddl.ILogicDataObjService;
import com.dragonsoft.cicada.datacenter.common.utils.ConvertToPinyinUtils;
import com.dragonsoft.cicada.datacenter.modules.dataplan.externaldatasources.service.IDataFilterService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.externaldatasources.service.IDataWarehousePlanService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.service.DynamicStructureAiService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.vo.ServicePublicationVo;
import com.fw.service.annotation.Service;
import com.google.common.base.Preconditions;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;


@Service
public class DynamicStructureAiServiceImpl implements DynamicStructureAiService {


    private static final String BASICPATH = "com.code.common.sm";
    private static final String SUPERCLASS = "com.code.common.mist.service.BaseSchemeService";

    private static final String IMPL = "Impl";

    private static final String schemeId = "9d06ecc648354bb6bdbc28854bb07721";

    private static final String ABSTRCATREFERENCE = "com.code.common.mist.service.model.SchemeServiceRequestModel";
    private static final String OUTPUTTYPE = "com.code.common.mist.service.model.SchemeExecutorResult";

    @Autowired
    private IDataWarehousePlanService dataWarehouseService;
    @Autowired
    private IDataFilterService dataFilterService;

    @Autowired
    private ILogicDataObjService logicDataObjService;

    @Override
    public ServiceClassMeta dynamicCreateAiClassMetaByVo(ServicePublicationVo vo) {
        EnumServiceType serviceType = EnumServiceType.getInstanceByCode(vo.getServiceType());
        ServiceClassMeta serviceClassMeta = new ServiceClassMeta();
        String interfaceName = ConvertToPinyinUtils.convertChineseName(vo.getApiName());
        ClassMeta interfaceMeta = buildInterface(vo.getVersion(), interfaceName, vo);
        ClassMeta classMeta = buildClass(vo.getVersion(), interfaceName, interfaceName + "Impl",vo);
        serviceClassMeta.setInterfaceMeta(interfaceMeta);
        serviceClassMeta.setImplMeta(classMeta);
        serviceClassMeta.setServiceType(serviceType);
        serviceClassMeta.setSourceId(vo.getSourceId());
        serviceClassMeta.setMemo(vo.getMemo());
        return serviceClassMeta;
    }

    protected ClassMeta buildClass(Integer version, String interfaceName, String publishImplName,ServicePublicationVo vo) {
        MethodMeta formParamJsonReturn = buildTestMethod(vo)
                .access(AccessFlag.PUBLIC)
                .addAnnotation(new AnnotationMeta("java.lang.Override"))
                .isPublish(true)
                .body("{" +
                        "System.out.println($1);" +
                        "return this.action($1);" +
                        "}")
                .build();

        return ClassMeta.builder()
                .packageName(BASICPATH)
                .name(publishImplName)
                .version(version)
                .addConstructor(buildConstructorMeta(schemeId))
                .memo(vo.getMemo())
                .urlPath("/test")
                .promulgator(EnumPromulgator.DATA_CENTER)
                .interfaces(new String[]{BASICPATH + "." + interfaceName, "java.io.Serializable"})
                .superClass(SUPERCLASS)
                .addMethod(formParamJsonReturn)
                .build();
    }

    protected ClassMeta buildInterface(Integer version, String publishInterfaceName,ServicePublicationVo vo) {
        ClassMeta classMeta = ClassMeta.builder()
                .packageName("com.code.common.sm")
                .name(publishInterfaceName)
                .isInterface(true)
                .version(version)
                .access(AccessFlag.PUBLIC)
                .memo(vo.getApiName())
                .build();
        MethodMeta methodMeta = buildTestMethod(vo).isPublish(true).build();
        classMeta.addMethod(methodMeta);
        return classMeta;
    }

    protected ConstructorMeta buildConstructorMeta(String schemeId) {
        ConstructorMeta.Builder builder = new ConstructorMeta.Builder();
        builder.access(AccessFlag.PUBLIC)
                .parameters(Collections.emptyMap())
                .body("{" +
                        "schemeId = \"" + schemeId + "\";" +
                        "}");
        return builder.build();
    }

    protected MethodMeta.Builder buildTestMethod(ServicePublicationVo vo) {
       /* String param1Type = "com.code.common.mist.service.model.SchemeServiceRequestModel";
        String returnType = "com.code.common.mist.service.model.SchemeExecutorResult";*/
        ParameterBuilder parameterBuilderInput = ParameterBuilder.aParameter()
                .code("op_input").name("op_input").memo("op_input").dataType(ABSTRCATREFERENCE);
        ParameterBuilder parameterBuilderOutput = ParameterBuilder.aParameter()
                .code("result").name("result").memo("result").dataType(OUTPUTTYPE);
        MethodMeta.Builder builder = MethodMeta.builder()
                .isPublish(true)
                .name("testMethod")
                .urlPath("/testMethod")
                .returnType(OUTPUTTYPE)
                .parameterConsumerTypeEnum(MethodMeta.ParameterRequestTypeEnum.JSON)
                .returnProduceTypeEnum(MethodMeta.ParameterRequestTypeEnum.JSON)
                .ruleEngineAcceptTypes(new String[]{String.class.getName(), Long.class.getName()})
                .addParameter(0, buildChildren(vo.getRequestParams(),parameterBuilderInput)
                        .annotations(new ArrayList<>())
                        .buildInputParameter()
                )
                .requestType(RequestType.POST)
                .returnParams(Arrays.asList(
                        buildChildren(vo.getResponseParams(),parameterBuilderOutput)
                                .buildReturnParam()
                ));
        /*List<ServicePublicationVo.Params> requestParams = vo.getRequestParams();
        int i = 0;
        for (ServicePublicationVo.Params requestParam : requestParams) {
            builder.addParameter(++i,ParameterBuilder.aParameter()
                    .code(requestParam.getParamName())
                    .name(requestParam.getParamName())
                    .memo(requestParam.getParamMemo())
                    .dataType(changeTypePath(requestParam.getParamType())).buildInputParameter())
                ;
        }*/
        return builder;
    }

    private ParameterBuilder buildChildren(List<ServicePublicationVo.Params> params,ParameterBuilder parameterBuilder){
        for (ServicePublicationVo.Params param : params) {
            ParameterBuilder builder = ParameterBuilder.aParameter()
                    .code(param.getParamName())
                    .name(param.getParamName())
                    .memo(param.getParamMemo())
                    .dataType(param.getParamType())
                    .desenType(param.getDesensitization())
                    .isMust(param.getIsMust() ? "t":"f")
                    .example(param.getExampleValue());
            if (param.getChildren().size() > 0){
                builder.addChildren(buildChildren(param.getChildren(),builder));
            }
            parameterBuilder.addChildren(builder);
        }
        return  parameterBuilder;
    }

    /**
     * 转换成输出的类型全路径
     *
     * @param type 类型
     * @return 算法组说目前只会有double 和 String 所以先已这两个适配...
     */
    public String changeTypePath(String type) {
        Preconditions.checkArgument("String".equals(type) || "Double".equals(type) || "DOUBLE".equals(type)
                || "STRING".equals(type) || "Object".equals(type) || "OBJECT".equals(type)
                || "Long".equals(type) || "LONG".equals(type)
                || "FLOAT".equals(type) || "Float".equals(type)
                || "INTEGER".equals(type) || "Integer".equals(type) || "Int".equalsIgnoreCase(type), "出参类型无法对应标准");
        switch (type) {
            case "Object":
                return Object.class.getName();
            case "String":
            case "STRING":
                return String.class.getName();
            case "LONG":
            case "Long":
                return Long.class.getName();
            case "FLOAT":
            case "Float":
                return Float.class.getName();
            case "INTEGER":
            case "Integer":
            case "Int":
                return Integer.class.getName();
            default:
                return Double.class.getName();
        }

    }
}
