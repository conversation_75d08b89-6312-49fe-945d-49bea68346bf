package com.dragonsoft.cicada.datacenter.modules.system.busmanagement.service.Impl;


import com.code.common.paging.PageInfo;
import com.code.common.utils.assertion.Assert;
import com.code.metadata.bus.management.*;
import com.code.metaservice.bus.management.*;
import com.dragonsoft.cicada.datacenter.modules.system.busmanagement.service.IServiceInfoManagementService;
import com.dragonsoft.cicada.datacenter.modules.system.busmanagement.vo.BodyParameterCfgVo;
import com.dragonsoft.cicada.datacenter.modules.system.busmanagement.vo.InterfaceCfgVo;
import com.dragonsoft.cicada.datacenter.modules.system.busmanagement.vo.ServiceConfigVo;
import com.dragonsoft.cicada.datacenter.modules.system.busmanagement.vo.ServiceInfoVo;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Sets;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/03/15
 */
@Service
public class ServiceInfoManagementServiceImpl implements IServiceInfoManagementService {

    @Autowired
    private IBusInfoService busInfoService;

    @Autowired
    private IServiceInfoService serviceInfoService;

    @Autowired
    private IServiceConfigService serviceConfigService;

    @Autowired
    private IHttpConfigRelationService httpConfigRelationService;

    @Autowired
    private IBodyAttributeService bodyAttributeService;

    @Autowired
    private IServiceTypeService serviceTypeService;

    @Autowired
    private IHeaderAttributeService headerAttributeService;

    @Override
    public PageInfo getServiceInfoPage(PageInfo pageInfo, String userId, String keyWord) {
        PageInfo pageInfo1 = serviceInfoService.queryServiceInfoPage(pageInfo, userId, keyWord);
        List<ServiceInfo> dataList = pageInfo1.getDataList();
        if (CollectionUtils.isNotEmpty(dataList)) {
            List<Map<String, Object>> serviceInfoList = Lists.newArrayList();
            for (ServiceInfo serviceInfo : dataList) {
                Map<String, Object> map = new HashMap<>();
                map.put("serviceInfo", buildServiceInfoVo(serviceInfo));
                map.put("interfaceInfo", getInterfaceInfoListBySId(serviceInfo.getId()));
                serviceInfoList.add(map);
            }
            pageInfo1.setDataList(serviceInfoList);
        }
        return pageInfo1;
    }

    @Override
    public List<InterfaceCfgVo> getInterfaceInfoListBySId(String serviceId) {
        List<InterfaceCfgVo> interfaceCfgVos = Lists.newArrayList();
        List<HttpConfigRelation> httpConfigRelations = httpConfigRelationService.queryConfigRelBySId(serviceId);
        httpConfigRelations.forEach(httpConfigRelation -> interfaceCfgVos.add(toInterfaceCfgVo(httpConfigRelation)));
        return interfaceCfgVos;
    }


    @Override
    public InterfaceCfgVo getBodyAttributeByCfgId(String configId) {
        Assert.hasText(configId, "接口id不能为空");
        List<BodyParameterCfgVo> bodyParameterCfgVos = Lists.newArrayList();
        HttpConfigRelation httpConfigRelation = httpConfigRelationService.get(HttpConfigRelation.class, configId);

        InterfaceCfgVo interfaceCfgVo = toInterfaceCfgVo(httpConfigRelation);
        interfaceCfgVo.setServiceType(httpConfigRelation.getServiceType().getParentId());
        interfaceCfgVo.setInterfaceType(httpConfigRelation.getServiceType().getId());
        if (null != httpConfigRelation && CollectionUtils.isNotEmpty(httpConfigRelation.getBodyAttributes())) {
            buildBodyAttributeVo(httpConfigRelation.getBodyAttributes(), bodyParameterCfgVos);
        }
        interfaceCfgVo.setInterfaceParameterCfgVoList(bodyParameterCfgVos);
        return interfaceCfgVo;
    }

    @Override
    public List<ServiceInfoVo> getServiceInfoList(String userId) {
        List<ServiceInfo> serviceInfoList = serviceInfoService.queryServiceInfoList(userId);
        List<ServiceInfoVo> serviceInfoVos = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(serviceInfoList)) {
            serviceInfoList.forEach(serviceInfo -> {
                serviceInfoVos.add(buildServiceInfoVo(serviceInfo));
            });
        }
        return serviceInfoVos;
    }

    private InterfaceCfgVo toInterfaceCfgVo(HttpConfigRelation httpConfigRelation) {
        InterfaceCfgVo interfaceCfgVo = new InterfaceCfgVo();
        interfaceCfgVo.setId(httpConfigRelation.getId());
        interfaceCfgVo.setName(httpConfigRelation.getName());
        interfaceCfgVo.setServiceType(httpConfigRelation.getServiceType().getName());
        interfaceCfgVo.setCode(httpConfigRelation.getCode());
        return interfaceCfgVo;
    }

    @Override
    public ServiceInfoVo getServiceInfoById(String id) {
        Assert.notNull(id, "查询的服务id不能为空！");
        ServiceInfo serviceInfo = serviceInfoService.get(ServiceInfo.class, id);
        ServiceInfoVo serviceInfoVo = buildServiceInfoVo(serviceInfo);
        Iterator<ServiceConfig> iterator = serviceInfo.getServiceConfigs().iterator();
        if (!iterator.hasNext()) throw new RuntimeException("为查询到请求头配置!");
        buildHeaderCfgVo(serviceInfoVo, iterator.next());
        return serviceInfoVo;
    }

    private ServiceInfoVo buildServiceInfoVo(ServiceInfo serviceInfo) {
        ServiceInfoVo serviceInfoVo = new ServiceInfoVo();
        serviceInfoVo.setId(serviceInfo.getId());
        serviceInfoVo.setName(serviceInfo.getName());
        serviceInfoVo.setBusId(serviceInfo.getBusInfoId());
        serviceInfoVo.setServiceId(serviceInfoService.queryServiceId(serviceInfo.getId()));
        return serviceInfoVo;
    }

    private void buildHeaderCfgVo(ServiceInfoVo serviceInfoVo, ServiceConfig serviceConfig) {
        Set<HeaderAttribute> headerAttributes = serviceConfig.getHeaderAttributes();
        List<ServiceConfigVo> requestHeaderCfgList = headerAttributes.stream()
                .filter(headerAttribute -> headerAttribute.getConfigType().equals(ServiceConfigVo.REQUEST_TYPE))
                .map(this::toServiceCfgVo).collect(Collectors.toList());
        List<ServiceConfigVo> responseHeaderCfgList = headerAttributes.stream()
                .filter(headerAttribute -> headerAttribute.getConfigType().equals(ServiceConfigVo.RESPONSE_TYPE))
                .map(this::toServiceCfgVo).collect(Collectors.toList());
        serviceInfoVo.setRequestHeaderCfgList(requestHeaderCfgList);
        serviceInfoVo.setResponseHeaderCfgList(responseHeaderCfgList);
    }

    private ServiceConfigVo toServiceCfgVo(HeaderAttribute headerAttribute) {
        ServiceConfigVo serviceConfigVo = new ServiceConfigVo();
        serviceConfigVo.setName(headerAttribute.getName());
        serviceConfigVo.setConfigType(headerAttribute.getConfigType());
        serviceConfigVo.setCode(headerAttribute.getCode());
        serviceConfigVo.setDefaultValue(headerAttribute.getDefaultValue());
        serviceConfigVo.setIsRequired(headerAttribute.getIsRequired());
        serviceConfigVo.setIsDelete(ServiceConfigVo.judgeIsDelete(headerAttribute.getCode(), headerAttribute.getConfigType()));
        serviceConfigVo.setColumnType(headerAttribute.getType());
        serviceConfigVo.setId(headerAttribute.getId());
        return serviceConfigVo;
    }

    @Override
    public String tSaveServiceInfo(ServiceInfoVo serviceInfoVo, String userId) {
        ServiceInfo serviceInfo = new ServiceInfo();
        BusInfo busInfo = busInfoService.get(BusInfo.class, serviceInfoVo.getBusId());
        Set<ServiceConfig> serviceConfigList = Sets.newHashSet();

        Assert.notNull(busInfo, "该总线配置不存在");
        if (StringUtils.isNotEmpty(serviceInfoVo.getId())) {
            serviceInfo = serviceInfoService.get(ServiceInfo.class, serviceInfoVo.getId());
        }

        if (serviceInfoService.checkServiceInfoName(serviceInfoVo.getName(), serviceInfoVo.getId(), userId))
            Assert.fail("该服务名称已存在！");
        serviceInfo.setBusInfoId(busInfo.getId());

        serviceInfo.setName(serviceInfoVo.getName());
        serviceInfo.setCode(serviceInfoVo.getName());
        serviceInfo.setOperateUserId(userId);
        serviceInfo.setType(ServiceInfo.class.getName());
        tDeleteHeader(serviceInfo.getServiceConfigs());
        serviceInfoService.saveOrUpdate(serviceInfo);
        tBuildServiceCfg(serviceConfigList, serviceInfoVo, serviceInfo);
        serviceInfo.setServiceConfigs(serviceConfigList);
        serviceInfoService.saveOrUpdate(serviceInfo);
        return serviceInfo.getId();
    }


    private void tDeleteHeader(Set<ServiceConfig> serviceConfigList) {
        if (CollectionUtils.isNotEmpty(serviceConfigList) && CollectionUtils.isNotEmpty(serviceConfigList.iterator().next().getHeaderAttributes())) {
            ServiceConfig serviceConfig = serviceConfigList.iterator().next();
            headerAttributeService.deleteHeaderByCfgId(serviceConfig.getId());
        }
    }

    private void tBuildServiceCfg(Set<ServiceConfig> serviceConfigList, ServiceInfoVo serviceInfoVo, ServiceInfo serviceInfo) {
        ServiceConfig serviceConfig = new ServiceConfig();
        if (CollectionUtils.isNotEmpty(serviceInfo.getServiceConfigs()))
            serviceConfig = serviceInfo.getServiceConfigs().iterator().next();
        Set<HeaderAttribute> headerAttributes = Sets.newHashSet();
        serviceConfig.setHeaderAttributes(headerAttributes);
        serviceConfig.setCode(ServiceConfig.class.getName());
        serviceConfig.setType(ServiceConfig.class.getName());
        serviceConfig.setOperateUserId(serviceInfo.getOperateUserId());
        serviceConfig.setServiceInfoId(serviceInfo.getId());
        serviceConfigService.saveOrUpdate(serviceConfig);
        buildCfgAttribute(headerAttributes, serviceInfoVo.getResponseHeaderCfgList(), serviceConfig.getId());
        buildCfgAttribute(headerAttributes, serviceInfoVo.getRequestHeaderCfgList(), serviceConfig.getId());
        serviceConfigList.add(serviceConfig);
    }

    private void buildCfgAttribute(Set<HeaderAttribute> serviceConfigAttributes, List<ServiceConfigVo> headerCfgList, String cfgId) {
        for (ServiceConfigVo serviceConfigVo : headerCfgList) {
            HeaderAttribute headerAttribute = new HeaderAttribute();
            headerAttribute.setConfigType(serviceConfigVo.getConfigType());
            headerAttribute.setDefaultValue(serviceConfigVo.getDefaultValue());
            headerAttribute.setIsRequired(serviceConfigVo.getIsRequired());
            headerAttribute.setName(serviceConfigVo.getName());
            headerAttribute.setCode(serviceConfigVo.getCode());
            headerAttribute.setServiceConfigId(cfgId);
            headerAttribute.setType(serviceConfigVo.getColumnType());
            serviceConfigAttributes.add(headerAttribute);
        }
    }

    @Override
    public InterfaceCfgVo getInterfaceCfgVoById(String id) {
        InterfaceCfgVo interfaceCfgVo = new InterfaceCfgVo();
        HttpConfigRelation httpConfigRelation = httpConfigRelationService.get(HttpConfigRelation.class, id);
        Assert.notNull(httpConfigRelation, "数据错误，找不到对应的接口");
        ServiceType serviceType = serviceTypeService.get(ServiceType.class, httpConfigRelation.getServiceType());
        Assert.notNull(serviceType, "找不到对应的服务类型");
        interfaceCfgVo.setId(httpConfigRelation.getId());
        interfaceCfgVo.setCode(httpConfigRelation.getCode());
        interfaceCfgVo.setName(httpConfigRelation.getName());
        interfaceCfgVo.setServiceType(serviceType.getParentId());
        interfaceCfgVo.setInterfaceType(serviceType.getId());
        List<BodyParameterCfgVo> bodyParameterCfgVos = Lists.newArrayList();
        buildBodyAttributeVo(httpConfigRelation.getBodyAttributes(), bodyParameterCfgVos);
        interfaceCfgVo.setInterfaceParameterCfgVoList(bodyParameterCfgVos);
        return interfaceCfgVo;
    }

    private void buildBodyAttributeVo(Set<BodyAttribute> bodyAttributes, List<BodyParameterCfgVo> bodyParameterCfgVos) {
        for (BodyAttribute bodyAttribute : bodyAttributes) {
            BodyParameterCfgVo bodyParameterCfgVo = new BodyParameterCfgVo();
            bodyParameterCfgVo.setId(bodyAttribute.getId());
            bodyParameterCfgVo.setName(bodyAttribute.getName());
            bodyParameterCfgVo.setCode(bodyAttribute.getCode());
            bodyParameterCfgVo.setDefaultValue(bodyAttribute.getDefaultValue());
            bodyParameterCfgVo.setColumnType(bodyAttribute.getType());
            bodyParameterCfgVo.setParameterType(bodyAttribute.getParameterType());
            bodyParameterCfgVos.add(bodyParameterCfgVo);
            if (CollectionUtils.isNotEmpty(bodyAttribute.getChildrenSet())) {
                buildBodyAttributeVo(bodyAttribute.getChildrenSet(), bodyParameterCfgVo.getChildren());
            }
        }

    }

    @Override
    public void deleteServiceById(String id) {
        ServiceInfo serviceInfo = serviceInfoService.get(ServiceInfo.class, id);
        Assert.notNull(serviceInfo, "该服务不存在");
        serviceInfoService.delete(serviceInfo);
    }

    @Override
    public void saveInterfaceBodyService(InterfaceCfgVo interfaceCfgVo, String userId) {
        HttpConfigRelation httpConfigRelation = new HttpConfigRelation();
        ServiceInfo serviceInfo = serviceInfoService.get(ServiceInfo.class, interfaceCfgVo.getServiceInfoId());
        Assert.notNull(serviceInfo, "该服务不存在");
        if (httpConfigRelationService.checkInterfaceName(serviceInfo.getId(), interfaceCfgVo.getName(), interfaceCfgVo.getId(), userId))
            throw new RuntimeException("该接口名称已存在！");
        if (StringUtils.isNotEmpty(interfaceCfgVo.getId())) {
            httpConfigRelation = httpConfigRelationService.get(HttpConfigRelation.class, interfaceCfgVo.getId());
        }
        Assert.notNull(serviceInfo.getServiceConfigs(), "服务选择错误");
        ServiceType serviceType = serviceTypeService.get(ServiceType.class, interfaceCfgVo.getInterfaceType());
        Assert.notNull(serviceType, "没有找到对应的服务类型");
        httpConfigRelation.setServiceConfigId(serviceInfo.getServiceConfigs().iterator().next().getId());
        httpConfigRelation.setOperateUserId(userId);
        httpConfigRelation.setName(interfaceCfgVo.getName());
        httpConfigRelation.setCode(HttpConfigRelation.class.getName());
        httpConfigRelation.setType(HttpConfigRelation.class.getName());
        tDeleteBody(interfaceCfgVo.getDeleteParameterLists());
        httpConfigRelation.setServiceType(serviceType);
        Set<BodyAttribute> bodyAttributes = Sets.newHashSet();
        httpConfigRelationService.saveOrUpdate(httpConfigRelation);
        buildBodyAttribute(bodyAttributes, interfaceCfgVo.getInterfaceParameterCfgVoList(), httpConfigRelation.getId());
        httpConfigRelation.setBodyAttributes(bodyAttributes);
        httpConfigRelationService.saveOrUpdate(httpConfigRelation);

    }


    private void tDeleteBody(List<String> deleteParameterLists) {
        if (CollectionUtils.isNotEmpty(deleteParameterLists)) {
            for (String id : deleteParameterLists) {
               if(StringUtils.isNotEmpty(id)){
                   BodyAttribute bodyAttribute = bodyAttributeService.get(BodyAttribute.class, id);
                   bodyAttributeService.delete(bodyAttribute);
               }
            }
        }
    }
    private void buildBodyAttribute(Set<BodyAttribute> bodyAttributes, List<BodyParameterCfgVo> bodyParameterCfgVos, String cfgId) {
        if (CollectionUtils.isNotEmpty(bodyParameterCfgVos)) {
            for (BodyParameterCfgVo bodyParameterCfgVo : bodyParameterCfgVos) {
                BodyAttribute bodyAttribute = new BodyAttribute();
                if(StringUtils.isNotEmpty(bodyParameterCfgVo.getId())) bodyAttribute = bodyAttributeService.get(BodyAttribute.class,bodyParameterCfgVo.getId());
                bodyAttribute.setDefaultValue(bodyParameterCfgVo.getDefaultValue());
                bodyAttribute.setCode(bodyParameterCfgVo.getCode());
                bodyAttribute.setName(bodyParameterCfgVo.getName());
                bodyAttribute.setType(bodyParameterCfgVo.getColumnType());
                bodyAttribute.setParameterType(bodyParameterCfgVo.getParameterType());
                bodyAttribute.setHttpCfgRelId(cfgId);
                bodyAttributes.add(bodyAttribute);
                if (CollectionUtils.isNotEmpty(bodyParameterCfgVo.getChildren())) {
                    buildBodyAttribute(bodyAttribute.getChildrenSet(), bodyParameterCfgVo.getChildren(), cfgId);
                }
            }
        }

    }

    @Override
    public void deleteInterfaceBodyById(String id) {
        HttpConfigRelation httpConfigRelation = httpConfigRelationService.get(HttpConfigRelation.class, id);
        Assert.notNull(httpConfigRelation, "该接口不存在");
        httpConfigRelationService.delete(httpConfigRelation);
    }


    @Override
    public ServiceInfoVo getRequestHeader(String userId) {
        ServiceConfig serviceConfig = serviceConfigService.queryServiceCfgBefore();
        ServiceInfoVo serviceInfoVo = new ServiceInfoVo();
        buildHeaderCfgVo(serviceInfoVo, serviceConfig);
        return serviceInfoVo;
    }
}
