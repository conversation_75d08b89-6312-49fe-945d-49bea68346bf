INSERT INTO "public"."t_sys_func" ("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('dataWarehouseDatasourceSynchronization', '0', '数据源同步', 'dataWarehouse', NULL, NULL, NULL, '2021-04-02 16:54:52.029', NULL, '1');
INSERT INTO "public"."t_sys_auth_obj_func" ("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('25e67166e2ee50fa9d5d28c3428099e4', 'd6121bc4248e45019942e2cb78362500', 'dataWarehouseDatasourceSynchronization', NULL, NULL, NULL, NULL, NULL, NULL, NULL);