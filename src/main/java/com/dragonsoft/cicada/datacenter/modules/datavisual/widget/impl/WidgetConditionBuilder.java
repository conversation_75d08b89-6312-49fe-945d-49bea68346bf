package com.dragonsoft.cicada.datacenter.modules.datavisual.widget.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.code.common.utils.StringUtils;
import com.dragonsoft.cicada.datacenter.modules.datavisual.model.AbsConditionWidget;
import com.dragonsoft.cicada.datacenter.modules.datavisual.model.NumberCondition;
import com.dragonsoft.cicada.datacenter.modules.datavisual.model.StringCondition;
import com.dragonsoft.cicada.datacenter.modules.datavisual.model.TimeCondition;
import com.dragonsoft.cicada.datacenter.modules.datavisual.widget.IWidgetConditionBuilder;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class WidgetConditionBuilder implements IWidgetConditionBuilder {


    @Override
    public AbsConditionWidget builder(String type, String json) {
        if ("number".equals(type)) {
            NumberCondition numFilter = JSONObject.parseObject(json, NumberCondition.class);
            return numFilter;
        } else if ("time".equals(type)) {
            TimeCondition timeFilter = JSONObject.parseObject(json, TimeCondition.class);
            return timeFilter;
        } else if ("string".equals(type)) {
            StringCondition stringFilter = JSONObject.parseObject(json, StringCondition.class);
            return stringFilter;
        } else if("".equals(type)){

        }
        return null;
    }

    @Override
    public List<AbsConditionWidget> builder(String json) {
        List<AbsConditionWidget> conditionWidgets = new ArrayList<>();
        JSONArray jsonArray = JSONArray.parseArray(json);
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject object = (JSONObject) jsonArray.get(i);
            String type = (String) object.get("type");
            if(StringUtils.isBlank(type)){
                continue;
            }
            AbsConditionWidget conditionWidget = this.builder(type, object.toJSONString());
            if (null != conditionWidget) {
                conditionWidgets.add(conditionWidget);
            }
        }
        return conditionWidgets;
    }

}
