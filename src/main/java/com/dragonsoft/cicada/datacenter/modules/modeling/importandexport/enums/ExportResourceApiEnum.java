package com.dragonsoft.cicada.datacenter.modules.modeling.importandexport.enums;
//导出服务资源枚举
public enum ExportResourceApiEnum {
    API("API","API"),
    USE_CASE("USE_CASE","测试用例");

    public String code;
    public String name;

    ExportResourceApiEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static ExportResourceApiEnum getInstanceByCode(String code) {
        ExportResourceApiEnum[] var1 = values();
        int var2 = var1.length;

        for(int var3 = 0; var3 < var2; ++var3) {
            ExportResourceApiEnum value = var1[var3];
            if (value.code.equals(code)) {
                return value;
            }
        }

        return API;
    }
}
