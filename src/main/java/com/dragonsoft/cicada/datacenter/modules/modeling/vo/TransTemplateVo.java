package com.dragonsoft.cicada.datacenter.modules.modeling.vo;

import com.alibaba.fastjson.JSON;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> Wu.D.J
 * @Create : 2020.05.12
 */
@Data
public class TransTemplateVo {

    private String transName;

    private String tableName;

    private String schemaId;

    private String tableId;


    public static void main(String[] args) {
        List<TransTemplateVo> list = new ArrayList<>();

        TransTemplateVo v1 = new TransTemplateVo();
        v1.setSchemaId("1");
        v1.setTableId("1");
        v1.setTableName("1");
        v1.setTransName("1");
        list.add(v1);

        TransTemplateVo v2 = new TransTemplateVo();
        v2.setSchemaId("v2");
        v2.setTableId("v2");
        v2.setTableName("v2");
        v2.setTransName("v2");
        list.add(v2);

        System.out.println(JSON.toJSONString(list));
    }
}
