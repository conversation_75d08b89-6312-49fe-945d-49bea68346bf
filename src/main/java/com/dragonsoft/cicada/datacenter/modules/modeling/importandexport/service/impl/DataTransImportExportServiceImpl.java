package com.dragonsoft.cicada.datacenter.modules.modeling.importandexport.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.code.common.bean.BeanFactory;
import com.code.common.paging.PageInfo;
import com.code.common.utils.StringUtils;
import com.code.common.utils.assertion.Assert;
import com.code.metadata.base.expression.ElementNode;
import com.code.metadata.base.expression.FeatureNode;
import com.code.metadata.business.directory.BaseBusiClassify;
import com.code.metadata.business.directory.BaseBusiDir;
import com.code.metadata.business.directory.BusiClassify;
import com.code.metadata.datavisual.Dashboard;
import com.code.metadata.datawarehouse.DwDbInstance;
import com.code.metadata.etl.trans.TransAttributeMeta;
import com.code.metadata.etl.trans.TransExpMeta;
import com.code.metadata.etl.trans.TransMeta;
import com.code.metadata.etl.trans.TransPluginMeta;
import com.code.metadata.model.core.DataType;
import com.code.metadata.model.core.ModelElement;
import com.code.metadata.portal.MenuConfig;
import com.code.metadata.portal.Portal;
import com.code.metadata.portal.PortalConfig;
import com.code.metadata.portal.PublishUrl;
import com.code.metadata.res.ddl.*;
import com.code.metadata.res.structured.rdb.RdbCatalog;
import com.code.metadata.res.structured.rdb.RdbSchema;
import com.code.metadata.udf.management.UdfGraph;
import com.code.metaservice.dataSet.IDataSetTreeService;
import com.code.metaservice.datavisual.IDashBoardService;
import com.code.metaservice.datawarehouse.IDataWareTreeService;
import com.code.metaservice.ddl.IDataSetStepMetaService;
import com.code.metaservice.ddl.ILogicDataObjService;
import com.code.metaservice.ddl.vo.LogicDataObjInfo;
import com.code.metaservice.etl.trans.TransMetaService;
import com.code.metaservice.portal.IMenuConfigService;
import com.code.metaservice.portal.IPortalConfigService;
import com.code.metaservice.portal.IPortalService;
import com.code.metaservice.portal.IPublishUrlService;
import com.code.metaservice.udf.management.IUdfGraphService;
import com.code.mist.builder.service.dubbo.ITransformApiService;
import com.code.plugin.db.TypeMapping;
import com.code.std.types.NonStandardType;
import com.code.std.types.StandardType;
import com.dragoninfo.dfw.entity.*;
import com.dragoninfo.dfw.service.SysAuthObjFuncService;
import com.dragoninfo.dfw.service.SysAuthObjService;
import com.dragoninfo.dfw.service.SysFuncService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.service.IDataSetEditService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.service.IDataSetOperationService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.util.DataSetUtils;
import com.dragonsoft.cicada.datacenter.modules.dataplan.externaldatasources.service.IDataWarehousePlanService;
import com.dragonsoft.cicada.datacenter.modules.modeling.importandexport.controller.DataTransImportExportController;
import com.dragonsoft.cicada.datacenter.modules.modeling.importandexport.service.IDataTransImportExportService;
import com.dragonsoft.cicada.datacenter.modules.modeling.importandexport.util.DataExportUtil;
import com.dragonsoft.cicada.datacenter.modules.modeling.importandexport.vo.ExportModelVo;
import com.dragonsoft.cicada.datacenter.modules.modeling.importandexport.vo.ImportCompleteResultVo;
import com.dragonsoft.cicada.datacenter.modules.modeling.vo.DataExportVo;
import com.dragonsoft.cicada.datacenter.modules.supermarkmodel.service.IMyModelService;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.service.IUserService;
import com.fw.dao.hbmimpl.BaseDaoImpl;
import com.fw.service.BaseService;
import com.fw.service.annotation.Service;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.util.DataSetUtils.defaultPageSize;
import static com.dragonsoft.cicada.datacenter.modules.modeling.importandexport.util.DataExportUtil.*;
import static com.dragonsoft.cicada.datacenter.modules.modeling.importandexport.vo.ImportCompleteResultVo.LOGIC_TYPE;

/**
 * <AUTHOR>
 * @Date: 2022/07/13/下午2:03
 */
@Slf4j
@Service
public class DataTransImportExportServiceImpl extends BaseService implements IDataTransImportExportService {
    @Autowired
    private ITransformApiService transformApiService;

    @Autowired
    private IMyModelService myModelServiceImpl;


    @Autowired
    private TransMetaService transMetaService;

    @Autowired
    private ILogicDataObjService logicDataObjService;

    @Autowired
    private IDashBoardService dashBoardService;

    @Autowired
    private IUdfGraphService udfGraphService;

    @Autowired
    private BaseDaoImpl baseDao;


    @Autowired
    IDataSetEditService editService;

    @Autowired
    private IPortalService portalService;

    @Autowired
    private IPortalConfigService portalConfigService;

    @Autowired
    private IMenuConfigService menuConfigService;

    @Autowired
    private IPublishUrlService publishUrlService;

    @Autowired
    BeanFactory beanFactory;

    @Autowired
    private SysAuthObjFuncService sysAuthObjFuncService;

    @Autowired
    private IUserService iUserService;

    private final List<String> collisionPlugins = Lists.newArrayList("'fullJoinPlugin'", "'innerJoinPlugin'", "'leftOrRightJoinPlugin'", "'subtractByKeyPlugin'", "'unionJoinPlugin'");


    private final String INPUTPLUGINCODE = "cicadaStandardSqlInput";

    private final String OUTPUTPLUGINCODE = "cicadaStandardSqlOutput";
    /**
     * select * from t_etl_trans_attribute where trans_id='bae48aa5f7054e4b877969c01cacb3ac'
     * 通过插件transmeta找到attribute  不能根据trans_attribute_id去找属性，id会变，得根据code筛选出来现在的attributeId,
     * 再去拿到attribute对应的属性值，所以才定义了下面的这些code的。
     */
    //输出插件 表的RDBOBJCode元信息
    private final String OUTPUTTABLEMETACODE = "tableId";
    //输出插件 dwInstanceCode 元信息
    private final String OUTPUTTABLEDWINSTANCECODE = "dataSetId";
    //输出插件 RDbschemaCode 元信息
    private final String OUTPUTSCHEMECODE = "schemaId";
    //输出插件 RdbCatalogCode元信息
    private final String OUTPUTCATALOGCODE = "catalogId";

    //输入插件 表的RdbObjCode元信息
    private final String INPUTTABLERDBOBJCODE = "classifierStatId";
    //输入插件 表的LogicObjCode元信息
    private final String INPUTTABLELOGICOBJCODE = "logicObjId";
    //输入插件 schemaCode元信息
    private final String INPUTTABLESCHEMATECODE = "schemaId";

    @Autowired
    private IDataSetOperationService dataSetOperationService;

    @Autowired
    private IDataSetTreeService dataSetTreeService;

    @Autowired
    private IDataWarehousePlanService dataWarehouseService;


    @Autowired
    private IDataWareTreeService dataWareTreeService;


    @Autowired
    private SysAuthObjService sysAuthObjService;


    @Autowired
    private SysFuncService sysFuncService;


    @Autowired
    IDataSetStepMetaService dataSetStepMetaService;

    @Override
    public RdbCatalog getRabCatalog(String catalogId) {
        return (RdbCatalog) this.baseDao.get(RdbCatalog.class, catalogId);
    }

    @Override
    public List<Map<String, Object>> getRabCatalogSql(String catalogId) {
        String sql = "select * from t_md_rdb_catalog where id =:catalogId";
        List<Map<String, Object>> list = this.baseDao.sqlQueryForList(sql, addParam("catalogId", catalogId).param());
        return list;
    }

    @Override
    public DwDbInstance getDbInstanceById(String instancdId) {
        return (DwDbInstance) this.baseDao.get(DwDbInstance.class, instancdId);
    }

    @Override
    public List<Map<String, Object>> getDbInstanceByIdSql(String instancdId) {
        String sql = "select * from T_MD_DW_DB_INSTANCE where id =:instancdId";
        List<Map<String, Object>> list = this.baseDao.sqlQueryForList(sql, addParam("instancdId", instancdId).param());
        return list;
    }

    @Override
    public BaseBusiDir getBaseBusiDirById(String dirId) {
        return (BaseBusiDir) this.baseDao.get(BaseBusiDir.class, dirId);
    }

    @Override
    public BaseBusiClassify getBaseBusiClassifiy(String classifyId) {
        return (BaseBusiClassify) this.baseDao.get(BaseBusiClassify.class, classifyId);
    }

    @Override
    public DataTransImportExportController.ClassifyElement getClassifyElement(String instanceId) {
        DataTransImportExportController.ClassifyElement classifyElement = new DataTransImportExportController.ClassifyElement();

        String sql = "select * FROM t_md_classify_element where element_id=:instanceId";
        Map map = this.baseDao.sqlQueryForMap(sql, addParam("instanceId", instanceId).param());
        if (map.size() == 0) return classifyElement;
        classifyElement.setElementId(instanceId);
        classifyElement.setBusiClassifyId(map.get("busi_classify_id").toString());
        return classifyElement;
    }

    @Override
    public TransMeta getTransMetaById(String transId) {
        return (TransMeta) this.baseDao.get(TransMeta.class, transId);
    }

    @Override
    public DataTransImportExportController.DwDbMapping getDwDbMappingbyInstanceId(String instanceId) {
        String sql = "select * from t_dw_db_mapping  where dw_db_instance_id=:instanceId";
        Map dbMap = this.baseDao.sqlQueryForMap(sql, addParam("instanceId", instanceId).param());
        DataTransImportExportController.DwDbMapping dwDbMapping = new DataTransImportExportController.DwDbMapping();
        dwDbMapping.setDwDbInstanceId(instanceId);
        dwDbMapping.setDeployedSoftwore(dbMap.get("deployed_software").toString());
        return dwDbMapping;
    }

    @Override
    public RdbSchema getRdbSchema(String schemaId) {
        return (RdbSchema) this.baseDao.get(RdbSchema.class, schemaId);
    }

    @Override
    public List<Map<String, Object>> getRdbSchemaSql(String schemaId) {
        String sql = "select * from t_md_rdb_schema where id =:schemaId";
        List<Map<String, Object>> list = this.baseDao.sqlQueryForList(sql, addParam("schemaId", schemaId).param());
        return list;
    }

    @Override
    public List<Map<String, Object>> getLabelElement(String subjectId) {
        String sql = "select * from T_MD_LABEL_ELEMENT where subject_id=:subjectId";
        List<Map<String, Object>> list = this.baseDao.sqlQueryForList(sql, addParam("subjectId", subjectId).param());
        return list;
    }

    @Override
    public List<Map<String, Object>> getRdbCatalogCluster(String catalogId) {
        String hql = "select * from  t_md_rdbcatalog_cluster where owner_id=:catalogId";
        List<Map<String, Object>> list = this.baseDao.sqlQueryForList(hql, addParam("catalogId", catalogId).param());
        return list;
    }

    @Override
    public List<Map<String, Object>> getMachine(String machineId) {
        String hql = "select * from t_md_machine where id=:machineId";
        List<Map<String, Object>> list = this.baseDao.sqlQueryForList(hql, addParam("machineId", machineId).param());
        return list;
    }

    @Override
    public List<String> getTransMetaListByClassifyId(String classifyId, String userId) {
        List<Map<String, Object>> list = listTransInfo(classifyId, userId);
        List<String> transIds = list.stream().map(s -> s.get("id").toString()).collect(Collectors.toList());

        return transIds;
    }


    /**
     * 通过id找到对应的方案(流程、可视化、数据集)，合在一起导出一份二进制模型文件
     *
     * @param portalIds
     * @param transIds  要导出的数据挖掘方案，多个用,隔开，也可不传
     * @param visualIds 要导出的可视化方案，多个用,隔开，也可不传
     * @param logicIds  要导出的数据集方案，多个用,隔开，也可不传
     * @return
     * @throws IOException
     */
    @Override
    public DataTransImportExportController.DataCenterTransExportBean exportModel(List<String> transIds, List<String> visualIds, List<String> logicIds, List<String> portalIds, String dbType) {
        DataTransImportExportController.DataCenterTransExportBean bean = new DataTransImportExportController.DataCenterTransExportBean();
        // 1. 导出数据挖掘方案
        if (transIds != null && transIds.size() > 0) {
            List<TransMeta> transMetas = new ArrayList<>();
            List<String> exportTransIds = Lists.newArrayList();
            List<Map<String, Object>> transPlugins = Lists.newArrayList();
            List<UdfGraph> graphs = new ArrayList<>();
            List<String> transTasks = Lists.newArrayList();
            List<String> transSchedules = Lists.newArrayList();
            List<String> transSubTransRelations = Lists.newArrayList();
            List<DataTransImportExportController.ClassifyElement> transClassifyElemnts = new ArrayList<>();
            for (String transId : transIds) {
                // 导出TransMeta对象
                TransMeta transMeta = transMetaService.getTransMetaById(transId);
                // transPluginSQL.append(buildTransPluginSQL(transId));
                //导出插件元信息
                transPlugins.addAll(buildTransPlugins(transId));
                //导出碰撞插件不同的元信息
                if (CollectionUtils.isNotEmpty(transPlugins.stream().filter(s -> ("collisionPlugin").equalsIgnoreCase((String) s.get("code"))).collect(Collectors.toList())))
                    transPlugins.addAll(buildTransCollisionPlugins(collisionPlugins));
                for (TransMeta child : transMeta.getChildren()) {
                    TransPluginMeta usedPlugin = child.getUsedPlugin();

                    // 由于算子编排有自己的数据库实体，导入时需要做特殊处理，因此导出时也要查询出UdfGraph对象，单独存放
                    if ("serviceOrganization".equals(usedPlugin.getCode())) {  // 处理算子编排
                        FeatureNode featureNode = usedPlugin.getPluginExp("serviceOrgOutputs");
                        ElementNode elementNode = (ElementNode) featureNode.getArgument("serviceOrgId");
                        Set<TransExpMeta> transExps = child.getTransExps();
                        for (TransExpMeta exp : transExps) {
                            String expValue = exp.getExpValue(elementNode);
                            UdfGraph graph = udfGraphService.get(UdfGraph.class, expValue);
                            if (null != graph)
                                graphs.add(graph);
                        }
                    }
                }

                // 导出方案对应的调度配置信息
                buildScheduleInfos(transId, transTasks, transSchedules, transSubTransRelations);
                transMetas.add(transMeta);
                exportTransIds.add(transMeta.getId());
                //导出方案与目录关系表
                DataTransImportExportController.ClassifyElement classifyElement = getClassifyElement(transId);
                transClassifyElemnts.add(classifyElement);
            }
            DataTransImportExportController.TransScheduleInfo scheduleInfo = new DataTransImportExportController.TransScheduleInfo();
            scheduleInfo.setTransSchedules(transSchedules);
            scheduleInfo.setTransSubTransRelations(transSubTransRelations);
            scheduleInfo.setTransTasks(transTasks);
            transPlugins = filterMap(transPlugins, "id");
            String params = collectParams(transPlugins, "id");

            //导出模型输出
            List<LogicDataObjInfo> transOutResult = getTransOutResult(transIds);

            bean.setTransPluginPartitions(buildTransPluginPartitions(transPlugins, "partition"));
            bean.setTransPluginExpRelations(buildTransPluginPartitions(transPlugins, "relation"));
            bean.setTransPluginExpNodes(buildTransPluginPartitions(transPlugins, "expNode"));
            bean.setTransPluginExpNodeRelation(buildTransPluginPartitions(transPlugins, "expNodeRelation"));
            //bean.setTransPluginTransExp(buildTransPluginPartitions(transPlugins, "transExp"));
            bean.setTransPluginTransAttributes(buildTransPluginPartitions(transPlugins, "attribute"));
            bean.setTransPlugins(transPlugins);
            bean.setTransMetas(transMetas);
            bean.setExportTransIds(exportTransIds);
            bean.setUdfGraphs(graphs);
            bean.setScheduleInfo(scheduleInfo);
            bean.setTransClassifyElemnts(transClassifyElemnts);
            bean.setTransOutputLogics(transOutResult);
        }

        // 2.导出数据集方案
        if (logicIds != null && logicIds.size() > 0) {
            List<LogicDataObjInfo> logics = new ArrayList<>();
            for (String id : logicIds) {
                LogicDataObjInfo logicDataObjInfo = logicDataObjService.getLogicDataObjInfo(id);
                logicDataObjInfo.getLogicDataObj().setDbType(dbType);
                logicDataObjInfo.setDataColumns(transLogicColumnDataType(logicDataObjInfo.getLogicDataObj(), logicDataObjInfo.getDataColumns()));
                logics.add(logicDataObjInfo); // 该接口佳培在维护
            }

            bean.setLogics(logics);
        }

        // 3. 导出可视化方案
        if (visualIds != null && visualIds.size() > 0) {
            List<Dashboard> dashboards = new ArrayList<>();
            for (String id : visualIds) {
                Dashboard dashboard = dashBoardService.getDashboard(id);
                if (dashboard != null) {
                    dashboards.add(dashboard);
                }
            }
            bean.setDashboards(dashboards);
        }
        //导出门户
        if (portalIds != null && portalIds.size() > 0) {
            List<DataTransImportExportController.PortalInfo> portalInfos = Lists.newArrayList();
            for (String portalId : portalIds) {
                DataTransImportExportController.PortalInfo portalInfo = new DataTransImportExportController.PortalInfo();
                Portal portal = portalService.get(Portal.class, portalId);
                PortalConfig portalConfig = portalConfigService.queryPortalConfigByPortalId(portalId);
                List<MenuConfig> menuConfigs = menuConfigService.queryMenuConfigByConfigId(portalConfig.getId());
                PublishUrl publishUrl = publishUrlService.queryPublishUrlByPortalId(portalId);

                if (null != portal) {
                    portalInfo.setPortal(portal);
                    portalInfo.setPortalConfig(portalConfig);
                    portalInfo.setMenuConfigs(menuConfigs);
                    portalInfo.setPublishUrl(publishUrl);
                    portalInfos.add(portalInfo);
                }
            }
            bean.setPortalInfos(portalInfos);
        }
        return bean;
    }

    private List<LogicDataObjInfo> getTransOutResult(List<String> transIds) {

        //找到所有方案涉及的数据集，数据源

        List<LogicDataObjInfo> logics = new ArrayList<>();
        String sql = "select * from t_md_logic_dataobj where owner_id=:rdbId and step_relation_id is NULL";
        for (String rdbid : getOutputRdbIdsByTransIds(transIds)) {
            Map<String, Object> logicMap = this.baseDao.sqlQueryForMap(sql, addParam("rdbId", rdbid).param());
            if (logicMap.size() > 0) {
                //如果这个是原生的logic才导出，也就是在关系表不存在的logic
                if (checkIsExistRelation(logicMap.get("id").toString())) {
                    LogicDataObjInfo logicDataObjInfo = getLogicDataObjInfo(logicMap.get("id").toString());
                    logics.add(logicDataObjInfo); // 该接口佳培在维护
                }
            }
        }
        return logics;
    }

    //获取输出插件的rdbId
    private List<String> getOutputRdbIdsByTransIds(List<String> transIds) {
        List<String> rdbObjIdList = new ArrayList<>();
        for (String transId : transIds) {
            TransMeta transMeta = (TransMeta) this.baseDao.get(TransMeta.class, transId);
            List<TransMeta> outputPluginMetas = transMeta.getChildren().stream().filter(s -> s.getUsedPlugin().getCode().equals(OUTPUTPLUGINCODE)).collect(Collectors.toList());
            for (TransMeta outputPluginMeta : outputPluginMetas) {
                //拿到插件属性
                Map<TransAttributeMeta, String> attributeValues = outputPluginMeta.getAttributeValues();
                for (TransAttributeMeta transAttributeMeta : attributeValues.keySet()) {
                    //输出插件是rdb，没有跟目录有挂钩
                    if (transAttributeMeta.getCode().equals(OUTPUTTABLEMETACODE)) {
                        String rdbobjId = attributeValues.get(transAttributeMeta);
                        rdbObjIdList.add(rdbobjId);
                    }
                }
            }
        }
        return rdbObjIdList;
    }

    private Set<String> getOutputLogicIds(List<String> transIds,Map<String,List<String>> listMap) {
        Set<String> logics = new HashSet<>();
        String sql = "select * from t_md_logic_dataobj where owner_id=:rdbId and step_relation_id is NULL";
        for (String rdbid : getOutputRdbIdsByTransIds(transIds)) {
            Map<String, Object> logicMap = this.baseDao.sqlQueryForMap(sql, addParam("rdbId", rdbid).param());
            if (logicMap.size() > 0) {
                LogicDataObjInfo logicDataObjInfo = getLogicDataObjInfo(logicMap.get("id").toString());
                if (logicDataObjInfo.getLogicDataObj() != null) {
                    logics.add(logicDataObjInfo.getLogicDataObj().getId());
                    //DataExportUtil.addMapList(listMap,); todo 这边输出插件的数据集暂时先不管
                }
            }
        }
        return logics;
    }
    private Set<String> getOutputLogicIds(List<String> transIds) {
        return getOutputLogicIds(transIds,new HashMap<>());
    }

    private LogicDataObjInfo getLogicDataObjInfo(String logicId) {
        LogicDataObjInfo logicDataObjInfo = logicDataObjService.getLogicDataObjInfo(logicId);
        logicDataObjInfo.setDataColumns(transLogicColumnDataType(logicDataObjInfo.getLogicDataObj(), logicDataObjInfo.getDataColumns()));
        return logicDataObjInfo;
    }

    private boolean checkIsExistRelation(String logicId) {
        String sql = "select count(1) as num from t_md_classify_element where element_id=:logicId";
        Map map = this.baseDao.sqlQueryForMap(sql, addParam("logicId", logicId).param());
        return Integer.parseInt(map.get("num").toString()) > 0 ? false : true;
    }

    @Override
    public List<String> getDatasourceIdByClassifyId(String classifyId) {
        String sql = "select i.id  from t_md_classify_element e , t_md_dw_db_instance i where e.element_id=i.id and e.busi_classify_id=:classifyId";
        List<Map<String, String>> list = this.baseDao.queryForList(sql, addParam("classifyId", classifyId).param());
        List<String> datasourceIds = list.stream().map(s -> s.get("id")).collect(Collectors.toList());
        return datasourceIds;
    }


    private List<Map<String, Object>> getAllLogicObjByClassifyId(String classifyId, String userId) {
        List<Map<String, Object>> logicDataObPageInfo = getLogicDataObPageInfo(classifyId, userId);
        return logicDataObPageInfo;
    }

    public List<Map<String, Object>> getLogicDataObPageInfo(String id, String userId) {
        Map<String, String> pathAndId = this.getPathAndId(id);
        Map<String, Object> param = Maps.newHashMap();
        StringBuffer sql = new StringBuffer();
        sql.append("select a.id, a.name, a.code, a.operate_time, a.belong_type, a.db_type, a.operate_user_id, a.is_fast, a.owner_id, a.data_type, b.busi_classify_id as parentId  from t_md_logic_dataobj a  left join  t_md_classify_element b  on  a.id = b.element_id  where 1=1 and a.belong_type is not null and b.busi_classify_id is not null ");
        param.put("userId", userId);
        sql.append(" and operate_user_id = (:userId)");
        if (pathAndId.size() > 0) {
            sql.append(" and busi_classify_id in (:ids)");
            param.put("ids", pathAndId.keySet());
        } else {
            sql.append(" and busi_classify_id in (:ids)");
            param.put("ids", id);
        }


        sql.append(" ORDER BY operate_time desc");
        List<Map<String, Object>> result = this.baseDao.sqlQueryForList(sql.toString(), param);
        return result;
    }


    public Map<String, String> getPathAndId(String id) {
        StringBuffer sql = new StringBuffer();
        Map<String, Object> params = Maps.newHashMap();
        params.put("id", id);
        sql.append("From BaseBusiClassify where id = :id");
        BaseBusiClassify baseBusiClassify = (BaseBusiClassify) this.baseDao.queryForObject(sql.toString(), params);
        Map<String, String> map = new HashMap();
        if (baseBusiClassify == null && !id.equalsIgnoreCase("6a155c5cdf9d5ddcbd8459f05b37186a")) {
            sql = new StringBuffer();
            sql.append("FROM BaseBusiDir WHERE id = :id");
            BaseBusiDir baseBusiDir = (BaseBusiDir) this.baseDao.queryForObject(sql.toString(), params);
            List<BaseBusiClassify> baseBusiClassifies = (List) baseBusiDir.getBusiClassifys().stream().filter((b) -> {
                return Objects.isNull(b.getOwnerId()) || baseBusiDir.getId().equals(b.getOwnerId());
            }).collect(Collectors.toList());
            Iterator var8 = baseBusiClassifies.iterator();

            while (var8.hasNext()) {
                BaseBusiClassify classify = (BaseBusiClassify) var8.next();
                ((Map) map).putAll(this.diguiChidren(classify, baseBusiDir.getName() + '/' + classify.getName()));
            }

            ((Map) map).put("isShare", "share");
        } else if (baseBusiClassify == null && id.equalsIgnoreCase("6a155c5cdf9d5ddcbd8459f05b37186a")) {
            ((Map) map).put("isShare", "share");
        } else {
            map = this.diguiChidren(baseBusiClassify, baseBusiClassify.getName());
            ((Map) map).put("isShare", "noShare");
        }

        return (Map) map;
    }


    public Map<String, String> diguiChidren(BaseBusiClassify busiClassify, String name) {
        Map<String, String> map = new HashMap();
        if (busiClassify.getElements().size() > 0) {
            map.put(busiClassify.getId(), name);
        }

        Iterator var4 = busiClassify.getBusiClassifies().iterator();

        while (var4.hasNext()) {
            BaseBusiClassify classify = (BaseBusiClassify) var4.next();
            String names = name + "/" + classify.getName();
            map.putAll(this.diguiChidren(classify, names));
        }

        return map;
    }

    public List<String> getAllAuthDatasetId(String roleId) {
        Map<String, String> params = Maps.newHashMap();
        params.put("obj_id", roleId);
        List<TSysAuthObjFunc> tSysAuthObjFuncs = sysAuthObjFuncService.queryList(params);

        List<String> authFunctionIds = tSysAuthObjFuncs.stream().filter(f ->
                f.gettSysFuncBase() != null &&
                        f.gettSysFuncBase().getFuncType().equals("1")).map(s -> s.gettSysFuncBase().getFuncCode()).collect(Collectors.toList());
        return authFunctionIds;
    }

    @Override
    public DataTransImportExportController.LogicObjBean exportCustomDataset(String classifyId, String userId) {

        //通过目录找到所有的logicobj
        List<Map<String, Object>> logicMaps = getAllLogicObjByClassifyId(classifyId, userId);
        List<String> logicIds = logicMaps.stream()
                .map(map -> map.get("id").toString())
                .collect(Collectors.toList());
        return exportLogicBeanById(logicIds);

    }

    @Override
    public DataTransImportExportController.LogicObjBean exportLogicBeanById(List<String> logicIds) {
        if(CollectionUtils.isEmpty(logicIds)) return new DataTransImportExportController.LogicObjBean();
        // 5. 通过源数据集id，查询出全部源数据集对象(这个接口是佳培在维护)
        List<LogicDataObjInfo> logicDataObjInfos = Lists.newArrayList();
        String[] ids = new String[logicIds.size()];

        for (String logicId : logicIds) {
            logicDataObjInfos.add(logicDataObjService.getLogicDataObjInfo(logicId));
        }

        for (int i = 0; i < logicIds.size(); i++) {
            String id = logicIds.get(i);
            if (StringUtils.isNotBlank(id)) {
                ids[i] = "'" + id + "'";
            }
        }
        // 6. 通过源数据集id，找到所有源数据集与数仓目录的挂接关系
        String condition = String.join(",", ids);
        //  0 逻辑数据集与数据源关系  1 逻辑数据集与数据集关系';
        String sql = String.format("select * from t_md_logic_data_relation where logic_data_obj_id in (%s) and relation_type = '0'", condition);
        List<Map<String, Object>> list = baseDao.sqlQueryForList(sql);
        String relationSQL = list.stream().map(map -> insertSQL("t_md_logic_data_relation", map, null, null)).collect(Collectors.joining("\n"));

        List<DataTransImportExportController.ClassifyElement> classifyElementList = new ArrayList<>();
        //导出资源与目录的关系
        for (String logicId : logicIds) {
            DataTransImportExportController.ClassifyElement classifyElement = getClassifyElement(logicId);
            if (classifyElement.getElementId() != null)
                classifyElementList.add(classifyElement);
        }

        // 7. 将全部源数据集对象和数仓目录挂接关系SQL，组装成LogicObjBean，序列化导出成logic文件
        DataTransImportExportController.LogicObjBean bean = new DataTransImportExportController.LogicObjBean(logicDataObjInfos, relationSQL, classifyElementList);
        return bean;
    }

    @Override
    public List<BaseBusiClassify> findFatherAndSonClassify(String classifyId) {
        List<BaseBusiClassify> baseBusiClassifies = new ArrayList<>();
        BaseBusiClassify baseBusiClassify = (BaseBusiClassify) this.baseDao.get(BaseBusiClassify.class, classifyId);
        //添加父类的
        List<BaseBusiClassify> busiClassifies = putParantClassify(baseBusiClassify, baseBusiClassifies);
        //添加儿子的
        List<BaseBusiClassify> busiClassifies1 = putSonfClassify(baseBusiClassify.getId(), busiClassifies);
        busiClassifies1.stream().forEach(s -> s.setBusiClassifies(null));
        return busiClassifies1;
    }

    @Override
    public BaseBusiDir getBaseBusiDirByClassify(String classifyId) {
        BaseBusiClassify baseBusiClassify = (BaseBusiClassify) this.baseDao.get(BaseBusiClassify.class, classifyId);
        BaseBusiClassify resultbusiclassify = queryDir(baseBusiClassify);
        BaseBusiDir busiDir = (BaseBusiDir) this.baseDao.get(BaseBusiDir.class, resultbusiclassify.getBusiDir().getId());
        return busiDir;
    }

    public BaseBusiClassify queryDir(BaseBusiClassify baseBusiClassif) {
        if (baseBusiClassif.getParentBc() != null) {
            return queryDir(baseBusiClassif.getParentBc());
        }
        return baseBusiClassif;

    }

    @Override
    public void checkExportIsOk(String transDirId, String datasourceDirId, String datasetDirId, String userId) {
        //根据目录找到方案集合
        List<String> transMetaList = getTransMetaListByClassifyId(transDirId, userId);
        //找到所有方案涉及的数据集，数据源
        TransDatasetAndDatasource transDatasetAndSource = getTransDatasetAndSource(transMetaList,userId);

        //找到目录查出的所有数据源
        List<Map<String, Object>> mapList = queryDataTable(datasourceDirId);
        List<String> dwInstanceIds = mapList.stream().map(s -> s.get("id").toString()).collect(Collectors.toList());
        //排查方案设计的数据源 没有在 要导出的目录里面
        List<String> noExportDataSourceId = transDatasetAndSource.getDwInstances().stream().filter(s -> !dwInstanceIds.contains(s)).collect(Collectors.toList());
        StringBuffer buffer = new StringBuffer();
        if (noExportDataSourceId.size() != 0) {
            buffer.append("\n");
            buffer.append("导出的方案不包含以下数据源：");
            for (String dwId : noExportDataSourceId) {
                DwDbInstance instance = getDbInstanceById(dwId);
                buffer.append("\n");
                buffer.append("[" + getElementBelongClassify(dwId) + "]目录 -- [" + instance.getName() + "]数据源");
            }
        }


        //找到目录查出的所有的数据集
        List<Map<String, Object>> logicObjs = getAllLogicObjByClassifyId(datasetDirId, userId);
        List<String> logicIds = logicObjs.stream().map(s -> s.get("id").toString()).collect(Collectors.toList());
        //排查方案涉及的数据集 有没有在 要导出的目录里面
        List<String> noExportDatasetIds = transDatasetAndSource.getLogicObjs().stream().filter(s -> !logicIds.contains(s)).collect(Collectors.toList());
        if (noExportDatasetIds.size() != 0) {
            buffer.append("\n");
            buffer.append("导出的方案不包含以下数据集：");
            for (String logicobjId : noExportDatasetIds) {
                LogicDataObj logicDataObj = logicDataObjService.findLogicDataObjById(logicobjId);
                buffer.append("\n");
                buffer.append("[" + getElementBelongClassify(logicobjId) + "]目录 -- [" + logicDataObj.getName() + "]数据集");
            }
        }

        //如果方案包含的数据源或者数据集中不包含通过目录导出的 则校验提示一下
        if (noExportDataSourceId.size() != 0 || noExportDatasetIds.size() != 0) {
            Assert.fail(buffer.toString());
        }

    }


    private String getElementBelongClassify(String elementId) {
        String sql = "select busi_classify_id from t_md_classify_element where element_id=:elementId";
        List<Map<String, String>> list = this.baseDao.sqlQueryForList(sql, addParam("elementId", elementId).param());
        if (list.size() > 0) {
            BaseBusiClassify baseBusiClassify = (BaseBusiClassify) this.baseDao.get(BaseBusiClassify.class, list.get(0).get("busi_classify_id"));
            return baseBusiClassify.getName();
        }
        return "";
    }

    @Override
    public List<Map<String, Object>> listTransInfo(String dirId, String userId) {
        List<Map<String, Object>> result = new ArrayList<>();
        String sql = "select name from t_md_busi_classify where id='" + dirId + "'";
        String s = baseDao.sqlQueryForValue(sql);
        if ("-1".equals(dirId)) {//根目录
            result = this.listRootTrans(null, userId);
        } else if (("我的空间").equals(s) || ("标准模型").equals(s)) {

            result = this.listRootTrans(dirId, userId);

        } else { //单个文件夹
            result = this.listTransByDirId(dirId, userId);
        }
        return result;
    }


    // 获取根目录下的所有过程集合
    private List<Map<String, Object>> listRootTrans(String dirId, String userId) {
        String dirType = "TRANS_DIR_MF";
        Map<String, Object> params = Maps.newHashMap();


        List<Map<String, String>> secondDirId = this.baseDao.sqlQueryForList("select id from t_md_busi_classify where parent_classify_id = :dirId", this.addParam("dirId", dirId).param());
        List<String> list = allDirIds(secondDirId);
        list.add(dirId);

        // 查询数据
        StringBuffer sb = new StringBuffer();
        String dirCodes = "";
        if (dirType.equals("DIG_DIR_MF") || dirType.equals("DIG_DIR_STANDARD")) {
            dirCodes = "'DIG_DIR_MF','DIG_DIR_STANDARD'";
        } else if (dirType.equals("TRANS_DIR_MF") || dirType.equals("TRANS_DIR_STANDARD")) {
            dirCodes = "'TRANS_DIR_MF','TRANS_DIR_STANDARD'";
        }

        sb.append("SELECT a.id as id, a.name as name, d.name as classifyName,  a.operate_time as releaseTime,a.task_group as taskGroup, b.busi_classify_id as parentId ");
        sb.append(" FROM t_etl_trans a, t_md_classify_element b, ");
        sb.append("(").append("SELECT t.ID as id,t.NAME as name,t2.CODE as dirtype ,t1.code as code FROM t_md_element t,  t_md_busi_classify t1, t_md_element t2  WHERE t.ID = t1.ID  AND t2.ID=t1.BUSI_DIR_ID AND t2.TYPE='BusiDir' AND t2.CODE in (" + dirCodes + ")");
        //为什么要注释，因为在最顶层的目录要显示所有的儿子孙子目录下的方案，所以去掉这个过滤
        if (StringUtils.isNotBlank(dirId)) {
            sb.append("and ( parent_classify_id in (:list) or t.id='" + dirId + "'" + " )");
            params.put("list", list);
        }
        sb.append(") d");
        sb.append(" WHERE a.trans_type = 'TRANSFORM' AND a.id = b.element_id ");
        sb.append("  AND b.busi_classify_id =d.id  ");
        sb.append("  AND a.operate_user_id =:userId ");
        sb.append(" AND position('预览_preview' in a.name) = 0");  //排除转换插件预览产出的方案
        sb.append(" AND position('发布_publish' in a.name) = 0");  //排除方案发布产出的方案

        sb.append(" ORDER by operate_time desc");

        params.put("userId", userId);


        List<Map<String, Object>> result = baseDao.sqlQueryForList(sb.toString(), params);

        return result;
    }

    private List<String> allDirIds(List<Map<String, String>> idMap) {
        List<String> allIds = new ArrayList<>();
        if (idMap.size() > 0) {
            for (Map<String, String> map : idMap) {
                String id = map.get("id");
                allIds.add(id);
                List list = this.baseDao.sqlQueryForList("select id from t_md_busi_classify where parent_classify_id = :dirId", this.addParam("dirId", id).param());
                allIds.addAll(allDirIds(list));
            }
        }
        return allIds;
    }

    //通过名称和文件夹id获取过程集合
    private List<Map<String, Object>> listTransByDirId(String dirId, String userId) {

        Map<String, Object> params = Maps.newHashMap(); //查询参数
        if (!"-2".equals(dirId)) {
            params.put("parentId", dirId);
        }

        // 查询数据
        StringBuffer sb = new StringBuffer();

        sb.append("SELECT a.id as id, a.name as name,  a.operate_time as releaseTime,a.task_group as taskGroup, b.busi_classify_id as parentId, d.name as classifyName");
        sb.append(" FROM t_etl_trans a, t_md_classify_element b,  t_md_busi_classify d ");
        sb.append(" WHERE a.trans_type = 'TRANSFORM' AND a.id = b.element_id AND  d.id = b.busi_classify_id ");
        if (!"-2".equals(dirId)) {
            sb.append("  AND b.busi_classify_id =:parentId ");
        }
        sb.append(" AND position('预览_preview' in a.name) = 0");  //排除转换插件预览产出的方案
        sb.append(" AND position('发布_publish' in a.name) = 0");  //排除方案发布产出的方案


        sb.append(" ORDER by a.operate_time desc");

        List<Map<String, Object>> resultList = baseDao.sqlQueryForList(sb.toString(), params);

        //在本级目录同时显示子级的目录
        String hql = "from BusiClassify where parentBc.id = :id";
        List<BusiClassify> bcs = this.baseDao.queryForList(hql, addParam("id", dirId).param());
        for (BusiClassify bc : bcs) {
            List<Map<String, Object>> sonList = listTransByDirId(bc.getId(), userId);

            resultList.addAll(sonList);
        }
        return resultList;
    }

    private TransDatasetAndDatasource getTransDatasetAndSource(List<String> transMetaList,String userId) {
        TransDatasetAndDatasource transDatasetAndDatasource = new TransDatasetAndDatasource();
        Set<String> dwInstances = new HashSet<>();
        Set<String> logicObjs = new HashSet<>();
        Map<String,List<String>> transAndLogicMap = new HashMap<>();
        Map<String,List<String>> transAndDwInstanceMap = new HashMap<>();
        for (String transId : transMetaList) {

            TransMeta transMeta = (TransMeta) this.baseDao.get(TransMeta.class, transId);
            //找到方案的所有插件中 输入插件
            List<TransMeta> InputPluginMetas = transMeta.getChildren().stream().filter(s -> s.getUsedPlugin().getCode().equals(INPUTPLUGINCODE)).collect(Collectors.toList());
            //找到方案的所有插件中 输入插件
            List<TransMeta> outputPluginMetas = transMeta.getChildren().stream().filter(s -> s.getUsedPlugin().getCode().equals(OUTPUTPLUGINCODE)).collect(Collectors.toList());

            for (TransMeta inputPluginMeta : InputPluginMetas) {
                //拿到插件属性
                Map<TransAttributeMeta, String> attributeValues = inputPluginMeta.getAttributeValues();
                String logicobjId;
                String dwInstanceId;
                for (TransAttributeMeta transAttributeMeta : attributeValues.keySet()) {
                    if (transAttributeMeta.getCode().equals(INPUTTABLELOGICOBJCODE)) {
                        logicobjId = attributeValues.get(transAttributeMeta);
                        logicObjs.add(logicobjId);
                        DataExportUtil.addMapList(transAndLogicMap,transId,logicobjId);
                    }
                    if (transAttributeMeta.getCode().equals(INPUTTABLESCHEMATECODE)) {
                        String schemeId = attributeValues.get(transAttributeMeta);
                        dwInstanceId = getDwInstanceId(schemeId);
                        dwInstances.add(dwInstanceId);
                        DataExportUtil.addMapList(transAndDwInstanceMap,transId,dwInstanceId);

                    }
                }
            }

            for (TransMeta outputPluginMeta : outputPluginMetas) {
                //拿到插件属性
                Map<TransAttributeMeta, String> attributeValues = outputPluginMeta.getAttributeValues();
                String logicobjId;
                String dwInstanceId;
                for (TransAttributeMeta transAttributeMeta : attributeValues.keySet()) {
                    //输出插件是rdb，没有跟目录有挂钩
                    if (transAttributeMeta.getCode().equals(OUTPUTTABLEMETACODE)) {
                        String rdbobjId = attributeValues.get(transAttributeMeta);
                    }
                    if (transAttributeMeta.getCode().equals(OUTPUTTABLEDWINSTANCECODE)) {
                        dwInstanceId = attributeValues.get(transAttributeMeta);
                        dwInstances.add(dwInstanceId);
                        DataExportUtil.addMapList(transAndDwInstanceMap,transId,dwInstanceId);
                    }
                }
            }

            Set<TransMeta> children = transMeta.getChildren();
            for(TransMeta c : children){
                TransPluginMeta usedPlugin = c.getUsedPlugin();
                if("cicadaCodeTableConversion".equals(usedPlugin.getCode())){
                    Set<TransExpMeta> transExps = c.getTransExps();
                    for(TransExpMeta transExpMeta : transExps){
                        Map<ElementNode, String> expValues = transExpMeta.getExpValues();
                        for(Map.Entry entry : expValues.entrySet()){
                            ElementNode node = (ElementNode)entry.getKey();
                            if(node.getCode().equals("relationTableId")){
                                logicObjs.add((String) entry.getValue());
                            }
                        }
                    }
                }
            }

        }

        transDatasetAndDatasource.setLogicObjs(getOwnerIds(new ArrayList<>(logicObjs),userId));
        transDatasetAndDatasource.setDwInstances(getOwnerIds(new ArrayList<>(dwInstances),userId));
        transDatasetAndDatasource.setTransAndLogicMap(transAndLogicMap);
        transDatasetAndDatasource.setTransAndDwInstanceMap(transAndDwInstanceMap);
        return transDatasetAndDatasource;
    }


    private String getDwInstanceId(String schemeId) {
        String sql = "select dw_db_instance_id from t_dw_db_mapping where deployed_software=:schemeId";
        List<Map<String, String>> list = this.baseDao.sqlQueryForList(sql, addParam("schemeId", schemeId).param());
        return list.size() > 0 ? list.get(0).get("dw_db_instance_id") : "";
    }



    @Data
    public static class TransDatasetAndDatasource {
        //方案里面的数据源
        private Set<String> dwInstances = new HashSet<>();
        //方案里面的数据集
        private Set<String> logicObjs = new HashSet<>();

        private Map<String,List<String>> transAndLogicMap = new HashMap<>();
        private Map<String,List<String>> transAndDwInstanceMap = new HashMap<>();

        public  void addLogicObjs(Set<String> logicObjResources){
            logicObjs.addAll(logicObjResources);
        }
        public  void addDwInstances(Set<String> dwInstances){
            this.dwInstances.addAll(dwInstances);
        }

    }


    private List<BaseBusiClassify> putSonfClassify(String busiclassifyId, List<BaseBusiClassify> baseBusiClassifies) {
        String sql = "select * from t_md_busi_classify where parent_classify_id=:busiclassifyId";
        List<Map<String, String>> list = this.baseDao.sqlQueryForList(sql, addParam("busiclassifyId", busiclassifyId).param());
        for (Map<String, String> map : list) {
            BaseBusiClassify baseBusiClassify = (BaseBusiClassify) this.baseDao.get(BaseBusiClassify.class, map.get("id"));
            baseBusiClassifies.add(baseBusiClassify);
            if (checkHaveSon(baseBusiClassify.getId())) {
                putSonfClassify(baseBusiClassify.getId(), baseBusiClassifies);
            }
        }
        return baseBusiClassifies;
    }

    private boolean checkHaveSon(String busiclassifyId) {
        String sql = "select id from t_md_busi_classify where parent_classify_id=:busiclassifyId";
        List<Map<String, String>> list = this.baseDao.sqlQueryForList(sql, addParam("busiclassifyId", busiclassifyId).param());
        return list.size() > 0 ? true : false;
    }

    private List<BaseBusiClassify> putParantClassify(BaseBusiClassify baseBusiClassify, List<BaseBusiClassify> baseBusiClassifies) {
        if (baseBusiClassify.getParentBc() != null) {
            putParantClassify(baseBusiClassify.getParentBc(), baseBusiClassifies);
        }
        baseBusiClassifies.add(baseBusiClassify);
        return baseBusiClassifies;
    }


    private List<Map<String, String>> getMetaLogicObjInfo(String dirId, boolean isSTD) {
        String sb = "select " +
                " b.id " +
                "from " +
                " t_md_logic_data_relation a  " +
                "left join  " +
                " t_md_logic_dataobj b " +
                "on a.logic_data_obj_id = b.id  " +
                "where " +
                " a.element_id in( " + getInstanceIdSQL(isSTD) + " ) " +
                " and relation_type = '0' " +
                " and b.operate_user_id = '40289754739d4e7e11729d4e682b2020'";
        Map<String, String> param = Maps.newHashMap();
        param.put("dir_id", dirId);
        return baseDao.sqlQueryForList(sb, param);
    }

    private String getInstanceIdSQL(boolean isSTD) {
        if (isSTD) {
            return "select " +
                    "   id " +
                    "  from " +
                    "   t_md_dw_db_instance " +
                    "  where " +
                    "   dir_id =:dir_id";
        }
        return "select element_id from t_md_classify_element where busi_classify_id in (select id from t_md_busi_classify where owner_id in (select id from t_md_busi_classify where busi_dir_id = :dir_id and owner_id is null and name != '集市层'))";
    }

    private String getDirIdByClassifyId(String classifyId) {
        String sql = "select busi_dir_id from t_md_busi_classify where id =:classifyId";
        Map<String, String> param = Maps.newHashMap();
        param.put("classifyId", classifyId);
        String dirId = baseDao.sqlQueryForValue(sql, param);
        return dirId;
    }


    public List<Map<String, Object>> queryDataTable(String dirId) {

        Map<String, String> dataTable = getDataTable(dirId);
        List<Map<String, Object>> value = new ArrayList<>();
        if (dataTable.size() > 0) {
            value = getValue(dataTable);
        }
        return value;
    }


    public List<Map<String, Object>> getValue(Map<String, String> map) {
        StringBuffer sql = new StringBuffer();
        sql.append("select * from t_md_classify_element t1 left join t_md_dw_db_instance t2 on t1.element_id = t2.id where ");
        int flag = 0;
        for (String key : map.keySet()) {
            if (flag == 0) {
                sql.append(" (t1.busi_classify_id = '" + key + "'");
                flag++;
            } else sql.append(" or t1.busi_classify_id = '" + key + "'");
        }
        sql.append(") ");
        sql.append(" order by t2.operate_time desc");
        return this.baseDao.sqlQueryForList(sql.toString());
    }


    public Map<String, String> diguiChidren(BaseBusiClassify busiClassify) {
        Map<String, String> map = new HashMap<>();
        if (busiClassify.getElements().size() > 0) {
            map.put(busiClassify.getId(), busiClassify.getName());
        }
        for (BaseBusiClassify classify : busiClassify.getBusiClassifies()) {
            //classify.setName(busiClassify.getName() + "/" + classify.getName());
            map.putAll(diguiChidren(classify));
        }
        return map;
    }

    public Map<String, String> getDataTable(String id) {
        Map<String, Object> params = new HashMap<>();
        Map<String, String> map = new HashMap<>();
        StringBuffer sql = new StringBuffer();
        sql.append("FROM BaseBusiClassify WHERE id = :id "); //获得此目录下所有的孩子
        params.put("id", id);
        BaseBusiClassify busiClassify = (BaseBusiClassify) this.baseDao.queryForObject(sql.toString(), params);
        if (busiClassify == null) {
            sql = new StringBuffer();
            sql.append("FROM BaseBusiDir WHERE id = :id");
            BaseBusiDir baseBusiDir = (BaseBusiDir) this.baseDao.queryForObject(sql.toString(), params);
            List<BaseBusiClassify> baseBusiClassifies = baseBusiDir.getBusiClassifys()
                    .stream()
                    .filter(b -> (Objects.isNull(b.getOwnerId())) || (baseBusiDir.getId().equals(b.getOwnerId())))
                    .collect(Collectors.toList());
            for (BaseBusiClassify classify : baseBusiClassifies) {
                //classify.setName(baseBusiDir.getName() + '/' + classify.getName());
                map.putAll(diguiChidren(classify));
            }
            return map;
        }
        map = diguiChidren(busiClassify);
        return map;
    }


    @Override
    public List<DataTransImportExportController.DataSourceBean> buildDataSourceBean(String datasourceDirId, String userId) {

        List<Map<String, Object>> mapList = queryDataTable(datasourceDirId);
        List<String> dwInstanceId = mapList.stream().map(s -> s.get("id").toString()).collect(Collectors.toList());

        List<DataTransImportExportController.DataSourceBean> dataSourceBeanList = new ArrayList<>();

        for (String datasourceId : dwInstanceId) {
            dataSourceBeanList.add(getDataSource(datasourceId, userId));
        }


        return dataSourceBeanList;
    }


    private List<Map<String, Object>> buildSysauth(String rdbobjIds, String userId) {
        List<Map<String, Object>> list = new ArrayList<>();
        if (rdbobjIds.length() != 0) {
            String sql = "select g.* from (select * from t_md_rdb_dataobj where id in(" + rdbobjIds + ")) f ,t_sys_auth_obj_func g where g.func_code = f.id and g.obj_id =:userId";
            list = this.baseDao.sqlQueryForList(sql, addParam("userId", userId).param());
        }
        return list;
    }

    private List<Map<String, Object>> buildSysfunc(String rdbobjIds, String userId) {
        List<Map<String, Object>> list = new ArrayList<>();
        if (rdbobjIds.length() != 0) {
            String sql = "select h.* from (select * from t_md_rdb_dataobj where id in(" + rdbobjIds + ")) f,t_sys_auth_obj_func g,t_sys_func h where h.func_code = g.func_code  AND g.func_code = f.id and g.obj_id =:userId";
            list = this.baseDao.sqlQueryForList(sql, addParam("userId", userId).param());
        }
        return list;
    }

    private List<Map<String, Object>> buildRdbDataColumns(List<Map<String, Object>> rdblist) {
        List<Map<String, Object>> list = new ArrayList<>();

        for (Map<String, Object> stringObjectMap : rdblist) {
            if (stringObjectMap.get("id") != null && StringUtils.isNotBlank(stringObjectMap.get("id").toString())) {
                List<Map<String, Object>> mapList = queryRdbColumnsbyRdbObjId(stringObjectMap.get("id").toString());
                list.addAll(mapList);
            }
        }
        return list;
    }

    private List<Map<String, Object>> queryRdbColumnsbyRdbObjId(String rdbobjId) {
        String sql = "select * from t_md_rdb_datacolumn where db_obj_id=:rdbobjId";
        List<Map<String, Object>> list = this.baseDao.sqlQueryForList(sql, addParam("rdbobjId", rdbobjId).param());

        return list;
    }


    private List<Map<String, Object>> buildDwTableMappings(String instanceId) {
        String sql = "select * from t_dw_table_mapping where dw_db_id =:instanceId";
        List<Map<String, Object>> list = this.baseDao.sqlQueryForList(sql, addParam("instanceId", instanceId).param());
        return list;
    }

    private List<Map<String, Object>> buildRdbDataObjsByIntanceId(String instanceId) {
        String sql = "select * from t_md_rdb_dataobj where id in (select classifier_stat_id from t_dw_table_mapping where dw_db_id =:instanceId)";
        List<Map<String, Object>> list = this.baseDao.sqlQueryForList(sql, addParam("instanceId", instanceId).param());
        return list;
    }


    /**
     * logic数据集字段类型更新
     *
     * @param dataColumns
     * @param logicDataObj
     * @return
     */
    private List<LogicDataColumn> transLogicColumnDataType(LogicDataObj logicDataObj, List<LogicDataColumn> dataColumns) {
        List<LogicDataColumn> logicDataColumnList = Lists.newArrayList();
        TypeMapping typeMapping = (TypeMapping) beanFactory.getBean("typeMapping");
        if (CollectionUtils.isNotEmpty(dataColumns)) {
            for (LogicDataColumn logicDataColumn : dataColumns) {
                LogicDataColumn dataColumn = new LogicDataColumn();
                BeanUtil.copyProperties(logicDataColumn, dataColumn);
                NonStandardType nonStandardType = DataSetUtils.changType(logicDataObj.getDbType(), logicDataColumn.getDataType().getCode());
                if (!(dataColumn.getDataType().getName().split("\\.")[0].startsWith("std"))) {
                    StandardType trans = typeMapping.trans(nonStandardType);
                    dataColumn.setDataType(getStandDataTypeByCode(trans.getCode()));
                }
                logicDataColumnList.add(dataColumn);
            }
        }
        return logicDataColumnList;
    }

    private DataType getStandDataTypeByCode(String code) {
        String sql = "select t2.* from t_md_type_system t1 left join t_md_element t2 on t1.id = t2.owner_id where t1.code = 'STANDARD' and  t2.code  = '" + code + "'";
        List<Map> list = this.baseDao.sqlQueryForList(sql);
        if (CollectionUtils.isNotEmpty(list)) {
            DataType dataType = new DataType();
            dataType.setCode(list.get(0).get("code").toString());
            dataType.setId(list.get(0).get("id").toString());
            return dataType;
        }
        return null;
    }

    @Override
    public void deleteDataByClassifyId(DataExportVo dataExportVo) {
        List<String> transClassifyIds = dataExportVo.getTransClassifyIds();
        //删除目录与方案
        for (String transClassifyId : transClassifyIds) {
            if (!isExistClassify(transClassifyId)) continue;
            myModelServiceImpl.deleteMarkModelByBusiClassifyId(transClassifyId); //删除模型市场数据
            this.transformApiService.deleteTransClassify(transClassifyId);
            //删除输出插件创建的数据集
            deleteTransLogicData(transClassifyId);
        }
        //删除目录与数据集
        List<String> dataSetClassifyIds = dataExportVo.getDataSetClassifyIds();
        if (CollectionUtils.isNotEmpty(dataSetClassifyIds)) {
            BaseBusiClassify classify = (BaseBusiClassify) this.baseDao.get(BaseBusiClassify.class, dataSetClassifyIds.get(0));
            for (String dateSetClassifyId : dataSetClassifyIds) {
                if (!isExistClassify(dateSetClassifyId)) continue;
                dataSetOperationService.deleteDataSetTreeNode(dateSetClassifyId, classify.getOperateUserId());
            }
        }

        //删除目录与数据源
        List<String> dataSourceClassifyIds = dataExportVo.getDataSourceClassifyIds();
        for (String dataSourceClassifyId : dataSourceClassifyIds) {
            if (!isExistClassify(dataSourceClassifyId)) continue;
            //删除数据对象
            recursiveDeleteDbWarehouse(dataSourceClassifyId);
            //删除数据仓库

            //删除数据源目录
            dataSetTreeService.deleteTreeNode(dataSourceClassifyId);
        }
    }


    @Override
    public void deleteImportDataResource(Set<String> transIds, Set<String> logicIds, Set<String> rdbIds) {
        //删除方案
        for (String transId : transIds) {
            if (isExistTrans(transId)) {
                myModelServiceImpl.deleteMarkModelByTransId(transId); //删除模型市场数据
                transformApiService.deleteTrans(transId);
            }
        }
        //删除数据集
        for (String logicId : logicIds) {
            if(!isExistLogic(logicId)) continue;
            dataSetOperationService.deleteDataSet(logicId);
        }
        //删除rdb表
        deleteBatchTable(new ArrayList<>(rdbIds), "");
    }

    private void deleteTransAttribute(String transId){
       if(StringUtils.isNotBlank(transId)){
           String sql =" delete from t_etl_trans_attribute where trans_id=:transId";
           this.baseDao.executeSqlUpdate(sql,addParam("transId",transId).param());
       }
    }

    private boolean isExistTrans(String transId) {
        String sql = "select id from t_etl_trans where id =:transId";
        List list = this.baseDao.sqlQueryForList(sql, addParam("transId", transId).param());
        return CollectionUtils.isNotEmpty(list);
    }

    private boolean isExistLogic(String logicId) {
        String sql = "select id from t_md_logic_dataobj where id =:logicId";
        List list = this.baseDao.sqlQueryForList(sql, addParam("logicId", logicId).param());
        return CollectionUtils.isNotEmpty(list);
    }
    private boolean isExistRdb(String rdbId) {
        String sql = "select id from t_md_rdb_dataobj where id =:rdbId";
        List list = this.baseDao.sqlQueryForList(sql, addParam("rdbId", rdbId).param());
        return CollectionUtils.isNotEmpty(list);
    }

    private void deleteTransLogicData(String transClassifyId) {
        //导出方案
        //通过目录找到方案集合
        if (StringUtils.isNotBlank(getUserIdByClassifyId(transClassifyId))) {
            List<String> transMetaList = getTransMetaListByClassifyId(transClassifyId, getUserIdByClassifyId(transClassifyId));
            List<LogicDataObjInfo> transOutResult = getTransOutResult(transMetaList);
            if (CollectionUtils.isNotEmpty(transOutResult)) {
                for (LogicDataObjInfo logicDataObjInfo : transOutResult) {
                    if (logicDataObjInfo.getLogicDataObj() == null) continue;
                    dataSetOperationService.deleteDataSet(logicDataObjInfo.getLogicDataObj().getId());
                }
            }
        }
    }

    private String getUserIdByClassifyId(String transClassifyId) {
        String sql = "select operate_user_id from t_md_busi_classify where id=:transClassifyId";
        return this.baseDao.sqlQueryForValue(sql, addParam("transClassifyId", transClassifyId).param());

    }

    private void recursiveDeleteDbWarehouse(String dataSourceClassifyId) {
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageIndex(1);
        pageInfo.setPageSize(defaultPageSize);
        PageInfo dataSet = dataWareTreeService.queryDataTable(pageInfo, dataSourceClassifyId, "", "");
        if (dataSet == null) return;
        //获取数据仓库信息
        List<Map> dataList = dataSet.getDataList();
        if (CollectionUtils.isNotEmpty(dataList)) {
            for (Map map : dataList) {
                String dbId = (String) map.get("id");
                //删除数据表
                deleteBatchTable(queryDbTableByDbId(dbId), dataSourceClassifyId);
                this.baseDao.getSession().flush();

                //删除数据源仓库
                this.deleteDataSourceDB(dbId);
            }
        }

    }


    private void deleteBatchTable(List<String> tableIds, String classifyId) {
        if (CollectionUtils.isEmpty(tableIds)) return;
        for (String tableId : tableIds) {
            if(!isExistRdb(tableId)) continue;
            deleteTable(tableId, classifyId);
        }
    }

    private void deleteDataSourceDB(String dBInstanceId) {
        if (StringUtils.isBlank(dBInstanceId)) return;
        //删除数据源实例
        if (dataWarehouseService.checkTable(dBInstanceId)) {
            throw new RuntimeException("该数据源存在数据对象，无法删除");
        }
        boolean deletePhysics = dataWarehouseService.deleteDataWarehouseDBInstance(dBInstanceId, null);
    }

    private void deleteTable(String tableId, String classifyId) {
        List<String> list = Lists.newArrayList();
        list.add(tableId);
        if (dataWarehouseService.isDataWareHouse(list)) {
            throw new RuntimeException("其他仓库有使用到该表，无法删除！");
        }
        if (dataWareTreeService.isBuildModel(list)) {
            throw new RuntimeException("该表已被用于流程建模，无法删除！");
        }
        if (dataWareTreeService.isBuildDMCModel(list)) {
            throw new RuntimeException("该表已被用于自助建模，无法删除！");
        }
        if (dataWareTreeService.isBuildVisualModel(list)) {
            throw new RuntimeException("该表已被用于可视化建模，无法删除！");
        }
        //删除此表跟角色和用户关系
        Map<String, String> functionRelationParams = Maps.newHashMap();
        functionRelationParams.put("func_code", tableId);
        List<TSysAuthObjFunc> tSysAuthObjFuncs = sysAuthObjFuncService.queryList(functionRelationParams);
        for (TSysAuthObjFunc tSysAuthObjFunc : tSysAuthObjFuncs) {
            sysAuthObjService.deleteAuthObj(tSysAuthObjFunc);
        }

        //删除注册到功能表的数据对象
        Map<String, String> functionParams = Maps.newHashMap();
        functionParams.put("func_code", tableId);
        TSysFuncBase tSysFuncBase = (TSysFuncBase) sysFuncService.query(functionParams);
        if (null != tSysFuncBase) {
            sysAuthObjService.deleteAuthObj(tSysFuncBase);
        }
        //删除引用大数据管家的数据
        //删除此表跟角色和用户关系
        Map<String, String> functionRelationParamsCode = Maps.newHashMap();
        functionRelationParamsCode.put("func_code", classifyId);
        List<TSysAuthObjFunc> tSysAuthObjFuncsCode = sysAuthObjFuncService.queryList(functionRelationParamsCode);
        if (CollectionUtils.isNotEmpty(tSysAuthObjFuncsCode)) {
            for (TSysAuthObjFunc tSysAuthObjFunc : tSysAuthObjFuncsCode) {
                sysAuthObjService.deleteAuthObj(tSysAuthObjFunc);
            }
        }

        Map<String, String> functionParamsCode = Maps.newHashMap();
        functionParamsCode.put("func_code", classifyId);
        TSysFuncBase tSysFuncBaseCode = (TSysFuncBase) sysFuncService.query(functionParamsCode);
        if (null != tSysFuncBaseCode) {
            sysAuthObjService.deleteAuthObj(tSysFuncBaseCode);
        }
        dataWarehouseService.deleteTable(tableId, "", "");
    }

    private List<String> queryDbTableByDbId(String dbId) {
        StringBuffer stringBuffer = new StringBuffer("SELECT ");
        stringBuffer.append(" id FROM")
                .append(" t_dw_table_mapping WHERE dw_db_id IN ( SELECT ID FROM")
                .append(" t_md_dw_db_instance  WHERE ID =:dbId )");
        List<Map> list = this.baseDao.sqlQueryForList(stringBuffer.toString(), addParam("dbId", dbId).param());
        List<String> tableIds = list.stream().map(s -> (String) s.get("id")).collect(Collectors.toList());
        return tableIds;
    }

    @Override
    public void updateUserInfo(String newUserId, List<String> elementUserIds, List<String> stepUserIds) {
        List list = this.baseDao.sqlQueryForList("select * from T_SYS_AUTH_OBJ where id =:newUserId", addParam("newUserId", newUserId).param());
        if (CollectionUtils.isEmpty(list)) return;
        if (CollectionUtils.isNotEmpty(elementUserIds)) {
            String sql = "update t_md_element  set operate_user_id = '%s'  where id in(%s) ";
            this.baseDao.executeSqlUpdate(String.format(sql, newUserId, buildSql(elementUserIds)));
        }

        if (CollectionUtils.isNotEmpty(stepUserIds)) {
            String sql = "update T_DATASET_STEP  set user_id = '%s'  where id in(%s) ";
            this.baseDao.executeSqlUpdate(String.format(sql, newUserId, buildSql(stepUserIds)));
        }

    }


    private String buildSql(List<String> elementIds) {
        StringBuilder stringBuilder = new StringBuilder();
        for (String elementId : elementIds) {
            stringBuilder.append("'").append(elementId).append("'");
            stringBuilder.append(",");
        }
        stringBuilder.deleteCharAt(stringBuilder.length() - 1);
        return stringBuilder.toString();
    }

    private String queryPluginPartitions() {
        String sql = "select * from T_MD_ETL_TRANS_PLUGIN_PARTITION where transplugin_id =:transplugin_id";
        return sql;
    }

    private String queryPluginRelations() {
        return "select * from T_MD_ETL_PLUGIN_EXP where transplugin_id =:transplugin_id";
    }

    //查询 插件的属性 比如 set  attribute
    private String queryPluginExpNodes() {
        return "select * from T_MD_EXP_NODE where exp_node_id in\n" +
                " (select exp_node_id from T_MD_ETL_PLUGIN_EXP where  transplugin_id=:transplugin_id)\n" +
                "UNION \n" +
                "select t1.* from T_MD_EXP_NODE t1 where t1.exp_node_id in (select child_exp_node_id from \n" +
                "\tT_MD_EXP_RELATION where parent_exp_node_id in ( (select exp_node_id from T_MD_ETL_PLUGIN_EXP \n" +
                "where  transplugin_id=:transplugin_id)))";
    }

    private String queryPluginTransExp() {
        return "select * from T_ETL_TRANS_EXP where exp_node_id in (select exp_node_id from T_MD_ETL_PLUGIN_EXP where  transplugin_id=:transplugin_id)";
    }


    private List<Map<String, Object>> buildTransPluginPartitions(List<Map<String, Object>> transPlugins, String queryCode) {
        List<Map<String, Object>> transPluginPartitions = Lists.newArrayList();
        for (Map map : transPlugins) {
            Map<String, String> param = Maps.newHashMap();
            param.put("transplugin_id", (String) map.get("id"));
            String sql = "";
            if ("partition".equalsIgnoreCase(queryCode))
                sql = queryPluginPartitions();
            else if ("relation".equalsIgnoreCase(queryCode))
                sql = queryPluginRelations();
            else if ("expNode".equalsIgnoreCase(queryCode))
                sql = queryPluginExpNodes();
            else if ("transExp".equalsIgnoreCase(queryCode))
                sql = queryPluginTransExp();
            else if ("attribute".equalsIgnoreCase(queryCode))
                sql = queryPluginTransAttributes();
            else if ("expNodeRelation".equalsIgnoreCase(queryCode)) {
                sql = queryPluginExpNodeRelations();
            }
            List list = baseDao.sqlQueryForList(sql, param);
            if (CollectionUtils.isNotEmpty(list)) {
                Map<String, Object> transPartition = Maps.newHashMap();
                transPartition.put((String) map.get("id"), list);
                transPluginPartitions.add(transPartition);
            }
        }
        return transPluginPartitions;
    }

    //查询插件属性之间的关系  比如  set下面的属性
    private String queryPluginExpNodeRelations() {
        return "select * from \n" +
                "\tT_MD_EXP_RELATION where parent_exp_node_id in ( (select exp_node_id from T_MD_ETL_PLUGIN_EXP \n" +
                "where  transplugin_id=:transplugin_id))";
    }


    private String queryPluginTransAttributes() {
        return "select * from t_md_etl_trans_attribute where owner_id=:transplugin_id";
    }

    private String collectParams(List<Map<String, Object>> arrayList, String code) {
        String[] params = new String[arrayList.size()];
        for (int i = 0; i < arrayList.size(); i++) {
            Map<String, Object> map = arrayList.get(i);
            String param = (String) map.get(code);
            if (StringUtils.isNotBlank(param)) {
                params[i] = "'" + param + "'";
            }
        }

        // 3. 通过源数据集id，找到所有源数据集与数仓目录的挂接关系
        String condition = String.join(",", params);
        return condition;
    }

    private List<Map<String, Object>> filterMap(List<Map<String, Object>> repeatList, String id) {
        Map<String, Map> msp = new HashMap<>();
        List<Map<String, Object>> listMap = Lists.newArrayList();
        for (Map map : repeatList) {
            String key = (String) map.get(id);
            msp.put(key, map);
        }
        Set<String> mspKey = msp.keySet();
        for (String key : mspKey) {
            if (key == null)
                continue;
            Map newMap = msp.get(key);
            listMap.add(newMap);
        }
        return listMap;
    }

    private List<Map<String, Object>> buildTransPlugins(String transId) {
        String sql = "select * from t_md_etl_trans_plugin\n" +
                " where id in (select transplugin_id from t_etl_trans where id in (select child_trans_id from T_ETL_TRANS_STEPDETAIL where trans_id =:transId))";
        Map<String, String> param = Maps.newHashMap();
        param.put("transId", transId);
        return baseDao.sqlQueryForList(sql, param);
    }

    private List<Map<String, Object>> buildTransCollisionPlugins(List<String> collisionPlugins) {
        String sql = "select * from t_md_etl_trans_plugin where code in (%s)";
        return baseDao.sqlQueryForList(String.format(sql, String.join(",", collisionPlugins)));
    }

    public void buildScheduleInfos(String transId, List<String> transTasks, List<String> transSchedules, List<String> transSubTransRelations) {
        // 导出方案对应的调度配置信息
        Map<String, String> param = Maps.newHashMap();
        param.put("trans_id", transId);
        Map taskMap = baseDao.sqlQueryForMap("select * from t_trans_task where trans_id = :trans_id", param);
        if (taskMap.size() > 0) {
            transTasks.add(JSON.toJSONString(taskMap));
        }
        Map scheduleMap = baseDao.sqlQueryForMap("select * from t_trans_schedule where trans_id = :trans_id", param);
        if (scheduleMap.size() > 0) {
            transSchedules.add(JSON.toJSONString(scheduleMap));
        }
        List<Map> relationList = baseDao.sqlQueryForList("select * from t_trans_subtrans_relation where trans_id = :trans_id", param);
        for (Map relationMap : relationList) {
            if (relationMap.size() > 0) {
                transSubTransRelations.add(JSON.toJSONString(relationMap));
            }
        }
    }

    public void deleteScheduleByTransId(String transId) {
        Map<String, String> param = Maps.newHashMap();
        param.put("transId", transId);
        this.baseDao.executeSqlUpdate("delete from t_trans_task   where trans_id = :transId", param);
        this.baseDao.executeSqlUpdate("delete from t_trans_schedule   where trans_id = :transId", param);
        this.baseDao.executeSqlUpdate("delete from t_trans_subtrans_relation   where trans_id = :transId", param);

    }

    @Override
    public boolean isExistClassify(String classifyId) {
        String sql = "select * from t_md_busi_classify where id=:classifyId";
        List list = this.baseDao.sqlQueryForList(sql, addParam("classifyId", classifyId).param());
        return CollectionUtils.isNotEmpty(list);
    }

    @Override
    public BaseBusiClassify findBaseClassifyByNameAndUserId(String name, String userId, String code) {
        String hql = "from  BaseBusiClassify where operateUserId=:userId and name =:name and code=:code";
        return (BaseBusiClassify) this.baseDao.queryForObject(hql, addParam("userId", userId).addParam("name", name).addParam("code", code).param());
    }

    @Override
    public BaseBusiDir findBaseDirByNameAndUserId(String name, String userId, String code) {
        if (StringUtils.isNotBlank(userId)) {
            String hql = " from BaseBusiDir where name=:name and code=:code and operateUserId=:userId";
            return (BaseBusiDir) this.baseDao.queryForObject(hql, addParam("userId", userId).addParam("name", name).addParam("code", code).param());
        } else {
            String hql = " from BaseBusiDir where name=:name and code=:code ";
            return (BaseBusiDir) this.baseDao.queryForObject(hql, addParam("name", name).addParam("code", code).param());
        }
    }

    @Override
    public void saveLogicInfo(List<LogicDataObjInfo> dataObjInfos) {
        for (LogicDataObjInfo dataObjInfo : dataObjInfos) {
            LogicDataObj logicDataObj = dataObjInfo.getLogicDataObj();
            try {
                //保存逻辑数据集
                // logicDataObj.setId(dataLogicIdMaps);
                logicDataObj.setOwner(null);
                addElementUserId(logicDataObj.getId());

                if (StringUtils.isNotBlank(logicDataObj.getOwnerId())) {
                    ModelElement modelElement = dataSetStepMetaService.get(ModelElement.class, logicDataObj.getOwnerId());
                    if (modelElement != null)
                        logicDataObj.setOwner(modelElement);
                }
                logicDataObjService.saveLogicDataObj(logicDataObj);
                logicDataObj.setOperateTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));


                //保存数据集字段
                List<LogicDataColumn> dataColumns = dataObjInfo.getDataColumns();
                for (LogicDataColumn dataColumn : dataColumns) {
                    dataColumn.setOwner(logicDataObj);
                    LogicDataColumn logicDataColumn = dataSetStepMetaService.get(LogicDataColumn.class, dataColumn.getId());
                    if (logicDataColumn == null) {
                        addElementUserId(dataColumn.getId());
                        dataSetStepMetaService.save(dataColumn);
                    }
                }

                //保存数据集之间的关系
                List<Map<String, String>> dataSetRelation = dataObjInfo.getDataSetRelation();

                for (Map<String, String> map : dataSetRelation) {

                    String insertSql = "INSERT INTO public.t_md_logic_data_relation\n" +
                            "(logic_data_obj_id, element_id, relation_type)\n" +
                            "VALUES(?, ?, ?);\n";
                    List<Object[]> params = new ArrayList<>();
                    String logicDataId = map.get("logic_data_obj_id");
                    // if (!orginal) logicDataId = logicDataObj.getId();

                    params.add(new Object[]{logicDataId, map.get("element_id"), "1"});
                    this.baseDao.batchInsert(insertSql, params);
                }

                //保存数据集的步骤
                DataSetStepRelation dataSetStepRelation = dataObjInfo.getDataSetStepRelation();
                if (dataSetStepRelation != null) {
                    for (DataSetStep step : dataSetStepRelation.getSteps()) {
                        step.setDataSet(logicDataObj);
                    }
                    dataSetStepRelation.setId(null);
                    Set<DataSetStep> steps = dataSetStepRelation.getSteps();
                    dataSetStepRelation.setSteps(null);
                    dataSetStepMetaService.save(dataSetStepRelation);
                    if (steps != null) {
                        for (DataSetStep step : steps) {
                            DataSetStepMeta dataSetStepMeta = step.getDataSetStepMeta();
                            String stepMetaCode = dataSetStepMeta.getCode();
                            DataSetStepMeta newSetStepMeta = dataSetStepMetaService.getStepMetaByCode(stepMetaCode);
                            Set<DataSetStepAttribute> dataSetStepAttributes = step.getDataSetStepAttributes();
                            step.setDataSetStepMeta(null);
                            step.setDataSetStepAttributes(null);
                            DataSetStep dataSetStep = dataSetStepMetaService.get(DataSetStep.class, step.getId());
                            if (dataSetStep == null) {
                                addStepUserId(step.getId());
                                dataSetStepMetaService.save(step);
                            }
                            step.setDataSetStepMeta(newSetStepMeta);

                            for (DataSetStepAttribute dataSetStepAttribute : dataSetStepAttributes) {
                                DataSetStepAttributeMeta dataSetStepAttributeMeta = dataSetStepAttribute.getDataSetStepAttributeMeta();

                                DataSetStepAttributeMeta newDataSetStepAttributeMete = dataSetStepMetaService.getStepMetaAttribute(newSetStepMeta.getId(), dataSetStepAttributeMeta.getCode());
                                dataSetStepAttribute.setDataSetStepAttributeMeta(null);
                                dataSetStepAttribute.setDataSetStep(null);
                                DataSetStepAttribute attribute = dataSetStepMetaService.get(DataSetStepAttribute.class, dataSetStepAttribute.getId());
                                if (attribute == null) {
                                    dataSetStepMetaService.save(dataSetStepAttribute);
                                }
                                dataSetStepAttribute.setDataSetStep(step);
                                dataSetStepAttributeMeta.setDataSetStepMeta(null);
                                dataSetStepAttributeMeta.setDataSetStepMeta(dataSetStepMeta);
                                dataSetStepAttribute.setDataSetStepAttributeMeta(newDataSetStepAttributeMete);
                            }
                            step.setDataSetStepAttributes(dataSetStepAttributes);
                            step.setDataSetStepRelation(dataSetStepRelation);
                        }
                    }
                    dataSetStepRelation.setSteps(steps);

                    logicDataObj.setStepRelationId(dataSetStepRelation.getId());
                }
                importCompleteSuccessResultVoList.add(ImportCompleteResultVo.success(logicDataObj.getId(), logicDataObj.getName(), LOGIC_TYPE));
            } catch (Exception e) {
             log.error(e.getMessage(),e);
                importCompleteFailResultVoList.add(ImportCompleteResultVo.fail(logicDataObj.getId(), logicDataObj.getName(), LOGIC_TYPE, e.getMessage()));
            }
        }
    }


    private List<Map<String, Object>> buildRdbDataObjsByRdbId(List<String> rdbIds) {
        if(CollectionUtils.isEmpty(rdbIds)) return new ArrayList<>();
        String sql = "select * from t_md_rdb_dataobj where id in ("+DataExportUtil.getCollectQuotesStr(rdbIds)+")";
        List<Map<String, Object>> list = this.baseDao.sqlQueryForList(sql);
        return list;
    }

    private List<Map<String, Object>> buildRdbColumnsByRdbObjIds(List<String> rdbObjIds) {
        if(CollectionUtils.isEmpty(rdbObjIds)) return new ArrayList<>();
        String sql = "select * from t_md_rdb_datacolumn where db_obj_id in ("+DataExportUtil.getCollectQuotesStr(rdbObjIds)+")";
        List<Map<String, Object>> list = this.baseDao.sqlQueryForList(sql);
        return list;
    }

    private List<Map<String, Object>> buildDwTableMappingByRdbIds(List<String> rdbObjIds) {
        if(CollectionUtils.isEmpty(rdbObjIds)) return new ArrayList<>();
        String sql = " select * from t_dw_table_mapping where classifier_stat_id in ("+DataExportUtil.getCollectQuotesStr(rdbObjIds)+")\n";
        List<Map<String, Object>> list = this.baseDao.sqlQueryForList(sql);
        return list;
    }

    @Override
    public List<DataTransImportExportController.DataSourceBean> buildDataSourceBeanByInstanceIds(List<String> dwInstanceIds, String userId) {
        List<DataTransImportExportController.DataSourceBean> dataSourceBeanList = new ArrayList<>();

        for (String datasourceId : dwInstanceIds) {
            dataSourceBeanList.add(getDataSource(datasourceId, userId));
        }
        return dataSourceBeanList;
    }

    private DataTransImportExportController.DataSourceBean getDataSource(String dwInstanceId, String userId) {
        DataTransImportExportController.DataSourceBean dataSourceBean = new DataTransImportExportController.DataSourceBean();
        dataSourceBean.setDatasourceId(dwInstanceId);
        //库实例
        DwDbInstance dbInstance = getDbInstanceById(dwInstanceId);
        List<Map<String, Object>> dbInstanceByIdSql = getDbInstanceByIdSql(dwInstanceId);
        //目录信息 我的空间(dir)-yecc1(classify)-yecc2(classify)
        //导出我的空间(dir)
        BaseBusiDir baseBusiDir = getBaseBusiDirById(dbInstance.getDirId());
        //将目录的setBusiClassifys 设置为null,他们的关系由BaseBusiClassify去管就好了，防止序列化失败
        if(baseBusiDir != null){
            baseBusiDir.setBusiClassifys(null);
        }
        //导出目录与资源的关系表
        DataTransImportExportController.ClassifyElement classifyElement = getClassifyElement(dwInstanceId);
        //导出yecc1(classify)-yecc2(classify)
        BaseBusiClassify baseBusiClassifiy = getBaseBusiClassifiy(classifyElement.getBusiClassifyId());
        //导出dwDbMapping
        DataTransImportExportController.DwDbMapping dwDbMapping = getDwDbMappingbyInstanceId(dwInstanceId);
        //导出rdbCatalog
        RdbCatalog rabCatalog = getRabCatalog(dbInstance.getSoftwareId());
        List<Map<String, Object>> rabCatalogSql = getRabCatalogSql(dbInstance.getSoftwareId());
        //导出Rdbschema
        List<Map<String, Object>> rdbSchema = getRdbSchemaSql(dwDbMapping.getDeployedSoftwore());
        //导出labelElement
        List<Map<String, Object>> labelElement = getLabelElement(rabCatalog.getId());
        //导出labelElement
        List<Map<String, Object>> rdbCatalogCluster = getRdbCatalogCluster(rabCatalog.getId());
        //导出Machine
        List<Map<String, Object>> machine = getMachine(rdbCatalogCluster.get(0).get("machine_id").toString());
        //导出相关联的Rdb相关信息,如果是创屏或者是从目录往下找的，才需要去加这rdb的信息
        if (isDeleteCatalogResources) {
            List<Map<String, Object>> rdblist = buildRdbDataObjsByIntanceId(dbInstance.getId());
            List<Map<String, Object>> dwtableList = buildDwTableMappings(dbInstance.getId());
            List<Map<String, Object>> rdbColumns = buildRdbDataColumns(rdblist);
            List<Map<String, Object>> sysFuncs = buildSysfunc(rdblist.stream().map(s -> "'" + s.get("id").toString() + "'").collect(Collectors.joining(",")), userId);
            List<Map<String, Object>> sysAuths = buildSysauth(rdblist.stream().map(s -> "'" + s.get("id").toString() + "'").collect(Collectors.joining(",")), userId);
            dataSourceBean.setRdbDataObjs(rdblist);
            dataSourceBean.setDwTableMappings(dwtableList);
            dataSourceBean.setRdbDataColumns(rdbColumns);
            dataSourceBean.setTSysFuncs(sysFuncs);
            dataSourceBean.setTSysAuthObjFuncs(sysAuths);
        }


        dataSourceBean.setDwDbInstance(dbInstanceByIdSql);
        dataSourceBean.setBaseBusiDir(baseBusiDir);
        dataSourceBean.setClassifyElement(classifyElement);
        dataSourceBean.setBaseBusiClassifie(baseBusiClassifiy);
        dataSourceBean.setDwDbMapping(dwDbMapping);
        dataSourceBean.setRdbCatalog(rabCatalogSql);
        dataSourceBean.setRdbSchema(rdbSchema);
        dataSourceBean.setBaseLabelElement(labelElement);
        dataSourceBean.setRdbCatalogCluster(rdbCatalogCluster);
        dataSourceBean.setMachine(machine);


        return dataSourceBean;

    }

    @Override
    public TransDatasetAndDatasource getDataSourceAndLogic(List<String> transIds, String userId) {
        //  List<String> transMetaList = getTransMetaListByClassifyId(transDirId, userId);
        //找到所有方案涉及的数据集，数据源  分享过来的数据源，跟数据集不要导出
        TransDatasetAndDatasource transDatasetAndSource = getTransDatasetAndSource(transIds,userId);
        transDatasetAndSource.getLogicObjs();
        transDatasetAndSource.getDwInstances();
        //找到输出插件的数据集
        Set<String> outputLogicIds = getOutputLogicIds(transIds);
        transDatasetAndSource.getLogicObjs().addAll(outputLogicIds);
        return transDatasetAndSource;

    }

    private  Set<String> getOwnerIds(List<String> objIds,String userId){
        if(CollectionUtils.isEmpty(objIds)) return new HashSet<>();
        String sql ="select * from t_md_element where id in("+DataExportUtil.getCollectQuotesStr(objIds)+") and operate_user_id = '"+userId+"'";
        List<Map> list = this.baseDao.sqlQueryForList(sql);
        Set<String> dataObjIds = list.stream().map(s -> (String) s.get("id")).collect(Collectors.toSet());
        return dataObjIds;
    }

    @Override
    public DataTransImportExportController.DataCenterRdbBean getRdbBeanByRdbIds(List<String> rdbIds, String userId) {
        DataTransImportExportController.DataCenterRdbBean dataCenterRdbBean = new DataTransImportExportController.DataCenterRdbBean();

        //查询rdb表数据
        List<Map<String, Object>> rdbObjList = buildRdbDataObjsByRdbId(rdbIds);
        List<Map<String, Object>> rdbColumnList = buildRdbColumnsByRdbObjIds(rdbIds);
        List<Map<String, Object>> tSysFuncList = buildSysfunc(getCollectQuotesStr(rdbIds), userId);
        List<Map<String, Object>> tSysAuthList = buildSysauth(getCollectQuotesStr(rdbIds), userId);
        List<Map<String, Object>> dwTableMappingList = buildDwTableMappingByRdbIds(rdbIds);
        dataCenterRdbBean.setRdbDataObjs(rdbObjList);
        dataCenterRdbBean.setRdbDataColumns(rdbColumnList);
        dataCenterRdbBean.setTSysFuncs(tSysFuncList);
        dataCenterRdbBean.setTSysAuthObjFuncs(tSysAuthList);
        dataCenterRdbBean.setDwTableMappings(dwTableMappingList);
        return dataCenterRdbBean;
    }

    public List<Map<String,Object>> queryTransVariableRelation(List<String> transIds){
        if(CollectionUtils.isEmpty(transIds)) return new ArrayList<>();
        String sql = "select * from t_md_trans_variable_relation where trans_id in ("+DataExportUtil.getCollectQuotesStr(transIds)+")";
        List<Map<String, Object>> list = this.baseDao.sqlQueryForList(sql);
        return list;
    }

    public List<Map<String,Object>> queryTransVariable(List<String> transIds){
        if(CollectionUtils.isEmpty(transIds)) return new ArrayList<>();
        String sql = "select * from T_MD_TRANS_VARIABLE where id in (\n" +
                "select variable_id from t_md_trans_variable_relation where trans_id in ("+DataExportUtil.getCollectQuotesStr(transIds)+"))";
        List<Map<String, Object>> list = this.baseDao.sqlQueryForList(sql);
        return list;
    }

    @Override
    public List<Map> getRdbIdsByLogicIds(Collection<String> logicIds) {
        if(CollectionUtils.isEmpty(logicIds)) return Lists.newArrayList();
        String sql = "select a.id,b.id logicid from t_md_rdb_dataobj a left join t_md_logic_dataobj b on a.id = b.owner_id\n" +
                "where b.id in ("+getCollectQuotesStr((List<String>) logicIds)+")";
        //还要再查数据集是自助数据集的情况
        List<Map> list = this.baseDao.sqlQueryForList(sql);

        String sqlLogic = "select logic_data_obj_id  logicid , element_id id from  t_md_logic_data_relation where logic_data_obj_id in ("+getCollectQuotesStr((List<String>) logicIds)+")";
        List<Map> listNew = this.baseDao.sqlQueryForList(sqlLogic);
        list.addAll(listNew);
        return list;
    }
    @Override
    public Set<String> queryLogicBySelf(List<String> logicIds){
        String sqlLogic = "select * from t_md_logic_dataobj where owner_id in (\n" +
                "select  element_id  from  t_md_logic_data_relation where logic_data_obj_id in (select id from t_md_logic_dataobj where id in ("+DataExportUtil.getCollectQuotesStr(logicIds)+") and belong_type ='SELF_HELP')\n" +
                " and relation_type is null ) and step_relation_id is null \n" +
                "and id not in ("+DataExportUtil.getCollectQuotesStr(logicIds)+")"; //确认这个目录树是属于数据仓库得
        List<Map> listNew = this.baseDao.sqlQueryForList(sqlLogic);
        Set<String> logicSelfIds = listNew.stream().map(s -> (String)s.get("id")).collect(Collectors.toSet());
        return logicSelfIds;

    }


    @Override
    public List<BaseBusiClassify> queryTransClassifyIdByTransId(List<String> transIds) {
        List<BaseBusiClassify> transClassifyList = Lists.newArrayList();
        List<String> classifyIdsByTransIds = getClassifyIdsByTransIds(transIds);
        for (String classifyId : classifyIdsByTransIds) {
            transClassifyList.addAll(findBusinessClassifyParent(classifyId));
        }
        return filterRepeat(transClassifyList);
    }

    @Override
    public List<BaseBusiClassify> queryLogicClassifyIdByLogicId(List<String> logicIds, String userId) {
        List<BaseBusiClassify> logicClassifyList = Lists.newArrayList();
        List<String> classifyIdsByTransIds = getClassifyIdsByLogicIds(logicIds);
        for (String classifyId : classifyIdsByTransIds) {
            logicClassifyList.addAll(findBusinessClassifyParent(classifyId));
        }
        return filterRepeat(logicClassifyList);
    }


    @Override
    public List<BaseBusiClassify> queryMyHouseClassifyIdByDwInstancesId(List<String> dwInstanceIds, String userId) {
        List<BaseBusiClassify> classifyMyHouseIdsByTrans = Lists.newArrayList();
        List<String> classifyMyHouseIdsByTransIds = getClassifyHouseByDwInstanceIdsAndDirCode(dwInstanceIds, "DATAWAREHOUSE_DIR", "我的空间");
        for (String classifyId : classifyMyHouseIdsByTransIds) {
            classifyMyHouseIdsByTrans.addAll(findBusinessClassifyParent(classifyId));
        }
        return filterRepeat(classifyMyHouseIdsByTrans);
    }

    @Override
    public List<BaseBusiClassify> queryDataWarehouseClassifyIdByDwInstancesId(List<String> dwInstanceIds, String userId) {

        List<BaseBusiClassify> classifyDataHouseIdsByTrans = Lists.newArrayList();
        List<String> classifyDataHouseIdsByTransIds = getClassifyHouseByDwInstanceIdsAndDirCode(dwInstanceIds, "DATAWAREHOUSE_DIR", "数据仓库");
        for (String classifyId : classifyDataHouseIdsByTransIds) {
            classifyDataHouseIdsByTrans.addAll(findBusinessClassifyParent(classifyId));
        }
        return filterRepeat(classifyDataHouseIdsByTrans);
    }

    private List<BaseBusiClassify> findBusinessClassifyParent(String classifyId) {
        List<BaseBusiClassify> baseBusinessClassifies = new ArrayList<>();
        BaseBusiClassify baseBusiClassify = (BaseBusiClassify) this.baseDao.get(BaseBusiClassify.class, classifyId);
        //添加父类的
        List<BaseBusiClassify> businessClassifies = putParantClassify(baseBusiClassify, baseBusinessClassifies);
        //清空儿子的
        businessClassifies.stream().forEach(s -> s.setBusiClassifies(null));
        return filterRepeat(businessClassifies);
    }


    private List<String> getClassifyIdsByTransIds(List<String> transIds) {
        String sql = "select id from t_md_busi_classify where id in (select busi_classify_id from t_md_classify_element where element_id in (" + getCollectQuotesStr(transIds) + "))";
        List<Map> list = this.baseDao.sqlQueryForList(sql);
        List<String> classifyIds = list.stream().map(s -> (String) s.get("id")).collect(Collectors.toList());
        return classifyIds;
    }

    private List<String> getClassifyIdsByLogicIds(List<String> logicIds) {
        if(CollectionUtils.isEmpty(logicIds)) return Lists.newArrayList();
        String sql = "select * from t_md_busi_classify where id in (select busi_classify_id from  t_md_classify_element where  element_id in (" + getCollectQuotesStr(logicIds) + "))";
        List<Map> list = this.baseDao.sqlQueryForList(sql);
        List<String> classifyIds = list.stream().map(s -> (String) s.get("id")).collect(Collectors.toList());
        return classifyIds;
    }

    private List<String> getClassifyHouseByDwInstanceIdsAndDirCode(List<String> dwInstanceIds, String dirCode, String dirName) {
        if(CollectionUtils.isEmpty(dwInstanceIds)) return Lists.newArrayList();
        String sql = "select classify.* from t_md_busi_classify classify\n" +
                "left join t_md_busi_dir dir  on classify.busi_dir_id = dir.id\n" +
                "where classify.id in (\n" +
                "select busi_classify_id from t_md_classify_element where element_id in(" + getCollectQuotesStr(dwInstanceIds) + ")" +
                ") and dir.code='" + dirCode + "' and dir.name ='" + dirName + "'";
        List<Map> list = this.baseDao.sqlQueryForList(sql);
        List<String> classifyIds = list.stream().map(s -> (String) s.get("id")).collect(Collectors.toList());
        return classifyIds;
    }



    private List<BaseBusiClassify> filterRepeat(List<BaseBusiClassify> baseBusinessClassifyList) {
        List<String> filterIds = Lists.newArrayList();
        List<BaseBusiClassify> resultBusinessClasifys = Lists.newArrayList();
        for (BaseBusiClassify baseBusiClassify : baseBusinessClassifyList) {
            if (!filterIds.contains(baseBusiClassify.getId())) {
                filterIds.add(baseBusiClassify.getId());
                resultBusinessClasifys.add(baseBusiClassify);
            }
        }
        return resultBusinessClasifys;
    }

    @Override
    public List<String> queryServiceIdsByTransIds(List<String> transIds) {
        String sql = "select * from t_md_service_publication where source_id in ("+DataExportUtil.getCollectQuotesStr(transIds)+")";
        List<Map<String, String>> list = this.baseDao.sqlQueryForList(sql);
        List<String> serviceIds = list.stream().map(s -> s.get("id")).collect(Collectors.toList());
        return serviceIds;
    }

    @Override
    public List<Map<String, Object>> queryServiceApiByServiceIds(List<String> serviceIds, String queryTableName, String queryKey) {
        String sql = " select * from   " +queryTableName+ " where  " + queryKey + " in ("+DataExportUtil.getCollectQuotesStr(serviceIds)+") ";
        List<Map<String, Object>> list = this.baseDao.sqlQueryForList(sql);
        return list;
    }

    @Override
    public List<BaseBusiClassify> queryServiceClassifyIdByServiceIds(List<String> serviceIds) {
        List<BaseBusiClassify> serviceBaseClassify = Lists.newArrayList();
        List<String> classifyIdsByServiceIds = getClassifyIdsByServiceIds(serviceIds);
        for (String classifyId : classifyIdsByServiceIds) {
            serviceBaseClassify.addAll(findBusinessClassifyParent(classifyId));
        }
        return filterRepeat(serviceBaseClassify);
    }

    private List<String> getClassifyIdsByServiceIds(List<String> serviceIds){
        String Sql = " select * from t_md_busi_classify where id in (select busi_classify_id from  t_md_classify_element\n" +
                "where element_id in("+DataExportUtil.getCollectQuotesStr(serviceIds)+"))";
        List<Map> list = this.baseDao.sqlQueryForList(Sql);
        List<String> classifyIds = list.stream().map(s -> (String) s.get("id")).collect(Collectors.toList());
        return classifyIds;
    }

    @Override
    public List<Map> queryResourceIdsServiceIds(List<String> serviceIds,String userId) {
        if(CollectionUtils.isEmpty(serviceIds)) return Lists.newArrayList();
        StringBuffer sql = new StringBuffer();
        sql.append("select * from t_md_element where id in (select resource_id  from t_md_resource_relation where service_id in ("+DataExportUtil.getCollectQuotesStr(serviceIds)+")) and operate_user_id ='"+userId+"' ");
        sql.append("union ");
        sql.append(" select * from t_md_element where id in (select source_id  from t_md_service_publication where id in ("+DataExportUtil.getCollectQuotesStr(serviceIds)+")) and operate_user_id ='"+userId+"' ");

        List<Map> listMap = this.baseDao.sqlQueryForList(sql.toString());
        return listMap;
    }


    @Override
    public Set<String> queryDwInstanceIds(List<String> logicIds) {
        String sql1= " select dw_db_id from t_dw_table_mapping where classifier_stat_id in (\n" +
                " select owner_id from t_md_logic_dataobj where belong_type='PHYSICAL' and id in ("+DataExportUtil.getCollectQuotesStr(logicIds)+")\n" +
                ")";
        List<Map> list1 = this.baseDao.sqlQueryForList(sql1);
        Set<String> dwInstanceIds = new HashSet<>();
        String sql2 = " select dw_db_instance_id from t_dw_db_mapping where deployed_software in (select owner_id from t_md_logic_dataobj where belong_type!='PHYSICAL' and id in ("+DataExportUtil.getCollectQuotesStr(logicIds)+"))";
        List<Map> list2 = this.baseDao.sqlQueryForList(sql2);
        if(CollectionUtils.isNotEmpty(list1)){
            for(Map<String,String> map : list1){
                dwInstanceIds.add(map.get("dw_db_id"));
            }
        }
        if(CollectionUtils.isNotEmpty(list2)){
            for(Map<String,String> map : list2){
                dwInstanceIds.add(map.get("dw_db_instance_id"));
            }
        }
        return dwInstanceIds;
    }

    @Override
    public List<String> queryServiceIdsByCaseIds(List<String> caseIds) {
        if(CollectionUtils.isEmpty(caseIds)) return Lists.newArrayList();
        String sql = "select * from t_md_service_publication where id in (select api_id from T_MD_USE_CASE_API where use_case_id in ("+DataExportUtil.getCollectQuotesStr(caseIds)+"))";
        List<Map<String, String>> list = this.baseDao.sqlQueryForList(sql);
        List<String> serviceIds = list.stream().map(s -> s.get("id")).collect(Collectors.toList());
        return serviceIds;
    }

    @Override
    public List<Map> queryServiceElement(List<String> classifyIds) {
        if(CollectionUtils.isEmpty(classifyIds)) return Lists.newArrayList();
        String sql = "select * from t_md_classify_element where busi_classify_id in ("+DataExportUtil.getCollectQuotesStr(classifyIds)+")";
        return this.baseDao.sqlQueryForList(sql);
    }

    @Override
    public List<Map> queryUseCaseByElement(List<String> elementIds) {
        String sql = "select * from T_MD_USE_CASE where id in ("+DataExportUtil.getCollectQuotesStr(elementIds)+") ";
        return this.baseDao.sqlQueryForList(sql);
    }

    @Override
    public void deleteVariable(List<String> variableIds) {
        String sql =" delete from T_MD_TRANS_VARIABLE where id in ("+DataExportUtil.getCollectQuotesStr(variableIds)+")";
        this.baseDao.executeSqlUpdate(sql);
    }

    @Override
    public void deleteVariableRelations(List<String> variableRelationIds) {
        String sql =" delete from t_md_trans_variable_relation where id in ("+DataExportUtil.getCollectQuotesStr(variableRelationIds)+")";
        this.baseDao.executeSqlUpdate(sql);
    }

    @Override
    public List<String> querySchemaIdByDwInstanceIds(List<String> dwInstanceIds) {
        String sql = " select id from t_md_rdb_schema where catalog_id in ( select deployed_software_id from t_md_dw_db_instance where id in ("+DataExportUtil.getCollectQuotesStr(dwInstanceIds)+") )";
        List<Map> list = this.baseDao.sqlQueryForList(sql);
        List<String> schemaId = list.stream().map(s -> (String) s.get("id")).collect(Collectors.toList());
        return schemaId;
    }

    @Override
    public void saveDataImportExportLog(HttpServletRequest request, ExportModelVo exportModelVo) {
        HttpSession session = request.getSession();

        Map<String, String[]> parameterMap = request.getParameterMap();

        TSysOperateLog tSysOperateLog = new TSysOperateLog();
        tSysOperateLog.setSessionId(session.getId());
        TSysFunc tSysFunc = new TSysFunc();
        tSysFunc.setFuncCode("modelSpaceImportAndExport");
        tSysFunc.setFuncName("模型导出");
        tSysOperateLog.settSysFunc(tSysFunc);
        TSysAuthUser tSysAuthUser = new TSysAuthUser();
        tSysAuthUser.setId(null == session.getAttribute("userId")? "" : session.getAttribute("userId").toString());//"8a8a801e7208a8fc017208a942590000"
        tSysOperateLog.settSysAuthUser(tSysAuthUser);
        tSysOperateLog.setVisitIp(request.getRemoteAddr());
        tSysOperateLog.setVisitUrl(request.getRequestURL().toString());
        tSysOperateLog.setVisitTime(new java.sql.Timestamp(System.currentTimeMillis()));
        tSysOperateLog.setCreateTime(new java.sql.Timestamp(System.currentTimeMillis()));
        tSysOperateLog.setOperateType("3");
        StringBuffer data = new StringBuffer();
        if(exportModelVo.getSpecifiedModelTransIdList().size()>0){
            data.append("  modelTransId =").append(String.join(",",exportModelVo.getSpecifiedModelTransIdList()));
        }
        if(exportModelVo.getSpecifiedTransClassifyIdList().size()>0){
            data.append("  transTransId =").append(String.join(",",exportModelVo.getSpecifiedTransClassifyIdList()));
        }
        tSysOperateLog.setOperateCondition(data.toString());
        this.baseDao.save(tSysOperateLog);
    }
}
