package com.dragonsoft.cicada.datacenter.modules.modeling.client;

import okhttp3.ResponseBody;
import retrofit2.Call;
import retrofit2.http.*;

import java.util.Map;

/**
 * <AUTHOR> Wu.D.J
 * @Create : 2020.11.09
 */
public interface MLSQLExecutorClient {

    @FormUrlEncoded
    @POST("/run/script")
    Call<ResponseBody> runScript(@Field("sql") String sql);

    @FormUrlEncoded
    @POST("/run/script")
    Call<ResponseBody> runScript(@FieldMap Map<String, Object> map);

    @GET("/runningjobs")
    Call<ResponseBody> runningjobs();

    @FormUrlEncoded
    @POST("/killjob")
    Call<ResponseBody> killjob(@Field("groupId") String groupId);

    @GET("/instance/resource")
    Call<ResponseBody> resource();

    @FormUrlEncoded
    @POST("/getSubtasksByJobId")
    Call<ResponseBody> getSubtasksByJobId(@Field("groupId")String groupId);

    @FormUrlEncoded
    @POST("/getJumpLogUrl")
    Call<ResponseBody> getJumpLogUrl(@Field("jobId")String jobId);

}
