package com.dragonsoft.cicada.datacenter.modules.datavisual.datapolishing.datapolishings;

import com.code.common.utils.assertion.Assert;

/**
 * <AUTHOR>
 * @Date 2021/3/16 10:05
 */
public class QuarterDataPolishing extends AbsDataPolishing {

    private final String QUARTER_FORMAT = "yyyyq";
    private final String QUARTER = "季度";


    @Override
    public void calculateDate() {
        if (minDate.length() != 5) {
            granularituFail(QUARTER, QUARTER_FORMAT, minDate);
        }
        if (maxDate.length() != 5) {
            granularituFail(QUARTER, QUARTER_FORMAT, maxDate);
        }
        //计算最小时间那年剩下的季度
        String minYear = minDate.substring(0, 4);
        String minQuarter = minDate.substring(4);
        String maxYear = maxDate.substring(0, 4);
        String maxQuarter = maxDate.substring(4);
        Integer minQuarterInt = parseInt(minQuarter, QUARTER_FORMAT);
        for (Integer i = minQuarterInt; i <= 4; i++) {
            this.dataPolishingList.add(minYear + i);
        }
        //计算中间的季度
        int middleMinYearInt = parseInt(minYear, QUARTER_FORMAT) + 1;
        int middleMaxYearInt = parseInt(maxYear, QUARTER_FORMAT) - 1;

        for (int i = 0; i < (middleMaxYearInt - middleMinYearInt) + 1; i++) {
            for (int j = 1; j <= 4; j++) {
                dataPolishingList.add(String.valueOf((middleMinYearInt + i)) + j);
            }
        }
        Integer maxQuarterInt = parseInt(maxQuarter, QUARTER_FORMAT);

        //计算最大时间前几个季度
        for (Integer i = 1; i <= maxQuarterInt; i++) {
            this.dataPolishingList.add(maxYear + i);
        }
    }

    @Override
    public void checkData(String data) {
        int temData = parseInt(data, QUARTER_FORMAT);
        int quarterInt = temData % 10;
        if (quarterInt > 4 || quarterInt == 0) {
            Assert.fail("季度最大为4季度，最小为1季度！");
        }
    }
}