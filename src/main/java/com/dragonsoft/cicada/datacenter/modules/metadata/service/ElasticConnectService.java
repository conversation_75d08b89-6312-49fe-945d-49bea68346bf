package com.dragonsoft.cicada.datacenter.modules.metadata.service;

import com.code.metadata.res.semistructured.fulltext.elasticsearch.ElasticSearchClusterNode;
import com.code.metadata.res.semistructured.fulltext.elasticsearch.ElasticSearchInstance;
import com.code.metaservice.datawarehouse.model.DataSourceVO;
import com.code.metaservice.res.response.datasource.InsertDataSourceResponse;
import com.code.metaservice.res.semistructured.fulltext.FullTextElasticSearchService;
import com.fw.service.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;


/**
 * <AUTHOR> Avizii
 * @Create : 2021.03.29
 */
@Service(mappingName = "esConnectService")
public class ElasticConnectService extends DataBaseConnectService {

    @Autowired
    private FullTextElasticSearchService fullTextElasticSearchService;

    @Override
    public boolean testConnection(DataSourceVO dataSourceVO) {
        return fullTextElasticSearchService.testConnection(toDataSourceRequest(dataSourceVO));
    }

    @Override
    public InsertDataSourceResponse insertDataSource(DataSourceVO dataSourceVO) {
        return fullTextElasticSearchService.insertResource(toDataSourceRequest(dataSourceVO));
    }

    @Override
    public void updateResource(DataSourceVO dataSourceVO) {
        fullTextElasticSearchService.updateResource(toDataSourceRequest(dataSourceVO));
    }

    @Override
    protected DataSourceVO getDataSourceInfo(String schemaId) {
        DataSourceVO dataSourceVo = new DataSourceVO();
        ElasticSearchInstance elasticSearchInstance = (ElasticSearchInstance) this.baseDao.get(ElasticSearchInstance.class, schemaId);
        dataSourceVo.setSoftwareId(elasticSearchInstance.getSoftware().getId());
        dataSourceVo.setDbCode(elasticSearchInstance.getCode());
        dataSourceVo.setDbName(elasticSearchInstance.getName());
        dataSourceVo.setClusterName("1");
//        dataSourceVo.setS(String.valueOf(elasticSearchInstance.getSoftware()));
        dataSourceVo.setIp(elasticSearchInstance.getDeployedComp().getMachine().getIpAddress());
        ElasticSearchClusterNode elasticSearchClusterNode = getElasticSearchClusterNode(schemaId);
        dataSourceVo.setPort(String.valueOf(elasticSearchClusterNode.getPort()));
        return dataSourceVo;
    }

    private ElasticSearchClusterNode getElasticSearchClusterNode(String id) {
        StringBuffer sql = new StringBuffer();
        sql.append(" From ElasticSearchClusterNode where owner_id = :id ");
        return (ElasticSearchClusterNode) this.baseDao.queryForObject(sql.toString(),addParam("id", id).param());
    }
}
