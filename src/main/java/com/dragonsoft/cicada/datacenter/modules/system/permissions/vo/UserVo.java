package com.dragonsoft.cicada.datacenter.modules.system.permissions.vo;

import com.dragoninfo.dfw.entity.TSysAuthObj;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/5/21
 */
@Data
@ApiModel(value="用户对象模型")
public class UserVo {
    @ApiModelProperty(value="数据库唯一标识ID" ,required=true)
    private String id;
    /**
     * 页面上的ID
     */
    @ApiModelProperty(value="code,为页面上展示ID" ,required=true)
    private String objCode;
    @ApiModelProperty(value="名称" ,required=true)
    private String objName;
    /**
     * 与objcode相同
     */
    @ApiModelProperty(value="与code相同" ,required=true)
    private String userName;
    @ApiModelProperty(value="用户密码" ,required=true)
    private String password;
    @ApiModelProperty(value="用户拥有的角色" ,required=true)
    private List<TSysAuthObj> roles;
    @ApiModelProperty(value="用户所属用户组" ,required=true)
    private TSysAuthObj belongGroup;
    @ApiModelProperty(value="邮箱" ,required=true)
    private String email;
    @ApiModelProperty(value="电话号码" ,required=true)
    private String phone;
    @ApiModelProperty(value="是否启用标识，0启用，1禁用" ,required=true)
    private String enableState;
    @ApiModelProperty(value="身份证号码" ,required=true)
    private String certificateNumber;
    /**
     * 编辑人
     */
    private String Editor;
    private int loginNumber;
    /**
     * 在数据源获取全部用户使用 当前user是有授权
     */
    private Boolean auth;

    private String pid;
}
