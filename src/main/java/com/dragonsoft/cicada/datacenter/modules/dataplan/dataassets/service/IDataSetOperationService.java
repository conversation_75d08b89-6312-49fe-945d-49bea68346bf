package com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.service;

import com.code.common.paging.PageInfo;
import com.code.metadata.datavisual.DataSet;
import com.code.metaservice.datawarehouse.model.DatasetTreeModel;
import com.code.metaservice.ddl.vo.LogicDataSetColumnVo;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.vo.DataSetOperationVo;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.vo.LogicDataSet.ColumnDataSetVo;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.vo.LogicDataSet.SaveTableColumnMappingVo;
import com.dragonsoft.cicada.datacenter.modules.dataplan.externaldatasources.vo.datawarehouse.ServicePublishColumnVo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/7/8
 */
public interface IDataSetOperationService {

    /**
     * 获取数据集目录树
     *
     * @return
     */
    List<DatasetTreeModel> queryDataSetTree(String userId, boolean hasLogicDataObject, String currentDataSetId);


    /**
     * 获取他人空间数据集目录树
     */
    DatasetTreeModel queryDatasetTreeFromOtherUser(String userId, boolean hasLogicDataObject, String currentDataSetId);

    /**
     * 获取他人数据集目录树
     */
    DatasetTreeModel queryDatasetTreeFromOtherUserBySourceType(String userId, boolean hasLogicDataObject, String currentDataSetId);

    /**
     * 获取生成api目录树
     *
     * @param userId
     * @param hasLogicDataObject
     * @param currentDataSetId
     * @return
     */
    List<DatasetTreeModel> queryPublishDataSetTree(String userId, boolean hasLogicDataObject, String currentDataSetId);

    /**
     * 获取源数据集目录树
     */
    List<DatasetTreeModel> querySourceDatasetTree(String userId, String currentDataSetId,String ignoreTypes);

    /**
     * 合并自定义数据集和源数据集两棵树
     */
    List<DatasetTreeModel> mergeDataSetTree(List<DatasetTreeModel> selfDataSet, List<DatasetTreeModel> sourceDataSet);

    /**
     * 添加数据集树节点
     *
     * @param name
     * @param busiId
     * @param userId
     */
    DatasetTreeModel addDataSetTreeNode(String name, String busiId, String userId);

    /**
     * 更新数据集树节点
     *
     * @param classifyName
     * @param classifyId
     */
    String updateNode(String classifyName, String classifyId, String userId, String oldName);

    /**
     * 更新数据集树节点
     *
     * @param nodeId
     */
    String deleteDataSetTreeNode(String nodeId, String userId);

    /**
     * 获取数据集分页
     * @param isJurisdiction  true 分页 false pageSize=-1
     * @return
     */
    PageInfo getDataSetPage(String id, String name, int page, int pageSize, String userId, String dbType, boolean isJurisdiction);

    /**
     * 获取他人数据集
     * @param id
     * @param name
     * @param page
     * @param pageSize
     * @param operatorId
     * @param dbType
     * @param isJurisdiction
     * @return
     */
    PageInfo getDataSetPageForOtherPerson(String id, String name, int page, int pageSize, String operatorId, String dbType, boolean isJurisdiction);


    /**
     * 获取分享数据集分页
     *
     * @return
     */
    PageInfo getDataSetSharePage(String id, String name, int page, int pageSize, String userId, String dbType, boolean isJurisdiction);

    /**
     * 更新数据集
     *
     * @param dataSetOperationVo
     */
    void updateDataSet(DataSetOperationVo dataSetOperationVo);

    /**
     * 删除数据集
     *
     * @param id
     */
    void deleteDataSet(String id);

    /**
     * 校验数据集是否能被删除
     *
     * @param id
     * @return
     */
    public String checkDataSetByVisual(String id);

    /**
     * 查询数据集
     *
     * @param id
     * @return
     */
    DataSetOperationVo queryDataSetById(String id);

    List<String> getAllLogicDataObj();

    /**
     * 获取数据集的字段信息
     *
     * @param id
     * @return
     */
    List<ServicePublishColumnVo> getDataSetColumn(String id);

    List<ServicePublishColumnVo> getDataSetColumnList(String id);

    /**
     * 设置缓存等级
     *
     * @param id
     */
    void setCacheLevel(String id, String cacheLevel);

    String accreditDataSet(List<String> ids, String treeNodeId, String userId);

    boolean isRepeatName(String name, String userId);

    boolean checkHasDataSet(String id);

    String createLogicDataSet(String id, String name, String userId);

    /**
     * 获取当前登录用户的所有数据集
     *
     * @param userId
     * @return
     */
    List<DataSet> getAllDataSetByUserId(String userId);

    /**
     * 移动数据集到指定目录
     * @param dataSetID 数据集ID
     * @param treeNodeId 源树ID
     * @param dTreeNodeId 目标树ID
     */
    void moveLogicDataSet(String dataSetID, String treeNodeId, String dTreeNodeId);

    void changeIsFast(String dataSetId, boolean ifFase);

    List<Map<String, List<String>>> treelist(List<DatasetTreeModel> treeModelList);

    List<String> findAuthRoleAndUserbyPhysicalDatasetId(String datasetId);

    boolean checkDataSetName(String name, String id);

    boolean checkDataSetNameByDataSetId(String name, String dataSetId, String userId);

    Boolean isAccreditDataSet(String userId, String datasetId);

    String getDataSetDbType(String datasetId);

    Boolean isRepeatDataSet(String userId, String datasetId);

    void deleteRepeatDataSet(String userId, String datasetId);


    void deleteLogicDataObjByOwnerId(String ownerId);

    void updateTableMappingOperateId(String classifierId);

    String getDwDbIdByClassifierStatId(String classifierStatId);

    /**
     * 获取数据集表的字段
     * @return
     */
    ColumnDataSetVo getClassifyFeature(String classifierStatId);

    String saveSingleTableDataSet(List<String> ids, String treeNodeId,String dsTypeTreeNodeId, String userId, List<LogicDataSetColumnVo> column, String name);


    String saveMultiTableDataSet(List<String> ids, String treeNodeId,String dsTypeTreeNodeId,String userId, List<LogicDataSetColumnVo> column, String name);

    List<Map<String, String>> findAllType();

    void saveDataSetNew(String datasetId, String treeNodeId, String userId);


    List<DatasetTreeModel> queryDataSetTreeBySourceType(String userId, boolean hasDataObj, String currentDataSetId);




    Map<String, String> getCatalogList(String id);


    Map<String,List<LogicDataSetColumnVo>> getClassifyFeatures(List<SaveTableColumnMappingVo> objIdList);
}
