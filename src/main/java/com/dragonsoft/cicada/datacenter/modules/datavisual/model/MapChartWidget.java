package com.dragonsoft.cicada.datacenter.modules.datavisual.model;

import com.code.dragonsoft.dataquery.service.querymodel.ColumnDataModel;
import com.code.meta.dml.standard.Dimension;
import com.code.meta.dml.standard.IMultCdin;
import com.code.meta.dml.standard.Measure;
import com.code.meta.dml.standard.StandardQuery;
import com.code.meta.dml.standard.cdins.Order;
import com.code.meta.dml.standard.cdins.QueryCdins;
import com.code.metadata.datavisual.WidgetDatasetDims;
import com.code.metadata.datavisual.WidgetDatasetMeasures;
import com.dragonsoft.cicada.datacenter.common.utils.Area;
import com.dragonsoft.cicada.datacenter.common.utils.AreaUtils;
import com.dragonsoft.cicada.datacenter.modules.datavisual.dataset.IDataSetBuilder;
import com.dragonsoft.cicada.datacenter.modules.datavisual.model.annotation.WidgetLabel;
import com.dragonsoft.cicada.datacenter.modules.datavisual.model.enums.WidgetType;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import java.util.*;

@WidgetLabel(name = "地图", type = WidgetType.MAP_CHART, describe = "地图控件")
@Slf4j
@Data
public class MapChartWidget extends AbsChartWidget {
    MapChartData mapChartData = new MapChartData();

    String mapCode;

    @Override

    public Map<String, Object> loadingData(IDataSetBuilder dataSetBuilder, int mode, String code, int timers) {

        String newQuery = getSQL(dataSetBuilder, mode);
        ColumnDataModel columns = null;
        StopWatch sw = new StopWatch();
        sw.start("统一访问层查询");
        columns = query(newQuery);
        sw.stop();

        sw.start("可视化前端对象封装");
        if (null == columns || columns.getFieldName().isEmpty()) {
            Map map = new HashMap();
            map.put("code", -1);
            return map;
        }

        Map<String, Object> datas = this.builderResult(columns);
        sw.stop();
        if (sw.getTotalTimeSeconds() >= timers) {
            log.info(sw.prettyPrint());
        }
        return datas;
    }

    @Override
    public String getSQL(IDataSetBuilder dataSetBuilder, int mode) {
        StandardQuery query = new StandardQuery(this.widgetDataset.getDbType());
        if (this.getWidgetDataset().getDbType() != null && this.getWidgetDataset().getDbType().equals(DB_TYPE_ES)) {
            query.setSqlTYpe(StandardQuery.type.ES);
        }
        QueryCdins queryCdins = query.getQueryCdins();
        query.setTableName(getSearchSQL(this.widgetDataset.getDatasetId()));
        List<WidgetDatasetMeasures> ms = this.widgetDataset.getFromListMeasures();
        ms.forEach(m -> query.addMeasures(new Measure(m.getFiledCode(), this.getFunc(m.getFuncType(), m.getIsDistinct()), m.getFiledCode())));
        WidgetDatasetDims d = this.widgetDataset.getFromListDims().get(0);
        query.addDimensions(new Dimension(d.getFiledCode(), d.getFiledCode()));
        IMultCdin iMultCdin = this.getCondition(queryCdins);

        // 码表 直辖市 需要做转换
        switch (mapCode) {
            case "110000":
                mapCode = "110100";
                break;
            case "310000":
                mapCode = "310100";
                break;
            case "120000":
                mapCode = "120100";
                break;
            case "500000":
                mapCode = "500100";
                break;
            default:
        }
        List<Area> areas = AreaUtils.getChildren(mapCode);
        if (!CollectionUtils.isEmpty(areas)) {
            iMultCdin.addCdin(queryCdins.in(d.getFiledCode(), areas.stream().map(Area::getCode).toArray(String[]::new)));
        }

        query.setConditions(iMultCdin);
        Order[] orders = this.getOrders(this.widgetDataset.getFromListDims(), ms);
        if (orders != null) {
            query.setOrderCdins(orders);
        }
        if (mode != PREVIEW_MODE) {
            this.previewLine = -1;
        }
        if (Objects.equals(this.widgetDataset.getDbType().toUpperCase(),"MYSQL")){
            return query.toExpression().getScript().replaceAll("`", "");
        }
        return query.toExpression().getScript().replaceAll("`","\"");
    }

    private Map<String, Object> builderResult(ColumnDataModel rs) {
        mapChartData.addColumns("位置");
        WidgetDatasetDims d = this.widgetDataset.getFromListDims().get(0);
        List<WidgetDatasetMeasures> ms = this.widgetDataset.getFromListMeasures();
        for (Map b : rs.getFieldValue()) {
            try {
//                BaseRow b = (BaseRow) o;
                Map map = new LinkedHashMap();
                Area area = AreaUtils.getArea((String) b.get(d.getFiledCode()));
                String resName = "";
                if (area == null) {
                    continue;
                }
                if (area.getShortName() != null && area.getShortName().contains("内蒙古")) {
                    resName = "内蒙古";
                } else if (area.getShortName().contains("特别行政区") || area.getShortName().contains("自治区")) {
                    resName = area.getShortName().substring(0, 2);
                } else {
                    resName = area.getShortName();
                }
                map.put("位置", resName);
                this.mapChartData.codeMap.put(resName, area.getCode());

                //TODO 码表
//                map.put("位置", b.get(d.getFiledCode()));
                ms.forEach(m -> {
                    mapChartData.addColumns(m.getFiledName());
                    if (null == b.get(m.getFiledCode())) {
                        map.put(m.getFiledName(), 0);
                    } else {
                        map.put(m.getFiledName(), this.getMeasuresFilterVal(m, b));
                    }
//                    this.mapChartData.codeMap.put(m.get)

                });
                this.mapChartData.rows.add(map);
            } catch (Exception e) {
                log.error("地图查询异常！",e);
            }
        }
        Map map = new HashMap();
        map.put("code", 1);
        map.put("data", this.mapChartData);
        return map;
    }


    @Data
    class MapChartData {
        List<String> columns = new LinkedList<>();
        List<Map<String, Object>> rows = new LinkedList<>();
        Map<String, String> codeMap = new HashMap<>();

        public void addColumns(String c) {
            for (String s : columns) {
                if (s.equals(c)) {
                    return;
                }
            }
            this.columns.add(c);
        }
    }
}
