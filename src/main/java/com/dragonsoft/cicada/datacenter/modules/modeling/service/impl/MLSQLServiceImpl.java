package com.dragonsoft.cicada.datacenter.modules.modeling.service.impl;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.code.common.bean.BeanFactory;
import com.code.common.mist.plugin.config.service.impl.PluginConfigService;
import com.code.common.utils.StringUtils;
import com.code.common.utils.assertion.Assert;
import com.code.metadata.etl.trans.TransHopMeta;
import com.code.metadata.etl.trans.TransMeta;
import com.code.metaservice.core.StructuralFeatureService;
import com.code.metaservice.etl.trans.TransMetaService;
import com.code.metaservice.res.common.ClassifierStatService;
import com.code.metaservice.udf.management.IUdfGraphService;
import com.code.metaservice.variable.ITransVariableService;
import com.code.mlsql.parse.Script;
import com.code.mlsql.utils.GraphParseHelper;
import com.code.mlsql.utils.GraphUtil;
import com.code.thirdplugin.cicada.service.meta.input.CicadaMetaServiceInput;
import com.dragonsoft.cicada.datacenter.common.config.MlSqlEngineConfig;
import com.dragonsoft.cicada.datacenter.common.config.PropertiesListenerConfig;
import com.dragonsoft.cicada.datacenter.modules.modeling.aimodeling.config.AiModelIpAndPort;
import com.dragonsoft.cicada.datacenter.modules.modeling.client.MLSQLExecutorClient;
import com.dragonsoft.cicada.datacenter.modules.modeling.client.RetrofitManager;
import com.dragonsoft.cicada.datacenter.modules.modeling.service.MLSQLService;
import com.dragonsoft.cicada.datacenter.modules.modeling.service.MlsqlTransScheduleService;
import com.dragonsoft.cicada.datacenter.modules.modeling.util.MLSQLConstant;
import com.dragonsoft.cicada.datacenter.modules.modeling.util.MlSqlSparkVersionFilterUtil;
import com.dragonsoft.cicada.datacenter.modules.modeling.vo.MlsqlTaskState;
import com.dragonsoft.cicada.datacenter.modules.modeling.vo.TransStatus;
import com.fw.service.BaseService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import okhttp3.ResponseBody;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import retrofit2.Call;
import retrofit2.Response;

import javax.annotation.Resource;
import java.io.IOException;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class MLSQLServiceImpl extends BaseService implements MLSQLService {

    @Value("${mlsql.executor.address}")
    public String MLSQLAddress;


    @Autowired
    ClassifierStatService classifierStatService;
    @Autowired
    PluginConfigService pluginConfigService;
    @Autowired
    StructuralFeatureService structuralFeatureService;
    @Autowired
    TransMetaService transMetaService;
    @Autowired
    BeanFactory beanFactory;

    @Resource(name = "graphParseHelper")
    private GraphParseHelper helper;
    @Autowired
    IUdfGraphService udfGraphService;

    @Autowired
    MlsqlTransScheduleService mlsqlTransScheduleService;
    @Value("${spark.version.use}")
    private String use_spark_version;
    @Autowired
    ITransVariableService transVariableService;

    @Autowired
    private AiModelIpAndPort aiModelIpAndPort;

    //本地服务调用的地址
    @Value("${dc.publish.path}")
    private String publishPath;

    @Autowired
    private MlSqlEngineConfig mlSqlEngineConfig;

    @Override
    public String runScript(String mlsql) {
        MLSQLExecutorClient executorClient = RetrofitManager.executorClient(MLSQLAddress);
        Call<ResponseBody> bodyCall = executorClient.runScript(mlsql);
        return bodyReturn(bodyCall);
    }

    @Override
    public String runScript(Map<String, Object> mlsql) {
        MLSQLExecutorClient executorClient = RetrofitManager.executorClient(MLSQLAddress);
        Call<ResponseBody> bodyCall = executorClient.runScript(mlsql);
        return bodyReturn(bodyCall);
    }

    @Override
    public Map<String, String> batchRunScript(Map<String, Object> mlSql) {
        Map<String, String> map = MapUtil.newHashMap();
        for (String url : mlSqlEngineConfig.getUrlList()) {
            MLSQLExecutorClient executorClient = RetrofitManager.executorClient(url);
            Call<ResponseBody> bodyCall = executorClient.runScript(mlSql);
            map.put(url,bodyReturn(bodyCall));
        }
        return map;
    }


    @Override
    public String runningJobs() {
        MLSQLExecutorClient executorClient = RetrofitManager.executorClient(MLSQLAddress);
        Call<ResponseBody> bodyCall = executorClient.runningjobs();
        return bodyReturn(bodyCall);
    }

    @Override
    public String killJob(String id) {
        MLSQLExecutorClient executorClient = RetrofitManager.executorClient(MLSQLAddress);
        Call<ResponseBody> bodyCall = executorClient.killjob(id);
        return bodyReturn(bodyCall);
    }

    protected String bodyReturn(Call<ResponseBody> bodyCall) {
        String res = "";
        try {
            Response<ResponseBody> response = execute(bodyCall);
            if (MLSQLConstant.SUCCESS == response.code()) {
                res = response.body().string();
            } else if (MLSQLConstant.ERROR == response.code()) {
                Assert.fail(response.errorBody().string());
            }
        } catch (IOException e) {
         log.error(e.getMessage(),e);
            Assert.fail("MLSQL执行失败！");
        }
        return res;
    }

    public Response<ResponseBody> execute(Call<ResponseBody> responseCall1) throws IOException {
        return responseCall1.execute();
    }


    @Override
    public String runJob(String id) {
//        String jobId = mistTransformScheduleService.createMlSqlJob(id);
//        TransMeta meta = transMetaService.getTransMetaById(id);
//        String mlSql = getMLSQL(meta);
//        校验mlsql
//        checkMlSql(mlSql, jobId, meta.getName(), id);

        return null;
    }

    private String getMLSQL(TransMeta meta, boolean isRun, String selectSize,String userId) {
        Script script = helper.parse(meta, isRun, selectSize);
        String mlSql = script.toScript();
        //服务输入插件sql
        mlSql = getCicadaMetaServiceSql(meta) + mlSql;
        mlSql =  MlSqlSparkVersionFilterUtil.functionFilter(mlSql,use_spark_version);
        mlSql = mlSql.replace("@{ipAndPort}",aiModelIpAndPort.getRunOpServiceIp());
        mlSql = mlSql.replace("http://@{modelServicePath}",publishPath);
        return mlSql;
    }

    @Override
    public Map<String, String> getAfterTableName(String id, String tranStepId, boolean hasEndTrans, String selectSize,String userId) {

        TransMeta newTransMeta = getNewTransMeta(id, tranStepId, hasEndTrans);

        String mlsql = getMLSQL(newTransMeta, false, selectSize,userId);

        Map<String, Object> scriptMap = Maps.newHashMap();
        scriptMap.put("sql", mlsql);
        scriptMap.put("executeMode", "analyze");


        Map<String, String> resMap = Maps.newHashMap();
        resMap.put("sql", mlsql);
        resMap.put("tableName", mlsql.substring(mlsql.toLowerCase().lastIndexOf(" as ")+4,mlsql.length()-2));
       /* String resInfo = runScript(scriptMap);
        JSONArray resArray = JSONArray.parseArray(resInfo);
        for (int i = resArray.size() - 1; i >= 0; i--) {
            JSONObject obj = (JSONObject) resArray.get(i);
            String tableName = (String) (obj.get("tableName") != null ? obj.get("tableName") : obj.get("outputTableName"));

            if (StringUtils.isBlank(tableName)) tableName = (String) obj.get("inputTableName");
            if (StringUtils.isNotBlank(tableName)) {
                resMap.put("tableName", tableName);
                break;
            }
        }*/
        return resMap;

    }

    @Override
    public String getCicadaMetaServiceSql(TransMeta transMeta) {
        if (transMeta == null) {
            return "";
        }
        Set<TransMeta> children = transMeta.getChildren();
        if (CollectionUtils.isEmpty(children)) {
            return "";
        }

        Map<String, String> jsonMap = Maps.newHashMap();

        for (TransMeta child : children) {
            if ("cicadaMetaServiceInput".equals(child.getUsedPlugin().getCode())) {
                CicadaMetaServiceInput cicadaMetaServiceInput = pluginConfigService.getPluginInstance(CicadaMetaServiceInput.class, child.getId());
                String testDatJson = cicadaMetaServiceInput.getTestDataJson();
                //Assert.isTrue(StringUtils.isNotBlank(testDatJson), child.getName() + "测试数据json不能为空");
                //if (StringUtils.isNotBlank(testDatJson)){
                    List<CicadaMetaServiceInput.ServiceInputColumn> columnList = cicadaMetaServiceInput.getServiceInputColumns().stream().filter(column -> column != null)
                           .collect(Collectors.toList());
                    try {
                        JSONArray jsonArray = JSONArray.parseArray(testDatJson);
                       if(null != jsonArray){
                           for (Object obj : jsonArray) {
                               JSONObject jsonObject = (JSONObject) obj;
                               for (CicadaMetaServiceInput.ServiceInputColumn column : columnList) {
                                   String columnCode = column.getColumnCode();
                                   if (jsonObject.get(columnCode) == null && column.getColumnType().equalsIgnoreCase("String")) {
                                       jsonObject.put(columnCode, "");
                                   }
                               }
                           }
                       }else {
                           jsonArray = new JSONArray();
                           JSONObject jsonObject = new JSONObject();
                           for(CicadaMetaServiceInput.ServiceInputColumn column : columnList){
                               String columnCode = column.getColumnCode();
                                jsonObject.put(columnCode,"");
                           }
                           jsonArray.add(jsonObject);
                           testDatJson = jsonArray.toJSONString();

                       }
                        testDatJson = JSONArray.toJSONString(jsonArray,SerializerFeature.WriteMapNullValue);

                    } catch (Exception e) {
                     log.error(e.getMessage(),e);
                        //Assert.fail(transMeta.getName() + "测试数据json格式错误");
                    }
                    if (StringUtils.isBlank(testDatJson)){
                        JSONArray array = new JSONArray();
                        Map object = new HashMap();
                        for (CicadaMetaServiceInput.ServiceInputColumn s : columnList) {
                            String columnCode = s.getColumnCode();
                            object.put(columnCode,null);
                        }
                        array.add(object);
                        testDatJson = array.toString();
                    }
                    jsonMap.put(cicadaMetaServiceInput.getTableName(), testDatJson);

            }
        }

        return GraphUtil.getCicadaMetaServiceSql(jsonMap);
    }

    @Override
    public String getSubtasksByJobId(String groupId) {
        String address = getMLSQLAddress(groupId);
        MLSQLExecutorClient executorClient = RetrofitManager.executorClient(address);
        Call<ResponseBody> bodyCall = executorClient.getSubtasksByJobId(groupId);
        return bodyReturn(bodyCall);
    }

    private String getMLSQLAddress(String groupId) {
        String sql = "select executor_service from t_trans_job where id = :groupId";
        String executorService = this.baseDao.sqlQueryForValue(sql, addParam("groupId", groupId).param());
        String address = MLSQLAddress;
       /* if ("address2".equals(executorService)) {
            address = MLSQLAddress2;
        }*/
        Map<String, String> allProperties = PropertiesListenerConfig.getAllProperties();
        String addressValue = allProperties.get(executorService);
        if (StringUtils.isNotBlank(addressValue)) {
            address = addressValue;
        }
        return address;
    }

    @Override
    public String getJumpLogUrl(String jobId,String groupId) {
        String address = getMLSQLAddress(groupId);
        MLSQLExecutorClient executorClient = RetrofitManager.executorClient(address);
        Call<ResponseBody> bodyCall = executorClient.getJumpLogUrl(jobId);
        return bodyReturn(bodyCall);
    }

    @Override
    public TransStatus execStatus(String transId) {
//        String status = mlsqlTransScheduleService.getTransTaskStatus(transId);
        Map<String, Object> transTask = mlsqlTransScheduleService.getTransTask(transId);
        if(transTask != null) {
            String status = transTask.get("execute_status").toString();
            Timestamp startTime = (Timestamp)transTask.get("start_time");
            Timestamp endTime = (Timestamp)transTask.get("end_time");
            if (MlsqlTaskState.SUCCESS.toString().equalsIgnoreCase(status)) {
                return new TransStatus("success", status, startTime, endTime);
            }
            if (MlsqlTaskState.ERROR.toString().equalsIgnoreCase(status)) {
                return new TransStatus("error", status, startTime, endTime);
            }
            if (MlsqlTaskState.RUNNING.toString().equalsIgnoreCase(status)) {
                return new TransStatus("running", status, startTime, endTime);
            }
            if (MlsqlTaskState.ONGOING.toString().equalsIgnoreCase(status)) {
                return new TransStatus("running", status, startTime, endTime);
            }
            if (MlsqlTaskState.CANCEL.toString().equalsIgnoreCase(status)) {
                return new TransStatus("cancel", status, startTime, endTime);
            }}
        return new TransStatus("none", "暂无状态", null, null);
    }

    @Override
    public void transDeleteHistoryTaskLog(String taskId) {
        mlsqlTransScheduleService.deleteByTaskId(taskId);
    }

    @Override
    public String getSql(String transId,String userId) {
        TransMeta transMeta = this.transMetaService.getTransMetaById(transId);
        return getMLSQL(transMeta, true, null,userId);
    }

    protected TransMeta getNewTransMeta(String id, String tranStepId, boolean hasEndTrans) {
        TransMeta transMeta = this.transMetaService.getTransMetaById(id);

        Set<TransHopMeta> newHops = getHops(transMeta.getHops(), tranStepId, hasEndTrans);
        Set<TransMeta> children = new HashSet<>();
        if (newHops.size() <= 0) {
            TransMeta fromTrans = null;
            Iterator<TransHopMeta> iterator = transMeta.getHops().iterator();
            while (iterator.hasNext()) {
                TransHopMeta next = iterator.next();
                if (tranStepId.equals(next.getToTrans().getId())) {
                    fromTrans = next.getFromTrans();
                    break;
                }
            }
            List<TransMeta> transMetas = Lists.newArrayList();
            if (null == fromTrans) {
                if (transMeta.getHops().iterator().hasNext()) {
                    Iterator<TransHopMeta> transHopMetaIterator = transMeta.getHops().iterator();
                    while (transHopMetaIterator.hasNext()) {
                        TransHopMeta transHopMeta = transHopMetaIterator.next();
                        if (tranStepId.equals(transHopMeta.getFromTrans().getId())) {
                            fromTrans = transHopMeta.getFromTrans();
                        }
                    }
                }
                if (null == fromTrans) {
                    Iterator<TransMeta> iterator1 = transMeta.getChildren().iterator();
                    while (iterator1.hasNext()) {
                        TransMeta meta = iterator1.next();
                        if (tranStepId.equals(meta.getId())) fromTrans = meta;
                    }
                    if (null == fromTrans) fromTrans = iterator1.next();
                }
            }
            transMetas.add(fromTrans);
            children.addAll(transMetas);
        } else {
            children.addAll(getTransChildren(newHops));
        }

        TransMeta newTransMeta = new TransMeta();
        BeanUtils.copyProperties(transMeta, newTransMeta);

        newTransMeta.setHops(newHops);
        newTransMeta.setChildren(children);
        return newTransMeta;
    }

    private Set<TransMeta> getTransChildren(Set<TransHopMeta> newHops) {
        Set<TransMeta> newChildren = Sets.newHashSet();
        for (TransHopMeta newHop : newHops) {
            TransMeta toTrans = newHop.getToTrans();
            TransMeta fromTrans = newHop.getFromTrans();
            if (!newChildren.contains(toTrans)) {
                newChildren.add(toTrans);
            }
            if (!newChildren.contains(fromTrans)) {
                newChildren.add(fromTrans);
            }
        }
        return newChildren;
    }

    private Set<TransHopMeta> getHops(Set<TransHopMeta> hops, String tranStepId, boolean isEndTran) {
        Set<TransHopMeta> newHops = Sets.newHashSet();
        for (TransHopMeta hop : hops) {
            if (tranStepId.equals(hop.getToTrans().getId())) {
                newHops.addAll(getHops(hops, hop.getFromTrans().getId(), true));
                if (isEndTran) {
                    newHops.add(hop);
                }
            }
        }
        return newHops;
    }

}

