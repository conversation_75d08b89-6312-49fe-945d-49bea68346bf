package com.dragonsoft.cicada.datacenter.modules.datavisual.businessrelation;

import com.code.dragonsoft.dataquery.service.querymodel.ColumnDataModel;
import com.code.metadata.datavisual.WidgetDataset;
import com.code.metadata.datavisual.WidgetDatasetDims;
import com.code.metadata.datavisual.WidgetDatasetMeasures;
import com.code.metadata.datavisual.relation.AbsRelationConfig;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/5/12 9:36
 */
public abstract class AbsBusinessRelation {
    public abstract String buildSQL(WidgetDataset widgetDataset, WidgetDatasetDims edge,String fromTableName, WidgetDatasetMeasures point, String linkageFilter,String query);

    public abstract AbsRelationConfig getPeerRelationConfig(String relationConfigJson);

    public abstract List<Map> builderEdge(List<Map> points);

    public abstract List<Map> builderPoint(ColumnDataModel originalColumns, String relationConfigJson, WidgetDatasetDims point, WidgetDatasetMeasures edge);

}
