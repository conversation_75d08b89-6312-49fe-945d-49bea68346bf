package com.dragonsoft.cicada.datacenter.modules.modeling.rapidanalysis.controller;

import com.code.common.paging.PageInfo;
import com.code.common.utils.R;
import com.code.common.utils.StringUtils;
import com.code.mist.builder.model.DubboResult;
import com.code.mist.builder.model.TreeNode;
import com.code.mist.builder.service.dubbo.ITransformApiService;
import com.dragoninfo.dfw.bean.Result;
import com.dragonsoft.cicada.datacenter.modules.modeling.rapidanalysis.service.RapidAnalysisService;
import com.dragonsoft.cicada.datacenter.modules.modeling.rapidanalysis.vo.ClassifyVo;
import com.dragonsoft.cicada.datacenter.modules.modeling.rapidanalysis.vo.RapidAnaPageVo;
import com.dragonsoft.cicada.datacenter.modules.modeling.rapidanalysis.vo.ResultRapidTreeNode;
import com.dragonsoft.cicada.datacenter.modules.modeling.service.DataModelingService;
import com.dragonsoft.cicada.datacenter.modules.modeling.vo.ModelTreeVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.List;

@CrossOrigin
@Slf4j
@PropertySource("classpath:case-config.properties")
@RestController
@RequestMapping("/dataModeling/rapidAnalysis")
public class RapidAnalysisController {

    @Autowired
    private DataModelingService dataModelingService;


    @Autowired
    private RapidAnalysisService rapidAnalysisService;


    @Autowired
    private ITransformApiService transformApiService;

    @Value("${standModel}")
    private boolean standModel;

    /**
     * 快速分析目录树
     * @param session
     * @return
     */
    @GetMapping("/getRapidAnalysisTree")
    public Result getRapidAnalysisTree(HttpSession session){
         try {
             String userId = (String) session.getAttribute("userId");
             List<ResultRapidTreeNode> busiClassifies = rapidAnalysisService.getAllRapidAnaTree(userId, "time");
             //List<Map<String, Object>> trans_rapid_dir_mf = dataModelingService.getModelingTransTreeNodes("", "", userId, "TRANS_RAPID_DIR_MF");
             for (int i = 0; i < busiClassifies.size(); i++) {
                 ResultRapidTreeNode resultRapidTreeNode = busiClassifies.get(i);
                if ("标准模型".equals(resultRapidTreeNode.getName())){
                    busiClassifies.remove(i);
                    break;
                }
             }
             return Result.toResult(R.ok(busiClassifies));
        } catch (Exception e) {
            log.error(e.getMessage());
            log.error(e.getMessage(),e);
            return Result.toResult(R.error(e.getMessage()));
        }
    }

    /**
     * 快速分析新增目录
     * @param session
     * @return
     */
    @PostMapping("/addRapidAnaDir")
    public Result addRapidAnaDir(HttpSession session, @RequestBody ClassifyVo classifyVo){
        try {
            String userId = (String) session.getAttribute("userId");
            String classifyName = classifyVo.getClassifyName();
            String parentClassifyId = classifyVo.getParentClassifyId();
            String dirType = "TRANS_RAPID_DIR_MF";
            //dataModelingService.createTransClassify(StringUtils.isBlank(parentClassifyId) ? null : parentClassifyId, classifyName, dirType, userId);
            rapidAnalysisService.addRapidDir(parentClassifyId,classifyName,dirType,userId);
            return Result.toResult(R.ok());
        } catch (Exception e) {
            log.error(e.getMessage());
            log.error(e.getMessage(),e);
            return Result.toResult(R.error(e.getMessage()));
        }
    }


    @GetMapping("/renameRapidDir")
    public Result renameRapidDir(String classifyId,String newClassifyName,HttpSession session){
        try {
            String userId = (String) session.getAttribute("userId");
            rapidAnalysisService.updateRapidAnaDirName(classifyId,newClassifyName,userId);
            return Result.toResult(R.ok());
        } catch (Exception e) {
            log.error(e.getMessage());
            log.error(e.getMessage(),e);
            return Result.toResult(R.error(e.getMessage()));
        }
    }


    @GetMapping("/moveDirectory")
    public Result moveDirectory(String curryClassifyId, String newParentClassifyId) {
        try {
            rapidAnalysisService.moveRapidDir(curryClassifyId,newParentClassifyId);
            return Result.toResult(R.ok());
        } catch (Exception e) {
            log.error(e.getMessage());
            log.error(e.getMessage(),e);
            return Result.toResult(R.error(e.getMessage()));
        }
    }

    @GetMapping("/deleteDir")
    public Result deleteDir(String transClassifyId){
        DubboResult<List<TreeNode>> dubboResult = this.transformApiService.getTreeNodes(transClassifyId, "xxx", null, "TRANS_DIR");
        List<TreeNode> list = dubboResult.getData();
        for (int i = 0; i < list.size(); i++) {
            if (StringUtils.isBlank(list.get(i).getRunMode())) {
                return Result.toResult(R.error("请按层级删除!"));
            }
        }
        DubboResult deleteResult = this.transformApiService.deleteTransClassify(transClassifyId);
        return Result.toResult(R.ok(deleteResult.getData()));
    }

    @PostMapping("/getRapidAnaPage")
    public Result getRapidAnaPage(@RequestBody RapidAnaPageVo rapidAnaPageVo,HttpSession session){
        try {
            String userId = (String) session.getAttribute("userId");
            if(StringUtils.isBlank(userId)){
                throw new RuntimeException("请先登录！");
            }
            String classifyId = rapidAnaPageVo.getClassifyId();
            Integer pageNum = rapidAnaPageVo.getPageNum();
            Integer pageSize = rapidAnaPageVo.getPageSize();
            String rapidAnaName = rapidAnaPageVo.getRapidAnaName();
            PageInfo rapidAnalysisPage = rapidAnalysisService.getRapidAnalysisPage(classifyId,rapidAnaName,pageNum,pageSize,userId);
            return Result.toResult(R.ok(rapidAnalysisPage));
        } catch (Exception e) {
            log.error(e.getMessage());
            log.error(e.getMessage(),e);
            return Result.toResult(R.error(e.getMessage()));
        }
    }

    @GetMapping("/getTreeAllNode")
    public Result getTreeDir(String dirType, HttpServletRequest request) {
        String userId = (String) request.getSession().getAttribute("userId");
        List<ModelTreeVo> list = dataModelingService.getTreeNode(userId, dirType);
        return Result.toResult(R.ok(list));
    }
}
