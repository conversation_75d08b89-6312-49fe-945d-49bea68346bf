package com.dragonsoft.cicada.datacenter.modules.modeling.importandexport.enums;

//导出内容枚举
public enum ExportContentApiEnum {
    API("API","仅导出API"),
    API_RESOURCE("API_RESOURCE","api依赖资源"),
    API_RESOURCE_CASE("API_RESOURCE_CASE","API关联测试用例");

    public String code;
    public String name;

    ExportContentApiEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static ExportContentApiEnum getInstanceByName(String name) {
        ExportContentApiEnum[] var1 = values();
        int var2 = var1.length;

        for(int var3 = 0; var3 < var2; ++var3) {
            ExportContentApiEnum value = var1[var3];
            if (value.name.equals(name)) {
                return value;
            }
        }

        return null;
    }

    public static ExportContentApiEnum getInstanceByCode(String code) {
        ExportContentApiEnum[] var1 = values();
        int var2 = var1.length;

        for(int var3 = 0; var3 < var2; ++var3) {
            ExportContentApiEnum value = var1[var3];
            if (value.code.equals(code)) {
                return value;
            }
        }

        return API_RESOURCE;
    }
}
