package com.dragonsoft.cicada.datacenter.modules.system.dataportal.service.impl;

import com.code.common.utils.assertion.Assert;
import com.code.metadata.portal.Portal;
import com.code.metadata.portal.PublishUrl;
import com.code.metaservice.portal.IPortalService;
import com.code.metaservice.portal.IPublishUrlService;
import com.dragonsoft.cicada.datacenter.modules.system.dataportal.service.IDataPublishUrlService;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.service.IFunctionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2020/9/3
 */
@Service
public class DataPublishUrlServiceImpl implements IDataPublishUrlService {

    @Autowired
    private IPublishUrlService publishUrlService;

    @Autowired
    private IFunctionService functionService;

    @Autowired
    private IPortalService portalService;



    @Override
    public void savePublishUrl(String url, String name, String userId, String stat,String portalId) {
        PublishUrl publishUrl = publishUrlService.queryPublishUrlByPortalId(portalId);
        Portal portal = portalService.queryPortalByPortalId(portalId);
        Assert.notNull(portal,"对应门户不存在");
        publishUrl.setCode(name);
        publishUrl.setName(name);
        publishUrl.setOwner(portal);
        publishUrl.setPublishUrl(url);
        publishUrl.setPublishState(stat);
        publishUrl.setOperateUserId(userId);
        publishUrlService.saveOrUpdate(publishUrl);
    }

    @Override
    public void deletePublishUrl(String id) {
        PublishUrl publishUrl = publishUrlService.get(PublishUrl.class,id);
        publishUrlService.delete(publishUrl);
        functionService.deleteFuncAndFuncRelation(id);
    }

    @Override
    public PublishUrl queryById(String id) {
        PublishUrl publishUrl = publishUrlService.get(PublishUrl.class,id);
        return publishUrl;
    }


}
