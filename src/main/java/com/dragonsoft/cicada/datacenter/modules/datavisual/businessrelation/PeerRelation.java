package com.dragonsoft.cicada.datacenter.modules.datavisual.businessrelation;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.code.common.utils.StringUtils;
import com.code.common.utils.assertion.Assert;
import com.code.dragonsoft.dataquery.service.querymodel.ColumnDataModel;
import com.code.metadata.datavisual.WidgetDataset;
import com.code.metadata.datavisual.WidgetDatasetDims;
import com.code.metadata.datavisual.WidgetDatasetMeasures;
import com.code.metadata.datavisual.relation.AbsRelationConfig;
import com.code.metadata.datavisual.relation.PeerRelationConfig;
import org.jetbrains.annotations.NotNull;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/5/12 9:39
 */
public class PeerRelation extends AbsBusinessRelation {

    @Override
    public String buildSQL(WidgetDataset widgetDataset, WidgetDatasetDims point,String fromTableName,
                           WidgetDatasetMeasures edge, String linkageFilter,String query) {
        String relationConfigJson = widgetDataset.getRelationConfigureJson();
        String tableName = fromTableName;
        PeerRelationConfig config = (PeerRelationConfig) getPeerRelationConfig(relationConfigJson);

        if(StringUtils.isNotBlank(query)){
            setIdNumberValueByQuery(config,query);
        }

        if (StringUtils.isNotBlank(linkageFilter)) {
            setIdNumberValueByLinkageFilter(config, linkageFilter);
        }
        return buildSql(config, tableName, point, edge, widgetDataset.getDataArticleNumber());
    }


    private void setIdNumberValueByQuery(PeerRelationConfig config, String query) {
        JSONArray jsonArray = JSONArray.parseArray(query);
        boolean flag = false;
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject object = (JSONObject) jsonArray.get(i);
            JSONObject filter = (JSONObject) object.get("filter");
            String value = (String) filter.get("value");
            String type = (String) filter.get("type");
            JSONObject field = (JSONObject) filter.get("field");
            String code = (String) field.get("code");
            String idNumber = getDimFieldCode(config.getIdNumberField());
            if("precise".equalsIgnoreCase(type) && idNumber.equalsIgnoreCase(code) && value!=null){
                config.setIdNumber(value);
                flag = true;
                return;
            }
        }
        if(StringUtils.isNotBlank(query) && !flag){
            Assert.fail("业务关系图目前只支持证件号码字段的精准过滤查询框！！！");
        }

    }

    private void setIdNumberValueByLinkageFilter(PeerRelationConfig config, String linkageFilter) {
        JSONArray jsonArray = JSONArray.parseArray(linkageFilter);
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject object = (JSONObject) jsonArray.get(i);
            String type = (String) object.get("value");
            if (type != null) {
                config.setIdNumber(type);
                return;
            }
        }
    }

    private String buildSql(PeerRelationConfig config, String tableName, WidgetDatasetDims point, WidgetDatasetMeasures edge, Long limit) {
        String joinSQL = builderJoinSQL(config, tableName, point, limit);
        String whereSQL = builderWhereSQL(config, tableName, joinSQL, point, edge, limit);
        return whereSQL;
//        return buildCompleteSQL(config, whereSQL, point, edge);
    }

    private String buildCompleteSQL(PeerRelationConfig config, String whereSQL, WidgetDatasetDims point, WidgetDatasetMeasures edge) {
        StringBuffer sb = new StringBuffer();
        List<String> fixedFiledList = new ArrayList<>();
        fixedFiledList.add(getMeasuresFieldCode(edge));
        boolean has = checkNodeCode(point, config);
        if (!has) {
            fixedFiledList.add(getDimFieldCode(point));
        }
        fixedFiledList.add(getDimFieldCode(config.getIdNumberField()));
        fixedFiledList.add("gxrzjhm");
        fixedFiledList.add(getDimFieldCode(config.getTimeInterval()));
        for (WidgetDatasetDims dims : config.getTimeLabelFiled()) {
            fixedFiledList.add(getDimFieldCode(dims));
        }
        fixedFiledList.add(getDimFieldCode(config.getBusinessField()));
        sb.append("select ");
        //添加查询字段
        sb.append("count(" + getDimFieldCode(config.getIdNumberField()) + ") as count,");
        sb.append(String.join(",", fixedFiledList));
        sb.append(" from (");
        sb.append(whereSQL + ") as c ");
        sb.append(" group by ");

        sb.append(String.join(",", fixedFiledList));
        return sb.toString();
    }

    private String builderWhereSQL(PeerRelationConfig config, String tableName,
                                   String joinSQL, WidgetDatasetDims point,
                                   WidgetDatasetMeasures edge, Long limit) {
        StringBuffer sb = new StringBuffer();
        sb.append(" select ");
        boolean has = checkNodeCode(point, config);
        if (!has) {
            sb.append("a." + getDimFieldCode(point) + " as gxrname,");
            sb.append("b." + getDimFieldCode(point) + " as dqrname,");
        }
        sb.append("a." + getDimFieldCode(config.getIdNumberField()) + " as gxrzjhm,");
        //构建查询的字段
        sb.append("b." + getDimFieldCode(config.getIdNumberField()) + ",");
        sb.append("a." + getMeasuresFieldCode(edge) + ",");
        sb.append("a." + getDimFieldCode(config.getBusinessField()) + ",");

        sb.append("a." + getDimFieldCode(config.getStartTime()) + ",");
        sb.append("a." + getDimFieldCode(config.getEndTime()) + ",");

        for (WidgetDatasetDims widgetDatasetDims : config.getTimeLabelFiled()) {
            sb.append("a." + getDimFieldCode(widgetDatasetDims) + ",");
        }
        sb.append("a." + getDimFieldCode(config.getTimeInterval()));
        sb.append(" from ");
        sb.append(tableName);
        sb.append(" as a ");
        sb.append(" left join (");
        sb.append(joinSQL + " )");
        sb.append(" as b on (");
        for (WidgetDatasetDims widgetDatasetDims : config.getTimeLabelFiled()) {
            sb.append("a." + getDimFieldCode(widgetDatasetDims) + " = b." + getDimFieldCode(widgetDatasetDims) + " and ");
        }
        sb.append("a." + getDimFieldCode(config.getBusinessField()) + " = b." + getDimFieldCode(config.getBusinessField()) + ")");

        sb.append(" where ");
        if (!config.getTimeIntervalValue().isEmpty()) {
            String in = getTimeIntervalValueIn(config);
            sb.append(" a." + getDimFieldCode(config.getTimeInterval()) + " in (" + in + ")   and ");
        }
        if (StringUtils.isNotBlank(config.getIdNumber())) {
            sb.append(" a." + getDimFieldCode(config.getIdNumberField()) + " != '" + config.getIdNumber() + "'   and ");
        }

        if (!config.getPeerTypes().isEmpty()) {
            String peerTypes = "";
            for (String peerType : config.getPeerTypes()) {
                peerTypes += "'" + peerType + "',";
            }
            peerTypes = peerTypes.substring(0, peerTypes.length() - 1);
            sb.append("a." + getMeasuresFieldCode(edge) + " in (" + peerTypes + ")   and ");
        }

        if (config.getEndTime() == null) {
            sb.append(" a." + getDimFieldCode(config.getStartTime()) + " = '" + config.getStartTimeValue() + "'   and ");
        } else {
            sb.append("(a." + getDimFieldCode(config.getStartTime()) + " >= '" + config.getStartTimeValue() + "'   and ");
            sb.append("a." + getDimFieldCode(config.getEndTime()) + " < '" + config.getEndTimeValue() + "')   and ");
        }
        //不加的话查出来的是全部的数据
        sb.append(" b." + getDimFieldCode(config.getIdNumberField()) + " is not null ");
        String sql = sb.toString();
//        return sql.substring(0, sql.length() - 6);
        return sql;
    }

    private String builderJoinSQL(PeerRelationConfig config, String tableName, WidgetDatasetDims point, long limit) {
        StringBuffer sb = new StringBuffer();
        sb.append(" select ");
        sb.append(getDimFieldCode(config.getIdNumberField()) + ",");
        boolean has = checkNodeCode(point, config);
        if (!has) {
            sb.append(getDimFieldCode(point) + ",");
        }
        //sql添加开始时间标签和结束时间标签
        for (WidgetDatasetDims dims : config.getTimeLabelFiled()) {
            sb.append(getDimFieldCode(dims) + ",");
        }
        //业务字段
        sb.append(getDimFieldCode(config.getBusinessField()));

        sb.append(" from ");
        sb.append(tableName);
        sb.append(" as c ");
        sb.append(" where ");
        //查询条件
        if (!config.getTimeIntervalValue().isEmpty()) {
            String in = getTimeIntervalValueIn(config);
            sb.append(getDimFieldCode(config.getTimeInterval()) + " in (" + in + ") ");
        }
        sb.append(" and ");
        sb.append(getDimFieldCode(config.getIdNumberField()) + " = '" + config.getIdNumber() + "'");
        /*if (limit != 0) {
            sb.append(" limit " + limit);
        }*/
        return sb.toString();
    }

    private boolean checkNodeCode(WidgetDatasetDims point, PeerRelationConfig config) {
        boolean has = false;
        String pointCode = getDimFieldCode(point);
        String idNumberField = getDimFieldCode(config.getIdNumberField());
        String businessField = getDimFieldCode(config.getBusinessField());
        for (WidgetDatasetDims widgetDatasetDims : config.getTimeLabelFiled()) {
            String dimFieldCode = getDimFieldCode(widgetDatasetDims);
            if (pointCode.equals(dimFieldCode)) {
                has = true;
            }
        }
        if (pointCode.equals(idNumberField)) {
            has = true;
        }

        if (pointCode.equals(businessField)) {
            has = true;
        }
        return has;
    }

    @NotNull
    private String getTimeIntervalValueIn(PeerRelationConfig config) {
        List<String> timeIntervalValue = Arrays.asList(config.getTimeIntervalValue().split(","));
        String in = "";
        for (String val : timeIntervalValue) {
            in += "'" + val + config.getTimeIntervalUnit() + "',";
        }
        in = in.substring(0, in.length() - 1);
        return in;
    }

    private String getDimFieldCode(WidgetDatasetDims widgetDatasetDims) {
        if (widgetDatasetDims == null) {
            return "";
        }
        if (StringUtils.isNotBlank(widgetDatasetDims.getFiledAlias())) {
            return widgetDatasetDims.getFiledAlias();
        }
        return widgetDatasetDims.getFiledCode();
    }

    private String getMeasuresFieldCode(WidgetDatasetMeasures widgetDatasetMeasures) {
        if (StringUtils.isNotBlank(widgetDatasetMeasures.getFiledAlias())) {
            return widgetDatasetMeasures.getFiledAlias();
        }
        return widgetDatasetMeasures.getFiledCode();
    }

    @Override
    public AbsRelationConfig getPeerRelationConfig(String relationConfigJson) {
        return JSONObject.parseObject(relationConfigJson, PeerRelationConfig.class);
    }

    @Override
    public List<Map> builderEdge(List<Map> points) {
        Map<String, List<Map>> relationType = points.stream().collect(Collectors.groupingBy(this::customKey));
        List<Map> resList = new ArrayList<>();
        for (Map.Entry<String, List<Map>> entry : relationType.entrySet()) {
            List<Map> value = entry.getValue();
            for (int i = 0; i < value.size(); i++) {
                Map map = value.get(i);
                Map<String, Object> edgeMap = new HashMap<>();
                int id = (int) map.get("id");
                if (id != 0) {
                    edgeMap.put("name", map.get("relationType"));
                    edgeMap.put("source", 0);
                    edgeMap.put("target", id);
                    edgeMap.put("startTime", map.get("startTime"));
                    edgeMap.put("endTime", map.get("endTime"));
                    resList.add(edgeMap);
                }
            }
        }
        return resList;
    }

    private String customKey(Map<String, Object> map) {
        return map.get("relationType").toString();
    }

    @Override
    public List<Map> builderPoint(ColumnDataModel originalColumns, String relationConfigJson, WidgetDatasetDims point, WidgetDatasetMeasures edge) {
        Map<String, List<Map>> relationTypes = originalColumns.getFieldValue().stream().collect(Collectors.groupingBy(val -> val.get(getMeasuresFieldCode(edge)).toString()));
        PeerRelationConfig config = (PeerRelationConfig) getPeerRelationConfig(relationConfigJson);
        List<Map> resList = new ArrayList<>();
        int num = 1;
        for (Map.Entry<String, List<Map>> entry : relationTypes.entrySet()) {
            for (Map<String, Object> map : entry.getValue()) {
                Map<String, Object> pointMap = new HashMap<>();
                String idNum = (String) map.get(getDimFieldCode(config.getIdNumberField()));
                String gxrzjhm = (String) map.get("gxrzjhm");
                String startTime = (String) map.get(getDimFieldCode(config.getStartTime()));
                String endTime = (String) map.get(getDimFieldCode(config.getEndTime()));
                String relationType = (String) map.get(getMeasuresFieldCode(edge));

                //查询sql过滤效率不行的话，在内存过滤，证件号码为null的数据
//                if (StringUtils.isNotBlank(idNum)) {
                pointMap.put("name", gxrzjhm);
                boolean hasField = checkNodeCode(point, config);
                if (num == 1) {
                    Map<String, Object> onePoint = new HashMap<>();
                    onePoint.put("name", idNum);
                    if (!hasField) {
                        onePoint.put("name", map.get("dqrname"));
                    }
                    onePoint.put("id", 0);
                    onePoint.put("category", 0);
                    onePoint.put("relationType", relationType);
                    onePoint.put("startTime", startTime);
                    onePoint.put("endTime", endTime);
                    resList.add(onePoint);
                }
                if (!hasField) {
                    pointMap.put("name", map.get("gxrname"));
                }
                pointMap.put("id", num);
                pointMap.put("category", 1);
                pointMap.put("relationType", relationType);
                pointMap.put("startTime", startTime);
                pointMap.put("endTime", endTime);
                resList.add(pointMap);
                num++;
//                }
            }
        }

        resList = resList.stream().sorted((s1, s2) -> {
            int id1 = (int) s1.get("id");
            int id2 = (int) s2.get("id");

            return id1 - id2;
        }).collect(Collectors.toList());
        return resList;
    }
}
