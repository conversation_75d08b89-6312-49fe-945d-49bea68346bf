package com.dragonsoft.cicada.datacenter.modules.datavisual.datapolishing.datapolishings;


import com.code.common.utils.assertion.Assert;

import java.util.Calendar;

/**
 * <AUTHOR>
 * @Date 2021/3/16 10:05
 */
public class DayDataPolishing extends AbsDataPolishing {

    private final String DAY_FORMAT = "yyyyMMdd";
    private final String DAY = "日";

    @Override
    public void calculateDate() {

        if (minDate.length() != 8) {
            granularituFail(DAY, DAY_FORMAT, minDate);
        }
        if (maxDate.length() != 8) {
            granularituFail(DAY, DAY_FORMAT, maxDate);
        }
        this.dataPolishingList = getAllDatesBetweenTwoDates(minDate, maxDate, Calendar.DAY_OF_YEAR, DAY_FORMAT, DAY);
    }

    @Override
    public void checkData(String data) {
        int temData = parseInt(data, DAY_FORMAT);
        int dayData = temData % 100;
        int monthData = ((temData % 10000) - dayData) / 100;

        if( monthData > 12 || monthData == 0){
            Assert.fail("月数最大为12月，最小为1月！");

        }
        if (dayData > 31 || dayData == 0) {
            Assert.fail("天数最大为31天，最小为1天！");
        }
    }


}
