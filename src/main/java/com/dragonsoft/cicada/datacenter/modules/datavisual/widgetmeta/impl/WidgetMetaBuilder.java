package com.dragonsoft.cicada.datacenter.modules.datavisual.widgetmeta.impl;


import com.code.common.utils.StringUtils;
import com.code.metadata.datavisual.ConfigMeta;
import com.code.metadata.datavisual.WidgetMeta;
import com.code.metaservice.datavisual.IWidgetCategoriesService;
import com.code.metaservice.datavisual.IWidgetMetaService;
import com.code.metaservice.datavisual.IWidgetService;
import com.dragonsoft.cicada.datacenter.modules.datavisual.model.annotation.WidgetConfig;
import com.dragonsoft.cicada.datacenter.modules.datavisual.model.annotation.WidgetLabel;
import com.dragonsoft.cicada.datacenter.modules.datavisual.model.enums.WidgetType;
import com.dragonsoft.cicada.datacenter.modules.datavisual.widgetmeta.IWidgetCategoriesBuilder;
import com.dragonsoft.cicada.datacenter.modules.datavisual.widgetmeta.IWidgetMetaBuild;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.util.*;

/**
 * 控件元构造器
 */
@Service
public class WidgetMetaBuilder implements IWidgetMetaBuild {
    @Autowired
    IWidgetCategoriesService widgetCategoriesService;
    @Autowired
    IWidgetMetaService widgetMetasService;
    @Autowired
    IWidgetService widgetService;
    @Autowired
    IWidgetCategoriesBuilder widgetCategoriesBuilder;


    @Override
    public List<WidgetMeta> builderList() {
        List<WidgetMeta> widgetMetas = widgetMetasService.getWidgetMetasAll();
        return widgetMetas;
    }

    @Override
    public List<WidgetMeta> builderNoChartList() {
        List<String> ids=new ArrayList<>();
        ids.add(WidgetType.SELECT.getId());
        ids.add(WidgetType.SMALL.getId());
        List<WidgetMeta> widgetMetas =widgetMetasService.getInCategoriesId(ids);
        return widgetMetas;
    }

    @Override
    public void updateWidgetMetaAll(Map<String, Object> beans) {
        //更新分组
        this.deleteNoUseWidgetMetas();//删除无用控件
        widgetCategoriesBuilder.saveWidgetCategories();
        List<WidgetMeta> updateWidgetMeta = getScanWidgetMate(beans); //获取扫描后的所有数据元
        widgetMetasService.saveUpdateWidgetMetas(updateWidgetMeta);//保存或更新控件元
    }


    /**
     * 删除未被使用的控件元
     */
    private void deleteNoUseWidgetMetas() {
        List<WidgetMeta> list = widgetMetasService.getWidgetMetasAll();
        for (WidgetMeta m : list) {
            boolean check = widgetService.checkExistByMetaId(m.getId());
            if (!check) {
                widgetMetasService.deleteWidgetMetas(m);
            }
        }
    }


    private WidgetMeta getWidgetMetaByWidgetCode(String code, List<WidgetMeta> dbWidgetMetas) {
        for (WidgetMeta w : dbWidgetMetas) {
            if (code.equals(w.getCode())) {
                return w;
            }
        }
        return null;
    }

    /**
     * 获取需要新增的控件信息
     *
     * @param beans
     * @return
     */
    private List<WidgetMeta> getScanWidgetMate(Map<String, Object> beans) {
        List<WidgetMeta> dbWidgetMetas = widgetMetasService.getWidgetMetasAll();
        List<WidgetMeta> newW = new ArrayList<>();
        for (Map.Entry<String, Object> b : beans.entrySet()) {
            Object o = b.getValue();
            WidgetLabel ls = o.getClass().getAnnotation(WidgetLabel.class);
            String code = ls.code();
            if (StringUtils.isBlank(code)) {
                code = o.getClass().getSimpleName();
            }
            WidgetMeta widgetMeta = this.getWidgetMetaByWidgetCode(code, dbWidgetMetas);
            if (null == widgetMeta) {
                widgetMeta = new WidgetMeta();
            }
            widgetMeta.setCode(code);
            widgetMeta.setType(o.getClass().getName());
            widgetMeta.setName(ls.name());
            widgetMeta.setCategoryId(ls.type().getId());
            widgetMeta.setOrderBy(ls.orderBy());
            this.setWidgetConfig(o, widgetMeta);
            //给数据元设置控件属性
            newW.add(widgetMeta);//添加到新的控件集合中
        }
        return newW;
    }

    /**
     * 设置控件配置
     *
     * @param o
     * @param widgetMeta
     */
    private void setWidgetConfig(Object o, WidgetMeta widgetMeta) {
        Set<ConfigMeta> configMetas = new HashSet<>();
        List<Field> fields = Arrays.asList(o.getClass().getDeclaredFields());
        for (Field f : fields) {
            if (f.isAnnotationPresent(WidgetConfig.class)) {
                WidgetConfig w = f.getAnnotation(WidgetConfig.class);
                String attr = w.attrName();
                if (StringUtils.isBlank(attr)) {
                    attr = f.getName();
                }
                ConfigMeta configMeta = this.getDbConfigMetasEntity(attr, widgetMeta.getConfigMetas());//获取数据库中的配置信息
                if (null == configMeta) { //如果数据库没有
                    configMeta = new ConfigMeta();//新增一个
                }
                configMeta.setAttribute(attr);
                configMeta.setConfigGroup(w.group());
                configMetas.add(configMeta);//添加到新的配置元集合
            }
        }
        widgetMeta.setConfigMetas(configMetas);
    }

    private ConfigMeta getDbConfigMetasEntity(String attr, Set<ConfigMeta> dbConfigMetas) {
        for (ConfigMeta c : dbConfigMetas) {
            if (c.getAttribute().equals(attr)) {
                return c;
            }

        }
        return null;
    }

}
