package com.dragonsoft.cicada.datacenter.modules.system.busmanagement.vo;

import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;
import lombok.Data;

import java.util.List;

@Data
public class ServiceInfoVo extends BusCommonVo {

    private String serviceId;

    private String busId;

    private List<ServiceConfigVo> requestHeaderCfgList = Lists.newArrayList();

    private List<ServiceConfigVo> responseHeaderCfgList = Lists.newArrayList();

    public static ServiceInfoVo getHeaderCfgModel() {
        ServiceInfoVo serviceInfoVo = new ServiceInfoVo();
        List<ServiceConfigVo> requestHeaderCfgList = JSONArray.parseArray(ServiceConfigVo.REQUEST_MODEL, ServiceConfigVo.class);
        List<ServiceConfigVo> responseHeaderCfgList = JSONArray.parseArray(ServiceConfigVo.RESPONSE_MODEL, ServiceConfigVo.class);
        serviceInfoVo.setRequestHeaderCfgList(requestHeaderCfgList);
        serviceInfoVo.setResponseHeaderCfgList(responseHeaderCfgList);
        return serviceInfoVo;
    }

}
