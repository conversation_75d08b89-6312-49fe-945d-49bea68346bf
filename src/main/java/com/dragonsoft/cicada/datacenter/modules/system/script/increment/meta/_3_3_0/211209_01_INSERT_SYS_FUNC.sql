INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('AIModelingBuildApi', '0', '生成API', 'AIModeling', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('AIModelingTestApi', '0', '测试API', 'AIModeling', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1');
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('20652e94a9cf4653913e2f00e5475ebe', 'd6121bc4248e45019942e2cb78362500', 'AIModelingBuildApi', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('b45e20cfe29047bb9e6ac224f8f1a279', 'd6121bc4248e45019942e2cb78362500', 'AIModelingTestApi', NULL, NULL, NULL, NULL, NULL, NULL, NULL);

