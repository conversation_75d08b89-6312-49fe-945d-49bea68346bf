package com.dragonsoft.cicada.datacenter.modules.datavisual.model.enums;

import com.dragonsoft.cicada.datacenter.modules.datavisual.model.NUMFilter;
import com.dragonsoft.cicada.datacenter.modules.datavisual.model.StringFilter;
import com.dragonsoft.cicada.datacenter.modules.datavisual.model.TimeFilter;
import com.dragonsoft.cicada.datacenter.modules.datavisual.model.TreeFilter;

public enum FilterEnum {
    TIME_FILTER("time", TimeFilter.class),
    STRING_FILTER("text", StringFilter.class),
    NUMBER_FILTER("number", NUMFilter.class),
    TREE_FILTER("tree", TreeFilter.class);

    private String type;
    private Class c;

    FilterEnum(String type, Class c) {
        this.type = type;
        this.c = c;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Class getC() {
        return c;
    }

    public void setC(Class c) {
        this.c = c;
    }

    public static Class getClassByType(String type) {
        for (FilterEnum value : values()) {
            if (value.type.equals(type)) {
                return value.c;
            }
        }
        return null;
    }
}