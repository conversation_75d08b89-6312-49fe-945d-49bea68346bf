--算子树
INSERT INTO "public"."t_md_busi_dir"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id", "is_show", "busi_dir_type", "sort_no") VALUES ('7621843a04ab41e955361ef589a6b58d', '常量算子', NULL, 'BusiDir', '', 'CONSTANT_OPERATOR', '', '', '', '', '', ' ', 'CONSTANT_OPERATOR', NULL);

INSERT INTO "public"."t_md_busi_dir"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id", "is_show", "busi_dir_type", "sort_no") VALUES ('7621843a04ab41e955361ef589a6ds1w', '变量算子', NULL, 'BusiDir', '', 'VARIABLE_OPERATOR', '', '', '', '', '', ' ', 'VARIABLE_OPERATOR', NULL);

INSERT INTO "public"."t_md_busi_dir"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id", "is_show", "busi_dir_type", "sort_no") VALUES ('7621843a04ab41e955361ef589a6dlpw', '通用算子', NULL, 'BusiDir', '', 'UDF_OPERATOR', '', '', '', '', '', ' ', 'UDF_OPERATOR', NULL);


INSERT INTO "public"."t_md_busi_classify"
("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id", "busi_dir_id", "parent_classify_id", "sort_no", "res_count", "x", "y")
VALUES('92bebf124ws541d489623f98561fdkis', '计算算子', NULL, 'BusiClassify', NULL, 'finance', NULL, NULL, 'UDF_OPERATOR', NULL, NULL, '7621843a04ab41e955361ef589a6dlpw', NULL, 4, NULL, NULL, NULL);


INSERT INTO "public"."t_md_busi_classify"
("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id", "busi_dir_id", "parent_classify_id", "sort_no", "res_count", "x", "y")
VALUES('92bebf124ws541d489623f98561fdhys', '文本算子', NULL, 'BusiClassify', NULL, 'finance', NULL, NULL, 'UDF_OPERATOR', NULL, NULL, '7621843a04ab41e955361ef589a6dlpw', NULL, 4, NULL, NULL, NULL);

INSERT INTO "public"."t_md_busi_classify"
("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id", "busi_dir_id", "parent_classify_id", "sort_no", "res_count", "x", "y")
VALUES('92bebf124ws541d489623f98561fdiph', '时间处理算子', NULL, 'BusiClassify', NULL, 'finance', NULL, NULL, 'UDF_OPERATOR', NULL, NULL, '7621843a04ab41e955361ef589a6dlpw', NULL, 4, NULL, NULL, NULL);

