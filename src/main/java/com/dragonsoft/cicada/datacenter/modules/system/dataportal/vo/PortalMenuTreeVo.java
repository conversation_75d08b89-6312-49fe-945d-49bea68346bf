package com.dragonsoft.cicada.datacenter.modules.system.dataportal.vo;

import com.google.common.collect.Lists;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/9/01
 */
@Data
public class PortalMenuTreeVo extends MenuAuthTree {

    public static final String DEFAULT_FOLD = "0";
    public static final String MENU_FOLD = "1";

    private Boolean isMenuFold = true;//允许折叠

    private Boolean defaultMenuFold = true;//默认折叠

    private Boolean isNullNode = true;//空结点

    private String viewType;//查看方式 当前页面、新窗口

    private String connectType;//连接类型 仪表板、外部连接

    private String getUrl;//get链接

    private String postRul;//post链接

    private String postParam;//post参数

    private Boolean isDefaultHomePage = false;//是默认主页

    private List<PortalMenuTreeVo> portalMenuChildrenList = Lists.newArrayList();

    public void setFoldType(String foldType){
        if (DEFAULT_FOLD.equals(foldType)) {
            this.setDefaultMenuFold(true);
            this.setIsMenuFold(true);
        } else if(MENU_FOLD.equals(foldType)){
            this.setDefaultMenuFold(false);
            this.setIsMenuFold(true);
        }else {
            this.setDefaultMenuFold(false);
            this.setIsMenuFold(false);
        }
    }
}
