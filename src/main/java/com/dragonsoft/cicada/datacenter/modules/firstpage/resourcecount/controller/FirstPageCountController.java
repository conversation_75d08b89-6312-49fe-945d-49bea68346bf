package com.dragonsoft.cicada.datacenter.modules.firstpage.resourcecount.controller;

import com.code.common.utils.R;
import com.dragoninfo.dfw.bean.Result;
import com.dragonsoft.cicada.datacenter.modules.firstpage.resourcecount.service.MyResourceService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpSession;
import java.util.Map;

@RestController
@Slf4j
@RequestMapping("/firstPage")
@CrossOrigin
public class FirstPageCountController {


    @Autowired
    private MyResourceService myResourceService;

    @GetMapping("/myResourceCount")
    @ApiOperation(value = "我的资源个数统计")
    public Result getResourceCount(HttpSession session){
        try {
            String userId = (String) session.getAttribute("userId");
            Map<String, Object> myResourceCount = myResourceService.getMyResourceCount(userId);
            return Result.toResult(R.ok(myResourceCount));
        } catch (Exception e) {
            log.error(e.getMessage(),e);
//            return Result.toResult(R.error(e.getMessage()));
        }
        return Result.success();
    }

    @GetMapping("/allResourceCount")
    @ApiOperation(value = "我的资源个数统计")
    public Result getAllResourceCount(){
        try {
            Map<String, Object> myResourceCount = myResourceService.getAllResourceCount();
            return Result.toResult(R.ok(myResourceCount));
        } catch (Exception e) {
            log.error(e.getMessage(),e);
//            return Result.toResult(R.error(e.getMessage()));
        }
        return Result.success();
    }
}
