package com.dragonsoft.cicada.datacenter.modules.firstpage.share.service.impl;

import cn.hutool.core.util.StrUtil;
import com.code.common.paging.PageInfo;
import com.code.common.utils.StringUtils;
import com.code.common.utils.assertion.Assert;
import com.code.metadata.datavisual.Dashboard;
import com.code.metadata.model.core.ModelElement;
import com.code.metadata.res.ddl.LogicDataObj;
import com.code.metadata.sm.EnumServiceType;
import com.code.metadata.sm.ServiceMeta;
import com.code.metaservice.ddl.LogicDataObjServiceImpl;
import com.dragoninfo.dfw.entity.TSysAuthObjFunc;
import com.dragoninfo.dfw.entity.TSysAuthObjRel;
import com.dragoninfo.dfw.entity.TSysAuthUser;
import com.dragoninfo.dfw.entity.TSysFuncBase;
import com.dragoninfo.dfw.service.SysAuthUserService;
import com.dragonsoft.cicada.datacenter.modules.firstpage.share.service.DataShareAuthRelService;
import com.dragonsoft.cicada.datacenter.modules.firstpage.share.vo.ResourceTypeEnum;
import com.dragonsoft.cicada.datacenter.modules.firstpage.share.vo.ResultResource;
import com.dragonsoft.cicada.datacenter.modules.firstpage.share.vo.ShareVo;
import com.fw.service.BaseService;
import com.fw.service.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class DataShareAuthRelServiceImpl extends BaseService implements DataShareAuthRelService {

    public static final String TSYS_AUTH_OBJ_REL_HQL = " from TSysAuthObjRel tr where tr.fromAuthObj.id = :userId and tr.relationType = '1' ";
    public static final String TSYS_AUTH_OBJ_FUNC_HQL = "from TSysAuthObjFunc tsf where tsf.tSysAuthObj.id = :userId or tsf.tSysAuthObj.id in (:roleIds) order by createTime desc";
    @Autowired
    private SysAuthUserService sysAuthUserService;

    @Autowired
    private LogicDataObjServiceImpl logicDataObjService;


    @Override
    public PageInfo getSharesFromOthers(int pageNum, int pageSize, String userId, String resourceName, String resourceType, List<String> fromUserIds) {
        //查询该用户所属角色
        List<TSysAuthObjRel> trs = this.baseDao.queryForList(TSYS_AUTH_OBJ_REL_HQL,
                this.addParam("userId", userId).param());

        String hql = TSYS_AUTH_OBJ_FUNC_HQL;
        Map<String,Object> map = new HashMap<>();
        List<String> roleIds = new ArrayList<>();
        for (TSysAuthObjRel tr : trs) {
            roleIds.add(tr.getToAuthObj().getId());
        }
        map.put("userId",userId);
        map.put("roleIds",roleIds);
        List<TSysAuthObjFunc> list = this.baseDao.queryForList(hql, map);
        Set<String> dataSetfuncCodes = new LinkedHashSet<>();
        Set<String> dataServiceFuncCodes = new LinkedHashSet<>();
        Set<String> dashboardFuncCodes = new LinkedHashSet<>();
        Set<String> aiServiceFuncCodes = new LinkedHashSet<>();
        Set<String> modelServiceFuncCodes = new LinkedHashSet<>();
        Set<String> compareServiceFuncCodes = new LinkedHashSet<>();
        Set<String> informationVerificationFuncCodes = new LinkedHashSet<>();
        Set<String> dataCollisionFuncCodes = new LinkedHashSet<>();
        for (TSysAuthObjFunc tSysAuthObjFunc : list) {
            TSysFuncBase tSysFuncBase = tSysAuthObjFunc.gettSysFuncBase();
            if (tSysFuncBase == null){
                continue;
            }
            if (ResourceTypeEnum.DATA_SET.getCode().equals(tSysFuncBase.getFuncType())){
                dataSetfuncCodes.add(tSysAuthObjFunc.getId());
            }else if (ResourceTypeEnum.DATA_SERVICE.getCode().equals(tSysFuncBase.getFuncType())){
                dataServiceFuncCodes.add(tSysAuthObjFunc.getId());
            }else if (ResourceTypeEnum.DASHBOARD.getCode().equals(tSysFuncBase.getFuncType())){
                dashboardFuncCodes.add(tSysAuthObjFunc.getId());
            }else if (ResourceTypeEnum.AI_SERVICE.getCode().equals(tSysFuncBase.getFuncType())){
                aiServiceFuncCodes.add(tSysAuthObjFunc.getId());
            }else if (ResourceTypeEnum.MODEL_SERVICE.getCode().equals(tSysFuncBase.getFuncType())){
                modelServiceFuncCodes.add(tSysAuthObjFunc.getId());
            }else if (ResourceTypeEnum.COMPARE_SERVICE.getCode().equals(tSysFuncBase.getFuncType())){
                compareServiceFuncCodes.add(tSysAuthObjFunc.getId());
            }else if (ResourceTypeEnum.INFORMATION_VERFICATION.getCode().equals(tSysFuncBase.getFuncType())){
                informationVerificationFuncCodes.add(tSysAuthObjFunc.getId());
            }else if (ResourceTypeEnum.DATA_COLLISION.getCode().equals(tSysFuncBase.getFuncType())){
                dataCollisionFuncCodes.add(tSysAuthObjFunc.getId());
            }
        }
        PageInfo pageInfo = new PageInfo();
        //数据集
        if (StrUtil.isNotEmpty(resourceType)){
            Map<String, Object> dataShareStr = new HashMap<>();
            if (ResourceTypeEnum.DATA_SET.getName().equals(resourceType)){
                dataShareStr = getDataShareStr(resourceType, resourceName, fromUserIds, dataSetfuncCodes,userId);
            }else if (ResourceTypeEnum.DATA_SERVICE.getName().equals(resourceType)){
                dataShareStr = getDataShareStr(resourceType, resourceName, fromUserIds, dataServiceFuncCodes,userId);
            }else if (ResourceTypeEnum.DASHBOARD.getName().equals(resourceType)){
                dataShareStr = getDataShareStr(resourceType, resourceName, fromUserIds, dashboardFuncCodes,userId);
            }else if (ResourceTypeEnum.AI_SERVICE.getName().equals(resourceType)){
                dataShareStr = getDataShareStr(resourceType, resourceName, fromUserIds, aiServiceFuncCodes,userId);
            }else if (ResourceTypeEnum.MODEL_SERVICE.getName().equals(resourceType)){
                dataShareStr = getDataShareStr(resourceType, resourceName, fromUserIds, modelServiceFuncCodes,userId);
            }else if (ResourceTypeEnum.COMPARE_SERVICE.getName().equals(resourceType)){
                dataShareStr = getDataShareStr(resourceType, resourceName, fromUserIds, compareServiceFuncCodes,userId);
            }else if (ResourceTypeEnum.INFORMATION_VERFICATION.getName().equals(resourceType)){
                dataShareStr = getDataShareStr(resourceType, resourceName, fromUserIds, informationVerificationFuncCodes,userId);
            }else if (ResourceTypeEnum.DATA_COLLISION.getName().equals(resourceType)){
                dataShareStr = getDataShareStr(resourceType, resourceName, fromUserIds, dataCollisionFuncCodes,userId);
            }
            pageInfo = getPageInfoOfCond(pageSize, pageNum, dataShareStr, list, resourceType);
        }else {
            //全部
            pageInfo = getAllPageInfo(pageSize,pageNum,resourceName,fromUserIds,dataSetfuncCodes,dataServiceFuncCodes,dashboardFuncCodes,aiServiceFuncCodes,modelServiceFuncCodes,list,userId,compareServiceFuncCodes,informationVerificationFuncCodes,dataCollisionFuncCodes);
        }
       /* List<ResultResource> dataList = pageInfo.getDataList();
        List<ResultResource> newList = dataList.stream().sorted(Comparator.comparing(ResultResource::getCreateTime).reversed()).collect(Collectors.toList());
        pageInfo.setDataList(newList);*/
        return pageInfo;
    }

    @Override
    public List<TSysAuthUser> getOtherUsers(String myUserId,String objType) {
        if (StrUtil.isEmpty(myUserId)){
            myUserId = "";
        }
        String hql = " from TSysAuthUser where id != :myUserId and objType = :objType ";
        return this.baseDao.queryForList(hql,this.addParam("myUserId",myUserId).addParam("objType",objType).param());
    }

    @Override
    public PageInfo getMySharesByPage(ShareVo shareVo, String myUserId) {
        int pageNum = shareVo.getPageNum();
        int pageSize = shareVo.getPageSize();
        String resourceType = shareVo.getResourceType();
        String resourceName = shareVo.getResourceName();
        String objType = shareVo.getUserType();
        String username = shareVo.getUsername();
        List<String> myFuncCodes = new ArrayList<>();
        List myResource = new ArrayList();
        if (StrUtil.isNotEmpty(resourceType)) {
            List<String> resourceTypeList = Arrays.asList(resourceType);
            if ("serviceAll".equals(resourceType)){
                resourceTypeList = Arrays.asList(
                        ResourceTypeEnum.DATA_SERVICE.getName(),
                        ResourceTypeEnum.MODEL_SERVICE.getName(),
                        ResourceTypeEnum.COMPARE_SERVICE.getName(),
                        ResourceTypeEnum.INFORMATION_VERFICATION.getName(),
                        ResourceTypeEnum.DATA_COLLISION.getName());
            }
            resourceTypeList.forEach(k -> {
                Map<String, Object> myShareCondStr = getMyShareCondStr(k, resourceName, myUserId);
                List list = this.baseDao.queryForList((String) myShareCondStr.get("hql"), (Map) myShareCondStr.get("condition"));
                myResource.addAll(list);
            });
        }else {
            for (ResourceTypeEnum value : ResourceTypeEnum.values()) {
                Map<String, Object> myShareCondStr = getMyShareCondStr(value.getName(), resourceName, myUserId);
                List list = this.baseDao.queryForList((String) myShareCondStr.get("hql"), (Map) myShareCondStr.get("condition"));
                myResource.addAll(list);
            }
        }
        for (Object o : myResource) {
            ModelElement model = (ModelElement) o;
            myFuncCodes.add(model.getId());
        }
        Map<String,Object> map = new HashMap<>();
        String myShareStr = " from TSysAuthObjFunc tsf where tsf.tSysFuncBase.funcCode in (:myFuncCodes) and tsf.createObj.id is null ";
        if (StrUtil.isNotEmpty(objType)){
            myShareStr += " and tsf.tSysAuthObj.objType = :objType ";
            map.put("objType",objType);
        }
        if (StrUtil.isNotEmpty(username)){
            myShareStr += "and tsf.tSysAuthObj.objName like :username ";
            map.put("username","%"+username+"%");
        }
        myShareStr += " and tsf.tSysAuthObj.id != :myUserId ";
        map.put("myUserId",myUserId);
        myShareStr += " order by tsf.createTime desc ";
        map.put("myFuncCodes",myFuncCodes);
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageIndex(pageNum);
        pageInfo.setPageSize(pageSize);
        //String hqlDataSet = " from TSysAuthObjFunc tsf where tsf.tSysFuncBase.funcType = :resourceType order by createTime desc";
        PageInfo pageInfo1 = this.baseDao.queryForPage(myShareStr, map, pageInfo);
        return setMyShareResultPageInfo(pageInfo1,myUserId);
    }

    @Override
    public List<String> getDataAuthByUserId(String userId) {
        List<TSysAuthObjRel> trs = this.baseDao.queryForList(TSYS_AUTH_OBJ_REL_HQL,
                this.addParam("userId", userId).param());

        String hql = TSYS_AUTH_OBJ_FUNC_HQL;
        Map<String,Object> map = new HashMap<>();
        List<String> roleIds = new ArrayList<>();
        for (TSysAuthObjRel tr : trs) {
            roleIds.add(tr.getToAuthObj().getId());
        }
        map.put("userId",userId);
        map.put("roleIds",roleIds);
        List<TSysAuthObjFunc> list = this.baseDao.queryForList(hql, map);
        List<String> dataSetfuncCodes = new ArrayList<>();
        for (TSysAuthObjFunc tSysAuthObjFunc : list) {
            TSysFuncBase tSysFuncBase = tSysAuthObjFunc.gettSysFuncBase();
            if (tSysFuncBase == null){
                continue;
            }
            if ("1".equals(tSysFuncBase.getFuncType())){
                dataSetfuncCodes.add(tSysAuthObjFunc.gettSysFuncBase().getFuncCode());
            }
        }
        return dataSetfuncCodes;
    }

    @Override
    public List<String> getDataFuncIdByUserId(String userId) {
        List<TSysAuthObjRel> trs = this.baseDao.queryForList(TSYS_AUTH_OBJ_REL_HQL,
                this.addParam("userId", userId).param());

        String hql = TSYS_AUTH_OBJ_FUNC_HQL;
        Map<String,Object> map = new HashMap<>();
        List<String> roleIds = new ArrayList<>();
        for (TSysAuthObjRel tr : trs) {
            roleIds.add(tr.getToAuthObj().getId());
        }
        map.put("userId",userId);
        map.put("roleIds",roleIds);
        List<TSysAuthObjFunc> list = this.baseDao.queryForList(hql, map);
        List<String> dataSetfuncCodes = new ArrayList<>();
        for (TSysAuthObjFunc tSysAuthObjFunc : list) {
            TSysFuncBase tSysFuncBase = tSysAuthObjFunc.gettSysFuncBase();
            if (tSysFuncBase == null){
                continue;
            }
            if ("1".equals(tSysFuncBase.getFuncType())){
                dataSetfuncCodes.add(tSysAuthObjFunc.getId());
            }
        }
        return dataSetfuncCodes;
    }


    private Map<String,Object> getDataShareStr(String resourceType,String resourceName,List<String> fromUserIds,Set<String> dataFuncCodes,String myUserId){
        /*String sql = "select count(*) from t_sys_auth_obj_func where func_code in (:dataFuncCodes) and obj_id = :myUserId";
        Map<String,Object> map = new HashMap<>();
        map.put("dataFuncCodes",dataFuncCodes);
        map.put("myUserId",myUserId);
        String s = "0";
        if (dataFuncCodes.size()>0){
            s = this.baseDao.sqlQueryForValue(sql, map);
        }*/
        StringBuilder sbHql = new StringBuilder(" SELECT  s.id,min(tf.create_time) as create_time,s.name,s.operate_user_id from ");
        Map<String,Object> condition = new HashMap<>();
        if (StrUtil.isNotEmpty(resourceType)){
            if (ResourceTypeEnum.DATA_SET.getName().equals(resourceType)){
                sbHql.append(" t_md_logic_dataobj s ");
            }else if (ResourceTypeEnum.DATA_SERVICE.getName().equals(resourceType) ||
                        ResourceTypeEnum.AI_SERVICE.getName().equals(resourceType) ||
                            ResourceTypeEnum.MODEL_SERVICE.getName().equals(resourceType) ||
                    ResourceTypeEnum.COMPARE_SERVICE.getName().equalsIgnoreCase(resourceType)||
                    ResourceTypeEnum.INFORMATION_VERFICATION.getName().equalsIgnoreCase(resourceType)||
                    ResourceTypeEnum.DATA_COLLISION.getName().equalsIgnoreCase(resourceType)){
                sbHql.append(" t_md_service_meta s inner join t_md_service_publication p on s.service_id = p.id ");
            }else if (ResourceTypeEnum.DASHBOARD.getName().equals(resourceType)){
                sbHql.append(" t_v_dashboards s ");
            }
            //on s.id =  tf.tSysFuncBase.funcCode
            sbHql.append(" INNER JOIN t_sys_auth_obj_func tf on s.id = tf.func_code ");
           /* if (!"0".equals(s)){
                sbHql.append(" and tf.obj_id = :curUserId ");
                condition.put("curUserId",myUserId);
            }*/
            if (ResourceTypeEnum.DATA_SERVICE.getName().equals(resourceType) ||
                    ResourceTypeEnum.AI_SERVICE.getName().equals(resourceType) ||
                    ResourceTypeEnum.MODEL_SERVICE.getName().equals(resourceType) ||
                    ResourceTypeEnum.COMPARE_SERVICE.getName().equalsIgnoreCase(resourceType)||
                    ResourceTypeEnum.INFORMATION_VERFICATION.getName().equalsIgnoreCase(resourceType)||
                    ResourceTypeEnum.DATA_COLLISION.getName().equalsIgnoreCase(resourceType)){
                String serviceType = getServiceType(resourceType);
                sbHql.append(" and p.service_type = '"+serviceType+"'");
            }
            sbHql.append(" and tf.id in (:funcCodes)  ");
            condition.put("funcCodes",dataFuncCodes);
            if (fromUserIds.size()>0){
                sbHql.append( " and s.operate_user_id in (:fromUserIds) ");
                condition.put("fromUserIds",fromUserIds);
            }
            sbHql.append(" and s.operate_user_id != :myUserId ");
            condition.put("myUserId",myUserId);
            if (StrUtil.isNotEmpty(resourceName)){
                sbHql.append(" and s.name like :resourceName ");
                condition.put("resourceName","%"+resourceName+"%");
            }
            sbHql.append(" GROUP BY s.id ORDER BY min(tf.create_time) desc");
        }
        Map<String,Object> hqlAndCon = new HashMap<>();
        hqlAndCon.put("hql",sbHql.toString());
        hqlAndCon.put("condition",condition);
        return hqlAndCon;
    }

    private PageInfo getPageInfoOfCond(int pageSize,int pageNum,Map<String,Object> hqlAndCon,List<TSysAuthObjFunc> objFuncs,String resourceType){
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageSize(pageSize);
        pageInfo.setPageIndex(pageNum);
        String hql = (String) hqlAndCon.get("hql");
        Map<String,Object> condition = (Map<String, Object>) hqlAndCon.get("condition");
        //Object condition1 = condition.get("condition");
        Set<String> funcCodes = (Set<String>) condition.get("funcCodes");
        if (funcCodes.size() <= 0){
            return new PageInfo();
        }
        PageInfo pageInfo1 = this.baseDao.sqlQueryForPage(hql, condition, pageInfo);
        return setResultPageInfo(pageSize,pageNum,pageInfo1,objFuncs, resourceType);
    }

    private PageInfo setResultPageInfo(int pageSize,int pageNum,PageInfo pageInfo,List<TSysAuthObjFunc> objFuncs,String resourceType){
        PageInfo resultPageInfo = new PageInfo();
        List<ResultResource> resultResources = setResultLists(pageInfo.getDataList(), objFuncs, resourceType);
        resultPageInfo.setPageSize(pageSize);
        resultPageInfo.setPageIndex(pageNum);
        resultPageInfo.setDataList(resultResources);
        resultPageInfo.setTotalCount(pageInfo.getTotalCount());
        resultPageInfo.setPageCount(pageInfo.getPageCount());
        resultPageInfo.setLimitPageCount(pageInfo.getLimitPageCount());
        return resultPageInfo;
    }

    private List<ResultResource> setResultLists(List dataList,List<TSysAuthObjFunc> objFuncs,String resourceType){
        List<ResultResource> resultResources = new ArrayList<>();
        for (Object o : dataList) {
            Map<String,Object> map = (Map<String, Object>) o;
            ResultResource resultResource = new ResultResource();
            resultResource.setCreateUserId((String) map.get("operate_user_id"));
            resultResource.setResourceId((String) map.get("id"));
            resultResource.setResourceType(resourceType);
           /* for (TSysAuthObjFunc tSysAuthObjFunc : objFuncs) {
                if (Objects.equals(tSysAuthObjFunc.gettSysFuncBase().getFuncCode(),model.getId())){
                    resultResource.setShareTime(tSysAuthObjFunc.getCreateTime());
                    break;
                }
            }*/

            resultResource.setShareTime((Date) map.get("create_time"));
            if (ResourceTypeEnum.DATA_SERVICE.getName().equals(resourceType) ||
                    ResourceTypeEnum.AI_SERVICE.getName().equals(resourceType) ||
                        ResourceTypeEnum.MODEL_SERVICE.getName().equals(resourceType) ||
                            ResourceTypeEnum.COMPARE_SERVICE.getName().equals(resourceType) ||
                                ResourceTypeEnum.INFORMATION_VERFICATION.getName().equals(resourceType) ||
                                    ResourceTypeEnum.DATA_COLLISION.getName().equals(resourceType)){
                String serviceMetaId = (String) map.get("id");
                ServiceMeta serviceMeta = (ServiceMeta) this.baseDao.get(ServiceMeta.class, serviceMetaId);
                if (serviceMeta != null && serviceMeta.getServicePublication() != null) {
                    resultResource.setResourceName(serviceMeta.getServicePublication().getName());
                    resultResource.setServiceId(serviceMeta.getServicePublication().getId());
                }
            }else {
                resultResource.setResourceName((String) map.get("name"));
            }


            Map<String,String> map1 = new HashMap<>();
            map1.put("id", (String) map.get("operate_user_id"));
            TSysAuthUser tSysAuthUser = (TSysAuthUser) sysAuthUserService.query(map1);
            resultResource.setCreateUser(tSysAuthUser.getObjName());
            resultResources.add(resultResource);
        }
        List<ResultResource> newList = resultResources.stream().sorted(Comparator.comparing(ResultResource::getShareTime).reversed()).collect(Collectors.toList());
        return newList;
    }

    private PageInfo getAllPageInfo(int pageSize,int pageNum,String resourceName,List<String> fromUserIds,Set<String> dataSetfuncCodes,Set<String> dataServiceFuncCodes,
            Set<String> dashboardFuncCodes,Set<String> aiServiceFuncCodes,Set<String> modelServiceFuncCodes,List<TSysAuthObjFunc> objFuncs,String myUserId,Set<String> compareServiceFuncCodes,Set<String> informationVerificationFuncCodes,Set<String> dataCollisionFuncCodes){

        Map<String,Object> dataSetMap = getDataShareStr("dataSet", resourceName, fromUserIds, dataSetfuncCodes,myUserId);
        String setHql = (String) dataSetMap.get("hql");
        Map<String,Object> setCondition = (Map<String, Object>) dataSetMap.get("condition");
        Set<String> funcCodesDataSet = (Set<String>) setCondition.get("funcCodes");
        List<ResultResource> setResultResources = new ArrayList<>();
        if (funcCodesDataSet.size() > 0){
            List setList = this.baseDao.sqlQueryForList(setHql, setCondition);
            setResultResources = setResultLists(setList, objFuncs,"dataSet");
        }

        Map<String,Object> dataServiceMap = getDataShareStr("dataService", resourceName, fromUserIds, dataServiceFuncCodes,myUserId);
        String serviceHql = (String) dataServiceMap.get("hql");
        Map<String,Object> serviceCondition = (Map<String, Object>) dataServiceMap.get("condition");
        Set<String> funcCodes = (Set<String>) serviceCondition.get("funcCodes");
        List<ResultResource> serviceResultResources = new ArrayList<>();
        if (funcCodes.size() > 0){
            List serviceList = this.baseDao.sqlQueryForList(serviceHql, serviceCondition);
            serviceResultResources = setResultLists(serviceList, objFuncs,"dataService");
        }
        //仪表盘
        Map<String,Object> dashboardMap = getDataShareStr("dashboard", resourceName, fromUserIds, dashboardFuncCodes, myUserId);
        String dashboardHql = (String) dashboardMap.get("hql");
        Map<String,Object> dashboardCondition = (Map<String, Object>) dashboardMap.get("condition");
        Set<String> funcCodesDashboard = (Set<String>) dashboardCondition.get("funcCodes");
        List<ResultResource> dashboardResultResources = new ArrayList<>();
        if (funcCodesDashboard.size() > 0){
            List dashboardList = this.baseDao.sqlQueryForList(dashboardHql, dashboardCondition);
            dashboardResultResources = setResultLists(dashboardList, objFuncs,"dashboard");
        }

        Map<String,Object> aiServiceMap = getDataShareStr(ResourceTypeEnum.AI_SERVICE.getName(), resourceName, fromUserIds, aiServiceFuncCodes,myUserId);
        String aiServiceHql = (String) aiServiceMap.get("hql");
        Map<String,Object> aiServiceCondition = (Map<String, Object>) aiServiceMap.get("condition");
        Set<String> funcCodesAi = (Set<String>) aiServiceCondition.get("funcCodes");
        List<ResultResource> aiServiceResultResources = new ArrayList<>();
        if (funcCodesAi.size() > 0){
            List aiServiceList = this.baseDao.sqlQueryForList(aiServiceHql, aiServiceCondition);
            aiServiceResultResources = setResultLists(aiServiceList, objFuncs,ResourceTypeEnum.AI_SERVICE.getName());
        }

        Map<String,Object> modelServiceMap = getDataShareStr(ResourceTypeEnum.MODEL_SERVICE.getName(), resourceName, fromUserIds, modelServiceFuncCodes,myUserId);
        String modelServiceHql = (String) modelServiceMap.get("hql");
        Map<String,Object> modelServiceCondition = (Map<String, Object>) modelServiceMap.get("condition");
        Set<String> funcCodesModel = (Set<String>) modelServiceCondition.get("funcCodes");
        List<ResultResource> modelServiceResultResources = new ArrayList<>();
        if (funcCodesModel.size() > 0){
            List modelServiceList = this.baseDao.sqlQueryForList(modelServiceHql, modelServiceCondition);
            modelServiceResultResources = setResultLists(modelServiceList, objFuncs,ResourceTypeEnum.MODEL_SERVICE.getName());
        }


        List<ResultResource> compareServiceResultResources = getResultResourceList(ResourceTypeEnum.COMPARE_SERVICE, resourceName, fromUserIds, compareServiceFuncCodes, myUserId, objFuncs);
        List<ResultResource> informationVerificationResultResources = getResultResourceList(ResourceTypeEnum.INFORMATION_VERFICATION, resourceName, fromUserIds, informationVerificationFuncCodes, myUserId, objFuncs);
        List<ResultResource> dataCollisionResultResources = getResultResourceList(ResourceTypeEnum.DATA_COLLISION, resourceName, fromUserIds, dataCollisionFuncCodes, myUserId, objFuncs);

        List<ResultResource> resourcesBeforePage = new ArrayList<>();
        resourcesBeforePage.addAll(setResultResources);
        resourcesBeforePage.addAll(serviceResultResources);
        resourcesBeforePage.addAll(dashboardResultResources);
        resourcesBeforePage.addAll(aiServiceResultResources);
        resourcesBeforePage.addAll(modelServiceResultResources);
        resourcesBeforePage.addAll(compareServiceResultResources);
        resourcesBeforePage.addAll(informationVerificationResultResources);
        resourcesBeforePage.addAll(dataCollisionResultResources);
        if (resourcesBeforePage == null) {
            return null;
        }
        if (resourcesBeforePage.size() == 0) {
            return null;
        }
        List<ResultResource> newList = resourcesBeforePage.stream().sorted(Comparator.comparing(ResultResource::getShareTime).reversed()).collect(Collectors.toList());
        Integer count = newList.size(); // 记录总数
        Integer pageCount = 0; // 页数
        if (count % pageSize == 0) {
            pageCount = count / pageSize;
        } else {
            pageCount = count / pageSize + 1;
        }

        int fromIndex = 0; // 开始索引
        int toIndex = 0; // 结束索引

        if (pageNum != pageCount) {
            fromIndex = (pageNum - 1) * pageSize;
            toIndex = fromIndex + pageSize;
        } else {
            fromIndex = (pageNum - 1) * pageSize;
            toIndex = count;
        }

        List<ResultResource> pageList = newList.subList(fromIndex, toIndex);
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageIndex(pageNum);
        pageInfo.setPageSize(pageSize);
        pageInfo.setPageCount(pageCount);
        pageInfo.setTotalCount(count);
        pageInfo.setDataList(pageList);
        return pageInfo;
    }


    private List<ResultResource> getResultResourceList(ResourceTypeEnum typeEnum,String resourceName,List<String> fromUserIds,Set<String> serviceFuncCodes,String myUserId,List<TSysAuthObjFunc> objFuncs) {
        Map<String,Object> serviceMap = getDataShareStr(typeEnum.getName(), resourceName, fromUserIds, serviceFuncCodes,myUserId);
        String serviceHql = (String) serviceMap.get("hql");
        Map<String,Object> serviceCondition = (Map<String, Object>) serviceMap.get("condition");
        Set<String> funcCodes = (Set<String>) serviceCondition.get("funcCodes");
        List<ResultResource> serviceResultResources = new ArrayList<>();
        if (funcCodes.size() > 0){
            List compareServiceList = this.baseDao.sqlQueryForList(serviceHql, serviceCondition);
            serviceResultResources = setResultLists(compareServiceList, objFuncs,typeEnum.getName());
        }
        return serviceResultResources;
    }

    private Map<String,Object> getMyShareCondStr(String resourceType,String resourceName,String myUserId){
        StringBuilder sbHql = new StringBuilder("from ");
        Map<String,Object> condition = new HashMap<>();
        if (StrUtil.isNotEmpty(resourceType)){
            if (ResourceTypeEnum.DATA_SET.getName().equalsIgnoreCase(resourceType)){
                sbHql.append(" LogicDataObj s where s.operateUserId = :myUserId ");
            }else if (ResourceTypeEnum.DATA_SERVICE.getName().equalsIgnoreCase(resourceType)||
                        ResourceTypeEnum.AI_SERVICE.getName().equalsIgnoreCase(resourceType) ||
                            ResourceTypeEnum.MODEL_SERVICE.getName().equalsIgnoreCase(resourceType) ||
                    ResourceTypeEnum.COMPARE_SERVICE.getName().equalsIgnoreCase(resourceType)||
                    ResourceTypeEnum.INFORMATION_VERFICATION.getName().equalsIgnoreCase(resourceType)||
                    ResourceTypeEnum.DATA_COLLISION.getName().equalsIgnoreCase(resourceType)){
                String serviceType = getServiceType(resourceType);
                sbHql.append(" ServiceMeta s where s.operateUserId = :myUserId and s.servicePublication.serviceType = '"+serviceType+"' ");
            }else if (ResourceTypeEnum.DASHBOARD.getName().equalsIgnoreCase(resourceType)){
                sbHql.append(" Dashboard s where s.operateUserId = :myUserId ");
            }
            condition.put("myUserId",myUserId);
            if (StrUtil.isNotEmpty(resourceName)){
                if (ResourceTypeEnum.INFORMATION_VERFICATION.getName().equalsIgnoreCase(resourceType)||
                        ResourceTypeEnum.DATA_SERVICE.getName().equalsIgnoreCase(resourceType)) {
                    sbHql.append(" and s.servicePublication.name like :resourceName ");
                } else {
                    sbHql.append(" and s.name like :resourceName ");
                }
                condition.put("resourceName","%"+resourceName+"%");
            }
        }
        Map<String,Object> hqlAndCon = new HashMap<>();
        hqlAndCon.put("hql",sbHql.toString());
        hqlAndCon.put("condition",condition);
        return hqlAndCon;
    }

    private String getServiceType(String resourceType){
        String serviceType = "";
        /*if (ResourceTypeEnum.DATA_SERVICE.getName().equalsIgnoreCase(resourceType)){
            serviceType = "4";
        }else if (ResourceTypeEnum.AI_SERVICE.getName().equalsIgnoreCase(resourceType)){
            serviceType = "2";
        }else if (ResourceTypeEnum.MODEL_SERVICE.getName().equalsIgnoreCase(resourceType)){
            serviceType = "1";
        }*/
        EnumServiceType serviceTypeByEnName = getServiceTypeByEnName(resourceType);
        if (serviceType == null) {
            Assert.fail("该服务类型不存在！");
        }

        return serviceTypeByEnName.getCode();
    }

    public static EnumServiceType getServiceTypeByEnName(String enName){

        if(StringUtils.isBlank(enName)){
            return null;
        }
        for (EnumServiceType value : EnumServiceType.values()) {
            if(value.getEnName().equals(enName)){
                return value;
            }
        }
        return null;
    }

    private PageInfo setMyShareResultPageInfo(PageInfo objFuncs,String myUserId){
        List<TSysAuthObjFunc> objFuncsDataList = objFuncs.getDataList();
        List<ResultResource> resultResources = new ArrayList<>();
        //long totalCount = objFuncs.getTotalCount();
        for (TSysAuthObjFunc tSysAuthObjFunc : objFuncsDataList) {
           /* if (tSysAuthObjFunc.gettSysAuthObj().getId().equals(myUserId)){
                totalCount--;
                continue;
            }*/
            TSysFuncBase tSysFuncBase = tSysAuthObjFunc.gettSysFuncBase();
            ResultResource resultResource = new ResultResource();
            resultResource.setShareTime(tSysAuthObjFunc.getCreateTime());
            String funcType = tSysFuncBase.getFuncType();
            resultResource.setResourceType(ResourceTypeEnum.getInstanceByCode(funcType).getName());
            if (ResourceTypeEnum.DATA_SET.getCode().equals(funcType)){
                LogicDataObj logicDataObj = (LogicDataObj) this.baseDao.queryForObject(" from LogicDataObj where id = :id ", this.addParam("id", tSysFuncBase.getFuncCode()).param());
                if (logicDataObj!=null){
                    resultResource.setResourceName(logicDataObj.getName());
                }
            }else if (ResourceTypeEnum.DATA_SERVICE.getCode().equals(funcType) ||
                        ResourceTypeEnum.AI_SERVICE.getCode().equals(funcType) ||
                            ResourceTypeEnum.MODEL_SERVICE.getCode().equals(funcType)||
                    ResourceTypeEnum.COMPARE_SERVICE.getCode().equalsIgnoreCase(funcType)||
                    ResourceTypeEnum.INFORMATION_VERFICATION.getCode().equalsIgnoreCase(funcType)||
                    ResourceTypeEnum.DATA_COLLISION.getCode().equalsIgnoreCase(funcType)){
                ServiceMeta serviceMeta = (ServiceMeta) this.baseDao.queryForObject(" from ServiceMeta where id = :id ", this.addParam("id", tSysFuncBase.getFuncCode()).param());
                if (serviceMeta != null && serviceMeta.getServicePublication() != null) {
                    resultResource.setResourceName(serviceMeta.getServicePublication().getName());
                    resultResource.setServiceId(serviceMeta.getServicePublication().getId());
                }
            }else if (ResourceTypeEnum.DASHBOARD.getCode().equals(tSysFuncBase.getFuncType())){
                Dashboard dashboard = (Dashboard) this.baseDao.queryForObject(" from Dashboard where id = :id ", this.addParam("id", tSysFuncBase.getFuncCode()).param());
                if (dashboard != null) {
                    resultResource.setResourceName(dashboard.getName());
                }
            }
            resultResource.setUsernameByShare(tSysAuthObjFunc.gettSysAuthObj().getObjName());
            resultResource.setUserTypeByShare(tSysAuthObjFunc.gettSysAuthObj().getObjType());
            resultResource.setUserIdByShare(tSysAuthObjFunc.gettSysAuthObj().getId());
            Map<String,String> map = new HashMap<>();
            map.put("id",myUserId);
            TSysAuthUser tSysAuthUser = (TSysAuthUser) sysAuthUserService.query(map);
            resultResource.setCreateUser(tSysAuthUser.getObjName());
            resultResource.setCreateUserId(tSysAuthUser.getId());
            resultResource.setResourceId(tSysFuncBase.getFuncCode());
            resultResource.setId(tSysAuthObjFunc.getId());
            resultResources.add(resultResource);
        }
        objFuncs.setDataList(resultResources);
        //objFuncs.setTotalCount(totalCount);
        return objFuncs;
    }
}
