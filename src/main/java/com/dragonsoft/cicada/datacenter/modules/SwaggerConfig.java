package com.dragonsoft.cicada.datacenter.modules;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

/**
 * <AUTHOR>
 * @date 2020/5/28 19:12
 */
@Configuration
@EnableSwagger2
public class SwaggerConfig {

    @Bean
    public Docket createRestApi(){
        return new Docket(DocumentationType.SWAGGER_2).apiInfo(apiInfo()).groupName("dc")
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.dragonsoft.cicada.datacenter"))
                .paths(PathSelectors.any()).build();
    }

    @Bean
    public Docket docket2(){
        return new Docket(DocumentationType.SWAGGER_2).apiInfo(apiInfo()).groupName("plugin")
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.code"))
                .paths(PathSelectors.any()).build();
    }


    @Bean
    public Docket docket3(){
        return new Docket(DocumentationType.SWAGGER_2).apiInfo(apiInfo()).groupName("dfw")
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.dragoninfo.dfw"))
                .paths(PathSelectors.any()).build();
    }

    private ApiInfo apiInfo(){
        return new ApiInfoBuilder()
                .title("数据中心接口")
                .description("数据中心后端接口")
                .version("1.0")
                .build();
    }
}
