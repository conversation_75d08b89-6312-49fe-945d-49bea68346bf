package com.dragonsoft.cicada.datacenter.modules.system.schedule.impl;

import com.code.common.sch.model.JobMeta;
import com.code.common.sch.model.SchedulePlan;
import com.code.common.sch.model.SimpleJobMeta;
import com.code.common.schedulectr.scheduleclient.schemeconfig.SimpleJobInfo;
import com.code.metadata.etl.trans.TransMeta;
import com.code.metadata.mag.sch.ScheduleModeType;
import com.code.metaservice.etl.trans.TransMetaService;
import com.dragonsoft.cicada.datacenter.modules.system.schedule.MLSQLScheduleService;
import com.fw.dao.hbmimpl.BaseDaoImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service("mlsqlScheduleService")
@Slf4j
public class MLSQLScheduleServiceImpl implements MLSQLScheduleService {

    @Autowired
    private TransMetaService transMetaService;

    @Autowired
    private BaseDaoImpl baseDao;

    @Override
    public String createJob(String transMetaId) {
        return createScheduleMeta(transMetaId);
    }

    @Override
    public void startJob(String transMetaId) {

    }

    @Override
    public void killJob(String transMetaId) {

    }

    @Override
    public void deleteJob(String transMetaId) {

    }

    @Override
    public void updateJob(SimpleJobInfo jobInfo) {

    }

    @Override
    public String executeStatus(String transMetaId) {
        return null;
    }

    @Override
    public String updateTransMeta(String transName, String runMode, String transId, String instanceCode, String handleMode, String classifyId, String dirType, String userId) {
        return null;
    }

    @Override
    public String updateTransMeta(String transName, String runMode, String transId, String instanceCode, String handleMode, String classifyId, String userId) {
        return null;
    }

    private String createScheduleMeta(String transMetaId) {
        TransMeta transMeta = transMetaService.getTransMetaById(transMetaId);
        SimpleJobInfo jobMeta = new SimpleJobInfo();
        jobMeta.setPrioritie(5);
        jobMeta.setBeginTime(this.baseDao.getCurrentTimeString());
        jobMeta.setJobType("etlJob");
        jobMeta.setName(transMeta.getCode());
        jobMeta.setScheduleId(transMeta.getEtlInstanceCode());
        jobMeta.setJobStrategyId(transMeta.getId());
        jobMeta.setSchemeScheduleMode(ScheduleModeType.HAND.getValue());
        return createJobMeta(jobMeta);
    }

    public String createJobMeta(SimpleJobInfo jobMeta) {
        SimpleJobMeta job = new SimpleJobMeta();
        job.setJobName(jobMeta.getName());
        job.setJobType(jobMeta.getJobType());
        job.setScheduleId(jobMeta.getScheduleId());
        job.setJobType(jobMeta.getJobType());
        job.setMemo(jobMeta.getName());
        job.setJobCode(jobMeta.getJobStrategyId());
        job.setActivate(JobMeta.UNACTIVE);
        job.setAlarmMethod(jobMeta.getAlarmMethod());
        job.setAlarmTarget(jobMeta.getAlarmTarget());
        SchedulePlan plan = new SchedulePlan();
        plan.setBeginTime(jobMeta.getBeginTime());
        plan.setEndTime(jobMeta.getEndTime());
        plan.setMemo(jobMeta.getMemo());
        plan.setScheduleWeek(jobMeta.getScheduleWeek());
        plan.setScheduleDay(jobMeta.getScheduleDay());
        plan.setScheduleHour(jobMeta.getScheduleHour());
        plan.setScheduleInterval(jobMeta.getScheduleInterval());
        if(jobMeta.getSchemeScheduleMode().equals(com.code.common.sch.model.ScheduleModeType.CRON.getValue())){
            plan.setScheduleMinute(jobMeta.getCronExpression());
        }else{
            plan.setScheduleMinute(jobMeta.getScheduleMinute());
        }
        plan.setSchMode(jobMeta.getSchemeScheduleMode());
        plan.setScheduleMonth(jobMeta.getScheduleMonth());
        job.addSchedulePlan(plan);
        return (String) this.baseDao.save(job);
    }

}
