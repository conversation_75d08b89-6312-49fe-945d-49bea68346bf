package com.dragonsoft.cicada.datacenter.modules.datavisual;//package com.dragonsoft.cicada.datavisual;

import com.dragonsoft.cicada.datacenter.modules.datavisual.model.annotation.WidgetLabel;
import com.dragonsoft.cicada.datacenter.modules.datavisual.widgetmeta.IWidgetMetaBuild;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 控件元信息监听扫描器
 *
 * <AUTHOR>
 */
@Component
public class WidgetListening implements ApplicationListener<ContextRefreshedEvent> {

    @Autowired
    IWidgetMetaBuild widgetMetaBuild;

    /**
     * 获取所有控件bean信息并更新数据库控件元信息
     *
     * @param event
     */
    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        ApplicationContext applicationContext = event.getApplicationContext();
        List<String> ss = Arrays.asList(applicationContext.getBeanDefinitionNames());
        if (event.getApplicationContext().getParent() == null) {
            Map<String, Object> beans = event.getApplicationContext().getBeansWithAnnotation(WidgetLabel.class);
            //更新控件元所有.
//            widgetMetaBuild.updateWidgetMetaAll(beans);
        }

    }


}
