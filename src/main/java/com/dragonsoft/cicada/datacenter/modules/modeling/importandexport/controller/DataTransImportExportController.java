package com.dragonsoft.cicada.datacenter.modules.modeling.importandexport.controller;

import com.alibaba.fastjson.JSON;
import com.code.common.paging.PageInfo;
import com.code.common.utils.DateUtils;
import com.code.common.utils.R;
import com.code.common.utils.StringUtils;
import com.code.common.utils.assertion.Assert;
import com.code.common.utils.io.SerializableMsgCodec;
import com.code.dragonsoft.dataquery.service.QueryDataService;
import com.code.metadata.business.directory.BaseBusiClassify;
import com.code.metadata.business.directory.BaseBusiDir;
import com.code.metadata.datavisual.Dashboard;
import com.code.metadata.etl.trans.TransMeta;
import com.code.metadata.portal.MenuConfig;
import com.code.metadata.portal.Portal;
import com.code.metadata.portal.PortalConfig;
import com.code.metadata.portal.PublishUrl;
import com.code.metadata.res.ddl.LogicDataColumn;
import com.code.metadata.res.ddl.LogicDataObj;
import com.code.metadata.sm.ServiceMeta;
import com.code.metadata.sm.ServicePublication;
import com.code.metadata.udf.management.UdfGraph;
import com.code.metadata.usecase.UseCase;
import com.code.metadata.variable.TransVariable;
import com.code.metadata.variable.TransVariableRelation;
import com.code.metaservice.datawarehouse.IDataWareTreeService;
import com.code.metaservice.datawarehouse.model.DataSourceVO;
import com.code.metaservice.datawarehouse.model.DatasetTableModel;
import com.code.metaservice.datawarehouse.model.DatasetTreeModel;
import com.code.metaservice.ddl.IDataSetStepMetaService;
import com.code.metaservice.ddl.vo.LogicDataObjInfo;
import com.code.metaservice.portal.IMenuConfigService;
import com.code.metaservice.sm.IServicePublicationService;
import com.code.metaservice.usecase.UseCaseService;
import com.dragoninfo.dfw.bean.Result;
import com.dragoninfo.dfw.entity.TSysAuthObjFunc;
import com.dragoninfo.dfw.entity.TSysAuthRole;
import com.dragoninfo.dfw.entity.TSysAuthUser;
import com.dragoninfo.dfw.entity.TSysFuncBase;
import com.dragoninfo.dfw.service.SysAuthObjFuncService;
import com.dragoninfo.dfw.service.SysAuthUserService;
import com.dragoninfo.dfw.service.ValidateAndLogService;
import com.dragonsoft.cicada.datacenter.common.token.GlobalConstant;
import com.dragonsoft.cicada.datacenter.modules.common.service.CommonBusiClassifyService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.apitest.service.UseCaseManageService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.service.IDataSetEditService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.service.IDataSetOperationService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.externaldatasources.service.IDataSetAuthService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.service.IServiceManagementService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.service.IServicePublicService;
import com.dragonsoft.cicada.datacenter.modules.metadata.service.DataBaseConnectService;
import com.dragonsoft.cicada.datacenter.modules.metadata.service.DataBaseFactory;
import com.dragonsoft.cicada.datacenter.modules.metadata.service.MetadataService;
import com.dragonsoft.cicada.datacenter.modules.modeling.importandexport.enums.*;
import com.dragonsoft.cicada.datacenter.modules.modeling.importandexport.service.IDataTransImportExportService;
import com.dragonsoft.cicada.datacenter.modules.modeling.importandexport.service.impl.DataTransImportExportServiceImpl;
import com.dragonsoft.cicada.datacenter.modules.modeling.importandexport.util.DataExportUtil;
import com.dragonsoft.cicada.datacenter.modules.modeling.importandexport.vo.*;
import com.dragonsoft.cicada.datacenter.modules.modeling.service.DataModelingService;
import com.dragonsoft.cicada.datacenter.modules.modeling.service.TransTemplateService;
import com.dragonsoft.cicada.datacenter.modules.modeling.vo.DataExportVo;
import com.dragonsoft.cicada.datacenter.modules.modeling.vo.DataSourceStatusVO;
import com.dragonsoft.cicada.datacenter.modules.modeling.vo.DeleteClassifyVo;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.service.IRoleService;
import com.fw.dao.hbmimpl.BaseDaoImpl;
import com.fw.service.BaseService;
import com.fw.tenon.tree.Tree;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.JSQLParserException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.StreamUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.File;
import java.io.IOException;
import java.io.Serializable;
import java.lang.reflect.InvocationTargetException;
import java.net.URLEncoder;
import java.text.Collator;
import java.util.*;
import java.util.stream.Collectors;

import static com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.util.DataSetUtils.defaultPageSize;
import static com.dragonsoft.cicada.datacenter.modules.modeling.importandexport.util.DataExportUtil.*;
import static com.dragonsoft.cicada.datacenter.modules.modeling.importandexport.vo.ImportCompleteResultVo.*;

/**
 * <AUTHOR>
 * @Date: 2022/07/12/上午9:29
 * <p>
 * 数据中心数据源，数据集，方案导入导出
 */

@CrossOrigin
@RestController
@RequestMapping("/dataTransImportExport")
@Slf4j
@Api(value = "DataTransImportExportController|数据中心导出")
public class DataTransImportExportController {

    private final String DefaultVersion = "standard";

    private DataCenterTransExportBean dataCenterTransExportBean;

    private static List<String> filterTransIds = Lists.newArrayList();

    private static List<String> filterServiceIds = Lists.newArrayList();

    private static List<String> filterCaseIds = Lists.newArrayList();

    private static List<String> filterLogicIds = Lists.newArrayList();

    private static List<String> filterRdbIds = Lists.newArrayList();

    private static List<String> filterDwIds = Lists.newArrayList();



    @Autowired
    QueryDataService queryDataService;


    @Autowired
    private IServicePublicationService servicePublicationService;
    @Value("${ai.dir.basicPath}")
    private String dirBasicPath;
    @Autowired
    private IServiceManagementService serviceManagementService;

    @Autowired
    private IDataTransImportExportService dataTransImportExportService;

    @Autowired
    private SysAuthUserService sysAuthUserService;

    @Autowired
    private IDataWareTreeService dataWareTreeService;

    @Autowired
    private IRoleService roleService;


    @Autowired
    private SysAuthObjFuncService sysAuthObjFuncService;

    @Autowired
    private IDataSetAuthService dataSetAuthService;

    @Autowired
    private ValidateAndLogService validateAndLogService;


    @Autowired
    private IDataSetOperationService dataSetOperationService;

    @Autowired
    private DataModelingService dataModelingService;

    @Value("${catalogResources.isDelete}")
    private String isDelete;


    @Autowired
    private IServicePublicService servicePublicService;

    //之前已经存进库里面的数据，不需要由用户再次手动修改对应的数据源信息
    private static List<DataSourceVO> dataSourceStatusVOS = Lists.newArrayList();

    //之前未入库的，需要查询出来由用户自己手动修改的数据源
    private static List<Map<String, String>> rdbSchemaMaps = Lists.newArrayList();

    private static String oldUserId = "";


    @Autowired
    IDataSetStepMetaService dataSetStepMetaService;

    /**
     * 给创屏使用的导出接口
     * 根据目录驱动  导出目录下的  方案 数据源 数据集
     *
     * @param response        将文件流写入response
     * @param transDirId      方案目录Id
     * @param datasourceDirId 数据源目录id
     * @param datasetDirId    数据集目录id
     * @param userId          用户id
     * @return
     * @throws IOException
     */

    @RequestMapping("/exportDataTrans")
    public Result exportDataTrans(HttpServletResponse response, String transDirId, String datasourceDirId, String datasetDirId, String userId) throws IOException {
        String NormalExport = "true";

        //校验导出
        try {
            dataTransImportExportService.checkExportIsOk(transDirId, datasourceDirId, datasetDirId, userId);

            DataTransExportBean resultBean = new DataTransExportBean();

            //导出方案
            //通过目录找到方案集合
            List<String> transMetaList = dataTransImportExportService.getTransMetaListByClassifyId(transDirId, userId);
            //目录下没有方案就没有必要导出方案了
            if (transMetaList.size() > 0) {
                resultBean = dataTransImportExportService.exportModel(transMetaList, null, null, null, null);
            }
            //导出方案目录树
            List<BaseBusiClassify> transClassifys = dataTransImportExportService.findFatherAndSonClassify(transDirId);

            //------------------
            //导出数据源
            List<DataSourceBean> dataSourceBeans = dataTransImportExportService.buildDataSourceBean(datasourceDirId, userId);
            //导出数据源目录树
            List<BaseBusiClassify> datasourceClassifys = dataTransImportExportService.findFatherAndSonClassify(datasourceDirId);
            resultBean.setDataSourceBeans(dataSourceBeans);

            //导出数据集
            LogicObjBean logicObjBean = dataTransImportExportService.exportCustomDataset(datasetDirId, userId);
            List<BaseBusiClassify> datasetClassifys = dataTransImportExportService.findFatherAndSonClassify(datasetDirId);
            resultBean.setLogicObjBeans(logicObjBean);

            resultBean.setTransDirs(transClassifys);
            resultBean.setDatasourceDirs(datasourceClassifys);
            resultBean.setDatasetDirs(datasetClassifys);

            resultBean.setDatasetDirId(datasetDirId);
            resultBean.setDatasourceDirId(datasourceDirId);
            resultBean.setTransDirId(transDirId);
            resultBean.setUserId(userId);
            resultBean.setTransRootDirId(dataTransImportExportService.getBaseBusiDirByClassify(transDirId));
            resultBean.setDatasetRootDirId(dataTransImportExportService.getBaseBusiDirByClassify(datasetDirId));
            resultBean.setDatasourceRootDirId(dataTransImportExportService.getBaseBusiDirByClassify(datasourceDirId));


            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("dataTrans.model", "UTF-8"));
            //NormalExport 标识导出是否成功
            response.addHeader("NormalExport", NormalExport);
            StreamUtils.copy(SerializableMsgCodec.encode(resultBean), response.getOutputStream());
        } catch (NullPointerException e) {
            NormalExport = "false";
            Assert.fail("空指针异常！");
        } catch (Exception e) {
            NormalExport = "false";
            Assert.fail(e.getMessage());
        } finally {
            response.addHeader("NormalExport", NormalExport);
//            response.getOutputStream().close();
        }
        return Result.success();

    }

    /**
     * 给数据中心使用的导出接口
     * 根据目录驱动  导出目录下的  方案 数据源 数据集
     *
     * @param response 将文件流写入response
     * @return
     * @throws IOException
     */

    @RequestMapping("/exportDataCenterTrans")
    @ApiOperation(value = "数据中心模型方案导出")
    public Result exportDataCenterTrans(HttpServletResponse response,HttpServletRequest request, @RequestBody ExportModelVo exportModelVo) throws IOException {
        String NormalExport = "true";
        String userId = exportModelVo.getUserId();
        //校验导出
        try {
            DataCenterTransExportBean resultBean = new DataCenterTransExportBean();

            //导出方案
            //目录下没有方案就没有必要导出方案了
            //通过方案Id，或者目录id找到方案集合
            List<String> allTransIds = getAllTransIds(exportModelVo);
            Assert.notNull(allTransIds, "找不到想要导出方案，请确认想要导出得方案存在！");
            String exportContentType = exportModelVo.getExportContentType();
            ExportContentEnum instanceByCode = ExportContentEnum.getInstanceByCode(exportContentType);
            switch (instanceByCode) {
                case MODEL:
                    //导出方案
                    resultBean = exportOnlyModel(allTransIds);
                    break;
                case MODEL_RESOURCE:
                    resultBean = getExportResultBean(allTransIds, exportModelVo.getUserId(), new ArrayList<>());
                    break;
                case MODEL_RESOURCE_API:
                    resultBean = getExportResultBean(allTransIds, exportModelVo.getUserId(), new ArrayList<>());
                    if (CollectionUtils.isNotEmpty(allTransIds)) {
                        List<String> serviceIds = dataTransImportExportService.queryServiceIdsByTransIds(allTransIds);
                        resultBean = getExportResultBeanApi(resultBean, serviceIds, userId);
                    }
                    break;
                default:
                    resultBean = getExportResultBean(allTransIds, exportModelVo.getUserId(), new ArrayList<>());
                    break;
            }
            resultBean.setUserId(userId);
           /*
           导入暂时没看到使用
            resultBean.setDatasetDirId(datasetDirId);
            resultBean.setDatasourceDirId(datasourceDirId);
            resultBean.setTransDirId(transDirId);
            resultBean.setUserId(userId);
            resultBean.setTransRootDirId(dataTransImportExportService.getBaseBusiDirByClassify(transDirId));
            resultBean.setDatasetRootDirId(dataTransImportExportService.getBaseBusiDirByClassify(datasetDirId));
            resultBean.setDatasourceRootDirId(dataTransImportExportService.getBaseBusiDirByClassify(datasourceDirId));*/


            dataTransImportExportService.saveDataImportExportLog(request,exportModelVo);

            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("dataTrans.model", "UTF-8"));
            //NormalExport 标识导出是否成功
            response.addHeader("NormalExport", NormalExport);
            response.addHeader("fileName", URLEncoder.encode("dataTrans.model", "UTF-8"));
            StreamUtils.copy(SerializableMsgCodec.encode(resultBean), response.getOutputStream());
        } catch (NullPointerException e) {
            NormalExport = "false";
            log.error("空指针异常！--->"+e.getMessage(),e);
            Assert.fail("空指针异常！");

        } catch (Exception e) {
            log.error(e.getMessage(),e);
            NormalExport = "false";
            Assert.fail(e.getMessage());
        } finally {
            response.addHeader("NormalExport", NormalExport);
            response.getOutputStream().close();
        }
        return Result.success();

    }


    /**
     * 给数据中心使用的导出接口
     * 根据目录驱动  导出目录下的  方案 数据源 数据集
     *
     * @param response 将文件流写入response
     * @return
     * @throws IOException
     */

    @RequestMapping("/exportDataCenterServiceApi")
    @ApiOperation(value = "数据中心服务Api导出")
    public Result exportDataCenterServiceApi(HttpServletResponse response, @RequestBody ExportApiVo exportApiVo) throws IOException {
        String NormalExport = "true";
        //校验导出
        try {
            DataCenterTransExportBean resultBean = new DataCenterTransExportBean();

            String exportResourceType = exportApiVo.getExportResourceType();
            ExportResourceApiEnum instanceByCode = ExportResourceApiEnum.getInstanceByCode(exportResourceType);
            switch (instanceByCode) {
                case USE_CASE:
                    resultBean = getExportUseCase(exportApiVo);
                    break;
                case API:
                    resultBean = getExportServiceApi(exportApiVo);//api类型导出
                    break;
            }
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("dataTrans.model", "UTF-8"));
            //NormalExport 标识导出是否成功
            response.addHeader("NormalExport", NormalExport);
            response.addHeader("fileName", URLEncoder.encode("dataServiceApis.model", "UTF-8"));
            StreamUtils.copy(SerializableMsgCodec.encode(resultBean), response.getOutputStream());
        } catch (NullPointerException e) {
            NormalExport = "false";
            log.error("空指针异常！--->"+e.getMessage(),e);
            Assert.fail("空指针异常！");

        } catch (Exception e) {
            log.error(e.getMessage(),e);
            NormalExport = "false";
            Assert.fail(e.getMessage());
        } finally {
            response.addHeader("NormalExport", NormalExport);
            response.getOutputStream().close();
        }
        return Result.success();

    }

    private DataCenterTransExportBean getExportServiceApi(ExportApiVo exportApiVo) {
        List<String> serviceIds = getAllServiceIds(exportApiVo);
        String userId = exportApiVo.getUserId();

        DataCenterTransExportBean resultBean = new DataCenterTransExportBean();
        //通过服务id导出方案，数据集

        List<Map> resources = dataTransImportExportService.queryResourceIdsServiceIds(serviceIds,exportApiVo.getUserId());
        List<String> resourceLogicIds = resources.stream().filter(f -> (("LogicDataObj").equals(f.get("type")))).map(m -> (String) m.get("id")).collect(Collectors.toList());
        List<String> allTransIds = resources.stream().filter(f -> (("TransMeta").equals(f.get("type")))).map(m -> (String) m.get("id")).collect(Collectors.toList());

        Assert.notNull(allTransIds, "找不到想要导出方案，请确认想要导出得方案存在！");
        String exportContentType = exportApiVo.getExportContentType();
        ExportContentApiEnum instanceByCode = ExportContentApiEnum.getInstanceByCode(exportContentType);
        DataTransImportExportServiceImpl.TransDatasetAndDatasource dataSourceAndLogic = new DataTransImportExportServiceImpl.TransDatasetAndDatasource();

        switch (instanceByCode) {
            case API:
                //仅导出api
                resultBean = getExportResultBeanApi(resultBean, serviceIds, userId);
                break;
            case API_RESOURCE:
                resultBean = getExportResultBean(allTransIds, userId, resourceLogicIds);
                exportRelyResourceByResourceLogic(dataSourceAndLogic, resultBean, userId, resourceLogicIds);

                resultBean = getExportResultBeanApi(resultBean, serviceIds, userId);
                break;
            case API_RESOURCE_CASE:
                resultBean = getExportResultBean(allTransIds, userId, resourceLogicIds);
                exportRelyResourceByResourceLogic(dataSourceAndLogic, resultBean, userId, resourceLogicIds);
                resultBean = getExportResultBeanApi(resultBean, serviceIds, userId);
                resultBean = getExportResultBeanUseCase(resultBean, serviceIds, userId);
                break;
            default:
                resultBean = getExportResultBean(allTransIds, userId, resourceLogicIds);
                break;
        }
        resultBean.setUserId(userId);
        return resultBean;
    }


    private DataCenterTransExportBean getExportUseCase(ExportApiVo exportApiVo) {
        DataCenterTransExportBean resultBean = new DataCenterTransExportBean();
        String userId = exportApiVo.getUserId();

        List<String> caseIds = getAllCaseIds(exportApiVo);

        List<String> serviceIds = dataTransImportExportService.queryServiceIdsByCaseIds(caseIds);
        //通过服务id导出方案，数据集
        List<Map> resources = dataTransImportExportService.queryResourceIdsServiceIds(serviceIds,exportApiVo.getUserId());
        List<String> resourceLogicIds = getResourceIds("LogicDataObj", resources);
        List<String> allTransIds = getResourceIds("TransMeta", resources);

        String exportContentType = exportApiVo.getExportContentType();
        ExportContentCaseEnum instanceByCode = ExportContentCaseEnum.getInstanceByCode(exportContentType);
        DataTransImportExportServiceImpl.TransDatasetAndDatasource dataSourceAndLogic = new DataTransImportExportServiceImpl.TransDatasetAndDatasource();

        switch (instanceByCode) {
            case CASE:
                //仅导出用例
                resultBean = getExportResultBeanUseCase(resultBean, serviceIds, userId);
                break;
            case API_CASE:
                resultBean = getExportResultBean(allTransIds, userId, resourceLogicIds);
                exportRelyResourceByResourceLogic(dataSourceAndLogic, resultBean, userId, resourceLogicIds);
                resultBean = getExportResultBeanApi(resultBean, serviceIds, userId);
                resultBean = getExportResultBeanUseCase(resultBean, serviceIds, userId);
                break;
        }
        resultBean.setUserId(userId);
        return resultBean;
    }


    private List<String> getResourceIds(String type, List<Map> resources) {
        return resources.stream().filter(f -> ((type).equals(f.get("type")))).map(m -> (String) m.get("id")).collect(Collectors.toList());
    }

    private DataCenterTransExportBean getExportResultBean(List<String> allTransIds, String userId, List<String> resourceLogicIds) {
        //导出方案
        DataCenterTransExportBean resultBean = new DataCenterTransExportBean();
        if (CollectionUtils.isNotEmpty(allTransIds)) {
            resultBean = exportOnlyModel(allTransIds);
            //导出方案依赖得资源
            exportRelyResource(resultBean, allTransIds, userId, resourceLogicIds);
        }
        return resultBean;
    }


    //导出服务资源
    private DataCenterTransExportBean getExportResultBeanApi(DataCenterTransExportBean dataCenterTransExportBean, List<String> serviceIdList, String userId) {
        if (CollectionUtils.isEmpty(serviceIdList)) return dataCenterTransExportBean;
        List<DataServiceApi> dataServiceApis = Lists.newArrayList();
        for (String serviceId : serviceIdList) {
            List<String> serviceIds = Lists.newArrayList(serviceId);
            //t_md_service_publication
            List<Map<String, Object>> dataServicePublications = dataTransImportExportService.queryServiceApiByServiceIds(serviceIds, "t_md_service_publication", "id");
            if(CollectionUtils.isEmpty(dataServicePublications)) continue;
            //t_md_service_meta
            List<Map<String, Object>> dataServiceMetas = dataTransImportExportService.queryServiceApiByServiceIds(serviceIds, "t_md_service_meta", "service_id");

            //t_md_service_params
            List<Map<String, Object>> dataServiceParams = dataTransImportExportService.queryServiceApiByServiceIds(serviceIds, "t_md_service_params", "owner_id");

            //t_md_resource_relation
            List<Map<String, Object>> dataResourceRelations = dataTransImportExportService.queryServiceApiByServiceIds(serviceIds, "t_md_resource_relation", "service_id");

            //t_md_resource_config
            List<Map<String, Object>> dataResourceConfigs = dataTransImportExportService.queryServiceApiByServiceIds(serviceIds, "t_md_resource_config", "service_id");

            //t_md_many_resource_config
            List<Map<String, Object>> dataManyResourceConfigs = dataTransImportExportService.queryServiceApiByServiceIds(serviceIds, "t_md_many_resource_config", "service_id");

            //t_resource_column_relation
            List<Map<String, Object>> dataResourceColumnRelation = dataTransImportExportService.queryServiceApiByServiceIds(serviceIds, "t_resource_column_relation", "service_id");


            DataServiceApi dataServiceApi = new DataServiceApi();
            dataServiceApi.setDataServicePublications(dataServicePublications);
            dataServiceApi.setDataServiceMetas(dataServiceMetas);
            dataServiceApi.setDataServiceParams(dataServiceParams);
            dataServiceApi.setDataResourceRelations(dataResourceRelations);
            dataServiceApi.setDataResourceConfigs(dataResourceConfigs);
            dataServiceApi.setDataManyResourceConfigs(dataManyResourceConfigs);
            dataServiceApi.setDataResourceColumnRelation(dataResourceColumnRelation);
            dataServiceApis.add(dataServiceApi);
        }
        //导出我的空间服务目录树
        List<BaseBusiClassify> serviceBaseClassify = dataTransImportExportService.queryServiceClassifyIdByServiceIds(serviceIdList);
        List<ClassifyElement> serviceClassifyElements = Lists.newArrayList();//服务与目录资源关系

        for (String elementId : serviceIdList) {
            ClassifyElement classifyElement = dataTransImportExportService.getClassifyElement(elementId);
            serviceClassifyElements.add(classifyElement);
        }
        dataCenterTransExportBean.setDataServiceApis(dataServiceApis);
        dataCenterTransExportBean.setServiceIds(serviceIdList);
        dataCenterTransExportBean.setDataServiceApiDirs(serviceBaseClassify);
        dataCenterTransExportBean.setApiClassifyElements(serviceClassifyElements);
        return dataCenterTransExportBean;

    }

    //导出用例资源
    private DataCenterTransExportBean getExportResultBeanUseCase(DataCenterTransExportBean dataCenterTransExportBean, List<String> serviceIdList, String userId) {
        List<DataUseCaseBean> dataUseCaseBeans = Lists.newArrayList();
        List<String> useCaseIdList = Lists.newArrayList();
        for (String serviceId : serviceIdList) {
            List<String> serviceIds = Lists.newArrayList(serviceId);
            //T_MD_USE_CASE_API
            List<Map<String, Object>> dataUseCaseApis = dataTransImportExportService.queryServiceApiByServiceIds(serviceIds, "T_MD_USE_CASE_API", "api_id");

            List<String> useCaseApiIds = dataUseCaseApis.stream().map(s -> (String) s.get("id")).collect(Collectors.toList());
            List<String> useCaseIds = dataUseCaseApis.stream().map(s -> (String) s.get("use_case_id")).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(useCaseApiIds) || CollectionUtils.isEmpty(useCaseIds)) continue;
            List<Map<String, Object>> dataUseCases = dataTransImportExportService.queryServiceApiByServiceIds(useCaseIds, "T_MD_USE_CASE", "id");

            List<Map<String, Object>> dataUseCaseApiInfos = dataTransImportExportService.queryServiceApiByServiceIds(useCaseApiIds, "T_MD_USE_CASE_API_INFO", "use_case_api_id");


            DataUseCaseBean dataUseCaseBean = new DataUseCaseBean();
            dataUseCaseBean.setDataUseCases(dataUseCases);
            dataUseCaseBean.setDataUseCaseApis(dataUseCaseApis);
            dataUseCaseBean.setDataUseCaseParamInfos(dataUseCaseApiInfos);

            dataUseCaseBeans.add(dataUseCaseBean);
            useCaseIdList.addAll(useCaseIds);
        }
        //导出我的空间服务目录树
        List<BaseBusiClassify> useCaseClassify = dataTransImportExportService.queryServiceClassifyIdByServiceIds(useCaseIdList);
        List<ClassifyElement> useCaseClassifyElements = Lists.newArrayList();//服务与目录资源关系

        for (String elementId : useCaseIdList) {
            ClassifyElement classifyElement = dataTransImportExportService.getClassifyElement(elementId);
            useCaseClassifyElements.add(classifyElement);
        }
        dataCenterTransExportBean.setDataUseCaseBeans(dataUseCaseBeans);
        dataCenterTransExportBean.setUseCaseIds(useCaseIdList);
        dataCenterTransExportBean.setDataUseCaseDirs(useCaseClassify);
        dataCenterTransExportBean.setUseCaseClassifyElements(useCaseClassifyElements);
        return dataCenterTransExportBean;

    }


    /**
     * 数据中心导出获取方案id集合
     *
     * @param exportModelVo 导出vo
     * @return 方案id集合
     */
    private List<String> getAllTransIds(ExportModelVo exportModelVo) {
        Set<String> transMetaList = new HashSet<>();
        if (ExportScopeEnum.SPECIFIED_MODEL.code.equals(exportModelVo.getExportScopeType())) {
            transMetaList.addAll(exportModelVo.getSpecifiedModelTransIdList());
        } else {
            for (String classifyId : exportModelVo.getSpecifiedTransClassifyIdList()) {
                List<String> transIds = dataTransImportExportService.getTransMetaListByClassifyId(classifyId, exportModelVo.getUserId());
                transMetaList.addAll(transIds);
            }
        }
        return new ArrayList<>(transMetaList);
    }

    /**
     * 数据中心导出获取方案id集合
     *
     * @param exportApiVo 导出vo
     * @return 方案id集合
     */
    private List<String> getAllServiceIds(ExportApiVo exportApiVo) {
        Set<String> serviceIds = new HashSet<>();
        if (ExportScopeApiEnum.SPECIFIED_RESOURCE.code.equals(exportApiVo.getExportScopeType())) {
            serviceIds.addAll(exportApiVo.getSpecifiedApiServiceIdList());
        } else {
            for (String classifyId : exportApiVo.getSpecifiedApiClassifyIdList()) {
                // 通过目录获取到所有的服务id
                List<String> allClassifyIds = serviceManagementService.queryBusiClassifyIdList(classifyId);
                //通过目录获取测试用例id集合
                List<String> elementIdsByClassifyIds = commonBusiClassifyService.getElementIdsByClassifyIds(allClassifyIds);
                serviceIds.addAll(elementIdsByClassifyIds);
            }
        }
        return new ArrayList<>(serviceIds);
    }

    private List<String> getAllCaseIds(ExportApiVo exportApiVo) {
        Set<String> caseIds = new HashSet<>();
        if (ExportScopeApiEnum.SPECIFIED_RESOURCE.code.equals(exportApiVo.getExportScopeType())) {
            caseIds.addAll(exportApiVo.getSpecifiedApiServiceIdList());
        } else {
            for (String classifyId : exportApiVo.getSpecifiedApiClassifyIdList()) {
                // 通过目录获取到所有的服务id
                List<String> allClassifyIds = serviceManagementService.queryBusiClassifyIdList(classifyId);
                //通过目录获取测试用例id集合
                List<String> elementIdsByClassifyIds = commonBusiClassifyService.getElementIdsByClassifyIds(allClassifyIds);
                caseIds.addAll(elementIdsByClassifyIds);
            }
        }
        return new ArrayList<>(caseIds);
    }

    /**
     * 数据中心导出获取方案id集合
     *
     * @param transIds 导出vo
     * @return 方案id集合
     */
    private List<String> getAllServiceIdsByTransIds(List<String> transIds) {
        List<String> serviceIds = Lists.newArrayList();
        List<String> strings = dataTransImportExportService.queryServiceIdsByTransIds(transIds);
        serviceIds.addAll(strings);
        return serviceIds;
    }

    //仅导出方案
    private DataCenterTransExportBean exportOnlyModel(List<String> allTransIds) {
        DataCenterTransExportBean dataCenterTransExportBean = new DataCenterTransExportBean();
        if (allTransIds.size() > 0) {
            //导出方案信息
            dataCenterTransExportBean = dataTransImportExportService.exportModel(allTransIds, null, null, null, null);
            //导出方案使用到的参数表
            List<String> exportTransIds = dataCenterTransExportBean.getExportTransIds();
            List<Map<String, Object>> transVariableS = dataTransImportExportService.queryTransVariable(exportTransIds);
            List<String> variableIds = transVariableS.stream().map(s -> (String)s.get("id")).collect(Collectors.toList());
            List<TransVariable> transVariables = Lists.newArrayList();
            List<TransVariableRelation> transVariableRelations = Lists.newArrayList();
            for(String variableId : variableIds ){
                TransVariable transVariable = dataTransImportExportService.get(TransVariable.class, variableId);
                transVariables.add(transVariable);
            }
            dataCenterTransExportBean.setTransVariables(transVariables);
            List<Map<String, Object>> transVariableRelationS = dataTransImportExportService.queryTransVariableRelation(exportTransIds);
            List<String> variableRelationIds = transVariableRelationS.stream().map(s -> (String)s.get("id")).collect(Collectors.toList());

            for(String variableRelationId : variableRelationIds ){
                TransVariableRelation transVariableRelation = dataTransImportExportService.get(TransVariableRelation.class, variableRelationId);
                transVariableRelations.add(transVariableRelation);
            }
            dataCenterTransExportBean.setTransVariableRelations(transVariableRelations);
        }
        //导出方案目录树
        List<BaseBusiClassify> transClassifyList = dataTransImportExportService.queryTransClassifyIdByTransId(allTransIds);
        dataCenterTransExportBean.setTransDirs(transClassifyList);
        return dataCenterTransExportBean;
    }

    //导出方案，数据集依赖资源
    private void exportRelyResource(DataCenterTransExportBean dataCenterTransExportBean, List<String> allTransIds, String userId, List<String> resourceLogicIds) {
        //通过方案id查出所有得数据源以及数据集id
        DataTransImportExportServiceImpl.TransDatasetAndDatasource dataSourceAndLogic = dataTransImportExportService.getDataSourceAndLogic(allTransIds, userId);
        //如果是服务资源的，可能会有数据集的服务
        exportRelyResourceByResourceLogic(dataSourceAndLogic, dataCenterTransExportBean, userId, resourceLogicIds);

    }

    //导出数据集依赖的数据空间资源
    private void exportRelyResourceByResourceLogic(DataTransImportExportServiceImpl.TransDatasetAndDatasource dataSourceAndLogic, DataCenterTransExportBean dataCenterTransExportBean, String userId, List<String> resourceLogicIds) {
        if (CollectionUtils.isNotEmpty(resourceLogicIds)) {
            //服务资源管关联的数据源id
            Set<String> dwInstanceIds = dataTransImportExportService.queryDwInstanceIds(resourceLogicIds);
            dataSourceAndLogic.addDwInstances(dwInstanceIds);
            dataSourceAndLogic.addLogicObjs(new HashSet<>(resourceLogicIds));
        }
        exportRelyResourceByLogic(dataCenterTransExportBean, userId, dataSourceAndLogic);
    }

    private void exportRelyResourceByLogic(DataCenterTransExportBean dataCenterTransExportBean, String userId, DataTransImportExportServiceImpl.TransDatasetAndDatasource dataSourceAndLogic) {
        List<String> dwInstanceList = new ArrayList();
        List<String> logicIds = new ArrayList();

        Set<String> dataSourceIds = new HashSet<>();
        for(DataSourceBean dataSourceBean : dataCenterTransExportBean.getDataSourceBeans()){
            dataSourceIds.add(dataSourceBean.getDatasourceId());
        }
        List<String> logicIds1 = dataCenterTransExportBean.getLogicIds();
        //数据源id集合
        for(String dwId : dataSourceAndLogic.getDwInstances()){
            if(!dataSourceIds.contains(dwId)){
                dwInstanceList.add(dwId);
            }
        }
        for(String logicId : dataSourceAndLogic.getLogicObjs()){
            if(!logicIds1.contains(logicId)){
                logicIds.add(logicId);
            }
        }
        //获取自助数据集中使用得数据表挂在数据仓库下得数据集
        logicIds.addAll(dataTransImportExportService.queryLogicBySelf(logicIds));

        //导出数据源
        List<DataSourceBean> dataSourceBeans = dataTransImportExportService.buildDataSourceBeanByInstanceIds(dwInstanceList, userId);

        //导出我的空间数据源目录树
        List<BaseBusiClassify> myDataSourceClassifyList = dataTransImportExportService.queryMyHouseClassifyIdByDwInstancesId(dwInstanceList, userId);

        //导出数据仓库据源目录树
        List<BaseBusiClassify> dataSourceHouseClassifyList = dataTransImportExportService.queryDataWarehouseClassifyIdByDwInstancesId(dwInstanceList, userId);


        //导出数据集
        LogicObjBean logicObjBean = dataTransImportExportService.exportLogicBeanById(logicIds);

        //数据集目录树
        List<BaseBusiClassify> dataSetLogicClassifyList = dataTransImportExportService.queryLogicClassifyIdByLogicId(logicIds, userId);

        //获取rdb表结构
        List<Map> rdbIdMap = dataTransImportExportService.getRdbIdsByLogicIds(logicIds);
        Set<String> rdbSets = new HashSet<>();
        Map<String, String> logicAndRdb = new HashMap<>();
        for (Map<String, String> map : rdbIdMap) {
            rdbSets.add(map.get("id"));
            logicAndRdb.put(map.get("logicid"), map.get("id"));

        }
        DataCenterRdbBean dataCenterRdbBean = dataTransImportExportService.getRdbBeanByRdbIds(new ArrayList<>(rdbSets), userId);


        dataCenterTransExportBean.getLogicIds().addAll(logicIds);
        dataCenterTransExportBean.getDwInstanceIds().addAll(dwInstanceList);
        dataCenterTransExportBean.getLogicObjBeansList().add(logicObjBean);
        dataCenterTransExportBean.getDataSourceBeans().addAll(dataSourceBeans);

        dataCenterTransExportBean.getDatasourceDirs().addAll(myDataSourceClassifyList);
        //数据仓库目录
        dataCenterTransExportBean.getDataWarehouseDirs().addAll(dataSourceHouseClassifyList);
        dataCenterTransExportBean.getDatasetDirs().addAll(dataSetLogicClassifyList);

        dataCenterTransExportBean.setTransAndLogicMap(dataSourceAndLogic.getTransAndLogicMap());
        dataCenterTransExportBean.setTransAndDwInstanceMap(dataSourceAndLogic.getTransAndDwInstanceMap());
        dataCenterTransExportBean.setLogicIdAndRdb(logicAndRdb);

        dataCenterTransExportBean.getDataCenterRdbBeans().add(dataCenterRdbBean);
    }

    public static void main(String[] args) throws IOException, JSQLParserException {
        String path = "/Users/<USER>/Desktop/dataTrans.model";
        File file = new File(path.trim());
        byte[] bytes = FileUtils.readFileToByteArray(file);
        DataTransExportBean bean = (DataTransExportBean) SerializableMsgCodec.decode(bytes);

    }

    /**
     * 我的数据源树结构
     *
     * @param userId 用户id
     * @return
     */

    @GetMapping(value = "/queryDataSourceDirTree")
    public Result queryDataSourceDirTree(String userId, HttpServletRequest request) {
        Map<String, String> params = Maps.newHashMap();
        params.put("id", userId);
        TSysAuthUser tSysAuthUser = (TSysAuthUser) this.sysAuthUserService.query(params);
        List<TSysAuthRole> roleList = this.validateAndLogService.getUserAuth(tSysAuthUser);

        List<DatasetTreeModel> tree = dataWareTreeService.getDataWarehouseTreeCopy("", userId);
        tree = tree.stream().sorted(Comparator.comparing(DatasetTreeModel::getName, Collator.getInstance(Locale.CHINA))).collect(Collectors.toList());
        if (roleList == null) {
            return Result.toResult(R.ok(tree));
        }
        return Result.toResult(R.ok(this.isHaveDataObjAuth(roleList) ? tree : this.isHaveAuth(tree, userId, roleList, request)));
    }

    /**
     * 我的数据集目录树
     *
     * @param userId 用户id
     * @return
     */

    @ResponseBody
    @GetMapping("/queryDataSetTree")
    @ApiOperation(value = "获取数据集目录树")
    @ApiImplicitParam(name = "hasDataObj", value = "是否带数据集", required = true, dataType = "boolean")
    public Result queryDataSetTree(String userId) {
        List<DatasetTreeModel> datasetTreeModels = dataSetOperationService.queryDataSetTree(userId, false, "");
        return Result.success(datasetTreeModels);
    }

    /**
     * 方案目录树
     *
     * @param userId 用户id
     * @return
     */

    @GetMapping(value = "/queryTransTree")
    public Result queryTransTree(String userId) {
        List<DatasetTreeModel> treeNodes = (List) dataModelingService.getModelingTransTreeNodes("", "", userId, "TRANS_DIR_MF");
        return Result.toResult(R.ok(treeNodes));
    }


    private Boolean isHaveDataObjAuth(List<TSysAuthRole> roleList) {
        //是否有查看权限 有则不需要过滤
        boolean flag = false;
        for (TSysAuthRole role : roleList) {
            List<String> code = roleService.queryRoleById(role.getId()).getFunctions()
                    .stream().map(TSysFuncBase::getFuncCode).collect(Collectors.toList());
            if (code.contains("dataWarehouseViewDataSet")) {
                flag = true;
                break;
            }
        }
        return flag;
    }

    private List<DatasetTreeModel> isHaveAuth(List<DatasetTreeModel> tree, String userId, List<TSysAuthRole> roleList, HttpServletRequest request) {
        List<DatasetTreeModel> copyTree = new ArrayList<>();
        for (DatasetTreeModel datasetTreeModel : tree) {
            if (null != datasetTreeModel.getChildren() && 3 != datasetTreeModel.getLevel()) {
                datasetTreeModel.setChildren(this.isHaveAuth(datasetTreeModel.getChildren(), userId, roleList, request));
            }

            if (3 == datasetTreeModel.getLevel()) {
                Boolean flag = false;
                Map<String, String> params = Maps.newHashMap();
                params.put("obj_id", userId);
                params.put("func_code", datasetTreeModel.getId());
                TSysAuthObjFunc tSysAuthObjFunc = (TSysAuthObjFunc) sysAuthObjFuncService.query(params);
                if (null != tSysAuthObjFunc) {
                    flag = true;
                }
                List<DatasetTableModel> datasetTableModels = dataWareTreeService.queryDataBaseTableAll(datasetTreeModel.getId());
                for (DatasetTableModel datasetTableModel : datasetTableModels) {
                    //当前用户有此数据源关系 或者此数据源下有授权对象时
                    if (dataSetAuthService.IsAuthDataset(datasetTableModel.getId(), userId, roleList)) {
                        flag = true;
                    }
                }
                if (!flag) {
                    copyTree.add(datasetTreeModel);
                }
            }
        }
        List<String> copyTreeId = copyTree.stream().map(DatasetTreeModel::getId).collect(Collectors.toList());
        return tree.stream().filter(s -> !copyTreeId.contains(s.getId())).collect(Collectors.toList());
    }


    @RequestMapping("/test1")
    public Result test1(String transId) {
        TransMeta transMetaById = dataTransImportExportService.getTransMetaById(transId);
        return Result.success(transMetaById);
    }

    //数据中心使用的bean
    @Data
    public static class DataCenterTransExportBean extends DataTransExportBean implements Serializable {
        //数据仓库目录树
        List<BaseBusiClassify> dataWarehouseDirs = new ArrayList<>();


        //数据仓库，源数据集与数仓目录的挂接关系
        // public List<String> relationSQLs;

        //原本是跟数据源绑定一起的，但是现在数据源下面的rdb不一定要全部导出
        //rdb的表结构
        List<DataCenterRdbBean> dataCenterRdbBeans = Lists.newArrayList();
        //数据中心导出的数据集集合
        public List<LogicObjBean> logicObjBeansList = Lists.newArrayList();

        List<String> logicIds = Lists.newArrayList();
        List<String> dwInstanceIds = Lists.newArrayList();

        //方案跟数据集的关联
        private Map<String, List<String>> transAndLogicMap = new HashMap<>();
        private Map<String, List<String>> transAndDwInstanceMap = new HashMap<>();
        private Map<String, String> logicIdAndRdb = new HashMap<>();

        //服务api导出
        private List<DataServiceApi> dataServiceApis = Lists.newArrayList();
        //服务id集合
        List<String> serviceIds = Lists.newArrayList();

        //api目录树
        List<BaseBusiClassify> dataServiceApiDirs = new ArrayList<>();
        //服务跟目录树的关系
        List<ClassifyElement> apiClassifyElements = Lists.newArrayList();

        //用例集合
        List<String> useCaseIds = Lists.newArrayList();

        private List<DataUseCaseBean> dataUseCaseBeans = Lists.newArrayList();
        //用例目录树
        List<BaseBusiClassify> dataUseCaseDirs = new ArrayList<>();

        //服务跟目录树的关系
        List<ClassifyElement> useCaseClassifyElements = Lists.newArrayList();
        //导出的方案id
        List<String> exportTransIds = Lists.newArrayList();

        //方案参数设置表 T_MD_TRANS_VARIABLE
        List<TransVariable> transVariables = Lists.newArrayList();
        //方案参数关联表 t_md_trans_variable_relation
        List<TransVariableRelation> transVariableRelations = Lists.newArrayList();


    }

    @Data
    public static class DataServiceApi implements Serializable {
        //t_md_service_publication
        public List<Map<String, Object>> dataServicePublications = Lists.newArrayList();

        //t_md_service_meta
        public List<Map<String, Object>> dataServiceMetas = Lists.newArrayList();

        //t_md_service_params
        public List<Map<String, Object>> dataServiceParams = Lists.newArrayList();

        //t_md_resource_relation
        public List<Map<String, Object>> dataResourceRelations = Lists.newArrayList();

        //t_md_resource_config
        public List<Map<String, Object>> dataResourceConfigs = Lists.newArrayList();

        //t_md_many_resource_config
        public List<Map<String, Object>> dataManyResourceConfigs = Lists.newArrayList();

        //t_resource_column_relation
        public List<Map<String, Object>> dataResourceColumnRelation = Lists.newArrayList();


    }

    @Data
    public static class DataUseCaseBean implements Serializable {
        //T_MD_USE_CASE
        public List<Map<String, Object>> dataUseCases = Lists.newArrayList();

        //T_MD_USE_CASE_API
        public List<Map<String, Object>> dataUseCaseApis = Lists.newArrayList();

        //T_MD_USE_CASE_API_INFO
        public List<Map<String, Object>> dataUseCaseParamInfos = Lists.newArrayList();

    }

    @Data
    public static class DataCenterRdbBean implements Serializable {
        // 导出元数据的表和字段信息
        public List<Map<String, Object>> rdbDataObjs = Lists.newArrayList();


        public Set<String> rbdIds = new HashSet<>();

        public Set<String> getRbdIds() {
            if (CollectionUtils.isNotEmpty(rdbDataObjs)) {
                for (Map<String, Object> map : rdbDataObjs) {
                    rbdIds.add((String) map.get("id"));
                }
            }
            return rbdIds;
        }

        public List<Map<String, Object>> rdbDataColumns;
        // 导出数据对象对应的权限控制信息
        public List<Map<String, Object>> tSysFuncs;
        public List<Map<String, Object>> tSysAuthObjFuncs;
        public List<Map<String, Object>> dwTableMappings;
    }

    //创屏使用的bean
    @Data
    public static class DataTransExportBean implements Serializable {

        //方案
        public List<TransMeta> transMetas = Lists.newArrayList();
        public List<LogicDataObjInfo> logics = Lists.newArrayList();
        public List<Dashboard> dashboards = Lists.newArrayList();
        public List<UdfGraph> udfGraphs = Lists.newArrayList();
        public DataTransImportExportController.TransScheduleInfo scheduleInfo;
        public List<Map<String, Object>> transPlugins = Lists.newArrayList();//插件元信息 t_md_etl_trans_plugin
        public List<Map<String, Object>> transPluginPartitions = Lists.newArrayList();//插件分区 T_MD_ETL_TRANS_PLUGIN_PARTITION
        public List<Map<String, Object>> transPluginExpRelations = Lists.newArrayList();//T_MD_ETL_PLUGIN_EXP 插件与exp关系表
        public List<Map<String, Object>> transPluginExpNodes = Lists.newArrayList();//T_MD_EXP_NODE  exp表
        public List<Map<String, Object>> transPluginExpNodeRelation = Lists.newArrayList();//T_MD_EXP_RELATION    exp_node之间的关系表
        public List<Map<String, Object>> transPluginTransAttributes = Lists.newArrayList();//t_md_etl_trans_attribute
        public List<DataTransImportExportController.PortalInfo> portalInfos = Lists.newArrayList();
        public List<ClassifyElement> transClassifyElemnts = Lists.newArrayList();//方案与目录资源关系
        public List<LogicDataObjInfo> transOutputLogics = Lists.newArrayList();//模型输出的数据集

        //数据源关联的信息
        public List<DataSourceBean> dataSourceBeans = Lists.newArrayList();

        //自定义数据集
        public LogicObjBean logicObjBeans;

        //方案目录树
        List<BaseBusiClassify> transDirs = new ArrayList<>();
        //我的空间数据源目录树
        List<BaseBusiClassify> datasourceDirs = new ArrayList<>();
        //自定义数据集目录树
        List<BaseBusiClassify> datasetDirs = new ArrayList<>();

        //当前方案目录
        String transDirId;
        //当前数据源目录
        String datasourceDirId;
        //当前数据集目录
        String datasetDirId;
        //用户id
        String userId;

        //当前方案根目录
        BaseBusiDir transRootDirId;
        //当前数据源根目录
        BaseBusiDir datasourceRootDirId;
        //当前数据集根目录
        BaseBusiDir datasetRootDirId;


    }

    @Data
    @AllArgsConstructor
    public static class LogicObjBean implements Serializable {
        List<LogicDataObjInfo> logicDataObjInfos;
        String relationSQL;//源数据集跟数仓目录的挂接关系，如果没有数据仓库，则这个为空
        List<ClassifyElement> classifyElement;

        public void addLogicBean(LogicObjBean logicObjBean) {
            this.logicDataObjInfos.addAll(logicObjBean.getLogicDataObjInfos());
            this.classifyElement.addAll(logicObjBean.getClassifyElement());
        }

        public LogicObjBean() {
        }
    }

    @Data
    public static class DataSourceBean implements Serializable {
        public String datasourceId;//数据源id
        public List<Map<String, Object>> machine = Lists.newArrayList();//导出机器信息
        public List<Map<String, Object>> rdbCatalog = Lists.newArrayList();// 导出数据库实例信息
        public List<Map<String, Object>> baseLabelElement = Lists.newArrayList();//导出标签信息, 这个是管家系统需要的，缺失会导致导出的数据在管家无法正常显示和使用
        public List<Map<String, Object>> rdbCatalogCluster = Lists.newArrayList();// 导出数据库集群信息
        public List<Map<String, Object>> rdbSchema = Lists.newArrayList();// 导出数据库schema信息
        //导出目录信息
        public BaseBusiDir baseBusiDir;
        public BaseBusiClassify baseBusiClassifie;
        //导出数仓的实例和库信息
        public List<Map<String, Object>> dwDbInstance = Lists.newArrayList();
        public ClassifyElement classifyElement;
        public DwDbMapping dwDbMapping;

        //导出数据集有关的
        // 导出元数据的表和字段信息,如果是数据中心自己得导出，这边得数据应该为空，只有给创屏得导出文件，这边才要导出
        public List<Map<String, Object>> rdbDataObjs = Lists.newArrayList();
        public List<Map<String, Object>> rdbDataColumns = Lists.newArrayList();
        // 导出数据对象对应的权限控制信息
        public List<Map<String, Object>> tSysFuncs = Lists.newArrayList();
        public List<Map<String, Object>> tSysAuthObjFuncs = Lists.newArrayList();
        // 导出数仓的表信息
        public List<Map<String, Object>> dwTableMappings = Lists.newArrayList();

    }

    @Data
    public static class PortalInfo implements Serializable {
        public Portal portal;
        public PortalConfig portalConfig;
        public PublishUrl publishUrl;
        public List<MenuConfig> menuConfigs;
    }

    @Data
    public static class TransScheduleInfo implements Serializable {
        public List<String> transTasks;
        public List<String> transSchedules;
        public List<String> transSubTransRelations;
    }

    /**
     * 目录与元素中间表
     */
    @Data
    public static class ClassifyElement implements Serializable {
        public String elementId;
        public String busiClassifyId;
    }

    /**
     * 库实例与库关系表 t_dw_db_mapping
     */
    @Data
    public static class DwDbMapping implements Serializable {
        public String dwDbInstanceId;
        public String deployedSoftwore;
    }

    /**
     * t_dw_table_mapping
     */
    @Data
    public static class DwTableMapping implements Serializable {
        public String id;
        public String name;
        public String type;
        public String code;
        public String operateTime;
        public String dwDbId;
        public String classifierStatId;
        public String ifStream;
        public String userName;
        public String datasetName;
        public String operateUserId;

    }


    @Autowired
    private BaseDaoImpl baseDao;

    @Autowired
    private BaseService baseService;

    @Autowired
    private TransTemplateService transTemplateService;

    @Autowired
    IDataSetEditService editService;


    @Autowired
    private IMenuConfigService menuConfigService;

    @Autowired
    BeanFactory beanFactory;

    @Autowired
    private DataBaseFactory dataBaseFactory;

    @Autowired
    private MetadataService metadataService;


    /**
     * 连接测试：检查数据源信息是否正常连接
     * 测试连接接口
     *
     * @param dataSourceVO 数据源信息
     * @return
     */
    @PostMapping("/datasource/check")
    public Result checkConnect(@RequestBody DataSourceStatusVO dataSourceVO) {
        return Result.success(check(dataSourceVO));
    }

    private DataSourceStatusVO check(DataSourceStatusVO dataSourceVO) {
        DataBaseConnectService connectService = dataBaseFactory.matchDb(dataSourceVO.getDbType());
        boolean connectionStatus = connectService.testConnection(dataSourceVO);
        dataSourceVO.setConnectState(connectionStatus);
        return dataSourceVO;
    }


    private void batchUpdateSource(List<DataSourceVO> dataSourceVOS) {
        for (DataSourceVO dataSourceVO : dataSourceVOS) {
            dataSourceUpdate(dataSourceVO);
        }
    }

    //保存服务api
    private void saveServiceApi(DataCenterTransExportBean exportBean,String newUserId) {
        List<DataServiceApi> dataServiceApis = exportBean.getDataServiceApis();
        if (CollectionUtils.isEmpty(dataServiceApis)) return;
        List<BaseBusiClassify> dataServiceApiDirs = exportBean.getDataServiceApiDirs();
        List<ClassifyElement> apiClassifyElements = exportBean.getApiClassifyElements();
        for (DataServiceApi dataServiceApi : dataServiceApis) {
            List<Map<String, Object>> dataServicePublications = dataServiceApi.getDataServicePublications();
            if(CollectionUtils.isEmpty(dataServicePublications)) continue;
            if (!filterServiceIds.contains(dataServicePublications.get(0).get("id"))) continue;
            try {
                List<Map<String, Object>> dataServiceMetas = dataServiceApi.getDataServiceMetas();
                List<Map<String, Object>> dataServiceParams = dataServiceApi.getDataServiceParams();
                List<Map<String, Object>> dataResourceConfigs = dataServiceApi.getDataResourceConfigs();

                List<Map<String, Object>> dataManyResourceConfigs = dataServiceApi.getDataManyResourceConfigs();
                List<Map<String, Object>> dataResourceColumnRelation = dataServiceApi.getDataResourceColumnRelation();
                List<Map<String, Object>> dataResourceRelations = dataServiceApi.getDataResourceRelations();

                StringBuilder executeSql = new StringBuilder();

                addElementUserIds(dataServicePublications);
                Map<String, String> replaceSaveType = new HashMap<>();
                replaceSaveType.put("save_type", "1");
                executeSql.append(standardSimpleBuildSqlByData(dataServicePublications, "t_md_service_publication", replaceSaveType, DataExportUtil.ID));

                addElementUserIds(dataServiceMetas);

                Map<String, String> replaceMap = replaceLoadPath(dataServiceMetas);
                executeSql.append(standardSimpleBuildSqlByData(dataServiceMetas, "t_md_service_meta", replaceMap, DataExportUtil.ID));

                addElementUserIds(dataServiceParams);
                executeSql.append(standardSimpleBuildSqlByData(dataServiceParams, "t_md_service_params", null, DataExportUtil.ID));

                executeSql.append(standardSimpleBuildSqlByData(dataResourceConfigs, "t_md_resource_config", null, DataExportUtil.ID));

                executeSql.append(standardSimpleBuildSqlByData(dataManyResourceConfigs, "t_md_many_resource_config", null, DataExportUtil.ID));

                executeSql.append(standardSimpleBuildSqlByData(dataResourceColumnRelation, "t_resource_column_relation", null, DataExportUtil.ID));

                executeSql.append(standardSimpleBuildSqlByData(dataResourceRelations, "t_md_resource_relation", null, DataExportUtil.ID));
                transTemplateService.sqlRun(executeSql.toString());
                importCompleteSuccessResultVoList.add(ImportCompleteResultVo.success(dataServicePublications.get(0), SERVICE_API_TYPE));
            } catch (Exception e) {
                log.error(e.getMessage(),e);
                importCompleteFailResultVoList.add(ImportCompleteResultVo.fail(dataServicePublications.get(0), SERVICE_API_TYPE, e.getMessage()));
            }
        }
        //todo 保存服务目录
        saveBaseClassify("全部", newUserId, "TRANS_SERVICE_DIR", "TRANS_SERVICE_CLASSIFY", "我的服务", dataServiceApiDirs, apiClassifyElements);

    }

    private Map<String,String> replaceLoadPath(List<Map<String,Object>> maps){
        Map<String,String> replaceMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(maps)){
            Map<String, Object> next = maps.iterator().next();
            String load_path = (String) next.get("load_path");
            String[] split = load_path.split("/");

            dirBasicPath = dirBasicPath.replace("\\", "/");
            if (dirBasicPath.toCharArray()[dirBasicPath.length() - 1] != '/') {
                dirBasicPath += '/';
            }

            String loadPath =  dirBasicPath + (dirBasicPath.endsWith("/") ? "" : "/")  + split[split.length-2] + "/" + split[split.length-1];
            replaceMap.put("load_path",loadPath);
        }
        replaceMap.put("status", "0");
        return replaceMap;

    }

    private void saveUseCase(DataCenterTransExportBean exportBean) {
        List<DataUseCaseBean> dataUseCaseBeans = exportBean.getDataUseCaseBeans();
        if (CollectionUtils.isEmpty(dataUseCaseBeans)) return;
        List<BaseBusiClassify> dataUseCaseDirs = exportBean.getDataUseCaseDirs();
        List<ClassifyElement> caseClassifyElements = exportBean.getUseCaseClassifyElements();
        for (DataUseCaseBean dataUseCaseBean : dataUseCaseBeans) {
            List<Map<String, Object>> dataUseCases = dataUseCaseBean.getDataUseCases();
            if (!filterCaseIds.contains(dataUseCases.get(0).get("id"))) continue;
            try {
                List<Map<String, Object>> dataUseCaseApis = dataUseCaseBean.getDataUseCaseApis();
                List<Map<String, Object>> dataUseCaseParamInfos = dataUseCaseBean.getDataUseCaseParamInfos();
                StringBuilder executeSql = new StringBuilder();

                addElementUserIds(dataUseCases);
                executeSql.append(standardSimpleBuildSqlByData(dataUseCases, "T_MD_USE_CASE", null, DataExportUtil.ID));

                addElementUserIds(dataUseCaseApis);
                executeSql.append(standardSimpleBuildSqlByData(dataUseCaseApis, "T_MD_USE_CASE_API", null, DataExportUtil.ID));


                addElementUserIds(dataUseCaseParamInfos);
                executeSql.append(standardSimpleBuildSqlByData(dataUseCaseParamInfos, "T_MD_USE_CASE_API_INFO", null, DataExportUtil.ID));

                transTemplateService.sqlRun(executeSql.toString());
                importCompleteSuccessResultVoList.add(ImportCompleteResultVo.success(dataUseCases.get(0), USE_CASE_TYPE));
            } catch (Exception e) {
                log.error(e.getMessage(),e);
                importCompleteFailResultVoList.add(ImportCompleteResultVo.fail(dataUseCases.get(0), USE_CASE_TYPE, e.getMessage()));
            }
        }
        //todo 保存用例的目录结构，暂时未找到用例的根目录
        saveBaseClassify("全部", exportBean.getUserId(), "USE_CASE_API_DIR", "USE_CASE_API_CLASSIFY", "全部用例", dataUseCaseDirs, caseClassifyElements);

    }


    private void saveBaseClassify(String dirName, String newUserId, String dirCode, String classifyMyCode, String classifyName, List<BaseBusiClassify> classifies, List<ClassifyElement> classifyElements) {
        //保存目录
        BaseBusiDir datasourceRootDir = dataTransImportExportService.findBaseDirByNameAndUserId(dirName, null, dirCode);

        //保存方案目录
        syncClassify(classifies, newUserId, classifyMyCode, classifyName);
        saveClassify(classifies, datasourceRootDir);

        //保存方案跟目录的关系
        String saveTransRelationSql = saveLogicRelation(classifyElements);
        if (StringUtils.isNotBlank(saveTransRelationSql))
            transTemplateService.saveSQL(saveTransRelationSql);
    }

    /**
     * 修改数据源连接信息
     *
     * @param dataSourceVO 数据源信息
     * @return
     */
    @PostMapping("/datasource/update")
    public Result update(@RequestBody DataSourceVO dataSourceVO) {
        dataSourceUpdate(dataSourceVO);
        return Result.success();
    }

    private void dataSourceUpdate(DataSourceVO dataSourceVO) {
        metadataService.updateDwbName(dataSourceVO.getId(), dataSourceVO.getDbType(), dataSourceVO.getDbName());
        metadataService.updateDataSource(dataSourceVO); //更新数据源
    }

    /**
     * 导入二进制模型文件(该文件是上步↑↑↑↑↑↑↑↑导出的.model文件)
     * 创屏使用
     *
     * @return
     * @throws IOException
     */
    @RequestMapping("/importDataTrans")
    public Result importDataTrans(@RequestParam("file") MultipartFile file, String transClassifyId, String dataSetClassifyId, String dataSourceClassifyId, String newUserId) throws IOException {
        try {
            queryDbSourceList(dataSourceClassifyId);
            DataExportVo dataExportVo = new DataExportVo(transClassifyId, dataSetClassifyId, dataSourceClassifyId);
            dataTransImportExportService.deleteDataByClassifyId(dataExportVo);
        } catch (Exception e) {
            throw new RuntimeException("删除目录失败！");
        }
        try {
            modelImport(file, newUserId);
            List<DataSourceVO> dataSourceBySchema = this.getDataSourceBySchemaId();
            return Result.success(dataSourceBySchema);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return Result.error("500", e.getMessage());
        }
    }

    /**
     * 解析文件返回列表
     *
     * @return
     * @throws IOException
     */
    @RequestMapping("/parseDataCenterModel")
    public Result parseDataCenterModel(@RequestParam("file") MultipartFile file) {
        try {
            dataCenterTransExportBean = (DataCenterTransExportBean) SerializableMsgCodec.decode(file.getBytes());
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return Result.error("500", "序列化异常，请检查导入文件是否正常！");
        }
        try {
            Map<String, String> map = new HashMap<>();
            map.put("id", dataCenterTransExportBean.getUserId());
            TSysAuthUser tSysAuthUser = (TSysAuthUser) sysAuthUserService.query(map);
            ImportResultVo importResultVo = new ImportResultVo();
            importResultVo.setTransClassify(dataCenterTransExportBean.getTransDirs());
            importResultVo.setTransClassifyRelation(dataCenterTransExportBean.getTransClassifyElemnts());
            setTransDataSource(tSysAuthUser, importResultVo);
            return Result.success(importResultVo);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return Result.error("500", e.getMessage());
        }
    }


    /**
     * 解析文件返回列表
     *
     * @return
     * @throws IOException
     */
    @RequestMapping("/parseDataCenterServiceModel")
    public Result parseDataCenterServiceModel(@RequestParam("file") MultipartFile file,HttpServletRequest httpServletRequest) {
        try {
            dataCenterTransExportBean = (DataCenterTransExportBean) SerializableMsgCodec.decode(file.getBytes());
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return Result.error("500", "序列化异常，请检查导入文件是否正常！");
        }
        try {
            Map<String, String> map = new HashMap<>();
            map.put("id", dataCenterTransExportBean.getUserId());
            TSysAuthUser tSysAuthUser = (TSysAuthUser) sysAuthUserService.query(map);
            ImportResultVo importResultVo = new ImportResultVo();
            if (CollectionUtils.isNotEmpty(dataCenterTransExportBean.getDataUseCaseDirs())) {
                importResultVo.setImportType(ImportFilterEnum.CASE.code);
                importResultVo.setTransClassify(dataCenterTransExportBean.getDataUseCaseDirs());
                importResultVo.setTransClassifyRelation(dataCenterTransExportBean.getUseCaseClassifyElements());
            } else {
                importResultVo.setImportType(ImportFilterEnum.SERVICE_API.code);
                importResultVo.setTransClassify(dataCenterTransExportBean.getDataServiceApiDirs());
                importResultVo.setTransClassifyRelation(dataCenterTransExportBean.getApiClassifyElements());
            }

            setTransDataSource(tSysAuthUser, importResultVo);
            return Result.success(importResultVo);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return Result.error("500", e.getMessage());
        }
    }

    private void setTransDataSource(TSysAuthUser tSysAuthUser, ImportResultVo importResultVo) {
        List<TransMeta> transMetas = dataCenterTransExportBean.getTransMetas();
        List<ImportResulObjVo> transMetaMaps = Lists.newArrayList();
        for (TransMeta transMeta : transMetas) {
            ImportResulObjVo transMap = new ImportResulObjVo();
            transMap.setId(transMeta.getId());
            transMap.setName(transMeta.getName());
           // transMap.setOperateUserId(transMeta.getOperateUserId());
           // transMap.setOperateUserName(tSysAuthUser.getObjName());
            transMap.setOperateTime(transMeta.getOperateTime());
            transMetaMaps.add(transMap);
        }
        importResultVo.setTransMetas(transMetaMaps);
        List<DataSourceBean> dataSourceBeans = dataCenterTransExportBean.getDataSourceBeans();
        List<ImportResulObjVo> dwInstances = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(dataSourceBeans)) {
            for (DataSourceBean dataSourceBean : dataSourceBeans) {
                List<Map<String, Object>> dwDbInstance = dataSourceBean.getDwDbInstance();
                for (Map<String, Object> db : dwDbInstance) {
                    ImportResulObjVo dwInstance = new ImportResulObjVo();
                    dwInstance.setId((String) db.get("id"));
                    dwInstance.setName((String) db.get("name"));
                    //dwInstance.setOperateUserId((String) db.get("operate_user_id"));
                    dwInstance.setOperateTime((String) db.get("operate_time"));
                    dwInstance.setBelongType((String) db.get("db_instance_type"));
                   // dwInstance.setOperateUserName(tSysAuthUser.getObjName());
                    dwInstances.add(dwInstance);

                }
            }
        }
        importResultVo.setDwInstances(dwInstances);
        List<LogicObjBean> logicObjBeanList = dataCenterTransExportBean.getLogicObjBeansList();
        List<ImportResulObjVo> logicMetaMaps = Lists.newArrayList();
        for(LogicObjBean logicObjBeans : logicObjBeanList){
            if (logicObjBeans != null) {
                List<LogicDataObjInfo> logicDataObjInfos = logicObjBeans.getLogicDataObjInfos();
                if (CollectionUtils.isNotEmpty(logicDataObjInfos)) {
                    for (LogicDataObjInfo logicDataObjInfo : logicDataObjInfos) {
                        ImportResulObjVo logic = new ImportResulObjVo();
                        LogicDataObj logicDataObj = logicDataObjInfo.getLogicDataObj();
                        logic.setId(logicDataObj.getId());
                        // logic.setOperateUserId(logicDataObj.getOperateUserId());
                        logic.setBelongType(logicDataObj.getBelongType());
                        logic.setName(logicDataObj.getName());
                        // logic.setOperateUserName(tSysAuthUser.getObjName());
                        logic.setOperateTime(logicDataObj.getOperateTime());
                        logicMetaMaps.add(logic);
                    }
                }
            }
        }

        //用例列表
        List<DataUseCaseBean> dataUseCaseBeans = dataCenterTransExportBean.getDataUseCaseBeans();
        List<ImportResulObjVo> useCaseResults = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(dataUseCaseBeans)) {
            for (DataUseCaseBean dataUseCaseBean : dataUseCaseBeans) {
                List<Map<String, Object>> useCases = dataUseCaseBean.getDataUseCases();
                for (Map<String, Object> db : useCases) {
                    ImportResulObjVo useCase = new ImportResulObjVo();
                    useCase.setId((String) db.get("id"));
                    useCase.setName((String) db.get("name"));
                   // useCase.setOperateUserId((String) db.get("operate_user_id"));
                    useCase.setOperateTime((String) db.get("operate_time"));
                    useCase.setBelongType((String) db.get("db_instance_type"));
                    //useCase.setOperateUserName(tSysAuthUser.getObjName());
                    useCaseResults.add(useCase);
                }
            }
        }
        //服务列表
        List<DataServiceApi> dataServiceApis = dataCenterTransExportBean.getDataServiceApis();
        List<ImportResulObjVo> serviceApisResults = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(dataServiceApis)) {
            for (DataServiceApi dataServiceApi : dataServiceApis) {
                List<Map<String, Object>> servicePublications = dataServiceApi.getDataServicePublications();
                for (Map<String, Object> db : servicePublications) {
                    ImportResulObjVo serviceApi = new ImportResulObjVo();
                    serviceApi.setId((String) db.get("id"));
                    serviceApi.setName((String) db.get("name"));
                   // serviceApi.setOperateUserId((String) db.get("operate_user_id"));
                    serviceApi.setOperateTime((String) db.get("operate_time"));
                    serviceApi.setBelongType((String) db.get("db_instance_type"));
                   // serviceApi.setOperateUserName(tSysAuthUser.getObjName());
                    serviceApisResults.add(serviceApi);
                }
            }
        }
        importResultVo.setServiceApis(serviceApisResults);
        importResultVo.setLogicInfos(logicMetaMaps);
        importResultVo.setUseCases(useCaseResults);
    }

    /**
     * 解析文件返回列表
     *
     * @return
     * @throws IOException
     */
    @RequestMapping("/importDataCenterModel")
    public Result importDataCenterModel(@RequestBody ImportFilterVo importFilterVo) {
        if (!checkDcSuperUser(importFilterVo.getUserId())) return Result.error("200", "要导入的模型中包含数据仓库，请重新选择导入的用户！");
        if (CollectionUtils.isEmpty(importFilterVo.getFilterIds())) return Result.error("200", "请选中要导入的模型！");
        try {
            init(importFilterVo);
            String newUserId = importFilterVo.getUserId();
            //查询到数据源信息，从而去做更新
            queryDbSourceListByUserId(newUserId);
            dataCenterModelImport(dataCenterTransExportBean, newUserId);
            //数据中心导入的数据源信息，让用户手动去修改
            //List<DataSourceVO> dataSourceBySchema = this.getDataSourceBySchemaId();
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return Result.error("500", e.getMessage());
        }finally {
            ImportCompleteList importCompleteList = new ImportCompleteList();
            importCompleteList.setImportCompleteFailResultVoList(importCompleteFailResultVoList);
            importCompleteList.setImportCompleteSuccessResultVoList(importCompleteSuccessResultVoList);
            return Result.success(importCompleteList);
        }
    }

    private boolean checkDcSuperUser(String newUserId) {
        List<BaseBusiClassify> dataWarehouseDirs = dataCenterTransExportBean.getDataWarehouseDirs();
        String userId = dataCenterTransExportBean.getUserId();
        if (CollectionUtils.isNotEmpty(dataWarehouseDirs)) {
            if (GlobalConstant.UserProperties.DC_SUPER_ID.equals(userId) || !userId.equals(newUserId)) {
                return false;
            }
        }
        return true;
    }

    private void init(ImportFilterVo importFilterVo) {
        filterTransIds = Lists.newArrayList();
        filterLogicIds = Lists.newArrayList();
        filterDwIds = Lists.newArrayList();
        filterRdbIds = Lists.newArrayList();
        filterServiceIds = Lists.newArrayList();
        filterCaseIds = Lists.newArrayList();
        importCompleteSuccessResultVoList = Lists.newArrayList();
        importCompleteFailResultVoList = Lists.newArrayList();

        ImportFilterEnum instanceByCode = ImportFilterEnum.getInstanceByCode(importFilterVo.getFilterType());
        switch (instanceByCode) {
            case TRANS:
                filterTransIds = importFilterVo.getFilterIds();
                filterServiceIds = dataCenterTransExportBean.getServiceIds();
                break;
            case SERVICE_API:
                filterServiceIds = importFilterVo.getFilterIds();
                filterTransIds = dataCenterTransExportBean.getExportTransIds();
                filterCaseIds = dataCenterTransExportBean.getUseCaseIds();
                break;
            case CASE:
                filterCaseIds = importFilterVo.getFilterIds();
                filterServiceIds = dataCenterTransExportBean.getServiceIds();
                filterTransIds = dataCenterTransExportBean.getExportTransIds();
                break;
        }
        //以下暂时用不上
        Map<String, List<String>> transAndLogicMap = dataCenterTransExportBean.getTransAndLogicMap();
        Map<String, List<String>> transAndDwInstanceMap = dataCenterTransExportBean.getTransAndDwInstanceMap();
        Map<String, String> logicIdAndRdb = dataCenterTransExportBean.getLogicIdAndRdb();
        for (String id : filterTransIds) {
            if (transAndLogicMap.get(id) != null) {
                filterLogicIds.addAll(transAndLogicMap.get(id));
            }
            if (transAndDwInstanceMap.get(id) != null) {
                filterDwIds.addAll(transAndDwInstanceMap.get(id));
            }
            if (logicIdAndRdb.get(id) != null) {
                filterRdbIds.add(logicIdAndRdb.get(id));
            }
        }
    }


    //导入创屏使用模型文件
    private void modelImport(@RequestParam("file") MultipartFile file, String newUserId) throws IOException {
        DataExportUtil.init(isDelete);
        // 1. 反序列化 .model 模型文件
        DataTransExportBean exportBean = (DataTransExportBean) SerializableMsgCodec.decode(file.getBytes());
        // 2. 保存导入文件的用户id
        oldUserId = exportBean.getUserId();
        // 3. 保存数据源，数据集，方案的目录结构
        saveDataClassify(exportBean, newUserId);
        // 4. 获取没有之前没有导入的schema信息
        queryNoUpdateDataSourceSchemaMaps(exportBean);
        // 5. 导入数据源
        dataSourceImport(exportBean, newUserId);
        // 6. 导入数据集管理方案
        saveLogicDataMeta(exportBean);
        // 7. 导入数据挖掘方案
        saveTransMeta(exportBean);
        // 8. 批量修改数据源
        batchUpdateSource(dataSourceStatusVOS);
        // 9. 修改用户信息
        dataTransImportExportService.updateUserInfo(newUserId, elementUserIds, stepUserIds);
    }

    //导入数据中心使用模型文件
    private void dataCenterModelImport(DataCenterTransExportBean exportBean, String newUserId) throws IOException, InvocationTargetException, IllegalAccessException {
        DataExportUtil.init(isDelete);
        // 1. 反序列化 .model 模型文件
        // 2. 导入前，先删掉，数据集，rdb物理表，方案信息
        deleteTransRdbLogic(exportBean);//删除服务得方法还没写完
        // 3. 保存导入文件的用户id
        oldUserId = exportBean.getUserId();
        // 4. 保存数据源，数据集，方案的目录结构
        saveDataCenterClassify(exportBean, newUserId);
        // 5. 获取没有之前没有导入的schema信息
        queryNoUpdateDataSourceSchemaMaps(exportBean);
        // 6. 导入数据源
        dataSourceImport(exportBean, newUserId);
        // 7. 导入rdb数据表信息
        saveDataRdbMapping(exportBean, newUserId);
        // 8. 导入数据集管理方案
        saveLogicDataMetas(exportBean);
        // 9. 导入数据挖掘方案
        saveTransMeta(exportBean);
        // 10. 导入方案参数信息
        transVariableImport(exportBean,newUserId);
        // 10. 批量修改数据源
        batchUpdateSource(dataSourceStatusVOS);
        // 11. 服务api导入
        saveServiceApi(exportBean,newUserId);
        // 12. 测试用例导入
        saveUseCase(exportBean);
        // 13. 修改用户信息
        dataTransImportExportService.updateUserInfo(newUserId, elementUserIds, stepUserIds);
    }

    private void transVariableImport(DataCenterTransExportBean dataCenterTransExportBean,String newUserId) throws InvocationTargetException, IllegalAccessException {
        StringBuffer executeSql = new StringBuffer();
        List<String> importTransIds = importCompleteSuccessResultVoList.stream().filter(s -> TRANS_TYPE.equals(s.getType())).map(ImportCompleteResultVo::getId).collect(Collectors.toList());

        List<TransVariableRelation> transVariableRelations = dataCenterTransExportBean.getTransVariableRelations();
        List<TransVariableRelation> importVariableRelations = transVariableRelations.stream().filter( s -> importTransIds.contains(s.getTransId())).collect(Collectors.toList());

        List<String> variable_ids = importVariableRelations.stream().map(s -> s.getVariableId()).collect(Collectors.toList());
        List<TransVariable> transVariables = dataCenterTransExportBean.getTransVariables();

        List<TransVariable> importVariables = transVariables.stream().filter(s -> variable_ids.contains(s.getId())).collect(Collectors.toList());
        importVariables.stream().forEach(s -> s.setOperateUserId(newUserId));
        List<String> collect = transVariableRelations.stream().map(s -> s.getId()).collect(Collectors.toList());

        List<TransVariable> tS = Lists.newArrayList();
        List<TransVariableRelation> rS = Lists.newArrayList();
        for(TransVariable importVariable :  importVariables){
            TransVariable transVariable = new TransVariable();
            transVariable.setVariable(importVariable.isVariable());
            transVariable.setOperateUserId(newUserId);
            transVariable.setGlobal(importVariable.isGlobal());
            transVariable.setParamCode(importVariable.getParamCode());
            transVariable.setParamValue(importVariable.getParamValue());
            transVariable.setParamType(importVariable.getParamType());
            transVariable.setCode(importVariable.getCode());
            transVariable.setId(importVariable.getId());
            transVariable.setName(importVariable.getName());
            transVariable.setMemo(importVariable.getMemo());
            transVariable.setType(importVariable.getType());
            transVariable.setOperateTime(importVariable.getOperateTime());
            tS.add(transVariable);
        }
        for(TransVariableRelation importVariableRelation :  importVariableRelations){
            TransVariableRelation transVariableRelation = new TransVariableRelation();
            transVariableRelation.setTransId(importVariableRelation.getTransId());
            transVariableRelation.setVariableId(importVariableRelation.getVariableId());
            transVariableRelation.setCode(importVariableRelation.getCode());
            transVariableRelation.setId(importVariableRelation.getId());
            transVariableRelation.setName(importVariableRelation.getName());
            transVariableRelation.setMemo(importVariableRelation.getMemo());
            transVariableRelation.setType(importVariableRelation.getType());
            transVariableRelation.setOperateTime(importVariableRelation.getOperateTime());
            rS.add(transVariableRelation);
        }
        dataTransImportExportService.deleteVariableRelations(collect);
        dataTransImportExportService.deleteVariable(variable_ids);
        for(TransVariable t :  tS){
            dataTransImportExportService.save(t);
        }
        for(TransVariableRelation r :  rS){
            dataTransImportExportService.save(r);
        }
    }

    private void deleteTransRdbLogic(DataCenterTransExportBean dataCenterTransExportBean) {
        List<String> transIds = dataCenterTransExportBean.getTransMetas().stream().map(TransMeta::getId).collect(Collectors.toList());
        //过滤掉不是要导入的
        List<String> collectTransIds = transIds;
        if (CollectionUtils.isNotEmpty(filterTransIds)) {
            collectTransIds = transIds.stream().filter(s -> filterTransIds.contains(s)).collect(Collectors.toList());
        }

        deleteServiceByIds(dataCenterTransExportBean.getServiceIds());
        deleteUseCaseByIds(dataCenterTransExportBean.getUseCaseIds());
        Set<String> rbdIds = new HashSet<>();
        for(DataCenterRdbBean dataCenterRdbBean : dataCenterTransExportBean.getDataCenterRdbBeans()){
             rbdIds.addAll(dataCenterRdbBean.getRbdIds());
        }
        dataTransImportExportService.deleteImportDataResource(new HashSet<>(collectTransIds), new HashSet<>(dataCenterTransExportBean.getLogicIds()), rbdIds);


    }

    //通过服务id删除服务
    private void deleteServiceByIds(List<String> serviceIds) {
        if (CollectionUtils.isEmpty(serviceIds)) return;
        for (String serviceId : serviceIds) {
            if (StringUtils.isBlank(serviceId)) continue;
            ServicePublication servicePublication = servicePublicationService.getServicePublication(serviceId);
            if (servicePublication == null) continue;
            Set<ServiceMeta> serviceMetas = servicePublication.getServiceMetas();
            try {
                for (ServiceMeta serviceMeta : serviceMetas) {
                    servicePublicService.deleteElementAndClassify(serviceMeta.getId());
                    servicePublicService.offlineServiceByServiceMetaId(serviceMeta.getId());
                }
            } catch (Exception e) {
                log.error(e.getMessage(),e);
            }
        }
    }

    //通过服务用例id删除用例
    private void deleteUseCaseByIds(List<String> useCaseIds) {
        if (CollectionUtils.isEmpty(useCaseIds)) return;
        for (String useCaseId : useCaseIds) {
            UseCase useCaseById = useCaseService.getUseCaseById(useCaseId);
            if (useCaseById == null) continue;
            useCaseService.deleteUseCase(useCaseById);
            commonBusiClassifyService.deleteClassifyRelationByElementId(useCaseId);
        }
    }

    private void saveDataRdbMapping(DataCenterTransExportBean exportBean, String newUserId) {
        List<DataCenterRdbBean> dataCenterRdbBeans = exportBean.getDataCenterRdbBeans();
        for(DataCenterRdbBean dataCenterRdbBean : dataCenterRdbBeans){
          if (dataCenterRdbBean == null) return;
          String executeSql = dataRdbMappingImport(dataCenterRdbBean.getRdbDataObjs(), dataCenterRdbBean.getRdbDataColumns(), dataCenterRdbBean.getTSysFuncs(), dataCenterRdbBean.getTSysAuthObjFuncs(), dataCenterRdbBean.getDwTableMappings(), newUserId);
          transTemplateService.sqlRun(executeSql);
        }
    }


    private void saveLogicDataMeta(DataTransExportBean exportBean) {
        List<LogicDataObjInfo> logics = exportBean.getLogics();
        LogicObjBean logicObjBeans = exportBean.getLogicObjBeans();
        if (logics != null && !logics.isEmpty()) {
            saveLogic(logics);
        }
        if (logicObjBeans != null && logicObjBeans.getLogicDataObjInfos() != null && !logicObjBeans.getLogicDataObjInfos().isEmpty()) {
            saveLogic(logicObjBeans.getLogicDataObjInfos());
            if (StringUtils.isNotBlank(logicObjBeans.getRelationSQL()))
                transTemplateService.saveSQL(logicObjBeans.getRelationSQL());
            String saveLogicRelationSql = saveLogicRelation(logicObjBeans.getClassifyElement());
            if (StringUtils.isNotBlank(saveLogicRelationSql))
                transTemplateService.saveSQL(saveLogicRelationSql);
        }

        //插入输出插件新建表生成的数据集
        if (CollectionUtils.isNotEmpty(exportBean.getTransOutputLogics()) && isDeleteCatalogResources) {
            saveLogic(exportBean.getTransOutputLogics());
        }

    }

    private void saveLogicDataMetas(DataCenterTransExportBean exportBean) {
        List<LogicDataObjInfo> logics = exportBean.getLogics();
        List<LogicObjBean> logicObjBeanList = exportBean.getLogicObjBeansList();
        if (logics != null && !logics.isEmpty()) {
            saveLogic(logics);
        }
        for(LogicObjBean logicObjBeans :logicObjBeanList  ){
            if (logicObjBeans != null && logicObjBeans.getLogicDataObjInfos() != null && !logicObjBeans.getLogicDataObjInfos().isEmpty()) {
                saveLogic(logicObjBeans.getLogicDataObjInfos());
                if (StringUtils.isNotBlank(logicObjBeans.getRelationSQL()))
                    transTemplateService.saveSQL(logicObjBeans.getRelationSQL());
                String saveLogicRelationSql = saveLogicRelation(logicObjBeans.getClassifyElement());
                if (StringUtils.isNotBlank(saveLogicRelationSql))
                    transTemplateService.saveSQL(saveLogicRelationSql);
            }
        }

        //插入输出插件新建表生成的数据集
        if (CollectionUtils.isNotEmpty(exportBean.getTransOutputLogics()) && isDeleteCatalogResources) {
            saveLogic(exportBean.getTransOutputLogics());
        }

    }

    private void saveTransMeta(DataTransExportBean exportBean) {
        List<TransMeta> transMetas = exportBean.getTransMetas();
        List<Map<String, Object>> transPlugins = exportBean.getTransPlugins();
        TransScheduleInfo scheduleInfo = exportBean.getScheduleInfo();
        List<UdfGraph> udfGraphs = exportBean.getUdfGraphs();
        if (transMetas != null && !transMetas.isEmpty()) {
            //通过transMeta信息删除调度信息
            deleteSchedule(transMetas);
            //先导入插件的元信息
            String transPluginSQL = buildTransPluginSQL(transPlugins);
            String pluginMetaSQL = buildTransPluginExpSQL(exportBean);
            if (StringUtils.isNotBlank(transPluginSQL)) transTemplateService.sqlRun(transPluginSQL);
            if (StringUtils.isNotBlank(pluginMetaSQL)) transTemplateService.sqlRun(pluginMetaSQL);

            // 通过 modelName 查找或创建标准模型目录
            Map<String, String> transMapping = new HashMap<>();
            Map<String, UdfGraph> graphMap = new HashMap<>();

            if (udfGraphs != null && !udfGraphs.isEmpty()) {
                graphMap = udfGraphs.stream().collect(Collectors.toMap(UdfGraph::getId, o -> o));
            }
            for (TransMeta t : transMetas) {
                if (CollectionUtils.isNotEmpty(filterTransIds) && !filterTransIds.contains(t.getId())) {
                    transMapping.put(t.getId(), t.getId());
                    continue;
                }
                try {
                    checkPluginRelation = Lists.newArrayList();
                    // 将TransMeta对象解析成SQL语句，并保存
                    String n_id = transTemplateService.saveTransModelBySQL(t, graphMap, null, false);
                    transMapping.put(t.getId(), n_id);
                    String s = standardSimpleBuildMergeSqlByData(checkPluginRelation, "t_etl_trans_stepdetail", null, "child_trans_id");
                    if(StringUtils.isNotBlank(s)){
                        transTemplateService.saveSQL(s);
                    }
                    importCompleteSuccessResultVoList.add(ImportCompleteResultVo.success(t.getId(), t.getName(), TRANS_TYPE));
                } catch (Exception e) {
                    log.error(e.getMessage(),e);
                    importCompleteFailResultVoList.add(ImportCompleteResultVo.fail(t.getId(), t.getName(), TRANS_TYPE, e.toString()));
                }
            }
            // 保存方案相关的调度、任务依赖信息
            if (scheduleInfo != null) {
                List<String> executeList = new ArrayList<>();
                Map<String, String> taskMapping = new HashMap<>();
                for (String json : scheduleInfo.getTransTasks()) {
                    Map map = JSON.parseObject(json, Map.class);
                    if (CollectionUtils.isNotEmpty(filterTransIds) && !filterTransIds.contains(transMapping.get(map.get("trans_id"))))
                        continue;
                    String uuid = StringUtils.uuid();
                    taskMapping.put((String) map.get("id"), uuid);
                    map.put("id", uuid);
                    map.put("trans_id", transMapping.get(map.get("trans_id")));
                    map.put("run_param", null);
                    executeList.add(insertSQL("t_trans_task", map, null, null));
                }
                for (String json : scheduleInfo.getTransSchedules()) {
                    Map map = JSON.parseObject(json, Map.class);
                    if (CollectionUtils.isNotEmpty(filterTransIds) && !filterTransIds.contains(transMapping.get(map.get("trans_id"))))
                        continue;
                    map.put("id", StringUtils.uuid());
                    map.put("trans_id", transMapping.get(map.get("trans_id")));
                    map.put("task_id", taskMapping.get(map.get("task_id")));
                    executeList.add(insertSQL("t_trans_schedule", map, null, null));
                }
                for (String json : scheduleInfo.getTransSubTransRelations()) {
                    Map map = JSON.parseObject(json, Map.class);
                    if (CollectionUtils.isNotEmpty(filterTransIds) && !filterTransIds.contains(transMapping.get(map.get("trans_id"))))
                        continue;
                    map.put("id", StringUtils.uuid());
                    map.put("trans_id", transMapping.get(map.get("trans_id")));
                    map.put("subtrans_id", transMapping.get(map.get("trans_id")));
                    executeList.add(insertSQL("t_trans_subtrans_relation", map, null, null));
                }
                String taskSQL = String.join(" ", executeList);
                if (StringUtils.isNotBlank(taskSQL)) transTemplateService.saveSQL(taskSQL);
            }
        }

    }

    private String saveLogicRelation(List<ClassifyElement> classifyElements) {
        StringBuilder executeSql = new StringBuilder();
        for (ClassifyElement classifyElement : classifyElements) {
            Map<String, Object> classifyElementParam = new HashMap();
            classifyElementParam.put("element_id", classifyElement.getElementId());
            classifyElementParam.put("busi_classify_id", classifyElement.getBusiClassifyId());
            if(classifyElementMap.get(classifyElement.getBusiClassifyId()) != null){
                classifyElementParam.put("busi_classify_id", classifyElementMap.get(classifyElement.getBusiClassifyId()));
            }
            executeSql.append(insertClassifyElementSQL("t_md_classify_element", classifyElementParam));
        }
        return executeSql.toString();
    }

    //统一保存目录
    private void saveDataClassify(DataTransExportBean dataTransExportBean, String newUserId) {
        List<BaseBusiClassify> transDirs = dataTransExportBean.getTransDirs();
        List<BaseBusiClassify> datasetDirs = dataTransExportBean.getDatasetDirs();
        List<BaseBusiClassify> datasourceDirs = dataTransExportBean.getDatasourceDirs();

        //自定义数据源的dir
        BaseBusiDir datasourceRootDir = dataTransImportExportService.findBaseDirByNameAndUserId("我的空间", newUserId, "DATAWAREHOUSE_DIR");

        BaseBusiDir dataTransRootDir = dataTransImportExportService.findBaseDirByNameAndUserId("我的", null, "TRANS_DIR_MF");

        //保存方案目录
        syncClassify(transDirs, newUserId, "TRANS_DIR_MF_MY", "我的空间");
        saveClassify(transDirs, dataTransRootDir);

        //保存数据集目录
        syncClassify(datasetDirs, newUserId, "DATASET_DIR_MY", "我的空间");
        saveClassify(datasetDirs, null);

        //保存自定义数据源目录
        saveClassify(datasourceDirs, datasourceRootDir);

        //todo 保存目录与对象之间的关系

        //保存方案跟目录的关系
        String saveTransRelationSql = saveLogicRelation(dataTransExportBean.getTransClassifyElemnts());
        if (StringUtils.isNotBlank(saveTransRelationSql))
            transTemplateService.saveSQL(saveTransRelationSql);

    }

    private void saveDataCenterClassify(DataCenterTransExportBean dataTransExportBean, String newUserId) {
        saveDataClassify(dataTransExportBean, newUserId);
        List<BaseBusiClassify> dataWarehouseDirs = dataTransExportBean.getDataWarehouseDirs();

        //数据空间数据源的dir
        BaseBusiDir dataWarehouseRootDir = dataTransImportExportService.findBaseDirByNameAndUserId("数据仓库", null, "DATAWAREHOUSE_DIR");
        //保存数据仓库数据源目录
        saveClassify(dataWarehouseDirs, dataWarehouseRootDir);

    }

    private void syncClassify(List<BaseBusiClassify> classifies, String newUserId, String code, String classifyName) {
        if (CollectionUtils.isEmpty(classifies)) return;
        BaseBusiClassify baseBusiClassify = dataTransImportExportService.findBaseClassifyByNameAndUserId(classifyName, newUserId, code);
        String classifyId = "";
        if (classifies.get(0).getName().equals(classifyName)) {
            BaseBusiClassify busiClassify = dataTransImportExportService.get(BaseBusiClassify.class, classifies.get(0).getId());
            if(busiClassify == null){
                classifyId = classifies.get(0).getId();
                classifies.remove(0);
            }else {
                baseBusiClassify = busiClassify;
            }
        }
        if(CollectionUtils.isEmpty(classifies)){
            classifyElementMap.put(classifyId,baseBusiClassify.getId());
            classifies.add(baseBusiClassify);
        }
        for(BaseBusiClassify baseBusiClassify1 : classifies){
           if((baseBusiClassify1.getParentBc() != null && classifyId.equals(baseBusiClassify1.getParentBc().getId()))){
               baseBusiClassify1.setParentBc(baseBusiClassify);
               classifyElementMap.put(classifyId,baseBusiClassify.getId());
           }
        }
    }

    private void saveClassify(List<BaseBusiClassify> baseBusiClassifies, BaseBusiDir baseBusiDir) {
        if (CollectionUtils.isEmpty(baseBusiClassifies)) return;
        for (BaseBusiClassify classify : baseBusiClassifies) {
            classify.setBusiDir(baseBusiDir);
            if (isDeleteCatalogResources && !dataTransImportExportService.isExistClassify(classify.getId())) {
               // classify.setElements(null);
                addElementUserId(classify.getId());
                Map<String, Object> objectMap = buildClassifyData(classify);
                transTemplateService.sqlRun(buildImportSql("t_md_busi_classify", objectMap, DataExportUtil.ID, (String) objectMap.get(DataExportUtil.ID)));
            }else if(!isDeleteCatalogResources) {
                addElementUserId(classify.getId());
                Map<String, Object> objectMap = buildClassifyData(classify);
                transTemplateService.sqlRun(buildImportSql("t_md_busi_classify", objectMap, DataExportUtil.ID, (String) objectMap.get(DataExportUtil.ID)));
            }
        }
    }


    private void saveLogic(List<LogicDataObjInfo> logics) {
        dataTransImportExportService.saveLogicInfo(logics);
    }

    private void saveLogicColumn(List<LogicDataColumn> logicDataColumns) {
        for (LogicDataColumn logicDataColumn : logicDataColumns) {
            logicDataColumn.setOwner(null);
            this.baseDao.save(logicDataColumn);
        }
    }

    private void saveDataLogic(List<LogicDataObjInfo> dataObjInfos) {

    }


    private String buildTransPluginSQL(List<Map<String, Object>> transPlugin) {
        //过滤掉已经存在的插件
        for (int i = 0; i < transPlugin.size(); i++) {
            Map plugin = transPlugin.get(i);
            if (checkPluginExist((String) plugin.get("name"), (String) plugin.get("code"))) {
                transPlugin.remove(i);
                i--;
            }
        }
        return standardSimpleBuildSqlByData(transPlugin, "t_md_etl_trans_plugin", null, null);
    }

    private String standardSimpleBuildSqlByData(List<Map<String, Object>> list, String tableName, Map<String, String> replaceMap, String key) {
        if (DataExportUtil.isDeleteCatalogResources) {
            return standardSimpleBuildInsertSqlByData(list, tableName, replaceMap, key);
        } else {
            return standardSimpleBuildMergeSqlByData(list, tableName, replaceMap, key);
        }
    }

    private String standardSimpleBuildInsertSqlByData(List<Map<String, Object>> list, String tableName, Map<String, String> replaceMap, String key) {
        StringBuilder builder = new StringBuilder();
        if (CollectionUtils.isEmpty(list)) return builder.toString();
        for (Map<String, Object> map : list) {
            if (map.size() > 0) {
                if (replaceMap != null) {
                    replaceMap.forEach((k, v) -> {
                        if (map.containsKey(k)) map.put(k, v);
                    });
                }
                if (StringUtils.isNotBlank(key)) {
                    builder.append(buildImportSql(tableName, map, key, (String) map.get(key)))
                            .append(" ");
                } else {
                    builder.append(buildImportSql(tableName, map, null, null))
                            .append(" ");
                }
            }
        }
        return builder.toString();
    }

    //合并数据所用sql
    private String standardSimpleBuildMergeSqlByData(List<Map<String, Object>> list, String tableName, Map<String, String> replaceMap, String key) {
        StringBuilder builder = new StringBuilder();
        if (CollectionUtils.isEmpty(list)) return builder.toString();
        for (Map<String, Object> map : list) {
            if (map.size() > 0) {
                if (replaceMap != null) {
                    replaceMap.forEach((k, v) -> {
                        if (map.containsKey(k)) map.put(k, v);
                    });
                }
                builder.append(mergeSql(tableName, map, key, (String) map.get(key)))
                        .append(" ");
            }
        }
        return builder.toString();
    }

    private boolean checkPluginExist(String name, String code) {
        String sql = "select * from t_md_etl_trans_plugin where  code =:code";
        Map<String, String> param = Maps.newHashMap();
        param.put("code", code);
        List list = baseDao.sqlQueryForList(sql, param);
        if (CollectionUtils.isNotEmpty(list)) return true;
        return false;
    }

    public BaseBusiClassify addBusiClassify(String classifyId, String modelName, String suffix, String busiClassifyCode) {
        BaseBusiClassify classify;
        if (StringUtils.isNotBlank(classifyId))
            classify = this.baseService.get(BaseBusiClassify.class, classifyId);
        else
            classify = transTemplateService.queryClassify(busiClassifyCode);
        Assert.notNull(classify, "目录不存在!");
        BaseBusiClassify childClassify = transTemplateService.queryModelClassify(modelName + suffix, classify.getId());
        if (childClassify == null) {
            childClassify = new BaseBusiClassify();
            childClassify.setParentBc(classify);
            childClassify.setName(modelName + suffix);
            childClassify.setCode(modelName + suffix);
            childClassify.setOperateTime(DateUtils.getTimeStr(new Date(), 2));
            childClassify.setOperateUserId(classify.getOperateUserId());
            baseService.saveOrUpdate(childClassify);
        }
        return childClassify;
    }

    private void tSaveMenuConfig(List<MenuConfig> menuConfigs, PortalConfig portalConfig, String parentId) {
        for (MenuConfig menuConfig : menuConfigs) {
            menuConfig.setOwner(portalConfig);
            menuConfig.setId(null);
            if ("dashboard".equals(menuConfig.getContentType())) {
                menuConfig.setPostURLParam(queryDashGroup(menuConfig.getContentGetURl()));
            }
            menuConfig.setParentMenuConfig(parentId == null ? null : menuConfigService.get(MenuConfig.class, parentId));
            menuConfigService.saveOrUpdate(menuConfig);
            if (CollectionUtils.isNotEmpty(menuConfig.getChildrenMenuConfigs())) {
                List<MenuConfig> childMenuConfigs = new ArrayList<>(menuConfig.getChildrenMenuConfigs());
                this.tSaveMenuConfig(childMenuConfigs, portalConfig, menuConfig.getId());
            }
        }
    }

    private String queryDashGroup(String id) {
        String sql = "select group_id from t_v_dashboards where id =:id";
        Map<String, String> params = new HashMap<>();
        params.put("id", id);
        return this.baseDao.sqlQueryForValue(sql, params);
    }

    public void replaceLogicDataObjSearchsql() {
        try {
            editService.replaceLogicDataObjSearchsql();
        } catch (Exception e) {
            log.error(e.getMessage(),e);
        }
    }


    private String buildTransPluginExpSQL(DataTransExportBean bean) {
        StringBuilder transPluginPartitionSQL = new StringBuilder();
        StringBuilder transPluginRelationSQL = new StringBuilder();
        StringBuilder transPluginExpNodeSQL = new StringBuilder();
        //StringBuilder transPluginExpSQL = new StringBuilder();
        StringBuilder transPluginAttributeSQL = new StringBuilder();
        StringBuilder transPluginExpNodeRelationSQL = new StringBuilder();
        List<Object> pluginIds = bean.getTransPlugins().stream().map(s -> s.get("id")).collect(Collectors.toList());
        for (Object pluginId : pluginIds) {
            List<Map<String, Object>> partitions = Lists.newArrayList();
            addPluginMetaData(bean.getTransPluginPartitions(), pluginId, partitions);
            transPluginPartitionSQL.append(standardSimpleBuildSqlByData(partitions, "T_MD_ETL_TRANS_PLUGIN_PARTITION", null, null));

            List<Map<String, Object>> expRelations = Lists.newArrayList();
            addPluginMetaData(bean.getTransPluginExpRelations(), pluginId, expRelations);
            transPluginRelationSQL.append(standardSimpleBuildSqlByData(expRelations, "T_MD_ETL_PLUGIN_EXP", null, null));

            List<Map<String, Object>> expNodes = Lists.newArrayList();
            addPluginMetaData(bean.getTransPluginExpNodes(), pluginId, expNodes);
            transPluginExpNodeSQL.append(standardSimpleBuildSqlByData(expNodes, "T_MD_EXP_NODE", null, null));

            List<Map<String, Object>> expNodeRelations = Lists.newArrayList();
            addPluginMetaData(bean.getTransPluginExpNodeRelation(), pluginId, expNodeRelations);
            transPluginExpNodeRelationSQL.append(standardSimpleBuildSqlByData(expNodeRelations, "T_MD_EXP_RELATION", null, null));


          /*  List<Map<String, Object>> transExps = Lists.newArrayList();
            addPluginMetaData(bean.getTransPluginTransExp(), pluginId, transExps);
            transPluginExpSQL.append(standardSimpleBuildSqlByData(transExps, "T_ETL_TRANS_EXP", null));*/

            List<Map<String, Object>> transAttributes = Lists.newArrayList();
            addPluginMetaData(bean.getTransPluginTransAttributes(), pluginId, transAttributes);
            transPluginAttributeSQL.append(standardSimpleBuildSqlByData(transAttributes, "t_md_etl_trans_attribute", null, null));
        }
        return transPluginPartitionSQL.append(transPluginExpNodeSQL.toString()).append(transPluginRelationSQL.toString()).append(transPluginExpNodeRelationSQL.toString()).append(transPluginAttributeSQL.toString()).toString();
    }


    private void dataSourceImport(DataTransExportBean exportBean, String newUserId) {
        List<DataSourceBean> dataSourceBeans = exportBean.getDataSourceBeans();
        if (CollectionUtils.isEmpty(dataSourceBeans)) return;
        for (DataSourceBean sourceBean : dataSourceBeans) {
            List<Map<String, Object>> dwDbInstance = sourceBean.getDwDbInstance();
            try {
                //数据源id
                StringBuilder executeSql = new StringBuilder();
                List<Map<String, Object>> machine = sourceBean.getMachine();
                addElementUserIds(machine);
                executeSql.append(standardSimpleBuildSqlByData(machine, "t_md_machine", null, DataExportUtil.ID));

                List<Map<String, Object>> rdbCatalog = sourceBean.getRdbCatalog();
                addElementUserIds(rdbCatalog);
                if (rdbCatalog != null)
                    executeSql.append(standardSimpleBuildSqlByData(rdbCatalog, "t_md_rdb_catalog", null, DataExportUtil.ID));

                List<Map<String, Object>> baseLabelElement = sourceBean.getBaseLabelElement();
                addElementUserIds(baseLabelElement);
                if (baseLabelElement != null)
                    executeSql.append(standardSimpleBuildSqlByData(baseLabelElement, "T_MD_LABEL_ELEMENT", null, DataExportUtil.ID));

                List<Map<String, Object>> rdbCatalogCluster = sourceBean.getRdbCatalogCluster();
                addElementUserIds(rdbCatalogCluster);
                if (rdbCatalogCluster != null)
                    executeSql.append(standardSimpleBuildSqlByData(rdbCatalogCluster, "t_md_rdbcatalog_cluster", null, DataExportUtil.ID));


                List<Map<String, Object>> rdbSchema = sourceBean.getRdbSchema();
                addElementUserIds(rdbSchema);
                if (rdbSchema != null)
                    executeSql.append(standardSimpleBuildSqlByData(rdbSchema, "t_md_rdb_schema", null, DataExportUtil.ID));

                addElementUserIds(dwDbInstance);
                if (dwDbInstance != null)
                    executeSql.append(standardSimpleBuildSqlByData(dwDbInstance, "T_MD_DW_DB_INSTANCE", null, DataExportUtil.ID));

                DwDbMapping dwDbMapping = sourceBean.getDwDbMapping();
                Map<String, Object> dwDbMappingParam = new HashMap();
                dwDbMappingParam.put("deployed_software", dwDbMapping.getDeployedSoftwore());
                dwDbMappingParam.put("dw_db_instance_id", dwDbMapping.getDwDbInstanceId());
                executeSql.append(insertSQL("t_dw_db_mapping", dwDbMappingParam, null, null));

                ClassifyElement classifyElement = sourceBean.getClassifyElement();
                Map<String, Object> classifyElementParam = new HashMap();
                classifyElementParam.put("element_id", classifyElement.getElementId());
                classifyElementParam.put("busi_classify_id", classifyElement.getBusiClassifyId());
                executeSql.append(insertClassifyElementSQL("t_md_classify_element", classifyElementParam));
                if (isDeleteCatalogResources) {//给创屏使用的bean结构中，数据源是存在着里面的
                    String sql = dataRdbMappingImport(sourceBean.getRdbDataObjs(), sourceBean.getRdbDataColumns(), sourceBean.getTSysFuncs(), sourceBean.getTSysAuthObjFuncs(), sourceBean.getDwTableMappings(), newUserId);
                    executeSql.append(sql);
                }
                transTemplateService.sqlRun(executeSql.toString());
                importCompleteSuccessResultVoList.add(ImportCompleteResultVo.success(dwDbInstance.get(0), DW_INSTANCE_TYPE));
            } catch (Exception e) {
                log.error(e.getMessage(),e);
                importCompleteFailResultVoList.add(ImportCompleteResultVo.fail(dwDbInstance.get(0), DW_INSTANCE_TYPE, e.getMessage()));
            }
        }
        if(CollectionUtils.isNotEmpty(exportBean.getDataSourceBeans())){
            List<String> dataSourceIds = exportBean.getDataSourceBeans().stream().map(DataSourceBean::getDatasourceId).collect(Collectors.toList());
            List<String> schemaIds = dataTransImportExportService.querySchemaIdByDwInstanceIds(dataSourceIds);
            queryDataService.refreshDataSourceCache(schemaIds);
        }

    }

    private String dataRdbMappingImport(List<Map<String, Object>> rdbDataObjs, List<Map<String, Object>> rdbDataColumns, List<Map<String, Object>> tSysFuncs, List<Map<String, Object>> tSysAuthObjFuncs, List<Map<String, Object>> dwTableMappings, String newUserId) {
        StringBuffer executeSql = new StringBuffer();
        addElementUserIds(rdbDataObjs);
        executeSql.append(standardSimpleBuildSqlByData(rdbDataObjs, "t_md_rdb_dataobj", null, DataExportUtil.ID));
        addElementUserIds(rdbDataColumns);
        executeSql.append(standardSimpleBuildSqlByData(rdbDataColumns, "t_md_rdb_datacolumn", null, DataExportUtil.ID));
        executeSql.append(standardSimpleBuildSqlByData(tSysFuncs, "t_sys_func", null, "func_code"));
        Map<String, String> replaceMap = new HashMap<>();
        replaceMap.put("obj_id", newUserId);
        executeSql.append(standardSimpleBuildSqlByData(tSysAuthObjFuncs, "t_sys_auth_obj_func", replaceMap, DataExportUtil.ID));
        addElementUserIds(dwTableMappings);
        executeSql.append(standardSimpleBuildSqlByData(dwTableMappings, "t_dw_table_mapping", null, DataExportUtil.ID));
        return executeSql.toString();
    }


    //删除目录下相应的资源
    public Result deleteDataByClassifyId(DataTransExportBean dataTransExportBean) {
        DataExportVo dataExportVo = new DataExportVo(dataTransExportBean.getTransDirId(), dataTransExportBean.getDatasetDirId(), dataTransExportBean.getDatasourceDirId());
        dataTransImportExportService.deleteDataByClassifyId(dataExportVo);
        return Result.success();
    }

    //删除调度信息
    private void deleteSchedule(List<TransMeta> transMetas) {
        for (TransMeta transMeta : transMetas) {
            dataTransImportExportService.deleteScheduleByTransId(transMeta.getId());
        }
    }


    //删除前进行缓存
    private void queryDbSourceList(String classifyId) {
        dataSourceStatusVOS = Lists.newArrayList();
        buildDbSourceList(classifyId);
    }

    private void buildDbSourceList(String classifyId) {
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageIndex(1);
        pageInfo.setPageSize(defaultPageSize);
        if (!dataTransImportExportService.isExistClassify(classifyId)) return;
        PageInfo dataSet = dataWareTreeService.queryDataTable(pageInfo, classifyId, "", "");
        if (dataSet == null) return;
        List<Map<String, String>> dataList = dataSet.getDataList();
        if (CollectionUtils.isNotEmpty(dataList)) {
            for (Map<String, String> objectMap : dataList) {
                DataBaseConnectService connectService = dataBaseFactory.matchDb(objectMap.get("instanceType"));
                DataSourceVO dataSourceVo = connectService.getDataSourceVo(objectMap.get("schemaId"), objectMap.get("instanceType"), objectMap.get("name"));
                dataSourceVo.setId(objectMap.get("catalogId"));
                //dataSourceVo.setClusterName();
                dataSourceStatusVOS.add(dataSourceVo);
            }
        }
    }

    //获取到当前用户跟节点的所有数据源对象
    private void queryDbSourceListByUserId(String newUserId) {
        dataSourceStatusVOS = Lists.newArrayList();
        BaseBusiDir datasourceRootDir = dataTransImportExportService.findBaseDirByNameAndUserId("我的空间", newUserId, "DATAWAREHOUSE_DIR");
        BaseBusiDir dataWarehouseRootDir = dataTransImportExportService.findBaseDirByNameAndUserId("数据仓库", null, "DATAWAREHOUSE_DIR");
        buildDbSourceList(datasourceRootDir.getId());
        buildDbSourceList(dataWarehouseRootDir.getId());

    }

    //比较已经入库的和没有入库的数据
    private void queryNoUpdateDataSourceSchemaMaps(DataTransExportBean dataTransExportBean) {
        rdbSchemaMaps = Lists.newArrayList();
        List<DataSourceBean> dataSourceBeans = dataTransExportBean.getDataSourceBeans();
        if (CollectionUtils.isEmpty(dataSourceBeans)) return;
        List<String> rdbCatalogIds = dataSourceStatusVOS.stream().map(DataSourceVO::getId).collect(Collectors.toList());
        for (DataSourceBean dataSourceBean : dataSourceBeans) {
            List<Map<String, Object>> rdbCatalogs = dataSourceBean.getRdbCatalog();
            Map<String, Object> rdbCatalog = rdbCatalogs.get(0);
            Map<String, Object> dwDbInstance = dataSourceBean.getDwDbInstance().get(0);
            Map<String, Object> rdbSchema = dataSourceBean.getRdbSchema().get(0);
            if (!rdbCatalogIds.contains(rdbCatalog.get("id"))) {
                Map<String, String> map = new HashMap<>();
                map.put("dbType", (String) dwDbInstance.get("db_instance_type"));
                map.put("rbdSchemaId", (String) rdbSchema.get("id"));
                map.put("dbName", (String) dwDbInstance.get("name"));
                map.put("id", (String) rdbCatalog.get("id"));
                rdbSchemaMaps.add(map);
            }
        }
    }

    //取出要给前端修改的数据,不在dataSourceStatusVOS的数据
    private List<DataSourceVO> getDataSourceBySchemaId() {
        List<DataSourceVO> dataSourceVOS = Lists.newArrayList();
        for (Map<String, String> map : rdbSchemaMaps) {
            String dbType = map.get("dbType");
            String rbdSchemaId = map.get("rbdSchemaId");
            String dbName = map.get("dbName");
            DataBaseConnectService connectService = dataBaseFactory.matchDb(dbType);
            DataSourceVO dataSourceVo = connectService.getDataSourceVo(rbdSchemaId, dbType, dbName);
            dataSourceVo.setId(map.get("id"));
            dataSourceVOS.add(dataSourceVo);
        }
        return dataSourceVOS;
    }

    @ResponseBody
    @GetMapping("/queryDbSourceByClassifyId")
    public Result queryDbSourceByClassifyId(String classifyId) {
        this.queryDbSourceList(classifyId);
        return Result.success(dataSourceStatusVOS);
    }

    /*以下是对外开放的接口*/


    //获取将要修改的数据源信息
    @ResponseBody
    @GetMapping("/queryWillUpdateDataSourceByFile")
    public Result queryWillUpdateDataSourceByFile(@RequestParam("file") MultipartFile file) throws IOException {
        byte[] bytes = FileUtils.readFileToByteArray((File) file);
        DataTransExportBean exportBean = (DataTransExportBean) SerializableMsgCodec.decode(bytes);
        queryNoUpdateDataSourceSchemaMaps(exportBean);
        return Result.success(this.getDataSourceBySchemaId());
    }

    //获取将要修改的数据源信息
    @ResponseBody
    @GetMapping("/queryWillUpdateDataSource")
    public Result queryWillUpdateDataSource() {
        return Result.success(this.getDataSourceBySchemaId());
    }

    //删除目录id信息
    @ResponseBody
    @RequestMapping("/deleteResourceByClassifyId")
    public Result deleteResourceByClassifyId(@RequestBody DeleteClassifyVo deleteClassifyVo) {
        DataExportVo dataExportVo = new DataExportVo(deleteClassifyVo.getTransClassifyId(), deleteClassifyVo.getDataSetClassifyId(), deleteClassifyVo.getDataSourceClassifyId());
        //先将库里面的数据源修改的信息缓存起来
        queryDbSourceList(deleteClassifyVo.getDataSourceClassifyId());
        dataTransImportExportService.deleteDataByClassifyId(dataExportVo);
        return Result.success(dataSourceStatusVOS);
    }

    /**
     * 修改数据源连接信息：批量修改
     * 批量修改连接接口
     *
     * @param dataSourceVOS 数据源信息
     * @return
     */
    @PostMapping("/datasource/batchUpdate")
    public Result updateConnect(@RequestBody List<DataSourceVO> dataSourceVOS) {
        batchUpdateSource(dataSourceVOS);
        return Result.success();
    }

    /**
     * 连接测试：检查数据源信息是否正常连接
     * 批量测试连接接口
     *
     * @param dataSourceVOS 数据源信息
     * @return
     */
    @PostMapping("/datasource/batchCheck")
    public Result batchCheckConnect(@RequestBody List<DataSourceStatusVO> dataSourceVOS) {
        for (DataSourceStatusVO dataSourceVO : dataSourceVOS) {
            check(dataSourceVO);
        }
        return Result.success(dataSourceVOS);
    }

    @RequestMapping("/updateUserInfo")
    public Result updateUserInfo(String newUserId) {
        dataTransImportExportService.updateUserInfo(newUserId, elementUserIds, stepUserIds);
        return Result.success();
    }


    private Map<String, Object> buildClassifyData(BaseBusiClassify t) {
        Map<String, Object> param = new HashMap<>();
        param.put("id", t.getId());
        param.put("name", t.getName());
        param.put("type", t.getType());
        param.put("code", t.getCode());
        param.put("owner_id", t.getOwner() != null ? t.getOwner().getId() : null);
        param.put("operate_time", t.getOperateTime());
        param.put("operate_user_id", t.getOperateUserId());
        param.put("version", t.getVersion());
        param.put("memo", t.getMemo());
        param.put("map_key", null);
        param.put("extended_type", null);
        param.put("busi_dir_id", t.getBusiDir() != null ? t.getBusiDir().getId() : null);
        param.put("parent_classify_id", t.getParentBc() != null ? t.getParentBc().getId() : null);
        param.put("sort_no", t.getSortNo());
        param.put("res_count", t.getResCount());
        param.put("x", t.getX());
        param.put("y", t.getY());
        return param;
    }

    @GetMapping("/getServiceClassify")
    public Result queryServiceClassifyTree(HttpSession session) {
        String userId = (String) session.getAttribute("userId");
        Assert.hasLength(userId, "请先登录!");
        List<Tree> trees = serviceManagementService.queryServiceClassifyTree(userId);

        List<String> allClassifyIds = Lists.newArrayList();

        List<Object> serviceApis = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(trees)) {
            allClassifyIds = serviceManagementService.queryBusiClassifyIdList(trees.get(0).getId());
            PageInfo pageInfo = new PageInfo();
            pageInfo.setPageSize(1000000);
            pageInfo.setPageIndex(1);
            String name = "";
            PageInfo result = servicePublicationService.queryDataByName(pageInfo, name, "0", userId, name, name, allClassifyIds);
            serviceApis.addAll(result.getDataList());
        }
        Map<String, Object> map = new HashMap<>();
        map.put("classifyTree", trees);
        map.put("serviceApis", serviceApis);
        List<DataTransImportExportController.ClassifyElement> classifyElements = Lists.newArrayList();
        List<Map> maps = dataTransImportExportService.queryServiceElement(allClassifyIds);
        for (Map<String, String> map1 : maps) {
            ClassifyElement classifyElement1 = new ClassifyElement();
            classifyElement1.setElementId(map1.get("element_id"));
            classifyElement1.setBusiClassifyId(map1.get("busi_classify_id"));
            classifyElements.add(classifyElement1);
        }
        map.put("classifyElements", classifyElements);

        return Result.toResult(R.ok(map));
    }


    private static final String USE_CASE_DIR_CODE = "USE_CASE_API_DIR";
    private static final String USE_CASE_CLASSIFY_CODE = "USE_CASE_API_CLASSIFY";

    @Autowired
    private CommonBusiClassifyService commonBusiClassifyService;

    @Autowired
    private UseCaseManageService useCaseManageService;

    @Autowired
    private UseCaseService useCaseService;

    @GetMapping("/queryUseCaseClassifyTree")
    public Result queryUseCaseClassifyTree(HttpSession session) {
        String userId = (String) session.getAttribute("userId");
        Assert.hasLength(userId, "请先登录!");
        List<Tree> trees = commonBusiClassifyService.queryClassifyTree(userId, USE_CASE_DIR_CODE);
        List<String> allClassifyIds = Lists.newArrayList();
        List<DataTransImportExportController.ClassifyElement> classifyElements = Lists.newArrayList();

        List<Map> useCases = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(trees)) {
            allClassifyIds = serviceManagementService.queryBusiClassifyIdList(trees.get(0).getId());
            List<Map> maps = dataTransImportExportService.queryServiceElement(allClassifyIds);
            for (Map<String, String> map1 : maps) {
                ClassifyElement classifyElement1 = new ClassifyElement();
                classifyElement1.setElementId(map1.get("element_id"));
                classifyElement1.setBusiClassifyId(map1.get("busi_classify_id"));
                classifyElements.add(classifyElement1);
            }
            useCases.addAll(dataTransImportExportService.queryUseCaseByElement(classifyElements.stream().map(ClassifyElement::getElementId).collect(Collectors.toList())));
        }

        Map<String, Object> map = new HashMap<>();
        map.put("classifyTree", trees);
        map.put("useCases", useCases);

        map.put("classifyElements", classifyElements);

        return Result.toResult(R.ok(map));
    }


}
