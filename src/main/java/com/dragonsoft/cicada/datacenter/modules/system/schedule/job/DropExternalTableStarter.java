package com.dragonsoft.cicada.datacenter.modules.system.schedule.job;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @date 2020-11-30 18:49
 */
@Component(DropExternalTableStarter.BEAN_NAME)
@ConfigurationProperties(prefix = "gp.external")
public class DropExternalTableStarter {

    public static final String BEAN_NAME = "dropExternalTableStarter";
    private List<DropExternalTableJobConfig> configs = new ArrayList<>();

    public void start() {
        for (int i = 0; i < configs.size(); i++) {
            DropExternalTableJobConfig jobConfig = configs.get(i);
            Map<String, Object> params = new HashMap<>();
            params.put(DropExternalTableJob.PARAM_URL, jobConfig.getUrl());
            params.put(DropExternalTableJob.PARAM_USER, jobConfig.getUser());
            params.put(DropExternalTableJob.PARAM_PASSWORD, jobConfig.getPassword());
            params.put(DropExternalTableJob.PARAM_SCHEMA, jobConfig.getSchema());
            QuartzManager.addJob("drop-job-" + i, params, DropExternalTableJob.class, jobConfig.getCronExpression());
        }
    }

    public List<DropExternalTableJobConfig> getConfigs() {
        return configs;
    }

    public void setConfigs(List<DropExternalTableJobConfig> configs) {
        this.configs = configs;
    }
}
