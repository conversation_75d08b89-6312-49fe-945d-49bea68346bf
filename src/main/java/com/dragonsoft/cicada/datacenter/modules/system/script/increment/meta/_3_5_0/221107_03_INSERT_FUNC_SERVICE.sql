-- 一级目录 建模空间

-- 二级 服务类型
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state", "is_doc_show") VALUES ('serviceType', '0', '服务类型', 'serviceSpace', NULL, NULL, NULL, '2022-08-01 14:21:03', NULL, '1', '1');
-- 三级 服务具体类型
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state", "is_doc_show") VALUES ('serviceTypeDataQuery', '0', '数据查询', 'serviceType', NULL, NULL, NULL, '2022-08-01 14:21:03', NULL, '1', '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state", "is_doc_show") VALUES ('serviceTypeInformationVerification', '0', '信息核查', 'serviceType', NULL, NULL, NULL, '2022-08-01 14:21:03', NULL, '1', '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state", "is_doc_show") VALUES ('serviceTypeCompareService', '0', '比对订阅', 'serviceType', NULL, NULL, NULL, '2022-08-01 14:21:03', NULL, '1', '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state", "is_doc_show") VALUES ('serviceTypeDataCollision', '0', '数据碰撞', 'serviceType', NULL, NULL, NULL, '2022-08-01 14:21:03', NULL, '1', '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state", "is_doc_show") VALUES ('serviceTypeModelAnalysis', '0', '模型分析', 'serviceType', NULL, NULL, NULL, '2022-08-01 14:21:03', NULL, '1', '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state", "is_doc_show") VALUES ('serviceTypeAiService', '0', 'AI算法', 'serviceType', NULL, NULL, NULL, '2022-08-01 14:21:03', NULL, '1', '1');

-- 绑定系统管理员权限
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('7ab3e2f38f2c507c9663af03b058ab44', 'd6121bc4248e45019942e2cb78362500', 'serviceType', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('0150d7c3cd9c5ed2bea0abde4563d8e6', 'd6121bc4248e45019942e2cb78362500', 'serviceTypeDataQuery', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('e21c84e1fc9e577dbcfdafca62d5699e', 'd6121bc4248e45019942e2cb78362500', 'serviceTypeInformationVerification', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('b7386c74171a583db2815151a0d7b0c8', 'd6121bc4248e45019942e2cb78362500', 'serviceTypeCompareService', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('3053ffa66637567cb2be84d03f80a11f', 'd6121bc4248e45019942e2cb78362500', 'serviceTypeDataCollision', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('37d21d6ee8aa5d07857ad1eda6c80a1f', 'd6121bc4248e45019942e2cb78362500', 'serviceTypeModelAnalysis', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('571b0083eb1d569b977d7ecee3594601', 'd6121bc4248e45019942e2cb78362500', 'serviceTypeAiService', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
