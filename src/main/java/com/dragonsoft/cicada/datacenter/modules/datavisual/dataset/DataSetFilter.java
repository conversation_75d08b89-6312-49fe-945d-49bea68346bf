package com.dragonsoft.cicada.datacenter.modules.datavisual.dataset;

import com.code.metadata.datavisual.DataSet;
import com.code.metadata.datavisual.Field;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.springframework.beans.BeanUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/3/24 13:45
 */
public class DataSetFilter {

    private static final String DATATYPE = "text";

    private static final String ELASTIC = "ELASTICSEARCH";

    public static List<DataSet> filterEsText(List<DataSet> dataSets) {
        List<DataSet> filterDataSet = Lists.newArrayList();
        for (DataSet dataSet : dataSets) {
            DataSet newDataSet = new DataSet();
            BeanUtils.copyProperties(dataSet, newDataSet);
            String dbType = dataSet.getDbType();
            if (!Strings.isNullOrEmpty(dbType) && ELASTIC.equals(dbType.toUpperCase())) {
                List<Field> newFields = Lists.newArrayList();
                List<Field> fields = dataSet.getFields();
                for (Field field : fields) {
                    if (DATATYPE.equals(field.getDBType())) {
                        continue;
                    }
                    newFields.add(field);
                }
                newDataSet.getFields().clear();
                newDataSet.setFields(newFields);
            }
            filterDataSet.add(newDataSet);

        }
        return filterDataSet;
    }
}
