package com.dragonsoft.cicada.datacenter.modules.system.permissions;

import com.dragonsoft.cicada.datacenter.common.enums.CarDetailTypeEnum;
import com.dragonsoft.cicada.datacenter.common.enums.CaseDetailTypeEnum;
import com.dragonsoft.cicada.datacenter.common.enums.PersonDetailTypeEnum;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/12/12
 */
@CrossOrigin
@RestController
@RequestMapping(value = "/sysinfo")
public class SystemInfoController {

    public static final String CAR_DETAIL_TYPE = "carer";
    public static final String CASE_DETAIL_TYPE = "case";
    public static final String PERSON_DETAIL_TYPE = "personer";

    private Map<String, Map<String, String>> enumMap = new HashMap<>();

    /**
     * 加载枚举表码
     *
     * @param type
     * @return
     */
    @GetMapping(value = "/loadBmEnum")
    public Map loadBmEnum(String type) {
        return enumMap;
    }

    @PostConstruct
    public void init() {
        enumMap = enumMapBuilder();
    }

    private Map<String, Map<String, String>> enumMapBuilder() {
        // 人
        enumMap.put(PERSON_DETAIL_TYPE, getPersonEnumMap());
        // 车
        enumMap.put(CAR_DETAIL_TYPE, getCarEnumMap());
        // 案件
        enumMap.put(CASE_DETAIL_TYPE, getCaseEnumMap());

        return enumMap;
    }

    private Map<String, String> getPersonEnumMap() {
        Map<String, String> personMap = new HashMap<>();
        for (PersonDetailTypeEnum value : PersonDetailTypeEnum.values()) {
            personMap.put(value.getCode(), value.getDesc());
        }

        return personMap;
    }

    private Map<String, String> getCarEnumMap() {
        Map<String, String> carMap = new HashMap<>();
        for (CarDetailTypeEnum value : CarDetailTypeEnum.values()) {
            carMap.put(value.getCode(), value.getDesc());
        }

        return carMap;
    }

    private Map<String, String> getCaseEnumMap() {
        Map<String, String> caseMap = new HashMap<>();
        for (CaseDetailTypeEnum value : CaseDetailTypeEnum.values()) {
            caseMap.put(value.getCode(), value.getDesc());
        }

        return caseMap;
    }


}

