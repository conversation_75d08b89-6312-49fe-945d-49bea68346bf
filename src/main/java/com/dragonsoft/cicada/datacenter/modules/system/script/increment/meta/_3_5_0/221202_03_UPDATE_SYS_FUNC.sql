INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state")
 VALUES ('tempFuncCode_Conversion', null, null, null, NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, null);

update t_sys_auth_obj_func set func_code ='tempFuncCode_Conversion' where id in ( select id from t_sys_auth_obj_func where func_code ='dataProcessDirCicadaCodeConversion' );

update t_sys_func set func_code ='dataProcessDirCicadaCodeTableConversion' where func_code ='dataProcessDirCicadaCodeConversion';

update t_sys_auth_obj_func set func_code ='dataProcessDirCicadaCodeTableConversion' where func_code ='tempFuncCode_Conversion';

delete from t_sys_func where func_code = 'tempFuncCode_Conversion' ;

