package com.dragonsoft.cicada.datacenter.modules.datavisual.model;

import com.code.dragonsoft.dataquery.service.querymodel.ColumnDataModel;
import com.code.metadata.datavisual.WidgetDatasetDims;
import com.code.metadata.datavisual.WidgetDatasetMeasures;
import com.dragonsoft.cicada.datacenter.modules.datavisual.businessrelation.AbsBusinessRelation;
import com.dragonsoft.cicada.datacenter.modules.datavisual.businessrelation.BusinessRelationEnum;
import com.dragonsoft.cicada.datacenter.modules.datavisual.dataset.IDataSetBuilder;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/5/11 16:30
 */
public class BusinessRelationshipWidget extends AbsChartWidget {
    @Override
    public Map<String, Object> loadingData(IDataSetBuilder dataSetBuilder, int mode, String code, int timers) {

        //构建查询sql
        String buildSQL = getSQL(dataSetBuilder, mode);
        //执行查询语句
        ColumnDataModel originalColumns = getColumnDataModel(buildSQL);
        //构建返回的数据
        return builderResultData(originalColumns);
    }

    private Map<String, Object> builderResultData(ColumnDataModel originalColumns) {
        Map<String, Object> resMap = new HashMap<>();
        Map<String, Object> re = new HashMap<>();

        String relationType = this.widgetDataset.getRelationType();
        WidgetDatasetDims point = this.widgetDataset.getFromListDims().iterator().next();
        WidgetDatasetMeasures edge = this.widgetDataset.getWidgetDatasetMeasures().iterator().next();
        AbsBusinessRelation businessRelation = BusinessRelationEnum.valueOf(relationType).getBusinessRelation();

        List<Map> points = businessRelation.builderPoint(originalColumns, this.widgetDataset.getRelationConfigureJson(), point, edge);

        //构建返回的类型
        resMap.put("categories", getCategories());
        resMap.put("nodes", points);
        resMap.put("links", businessRelation.builderEdge(points));
        re.put("code", 1);
        re.put("data", resMap);
        return re;
    }

    private List<Map> getCategories() {
        List<Map> categories = new ArrayList<>();
        Map<String, String> categories1 = new HashMap<>();
        categories1.put("name", "当前人员");
        Map<String, String> categories2 = new HashMap<>();
        categories2.put("name", "关系人员");
        categories.add(categories1);
        categories.add(categories2);
        return categories;
    }

    @Nullable
    private ColumnDataModel getColumnDataModel(String newQuery) {
        ColumnDataModel columns = null;
        columns = query(newQuery);
        return columns;
    }

    @Override
    public String getSQL(IDataSetBuilder dataSetBuilder, int mode) {
        //点数据项
        WidgetDatasetDims point = this.widgetDataset.getFromListDims().iterator().next();
        //边数据项
        WidgetDatasetMeasures edge = this.widgetDataset.getFromListMeasures().iterator().next();
        //关系类行
        String relationType = this.widgetDataset.getRelationType();
        AbsBusinessRelation businessRelation = BusinessRelationEnum.valueOf(relationType).getBusinessRelation();

        String tableName = getSearchSQL(this.widgetDataset.getDatasetId());

        String sql = businessRelation.buildSQL(this.widgetDataset, point,tableName, edge, this.getLinkageFilter(),this.getQuery());

        return sql;
    }

    @Override
    public int compareTo(@NotNull Object o) {
        return 0;
    }
}
