package com.dragonsoft.cicada.datacenter.modules.system.permissions.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 分页请求的VO 包含过滤条件以及分页信息
 *
 * <AUTHOR>
 * @date 2020/5/21
 */
@Data
@ApiModel(value="分页参数模型")
public class PageVo {
    @ApiModelProperty(value="页面模糊查询条件ID" ,required=true)
    private String code;
    @ApiModelProperty(value="页面模糊查询条件name" ,required=true)
    private String name;
    @ApiModelProperty(value="当前页" ,required=true)
    private int pageNum;
    @ApiModelProperty(value="分页大小" ,required=true)
    private int pageSize;
}
