package com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.vo;

import com.code.dataset.operator.column.addcolumn.AddColumnStep;
import com.code.dataset.operator.column.editcolumn.EditColumnStep;
import com.code.dataset.operator.column.format.FormatStep;
import com.code.dataset.operator.column.indextype.IndexTypeStep;
import com.code.dataset.operator.column.numberformat.NumberFormatStep;
import com.code.dataset.operator.filter.FilterConditionStep;
import com.code.dataset.operator.join.JoinTableStep;
import com.code.dataset.operator.join.UnionTableStep;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020.8.8
 */
@Data
public class StepVo implements Serializable {
    private String dataSetId;
    private String stepId;
    private EditColumnStep editColumnStep;
    private AddColumnStep addColumnStep;
    private FormatStep formatStep;
    private IndexTypeStep indexTypeStep;
    private FilterConditionStep filterConditionStep;
    private JoinTableStep joinTableStep;
    private NumberFormatStep numberFormatStep;
    private UnionTableStep unionTableStep;
}
