package com.dragonsoft.cicada.datacenter.modules.datavisual.widget.impl;

import cn.hutool.core.date.StopWatch;
import cn.hutool.core.io.FileUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.code.common.utils.StringUtils;
import com.code.common.utils.assertion.Assert;
import com.code.common.utils.assertion.IllegalLogicException;
import com.code.dataset.IStepRelationService;
import com.code.dragonsoft.dataquery.service.QueryDataService;
import com.code.metadata.datavisual.Dashboard;
import com.code.metadata.datavisual.WidgetDataset;
import com.code.metadata.datavisual.WidgetMeta;
import com.code.metadata.res.common.ClassifierStat;
import com.code.metaservice.datavisual.IDashBoardService;
import com.code.metaservice.datavisual.IWidgetMetaService;
import com.code.metaservice.ddl.ILogicDataColumnService;
import com.code.metaservice.ddl.ILogicDataObjService;
import com.code.metaservice.res.common.ClassifierStatService;
import com.dragonsoft.cicada.datacenter.modules.datavisual.dashboard.IDashboardBuilder;
import com.dragonsoft.cicada.datacenter.modules.datavisual.dashboard.impl.DashboardScheduled;
import com.dragonsoft.cicada.datacenter.modules.datavisual.dataset.IDataSetBuilder;
import com.dragonsoft.cicada.datacenter.modules.datavisual.function.FunctionScheduler;
import com.dragonsoft.cicada.datacenter.modules.datavisual.model.AbsChartWidget;
import com.dragonsoft.cicada.datacenter.modules.datavisual.model.AbsConditionWidget;
import com.dragonsoft.cicada.datacenter.modules.datavisual.model.AbsWidgetFilter;
import com.dragonsoft.cicada.datacenter.modules.datavisual.widget.IChartWidgetBuilder;
import com.dragonsoft.cicada.datacenter.modules.datavisual.widget.IWidgetConditionBuilder;
import com.dragonsoft.cicada.datacenter.modules.datavisual.widget.IWidgetFilterBuilder;
import com.dragonsoft.cicada.datacenter.modules.datavisual.widget.TimerClicker;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.service.MbService;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.*;

@Service
@Slf4j
@EnableCaching
@CacheConfig(cacheNames = {"chartWidgetBuilder"})
public class ChartWidgetBuilder implements IChartWidgetBuilder {
    @Autowired
    IWidgetFilterBuilder widgetFilterBuilder;
    @Autowired
    IWidgetMetaService widgetMetaService;
    @Autowired
    IDataSetBuilder dataSetBuilder;
    @Autowired
    IWidgetConditionBuilder widgetConditionBuilder;
    @Autowired
    private QueryDataService queryDataService;

    @Autowired
    private IDashboardBuilder dashboardBuilder;
    @Autowired
    private IDashBoardService dashBoardService;

    @Autowired
    private ChartWidgetBuilder chartWidgetBuilder;

    @Autowired
    private DashboardScheduled dashboardScheduled;

    @Autowired
    private ILogicDataColumnService logicDataColumnService;
    @Autowired
    private ClassifierStatService classifierStatService;
    @Autowired
    private ILogicDataObjService logicDataObjService;
    @Autowired
    private MbService mbService;

    @Autowired
    private IStepRelationService stepRelationService;



    @Autowired
    private TimerClicker timerClicker;

    @Autowired
    private FunctionScheduler functionScheduler;

    public AbsChartWidget builderChartWidget(String code, String json) {
        AbsChartWidget widget = null;
        WidgetMeta widgetMeta = widgetMetaService.getMetaByCode(code);
        if (null == widgetMeta) {
            log.error("你查询的控件不存在  code:" + code);
        }
        Class c = null;
        try {
            c = Class.forName(widgetMeta.getType());
        } catch (ClassNotFoundException e) {
            log.error("空间转换对象失败  code:" + code,e);
        }
        widget = JSONObject.parseObject(json, (Type) c);

        if(widget.getWidgetDataset() ==null ||(widget.getWidgetDataset().getWidgetDatasetDims().size() <= 0 && widget.getWidgetDataset().getWidgetDatasetMeasures().size() <= 0)){
            Assert.fail("请先配置相关信息！");
        }

        widgetSetTableCode(widget);

        String filter = widget.getFilter();
        List<AbsWidgetFilter> widgetFilters = Lists.newArrayList();
        if (!StringUtils.isBlank(filter)) {
            widgetFilters.addAll(widgetFilterBuilder.builder(widget.getFilter()));
        }
        //添加联动的过滤条件
        String linkage = widget.getLinkageFilter();
        if (!StringUtils.isBlank(linkage)) {
            widgetFilters.addAll(widgetFilterBuilder.builder(linkage));
        }

        //钻取
        String drill = widget.getDrillFilter();
        if (!StringUtils.isBlank(drill)) {
            widgetFilters.addAll(widgetFilterBuilder.builder(drill));
        }
        widget.setQueryDataService(queryDataService);
        widget.setLogicDataColumnService(logicDataColumnService);
        widget.setClassifierStatService(classifierStatService);
        widget.setLogicDataObjService(logicDataObjService);
        widget.setFunctionScheduler(functionScheduler);
        widget.setMbService(mbService);
        widget.setStepRelationService(stepRelationService);

        //查询面板过滤
        String queryCdin = widget.getQuery();
        if (!Strings.isNullOrEmpty(queryCdin)) {
            JSONArray jsonArray = (JSONArray) JSON.parse(widget.getQuery());
            if (jsonArray == null) {
                return null;
            }
            StringJoiner stringJoiner = new StringJoiner(",");
            for (int i = 0; i < jsonArray.size(); i++) {
                String filterCdin = jsonArray.getJSONObject(i).getString("filter");
                if (!Strings.isNullOrEmpty(filterCdin)) {
                    stringJoiner.add(filterCdin);
                }
            }
            widgetFilters.addAll(widgetFilterBuilder.builder("[" + stringJoiner + "]"));
        }
        widget.setWidgetFilters(widgetFilters);
        String query = widget.getQuery();
        if (!StringUtils.isBlank(query)) {
            List<AbsConditionWidget> conditionWidgets = widgetConditionBuilder.builder(widget.getQuery());
            widget.setConditionWidgets(conditionWidgets);
        }
        return widget;
    }

    private void widgetSetTableCode(AbsChartWidget widget) {
        //为了适配以前方案表名是物理表名且没有带schemaName，导出模型后物理表必须带shcemaName才能查到表
        String datasetId = widget.getWidgetDataset().getDatasetId();
        ClassifierStat classifierStat = classifierStatService.getClassifierStat(ClassifierStat.class, datasetId);
        WidgetDataset widgetDataset = widget.getWidgetDataset();
        if("LogicDataObj".equalsIgnoreCase(classifierStat.getType())){
            //todo 临时解决旧的可视化方案的表名问题
            if(!classifierStat.getGlobalCode().startsWith("GB_")){
                widgetDataset.setTableCode(classifierStat.getGlobalCode());
            }
        }else{
            widgetDataset.setTableCode(classifierStat.getCode());
        }
    }

    @Override
    public Map<String, Object> loadingData(String code, String json, boolean type) {
        Map map = JSON.parseObject(json, Map.class);
        String dashboardId = (String) map.get("dashboardId");
        Dashboard dashboard = new Dashboard();
        if (StringUtils.isNotBlank(dashboardId)) {
            dashboard = dashBoardService.queryDashboardById(dashboardId, true);
        }
        if (null != dashboard && dashboard.getIsCache()) {
            dashboardBuilder.configScheduled(dashboard, code, json, type);
            log.debug("走缓存接口");
            Long beginTime = new Date().getTime();
            Map<String, Object> map1 = chartWidgetBuilder.loadingCacheData(code, json, type);
            Long totalConsumedTime = new Date().getTime() - beginTime;
            log.debug("走缓存耗时："+totalConsumedTime);
            return map1;
        }
        this.deleteCacheList(dashboardId);
        Long beginTime = new Date().getTime();
        log.debug("没有走缓存接口");
        Map<String, Object> map1 = chartWidgetBuilder.loadingWidgetData(code, json, type);
        Long totalConsumedTime = new Date().getTime() - beginTime;
        log.debug("不走缓存耗时："+totalConsumedTime);
        return map1;
    }

    public void deleteCacheList(String dashboardId) {
        if (StringUtils.isNotBlank(dashboardId)) {
            List<DashboardScheduled.TaskConstant> taskConstants = dashboardScheduled.getTaskConstants();
            Dashboard dashboard = dashBoardService.queryDashboardById(dashboardId, false);
            for (DashboardScheduled.TaskConstant taskConstant : taskConstants) {
                Map map = JSON.parseObject(taskConstant.getTaskJson(), Map.class);
                String dashboardId0 = (String) map.get("dashboardId");
                if (dashboardId.equals(dashboardId0)) {
                    dashboardBuilder.configScheduled(dashboard, taskConstant.getTaskCode(), taskConstant.getTaskJson(), taskConstant.isType());
                    chartWidgetBuilder.deleteCacheDashBoard(taskConstant.getTaskCode(), taskConstant.getTaskJson(), taskConstant.isType());
                }
            }
        }
    }

    public Map<String, Object> loadingWidgetData(String code, String json, boolean type) {
        AbsChartWidget widget = this.builderChartWidget(code, json);
        int mode;
        if (type) {
            mode = AbsChartWidget.NORMAL_MODE;
        } else {
            mode = AbsChartWidget.PREVIEW_MODE;
        }
        Map map = new HashMap();
        try {
            StopWatch stopWatch = new StopWatch("可视化查询");
            stopWatch.start(String.format("可视化方案组件:%s", code));
            map = widget.loadingData(dataSetBuilder, mode,code, timerClicker.getVisSecond());
            stopWatch.stop();
            if (stopWatch.getTotalTimeSeconds() >= timerClicker.getVisSecond()) {
                WidgetMeta widgetMeta = widgetMetaService.getMetaByCode(code);
                String sql = widget.getSQL(dataSetBuilder, mode);
                log.info("查询组件【{}】{}查询SQL【{}】{}耗时:{}秒{}", widgetMeta.getName(), FileUtil.getLineSeparator(),
                        sql, FileUtil.getLineSeparator(), stopWatch.getTotalTimeSeconds(), FileUtil.getLineSeparator());

            }
        }catch (IllegalLogicException ile){
            log.error("后端数据库查询异常 注意检查", ile);

            map.put("code", -2);
            map.put("msg", ile.getMessage());
        }catch (Exception e) {
            log.error("后端数据库查询异常 注意检查", e);

            map.put("code", -2);
            map.put("msg", "查询统计错误请检查配置信息或联系管理员");
        }
        return map;
    }

    @Override
    @Cacheable(cacheNames = {"absChartCacheWidget", "absChartCacheWidget"}, key = "#json")
    public Map<String, Object> loadingCacheData(String code, String json, boolean type) {
        try {
            log.debug("缓存开启中！-->");
            log.debug("第一次走缓存");
            Map<String, Object> widgetDataMap = this.loadingWidgetData(code, json, type);
            log.debug("缓存开启成功！-->");
            return widgetDataMap;
        } catch (Exception e) {
            log.debug("缓存开启失败！-->");
            log.error(e.getMessage(),e);
            return null;
        }
    }

    @CacheEvict(cacheNames = {"absChartCacheWidget", "absChartCacheWidget"}, key = "#json")
    public void deleteCacheDashBoard(String code, String json, boolean type) {
        log.info("删除缓存-->");
    }

    @Override
    public String getSQL(String code, String data, boolean type) {
        AbsChartWidget widget = this.builderChartWidget(code, data);
        int mode;
        if (type) {
            mode = AbsChartWidget.NORMAL_MODE;
        } else {
            mode = AbsChartWidget.PREVIEW_MODE;
        }
        String resSQL = widget.getSQL(dataSetBuilder, mode);
        return resSQL.replaceAll("`","\"");
    }
}
