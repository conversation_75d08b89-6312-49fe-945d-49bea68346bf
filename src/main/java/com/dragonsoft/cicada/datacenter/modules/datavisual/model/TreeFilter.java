package com.dragonsoft.cicada.datacenter.modules.datavisual.model;

import com.code.common.utils.StringUtils;
import com.code.meta.dml.standard.IMultCdin;
import com.code.meta.dml.standard.cdins.IQueryCdin;
import com.code.meta.dml.standard.cdins.MultCdin;
import com.code.meta.dml.standard.cdins.QueryCdins;

import java.util.ArrayList;
import java.util.List;

public class TreeFilter extends AbsWidgetFilter {
    List<values> values;

    @Override
    public IMultCdin builderCondition(QueryCdins queryCdins) {
        IMultCdin c = new MultCdin();
        List<IQueryCdin> list = new ArrayList<>();
        String fieldCode = StringUtils.isBlank(this.field.getFieldAlias()) ? this.field.getCode() : this.field.getFieldAlias();

        values.forEach(n -> {
            list.add(queryCdins.eq(fieldCode , n.getName()));
            list.add(queryCdins.eq(fieldCode, n.getCode()));
        });
        c.ors(list.toArray(new IQueryCdin[list.size()]));
        return c;
    }

    public List<TreeFilter.values> getValues() {
        return values;
    }

    public void setValues(List<TreeFilter.values> values) {
        this.values = values;
    }

    class values {
        String code;
        String name;

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }
}
