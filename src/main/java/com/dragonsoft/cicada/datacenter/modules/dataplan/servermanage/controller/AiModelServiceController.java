package com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.code.common.utils.R;
import com.code.common.utils.StringUtils;
import com.code.common.utils.assertion.Assert;
import com.code.metadata.sm.EnumServiceType;
import com.code.metadata.sm.ServiceMeta;
import com.code.metaservice.sm.IServiceMetaService;
import com.dragoninfo.dfw.bean.Result;
import com.dragonsoft.cicada.datacenter.ServicePublishCenterClient;
import com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.service.IAiServicePublishService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.service.IServicePublicService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.vo.ServicePublicationVo;
import com.dragonsoft.cicada.datacenter.modules.modeling.aimodeling.service.AiDataModelingService;
import com.dragonsoft.cicada.datacenter.modules.modeling.aimodeling.vo.ModelTreeResult;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;
import org.springframework.cloud.netflix.feign.EnableFeignClients;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpSession;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * ai建模服务发布
 */
@RestController
@RequestMapping("/ai/publish")
@Slf4j
@CrossOrigin
// 添加注解声明是注册中心客户端
@EnableEurekaClient
// 实现不同子服务调用
@EnableFeignClients
public class AiModelServiceController {

    @Autowired
    private IAiServicePublishService aiServicePublishService;

    @Resource
    private ServicePublishCenterClient servicePublishCenterClient;

    @Autowired
    private IServiceMetaService serviceMetaService;

    @Autowired
    private AiDataModelingService aiDataModelingService;

    @Autowired
    private IServicePublicService servicePublicService;



    @GetMapping("/getLogAndParams")
    public Result getLogAndParams(String scriptId){
        JSONArray logIdAndParams = aiServicePublishService.getLogIdAndParams(scriptId);
        return  Result.toResult(R.ok(logIdAndParams));
    }

    @PostMapping("/publishAiService")
    /*@FuncScanAnnotation(code = "AIModelingBuildApi", name = "生成API", parentCode = "AIModeling")
    @ValidateAndLogAnnotation*/
    public Result publishAiService(@RequestBody ServicePublicationVo servicePublicationVo, HttpSession session){
        String userId = (String) session.getAttribute("userId");
        servicePublicService.checkPublishName("",servicePublicationVo.getApiName(),false, EnumServiceType.ALGORITHM_SERVICE.getCode(),userId);
        Map<String, Object> map = aiServicePublishService.publishTestService(servicePublicationVo, userId);
        ServicePublishCenterClient.ServicePublishVo servicePublishVo = (ServicePublishCenterClient.ServicePublishVo) map.get("servicePublishVo");
        String code = (String) map.get("code");
        String publish = servicePublishCenterClient.publish(servicePublishVo);
        Map map1 = aiServicePublishService.updateRequestPath(publish, code);
        servicePublishCenterClient.export(publish);
        String serviceMetaId = (String) map1.get("serviceMetaId");
        servicePublicService.saveElementAndClassifyByMetaId(serviceMetaId,servicePublicationVo.getClassifyId());
        return  Result.toResult(R.ok(map1));
    }

    @PostMapping("/updateAiService")
   /* @FuncScanAnnotation(code = "AIModelingUpdateApi", name = "更新api", parentCode = "AIModeling")
    @ValidateAndLogAnnotation*/
    public Result updateAiService(@RequestBody ServicePublicationVo servicePublicationVo, HttpSession session) throws InterruptedException {
        String userId = (String) session.getAttribute("userId");
        if (StringUtils.isBlank(servicePublicationVo.getServiceId())){
            Assert.fail("serviceId不能为空!");
        }
        ServiceMeta serviceMeta = serviceMetaService.queryDefault(servicePublicationVo.getServiceId());
        String status = serviceMeta.getStatus();
        String serviceMetaId = serviceMeta.getId();
        //1是启用
        /*if (!"1".equals(status)) {
            // 先启用 在删除
            servicePublicService.redistributionService(serviceMeta.getId());
            Thread.sleep(500);

        }*/
        servicePublicService.checkPublishName(serviceMeta.getId(),servicePublicationVo.getApiName(),true, EnumServiceType.ALGORITHM_SERVICE.getCode(),userId);
        servicePublishCenterClient.uninstall(serviceMeta.getId());
        Map<String, Object> map = aiServicePublishService.publishTestService(servicePublicationVo, userId);
        ServicePublishCenterClient.ServicePublishVo servicePublishVo = (ServicePublishCenterClient.ServicePublishVo) map.get("servicePublishVo");
        String code = (String) map.get("code");
        String publish = servicePublishCenterClient.publish(servicePublishVo);
        Map map1 = aiServicePublishService.updateRequestPath(publish, code);
        servicePublishCenterClient.export(publish);
        servicePublicService.updateElementAndClassifyByMetaId(publish,servicePublicationVo.getClassifyId());

        //再删一次 如果未启用状态 卸载的时候数据没有删除成功
        aiServicePublishService.deleteServiceMetaById(serviceMetaId);
        return  Result.toResult(R.ok(map1));
    }

    @PostMapping("/testAiService")
   /* @FuncScanAnnotation(code = "AIModelingTestApi", name = "测试API", parentCode = "AIModeling")
    @ValidateAndLogAnnotation*/
    public Result testAiService(String serviceId,@RequestBody Map<String,Object> requestParams){
        Map map = aiServicePublishService.testAiService(serviceId, requestParams);
        return  Result.toResult(R.ok(map));
    }

    /**
     * 卸载
     * @param serviceMetaId
     * @return
     */
    @GetMapping("/unInstall")
    public Result unInstall(String serviceMetaId){
        servicePublicService.deleteElementAndClassifyByMetaId(serviceMetaId);
        servicePublishCenterClient.uninstall(serviceMetaId);
        return  Result.toResult(R.ok());
    }

    /**
     * 下线
     * @param serviceMetaId
     * @return
     */
    @GetMapping("/offLine")
    public Result offLine(String serviceMetaId){
        servicePublishCenterClient.offlineExport(serviceMetaId);
        return  Result.toResult(R.ok());
    }

    /**
     * 上线
     * @param serviceMetaId
     * @return
     */
    @GetMapping("/export")
    public Result export(String serviceMetaId){
        servicePublishCenterClient.export(serviceMetaId);
        return  Result.toResult(R.ok());
    }


    @GetMapping("/getLogIdTree")
    public Result getLogIdTree(HttpSession session){
        String userId = (String) session.getAttribute("userId");
        if (StringUtils.isBlank(userId)){
            Assert.fail("请先登录！");
        }
        List<ModelTreeResult> results = new ArrayList<>();
        try {
            List<ModelTreeResult> busiClassifies = aiDataModelingService.getAllAiModelWithChildren(userId, "time");
            results = setModelServiceTree(busiClassifies, userId);
        }catch (Exception e) {
            log.error("获取训练版本号异常，请检查连接！");
        }

        return  Result.toResult(R.ok(results));
    }

    private List<ModelTreeResult> setModelServiceTree(List<ModelTreeResult> modelTreeResults,String userId){
        List<ModelTreeResult> results = Lists.newArrayList();
        for (ModelTreeResult modelTreeResult : modelTreeResults) {
            List<ModelTreeResult> children = modelTreeResult.getChildren();
            if (children != null && children.size() > 0 ){
                modelTreeResult.setChildren(setModelServiceTree(children,userId));
            }else if (!modelTreeResult.getIsParent()){
                modelTreeResult.setIsParent(true);
                String id = modelTreeResult.getId();
                JSONArray logs = aiServicePublishService.getLogIdAndParams(id);
                if (logs.size() > 0){
                    List<ModelTreeResult> services = Lists.newArrayList();
                    for (Object objectMap : logs) {
                        JSONObject log = (JSONObject) objectMap;
                        ModelTreeResult service = new ModelTreeResult();
                        service.setChildren(null);
                        service.setIsParent(false);
                        service.setId((String) log.get("log_id"));
                        service.setName((String) log.get("practiceVersion"));
                        service.setOperateTime(JSONObject.toJSONString((JSONObject) log.get("log_content")));
                        service.setIsService(true);
                        service.setParentId(modelTreeResult.getId());
                        services.add(service);
                    }
                    modelTreeResult.setChildren(services);
                }



            }
            results.add(modelTreeResult);
        }
        return results;
    }
}
