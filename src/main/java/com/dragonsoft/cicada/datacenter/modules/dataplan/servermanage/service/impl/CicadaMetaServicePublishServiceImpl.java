package com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.code.common.mist.service.structure.model.*;
import com.code.common.utils.assertion.Assert;
import com.code.metadata.etl.trans.TransMeta;
import com.code.metadata.sm.EnumServiceParamDesensitization;
import com.code.metadata.sm.EnumServiceType;
import com.code.metadata.sm.ServiceMeta;
import com.code.metadata.sm.ServicePublication;
import com.code.metaservice.etl.trans.TransMetaService;
import com.code.metaservice.sm.IServiceMetaService;
import com.code.mist.builder.service.dubbo.ITransformApiService;
import com.dragoninfo.dfw.bean.Result;
import com.dragonsoft.cicada.datacenter.ServicePublishCenterClient;
import com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.service.DynamicStructureService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.service.ICicadaMetaServicePublishService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.service.IServicePublicService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.utils.ClientAccesstIpUtil;
import com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.utils.ServicePusblishUtil;
import com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.vo.ModelServiceRequestVo;
import com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.vo.ParamConfigVo;
import com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.vo.SubscribeParamConfigVo;
import com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.vo.TransStepInputVo;
import com.dragonsoft.cicada.datacenter.modules.modeling.service.DataModelingService;
import com.fw.service.BaseService;
import com.fw.service.annotation.Service;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import okio.BufferedSink;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CicadaMetaServicePublishServiceImpl extends BaseService implements ICicadaMetaServicePublishService {

    private static final String BASIC_PACK_PATH = "com.code.common.sm";


    @Autowired
    private TransMetaService transMetaService;

    @Autowired
    private IServiceMetaService serviceMetaService;

    @Value("${ai.dir.basicPath}")
    //@Value("${plugin.base.dir}")
    private String publishPath;

    @Value("${dc.publish.path}")
    private String testPublishPath;

    @Value("${service.publish.time}")
    private long servicePublishTime;

    @Autowired
    private IServicePublicService servicePublicServiceImpl;

    @Autowired
    private ServicePublishCenterClient servicePublishCenterClient;

    @Autowired
    private DataModelingService dataModelingService;

    @Autowired
    private ITransformApiService transformApiService;

    @Autowired
    private DynamicStructureService dynamicStructureService;

    @Override
    public Result createService(SubscribeParamConfigVo paramConfigVo, String userId) {
        checkCicadaMetaServiceVo(paramConfigVo, userId);

        ServicePublishCenterClient.ServicePublishVo modelServicePublishVo = getModelServicePublishVo(paramConfigVo, userId);
        String serviceMetaId = servicePublishCenterClient.publish(modelServicePublishVo);

        ServiceMeta serviceMeta = serviceMetaService.get(ServiceMeta.class, serviceMetaId);
        Assert.notNull(serviceMeta, "服务发布失败");
        ServicePublication servicePublication = serviceMeta.getServicePublication();

        String id = servicePublication.getId();
        String classifyId = paramConfigVo.getClassifyId();
        Assert.hasLength(classifyId, "目录id不能为空！");
        servicePublicServiceImpl.saveElementAndClassify(id, classifyId);

        return Result.success(serviceMeta.getServicePublication().getId());
    }

    @Override
    public Result publishModelServiceCreateJar(SubscribeParamConfigVo paramConfigVo, String userId)  {
        ServicePublishCenterClient.ServicePublishVo modelServicePublishVo = getModelServicePublishVo(paramConfigVo, userId);
        String serviceMetaId = servicePublishCenterClient.publishWithoutMeta(modelServicePublishVo);
        ServiceMeta serviceMeta = serviceMetaService.get(ServiceMeta.class, serviceMetaId);
        String id = serviceMeta.getServicePublication().getId();
        return Result.success(id);
    }

    private ServicePublishCenterClient.ServicePublishVo getModelServicePublishVo(SubscribeParamConfigVo paramConfigVo, String userId){
        //检查输入参数

        if (StringUtils.isBlank(paramConfigVo.getServiceMetaId())) {
            paramConfigVo.setInterfaceEnglishName(ServicePusblishUtil.buildImplClassName(paramConfigVo.getInterfaceChineseName()));
            paramConfigVo.setImplEnglishName(ServicePusblishUtil.buildImplClassName(paramConfigVo.getImplChineseName()));
        } else {
            paramConfigVo.setImplEnglishName(ServicePusblishUtil.getOldImplClassName(paramConfigVo.getImplEnglishName()));
        }
        //生成serviceClassMeta
        ServiceClassMeta serviceClassMeta = this.dynamicCreate(paramConfigVo, userId);
        //检查服务是否已存在
        serviceClassMeta.setUserId(userId);
        serviceClassMeta.setServiceMetaId(paramConfigVo.getServiceMetaId());
        serviceClassMeta.setServiceType(EnumServiceType.getInstanceByCode(paramConfigVo.getServiceType()));
        if(serviceClassMeta.getServiceType() == null){
            serviceClassMeta.setServiceType(EnumServiceType.getServiceTypeByName(paramConfigVo.getServiceType()));
        }        serviceClassMeta.setServicePublicationId(paramConfigVo.getServiceId());
        ServicePublishCenterClient.ServicePublishVo servicePublishVo = new ServicePublishCenterClient.ServicePublishVo();
        servicePublishVo.setServiceClassMeta(serviceClassMeta);
        servicePublishVo.setFilePath(ServicePusblishUtil.buildFilepath(publishPath, paramConfigVo.getInterfaceEnglishName(), paramConfigVo.getImplVersion()));
        return servicePublishVo;
    }

    @Override
    public Result updateService(SubscribeParamConfigVo paramConfigVo, String userId) {
        paramConfigVo.setImplEnglishName(
                replaceName(paramConfigVo.getInterfaceEnglishName(), paramConfigVo.getImplEnglishName()).toLowerCase(Locale.ROOT)
        );
        ServiceClassMeta serviceClassMeta = this.dynamicCreate(paramConfigVo, userId);
        serviceClassMeta.setServiceMetaId(paramConfigVo.getServiceMetaId());
        ClassMeta implMeta = serviceClassMeta.getImplMeta();
        implMeta.setId(paramConfigVo.getServiceMetaId());
        //去掉多余的下划线
//        rename(serviceClassMeta.getInterfaceMeta(), implMeta);
        serviceClassMeta.setServicePublicationId(paramConfigVo.getServicePublicationId());
        serviceClassMeta.getInterfaceMeta().setId(paramConfigVo.getServicePublicationId());
        ServicePublishCenterClient.ServicePublishVo servicePublishVo = new ServicePublishCenterClient.ServicePublishVo();
        servicePublishVo.setServiceClassMeta(serviceClassMeta);
        servicePublishVo.setFilePath(ServicePusblishUtil.buildFilepath(publishPath, paramConfigVo.getInterfaceEnglishName(), paramConfigVo.getImplVersion()));
        servicePublishCenterClient.update(servicePublishVo);
        return Result.success(serviceClassMeta.getServicePublicationId());
    }

    @Override
    public Result updateQueryService(ParamConfigVo paramConfigVo, String userId) throws Exception {

        paramConfigVo.setImplEnglishName(
                replaceName(paramConfigVo.getInterfaceEnglishName(), paramConfigVo.getImplEnglishName()).toLowerCase(Locale.ROOT)
        );
        ServiceClassMeta serviceClassMeta = dynamicStructureService.dynamicQueryCreateClassMetaByVo(paramConfigVo,userId);
        serviceClassMeta.setServiceMetaId(paramConfigVo.getServiceMetaId());
        ClassMeta implMeta = serviceClassMeta.getImplMeta();
        implMeta.setId(paramConfigVo.getServiceMetaId());
        serviceClassMeta.setUserId(userId);

        serviceClassMeta.setServicePublicationId(paramConfigVo.getServiceId());
        serviceClassMeta.getInterfaceMeta().setId(paramConfigVo.getServiceId());
        ServicePublishCenterClient.ServicePublishVo servicePublishVo = new ServicePublishCenterClient.ServicePublishVo();
        servicePublishVo.setServiceClassMeta(serviceClassMeta);
        servicePublishVo.setFilePath(ServicePusblishUtil.buildFilepath(publishPath, paramConfigVo.getInterfaceEnglishName(), paramConfigVo.getImplVersion()));
        servicePublishCenterClient.update(servicePublishVo);

        return Result.success(serviceClassMeta.getServicePublicationId());
    }

    @Override
    public Result updateVerifactionService(ParamConfigVo paramConfigVo, String userId) throws Exception {
        paramConfigVo.setImplEnglishName(
                replaceName(paramConfigVo.getInterfaceEnglishName(), paramConfigVo.getImplEnglishName()).toLowerCase(Locale.ROOT)
        );
        ServiceClassMeta serviceClassMeta = dynamicStructureService.dynamicVerifcationCreateClassMetaByVo(paramConfigVo,userId);
        serviceClassMeta.setUserId(userId);
        serviceClassMeta.setServiceMetaId(paramConfigVo.getServiceMetaId());
        ClassMeta implMeta = serviceClassMeta.getImplMeta();
        implMeta.setId(paramConfigVo.getServiceMetaId());
        serviceClassMeta.setUserId(userId);

        serviceClassMeta.setServicePublicationId(paramConfigVo.getServiceId());
        serviceClassMeta.getInterfaceMeta().setId(paramConfigVo.getServiceId());
        ServicePublishCenterClient.ServicePublishVo servicePublishVo = new ServicePublishCenterClient.ServicePublishVo();
        servicePublishVo.setServiceClassMeta(serviceClassMeta);
        servicePublishVo.setFilePath(ServicePusblishUtil.buildFilepath(publishPath, paramConfigVo.getInterfaceEnglishName(), paramConfigVo.getImplVersion()));
        servicePublishCenterClient.update(servicePublishVo);
        return Result.success(serviceClassMeta.getServicePublicationId());
    }

    @Override
    public Result updateNoPublishService(ParamConfigVo paramConfigVo, String userId) throws Exception {
        paramConfigVo.setImplEnglishName(
                replaceName(paramConfigVo.getInterfaceEnglishName(), paramConfigVo.getImplEnglishName()).toLowerCase(Locale.ROOT)
        );
        ServiceClassMeta serviceClassMeta=new ServiceClassMeta();
        if("6".equals(paramConfigVo.getServiceType())){
            serviceClassMeta = dynamicStructureService.dynamicVerifcationCreateClassMetaByVo(paramConfigVo,userId);
        }else{
            serviceClassMeta = dynamicStructureService.dynamicQueryCreateClassMetaByVo(paramConfigVo,userId);
        }
        serviceClassMeta.setUserId(userId);
        serviceClassMeta.setServiceMetaId(paramConfigVo.getServiceMetaId());
        ClassMeta implMeta = serviceClassMeta.getImplMeta();
        implMeta.setId(paramConfigVo.getServiceMetaId());
        serviceClassMeta.setUserId(userId);

        serviceClassMeta.setServicePublicationId(paramConfigVo.getServiceId());
        serviceClassMeta.getInterfaceMeta().setId(paramConfigVo.getServiceId());
        ServicePublishCenterClient.ServicePublishVo servicePublishVo = new ServicePublishCenterClient.ServicePublishVo();
        servicePublishVo.setServiceClassMeta(serviceClassMeta);
        servicePublishVo.setFilePath(ServicePusblishUtil.buildFilepath(publishPath, paramConfigVo.getInterfaceEnglishName(), paramConfigVo.getImplVersion()));
        servicePublishCenterClient.update(servicePublishVo);
        return Result.success(serviceClassMeta.getServicePublicationId());
    }

    private void rename(ClassMeta interfaceMeta, ClassMeta implMeta) {
        String interfaceMetaName = interfaceMeta.getName();
        String interfaceMetaPackageName = interfaceMeta.getPackageName();
        String implMetaName = implMeta.getName();
        String implMetaPackageName = implMeta.getPackageName();
        implMeta.setPackageName(replacePackageName(interfaceMetaPackageName, implMetaPackageName));
        implMeta.setName(replaceName(interfaceMetaName, implMetaName));
    }

    private String replacePackageName(String interfaceMetaPackageName, String implMetaPackageName) {
        if (implMetaPackageName.contains(interfaceMetaPackageName)) {
            String packageName = implMetaPackageName.substring(0, implMetaPackageName.lastIndexOf("."))
                    + "."
                    + implMetaPackageName.replace(interfaceMetaPackageName + "_", "");
            return packageName;
        }
        return implMetaPackageName;
    }

    private String replaceName(String interfaceMetaName, String implMetaName) {
        if (implMetaName.contains(interfaceMetaName)) {
            String packageName = implMetaName.replace(interfaceMetaName + "_", "");
            packageName = packageName.substring(0, 1).toUpperCase(Locale.ROOT) + packageName.substring(1);
            return packageName;
        }
        return implMetaName;
    }

    public void checkCicadaMetaServiceVo(ParamConfigVo paramConfigVo, String userId) {
        Assert.notNull(paramConfigVo, "API发布请求参数不能为空");
        Assert.notNull(paramConfigVo.getInterfaceChineseName(), "API名称不能为空");
        Assert.notNull(paramConfigVo.getImplVersion(), "版本号不能为空");
        Assert.notNull(paramConfigVo.getModelId(), "模型ID不能为空");
        Assert.isTrue(CollectionUtils.isNotEmpty(paramConfigVo.getParamList()), "请求参数不能为空");
        Assert.isTrue(CollectionUtils.isNotEmpty(paramConfigVo.getGinsengList()), "返回参数不能为空");

        TransMeta transMeta = this.transMetaService.getTransMetaById(paramConfigVo.getModelId());
        Assert.notNull(transMeta, "插件元信息不存在");
        Set<TransMeta> children = transMeta.getChildren();
        Assert.notEmpty(children, "子插件信息不存在");
        Map<String, String> pluginCodeMap = children.stream().collect(Collectors.toMap(child -> child.getUsedPlugin().getCode(), child -> child.getUsedPlugin().getCode(), (k1, k2) -> k1));
        if (pluginCodeMap.get("cicadaMetaServiceInput") == null || (pluginCodeMap.get("cicadaMetaServiceOutput") == null && pluginCodeMap.get("cicadaMetaServiceCheckOutPut") == null)) {
            Assert.fail("模型必须包含一个服务输入和服务输出插件");
        }
        if (!"update".equals(paramConfigVo.getSaveOrUpdate())) {
            servicePublicServiceImpl.checkChineseName(paramConfigVo, userId);
        }
    }

    @Override
    public ServiceClassMeta dynamicCreateClassMetaByVo(ParamConfigVo paramConfigVo, String userId) {
        EnumServiceType serviceType = EnumServiceType.getInstanceByCode(paramConfigVo.getServiceType());
        ServiceClassMeta serviceClassMeta = new ServiceClassMeta();
        serviceClassMeta.setImplMeta(this.builderImpl(paramConfigVo));
        serviceClassMeta.setInterfaceMeta(this.builderInterface(paramConfigVo));
        serviceClassMeta.setServiceType(serviceType);
        serviceClassMeta.setSourceId(paramConfigVo.getModelId());
        serviceClassMeta.setMemo(paramConfigVo.getMemo());
        serviceClassMeta.setUserId(userId);
        serviceClassMeta.setSaveType(paramConfigVo.getSaveType());

        return serviceClassMeta;
    }


    private ServiceClassMeta dynamicCreate(SubscribeParamConfigVo paramConfigVo, String userId){
        EnumServiceType serviceType = EnumServiceType.getInstanceByCode(paramConfigVo.getServiceType());

        ServiceClassMeta serviceClassMeta = dynamicCreateClassMetaByVo(paramConfigVo, userId);
        ServiceClassMeta.ResourceConfigMeta resourceConfigMeta = new ServiceClassMeta.ResourceConfigMeta();
        resourceConfigMeta.setCompareType(paramConfigVo.getCompareType());
        resourceConfigMeta.setIncrementalColumn(paramConfigVo.getIncrementalColumn());
        resourceConfigMeta.setIncrementalStepId(paramConfigVo.getIncrementalStepId());
        resourceConfigMeta.setIncrementalZhColumn(paramConfigVo.getIncrementalZhColumn());
        serviceClassMeta.setResourceConfigMeta(resourceConfigMeta);
        serviceClassMeta.setSingleOrManyTable(paramConfigVo.getSingleOrManyTable());
        serviceClassMeta.setServiceType(serviceType);
        serviceClassMeta.setSourceId(paramConfigVo.getModelId());
        serviceClassMeta.setMemo(paramConfigVo.getMemo());
        serviceClassMeta.setClassifyId(paramConfigVo.getClassifyId());
        serviceClassMeta.setResourceIds(paramConfigVo.getResourceIds());
        serviceClassMeta.setSaveType(paramConfigVo.getSaveType());
        return serviceClassMeta;
    }

    private ClassMeta builderImpl(ParamConfigVo paramConfigVo) {
        ClassMeta implMeta = ClassMeta.builder()
                .packageName(BASIC_PACK_PATH + "." + StrUtil.lowerFirst(paramConfigVo.getImplEnglishName()))
                .name(StrUtil.upperFirst(paramConfigVo.getImplEnglishName()))
                .superClass("com.code.common.mist.service.BaseSchemeService")
                .interfaces(new String[]{BASIC_PACK_PATH + "." + StrUtil.lowerFirst(paramConfigVo.getInterfaceEnglishName()) + "." + StrUtil.upperFirst(paramConfigVo.getInterfaceEnglishName()), "java.io.Serializable"})
                .addConstructor(buildConstructorMeta(paramConfigVo.getModelId()))
                .urlPath("/cicada/service")
                .access(AccessFlag.PUBLIC)
                .isInterface(false)
                .version(paramConfigVo.getImplVersion())
                .promulgator(EnumPromulgator.DATA_CENTER)
                .memo(paramConfigVo.getInterfaceChineseName())
                .build();

        MethodMeta.Builder methodBuilder = builderMethod(paramConfigVo);
               /* .annotations(buildSensitiveAnnotations(paramConfigVo))
                .addAnnotation(new AnnotationMeta("java.lang.Override"))
                ;*/
        List<AnnotationMeta> annotationMetas = buildSensitiveAnnotations(paramConfigVo);
        if (CollectionUtil.isNotEmpty(annotationMetas)) {
            methodBuilder.annotations(annotationMetas);
        }
        methodBuilder.addAnnotation(new AnnotationMeta("java.lang.Override"));
        methodBuilder.body("{" +
                "System.out.println($1);" +
                "return this.action($1);" +
                //"return null;" +
                "}");

        implMeta.addMethod(methodBuilder.build());

        MethodMeta.Builder getValue = builderGetValueMethod(paramConfigVo);
               /* .annotations(buildSensitiveAnnotations(paramConfigVo))
                .addAnnotation(new AnnotationMeta("java.lang.Override"))
                ;*/
        if (CollectionUtil.isNotEmpty(annotationMetas)) {
            getValue.annotations(annotationMetas);
        }
        getValue.addAnnotation(new AnnotationMeta("java.lang.Override"));
        getValue.addAnnotation(new AnnotationMeta("com.fw.tenon.annotation.PublishServiceMethod"));
        getValue.body("{" +
                "return super.getValue($1, $2, $3, $4);" +
                "}");
        implMeta.addMethod(getValue.build());
        return implMeta;
    }

    private ClassMeta builderInterface(ParamConfigVo paramConfigVo) {
        ClassMeta interfaceMeta = ClassMeta.builder()
                .packageName(BASIC_PACK_PATH + "." + StrUtil.lowerFirst(paramConfigVo.getInterfaceEnglishName()))
                .name(StrUtil.upperFirst(paramConfigVo.getInterfaceEnglishName()))
                .isInterface(true)
                .version(paramConfigVo.getImplVersion())
                .access(AccessFlag.PUBLIC)
                .memo(paramConfigVo.getInterfaceChineseName())
                .build();
        MethodMeta methodMeta = builderMethod(paramConfigVo).build();
        interfaceMeta.addMethod(methodMeta);

      /*  MethodMeta getValue = builderGetValueMethod(paramConfigVo).build();
        interfaceMeta.addMethod(getValue);*/
        return interfaceMeta;
    }

    /**
     * @Desensitization(fields = {
     * @FieldDesensitization(field = "address", sensitiveStrategy = SensitiveStrategyEnum.ADDRESS),
     * @FieldDesensitization(field = "chineseName", sensitiveStrategy = SensitiveStrategyEnum.CHINESE_NAME)
     * })
     * @Override public SchemeExecutorResult getValue(String requestId, Integer pageIndex, Integer pageSize) {
     * return super.getValue(requestId, pageIndex, pageSize);
     * }
     */
    private MethodMeta.Builder builderGetValueMethod(ParamConfigVo paramConfigVo) {
        String returnType = "com.code.common.mist.service.model.SchemeExecutorResult";
        MethodMeta.Builder methodBuilder = MethodMeta.builder();
        methodBuilder.name("getValue")
                .isPublish(true)
                /* .urlPath("/" + paramConfigVo.getImplEnglishName())*/
                .access(AccessFlag.PUBLIC)
        /*.requestType(RequestType.POST)*/
              /*  .parameterConsumerTypeEnum(MethodMeta.ParameterRequestTypeEnum.JSON)
                .returnProduceTypeEnum(MethodMeta.ParameterRequestTypeEnum.JSON)
                .ruleEngineAcceptTypes(new String[]{String.class.getName(), Long.class.getName()})*/
        //.addParameter(0, new Parameter(param1Type, "schemeServiceRequestModel", "memo"))
        //.returnParams(Arrays.asList(new MethodMeta.ReturnParam("result", "result", returnType)))
        ;
        //请求参数
        ParameterBuilder requestId = ParameterBuilder.aParameter();
        requestId.name("requestId").code("requestId").memo("requestId").dataType("java.lang.String");
        methodBuilder.addParameter(0, requestId.buildInputParameter());

        ParameterBuilder insertTime = ParameterBuilder.aParameter();
        insertTime.name("insertTime").code("insertTime").memo("insertTime").dataType("java.lang.String");
        methodBuilder.addParameter(1, insertTime.buildInputParameter());

        ParameterBuilder pageIndex = ParameterBuilder.aParameter();
        pageIndex.name("pageIndex").code("pageIndex").memo("pageIndex").dataType("java.lang.Integer");
        methodBuilder.addParameter(2, pageIndex.buildInputParameter());

        ParameterBuilder pageSize = ParameterBuilder.aParameter();
        pageSize.name("pageSize").code("pageSize").memo("pageSize").dataType("java.lang.Integer");
        methodBuilder.addParameter(3, pageSize.buildInputParameter());
        //返回参数
        ParameterBuilder responseParamBuilder = ParameterBuilder.aParameter();
        responseParamBuilder.name("schemeExecutorResult").code("schemeExecutorResult").memo("服务响应参数").dataType(returnType);
        buildParams(responseParamBuilder, paramConfigVo.getGinsengList());
        methodBuilder.addReturnParam(responseParamBuilder.buildReturnParam());
        methodBuilder.returnType(returnType);
        return methodBuilder;
    }


    private MethodMeta.Builder builderMethod(ParamConfigVo paramConfigVo) {
        String param1Type = "com.code.common.mist.service.model.SchemeServiceRequestModel";
        String returnType = "com.code.common.mist.service.model.SchemeExecutorResult";

        MethodMeta.Builder methodBuilder = MethodMeta.builder();
        methodBuilder.name(StrUtil.lowerFirst(paramConfigVo.getImplEnglishName()))
                .isPublish(true)
                .urlPath("/" + paramConfigVo.getImplEnglishName())
                .access(AccessFlag.PUBLIC)
                .requestType(RequestType.POST)
                .parameterConsumerTypeEnum(MethodMeta.ParameterRequestTypeEnum.JSON)
                .returnProduceTypeEnum(MethodMeta.ParameterRequestTypeEnum.JSON)
                .ruleEngineAcceptTypes(new String[]{String.class.getName(), Long.class.getName()})
        //.addParameter(0, new Parameter(param1Type, "schemeServiceRequestModel", "memo"))
        //.returnParams(Arrays.asList(new MethodMeta.ReturnParam("result", "result", returnType)))
        ;
        //请求参数
        ParameterBuilder requestParamBuilder = ParameterBuilder.aParameter();
        requestParamBuilder.name("schemeServiceRequestModel").code("schemeServiceRequestModel").memo("服务请求参数").dataType(param1Type);
        buildParams(requestParamBuilder, paramConfigVo.getParamList());
        methodBuilder.addParameter(0, requestParamBuilder.buildInputParameter());
        //返回参数
        ParameterBuilder responseParamBuilder = ParameterBuilder.aParameter();
        responseParamBuilder.name("schemeExecutorResult").code("schemeExecutorResult").memo("服务响应参数").dataType(returnType);
        buildParams(responseParamBuilder, paramConfigVo.getGinsengList());
        methodBuilder.addReturnParam(responseParamBuilder.buildReturnParam());
        methodBuilder.returnType(returnType);

        return methodBuilder;
    }

    private void buildParams(ParameterBuilder parameterBuilder, List<ParamConfigVo.Param> params) {
        if (CollectionUtils.isEmpty(params)) {
            return;
        }
        for (ParamConfigVo.Param param : params) {
            ParameterBuilder childParamBuilder = ParameterBuilder.aParameter();
            parameterBuilder.addChildren(childParamBuilder.name(param.getParamName()).code(param.getParamCode()).dataType(ServicePusblishUtil.changeTypePath(param.getType()))
                    .memo(param.getMemo()).example(param.getExample()).isMust(param.getIsMust()).desenType(param.getDesensitization()));
            buildParams(childParamBuilder, param.getChildren());
        }
    }

    //脱敏注解
    @Override
    public List<AnnotationMeta> buildSensitiveAnnotations(ParamConfigVo paramConfigVo) {
        List<ParamConfigVo.Param> ginsengList = paramConfigVo.getGinsengList();
        List<ParamConfigVo.Param> children = new ArrayList<>();

        if (!"4".equals(paramConfigVo.getServiceType()) && !"6".equals(paramConfigVo.getServiceType())) {
            List<ParamConfigVo.Param> operatorDataSets = ginsengList.stream().filter(g -> g.getParamCode().equals("operatorDataSet")).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(operatorDataSets)) {
                return Collections.emptyList();
            }
            ParamConfigVo.Param operatorDataSet = operatorDataSets.get(0);
            children = operatorDataSet.getChildren();
        } else {
            children = ginsengList;
        }

        AnnotationMeta desensitizationAnno = new AnnotationMeta("com.fw.tenon.desensitization.annotation.Desensitization");
        for (ParamConfigVo.Param child : children) {
            String desensitization = child.getDesensitization();
            if(StringUtils.isBlank(desensitization)) continue;
            if (desensitization.equals(EnumServiceParamDesensitization.No_DESENSITIZATION.getCode())) {
                continue;
            }
            String classValue = EnumServiceParamDesensitization.getInstanceByCode(desensitization).getClassPath();
            AnnotationMeta fieldDesensitization = getAnnotationMeta(child.getParamCode(), classValue);
            desensitizationAnno.addValueMeta("fields", fieldDesensitization);
        }
        if (CollectionUtil.isEmpty(desensitizationAnno.getAnnotationMemberMeta())) {
            return Collections.emptyList();
        }
        return new ArrayList<>(Arrays.asList(desensitizationAnno));
    }



    private AnnotationMeta getAnnotationMeta(String field, String value) {
        AnnotationMeta fieldDesensitizationAddress =
                new AnnotationMeta(field, "com.fw.tenon.desensitization.annotation.FieldDesensitization");
        //脱敏-字段address-field
        AnnotationMeta.MemberValueMeta desensitizationAddressField = new AnnotationMeta.MemberValueMeta(
                "field", "java.lang.String", field);
        //脱敏-字段address-sensitiveStrategy
        AnnotationMeta.MemberValueMeta desensitizationAddressSensitiveStrategy = new AnnotationMeta.MemberValueMeta(
                "sensitiveStrategy", "enum", value);
        fieldDesensitizationAddress.addValueMeta(desensitizationAddressField);
        fieldDesensitizationAddress.addValueMeta(desensitizationAddressSensitiveStrategy);
        return fieldDesensitizationAddress;
    }

    private ConstructorMeta buildConstructorMeta(String transId) {
        ConstructorMeta.Builder builder = new ConstructorMeta.Builder();
        builder.access(AccessFlag.PUBLIC)
                .parameters(Collections.emptyMap())
                .body("{" +
                        "schemeId = \"" + transId + "\";" +
                        "}");
        return builder.build();
    }


    @Override
    public Result testService(ModelServiceRequestVo requestVo, HttpServletRequest servletRequest) throws UnsupportedEncodingException {
        Assert.notNull(requestVo, "请求参数不能为空");
        Assert.notNull(requestVo.getServiceMetaId(), "服务ID不能为空");
        Assert.notNull(requestVo.getTestDataJson(), "测试数据json不能为空");
        ServiceMeta serviceMeta = serviceMetaService.queryByMetaId(requestVo.getServiceMetaId());
        Assert.notNull(serviceMeta, "服务不存在");
        String requestPath = serviceMeta.getRequestPath();

        OkHttpClient httpClient = new OkHttpClient().newBuilder().connectTimeout(servicePublishTime * 2, TimeUnit.MILLISECONDS)
                .readTimeout(servicePublishTime * 2, TimeUnit.MILLISECONDS)
                .build();

        RequestBody requestBody = new RequestBody() {
            @Override
            public MediaType contentType() {
                return MediaType.parse("application/json; charset=utf-8");
            }

            @Override
            public void writeTo(BufferedSink sink) throws IOException {
                sink.writeUtf8(requestVo.getTestDataJson());
            }
        };

        Request request = new Request.Builder()
                .url(testPublishPath + requestPath)
                .addHeader("Accept", "application/json; charset=UTF-8")
                .addHeader("Reg_ID", "000001000001")
                .addHeader("Requester", URLEncoder.encode("数据建模分析", "UTF-8"))
                .addHeader("Terminal_ID", ClientAccesstIpUtil.getRemoteIpAddress(servletRequest))
                .addHeader("token", requestVo.getToken())
                .post(requestBody)
                .build();
        try {
            Response response = httpClient.newCall(request).execute();
            if (response.code() > 200 || response.code() < 200) {
                return Result.error(String.valueOf(response.code()), response.message());
            }
            return Result.success(response.body().string());
        } catch (Exception e) {
         log.error(e.getMessage(),e);
            return Result.error("500", e.getMessage());
        }
    }

    private String getRemoteIpAddress() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();
        return request.getRemoteAddr();
    }

    @Override
    public Result testServiceGetResult(ModelServiceRequestVo requestVo, HttpServletRequest servletRequest) throws UnsupportedEncodingException {
        Assert.notNull(requestVo, "请求参数不能为空");
        Assert.notNull(requestVo.getRequestId(), "请求Id不能为空");
        Assert.notNull(requestVo.getRequestUrl(), "请求url不能为空");
        Assert.notNull(requestVo.getPageIndex(), "请求分页不能为空");
        Assert.notNull(requestVo.getPageSize(), "请求分页不能为空");

        OkHttpClient httpClient = new OkHttpClient().newBuilder().connectTimeout(servicePublishTime * 2, TimeUnit.MILLISECONDS)
                .readTimeout(servicePublishTime * 2, TimeUnit.MILLISECONDS)
                .build();

        String requestPath = requestVo.getRequestUrl() + "?requestId=" + requestVo.getRequestId() + "&pageIndex=" + requestVo.getPageIndex()
                + "&pageSize=" + requestVo.getPageSize();
        Request request = new Request.Builder()
                .url(requestPath)
                .addHeader("Reg_ID", "000001000001")
                .addHeader("Requester", URLEncoder.encode("数据建模分析", "UTF-8"))
                .addHeader("Terminal_ID", ClientAccesstIpUtil.getRemoteIpAddress(servletRequest))
                .addHeader("token", requestVo.getToken())
                .get()
                .build();
        try {
            Response response = httpClient.newCall(request).execute();
            if (response.code() > 200 || response.code() < 200) {
                return Result.error(String.valueOf(response.code()), response.message());
            }

            return Result.success(response.body().string());
        } catch (IOException e) {
         log.error(e.getMessage(),e);
            return Result.error("500", e.getMessage());
        }

    }

    @Override
    public String createServiceTransTemp(String transName, HttpServletRequest request,String serviceType) {
        String transId = dataModelingService.saveTempTrans(transName, "", request);
        //这边的serviceType是数值  6
        EnumServiceType serviceTypeByEnName = EnumServiceType.getInstanceByCode(serviceType);
        switch (serviceTypeByEnName){
            case INFORMATION_VERFICATION:
                initInfoCheckTrans(transId);
                break;
            default:
                initServiceTrans(transId);
                break;
        }
        return transId;
    }

    @Override
    public List<TransStepInputVo> queryTransSteps(String transId) {
        TransMeta transMetaById = transMetaService.getTransMetaById(transId);
        List<TransStepInputVo> transStepInputVos = Lists.newArrayList();
        Set<TransMeta> children = transMetaById.getChildren();
        for (TransMeta stepMeta : children) {
            TransStepInputVo transStepInputVo = new TransStepInputVo();
            String code = stepMeta.getUsedPlugin().getCode();
            if ("cicadaStandardSqlInput".equals(code)) {
                transStepInputVo.setName(stepMeta.getName());
                transStepInputVo.setCode(stepMeta.getCode());
                transStepInputVo.setId(stepMeta.getId());
                transStepInputVos.add(transStepInputVo);
            }
        }

        return transStepInputVos;
    }
    private void initInfoCheckTrans(String transId) {
        transformApiService.createTransStep("cicadaMetaServiceInput", "API请求入参_1", "300", "100", transId);
        transformApiService.createTransStep("cicadaMetaServiceCheckOutPut", "信息核查服务输出插件_2", "500", "500", transId);
        transformApiService.createTransStep("cicadaMetaServiceOutput", "API响应结果_3", "700", "700", transId);
    }
    //初始化服务模型   初始化输入输出插件
    private void initServiceTrans(String transId) {
        transformApiService.createTransStep("cicadaMetaServiceInput", "API请求入参_1", "300", "100", transId);
        transformApiService.createTransStep("cicadaMetaServiceOutput", "API响应结果_2", "500", "500", transId);
    }


    /**
     * 测试结果返回参数
     */
    /*private ModelServiceResultVo getModelServiceResultVo(SchemeExecutorResult schemeExecutorResult) {
        ModelServiceResultVo serviceResultVo = new ModelServiceResultVo();
        serviceResultVo.setRequestId(schemeExecutorResult.getRequestId());
        serviceResultVo.setMessageCode(String.valueOf(schemeExecutorResult.getStatusCode()));
        serviceResultVo.setMessage(schemeExecutorResult.getMsg());
        serviceResultVo.setResponseData(schemeExecutorResult.getOperatorDataSet());

        PageInfo pageInfo = schemeExecutorResult.getPageInfo();

        if (pageInfo != null) {
            ModelServiceResultVo.ResponseParam responseParam = new ModelServiceResultVo.ResponseParam();
            responseParam.setTotalCount(String.valueOf(pageInfo.getTotalCount()));
            responseParam.setPageNumber(String.valueOf(pageInfo.getPageIndex()));
            responseParam.setPageSize(String.valueOf(pageInfo.getPageSize()));
            serviceResultVo.setResponseParam(responseParam);
        }

        return serviceResultVo;
    }*/

}
