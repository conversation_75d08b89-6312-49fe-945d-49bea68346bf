package com.dragonsoft.cicada.datacenter.modules.modeling.importandexport.enums;

//导出内容枚举
public enum ExportContentEnum {
    MODEL("MODEL","仅模型"),
    MODEL_RESOURCE("MODEL_RESOURCE","模型依赖资源"),
    MODEL_RESOURCE_API("MODEL_RESOURCE_API","模型关联资源");

    public String code;
    public String name;

    ExportContentEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static ExportContentEnum getInstanceByName(String name) {
        ExportContentEnum[] var1 = values();
        int var2 = var1.length;

        for(int var3 = 0; var3 < var2; ++var3) {
            ExportContentEnum value = var1[var3];
            if (value.name.equals(name)) {
                return value;
            }
        }

        return null;
    }

    public static ExportContentEnum getInstanceByCode(String code) {
        ExportContentEnum[] var1 = values();
        int var2 = var1.length;

        for(int var3 = 0; var3 < var2; ++var3) {
            ExportContentEnum value = var1[var3];
            if (value.code.equals(code)) {
                return value;
            }
        }

        return MODEL_RESOURCE;
    }
}
