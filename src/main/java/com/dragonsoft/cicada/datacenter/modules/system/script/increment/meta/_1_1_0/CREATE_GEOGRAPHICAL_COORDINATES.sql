-- Drop table

-- DROP TABLE public.t_md_geographical_coordinates

CREATE TABLE IF NOT EXISTS public.t_md_geographical_coordinates (
	sf varchar(300) NOT NULL,
	ds varchar(300) NOT NULL,
	qx varchar(300) NOT NULL,
	jd float8 NOT NULL,
	wd float8 NOT NULL
)
WITH (
	OIDS=FALSE
) ;


insert into t_md_geographical_coordinates (sf,ds,qx,jd,wd) values ('天津市','天津市','天津市',117.2,39.12);
insert into t_md_geographical_coordinates (sf,ds,qx,jd,wd) values ('天津市','天津市','和平区',117.2,39.12);
insert into t_md_geographical_coordinates (sf,ds,qx,jd,wd) values ('天津市','天津市','河东区',117.22,39.12);
insert into t_md_geographical_coordinates (sf,ds,qx,jd,wd) values ('天津市','天津市','河西区',117.22,39.12);
insert into t_md_geographical_coordinates (sf,ds,qx,jd,wd) values ('天津市','天津市','南开区',117.15,39.13);
insert into t_md_geographical_coordinates (sf,ds,qx,jd,wd) values ('天津市','天津市','河北区',117.18,39.15);
insert into t_md_geographical_coordinates (sf,ds,qx,jd,wd) values ('天津市','天津市','红桥区',117.15,39.17);
insert into t_md_geographical_coordinates (sf,ds,qx,jd,wd) values ('天津市','天津市','塘沽区',117.65,39.02);
insert into t_md_geographical_coordinates (sf,ds,qx,jd,wd) values ('天津市','天津市','汉沽区',117.8,39.25);
insert into t_md_geographical_coordinates (sf,ds,qx,jd,wd) values ('天津市','天津市','大港区',117.45,38.83);
insert into t_md_geographical_coordinates (sf,ds,qx,jd,wd) values ('天津市','天津市','东丽区',117.3,39.08);
insert into t_md_geographical_coordinates (sf,ds,qx,jd,wd) values ('天津市','天津市','西青区',117.0,39.13);
insert into t_md_geographical_coordinates (sf,ds,qx,jd,wd) values ('天津市','天津市','津南区',117.38,38.98);
insert into t_md_geographical_coordinates (sf,ds,qx,jd,wd) values ('天津市','天津市','北辰区',117.13,39.22);
insert into t_md_geographical_coordinates (sf,ds,qx,jd,wd) values ('天津市','天津市','武清区',117.03,39.38);
insert into t_md_geographical_coordinates (sf,ds,qx,jd,wd) values ('天津市','天津市','宝坻区',117.3,39.72);
insert into t_md_geographical_coordinates (sf,ds,qx,jd,wd) values ('天津市','天津市','滨海新区',117.68,39.03);
insert into t_md_geographical_coordinates (sf,ds,qx,jd,wd) values ('天津市','天津市','宁河区',117.82,39.33);
insert into t_md_geographical_coordinates (sf,ds,qx,jd,wd) values ('天津市','天津市','静海区',116.92,38.93);
insert into t_md_geographical_coordinates (sf,ds,qx,jd,wd) values ('天津市','天津市','蓟州区',117.4,40.05);