package com.dragonsoft.cicada.datacenter.modules.datavisual.model;

import com.code.common.utils.StringUtils;
import com.code.dragonsoft.dataquery.service.querymodel.ColumnDataModel;
import com.code.meta.dml.standard.Measure;
import com.code.meta.dml.standard.StandardQuery;
import com.code.metadata.datavisual.WidgetDatasetDims;
import com.code.metadata.datavisual.WidgetDatasetMeasures;
import com.code.metadata.res.ddl.LogicDataColumn;
import com.dragonsoft.cicada.datacenter.modules.datavisual.model.annotation.WidgetLabel;
import com.dragonsoft.cicada.datacenter.modules.datavisual.model.enums.WidgetType;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@WidgetLabel(name = "指标卡", describe = "指标卡", type = WidgetType.INDICATOR_CARD)
public class IndicatorCardWidget extends BarChartWidget {
    @Override
    protected void addMeasures(StandardQuery query) {
        Set<WidgetDatasetMeasures> widgetDatasetMeasures = this.widgetDataset.getWidgetDatasetMeasures();
        for (WidgetDatasetMeasures m : widgetDatasetMeasures) {
            query.addMeasures(new Measure(m.getFiledCode(), this.getFunc(m.getFuncType(), m.getIsDistinct()), m.getFiledCode()));
        }
    }

    @Override
    public Map<String, Object> builderResult(ColumnDataModel columns,String code) {
        return buildColumns(columns);
    }

    private Map<String, Object> buildColumns(ColumnDataModel result) {
        Map<String, Object> rMap = new HashMap<>();
        if (null == result || result.getFieldName().isEmpty()) {
            rMap.put("code", -1);
            return rMap;
        }
        //维度
        WidgetDatasetDims dim = null;
        if(!this.widgetDataset.getWidgetDatasetDims().isEmpty()){

            dim = this.widgetDataset.getWidgetDatasetDims().iterator().next();
        }
        //度量
        List<WidgetDatasetMeasures> measures = this.widgetDataset.getFromListMeasures();

        //添加字段
        if(dim != null){
            this.barChartData.addColumns(StringUtils.isNotBlank(dim.getFiledName()) ? dim.getFiledName() : dim.getFiledCode());
        }
        for (WidgetDatasetMeasures measure : measures) {
            this.barChartData.addColumns(StringUtils.isNotBlank(measure.getFiledName()) ? measure.getFiledName() : measure.getFiledCode());
        }

        for (Map rowMap : result.getFieldValue()) {

            Map<String,Object> map = Maps.newLinkedHashMap();
            if(dim != null){
                String val = this.getDimsFilterVal(dim,null, rowMap);
                if (StringUtils.isNotBlank(val)) {
                    String dname = StringUtils.isNotBlank(dim.getFiledName()) ? dim.getFiledName() : dim.getFiledCode();
                    map.put(dname, val);
                }
            }
            for (WidgetDatasetMeasures measure : measures) {

                String mname = StringUtils.isNotBlank(measure.getFiledName()) ? measure.getFiledName() : measure.getFiledCode();
                if (null == rowMap.get(measure.getFiledCode())) {
                    map.put(mname, 0);
                } else {
                    map.put(mname, this.getMeasuresFilterVal(measure, rowMap));
                }
            }
            this.barChartData.rows.add(map);

        }
        List<String> dimsCode = this.widgetDataset.getFromListDims().stream().map(s->s.getFiledName()).collect(Collectors.toList());
        this.barChartData.setDimsCodes(dimsCode);
        rMap.put("code", 1);
        rMap.put("data", this.barChartData);
        return rMap;
    }

    private ColumnDataModel changeColumnCode(ColumnDataModel columns, String datasetId) {
        List<LogicDataColumn> logicColumns = this.logicDataColumnService.findLogicDataColumnsByLogicDataObjId(datasetId);
        Map<String, String> map = logicColumns.stream().collect(Collectors.toMap(code -> {
            String resCode = code.getAlias();
            if (StringUtils.isBlank(resCode)) {
                resCode = code.getCode();
            }
            return resCode;
        }, code -> {
            String resName = code.getName();
            if (StringUtils.isBlank(resName)) {
                resName = code.getCode();
            }
            return resName;
        }));

        //修改FieldName
        Map<String, String> newFieldName = new HashMap<>();
        columns.getFieldName().forEach((k, v) -> {
            newFieldName.put(map.get(k), v);
        });
        List<Map> newFieldValue = Lists.newArrayList();
        //修改row
        for (Map valueMap : columns.getFieldValue()) {
            Map<String, Object> newFieldValueMap = new HashMap<>();
            valueMap.forEach((k, v) -> {
                newFieldValueMap.put(map.get(k), v);
            });
            newFieldValue.add(newFieldValueMap);
        }

        columns.setFieldName(newFieldName);
        columns.setFieldValue(newFieldValue);

        return columns;
    }

}
