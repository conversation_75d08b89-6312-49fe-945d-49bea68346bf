package com.dragonsoft.cicada.datacenter.modules.supermarkmodel.service.impl;

import com.code.common.paging.PageInfo;
import com.code.common.utils.assertion.Assert;
import com.code.metadata.modelsupermark.ModelLabelRelation;
import com.code.metaservice.modelsupermark.*;
import com.dragoninfo.dfw.bean.Result;
import com.dragonsoft.cicada.datacenter.modules.supermarkmodel.service.IMainPageModelService;
import com.dragonsoft.cicada.datacenter.modules.supermarkmodel.service.IMyModelService;
import com.dragonsoft.cicada.datacenter.modules.supermarkmodel.vo.ModelLabelVo;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.service.IUserService;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.vo.UserVo;
import com.fw.service.BaseService;
import com.fw.service.annotation.Service;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class MainPageModelServiceImpl extends BaseService implements IMainPageModelService {

    @Autowired
    private IModelPublishService modelPublishServiceImpl;

    @Autowired
    private ISuperMarkModelingService superMarkModelingServiceImpl;

    @Autowired
    private IModelBrowseService modelBrowseServiceImpl;

    @Autowired
    private IModelFocusService modelFocusServiceImpl;

    @Autowired
    private IMyModelService myModelServiceImpl;

    @Autowired
    private IUserService userService;

    @Autowired
    private IModelSearchService modelSearchServiceImpl;

    /**
     * 模型信息统计
     */
    @Override
    public Result queryModelCountInfo(Map<String, Object> queryCountMap) {
        String modelPublishNum = modelPublishServiceImpl.queryModelPublishCount(queryCountMap); //模型上架数
        String userLoginNum = superMarkModelingServiceImpl.queryUserLoginCount(queryCountMap); //用户访问量
        String modelBrowseNum = modelBrowseServiceImpl.queryModelBrowseCount(null); //用户浏览量
        String modelFocusNum = modelFocusServiceImpl.queryPublishedModelFocusCount(null); //已上架模型用户关注量

        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("modelPublishNum", modelPublishNum);
        resultMap.put("userLoginNum", userLoginNum);
        resultMap.put("modelBrowseNum", modelBrowseNum);
        resultMap.put("modelFocusNum", modelFocusNum);

        return Result.success(resultMap);
    }

    /**
     * 查询模型排行榜列表
     */
    @Override
    public Result queryModelRankingList(Map<String, Object> queryModelMap) {
        Assert.notNull(queryModelMap.get("pageNum"), "分页不能为空");
        Assert.notNull(queryModelMap.get("pageSize"), "分页不能为空");
        Assert.notNull(queryModelMap.get("rankType"), "排行榜类型不能为空");

        PageInfo pageResult = superMarkModelingServiceImpl.queryModelRankingList(queryModelMap);

        List<Map> dataList = pageResult.getDataList();
        if (CollectionUtils.isNotEmpty(dataList)) {
            for (Map rankVo : dataList) {
                String transId = (String) rankVo.get("transid");
                String type = (String) rankVo.get("type");
                //模型名称
                Map<String, Object> transInfo = myModelServiceImpl.getModelTransInfo(transId, type);
                rankVo.put("name", transInfo.get("name"));
                //用户名
                String userId = (String) transInfo.get("operateUserId");
                if (StringUtils.isNotEmpty(userId)) {
                    UserVo userVo = userService.queryUserById(userId);
                    rankVo.put("userName", userVo.getObjName());
                }
                //评分
                Double avgScoreStr = (Double) rankVo.get("avgscore");
                BigDecimal avgScore = new BigDecimal(avgScoreStr);
                avgScore.setScale(2, RoundingMode.HALF_UP);
                rankVo.put("avgscore", avgScore);
                //标签
                String modelId = (String) rankVo.get("id");
                setModelLabelByModelId(rankVo, modelId);
            }
        }

        return Result.success(pageResult);
    }

    /**
     * 获取模型标签
     */
    public void setModelLabelByModelId(Map rankVo, String modelId) {
        //模型标签
        List<ModelLabelRelation> labelList = modelSearchServiceImpl.getModelLabel(modelId);
        if (CollectionUtils.isNotEmpty(labelList)) {
            List<ModelLabelVo> labelVos = labelList.stream().filter(labelRel -> labelRel.getModelLabel() != null).map(labelRel -> {
                ModelLabelVo modelLabelVo = new ModelLabelVo();
                modelLabelVo.setLabelName(labelRel.getModelLabel().getLabelName());
                return  modelLabelVo;
            }).collect(Collectors.toList());
            rankVo.put("labels", labelVos);
        }
    }

    /**
     * 查询模型用户排行上架榜列表
     */
    @Override
    public Result queryModelUserRankingList(Map<String, Object> queryModelMap) {
        Assert.notNull(queryModelMap.get("pageNum"), "分页不能为空");
        Assert.notNull(queryModelMap.get("pageSize"), "分页不能为空");
        Assert.notNull(queryModelMap.get("rankType"), "排行榜类型不能为空");

        PageInfo pageResult = superMarkModelingServiceImpl.queryModelUserRankingList(queryModelMap);

        return Result.success(pageResult);
    }
}
