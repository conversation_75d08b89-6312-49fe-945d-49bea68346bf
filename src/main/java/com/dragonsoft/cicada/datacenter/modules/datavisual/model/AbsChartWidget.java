package com.dragonsoft.cicada.datacenter.modules.datavisual.model;


import com.alibaba.fastjson.JSONObject;
import com.code.common.utils.StringUtils;
import com.code.dataset.AbstractAtomDataSetOperator;
import com.code.dataset.IStepRelationService;
import com.code.dragonsoft.dataquery.service.QueryDataService;
import com.code.dragonsoft.dataquery.service.querymodel.ColumnDataModel;
import com.code.dragonsoft.dataquery.service.querymodel.ParamDataModel;
import com.code.meta.dml.IMeasureFunc;
import com.code.meta.dml.standard.IMultCdin;
import com.code.meta.dml.standard.aggregate.*;
import com.code.meta.dml.standard.cdins.MultCdin;
import com.code.meta.dml.standard.cdins.Order;
import com.code.meta.dml.standard.cdins.QueryCdins;
import com.code.metadata.datavisual.Widget;
import com.code.metadata.datavisual.WidgetDatasetDims;
import com.code.metadata.datavisual.WidgetDatasetDimsDrill;
import com.code.metadata.datavisual.WidgetDatasetMeasures;
import com.code.metadata.res.common.ClassifierStat;
import com.code.metadata.res.ddl.LogicDataColumn;
import com.code.metadata.res.ddl.LogicDataObj;
import com.code.metaservice.ddl.ILogicDataColumnService;
import com.code.metaservice.ddl.ILogicDataObjService;
import com.code.metaservice.res.common.ClassifierStatService;
import com.dragonsoft.cicada.datacenter.modules.datavisual.dataset.IDataSetBuilder;
import com.dragonsoft.cicada.datacenter.modules.datavisual.function.ChartConfig;
import com.dragonsoft.cicada.datacenter.modules.datavisual.function.ColumnMeta;
import com.dragonsoft.cicada.datacenter.modules.datavisual.function.FunctionScheduler;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.service.MbService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;

import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

@Slf4j
public abstract class AbsChartWidget extends Widget {

    public static final int PREVIEW_MODE = 0;
    public static final int NORMAL_MODE = 1;
    public static String DB_TYPE_ES = "elasticsearch";
    private static String[] parsePatterns = {"yyyy-MM-dd", "yyyy年MM月dd日",
            "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm:ss.SSS", "yyyy-MM-dd HH:mm:ss.SSSZ", "yyyy-MM-dd HH:mm", "yyyy/MM/dd",
            "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm:ss.SSS", "yyyy/MM/dd HH:mm:ss.SSSZ", "yyyy/MM/dd HH:mm", "yyyyMMdd"};

    List<AbsWidgetFilter> widgetFilters;

    public List<AbsWidgetFilter> getWidgetFilters() {
        return widgetFilters;
    }

    List<AbsConditionWidget> conditionWidgets;

    protected QueryDataService queryDataService;
    protected ILogicDataColumnService logicDataColumnService;
    protected ClassifierStatService classifierStatService;
    protected ILogicDataObjService logicDataObjService;
    protected FunctionScheduler functionScheduler;
    protected MbService mbService;
    protected IStepRelationService stepRelationService;

    protected ChartConfig builderChartConfig(List<WidgetDatasetDims> widgetDatasetDims, List<WidgetDatasetMeasures> measures, ColumnDataModel originalColumns) {
        List<ColumnMeta> dimensions = new LinkedList<>();
        for (WidgetDatasetDims widgetDatasetDim : widgetDatasetDims) {
            LogicDataColumn logicDataColumn = logicDataColumnService.findLogicDataColumnById(widgetDatasetDim.getFiledId());
            ColumnMeta columnMeta = new ColumnMeta(widgetDatasetDim.getFiledName(),
                    widgetDatasetDim.getFiledCode(),
                    logicDataColumn.getDataType(),
                    logicDataColumn.getFormat(),
                    widgetDatasetDim.getFormatDateGranularity());
            if (widgetDatasetDim.getId() == null) {
                columnMeta.setQueryColumnl(true);
            }
            dimensions.add(columnMeta);
        }
        List<ColumnMeta> metrics = new LinkedList<>();
        for (WidgetDatasetMeasures widgetDatasetMeasure : measures) {
            LogicDataColumn logicDataColumn = logicDataColumnService.findLogicDataColumnById(widgetDatasetMeasure.getFiledId());
            //目前只会有一个function
            String functionsJson = widgetDatasetMeasure.getFunctionsJson();
            List<String> functionList = new ArrayList<>();
            functionList = JSONObject.parseArray(functionsJson, String.class);
            String[] functions = functionList.toArray(new String[functionList.size()]);
            String code = StringUtils.isBlank(widgetDatasetMeasure.getFiledAlias()) ? widgetDatasetMeasure.getFiledCode() : widgetDatasetMeasure.getFiledAlias();
            ColumnMeta columnMeta = new ColumnMeta(widgetDatasetMeasure.getFiledName(), code, functions, logicDataColumn.getDataType());
            metrics.add(columnMeta);
        }

        ChartConfig chartConfig = new ChartConfig(dimensions, metrics);
        return chartConfig;
    }

    public void setMbService(MbService mbService) {
        this.mbService = mbService;
    }

    public void setLogicDataObjService(ILogicDataObjService logicDataObjService) {
        this.logicDataObjService = logicDataObjService;
    }

    public void setClassifierStatService(ClassifierStatService classifierStatService) {
        this.classifierStatService = classifierStatService;
    }

    public void setQueryDataService(QueryDataService queryDataService) {
        this.queryDataService = queryDataService;
    }

    public void setLogicDataColumnService(ILogicDataColumnService logicDataColumnService) {
        this.logicDataColumnService = logicDataColumnService;
    }

    public FunctionScheduler getFunctionScheduler() {
        return functionScheduler;
    }

    public void setFunctionScheduler(FunctionScheduler functionScheduler) {
        this.functionScheduler = functionScheduler;
    }

    public IStepRelationService getStepRelationService() {
        return stepRelationService;
    }

    public void setStepRelationService(IStepRelationService stepRelationService) {
        this.stepRelationService = stepRelationService;
    }

    //加载数据
    public abstract Map<String, Object> loadingData(IDataSetBuilder dataSetBuilder, int mode, String code, int timers);

    //获取sql
    public abstract String getSQL(IDataSetBuilder dataSetBuilder, int mode);


    public String getSearchSQL(String dataSetId) {

        String table = "";
        LogicDataObj logicDataObj = logicDataObjService.findLogicDataObjById(dataSetId);
        //获取当前数据集的所有步骤
        List<AbstractAtomDataSetOperator> setOperators = getSetOperators(dataSetId);

        if (setOperators.size() <= 0) {
            String sql = logicDataObj.getSql();
            if (!"QUICK_SQL".equals(logicDataObj.getBelongType())) {
                for (int i = 0; i < sql.length(); i++) {
                    char c = sql.charAt(i);
                    if (Character.isUpperCase(c)) {
                        String tableName = logicDataObj.getCode();
                        if (tableName.contains(".")) {
                            tableName = tableName.replace(".", "\".\"");
                        }
                        sql = "select * from " + "\"" + tableName + "\"";
                        break;
                    }
                }
            }
            table = sql;
        } else {
            table = stepRelationService.getSQL(setOperators, null);
        }
        return " ( " + table + " ) ";
    }

    private List getSetOperators(String dataSetId) {
        String relationId = stepRelationService.getRelationId(dataSetId);
        List<AbstractAtomDataSetOperator> setOperators = stepRelationService.flushStep(relationId, null);
        if (setOperators == null) {
            setOperators = Lists.newArrayList();
        }
        return setOperators;
    }


    protected IMultCdin getCondition(QueryCdins queryCdins) {
        IMultCdin all = new MultCdin();
        IMultCdin filter = new MultCdin();
        IMultCdin condition = new MultCdin();
        if (null != this.widgetFilters && !this.widgetFilters.isEmpty()) {
            this.widgetFilters.forEach(w -> {
                IMultCdin m = w.builderCondition(queryCdins);
                if (!m.isEmpty()) {
                    filter.addCdin(m);
                }
            });
        }

        if (null != this.conditionWidgets && !this.conditionWidgets.isEmpty()) {
            this.conditionWidgets.forEach(c -> {
                IMultCdin m = c.builderCondition(queryCdins);
                if (!m.isEmpty()) {
                    condition.addCdin(m);
                }
            });
        }
        if (!filter.isEmpty()) {
            all.addCdin(filter);
        }
        if (!condition.isEmpty()) {
            all.addCdin(condition);
        }
        return all;
    }


    public void setWidgetFilters(List<AbsWidgetFilter> widgetFilters) {
        this.widgetFilters = widgetFilters;
    }


    public void setConditionWidgets(List<AbsConditionWidget> conditionWidgets) {
        this.conditionWidgets = conditionWidgets;
    }

    public IMeasureFunc getFunc(String code, int isDistinct) {
        boolean isD = isDistinct != 0;
        if (StringUtils.isBlank(code)) {
            return new Sum(isD);
        }
        if ("sum".equals(code)) {
            return new Sum(isD);
        }
        if ("max".equals(code)) {
            return new Max(isD);
        }
        if ("min".equals(code)) {
            return new Min(isD);
        }
        if ("count".equals(code)) {
            return new Count(isD);
        }
        if ("mean".equals(code)) {
            return new Avg(isD);
        }
        if ("std".equals(code)) {
            return new StddevPop(isD);
        }
        if ("variance".equals(code)) {
            return new VarPop(isD);
        }
        if ("none".equals(code)) {
            return new None(isD);
        }
        return new Sum(isD);
    }

    public Order[] getOrders(List<WidgetDatasetDims> datasetDims, List<WidgetDatasetMeasures> measures) {
        List<Order> orders = new LinkedList<>();
        if (null != datasetDims) {
            datasetDims.forEach(n -> {
                if (StringUtils.isNotBlank(n.getOrderBy())) {
                    Order order = new Order("\"" + n.getFiledCode() + "\"", n.getOrderBy().toUpperCase());
                    orders.add(order);
                }
            });
        }
        if (null != measures) {
            measures.forEach(n -> {
                if (StringUtils.isNotBlank(n.getOrderBy())) {
                    Order order = null;
                    if (StringUtils.isBlank(n.getFuncType()) || "none".equalsIgnoreCase(n.getFuncType())) {
                        order = new Order("\"" + n.getFiledCode() + "\"", n.getOrderBy().toUpperCase());
                    } else {
                        order = new Order(n.getFuncType() + "(`" + n.getFiledCode() + "`)", n.getOrderBy().toUpperCase());
                    }
                    orders.add(order);
                }
            });
        }

        if (orders.size() == 0) {
            if ("GREENPLUM".equalsIgnoreCase(this.widgetDataset.getDbType()) && this instanceof TableChartWidget) {
                if (null != datasetDims) {
                    Order order = new Order("\"" + datasetDims.get(0).getFiledCode() + "\"", "ASC");
                    orders.add(order);
                } else {
                    if (null != measures) {
                        Order order = new Order("\"" + measures.get(0).getFiledCode() + "\"", "ASC");
                        orders.add(order);
                    } else {
                        return null;
                    }
                }
            } else {
                return null;
            }
        }
        return orders.toArray(new Order[orders.size()]);
    }

    public String getDimsFilterVal(WidgetDatasetDims d, WidgetDatasetDimsDrill dd, Map b) {
        String dimFiledAlias = d.getFiledAlias();
        String dimFilerType = d.getFilterType();
        String dimFiledCode = d.getFiledCode();
        String dimFormatTime = d.getVisualTimeFormat();
        Integer dimIsSeparator = d.getIsSeparator();
        Integer formatDecimals = d.getFormatDecimals();
        if (dd != null) {
            dimFiledAlias = dd.getFiledAlias();
            dimFilerType = dd.getFilterType();
            dimFiledCode = dd.getFiledCode();
            dimFormatTime = dd.getFormatTime();
            dimIsSeparator = dd.getIsSeparator();
            formatDecimals = d.getFormatDecimals();
        }

        String fieldCode = StringUtils.isBlank(dimFiledAlias) ? dimFiledCode : dimFiledAlias;
        if (StringUtils.isBlank(dimFilerType)) {
            String val = b.get(fieldCode) == null ? "" : b.get(fieldCode).toString();
            return val;
        }
        if ("time".equals(dimFilerType)) {
            if (b.get(fieldCode) == null) {
                return "";
            }

            Object val = b.get(fieldCode);
            //获取时间：parsePatterns
            Date time = parseDate(String.valueOf(val));
            String dateTime = null;
            if (time != null) {
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat(dimFormatTime);
                dateTime = simpleDateFormat.format(time);
            }
            return dateTime;
//            FastDateFormat dateInstance = FastDateFormat.getInstance(d.getFormatTime());
//            DateTime dateTime = new DateTime(String.valueOf(val), dateInstance);
//            return dateTime.toString(d.getFormatTime());
        }
        if ("number".equals(dimFilerType)) {
            Double val = Double.parseDouble(b.get(fieldCode).toString());
            DecimalFormat df2 = new DecimalFormat(this.getFormatValString(dimIsSeparator, formatDecimals));
            return df2.format(val);
        }


        return "";

    }

    private Date parseDate(String date) {
        if (date == null) {
            return null;
        }
        try {
            return DateUtils.parseDate(date, parsePatterns);
        } catch (ParseException e) {
            return null;
        }
    }

    public Double getMeasuresFilterVal(WidgetDatasetMeasures d, Map b) {

        if (StringUtils.isBlank(d.getFilterType())) {
            String val = b.get(d.getFiledCode()) == null ? "0" : b.get(d.getFiledCode()).toString();
            return val == null ? 0.0 : Double.parseDouble(val);
        }
        if ("number".equals(d.getFilterType())) {
            Double val = Double.parseDouble(b.get(d.getFiledCode()).toString());
            DecimalFormat df2 = new DecimalFormat(this.getFormatValString(d.getIsSeparator(), d.getFormatDecimals()));
            return Double.valueOf(df2.format(val));
        }
        return 0.0;
    }

    private String getFormatValString(Integer isP, Integer d) {
        String v = "#";
        if (null != d && d > 0) {
            v += ".";
            for (int i = 0; i < d; i++) {
                v += "0";
            }
        }
        return v;
    }

    protected ColumnDataModel query(String newQuery) {
        ColumnDataModel columns = null;
        try {
            ParamDataModel paramDataModel = new ParamDataModel();
            paramDataModel.setScript(newQuery);
//            ClassifierStat obj = classifierStatService.getClassifierStat(ClassifierStat.class, this.getWidgetDataset().getClassifierStatId());
            LogicDataObj obj = logicDataObjService.findLogicDataObjById(this.getWidgetDataset().getClassifierStatId());
            if (!"QUICK_SQL".equalsIgnoreCase(obj.getBelongType())) {
                ClassifierStat classifierStat = logicDataObjService.getClassifierStatByLogic(obj);
                columns = queryDataService.queryData(classifierStat.getId(), paramDataModel);
            } else {
                LogicDataObj metaLogicDataObJ = logicDataObjService.getMetaLogicDataObJ(obj);
                columns = queryDataService.queryDataBySchema(metaLogicDataObJ.getOwnerId(), paramDataModel);
            }
        } catch (Exception e) {
            log.error(e.getMessage(),e);
        }
        return columns;
    }

}
