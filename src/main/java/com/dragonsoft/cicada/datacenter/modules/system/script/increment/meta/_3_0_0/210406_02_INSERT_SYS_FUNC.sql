INSERT INTO "public"."t_sys_func" ("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('busManagement', '0', '接入服务管理', NULL, NULL, NULL, NULL, '2021-01-15 14:18:30.517', NULL, '1');
INSERT INTO "public"."t_sys_func" ("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('accessBusManagement', '0', '接入服务管理', 'busManagement', NULL, NULL, NULL, '2021-01-15 14:18:30.518', NULL, '1');
INSERT INTO "public"."t_sys_func" ("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('accessBusManagementEdit', '0', '编辑', 'accessBusManagement', NULL, NULL, NULL, '2021-01-15 14:18:30.519', NULL, '1');
INSERT INTO "public"."t_sys_func" ("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('accessBusManagementDetails', '0', '详情', 'accessBusManagement', NULL, NULL, NULL, '2021-01-15 14:18:30.518', NULL, '1');
INSERT INTO "public"."t_sys_func" ("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('accessBusManagementDelete', '0', '删除', 'accessBusManagement', NULL, NULL, NULL, '2021-01-15 14:18:30.518', NULL, '1');
INSERT INTO "public"."t_sys_func" ("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('accessBusManagementAddServiceInterface', '0', '添加服务接口', 'accessBusManagement', NULL, NULL, NULL, '2021-01-15 14:18:30.518', NULL, '1');
INSERT INTO "public"."t_sys_func" ("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('accessBusManagementAddNewService', '0', '新增服务', 'accessBusManagement', NULL, NULL, NULL, '2021-01-15 14:18:30.518', NULL, '1');
INSERT INTO "public"."t_sys_func" ("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('accessBusManagementSaved', '0', '保存', 'accessBusManagement', NULL, NULL, NULL, '2021-01-15 14:18:30.518', NULL, '1');
INSERT INTO "public"."t_sys_func" ("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('accessBusManagementMsgInfoEdit', '0', '请求参数编辑', 'accessBusManagement', NULL, NULL, NULL, '2021-01-15 14:18:30.518', NULL, '1');
INSERT INTO "public"."t_sys_func" ("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('accessBusManagementMsgInfoDetails', '0', '请求参数详情', 'accessBusManagement', NULL, NULL, NULL, '2021-01-15 14:18:30.518', NULL, '1');
INSERT INTO "public"."t_sys_func" ("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('accessBusManagementMsgInfoDelete', '0', '请求参数删除', 'accessBusManagement', NULL, NULL, NULL, '2021-01-15 14:18:30.518', NULL, '1');



INSERT INTO "public"."t_sys_auth_obj_func" ("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('f70f59a4e88d567da1854f7526398f03', 'd6121bc4248e45019942e2cb78362500', 'busManagement', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func" ("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('9951214f3ec453ea8ebee56a012b345f', 'd6121bc4248e45019942e2cb78362500', 'accessBusManagement', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func" ("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('f60d979f7b6954c6a30e7361a6452404', 'd6121bc4248e45019942e2cb78362500', 'accessBusManagementEdit', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func" ("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('df7383e52a6d5b1d8617e2f07e88750e', 'd6121bc4248e45019942e2cb78362500', 'accessBusManagementDetails', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func" ("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('5b411f623b7a595a98b26a95f7c52492', 'd6121bc4248e45019942e2cb78362500', 'accessBusManagementDelete', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func" ("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('5300e838e4785410995b84df8dd1057b', 'd6121bc4248e45019942e2cb78362500', 'accessBusManagementAddServiceInterface', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func" ("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('f478e6ddf69454be8aeb0a428e3ce564', 'd6121bc4248e45019942e2cb78362500', 'accessBusManagementAddNewService', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func" ("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('2c16325e745057448941ed6a349e4fa8', 'd6121bc4248e45019942e2cb78362500', 'accessBusManagementSaved', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func" ("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('a0cc814b92c0561f81e0bfb79e9b4a9a', 'd6121bc4248e45019942e2cb78362500', 'accessBusManagementMsgInfoEdit', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func" ("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('e73b00dea23d5a85a1d1b765e8663675', 'd6121bc4248e45019942e2cb78362500', 'accessBusManagementMsgInfoDetails', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func" ("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('85e03654bd365ac089b23cd30007775f', 'd6121bc4248e45019942e2cb78362500', 'accessBusManagementMsgInfoDelete', NULL, NULL, NULL, NULL, NULL, NULL, NULL);