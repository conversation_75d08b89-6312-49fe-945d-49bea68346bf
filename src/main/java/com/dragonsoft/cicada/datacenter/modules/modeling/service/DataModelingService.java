package com.dragonsoft.cicada.datacenter.modules.modeling.service;


import com.code.common.paging.PageInfo;
import com.code.common.utils.R;
import com.code.metadata.business.directory.BaseBusiClassify;
import com.code.ms.domain.application.productionline.dto.RefreshTransStatusDTO;
import com.code.ms.domain.application.productionline.dto.RefreshTransStatusResultDTO;
import com.dragonsoft.cicada.datacenter.modules.dataplan.externaldatasources.vo.SQLModelVo;
import com.dragonsoft.cicada.datacenter.modules.modeling.aimodeling.vo.ModelTreeResult;
import com.dragonsoft.cicada.datacenter.modules.modeling.vo.ModelTreeVo;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

public interface DataModelingService {

    /**
     * 获取过程文件夹目录树
     *
     * @param parentId
     * @param parentName
     * @param userId
     * @param dirType
     * @return
     */
    List<Map<String, Object>> getModelingTransTreeNodes(String parentId, String parentName, String userId, String dirType);

    /**
     * 数据建模的用户
     * @param userId
     * @return
     */
    Map<String, String> getEtlTransUsers(String userId);


    void updateOutputColumn(String stepId, String field, int length, int precsn, String valType, String filterExpress, String uniqueValue);

    /**
     * 根据条件获取魔方过程信息
     *
     * @param condition
     * @param dirId
     * @param pageSize
     * @return
     * @parageNum
     */
    Map<String, Object> listTransInfo(String condition,String state, String dirId, String dirType, Integer pageNum, Integer pageSize, String userId);

    /**
     * 缓存未保存的过程到根目录
     *
     * @param transName //     * @param userInfo
     */
    String saveTempTrans(String transName, String transType, HttpServletRequest request);

    /**
     * 判断该目录底下是否已存在相同名称的
     *
     * @param classifyId
     * @param transName
     * @return
     */
    Boolean checkExistByName(String classifyId, String transName);

    /**
     * 获取本地数据信息
     *
     * @param condition
     * @param dataType  //     * @param userId
     * @return
     */
    Map<String, List<Map<String, String>>> listLocalData(String condition, String dataType);

    /**
     * 获取原始数据信息
     * <p>
     * //     * @param userId
     *
     * @return
     */
    List<Map<String, Object>> getOriginDataList(String condition, String pageSize, String page, boolean isStdlib, String userId);

    List<Map<String, Object>> getOriginDataListByView(String condition, String pageSize, String page, boolean isStdlib, String userId);


    /**
     * 获取结果复用信息
     *
     * @param dateType //     * @param userId
     * @return
     */
//    List<Map<String, Object>> listResultReuse(String condition, String dateType);
    List<SQLModelVo> listResultReuse(String condition, String dateType);

//    void deleteTrans(String transId);

    /**
     * 根据步骤id获取上一步骤id
     *
     * @param transStepId
     * @return
     */
    List<Map<String, String>> getFromTransStepId(String transStepId);

    /**
     * 新增一个过程分类
     *
     * @param parentBcId
     * @param transClassifyName
     * @param dirType
     * @return
     */
    R createTransClassify(String parentBcId, String transClassifyName, String dirType, String userId);

    void createTransClassifyForMy(String parentBcId, String transClassifyName, String transClassifyCode, String dirType, String userId);

    /**
     * 更新过程分类名称
     *
     * @param classifyId
     * @param transClassifyName
     * @return
     */
    R updateTransClassify(String classifyId, String transClassifyName);

    /**
     * 移动目录
     * @param keyword
     * @return
     */
    R moveDirectory(String curryClassifyId,String newParentClassifyId,String dirType);

    // 魔方专用，根据code查询插件节点
    Map getPluginIdByCode(String keyword);

    Map showInputColumns(String tranStepId);

    Map showOutputColumns(String tranStepId);

    List getInputColumn(String stepId);

    List getOutputColumn(String stepId);

    /**
     * 是否根目录下的临时模型
     *
     * @param transId
     * @return
     */
    Boolean hasRootTrans(String transId);


    String getTransMetaTaskGroup(String transId);

    // 魔方专用，根据code查询插件节点
    Map<String, String> queryDmcPluginTreeNodeIdByKeyword(String keyword);

    String getFirstTransId(String transId);
    /**
     * 获取所有实例code
     *
     * @return
     */
    List<String> findAllInstanceList();

    /**
     * 获取插件信息
     *
     * @return
     */
    Map<String, Object> getPluginInfo();

    List<ModelTreeVo> getTreeNode(String userId, String dirType);

    List<BaseBusiClassify> getBaseBusiDir(String userId, String dirType);


    /**
     * 获取首页dataRows数量
     *
     * @param dirId
     * @param userId
     */
    Map dirCount(String dirId, String userId);

    /**
     * 移动模型
     *
     * @param elementId
     * @param classifyId
     * @return
     */
    String moveModel(String elementId, String classifyId);

    void changeTime(String transId);

    List getDataSetByTrans(String transId);

    Map<String,Object> getState(String trans);

    PageInfo getShareResourceByTrans(Map<String,Object> reqMap, String userId);

    Map<String,Object> getResourcesByTranId(String tranId);

    List<ModelTreeResult> getModelService(String userId);

    /**
     * 模型服务模型资源树
     * @param userId
     * @return
     */
    List<ModelTreeVo> queryModelServiceSourceTree(String userId,String transClassify);


    boolean isExistReference(List<String> transIds);

    RefreshTransStatusResultDTO refreshTransStatus(RefreshTransStatusDTO dto);

    /**
     * 获取运行详情日志信息
     * @param detailId
     * @return
     */
    String getTransJobDetail(String detailId);
}

