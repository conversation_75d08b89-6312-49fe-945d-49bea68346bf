package com.dragonsoft.cicada.datacenter.modules.datavisual.function.functions;

import com.code.dragonsoft.dataquery.service.querymodel.ColumnDataModel;
import com.dragonsoft.cicada.datacenter.modules.datavisual.function.ChartConfig;
import com.dragonsoft.cicada.datacenter.modules.datavisual.function.ColumnMeta;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Assert;

import java.sql.Timestamp;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;

/**
 * <AUTHOR>
 * @date 2021/1/4
 */
public class YearOnYearProportionFunction extends AbstractFunction<YearOnYearProportionFunction.YearOnYearProportionMeta> {

//    protected Set<String> timeType = Sets.newHashSet("TIME", "TIMESTAMP", "DATE");

    private final Logger logger = LoggerFactory.getLogger(YearOnYearFunction.class);

    @Override
    protected YearOnYearProportionMeta buildMeta(ChartConfig config,ColumnDataModel dates) {
        List<ColumnMeta> dimensions = config.getDimensions();
        Assert.notEmpty(dimensions, "chart dimension cannot be empty!");
        /*List<ColumnMeta> timeColumns = dimensions.stream()
                .filter(dimension -> timeType.contains(dates.getFieldName().get(dimension.getCode())))
                .collect(Collectors.toList());
        Assert.notEmpty(timeColumns, "time field cannot be empty!");*/
        List<ColumnMeta> timeColumns = dimensions;
        if (timeColumns.size() > 1 && logger.isWarnEnabled()) {
            logger.warn("There is more than one time field, actual: {}", timeColumns.size());
        }
        ColumnMeta timeColumn = timeColumns.get(ThreadLocalRandom.current().nextInt(timeColumns.size()));
        for (ColumnMeta dimension : dimensions) {
            if(dimension.isQueryColumnl()){
                timeColumn = dimension;
            }
        }        if (logger.isDebugEnabled()) {
            logger.debug("get random time field: {}", timeColumn.getCode());
        }
        List<ColumnMeta> metrics = config.getMetrics();
        Assert.notEmpty(metrics, "chart metric cannot be empty!");
        ColumnMeta metricColumn = metrics.get(0);
        return new YearOnYearProportionFunction.YearOnYearProportionMeta(timeColumn.getCode(),
                metricColumn.getCode(),
                timeColumn.getTimeFormat(),
                timeColumn.getDateGranularity());
    }

    @Override
    protected ColumnDataModel doCalculate(ColumnDataModel dates, YearOnYearProportionMeta yearOnYearProportionMeta) {

        String timeColumn = yearOnYearProportionMeta.timeColumn;
        String metricColumn = yearOnYearProportionMeta.metricColumn;

        //提取值map
        Map<Object, Double> dataMap = super.getInitialValueMap(dates.getFieldValue(), timeColumn, metricColumn);

        List<Map> newFieldValue = new LinkedList<>();
        for (Map valueMap : dates.getFieldValue()) {

            //当前数据
            String currentDate = (String) valueMap.get(timeColumn);
            Integer preInt = currentDateChangeIntType(currentDate) - 1;
            String preStr = preInt + currentDate.substring(4);
            if (dataMap.containsKey(preStr)) {
                newFieldValue.add(getProportionValueMap(preStr, dataMap, metricColumn, valueMap, timeColumn));
            }else{
                //没有去年的数据就不计算，直接设置为0.0
                valueMap.put(metricColumn, Double.NaN);
                newFieldValue.add(valueMap);
            }

            /*if (weekAndQuarter.contains(yearOnYearProportionMeta.dateGranularity)) {
                //时间颗粒度是周和季度
                if (!yearOnYearProportionMeta.timeFormat.equalsIgnoreCase("yyyyMM")) {
                    com.code.common.utils.assertion.Assert.fail(String.format("目前仅支持周和季度的原始数据格式为：" +
                                    "时间类型yyyyMM格式,%s为%s类型",
                            yearOnYearProportionMeta.timeColumn,
                            yearOnYearProportionMeta.timeFormat));                }
                //当前数据
                String currentDate = (String) valueMap.get(timeColumn);
                Integer preInt = Integer.valueOf(currentDate.substring(0, currentDate.length() - 1)) - 1;
                String preStr = preInt + currentDate.substring(currentDate.length() - 1);
                if (dataMap.containsKey(preStr)) {
                    newFieldValue.add(getProportionValueMap(preStr, dataMap, metricColumn, valueMap, timeColumn));
                }

            } else {
                if (valueMap.get(timeColumn) instanceof String) {
                    //当前数据
                    String currentDate = (String) valueMap.get(timeColumn);

                    Date date = null;
                    SimpleDateFormat formatter = new SimpleDateFormat(yearOnYearProportionMeta.timeFormat);
                    try {
                        date = formatter.parse(currentDate);
                    } catch (ParseException e) {
                        logger.error(e.getMessage(),e);
                        com.code.common.utils.assertion.Assert.fail(String.format("时间格式转换错误，数据为[%s],数据集数据格式为[%s]",
                                currentDate, yearOnYearProportionMeta.timeFormat));
                    }
                    //用于计算的数据
                    Date previousDate = super.getPreviousYear(date);
                    String time = formatter.format(previousDate);
                    if (dataMap.containsKey(time)) {
                        newFieldValue.add(getProportionValueMap(time, dataMap, metricColumn, valueMap, timeColumn));
                    }
                }

                if (valueMap.get(timeColumn) instanceof java.sql.Date) {
                    java.sql.Date currentDate = (java.sql.Date) valueMap.get(timeColumn);
                    Date previousDate = super.getPreviousYear(currentDate);
                    if (dataMap.containsKey(previousDate)) {
                        newFieldValue.add(this.getProportionValueMap(previousDate, dataMap, metricColumn, valueMap, timeColumn));
                    }
                }


                if (valueMap.get(timeColumn) instanceof Time) {
                    Time currentTime = (Time) valueMap.get(timeColumn);
                    Date previousDate = super.getPreviousYear(currentTime);
                    if (dataMap.containsKey(previousDate)) {
                        newFieldValue.add(this.getProportionValueMap(previousDate, dataMap, metricColumn, valueMap, timeColumn));
                    }
                }


                if (valueMap.get(timeColumn) instanceof Timestamp) {
                    Timestamp currentTimeStamp = (Timestamp) valueMap.get(timeColumn);
                    Date previousDate = super.getPreviousYear(currentTimeStamp);

                    if (dataMap.containsKey(super.getTimeStampDate(previousDate))) {
                        newFieldValue.add(this.getProportionValueMap(previousDate, dataMap, metricColumn, valueMap, timeColumn));
                    }
                }
            }*/

        }
        dates.setFieldValue(newFieldValue);
        return dates;
    }

    private Map getProportionValueMap(Date previousDate, Map<Object, Double> dataMap, String metricColumn, Map valueMap, String timeColumn) {
        Double previousValue = 0.0;

        if (valueMap.get(timeColumn) instanceof Timestamp) {
            previousValue = dataMap.get(this.getTimeStampDate(previousDate));
        } else {
            previousValue = dataMap.get(previousDate);
        }

        Double currentValue = Double.valueOf(valueMap.get(metricColumn).toString());
        Double differenceValue = currentValue - previousValue;

        if (0.0 == previousValue) {
            valueMap.put(metricColumn, Double.NaN);
        } else {
            valueMap.put(metricColumn, super.KeepDecimals(differenceValue / previousValue, 2));
        }
        return valueMap;
    }
    private Map getProportionValueMap(String previousDate, Map<Object, Double> dataMap, String metricColumn, Map valueMap, String timeColumn) {
        Double previousValue = 0.0;

        previousValue = dataMap.get(previousDate);

        Double currentValue = Double.valueOf(valueMap.get(metricColumn).toString());
        Double differenceValue = currentValue - previousValue;

        if (0.0 == previousValue) {
            valueMap.put(metricColumn, Double.NaN);
        } else {
            valueMap.put(metricColumn, super.KeepDecimals(differenceValue / previousValue, 2));
        }
        return valueMap;
    }

    @Override
    public String getName() {
        return "求同比(比例)";
    }

    @Override
    public String getCode() {
        return "year-on-year-proportion";
    }

    protected static class YearOnYearProportionMeta {
        private String timeColumn;
        private String metricColumn;
        private String timeFormat;
        private String dateGranularity;

        public YearOnYearProportionMeta(String timeColumn, String metricColumn, String timeFormat, String dateGranularity) {
            this.timeColumn = timeColumn;
            this.metricColumn = metricColumn;
            this.timeFormat = timeFormat;
            this.dateGranularity = dateGranularity;
        }
    }

}
