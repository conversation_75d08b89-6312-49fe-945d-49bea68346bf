package com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.service;

import com.code.common.paging.PageInfo;
import com.dragoninfo.dfw.bean.Result;
import com.dragonsoft.cicada.datacenter.modules.dataplan.apitest.vo.TestApiParamVo;
import com.dragonsoft.cicada.datacenter.modules.dataplan.apitest.vo.TestApiResultVo;
import com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.vo.GetModelServiceClassifyVo;
import com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.vo.GetPluginModelServiceListOutVo;
import com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.vo.ManagementPageVo;
import com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.vo.ParamConfigVo;
import com.dragonsoft.cicada.datacenter.modules.modeling.vo.TransClassifyVo;
import com.fw.tenon.tree.Tree;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * 版权所有：厦门市巨龙信息科技有限公司
 * Copyright 2018 Xiamen Dragon Information Technology Co. Ltd.
 * All right reserved.
 * ====================================================
 * 修订记录：
 * No    日期				作者(操作:具体内容)
 * 1.    2021/9/8 7:56			baijx（<EMAIL>）(创建:创建文件)
 * ====================================================
 * 类描述：
 */
public interface IServiceManagementService {
    /**
     * 根据前端返回的分页信息，查询分页
     * @param managementPage 信息Vo
     * @return 分页
     */
    PageInfo queryServicePageByCondition(ManagementPageVo managementPage,String userId);

    /**
     * 根据服务id的，返回对应的详情数据
     * @param serviceId id
     * @return 数据
     */
    ParamConfigVo queryServiceDetails(String serviceId) throws Exception;

    /**
     * 根据服务资源sourceId，返回已发布服务下拉列表
     * @param sourceId
     * @return
     */
    Result queryPublishedServiceMetaSelectList(String sourceId);

    /**
     * 根据模型id查找发布的服务
     * @param modelId
     * @return
     */
    List<Map<String,Object>> queryServicesByModelId(String modelId,String type);

    /**
     * 根据服务发布id的，返回对应的详情数据
     * @param serviceId id
     * @return 数据
     */
    Map<String,Object> queryServiceDetailsByPublishedId(String serviceId);

    /**
     * 查找服务参数
     * @param serviceId
     * @param isOutput
     * @return
     */
    List<Map<String,Object>> queryServiceParamsByCondition(String serviceId,String isOutput);

    /**
     * 查找我的服务api
     * @param serviceType
     * @return
     */
    List<Map<String,Object>> queryMyServiceByType(String serviceType,String userId);


    String newServiceClassify(String parentId,String name,String code,String userId);

    List<Tree> queryServiceClassifyTree(String userId);

    List<Tree> queryServiceTree(String userId);

    void deleteServiceClassify(String classifyId);

    void editServiceClassify(String id,String parentId,String name,String code,String userId);

    //移动服务
    void moveModelService(String id,String classifyId);


    List<GetModelServiceClassifyVo> getModelServiceClassify();

    List<GetPluginModelServiceListOutVo> getPluginModelServiceList(String firstClassify,String secondClassify,String userId);

    List<String> checkPlugin(TransClassifyVo classifyVo);

    TestApiResultVo testServiceResult(TestApiParamVo paramVo, HttpServletRequest request);
    List<String> queryBusiClassifyIdList(String classifyId);

    /**
     * 获取他人空间的API目录
     * @param userId
     * @return
     */
    List<Tree>  getOtherApiByUser(String userId);
}
