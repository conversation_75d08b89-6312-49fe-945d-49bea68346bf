package com.dragonsoft.cicada.datacenter.modules.system.permissions.service;

import com.code.common.paging.PageInfo;
import com.dragonsoft.cicada.datacenter.modules.system.common.vo.TreeVo;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.vo.PageVo;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.vo.UserGroupsVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/5/21
 */
public interface IUserGroupsService {

    /**
     * 获取用户组分页列表
     *
     * @param pageVo
     * @return
     */
    PageInfo queryUserGroupsPage(PageVo pageVo);

    /**
     *
     * @param groupId
     * @return
     */
    UserGroupsVo queryGroupById(String groupId);

    /**
     * 查询所有分组
     *
     * @return
     */
    List<TreeVo> queryAllUserGroups(String editId);

    /**
     * 添加用户组
     *
     * @param userGroupsVo
     */
    void addUserGroup(UserGroupsVo userGroupsVo);

    /**
     * 编辑用户组
     *
     * @param userGroupsVo
     */
    void upDataUserGroup(UserGroupsVo userGroupsVo);

    /**
     * 删除用户组
     *
     * @param id
     */
    String deleteUserGroup(String id);

    /**
     * 获取分组随机code
     * @return
     */
    String getGroupRandom();
}
