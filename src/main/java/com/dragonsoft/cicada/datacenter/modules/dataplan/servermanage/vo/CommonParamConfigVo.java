package com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.vo;

import lombok.Data;

import java.util.List;
@Data
public class CommonParamConfigVo {
    private String modelId;
    private String interfaceEnglishName;
    private String interfaceChineseName;
    private String implEnglishName;
    private String implChineseName;
    private String serviceType;
    private String status;
    private String fileUrl;
    private String requestPath;
    private String requestMethod = "post";
    private Integer interfaceVersion;
    private Integer implVersion;
    private List<ParamConfigVo.Param> paramList;
    private List<ParamConfigVo.Param> ginsengList; //响应字段
    private String dbType;
    private String tableName;
    private String filterJson;
    private String memo;

    private String serviceMetaId;
    private String time;
    private String sourceName;

    private String saveOrUpdate; //save-新增API update-修改API
    private String testDataJson; //测试数据

    private String serviceId;

    private String practiceVersion;//ai服务训练版本
    private String transId;//ai方案id


    private String classifyId;//目录id

    private String token;
}
