package com.dragonsoft.cicada.datacenter.modules.logaudit.controller;

import com.code.common.paging.PageInfo;
import com.dragoninfo.dfw.annotation.FuncScanAnnotation;
import com.dragoninfo.dfw.bean.Result;
import com.dragonsoft.cicada.datacenter.modules.logaudit.service.LogAuditService;
import com.dragonsoft.cicada.datacenter.modules.logaudit.vo.LogAuditEnum;
import com.dragonsoft.cicada.datacenter.modules.logaudit.vo.TypesEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/10/9
 */

@CrossOrigin
@RestController
@RequestMapping("/logAudit")
@FuncScanAnnotation(code="logQuery", name="日志审计", parentCode = "systemManagement")
public class LogAuditController {

    @Autowired
    LogAuditService logAuditService;

    @RequestMapping("/getLogInfo")
    @FuncScanAnnotation(code="logQueryViewDetails", name="查看详情", parentCode = "logQuery")
    public Result getLogInfo(@RequestBody Map<String, Object> info) {
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageSize(Integer.parseInt(info.get("pageSize").toString()));
        pageInfo.setPageIndex(Integer.parseInt(info.get("pageIndex").toString()));
        return Result.success(logAuditService.getLogInfo(pageInfo, info));
    }

    @RequestMapping("/getLogModels")
    public Result getLogModels() {
        return Result.success(LogAuditEnum.getAllTypes());
    }

    @RequestMapping("/getTypes")
    public Result getTypes() {
        return Result.success(TypesEnum.getAllTypes());
    }
}
