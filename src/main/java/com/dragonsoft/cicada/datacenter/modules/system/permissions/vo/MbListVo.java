package com.dragonsoft.cicada.datacenter.modules.system.permissions.vo;

import lombok.Data;

@Data
public class MbListVo {
    private String id;
    private String codeCnName;
    private String codeEnName;

    private String sourceId;

    private String dwInstanceId;

    private String dwTableId;

    private String chartName;
    private String chartId;

    private String keyFiledName;
    private String valFiledName;

    private String keyFiledId;
    private String valFiledId;

    public String getDwInstanceId() {
        return dwInstanceId;
    }

    public void setDwInstanceId(String dwInstanceId) {
        this.dwInstanceId = dwInstanceId;
    }

    public String getDwTableId() {
        return dwTableId;
    }

    public void setDwTableId(String dwTableId) {
        this.dwTableId = dwTableId;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCodeCnName() {
        return codeCnName;
    }

    public void setCodeCnName(String codeCnName) {
        this.codeCnName = codeCnName;
    }

    public String getCodeEnName() {
        return codeEnName;
    }

    public void setCodeEnName(String codeEnName) {
        this.codeEnName = codeEnName;
    }

    public String getSourceId() {
        return sourceId;
    }

    public void setSourceId(String sourceId) {
        this.sourceId = sourceId;
    }

    public String getChartName() {
        return chartName;
    }

    public void setChartName(String chartName) {
        this.chartName = chartName;
    }

    public String getChartId() {
        return chartId;
    }

    public void setChartId(String chartId) {
        this.chartId = chartId;
    }

    public String getKeyFiledName() {
        return keyFiledName;
    }

    public void setKeyFiledName(String keyFiledName) {
        this.keyFiledName = keyFiledName;
    }

    public String getValFiledName() {
        return valFiledName;
    }

    public void setValFiledName(String valFiledName) {
        this.valFiledName = valFiledName;
    }

    public String getKeyFiledId() {
        return keyFiledId;
    }

    public void setKeyFiledId(String keyFiledId) {
        this.keyFiledId = keyFiledId;
    }

    public String getValFiledId() {
        return valFiledId;
    }

    public void setValFiledId(String valFiledId) {
        this.valFiledId = valFiledId;
    }
}
