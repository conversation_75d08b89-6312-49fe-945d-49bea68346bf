package com.dragonsoft.cicada.datacenter.modules.modeling.charts;

import com.alibaba.fastjson.JSONArray;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2021/3/4 10:01
 */
public interface IPreviewChartsModelCreator {
    /**
     * @param json    执行sql返回的json对象
     * @param columns columns第一个字段为分组字段，最后一个字段为count字段，
     *                中间所有字段为维度字段，多个维度情况还未考虑，需要和前端页面共同考虑
     *                所以目前columns最多只有三个，最少有两个，除了这情况为异常情况
     * @return
     */
    PreviewChartsModel create(JSONArray json, List<String> columns);
}
