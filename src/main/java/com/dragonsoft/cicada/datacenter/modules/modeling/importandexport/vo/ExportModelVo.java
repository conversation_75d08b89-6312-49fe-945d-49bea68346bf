package com.dragonsoft.cicada.datacenter.modules.modeling.importandexport.vo;

import lombok.Data;

import java.util.List;

@Data
public class ExportModelVo {

    private String exportResourceType;//模型，作业

    private String exportScopeType;//全部，指定模型，指定目录

    private List<String> specifiedModelTransIdList;//指定模型，方案得集合

    private List<String> specifiedTransClassifyIdList;//指定目录时id得集合

    private String exportContentType;//仅导出模型，导出模型依赖的资源，导出模型关联的资源定义

    private String userId;//导出得用户id

}
