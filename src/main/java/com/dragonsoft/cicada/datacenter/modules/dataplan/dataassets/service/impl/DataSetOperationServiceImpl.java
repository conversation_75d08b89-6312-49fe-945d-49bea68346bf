package com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.fastjson.JSON;
import com.code.common.bean.BeanFactory;
import com.code.common.paging.PageInfo;
import com.code.common.spark.SparkConsumer;
import com.code.common.spark.service.ISparkClientService;
import com.code.common.utils.StringUtils;
import com.code.common.utils.assertion.Assert;
import com.code.dataset.IStepRelationService;
import com.code.dataset.operator.column.synccolumn.vo.LogicSyncColumn;
import com.code.dragonsoft.dataquery.service.DDLOperationService;
import com.code.metadata.business.directory.BaseBusiClassify;
import com.code.metadata.datavisual.DataSet;
import com.code.metadata.datavisual.Field;
import com.code.metadata.logic.LogicDataRelation;
import com.code.metadata.res.common.ClassifierStat;
import com.code.metadata.res.ddl.LogicDataColumn;
import com.code.metadata.res.ddl.LogicDataObj;
import com.code.metadata.res.structured.rdb.RdbDataColumn;
import com.code.metadata.res.structured.rdb.RdbDataObj;
import com.code.metadata.res.structured.rdb.RdbUkDataColumn;
import com.code.metadata.res.structured.rdb.RdbUniqueKey;
import com.code.metadata.sql.utils.SQLUtils;
import com.code.metaservice.business.directory.BusiClassifyService;
import com.code.metaservice.dataSet.IDataSetTreeService;
import com.code.metaservice.datawarehouse.IDataWareTreeService;
import com.code.metaservice.datawarehouse.model.DatasetTreeModel;
import com.code.metaservice.ddl.IDataSetStepService;
import com.code.metaservice.ddl.ILogicDataColumnService;
import com.code.metaservice.ddl.ILogicDataObjService;
import com.code.metaservice.ddl.vo.LogicDataSetColumnVo;
import com.code.metaservice.ddl.vo.SaveLogicDataObjectVo;
import com.code.metaservice.logic.LogicDataRelationService;
import com.code.metaservice.res.common.ClassifierStatService;
import com.code.metaservice.util.LogicDataObjectUtil;
import com.code.mist.builder.service.dubbo.ITransformApiService;
import com.code.plugin.db.TypeMapping;
import com.code.std.types.NonStandardType;
import com.code.std.types.StandardType;
import com.code.thirdplugin.cicada.sql.page.page.service.CicadaStandardSqlOutputService;
import com.dragoninfo.dfw.entity.TSysAuthObj;
import com.dragoninfo.dfw.entity.TSysAuthUser;
import com.dragoninfo.dfw.entity.TSysFunc;
import com.dragoninfo.dfw.service.SysAuthObjService;
import com.dragoninfo.dfw.service.SysAuthUserService;
import com.dragoninfo.dfw.service.SysFuncService;
import com.dragonsoft.cicada.datacenter.common.token.GlobalConstant;
import com.dragonsoft.cicada.datacenter.common.utils.DatasetTreeModelUtils;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.common.LogicDataSetConstant;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.service.IDataSetEditService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.service.IDataSetOperationService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.vo.DataSetOperationVo;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.vo.LogicDataSet.ColumnDataSetVo;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.vo.LogicDataSet.SaveTableColumnMappingVo;
import com.dragonsoft.cicada.datacenter.modules.dataplan.externaldatasources.vo.datawarehouse.ServicePublishColumnVo;
import com.dragonsoft.cicada.datacenter.modules.modeling.service.DataModelingService;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.service.IUserService;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.vo.UserVo;
import com.fw.service.BaseService;
import com.fw.service.annotation.Service;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.orm.hibernate4.HibernateTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.util.DataSetUtils.defaultPageSize;

/**
 * <AUTHOR>
 * @date 2020/7/8
 */
@Service
@Slf4j
@PropertySource("classpath:case-config.properties")
public class DataSetOperationServiceImpl extends BaseService implements IDataSetOperationService {

    private static final List<String> EXCLUDE_DATABASE = Arrays.asList("ELASTICSEARCH", "HBASE");
    public static final String LAST_UPDATE_TIME = "last_update_time";
    public static final String BELONG_TYPE = "belong_type";
    public static final String DB_TYPE = "db_type";
    public static final String DATASET_ID = "datasetId";
    public static final String USER_ID = "userId";
    @Autowired
    IDataSetStepService dataSetStepService;
    @Autowired
    IStepRelationService stepRelationService;
    //    @Value("${spark.dubbo.address}")
    private String sparkDubboAddress;
    @Autowired
    private IDataSetTreeService dataSetTreeService;
    @Autowired
    private BusiClassifyService busiClassifyService;
    @Autowired
    private SysFuncService sysFuncService;
    @Autowired
    private ILogicDataObjService logicDataObjService;
    @Autowired
    private ILogicDataColumnService logicDataColumnService;
    @Autowired
    private SysAuthUserService sysAuthUserService;
    @Autowired
    private IDataSetEditService editService;
    @Autowired
    private SysAuthObjService sysAuthObjService;
    @Autowired
    private DDLOperationService ddlOperationService;
    @Autowired
    private ClassifierStatService classifierStatService;

    @Autowired
    private IDataWareTreeService dataWareTreeService;

    @Autowired
    private IUserService userService;

    @Autowired
    private LogicDataRelationService logicDataRelationService;

    @Autowired
    private BeanFactory beanFactory;


    @Autowired
    private DataModelingService dataModelingService;

    @Autowired
    private ITransformApiService transformApiService;

    @Autowired
    private CicadaStandardSqlOutputService standardSqlOutputService;

    @Autowired
    private HibernateTransactionManager transactionManager;

    @Value("${standModel}")
    private boolean standModel;

    @Override
    public List<DatasetTreeModel> queryDataSetTree(String userId, boolean hasLogicDataObject, String currentDataSetId) {

        //获取同库的数据集
        String catalogId = getCatalogId(currentDataSetId);

        List<DatasetTreeModel> datasetTreeModels = dataSetTreeService.queryDataSetTree(hasLogicDataObject, userId, catalogId);
        if (CollectionUtils.isEmpty(datasetTreeModels)) {
            return datasetTreeModels;
        }
        DatasetTreeModel datasetTreeModel = DatasetTreeModelUtils.buildShareDataset(datasetTreeModels.get(0));//创建来自分享节点
        //给分享节点增加孩子
        if (hasLogicDataObject) {
            DatasetTreeModel modelResultSet = DatasetTreeModelUtils.buildModelResultSet(datasetTreeModels.get(0));//创建模型结果节点
            List<String> allAuthDatasetId = userService.dataSetAuthFromOthers(userId); //搜出所有的節點
            List<Map<String, Object>> shareLogicDataSet = getShareLogicDataSet(allAuthDatasetId, "", "", userId);
            datasetTreeModel.setChildren(setChildren(shareLogicDataSet, datasetTreeModel.getId(), true));

            //给模型结果节点增加孩子
            List<Map<String, Object>> treeNodes = dataModelingService.getModelingTransTreeNodes("", "", userId,
                    GlobalConstant.BusiProperties.TRANS_DIR_MF);
            modelResultSet.setChildren(setModelResultChildren(treeNodes, userId));
            datasetTreeModels.get(0).addChild(modelResultSet);
        }
        datasetTreeModels.get(0).addChild(datasetTreeModel);
        buildDataSetTree(userId, datasetTreeModels);
        return datasetTreeModels;
    }

    @Override
    public DatasetTreeModel queryDatasetTreeFromOtherUser(String userId, boolean hasLogicDataObject, String currentDataSetId) {
        DatasetTreeModel personalDataset = DatasetTreeModelUtils.buildPersonalDataset("他人空间");
        
        // 获取其他用户Id SQL
        String sql = "SELECT DISTINCT tmld.operate_user_id FROM t_md_logic_dataobj tmld " + 
                     "INNER JOIN t_md_classify_element tmce ON tmld.id = tmce.element_id " + 
                     "WHERE tmld.operate_user_id != :userId";
        
        List<Map<String, String>> datasetUserList = this.baseDao.sqlQueryForList(sql, addParam("userId", userId).param());
        if (CollUtil.isEmpty(datasetUserList)) {
            return personalDataset;
        }
        List<DatasetTreeModel> resultList = new ArrayList<>();
        String catalogId = getCatalogId(currentDataSetId);
        for (Map<String, String> map : datasetUserList) {
            //获取同库的数据集
            String otherUserId = map.get("operate_user_id");
            List<DatasetTreeModel> datasetTreeModels = dataSetTreeService.queryDataSetTree(hasLogicDataObject, otherUserId, catalogId);
            UserVo userVo = userService.queryUserById(otherUserId);
            if (CollectionUtils.isNotEmpty(datasetTreeModels)) {
                buildDataSetTree(otherUserId, datasetTreeModels);
                for (DatasetTreeModel datasetTreeModel : datasetTreeModels) {
                    for (DatasetTreeModel model : datasetTreeModel.getChildren()) {
                        if ("我的空间".equals(model.getName())) {
                            model.setName(userVo.getObjName());
                            model.setLabel(userVo.getObjName());
                            model.setpId(GlobalConstant.CommonProperties.FROM_PERSONAL_TOP_NODE_ID);
                            resultList.add(model);
                        }
                    }
                }
            }
        }
        personalDataset.setChildren(resultList);
        return personalDataset;
    }

    @Override
    public DatasetTreeModel queryDatasetTreeFromOtherUserBySourceType(String userId, boolean hasLogicDataObject, String currentDataSetId) {
        DatasetTreeModel personalDataset = DatasetTreeModelUtils.buildPersonalDataset("他人数据集");
        // 获取其他用户Id SQL
        String sql = "SELECT DISTINCT tmld.operate_user_id FROM t_md_logic_dataobj tmld " + 
        "INNER JOIN t_md_classify_element tmce ON tmld.id = tmce.element_id " + 
        "WHERE tmld.operate_user_id != :userId";
        List<Map<String, String>> datasetUserList = this.baseDao.sqlQueryForList(sql, addParam("userId", userId).param());
        if (CollUtil.isEmpty(datasetUserList)) {
            return personalDataset;
        }
        List<DatasetTreeModel> resultList = new ArrayList<>();
        String catalogId = getCatalogId(currentDataSetId);
        for (Map<String, String> map : datasetUserList) {
            //获取同库的数据集
            String otherUserId = map.get("operate_user_id");
            List<DatasetTreeModel> datasetTreeModels = dataSetTreeService.queryDataSetTreeByDsType(hasLogicDataObject, otherUserId, catalogId, GlobalConstant.BusiProperties.POLICE_DATA_SOURCE_DIR);
            UserVo userVo = userService.queryUserById(otherUserId);
            if (CollectionUtils.isNotEmpty(datasetTreeModels)) {
                buildDataSetTree(otherUserId, datasetTreeModels);
                for (DatasetTreeModel datasetTreeModel : datasetTreeModels) {
                    if ("我的数据集".equals(datasetTreeModel.getName())) {
                        datasetTreeModel.setName(userVo.getObjName());
                        datasetTreeModel.setLabel(userVo.getObjName());
                        datasetTreeModel.setpId(GlobalConstant.CommonProperties.FROM_PERSONAL_TOP_NODE_ID);
                        resultList.add(datasetTreeModel);
                    }
                }
            }
        }
        personalDataset.setChildren(resultList);
        return personalDataset;
    }


    @Override
    public List<DatasetTreeModel> queryDataSetTreeBySourceType(String userId, boolean hasDataObj, String currentDataSetId) {

        //获取同库的数据集
        String catalogId = getCatalogId(currentDataSetId);

        List<DatasetTreeModel> datasetTreeModels = dataSetTreeService.queryDataSetTreeByDsType(hasDataObj, userId, catalogId, GlobalConstant.BusiProperties.POLICE_DATA_SOURCE_DIR);
        //创建来自分享节点
        DatasetTreeModel sharedModel = getShardModel(userId, catalogId);
        //

        if (hasDataObj) {
            List<String> funcIds = userService.dataSetAuthFuncIdFromOthers(userId);
            Map<String, List<DatasetTreeModel>> map = getShareLogicDataSet(funcIds, userId);
            //给分享节点增加子节点
            setShareDatasetChildren(sharedModel, map);
            final String topOther = "4028b8855c3ecccfbbbc3e5b8f3caa99";
            final String secondOther = "4028b8855c3ecccfbbbc3e5b8f402000";
            // 模型结构子节点
            datasetTreeModels.get(0).getChildren().forEach(datasetTreeModel -> {
                if (datasetTreeModel.getId().equals(topOther)) {
                    datasetTreeModel.getChildren().forEach(model -> {
                        if (model.getId().equals(secondOther)) {
                            List<DatasetTreeModel> dataModelList = buildModelingData(userId);
                            if (CollUtil.isNotEmpty(model.getChildren())) {
                                model.getChildren().addAll(dataModelList);
                            } else {
                                model.setChildren(dataModelList);
                            }
                        }
                    });
                }
            });
        }
        datasetTreeModels.add(sharedModel);
        buildDataSetTree(userId, datasetTreeModels);
        return datasetTreeModels;
    }

    @Override
    public Map<String, String> getCatalogList(String id) {
        Map<String, String> dataMap = Maps.newHashMap();
        String sql = "select busi_classify_id from t_md_classify_element where element_id=:id";
        List<Map<String, String>> mapList = this.baseDao.sqlQueryForList(sql, this.addParam("id", id).param());
        String countSQL = "select count(*) as total from  t_md_busi_classify tmbc inner " +
                "join t_md_busi_dir tmbd on tmbc.busi_dir_id =tmbd.id and  tmbd.busi_dir_type = :dirType" +
                " where  tmbc.id =:id";
        for (Map<String, String> map : mapList) {
            String busiClassifyId = map.get("busi_classify_id");
            Map<String, String> param = new HashMap<>();
            param.put("dirType", GlobalConstant.BusiProperties.POLICE_DATA_SOURCE_DIR);
            param.put("id", busiClassifyId);
            int count = Integer.parseInt(this.baseDao.sqlQueryForValue(countSQL, param));
            String dataFromType = count > 0 ? "fromDsType" : "fromCatalog";
            dataMap.put(busiClassifyId, dataFromType);
        }
        return dataMap;
    }

    @Override
    public Map<String, List<LogicDataSetColumnVo>> getClassifyFeatures(List<SaveTableColumnMappingVo> tableMapping) {
        Map<String, ColumnDataSetVo> trans = new HashMap<>();
        TimeInterval costTime = new TimeInterval();
        List<CompletableFuture<Void>> futureList = new ArrayList<>();
        if (tableMapping.size() == 1) {
            ColumnDataSetVo vo = getClassifyFeature(tableMapping.get(0).getTableId());
            trans.put(tableMapping.get(0).getTableMappingId(), vo);
        } else {
            for (SaveTableColumnMappingVo mappingVo : tableMapping) {
                futureList.add(CompletableFuture.runAsync(() -> {
                    DefaultTransactionDefinition def = new DefaultTransactionDefinition();
                    // 事物隔离级别，开启新事务，这样会比较安全些。
                    def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
                    def.setReadOnly(true);
                    // 获得事务状态
                    TransactionStatus status = transactionManager.getTransaction(def);
                    ColumnDataSetVo vo = getClassifyFeature(mappingVo.getTableId());
                    trans.put(mappingVo.getTableMappingId(), vo);
                    transactionManager.commit(status);
                }));
            }
        }
        CompletableFuture<Void> futureAll = CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0]));
        CompletableFuture<List<Void>> completableFuture = futureAll.thenApplyAsync(
                value -> futureList.stream().map(CompletableFuture::join).collect(Collectors.toList()));
        List<Void> modules = completableFuture.join();
        log.info("---->>>获取字段信息共消耗{}ms<<<-----", costTime.intervalRestart());

        Map<String, List<LogicDataSetColumnVo>> resultMap = Maps.newHashMap();
        for (Map.Entry<String, ColumnDataSetVo> entry : trans.entrySet()) {
            resultMap.put(entry.getKey(), getColumnTrans(entry.getValue()));
        }
        return resultMap;
    }

    private List<LogicDataSetColumnVo> getColumnTrans(ColumnDataSetVo columnDataSetVo) {
        List<LogicDataSetColumnVo> result = new ArrayList<>();
        if (columnDataSetVo == null) {
            return result;
        }
        if (CollUtil.isNotEmpty(columnDataSetVo.getDimension())) {
            for (LogicSyncColumn column : columnDataSetVo.getDimension()) {
                LogicDataSetColumnVo vo = getLogicDataSetColumnVo(column);
                result.add(vo);
            }
        }
        if (CollUtil.isNotEmpty(columnDataSetVo.getMeasure())) {
            for (LogicSyncColumn column : columnDataSetVo.getMeasure()) {
                LogicDataSetColumnVo vo = getLogicDataSetColumnVo(column);
                result.add(vo);
            }
        }
        return result;
    }

    private LogicDataSetColumnVo getLogicDataSetColumnVo(LogicSyncColumn column) {
        LogicDataSetColumnVo vo = new LogicDataSetColumnVo();
        vo.setName(column.getName());
        vo.setCode(column.getCode());
        vo.setId(column.getId());
        vo.setIndexType(column.getIndexType());
        vo.setMemo(column.getMemo());
        vo.setColumnAlias(column.getColumnAlias());
        vo.setBelongParentId(column.getBelongParentId());
        vo.setDataTypeId(column.getDataTypeId());
        vo.setDisplayTypeId(column.getDisplayTypeId());
        vo.setFormat(column.getFormat());
        vo.setNumberFormat(column.getNumberFormat());
        return vo;
    }

    private List<DatasetTreeModel> buildModelingData(String userId) {
        List<Map<String, Object>> treeNodes = dataModelingService.getModelingTransTreeNodes("", "",
                userId, GlobalConstant.BusiProperties.TRANS_DIR_MF);
        List<DatasetTreeModel> dataList = setModelResultChildren(treeNodes, userId);
        List<DatasetTreeModel> resultList = new ArrayList<>();
        getModelingData(dataList, resultList);
        return resultList;

    }

    private void getModelingData(List<DatasetTreeModel> sourceList, List<DatasetTreeModel> resultList) {
        if (CollUtil.isEmpty(sourceList)) {
            return;
        }
        for (DatasetTreeModel model : sourceList) {
            if (CollUtil.isEmpty(model.getChildren()) && CharSequenceUtil.isNotBlank(model.getId())) {
                resultList.add(model);
            } else {
                getModelingData(model.getChildren(), resultList);
            }
        }
    }

    private DatasetTreeModel getShardModel(String userId, String catalogId) {

        List<DatasetTreeModel> datasetTreeModels = dataSetTreeService.queryDataSetTreeByDsType(false, userId, catalogId, GlobalConstant.BusiProperties.POLICE_DATA_SOURCE_DIR);
        if (CollectionUtils.isEmpty(datasetTreeModels)) {
            return new DatasetTreeModel();
        }
        buildDatasetTreeModelShareMark(datasetTreeModels);
        DatasetTreeModel model = DatasetTreeModel.builder().id("-1").build();
        //创建来自分享节点
        DatasetTreeModel datasetTreeModel = DatasetTreeModelUtils.buildShareDataset(model);
        for (DatasetTreeModel shardModel : datasetTreeModels.get(0).getChildren()) {
            shardModel.setpId(datasetTreeModel.getId());
        }
        datasetTreeModel.setChildren(datasetTreeModels.get(0).getChildren());
        return datasetTreeModel;
    }

    private void buildDatasetTreeModelShareMark(List<DatasetTreeModel> datasetTreeModels) {
        if (CollUtil.isEmpty(datasetTreeModels)) {
            return;
        }
        for (DatasetTreeModel datasetTreeModel : datasetTreeModels) {

            if (CollUtil.isNotEmpty(datasetTreeModel.getChildren())) {
                buildDatasetTreeModelShareMark(datasetTreeModel.getChildren());
            }
            datasetTreeModel.setMsg(getShareMark());
        }
    }


    private void setShareDatasetChildren(DatasetTreeModel sharedModel, Map<String, List<DatasetTreeModel>> map) {
        if (MapUtil.isEmpty(map)) {
            return;
        }
        if (CollUtil.isEmpty(sharedModel.getChildren())) {
            List<DatasetTreeModel> data = map.get(sharedModel.getId());
            if (CollUtil.isNotEmpty(data)) {
                sharedModel.setChildren(data);
            }
        } else {
            for (DatasetTreeModel model : sharedModel.getChildren()) {
                setShareDatasetChildren(model, map);
            }
        }


    }

    private Map<String, List<DatasetTreeModel>> getShareLogicDataSet(List<String> allAuthDatasetId, String userId) {
        Map<String, Object> param = Maps.newHashMap();
        String sharedSql = GlobalConstant.DataSetSQL.SHARE_BASE_SQL;
        if (CollUtil.isNotEmpty(allAuthDatasetId)) {
            param.put("ids", allAuthDatasetId);
            param.put("funIds", allAuthDatasetId);
        } else {
            param.put("ids", "");
            param.put("funIds", "");
        }
        param.put(USER_ID, userId);
        param.put("authUserId", userId);
        List<Map<String, Object>> mapList = this.baseDao.sqlQueryForList(sharedSql, param);
        if (CollUtil.isEmpty(mapList)) {
            return Maps.newHashMap();
        }
        List<DatasetTreeModel> modelList = setChildren(mapList, null, true);
        if (CollUtil.isEmpty(modelList)) {
            return Maps.newHashMap();
        }
        return modelList.stream().collect(Collectors.groupingBy(DatasetTreeModel::getpId));
    }

    private List getShareLogicDataSet(List<String> shareLogicId, String name, String dbType, String userId) {
        PageInfo pageInfo = new PageInfo();
        Map<String, Object> params = new HashMap<>();
        pageInfo.setPageIndex(1);
        pageInfo.setPageSize(-1);
        StringBuilder sql = new StringBuilder();
        sql.append("select a.id, a.name, a.code, a.operate_time , a.belong_type, a.db_type, a.memo , a.operate_user_id," +
                " a.is_fast, a.owner_id, a.data_type, -1 as pid ,a.last_update_time\n " +
                "from t_md_logic_dataobj a  where a.id in (:ids) and a.operate_user_id != :userId ");
        if (StringUtils.isNotBlank(name)) {
            sql.append(" and upper(name) like '%").append(name.toUpperCase()).append("%'");
        }

        if (StringUtils.isNotBlank(dbType)) {
            sql.append(" and upper(db_Type) = :dbType");
            params.put("dbType", dbType.toUpperCase());
        }

        //sql.append(" GROUP BY a.id ORDER BY min(tf.create_time) desc ");
        if (shareLogicId.size() == 0) {
            params.put("ids", "");
        } else {
            params.put("ids", shareLogicId);
        }

        params.put(USER_ID, userId);
        return this.baseDao.sqlQueryForList(sql.toString(), params);
    }

    public PageInfo getShareLogicDataSetPage(List<String> funcIds, String name, String dbType, String userId, PageInfo pageInfo) {
        Map<String, Object> params = new HashMap<>();
        StringBuilder sql = new StringBuilder();
        sql.append("select a.id, a.name,min(b.create_time) as operate_time, a.code, a.belong_type, a.db_type," +
                " a.operate_user_id, a.is_fast, a.owner_id, a.data_type, -1 as parentId,a.last_update_time" +
                "\n from t_md_logic_dataobj a right join t_sys_auth_obj_func b on a.id = b.func_code  " +
                "where b.id in (:ids) and a.operate_user_id != :userId ");
        if (StringUtils.isNotBlank(name)) {
            sql.append(" and upper(name) like '%").append(name.toUpperCase()).append("%'");
        }

        if (StringUtils.isNotBlank(dbType)) {
            sql.append(" and upper(belong_Type) = :dbType");
            params.put("dbType", dbType.toUpperCase());
        }
        //sql.append(" order by b.create_time desc ");
        sql.append(" GROUP BY a.id ORDER BY min(b.create_time) desc ");
        if (CollUtil.isEmpty(funcIds)) {
            params.put("ids", "");
        } else {
            params.put("ids", funcIds);
        }

        params.put(USER_ID, userId);
        PageInfo pageInfo1 = this.baseDao.sqlQueryForPage(sql.toString(), params, pageInfo);
        List<Map<String, Object>> dataList = pageInfo1.getDataList();
        for (Map<String, Object> map : dataList) {
            map.put("path", "来自分享");
            Timestamp operateTime = (Timestamp) map.get("operate_time");
            String operateTimeStr = "";
            if (operateTime != null) {
                long time = operateTime.getTime();
                SimpleDateFormat sfd = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                Date d = new Date(time);
                operateTimeStr = sfd.format(d);

            }
            map.put("operate_time", operateTimeStr);
        }
        pageInfo1.setDataList(dataList);
        return pageInfo1;
    }

    private void buildDataSetTree(String userId, List<DatasetTreeModel> datasetTreeModels) {
        List<TSysFunc> tSysFuncs = gettSysFuncs(userId);
        List<String> funcIds = tSysFuncs.stream().map(s -> s.getFuncCode()).collect(Collectors.toList());

        List<String> newList = addFuncIds(datasetTreeModels, funcIds);

        datasetTreeModels = datasetTreeModels.stream().filter(s -> newList.indexOf(s.getId()) != -1).collect(Collectors.toList());
        if (!standModel) {
            if (datasetTreeModels.size() > 0) {
                DatasetTreeModel datasetTreeModel = datasetTreeModels.get(0);
                for (int i = 0; i < datasetTreeModel.getChildren().size(); i++) {
                    if ("标准模型".equals(datasetTreeModel.getChildren().get(i).getName())) {
                        datasetTreeModel.getChildren().remove(i);
                        break;
                    }
                }
            }
        }
    }

    @Override
    public List<DatasetTreeModel> queryPublishDataSetTree(String userId, boolean hasLogicDataObject, String currentDataSetId) {
        //获取同库的数据集
        String catalogId = getCatalogId(currentDataSetId);

        List<DatasetTreeModel> datasetTreeModels = dataSetTreeService.queryDataSetTree(hasLogicDataObject, userId, catalogId);
        if (CollectionUtils.isNotEmpty(datasetTreeModels)) {
            //给分享节点增加孩子
            if (hasLogicDataObject) {
                DatasetTreeModel modelResultSet = DatasetTreeModelUtils.buildModelResultSet(datasetTreeModels.get(0));//创建模型结果节点
                //List<String> allAuthDatasetId = userService.getAllAuthDatasetId(userId); //搜出所有的節點
                //PageInfo shareLogicDataSet = logicDataObjService.getShareLogicDataSet(allAuthDatasetId, "", "", userId);
                //给模型结果节点增加孩子
                List<Map<String, Object>> treeNodes = dataModelingService.getModelingTransTreeNodes("", "",
                        userId, GlobalConstant.BusiProperties.TRANS_DIR_MF);
                modelResultSet.setChildren(setModelResultChildren(treeNodes, userId));
                datasetTreeModels.get(0).addChild(modelResultSet);
            }
        }
        buildDataSetTree(userId, datasetTreeModels);
        return datasetTreeModels;
    }

    /**
     * 建立treeModelVo
     */
    private List<DatasetTreeModel> setChildren(List<Map<String, Object>> dataList, String pid, boolean isShare) {
        Map<String, String> userMap = Maps.newHashMap();
        List<DatasetTreeModel> list = new ArrayList<>();
        if (CollectionUtils.isEmpty(dataList)) return list;
        for (Map<String, Object> map : dataList) {
            String operateUserId = (String) map.get("operate_user_id");
            String userName;
            if (userMap.containsKey(operateUserId)) {
                userName = userMap.get(operateUserId);
            } else {
                TSysAuthObj user = (TSysAuthObj) this.baseDao.get(TSysAuthObj.class, operateUserId);
                userName = user == null ? null : user.getObjName();
                userMap.put(operateUserId, userName);
            }
            String memo = (String) map.get("memo");
            boolean flag = CharSequenceUtil.isNotBlank(memo) && memo.matches(".*[\\u4e00-\\u9fa5].*");
            String belongTableName = flag ? (memo + (String) map.get("code")) : (String) map.get("code");
            if (CharSequenceUtil.isNotBlank(pid)) {
                map.put("pid", pid);
            }
            DatasetTreeModel child = DatasetTreeModel.builder()
                    .id((String) map.get("id"))
                    .code((String) map.get("code"))
                    .name((String) map.get("name"))
                    .open(true)
                    .dbType((String) map.get(DB_TYPE))
                    .belongType((String) map.get(BELONG_TYPE))
                    .belongTableName(belongTableName)
                    .label((String) map.get("name"))
                    .operateTime(map.get("operate_time").toString())
                    .lastUpdateTime((String) map.get(LAST_UPDATE_TIME))
                    .operateUser(userName)
                    .pId((String) map.get("pid"))
                    .build();
            if (isShare) {
                // 设置分享标志
                child.setMsg(getShareMark());
            }

            list.add(child);
        }
        return list;
    }

    private List<DatasetTreeModel> setModelResultChildren(List treeNodes, String userId) {
        List<DatasetTreeModel> list = new ArrayList<>();
        String objName = this.baseDao.sqlQueryForValue("select obj_name from t_sys_auth_obj where id = :id ", this.addParam("id", userId).param());
        for (Object o : treeNodes) {
            Map<String, Object> map = (Map<String, Object>) o;
            DatasetTreeModel child = DatasetTreeModel.builder()
                    .id((String) map.get("id"))
                    .code((String) map.get("code"))
                    .name((String) map.get("name"))
                    .open(true)
                    .dbType((String) map.get(DB_TYPE))
                    .belongType((String) map.get(BELONG_TYPE))
                    .label((String) map.get("name"))
                    .operateTime(String.valueOf(map.get("operate_time")))
                    .operateUser(StringUtils.isBlank(objName) ? null : objName)
                    .belongTableName((String) map.get("code"))
                    .build();
            List children = (List) map.get("children");
            if (CollUtil.isNotEmpty(children)) {
                child.setChildren(setModelResultChildren(children, userId));
            }

            buildModeling(list, objName, map, child);
        }
        return list;
    }

    private void buildModeling(List<DatasetTreeModel> list, String objName, Map<String, Object> map, DatasetTreeModel child) {
        String sql = "select element_id from t_md_classify_element where busi_classify_id = :classifyId ";
        List<Map<String, String>> elementIdsMap = this.baseDao.sqlQueryForList(sql, addParam("classifyId", map.get("id")).param());
        List<String> elementsId = new ArrayList<>();
        for (Map<String, String> stringStringMap : elementIdsMap) {
            elementsId.add(stringStringMap.get("element_id"));
        }
        if (!elementsId.isEmpty()) {
            List<DatasetTreeModel> models = new ArrayList<>();
            for (String transMeta : elementsId) {
                Map<String, String> map1 = new HashMap<>();
                map1.put("id", transMeta);
                List<Map<String, String>> childTrans = this.baseDao.sqlQueryForList("select ts.name,tp.code,ts.id as id " +
                        "from t_etl_trans ts left JOIN t_md_etl_trans_plugin tp on  ts.transplugin_id = tp.id " +
                        "where ts.id in (SELECT child_trans_id from t_etl_trans_stepdetail where trans_id = :id and " +
                        "tp.code in ('cicadaStandardSqlOutput','analysisResultLibraryOutPutMeta')) ", map1);
                models.addAll(buildLogicDataObjFromTrans(objName, map, childTrans));
            }

            List<DatasetTreeModel> children1 = child.getChildren();
            if (CollUtil.isEmpty(children1)) {
                children1 = new ArrayList<>();
            }
            children1.addAll(models);
            child.setChildren(children1);
        }

        list.add(child);
    }

    private List<DatasetTreeModel> buildLogicDataObjFromTrans(String objName, Map<String, Object> map,
                                                              List<Map<String, String>> childTrans) {
        List<DatasetTreeModel> models = new ArrayList<>();
        for (Map<String, String> childTran : childTrans) {
            if (StringUtils.isBlank(childTran.get("id"))) {
                continue;
            }
            //and step_relation_id is null
            String sqlForLogic = "select * from t_md_logic_dataobj where owner_id = (select ta.param_value " +
                    " from T_ETL_TRANS_ATTRIBUTE ta  left join t_md_etl_trans_attribute tma on tma.id = ta.trans_attribute_id " +
                    " where ta.trans_id = :childId and tma.code = 'tableId' ) and (step_relation_id is null or step_relation_id = '')";
            Map dataObjMap = this.baseDao.sqlQueryForMap(sqlForLogic, this.addParam("childId", childTran.get("id")).param());
            DatasetTreeModel d = new DatasetTreeModel();
            d.setName((String) dataObjMap.get("name"));
            d.setLabel((String) dataObjMap.get("name"));
            d.setCode((String) dataObjMap.get("code"));
            d.setParent(false);
            d.setOpen(false);
            d.setOperateTime((String) dataObjMap.get("operate_time"));
            d.setOperateUser(StringUtils.isBlank(objName) ? null : objName);
            d.setId((String) dataObjMap.get("id"));
            d.setDbType((String) dataObjMap.get(DB_TYPE));
            d.setBelongType((String) dataObjMap.get(BELONG_TYPE));
            d.setLastUpdateTime((String) dataObjMap.get(LAST_UPDATE_TIME));
            d.setChildOuter(false);
            d.setpId((String) map.get("id"));
            String memo = (String) dataObjMap.get("memo");
            boolean flag = CharSequenceUtil.isNotBlank(memo) && memo.matches(".*[\\u4e00-\\u9fa5].*");
            String belongTableName = flag ? (memo + (String) dataObjMap.get("code")) : (String) dataObjMap.get("code");
            d.setBelongTableName(belongTableName);
            models.add(d);
        }
        return models;
    }


    protected String getCatalogId(String currentDataSetId) {
        String schemaId = "";
        if (StringUtils.isNotBlank(currentDataSetId)) {
//            ClassifierStat obj = classifierStatService.getClassifierStat(ClassifierStat.class, currentDataSetId);
            LogicDataObj obj = logicDataObjService.findLogicDataObjById(currentDataSetId);
            ClassifierStat classifierStat = logicDataObjService.getClassifierStatByLogic(obj);

            if (classifierStat == null) return "";
            schemaId = classifierStat.getOwner().getOwnerId();
        }
        return schemaId;
    }

    @Override
    public List<DatasetTreeModel> querySourceDatasetTree(String userId, String currentDataSetId, String ignoreTypes) {
        //获取同库的数据集
        String catalogId = getCatalogId(currentDataSetId);
        Map<String, Map<String, String>> dataMaps = dataSetTreeService.queryLogicRdb();

        List<String> authIds = new ArrayList<>();
        if (null != userId) {
            authIds = userService.getAllAuthId(userId);
        }
        //拿到数据源那一层的树
        List<DatasetTreeModel> treeModelList = dataWareTreeService.getDataWarehouseTreeCopy("", false, true, userId, authIds, "");
        Stack<DatasetTreeModel> stack = new Stack<>();
        List<String> idList = new ArrayList<>(20);
        Map<String, DatasetTreeModel> rtValue = new HashMap<>(20);
        DatasetTreeModel temp = null;
        for (DatasetTreeModel treeModel : treeModelList) {
            treeModel.setBelongTableName(treeModel.getCode());
            stack.push(treeModel);
        }
        List<String> ignoreTypeList = new ArrayList<>();
        if (StringUtils.isNotBlank(ignoreTypes)) {
            ignoreTypeList = new ArrayList<>(Arrays.asList(ignoreTypes.split(";")));
        }
        while (!stack.isEmpty()) {
            temp = stack.pop();
            if (temp.getInstanceType() != null && !ignoreTypeList.contains(temp.getInstanceType().toUpperCase())) {
                idList.add(temp.getId());
                rtValue.put(temp.getId(), temp);
            }
            for (DatasetTreeModel child : temp.getChildren()) {
                child.setBelongTableName(child.getCode());
                stack.push(child);
            }
        }
        Map<String, List<DatasetTreeModel>> allChildren = idList.size() > 0 ? getAllChildren(idList, userId) : new HashMap<>();
        for (Map.Entry<String, List<DatasetTreeModel>> tempValue : allChildren.entrySet()) {
            DatasetTreeModel treeModel = rtValue.get(tempValue.getKey());
            if (treeModel != null) {
                treeModel.setBelongTableName(treeModel.getCode());
                treeModel.setChildren(tempValue.getValue());
                treeModel.setIsParent(true);
            }
        }
//        for (DatasetTreeModel dataSource : treeModelList) {
//            List<DatasetTreeModel> children1 = dataSource.getChildren();
//            if (children1 != null) {
//                for (DatasetTreeModel child : children1) {
//                    List<DatasetTreeModel> children2 = child.getChildren();
//                    if (children2 != null) {
//                        for (DatasetTreeModel treeModel : children2) {
//                            //儿子目录
//                            List<DatasetTreeModel> children3 = treeModel.getChildren();
//                            if (children3 != null) {
//                                /*for (DatasetTreeModel model : children3) {
//                                        List<DatasetTreeModel> list = dataWareTreeService.queryDataBaseTable(model.getId(), userId, catalogId, dataMaps);
//                                        model.setIsParent(true);
//                                        model.setChildren(list);
//                                }*/
//                                //数据集底层使用创建视图方式，es和hbase无法创建视图
//                                for (int i = children3.size() - 1; i >= 0; i--) {
//                                    DatasetTreeModel model = children3.get(i);
//                                    if (!EXCLUDE_DATABASE.contains(model.getInstanceType().toUpperCase())) {
//                                        List<DatasetTreeModel> list = dataWareTreeService.queryDataBaseTable(model.getId(), userId, catalogId, dataMaps);
//                                        model.setIsParent(true);
//                                        model.setChildren(list);
//                                    } else {
//                                        children3.remove(i);
//                                    }
//                                }
//                            }
//                            //本级目录是否绑了资源
//                            List<DatasetTreeModel> children2list = dataWareTreeService.queryDataBaseTable(treeModel.getId(), userId, catalogId, dataMaps);
//                            if(children2list.size()>0){
//                                List<DatasetTreeModel> blankList=new ArrayList<>();
//                                treeModel.setChildren(blankList);
//                                for (DatasetTreeModel datasetTreeModel : children2list) {
//                                    treeModel.addChild(datasetTreeModel);
//                                }
//                            }
//                        }
//                    }
//                }
//            }
//        }

        return treeModelList;
    }

    private Map<String, List<DatasetTreeModel>> getAllChildren(List<String> idList, String userId) {
        StringBuilder sb = new StringBuilder();
        Map<String, Object> params = new HashMap<>();
        sb.append("SELECT ")
                .append("M .belong_type, M .code, M .db_type,M . ID,M .is_fast,M . NAME,M .operate_user_id ,M.global_code, r.element_id, M.operate_time,M.last_update_time")
                .append(" FROM ")
                .append("t_md_logic_data_relation r ")
                .append("LEFT JOIN t_md_logic_dataobj M ON M . ID = r.logic_data_obj_id ")
                .append("WHERE ")
                .append("r.element_id in " + SQLUtils.foreachSqlByIn(idList))
                .append(" and r.relation_type = '0' ")
                .append("and M.operate_user_id= :userId")
                .append(" order by M.operate_time desc ");
        params.put(USER_ID, userId);
        List<Map<String, String>> list = this.baseDao.sqlQueryForList(sb.toString(), params);
        List<DatasetTreeModel> datasetList = new ArrayList<>(list.size());
        for (Map<String, String> map : list) {
            DatasetTreeModel datasetTreeModel = new DatasetTreeModel();
            datasetTreeModel.setId(map.get("id").toString());
            datasetTreeModel.setIsParent(false);
            datasetTreeModel.setName(map.get("name").toString());
            datasetTreeModel.setBelongType(map.get(BELONG_TYPE).toString());
            datasetTreeModel.setDbType(map.get(DB_TYPE).toString());
            datasetTreeModel.setCode(map.get("code").toString());
            datasetTreeModel.setpId(map.get("element_id").toString());
            datasetTreeModel.setLabel(map.get("name") == null ? map.get("code").toString() : map.get("name").toString());
            datasetTreeModel.setGlobalCode(map.get("global_code").toString());
            datasetTreeModel.setOperateTime(map.get("operate_time"));
            datasetTreeModel.setLastUpdateTime(map.get(LAST_UPDATE_TIME));
            datasetTreeModel.setOperateUser(getAuthObjName(userId));
            datasetTreeModel.setBelongTableName(map.get("code"));
            datasetTreeModel.setLevel(4);
            datasetList.add(datasetTreeModel);
        }
        Map<String, List<DatasetTreeModel>> rtValue = datasetList.parallelStream()
                .collect(Collectors.groupingBy(DatasetTreeModel::getpId));
        return rtValue;
    }

    private String getAuthObjName(String userid) {
        String sql = "select obj_name from t_sys_auth_obj where id= :id";
        List authNameLists = this.baseDao.sqlQueryForList(sql, addParam("id", userid).param());
        return authNameLists.size() > 0 ? ((HashMap) authNameLists.get(0)).get("obj_name").toString() : "无";
    }

    @Override
    public List<DatasetTreeModel> mergeDataSetTree(List<DatasetTreeModel> selfDataSet, List<DatasetTreeModel> sourceDataSet) {
        List<DatasetTreeModel> allTreeModel = new ArrayList<>();
        DatasetTreeModel fatherDataSet = new DatasetTreeModel();
        fatherDataSet.setId("9c83a5d778935bffbbfe7e2d735dffa6");
        fatherDataSet.setName("全部");
        fatherDataSet.setCode("全部");
        fatherDataSet.setpId("-1");
        //源数据集
        List<DatasetTreeModel> sourceData = this.addHierarchyTreeModel(sourceDataSet);
        //自定义数据集
        selfDataSet.get(0).setpId(null);
        selfDataSet.get(0).setName("自定义数据集");
        selfDataSet.get(0).setCode("自定义数据集");
        selfDataSet.get(0).setLabel("自定义数据集");
        //合并起来
        List<DatasetTreeModel> sonDataTreeModels = new ArrayList<>();
        sonDataTreeModels.add(sourceData.get(0));
        sonDataTreeModels.add(selfDataSet.get(0));

        fatherDataSet.setChildren(sonDataTreeModels);

        allTreeModel.add(fatherDataSet);
        return allTreeModel;
    }

    /**
     * 源数据集这棵树添加顶层节点
     *
     * @return
     */
    public List<DatasetTreeModel> addHierarchyTreeModel(List<DatasetTreeModel> sourceDataSet) {
        DatasetTreeModel fatherDataSet = new DatasetTreeModel();
        fatherDataSet.setName("源数据集");
        fatherDataSet.setCode("源数据集");
        fatherDataSet.setLabel("源数据集");
        fatherDataSet.setId("bb699ad63e1352c8a5895366ae410a75");
        fatherDataSet.setChildren(sourceDataSet);
        List<DatasetTreeModel> treeModels = new ArrayList<>();
        treeModels.add(fatherDataSet);
        return treeModels;
    }


    private List<String> addFuncIds(List<DatasetTreeModel> datasetTreeModels, List<String> funcIds) {

        for (DatasetTreeModel datasetTreeModel : datasetTreeModels) {
            funcIds.add(datasetTreeModel.getId());
            for (DatasetTreeModel child : datasetTreeModel.getChildren()) {
                funcIds.add(child.getId());
            }
        }
        return funcIds;
    }

    @Override
    public DatasetTreeModel addDataSetTreeNode(String name, String busiId, String userId) {
        Assert.notNull(busiId, "父id不能为空！");
        BaseBusiClassify bsClassify = new BaseBusiClassify();
        BaseBusiClassify parentBc = (BaseBusiClassify) this.baseDao.load(BaseBusiClassify.class, busiId);

        bsClassify.setParentBc(parentBc);
        bsClassify.setCode(parentBc.getCode().equals("DATASET_DIR_MY") ? LogicDataSetConstant.LOGIC_BUSI_CLASSIFY_CODE : parentBc.getCode());
        bsClassify.setName(name);
        bsClassify.setOperateUserId(userId);

        bsClassify.setOwner(parentBc);
        this.baseDao.save(bsClassify);

        TSysFunc tSysFuncBase = new TSysFunc();
        tSysFuncBase.setFuncCode(bsClassify.getId());
        tSysFuncBase.setFuncName(name);
        tSysFuncBase.setFuncType("2");
        tSysFuncBase.setEnableState("1");
        tSysFuncBase.setDescription(userId);
        sysFuncService.store(tSysFuncBase);

        return getDataSetTreeModel(bsClassify, busiId);
    }

    private DatasetTreeModel getDataSetTreeModel(BaseBusiClassify bsClassify, String busiId) {
        DatasetTreeModel model = new DatasetTreeModel();
        model.setId(bsClassify.getId());
        model.setName(bsClassify.getName());
        model.setCode(bsClassify.getCode());
        model.setLabel(bsClassify.getName());
        model.setpId(busiId);
        model.setIsParent(true);
        model.setOpen(true);
        return model;
    }

    @Override
    public String updateNode(String classifyName, String classifyId, String userId, String oldName) {
        BaseBusiClassify bc = busiClassifyService.findBusiClassifyBy(classifyId);
        bc.setCode(classifyName);
        bc.setName(classifyName);
        this.baseDao.update(bc);
        List<TSysFunc> sysFuncs = gettSysFuncs(userId);
        Assert.notNull(oldName, "oldName不能为空！");
        for (TSysFunc sysFunc : sysFuncs) {
            if (oldName.equals(sysFunc.getFuncName())) {
                sysFunc.setFuncName(classifyName);
                sysAuthObjService.updateAuthObj(sysFunc);
                break;
            }
        }
        return "修改成功";
    }

    @Override
    public String deleteDataSetTreeNode(String nodeId, String userId) {
        PageInfo dataSetPage = getDataSetPage(nodeId, "", 1, defaultPageSize, userId, "", false);
        if (dataSetPage != null) {
            if (CollectionUtils.isNotEmpty(dataSetPage.getDataList())) {
                List<Map<String, String>> dataList = dataSetPage.getDataList();
                for (Map<String, String> dataSetMap : dataList) {
                    String dataSetId = dataSetMap.get("id");
                    if (StringUtils.isNotBlank(dataSetId))
                        deleteDataSet(dataSetId);
                }
            }
        }
        dataSetTreeService.deleteTreeNode(nodeId);
        String nodeName = getNodeName(nodeId);
        List<TSysFunc> sysFuncs = gettSysFuncs(userId);
        for (TSysFunc sysFunc : sysFuncs) {
            if (nodeName.equals(sysFunc.getFuncName())) {
                sysAuthObjService.deleteAuthObj(sysFunc);
                break;
            }
        }
        return "删除成功";
    }

    private String getNodeName(String nodeId) {
        String sql = "select name from t_md_busi_classify where id = :id";
        return this.baseDao.sqlQueryForValue(sql, addParam("id", nodeId).param());
    }

    @Override
    public PageInfo getDataSetPage(String id, String name, int page, int pageSize, String userId, String dbType, boolean isJurisdiction) {
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageIndex(page);
        pageInfo.setPageSize(isJurisdiction ? pageSize : -1);

        PageInfo logicDataObPageInfo;
        if (GlobalConstant.CommonProperties.FROM_SHARE_TOP_NODE_ID.equalsIgnoreCase(id)) {
            List<String> funcIds = userService.dataSetAuthFuncIdFromOthers(userId);
            logicDataObPageInfo = getShareLogicDataSetPage(funcIds, name, dbType, userId, pageInfo);
        } else if (GlobalConstant.CommonProperties.FROM_PERSONAL_TOP_NODE_ID.equalsIgnoreCase(id)) {
            Assert.fail("请选择他人空间或他人数据集的二级目录");
            logicDataObPageInfo = null;
        } else {

            List<DatasetTreeModel> datasetTreeModels = dataSetTreeService.queryDataSetTreeByDsType(false, userId, id, GlobalConstant.BusiProperties.POLICE_DATA_SOURCE_DIR);
            List<String> treeModelIds = new ArrayList<>();
            for (DatasetTreeModel model : datasetTreeModels) {
                treeModelIds.addAll(DatasetTreeModelUtils.getDatasetTreeModelAllId(model));
            }
            if (treeModelIds.contains(id)) {
                List<String> catalogIds = DatasetTreeModelUtils.getCatalogIds(id, datasetTreeModels);
                Map<String, String> pathMap = Maps.newHashMap();
                for (DatasetTreeModel datasetTreeModel : datasetTreeModels) {
                    pathMap.putAll(DatasetTreeModelUtils.getDatasetTreeModelAllPath(datasetTreeModel, datasetTreeModel.getName()));
                }
                logicDataObPageInfo = getLogicDataObPageInfo(id, name, pageInfo, userId, dbType, catalogIds, pathMap);
            } else {
                List<String> allAuthDatasetId = userService.getAllAuthDatasetId(userId);
                logicDataObPageInfo = logicDataObjService.getLogicDataObPageInfo(id, name, pageInfo, userId, dbType, allAuthDatasetId);
            }

        }
        //List<String> allAuthDatasetId = userService.dataSetAuthFromOthers(userId);
        //PageInfo logicDataObPageInfo = logicDataObjService.getLogicDataObPageInfo(id, name, pageInfo, userId, dbType, allAuthDatasetId);
        //PageInfo logicDataObPageInfo = getShareLogicDataSetPage(allAuthDatasetId,name,dbType,userId,pageInfo);
        return rebuildLogicObj("", logicDataObPageInfo, "");
    }

    @Override
    public PageInfo getDataSetPageForOtherPerson(String id, String name, int page, int pageSize, String operatorId, String dbType, boolean isJurisdiction) {
        List<DatasetTreeModel> datasetTreeModels = dataSetTreeService.queryDataSetTreeByDsType(false, operatorId, id, GlobalConstant.BusiProperties.POLICE_DATA_SOURCE_DIR);
        List<String> treeModelIds = new ArrayList<>();
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageIndex(page);
        pageInfo.setPageSize(isJurisdiction ? pageSize : -1);
        PageInfo logicDataObPageInfo;
        UserVo userVo = userService.queryUserById(operatorId);

        for (DatasetTreeModel model : datasetTreeModels) {
            treeModelIds.addAll(DatasetTreeModelUtils.getDatasetTreeModelAllId(model));
        }
        String type = "";
        if (treeModelIds.contains(id)) {
            List<String> catalogIds = DatasetTreeModelUtils.getCatalogIds(id, datasetTreeModels);
            Map<String, String> pathMap = Maps.newHashMap();
            for (DatasetTreeModel datasetTreeModel : datasetTreeModels) {
                pathMap.putAll(DatasetTreeModelUtils.getDatasetTreeModelAllPath(datasetTreeModel, datasetTreeModel.getName()));
            }
            logicDataObPageInfo = getLogicDataObPageInfo(id, name, pageInfo, operatorId, dbType, catalogIds, pathMap);
            type = "dsType";
        } else {
            List<String> allAuthDatasetId = userService.getAllAuthDatasetId(operatorId);
            logicDataObPageInfo = logicDataObjService.getLogicDataObPageInfo(id, name, pageInfo, operatorId, dbType, allAuthDatasetId);
            type = "catalog";
        }
        return rebuildLogicObj(userVo.getObjName(), logicDataObPageInfo, type);


    }

    private PageInfo rebuildLogicObj(String userName, PageInfo pageInfo, String type) {
        if (pageInfo == null) {
            return null;
        }
        String replaceStr = "";
        String targetStr = "";

        if ("dsType".equals(type)) {
            replaceStr = String.format("他人数据集/%s", userName);
            targetStr = "我的数据集";
        } else if ("catalog".equals(type)) {
            replaceStr = String.format("他人空间/%s", userName);
            targetStr = "我的空间";
        }
        for (Object object : pageInfo.getDataList()) {
            Map map = (Map) object;
            if (CharSequenceUtil.isNotBlank(replaceStr) && CharSequenceUtil.isNotBlank(targetStr)) {
                String path = (String) map.get("path");
                map.put("path", path.replace(targetStr, replaceStr));
            }
            String classifiedId = (String) map.get("owner_id");
            String operateUserId = (String) map.get("operate_user_id");
            String logicDataObjId = (String) map.get("id");
            String belongType = (String) map.get(BELONG_TYPE);
            //即席sql和快速分析的数据集无法从t_dw_table_mapping获取创建者
            String createUserId = getCreateUserId(classifiedId, logicDataObjId, belongType);
            map.put("user", getUserNameById(createUserId));
            map.put("operateUserName", getUserNameById(operateUserId));
        }
        return pageInfo;
    }

    private String getCreateUserId(String classifiedId, String logicDataObjId, String belongType) {
        String createUserId = "";
        if ("QUICK_SQL".equalsIgnoreCase(belongType) || "LOGIC".equalsIgnoreCase(belongType)) {
            if (StringUtils.isNotBlank(logicDataObjId)) {
                createUserId = getCreateUserByLoginDataSet(logicDataObjId);
            }
        } else {
            if (StringUtils.isNotBlank(classifiedId)) {
                createUserId = getCreateUserId(classifiedId);
            }
        }
        return createUserId;
    }

    private PageInfo getLogicDataObPageInfo(String id, String name, PageInfo pageInfo, String userId, String dbType, List<String> pids, Map<String, String> pathMap) {
        Map<String, Object> param = Maps.newHashMap();
        StringBuilder sql = new StringBuilder();
        sql.append("select a.id, a.name, a.code, a.operate_time, a.belong_type, a.db_type, a.operate_user_id, a.is_fast," +
                " a.owner_id, a.data_type, b.busi_classify_id as parentId,a.last_update_time " +
                " from t_md_logic_dataobj a  left join t_md_classify_element b  on  a.id = b.element_id " +
                " where a.belong_type is not null" + " and b.busi_classify_id is not null ");

        param.put(USER_ID, userId);
        sql.append(" and operate_user_id = (:userId)");
        sql.append(" and busi_classify_id in (:ids)");
        if (CollUtil.isNotEmpty(pids)) {
            param.put("ids", pids);
        } else {
            param.put("ids", id);
        }
        if (StringUtils.isNotBlank(dbType)) {
            sql.append(" and belong_type = :dbType");
            param.put("dbType", dbType);
        }
        if (StringUtils.isNotBlank(name)) {
            sql.append(" and (upper(name) like '%").append(name.toUpperCase()).append("%'");
            sql.append(" or upper(code) like '%").append(name.toUpperCase()).append("%')");
        }
        sql.append(" ORDER BY operate_time desc");
        PageInfo page = this.baseDao.sqlQueryForPage(sql.toString(), param, pageInfo);

        List<Map<String, Object>> collect = page.getDataList();
        if (pageInfo.getPageSize() == -1)
            collect = collect.stream().sorted(Comparator.comparing(this::compareWithTime, Comparator.reverseOrder())).collect(Collectors.toList());
        else
            collect = collect.stream().sorted(Comparator.comparing(this::compareWithTime, Comparator.reverseOrder())).limit(pageInfo.getPageSize()).collect(Collectors.toList());
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        for (Map<String, Object> rtMap : collect) {
            try {
                rtMap.put("operate_time", rtMap.get("operate_time") != null ? formatter.format(formatter.parse(rtMap.get("operate_time").toString())) : "");
            } catch (ParseException e) {
                log.error(e.getMessage(), e);
            }
            rtMap.put("path", pathMap.get(rtMap.get("parentid")));
        }
        page.setDataList(collect);
        return page;
    }

    private Date compareWithTime(Map<String, Object> map) {
        String time = String.valueOf(map.get("operate_time") == null ? "1998-09-22 11:11:11" : map.get("operate_time"));
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            return sdf.parse(time);
        } catch (ParseException e) {
            return null;
        }
    }

    @Override
    public PageInfo getDataSetSharePage(String id, String name, int page, int pageSize, String userId, String dbType, boolean isJurisdiction) {
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageIndex(page);
        pageInfo.setPageSize(isJurisdiction ? pageSize : -1);

        PageInfo logicDataObPageInfo;
        List<String> funcIds = userService.dataSetAuthFuncIdFromOthers(userId);
        if (GlobalConstant.CommonProperties.FROM_SHARE_TOP_NODE_ID.equalsIgnoreCase(id)) {
            logicDataObPageInfo = getShareLogicDataSetByPage(funcIds, name, dbType, userId, pageInfo, null);
        } else {
            logicDataObPageInfo = getShareLogicDataSetByPage(funcIds, name, dbType, userId, pageInfo, id);
        }

        return rebuildLogicObj("", logicDataObPageInfo, "");
    }

    private PageInfo getShareLogicDataSetByPage(List<String> funcIds, String name, String dbType, String userId,
                                                PageInfo pageInfo, String catalogId) {


        StringBuilder sql = new StringBuilder();
        sql.append(GlobalConstant.DataSetSQL.SHARE_BASE_SQL);

        List<DatasetTreeModel> datasetTreeModels = dataSetTreeService.queryDataSetTreeByDsType(false, userId, catalogId, GlobalConstant.BusiProperties.POLICE_DATA_SOURCE_DIR);
        Map<String, Object> sqlConditionMap = getShareCondition(name, dbType, catalogId, datasetTreeModels, funcIds, userId);
        if (sqlConditionMap.containsKey("sql")) {
            sql.append(" where 1=1 ").append(sqlConditionMap.get("sql"));
            sqlConditionMap.remove("sql");
        }

        sql.append(" ORDER BY operate_time desc ");
        Map<String, String> pathMap = Maps.newHashMap();
        for (DatasetTreeModel datasetTreeModel : datasetTreeModels.get(0).getChildren()) {
            pathMap.putAll(DatasetTreeModelUtils.getDatasetTreeModelAllPath(datasetTreeModel, datasetTreeModel.getName()));
        }

        PageInfo pageInfo1 = this.baseDao.sqlQueryForPage(sql.toString(), sqlConditionMap, pageInfo);
        List<Map<String, Object>> dataList = pageInfo1.getDataList();
        for (Map<String, Object> map : dataList) {
            map.put("path", String.format("来自分享/%s", pathMap.get(MapUtil.getStr(map, "pid"))));
            Timestamp operateTime = (Timestamp) map.get("operate_time");
            String operateTimeStr = "";
            if (operateTime != null) {
                long time = operateTime.getTime();
                SimpleDateFormat sfd = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                Date d = new Date(time);
                operateTimeStr = sfd.format(d);

            }
            map.put("operate_time", operateTimeStr);
        }
        pageInfo1.setDataList(dataList);
        return pageInfo1;
    }

    private Map<String, Object> getShareCondition(String name, String dbType, String catalogId, List<DatasetTreeModel> datasetTreeModels, List<String> funcIds, String userId) {

        Map<String, Object> params = new HashMap<>();
        StringBuilder sql = new StringBuilder();

        if (StringUtils.isNotBlank(name)) {
            sql.append(" and upper(name) like '%").append(name.toUpperCase()).append("%'");
        }
        if (StringUtils.isNotBlank(dbType)) {
            sql.append(" and upper(belong_Type) = :dbType");
            params.put("dbType", dbType.toUpperCase());
        }
        if (CharSequenceUtil.isNotBlank(catalogId)) {
            List<String> catalogIds = new ArrayList<>();
            catalogIds.add(catalogId);
            List<DatasetTreeModel> modelList = new ArrayList<>();

            for (DatasetTreeModel datasetTreeModel : datasetTreeModels) {
                modelList.addAll(DatasetTreeModelUtils.getDatasetTreeModelByCatalogId(datasetTreeModel, catalogId));
            }
            for (DatasetTreeModel datasetTreeModel : modelList) {
                catalogIds.addAll(DatasetTreeModelUtils.getDatasetTreeModelAllId(datasetTreeModel));
            }
            params.put("catalogIds", catalogIds);
            sql.append(" and pid in(:catalogIds)");
        }
        if (sql.length() > 0) {
            params.put("sql", sql.toString());
        }
        if (CollUtil.isEmpty(funcIds)) {
            params.put("ids", "");
            params.put("funIds", "");
        } else {
            params.put("ids", funcIds);
            params.put("funIds", funcIds);
        }
        params.put(USER_ID, userId);
        params.put("authUserId", userId);
        return params;
    }

    private String getUserNameById(String id) {
        if (CharSequenceUtil.isNotBlank(id)) {
            Map<String, String> params = Maps.newHashMap();
            params.put("id", id);
            TSysAuthUser tSysAuthUser = (TSysAuthUser) sysAuthUserService.query(params);
            return tSysAuthUser.getObjName();
        }
        return "无";
    }

    @Override
    public void updateDataSet(DataSetOperationVo dataSetOperationVo) {
        LogicDataObj logicDataObj = logicDataObjService.findLogicDataObjById(dataSetOperationVo.getId());
        logicDataObj.setName(dataSetOperationVo.getName());

        logicDataObjService.updateLogicDataObj(logicDataObj);
    }

    @Override
    public void deleteDataSet(String id) {

        String relationId = editService.getRelationId(id);
        if (StringUtils.isNotBlank(relationId)) {
            //删除数据库里的关系和步骤
            dataSetStepService.deleteByRelation(relationId);
        }
        //删除t_md_logic_data_relation的关系
        logicDataObjService.deleteLogicDataRelation(id);
        //清空数据集
        logicDataObjService.deleteLogicDataObjById(id);
        //删除t_md_classify_element的数据
        deleteClassifyElementByElementId(id);
    }

    private String getCreateUserId(String id) {
        String sql = "select operate_user_id from t_dw_table_mapping where classifier_stat_id=(select id from t_md_classifier_stat where id=:id)";
        return this.baseDao.sqlQueryForValue(sql, addParam("id", id).param());
    }

    private String getCreateUserByLoginDataSet(String id) {
        LogicDataObj dataObj = logicDataObjService.findLogicDataObjById(id);
        if (dataObj != null) {
            return dataObj.getUserId();
        }
        return "";
    }

    private void deleteClassifyElementByElementId(String id) {
        String deleteSQL = "DELETE FROM t_md_classify_element where  element_id = :id";
        this.baseDao.executeSqlUpdate(deleteSQL, addParam("id", id).param());
    }

    public String checkDataSetByVisual(String id) {
        boolean flag = logicDataObjService.checkDataSetByVisual(id);
        return flag ? "该数据集被可视化使用，是否确认删除？" : "success";
    }

    @Override
    public DataSetOperationVo queryDataSetById(String id) {
        LogicDataObj logicDataObj = logicDataObjService.findLogicDataObjById(id);
        DataSetOperationVo dataSetOperationVo = new DataSetOperationVo();
        dataSetOperationVo.setId(logicDataObj.getId());
        dataSetOperationVo.setCode(logicDataObj.getCode());
        dataSetOperationVo.setName(logicDataObj.getName());
        return dataSetOperationVo;
    }

    @Override
    public List<String> getAllLogicDataObj() {
//        List<LogicDataObj> logicDataObjs =
//        List<DataSetOperationVo> dataSetOperationVos = new ArrayList<>();
//        for(LogicDataObj logicDataObj: logicDataObjs){
//            DataSetOperationVo dataSetOperationVo = new DataSetOperationVo();
//            dataSetOperationVo.setId(logicDataObj.getId());
//            dataSetOperationVo.setCode(logicDataObj.getCode());
//            dataSetOperationVo.setName(logicDataObj.getName());
//            dataSetOperationVos.add(dataSetOperationVo);
//        }
        return logicDataObjService.getAllLogicDataObj();
    }

    @Override
    public List<ServicePublishColumnVo> getDataSetColumn(String id) {
        List<LogicDataColumn> columns = logicDataColumnService.findLogicDataColumnsByLogicDataObjId(id);
        List<ServicePublishColumnVo> columnVos = new ArrayList<>();
        for (LogicDataColumn column : columns) {
            String alias = column.getAlias();
            ServicePublishColumnVo columnVo = new ServicePublishColumnVo();
            columnVo.setId(column.getId());
            columnVo.setCode(StringUtils.isNotBlank(alias) ? alias : column.getCode());
            columnVo.setRealCode(column.getCode());
            columnVo.setName(column.getName());
            columnVo.setDataType(column.getDataType().getCode());
            columnVo.setMemo(column.getMemo());
            columnVos.add(columnVo);
        }
        return columnVos;
    }

    @Override
    public List<ServicePublishColumnVo> getDataSetColumnList(String id) {
        List<LogicDataColumn> columns = logicDataColumnService.findLogicDataColumnsByLogicDataObjId(id);
        List<ServicePublishColumnVo> columnVos = new ArrayList<>();
        for (LogicDataColumn column : columns) {
            ServicePublishColumnVo columnVo = new ServicePublishColumnVo();
            columnVo.setId(column.getId());
            columnVo.setCode(column.getCode());
            columnVo.setName(column.getName());
            columnVo.setDataType(column.getDataType().getCode().toLowerCase());
            columnVo.setMemo(column.getMemo());
            columnVos.add(columnVo);
        }
        return columnVos;
    }


    @Override
    public void setCacheLevel(String id, String cacheLevel) {
        LogicDataObj logicDataObj = logicDataObjService.findLogicDataObjById(id);
        Assert.notNull(logicDataObj, "数据集列不能为空!");
        logicDataObj.setCacheLevel(cacheLevel);
        logicDataObjService.updateLogicDataObj(logicDataObj);
    }

    //判断该用户 是否已有此层次名称
    @Override
    public boolean isRepeatName(String name, String userId) {
        List<TSysFunc> tSysFuncs = gettSysFuncs(userId);
        List<String> names = tSysFuncs.stream().map(s -> s.getFuncName()).collect(Collectors.toList());

        return names.indexOf(name) == -1 ? false : true;
    }

    private List<TSysFunc> gettSysFuncs(String userId) {
        Map<String, String> params = Maps.newHashMap();
        params.put("description", userId);
        params.put("func_type", "2");
        return sysFuncService.queryList(params);
    }

    @Override
    public String accreditDataSet(List<String> ids, String treeNodeId, String userId) {
        SaveLogicDataObjectVo dataObjectVo = null;
        Boolean isdwInstance = false;
        if (StringUtils.isNotBlank(treeNodeId)) {
            isdwInstance = isDwInstance(treeNodeId);
        }
        if (isdwInstance) {//如果是数据源id的话，走另外一套
            //保存数据集
            TypeMapping typeMapping = (TypeMapping) beanFactory.getBean("com.code.plugin.db.DefTypeMapping");
            dataObjectVo = this.logicDataObjService.saveClassifierStatToLogicDataObject(ids, userId, treeNodeId, typeMapping);
            //保存目录树和数据集的关系
            /*String sql = getInsertDatasetRelationSQL(dataObjectVo.getLogicDataObjs(), treeNodeId);
            executeInsertDatasetRelationSQL(sql);*/
            for (LogicDataObj logicDataObj : dataObjectVo.getLogicDataObjs()) {
                LogicDataRelation logicDataRelation = new LogicDataRelation();
                logicDataRelation.setLogicDataObjId(logicDataObj.getId());
                logicDataRelation.setElementId(treeNodeId);
                logicDataRelation.setRelationType("0");
                logicDataRelationService.saveOrUpdate(logicDataRelation);
            }

        } else {
            //保存数据集
            dataObjectVo = this.logicDataObjService.saveClassifierStatToLogicDataObject(ids, userId, treeNodeId);
            //保存目录树和数据集的关系
            String sql = getInsertSQL(dataObjectVo.getLogicDataObjs(), treeNodeId);
            this.baseDao.executeSqlUpdate(sql);
        }

        //创建数据集sql
        for (LogicDataObj logicDataObj : dataObjectVo.getLogicDataObjs()) {
            try {
                //editService.registerTable(logicDataObj, logicDataObj.getSql(), true);
                editService.createLogicDatObjSearchSql(logicDataObj.getId());
            } catch (Exception e) {
//             log.error(e.getMessage(),e);
                log.error("发生异常---" + e.getMessage(), e);
            }
        }


        return dataObjectVo.getMsg();
    }


    public void executeInsertDatasetRelationSQL(String sql) {
        this.baseDao.executeSqlUpdate(sql);
    }

    private Boolean isDwInstance(String treeNodeId) {
        Assert.notNull(treeNodeId, "节点id不能为空！");
        String sql = "select name from t_md_dw_db_instance where id = (:id)";
        List id = this.baseDao.sqlQueryForList(sql, addParam("id", treeNodeId).param());
        return id.size() == 0 ? false : true;
    }

    private String getInsertSQL(List<LogicDataObj> logicDataObjList, String dataSetTreeNodeId) {
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append("INSERT INTO T_MD_CLASSIFY_ELEMENT VALUES ");
        for (int i = 0; i < logicDataObjList.size(); i++) {
            stringBuffer.append("(");
            stringBuffer.append("'" + logicDataObjList.get(i).getId() + "'");
            stringBuffer.append(",");
            stringBuffer.append("'" + dataSetTreeNodeId + "'");
            if (i == logicDataObjList.size() - 1) {
                stringBuffer.append(")");
            } else {
                stringBuffer.append("),");
            }
        }
        return stringBuffer.toString();
    }

    private String getInsertDatasetRelationSQL(List<LogicDataObj> logicDataObjList, String dataSetTreeNodeId) {
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append("INSERT INTO t_md_logic_data_relation VALUES ");
        for (int i = 0; i < logicDataObjList.size(); i++) {
            stringBuffer.append("(");
            stringBuffer.append("'" + logicDataObjList.get(i).getId() + "'");
            stringBuffer.append(",");
            stringBuffer.append("'" + dataSetTreeNodeId + "'");
            stringBuffer.append(",");
            stringBuffer.append("'0'");
            if (i == logicDataObjList.size() - 1) {
                stringBuffer.append(")");
            } else {
                stringBuffer.append("),");
            }
        }
        return stringBuffer.toString();
    }


    @Override
    public boolean checkHasDataSet(String id) {
        String sql = "SELECT COUNT(1) FROM T_MD_CLASSIFY_ELEMENT WHERE BUSI_CLASSIFY_ID = :id";
        int count = Integer.parseInt(this.baseDao.sqlQueryForValue(sql, addParam("id", id).param()));
        return count > 0 ? true : false;
    }

    @Override
    public String createLogicDataSet(String id, String name, String userId) {
        String dataSetId = saveLgoicDataSet(name, userId);
        saveRelevancy(id, dataSetId);
        return dataSetId;
    }

    @Override
    public List<DataSet> getAllDataSetByUserId(String userId) {
        String hql = "FROM BaseBusiClassify where operateUserId = :userId  and   ( code = 'DATASET_DIR_MY' or code = 'DATASET_DIR' ) ";
        List<BaseBusiClassify> baseBusiClassify = this.baseDao.queryForList(hql, addParam(USER_ID, userId).param());
        List<String> baseBusiClassifyIds = baseBusiClassify.stream().map(s -> s.getId()).collect(Collectors.toList());

        List<DataSet> dataSetOperationVos = new ArrayList<>();
        for (String id : baseBusiClassifyIds) {
            List<Map> logicDatas = logicDataObjService.getLogicDataObjByClassifyId(id);
            for (Map map : logicDatas) {
                DataSet dataSetOperationVo = new DataSet();
                dataSetOperationVo.setId(map.get("id").toString());
                dataSetOperationVo.setName(map.get("name") == null ? map.get("code").toString() : map.get("name").toString());
                dataSetOperationVo.setCode(map.get("code").toString());
                dataSetOperationVo.setDbType(map.get("dbtype") == null ? "" : map.get("dbtype").toString());
                List<LogicDataColumn> logicDataColumns = logicDataColumnService.findLogicDataColumnsByLogicDataObjId(map.get("id").toString());
                List<Field> fields = new ArrayList<>();
                for (LogicDataColumn logicDataColumn : logicDataColumns) {
                    fields.add(getField(logicDataColumn));
                }
                dataSetOperationVo.setFields(fields);
                dataSetOperationVos.add(dataSetOperationVo);
            }
        }
        return dataSetOperationVos;
    }

    private Field getField(LogicDataColumn logicDataColumn) {
        Field field = new Field();
        String name = CharSequenceUtil.isNotBlank(logicDataColumn.getName()) ? logicDataColumn.getName() : logicDataColumn.getCode();
        field.setId(logicDataColumn.getId());
        field.setName(name);
        field.setCode(logicDataColumn.getCode());
        field.setIndexType(logicDataColumn.getIndexType());
        return field;
    }

    private void saveRelevancy(String id, String dataSetId) {
        String sql = "INSERT INTO T_MD_CLASSIFY_ELEMENT VALUES (':dataSetId',':id')";
        this.baseDao.executeSqlUpdate(sql, addParam("dataSetId", dataSetId).addParam("id", id).param());
    }

    private String saveLgoicDataSet(String name, String userId) {
        LogicDataObj logicDataObj = new LogicDataObj();
        logicDataObj.setName(name);
        logicDataObj.setCode(name);
        logicDataObj.setUserId(userId);
        logicDataObj.setType("LogicDataObj");
        logicDataObj.setIsFast("0");
        this.baseDao.save(logicDataObj);
        return logicDataObj.getId();
    }

    @Override
    public void moveLogicDataSet(String dataSetID, String treeNodeId, String dTreeNodeId) {
        checkTreeNode(dataSetID, treeNodeId, dTreeNodeId);
        moveDataObj(dataSetID, treeNodeId, dTreeNodeId);
    }

    /**
     * @param dataSetID   数据集id
     * @param treeNodeId  源id
     * @param dTreeNodeId 目标id
     */
    private void moveDataObj(String dataSetID, String treeNodeId, String dTreeNodeId) {
        String sql = "update t_md_classify_element set busi_classify_id = :nodeid where element_id = :id and busi_classify_id=:catalogId";
        this.baseDao.executeSqlUpdate(sql, addParam("nodeid", dTreeNodeId)
                .addParam("catalogId", treeNodeId)
                .addParam("id", dataSetID).param());
    }

    /**
     * @param dataSetID   数据集id
     * @param treeNodeId  源id
     * @param dTreeNodeId 目标id
     */
    private void checkTreeNode(String dataSetID, String treeNodeId, String dTreeNodeId) {
        Assert.notNull(dataSetID, "数据集ID不能为空");
        Assert.notNull(dTreeNodeId, "请选择目录树节点！");

        String sql = "select busi_classify_id from t_md_classify_element where element_id = :id and busi_classify_id=:catalogId";
        String value = this.baseDao.sqlQueryForValue(sql, addParam("id", dataSetID)
                .addParam("catalogId", treeNodeId).param());
        if (CharSequenceUtil.isNotBlank(value) && value.equals(dTreeNodeId)) {
            Assert.fail("请选择不同的目录树！");
        }
    }

    @Override
    public void changeIsFast(String dataSetId, boolean ifFase) {
        LogicDataObj logicDataObj = (LogicDataObj) this.baseDao.get(LogicDataObj.class, dataSetId);
        String fast = ifFase ? LogicDataSetConstant.FAST : LogicDataSetConstant.NOT_FAST;
        logicDataObj.setIsFast(fast);
        this.baseDao.saveOrUpdate(logicDataObj);
        //更新spark
        changeSpark(logicDataObj.getGlobalCode(), fast);
    }

    /**
     * 获取树的第四层节点  封装成map  key:数据源id,value:数据集id集合
     *
     * @param treeModelList
     * @return
     */
    @Override
    public List<Map<String, List<String>>> treelist(List<DatasetTreeModel> treeModelList) {
        List<DatasetTreeModel> datasetTreeModels = new ArrayList<>();
        for (DatasetTreeModel datasetTreeModel : treeModelList) {
            datasetTreeModels.addAll(DatasetTreeModelUtils.getDatasetTreeModelAtLevel(datasetTreeModel, 4));
        }
        Map<String, List<String>> flatMap = datasetTreeModels.stream().collect(Collectors.groupingBy(DatasetTreeModel::getpId,
                Collectors.mapping(DatasetTreeModel::getDataObjId, Collectors.toList())));
        List<Map<String, List<String>>> treelist = new ArrayList<>();
        for (Map.Entry<String, List<String>> entry : flatMap.entrySet()) {
            Map<String, List<String>> map = Maps.newHashMap();
            map.put(entry.getKey(), entry.getValue());
            treelist.add(map);
        }
        return treelist;
    }

    @Override
    public List<String> findAuthRoleAndUserbyPhysicalDatasetId(String datasetId) {
        String sql = "SELECT a.obj_id,r.from_obj_id as userId FROM t_sys_auth_obj_func A LEFT JOIN t_sys_auth_obj_rel r on a.obj_id=r.to_obj_id WHERE A.func_code=:datasetId";
        List<Map> datasetId1 = this.baseDao.sqlQueryForList(sql, addParam(DATASET_ID, datasetId).param());
        List<String> userIds = new ArrayList<String>();
        for (Map map : datasetId1) {
            String userId = "";
            if (map.get("userid") != null) {
                userId = map.get("userid").toString();
            } else {
                userId = map.get("obj_id").toString();
            }
            userIds.add(userId);
        }
        return userIds;
    }


    @Override
    public boolean checkDataSetName(String checkName, String id) {
        String checkSQL = "select name from t_md_logic_dataobj where id in (select element_id from t_md_classify_element where busi_classify_id = :id)";
        List<Map<String, String>> query = this.baseDao.sqlQueryForList(checkSQL, addParam("id", id).param());
        boolean res = false;
        for (Map<String, String> name : query) {
            if (checkName.equals(name.get("name"))) {
                res = true;
                break;
            }
        }
        return res;
    }

    @Override
    public boolean checkDataSetNameByDataSetId(String name, String dataSetId, String userId) {
        String checkSQL = "select name from  t_md_logic_dataobj where id in ( " +
                "select  element_id  from  t_md_classify_element  where  busi_classify_id in ( " +
                "select busi_classify_id from  t_md_classify_element a  inner join t_md_busi_classify b" +
                " on a.busi_classify_id = b.id" +
                "  where  element_id = :dataSetId  and b.operate_user_id =:userId )) and id !=:id";
        Map<String, Object> param = addParam("dataSetId", dataSetId).addParam("id", dataSetId).addParam("userId", userId).param();
        List<Map<String, String>> query = this.baseDao.sqlQueryForList(checkSQL, param);
        boolean res = false;
        for (Map<String, String> map : query) {
            if (name.equals(map.get("name"))) {
                res = true;
                break;
            }
        }
        return res;
    }


    @Override
    public Boolean isAccreditDataSet(String userId, String datasetId) {
        String sql = "SELECT count(1) as num from t_md_logic_data_relation where relation_type='0' and logic_data_obj_id in(select id from t_md_logic_dataobj where operate_user_id=:userId and owner_id=:datasetId)";
        List<HashMap> list = this.baseDao.sqlQueryForList(sql, addParam(USER_ID, userId).addParam(DATASET_ID, datasetId).param());
        return "0".equals(list.get(0).get("num").toString()) ? false : true;
    }

    @Override
    public String getDataSetDbType(String datasetId) {
        String sql = "select db_type from t_md_classifier_stat where id='" + datasetId + "'";
        List list = this.baseDao.sqlQueryForList(sql);

        return list.size() > 0 ? ((HashMap) list.get(0)).get(DB_TYPE).toString() : "";
    }

    @Override
    public Boolean isRepeatDataSet(String userId, String datasetId) {
        String sql = "SELECT count(1) as num from t_md_logic_data_relation where relation_type='0' and logic_data_obj_id in(select id from t_md_logic_dataobj where operate_user_id=:userId and owner_id=:datasetId)";
        List<HashMap> list = this.baseDao.sqlQueryForList(sql, addParam(USER_ID, userId).addParam(DATASET_ID, datasetId).param());
        return Integer.parseInt(list.get(0).get("num").toString()) > 1 ? true : false;
    }

    @Override
    public void deleteRepeatDataSet(String userId, String datasetId) {
        deleteRepeatLogicDataSetRelation(userId, datasetId);
        deleteRepeatLoicDataSet(userId, datasetId);
    }

    private void deleteRepeatLoicDataSet(String userId, String datasetId) {
        String sql = "DELETE from t_md_logic_dataobj where id in(select id from t_md_logic_dataobj where operate_user_id=:userId and owner_id=:datasetId)";
        this.baseDao.executeSqlUpdate(sql, addParam(USER_ID, userId).addParam(DATASET_ID, datasetId).param());
    }

    private void deleteRepeatLogicDataSetRelation(String userId, String datasetId) {
        String sql = "DELETE from t_md_logic_data_relation where relation_type='0' and logic_data_obj_id in(select id from t_md_logic_dataobj where operate_user_id=:userId and owner_id=:datasetId)";
        this.baseDao.executeSqlUpdate(sql, addParam(USER_ID, userId).addParam(DATASET_ID, datasetId).param());
    }

    private void changeSpark(String globalCode, String fase) {
        ISparkClientService sparkClient = SparkConsumer.getSparkClient(sparkDubboAddress);
        if (LogicDataSetConstant.FAST.equals(fase)) {
            sparkClient.cacheTable(globalCode);
        } else {
            sparkClient.unCacheTable(globalCode);
        }
    }


    @Override
    public void deleteLogicDataObjByOwnerId(String ownerId) {
        String hql = "delete LogicDataObj where owner.id = :id ";
        this.baseDao.executeUpdate(hql, addParam("id", ownerId).param());
    }

    @Override
    public void updateTableMappingOperateId(String classifierId) {
        String hql = "update t_dw_table_mapping set operate_user_id=:userId where classifier_stat_id=:classifierId";
        this.baseDao.executeSqlUpdate(hql, addParam("classifierId", classifierId)
                .addParam("userId", GlobalConstant.UserProperties.DC_SUPER_ID)
                .param());
    }

    @Override
    public String getDwDbIdByClassifierStatId(String classifierStatId) {
        String sql = "select dw_db_id from t_dw_table_mapping where classifier_stat_id='" + classifierStatId + "'";
        List list = this.baseDao.sqlQueryForList(sql);

        return list.size() > 0 ? ((HashMap) list.get(0)).get("dw_db_id").toString() : "";
    }

    @Override
    public ColumnDataSetVo getClassifyFeature(String classifierStatId) {
        TypeMapping typeMapping = (TypeMapping) beanFactory.getBean("com.code.plugin.db.DefTypeMapping");
        ClassifierStat classifierStat = null;
        RdbDataObj obj = (RdbDataObj) this.baseDao.get(RdbDataObj.class, classifierStatId);
        if (obj != null) {
            classifierStat = obj;
        } else {
            classifierStat = (ClassifierStat) this.baseDao.get(ClassifierStat.class, classifierStatId);
        }
        if (classifierStat == null) {
            return null;
        }
        Map<String, List<LogicSyncColumn>> parentMap = new HashMap<>();
        List<LogicDataColumn> columns = logicDataColumnService.findLogicDataColumn(classifierStat);
        for (LogicDataColumn column : columns) {
            //获取获取字段来源的数据集
            NonStandardType type = null;
            //为什么有这个判断，还有下面的三目判断，因为大小写的问题，本质上解决还是去改那个包，全部统一标准，但是先不折腾，后面有需要再改一波，先记一下！
            if ("Hive".equalsIgnoreCase(classifierStat.getDbType()) || "tbase".equalsIgnoreCase(classifierStat.getDbType())) {
                type = new NonStandardType(classifierStat.getDbType() + "." + column.getDataType().getCode(), column.getDataType().getCode(),
                        0, 0);
            } else {
                if (classifierStat.getDbType().equalsIgnoreCase("vertica")) {
                    type = new NonStandardType("Vertica." + column.getDataType().getCode(), column.getDataType().getCode(), 0, 0);
                } else {
                    type = new NonStandardType(
                            classifierStat.getDbType().equals("hwmpp") ? "hwmpp." + column.getDataType().getCode() : classifierStat.getDbType().toUpperCase() + "." + column.getDataType().getCode(), column.getDataType().getCode(),
                            0, 0
                    );
                }
            }
            StandardType stdType = typeMapping.trans(type);
            String dataCode = stdType.getCode();
            String dataTypeId = getDataTypeIdByCode(dataCode);
            if (LogicDataObjectUtil.DIMENSION.equals(column.getIndexType())) {
                List<LogicSyncColumn> dimension = parentMap.get(LogicDataSetConstant.DIMENSION);
                if (dimension == null) {
                    dimension = Lists.newArrayList();
                }
                LogicSyncColumn logicColumnInfoVo = getLogicColumnInfoVo(column, column.getId());
                logicColumnInfoVo.setDataTypeId(dataTypeId);
                logicColumnInfoVo.setDisplayTypeId(dataTypeId); //保留以前的数据类型
                dimension.add(logicColumnInfoVo);
                parentMap.put(LogicDataSetConstant.DIMENSION, dimension);
            } else {
                List<LogicSyncColumn> measure = parentMap.get(LogicDataSetConstant.MEASURE);
                if (measure == null) {
                    measure = Lists.newArrayList();
                }
                LogicSyncColumn logicColumnInfoVo = getLogicColumnInfoVo(column, column.getId());
                logicColumnInfoVo.setDataTypeId(dataTypeId);
                logicColumnInfoVo.setDisplayTypeId(dataTypeId);
                measure.add(logicColumnInfoVo);
                parentMap.put(LogicDataSetConstant.MEASURE, measure);
            }
        }
        List<LogicSyncColumn> pks = getPks(obj);
        ClassifierStat objById = logicDataObjService.findClassifier(classifierStatId);
        Assert.notNull(objById, "数据集来源被删除！");
        ColumnDataSetVo columnDataSetVo = new ColumnDataSetVo();
        columnDataSetVo.setName(objById.getName());
        columnDataSetVo.setCode(objById.getCode());
        columnDataSetVo.setId(objById.getId());
        //字段信息
        columnDataSetVo.setDimension(parentMap.get(LogicDataSetConstant.DIMENSION));
        columnDataSetVo.setMeasure(parentMap.get(LogicDataSetConstant.MEASURE));
        columnDataSetVo.setPks(pks);
        return columnDataSetVo;
    }

    private List<LogicSyncColumn> getPks(RdbDataObj obj) {
        List<LogicSyncColumn> pks = null;
        if (obj != null) {
            pks = new ArrayList<>();
            Set<RdbUniqueKey> rdbUniqueKeys = obj.getRdbUniqueKeys();
            for (RdbUniqueKey rdbUniqueKey : rdbUniqueKeys) {
                Set<RdbUkDataColumn> ukDatacolumns = rdbUniqueKey.getUkDatacolumns();
                for (RdbUkDataColumn ukDatacolumn : ukDatacolumns) {
                    RdbDataColumn rdbDatacolumn = ukDatacolumn.getId().getRdbDatacolumn();
                    LogicSyncColumn logicColumnInfoVo = new LogicSyncColumn();
                    logicColumnInfoVo.setId(rdbDatacolumn.getId());
                    logicColumnInfoVo.setCode(rdbDatacolumn.getCode());
                    pks.add(logicColumnInfoVo);
                }
            }
        }
        return pks;
    }

    private LogicSyncColumn getLogicColumnInfoVo(LogicDataColumn logicDataColumn, String columnId) {
        LogicSyncColumn columnInfoVo = new LogicSyncColumn();
        columnInfoVo.setId(columnId);
        columnInfoVo.setBelongParentId(logicDataColumn.getBelongParentId());
        columnInfoVo.setFuncExp(logicDataColumn.getFuncExp());
        columnInfoVo.setColumnAlias(StringUtils.isBlank(logicDataColumn.getAlias()) ? logicDataColumn.getCode() : logicDataColumn.getAlias());
        columnInfoVo.setDataTypeId(logicDataColumn.getDataType().getId());
        columnInfoVo.setName(logicDataColumn.getName());
        columnInfoVo.setNumberFormat(logicDataColumn.getNumberFormat());
        columnInfoVo.setFormat(logicDataColumn.getFormat());
        columnInfoVo.setCode(logicDataColumn.getCode());
        columnInfoVo.setIndexType(logicDataColumn.getIndexType());
        columnInfoVo.setMemo(logicDataColumn.getMemo());
        return columnInfoVo;
    }

    public String saveDataSet(List<String> ids, String treeNodeId, String dsTypeTreeNodeId, String userId, List<LogicDataSetColumnVo> column, String name, boolean isMultiTableDataSet) {
        SaveLogicDataObjectVo dataObjectVo;

        //保存数据集
        if (isMultiTableDataSet) {
            dataObjectVo = this.logicDataObjService.saveMultiClassifierStatToLogicDataObject(ids, userId, treeNodeId, column, name);
        } else {
            dataObjectVo = this.logicDataObjService.saveDataObjectToLogicDataObjectCopy(ids, userId, treeNodeId, column, name);
        }
        //保存目录树和数据集的关系
        String sql = getInsertSQL(dataObjectVo.getLogicDataObjs(), treeNodeId);
        this.baseDao.executeSqlUpdate(sql);
        if (CharSequenceUtil.isNotBlank(dsTypeTreeNodeId)) {
            sql = getInsertSQL(dataObjectVo.getLogicDataObjs(), dsTypeTreeNodeId);
            this.baseDao.executeSqlUpdate(sql);
        }
        //创建数据集sql
        for (LogicDataObj logicDataObj : dataObjectVo.getLogicDataObjs()) {
            try {
                editService.createLogicDatObjSearchSql(logicDataObj.getId());
            } catch (Exception e) {
                throw new RuntimeException("创建数据集异常", e);
            }
        }
        return dataObjectVo.getLogicDataObjs().get(0).getId();
    }

    public String saveSingleTableDataSet(List<String> ids, String treeNodeId, String dsTypeTreeNodeId, String userId, List<LogicDataSetColumnVo> column, String name) {
        return saveDataSet(ids, treeNodeId, dsTypeTreeNodeId, userId, column, name, false);
    }


    @Override
    public String saveMultiTableDataSet(List<String> ids, String treeNodeId, String dsTypeTreeNodeId, String userId, List<LogicDataSetColumnVo> column, String name) {
        return saveDataSet(ids, treeNodeId, dsTypeTreeNodeId, userId, column, name, true);
    }

    @Override
    public List<Map<String, String>> findAllType() {
        String sql = "select t1.id as id, t1.code as code from t_md_element t1 left join t_md_type_system t2 on t1.owner_id = t2.id where t2.code = 'STANDARD'";
        return this.baseDao.sqlQueryForList(sql);
    }

    private String getDataTypeIdByCode(String code) {
        String sql = "select t2.id from t_md_type_system t1 left join t_md_element t2 on t1.id = t2.owner_id where t1.code = 'STANDARD' and  t2.code  = '" + code + "'";
        return this.baseDao.sqlQueryForValue(sql);
    }

    @Override
    public void saveDataSetNew(String datasetId, String dwId, String userId) {
        ClassifierStat stat = (ClassifierStat) baseDao.get(ClassifierStat.class, datasetId);
        if (null != stat) {
            if (!"".equals(stat.getDbType()) && !"ElasticSearch".equalsIgnoreCase(stat.getDbType()) && !
                    "Hbase".equalsIgnoreCase(stat.getDbType()) && !"FILE".equalsIgnoreCase(stat.getDbType())) {
                List<String> codeList = new ArrayList<String>();
                codeList.add(datasetId);
                //不放在同一个方法里会导致以下调用查不到stat
                this.accreditDataSet(codeList, dwId, userId);//物理表转换成logic表
            }
        }
    }

    private String getShareMark() {
        Map<String, String> shardMap = Maps.newHashMap();
        shardMap.put("isShare", "1");
        return JSON.toJSONString(shardMap);
    }
}
