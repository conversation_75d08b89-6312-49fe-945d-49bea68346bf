package com.dragonsoft.cicada.datacenter.modules.datavisual.model;

import com.code.dragonsoft.dataquery.service.querymodel.ColumnDataModel;
import com.code.metadata.datavisual.WidgetDatasetDims;
import com.code.metadata.datavisual.WidgetDatasetMeasures;
import com.dragonsoft.cicada.datacenter.modules.datavisual.model.annotation.WidgetLabel;
import com.dragonsoft.cicada.datacenter.modules.datavisual.model.enums.WidgetType;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * <AUTHOR>
 * @Date 2021/3/29 14:30
 */
@Slf4j
@WidgetLabel(name = "关系图", describe = "关系图", type = WidgetType.RELATIONSHIP)
public class RelationshipWidget extends BarChartWidget {


    /**
     * 节点数据项放在维度，边数据项放在度量
     */
    @Override
    public Map<String, Object> builderResult(ColumnDataModel columns, String code) {
        Map re = new HashMap();
        Map<String, List<Map>> data = new HashMap<String, List<Map>>();

        //维度
        List<WidgetDatasetDims> fromListDims = this.widgetDataset.getFromListDims();
        //度量
        List<WidgetDatasetMeasures> fromListMeasures = this.widgetDataset.getFromListMeasures();
        //拿出所有字段的字段名
        Set<String> keys = columns.getFieldName().keySet();
        //拿到值
        List<Map> fieldValue = columns.getFieldValue();

        List<Map> nodeList=new ArrayList<>();
        List<Map> edgeList=new ArrayList<>();
        int num=0;
        List filterNode1List=new ArrayList();
        List filterNode2List=new ArrayList();
        for (Map map : fieldValue) {
            //拿到点和边
            String node1 = (String) map.get(fromListDims.get(0).getFiledCode());
            String node2 = (String) map.get(fromListDims.get(1).getFiledCode());
            String edge = (String) map.get(fromListMeasures.get(0).getFiledCode());
            //计算点出现的次数
            //Map<String, Integer> value1 = calculateWeight(fromListDims.get(0).getFiledCode(), fieldValue);
            //Map<String, Integer> value2 = calculateWeight(fromListDims.get(1).getFiledCode(), fieldValue);
            Map map1=new HashMap();
            Map map2=new HashMap();
            Map edgeMap=new HashMap();
            //构造点
            if(!filterNode1List.contains(node1)){
                map1.put("id",num);
                map1.put("name",node1);
                //map1.put("value",value1.get(node1)*5);
                map1.put("category",0);
                num++;
                nodeList.add(map1);
                filterNode1List.add(node1);
            }
            if(!filterNode2List.contains(node2)){
                map2.put("id",num);
                map2.put("name",node2);
                //map2.put("value",value2.get(node2)*5);
                map2.put("category",1);
                num++;
                nodeList.add(map2);
                filterNode2List.add(node2);
            }
            //构造边
            for (Map map3 : nodeList) {
                if(node1.equals(map3.get("name"))){
                    edgeMap.put("source",map3.get("id"));
                }
                if (node2.equals(map3.get("name"))){
                    edgeMap.put("target",map3.get("id"));
                }
            }
            edgeMap.put("name",edge);
            edgeList.add(edgeMap);
        }
        data.put("nodes",nodeList);
        data.put("links",edgeList);
        data.put("categories",this.buildCategories(fromListDims));
        re.put("code", 1);
        re.put("data", data);
        return re;
    }

    private List<Map> buildCategories(List<WidgetDatasetDims> fromListDims){
        List<Map> categoriesList=new ArrayList<>();
        Map caMap1=new HashMap();
        caMap1.put("name",fromListDims.get(0).getFiledName());
        Map caMap2=new HashMap();
        caMap2.put("name",fromListDims.get(1).getFiledName());
        categoriesList.add(caMap1);
        categoriesList.add(caMap2);
        return categoriesList;
    }

    private Map<String, Integer> calculateWeight(String key,List<Map> fieldValue){
       /* int num=0;
        for (Map map : fieldValue) {
            String str =(String)map.get(key);
            if (node.equals(str)){
                num++;
            }
        }
        return num*5;*/
        Map<String, Integer> countMap = new HashMap<>();
        for (Map map : fieldValue) {
            String str =(String)map.get(key);
            Integer num = countMap.get(str);
            countMap.put(str, num == null ? 1 : num + 1);
        }
        return countMap;
    }


}
