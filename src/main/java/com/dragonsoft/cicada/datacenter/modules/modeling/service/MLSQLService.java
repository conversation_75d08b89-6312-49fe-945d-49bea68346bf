package com.dragonsoft.cicada.datacenter.modules.modeling.service;

import com.code.metadata.etl.trans.TransMeta;
import com.dragonsoft.cicada.datacenter.modules.modeling.vo.TransStatus;

import java.util.Map;

public interface MLSQLService {
    String runScript(String mlsql);

    String runScript(Map<String,Object> mlsql);

    Map<String,String> batchRunScript(Map<String,Object> mlSql);

    String runningJobs();

    String killJob(String id);

    String runJob(String id);

    Map<String,String> getAfterTableName(String id,String stepId,boolean hasEndTrans,String selectSize,String userId);

    String getCicadaMetaServiceSql(TransMeta transMeta);

    String getSubtasksByJobId(String groupId);

    String getJumpLogUrl(String jobId,String groupId);

    TransStatus execStatus(String transId);

    void transDeleteHistoryTaskLog(String taskId);

    String getSql(String transId,String userId);
}
