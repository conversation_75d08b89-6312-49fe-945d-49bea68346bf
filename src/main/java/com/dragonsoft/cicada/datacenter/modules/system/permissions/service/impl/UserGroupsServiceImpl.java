package com.dragonsoft.cicada.datacenter.modules.system.permissions.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.code.common.paging.PageInfo;
import com.dragoninfo.dfw.entity.TSysAuthGroup;
import com.dragoninfo.dfw.entity.TSysAuthObj;
import com.dragoninfo.dfw.entity.TSysAuthObjRel;
import com.dragoninfo.dfw.entity.TSysAuthUser;
import com.dragoninfo.dfw.exception.BusiException;
import com.dragoninfo.dfw.service.SysAuthGroupService;
import com.dragoninfo.dfw.service.SysAuthObjRelService;
import com.dragoninfo.dfw.service.SysAuthObjService;
import com.dragoninfo.dfw.service.SysAuthUserService;
import com.dragonsoft.cicada.datacenter.common.utils.CastUtils;
import com.dragonsoft.cicada.datacenter.modules.system.common.vo.TreeVo;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.enums.PermissionErrorCode;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.service.IUserGroupsService;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.util.UserUtil;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.vo.PageVo;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.vo.UserGroupsVo;
import com.fw.service.DfwBaseService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.spire.xls.SheetProtectionType.Objects;

/**
 * <AUTHOR>
 * @date 2020/5/21
 */
@Service
@Transactional
public class UserGroupsServiceImpl implements IUserGroupsService {


    public static final String YHZ = "YHZ";
    public static final String ID = "id";
    public static final String TWO = "2";

    @Autowired
    private SysAuthGroupService sysAuthGroupService;
    @Autowired
    private SysAuthObjRelService sysAuthObjRelService;
    @Autowired
    private SysAuthObjService sysAuthObjService;
    @Autowired
    private SysAuthUserService sysAuthUserService;
    @Autowired
    private DfwBaseService dfwBaseService;

    @Override
    public PageInfo queryUserGroupsPage(PageVo pageVo) {
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageIndex(pageVo.getPageNum());
        pageInfo.setPageSize(pageVo.getPageSize());
        return sysAuthGroupService.queryBlurryPage(UserUtil.setPageParams(pageVo), pageInfo);
    }

    @Override
    public UserGroupsVo queryGroupById(String groupId) {
        Map<String, String> groupsParams = Maps.newHashMap();
        groupsParams.put(ID, groupId);
        TSysAuthGroup tSysAuthGroup = (TSysAuthGroup) sysAuthGroupService.query(groupsParams);
        UserGroupsVo userGroupsVo = new UserGroupsVo();
        userGroupsVo.setId(tSysAuthGroup.getId());
        userGroupsVo.setCode(tSysAuthGroup.getObjCode());
        userGroupsVo.setName(tSysAuthGroup.getObjName());
        userGroupsVo.setEditor(tSysAuthGroup.getCreateObj().getObjName());
        return userGroupsVo;
    }

    @Override
    public List<TreeVo> queryAllUserGroups(String editId) {
//        List<TSysAuthGroup> tSysAuthGroups = CastUtils.cast(sysAuthGroupService.queryList(Maps.newHashMap()));
//        Map<String, String> params = Maps.newHashMap();
//        params.put("relationType", TWO);
//        //查出所有组关系
//        List<TSysAuthObjRel> tSysAuthObjRels = CastUtils.cast(sysAuthObjRelService.queryList(params));
//        //获取所有组id
//        List<String> allGroupId = tSysAuthGroups.stream().map(TSysAuthObj::getId).collect(Collectors.toList());
//        //获取所有关系中的下级层次
//        List<String> allToObjectId = tSysAuthObjRels.stream().map(tSysAuthObjRel -> tSysAuthObjRel.getFromAuthObj().getId()).collect(Collectors.toList());
//        //没有存在于下级ID的组为首层次
//        List<String> firstGroupsId = allGroupId.stream().filter(tSysAuthObjId -> !allToObjectId.contains(tSysAuthObjId)).collect(Collectors.toList());
//
//        return this.getGroupTree(firstGroupsId, tSysAuthGroups, tSysAuthObjRels, editId);
        List<TSysAuthGroup> tSysAuthGroups = getAllUserGroup();
        return getTreeVos(editId, tSysAuthGroups);
    }

    private List<TreeVo> getTreeVos(String editId, List<TSysAuthGroup> tSysAuthGroups) {
        Map<String, String> relaMap = getGroupList();
        List<TreeVo> resultList = new ArrayList<>();
        for (TSysAuthGroup group : tSysAuthGroups) {
            TreeVo treeVo = new TreeVo();
            treeVo.setId(group.getId());
            treeVo.setCode(group.getObjCode());
            treeVo.setName(group.getObjName());
            treeVo.setPid(relaMap.get(group.getId()));
            if (editId.isEmpty() || !editId.equals(group.getId())) {
                resultList.add(treeVo);
            }
        }
        return resultList.stream().filter(treeVo -> CharSequenceUtil.isBlank(treeVo.getPid()))
                .peek(treeVo -> treeVo.setChildren(getChildrens(treeVo, resultList))).
                collect(Collectors.toList());


    }

    /**
     * 递归查询子节点
     *
     * @param root 根节点
     * @param all  所有节点
     * @return 根节点信息
     */
    private List<TreeVo> getChildrens(TreeVo root, List<TreeVo> all) {
        return all.stream().filter(treeVo -> CharSequenceUtil.equals(treeVo.getPid(), root.getId()))
                .peek(treeVo -> treeVo.setChildren(getChildrens(treeVo, all)))
                .collect(Collectors.toList());
    }


    private List<TSysAuthGroup> getAllUserGroup() {
        List<Map<String, String>> mapList = CastUtils.cast(dfwBaseService.getDfwBaseDao()
                .sqlQueryForList(" select id,obj_code ,obj_name  from T_SYS_AUTH_OBJ where obj_type ='1'"));
        List<TSysAuthGroup> list = new ArrayList<>();
        if (CollUtil.isNotEmpty(mapList)) {
            TSysAuthGroup tSysAuthGroup;
            for (Map<String, String> map : mapList) {
                tSysAuthGroup = new TSysAuthGroup();
                tSysAuthGroup.setId(map.get("id"));
                tSysAuthGroup.setObjCode(map.get("obj_code"));
                tSysAuthGroup.setObjName(map.get("obj_name"));
                list.add(tSysAuthGroup);
            }
        }
        return list;
    }

    private Map<String, String> getGroupList() {
        List<Map<String, String>> mapList = CastUtils.cast(dfwBaseService.getDfwBaseDao()
                .sqlQueryForList("select from_obj_id as id ,to_obj_id as pid  from t_sys_auth_obj_rel where relation_type ='2'"));
        if (CollUtil.isEmpty(mapList)) {
            return new HashMap<>();
        }
        Map<String, String> result = new HashMap<>();
        for (Map<String, String> map : mapList) {
            result.put(map.get("id"), map.get("pid"));
        }
        return result;
    }


    /**
     * 获取分组树
     *
     * @param groupIds        首层次
     * @param tSysAuthGroups  所有组
     * @param tSysAuthObjRels 所有组关系
     * @return
     */
    private List<TreeVo> getGroupTree(List<String> groupIds, List<TSysAuthGroup> tSysAuthGroups, List<TSysAuthObjRel> tSysAuthObjRels, String editId) {
        List<TreeVo> treeVos = new ArrayList<>();
        for (String groupId : groupIds) {
            //查出当前ID 对应的组
            TSysAuthGroup tSysAuthGroup = tSysAuthGroups.stream().filter(t -> t.getId().equals(groupId)).collect(Collectors.toList()).get(0);
            TreeVo treeVo = new TreeVo();
            treeVo.setId(tSysAuthGroup.getId());
            treeVo.setCode(tSysAuthGroup.getObjCode());
            treeVo.setName(tSysAuthGroup.getObjName());
            //获取此组所有下属组ID
            List<String> childrenGroupIds = tSysAuthObjRels.stream().filter(s -> s.getToAuthObj().getId().equals(groupId)).map(t -> t.getFromAuthObj().getId()).collect(Collectors.toList());
            if (!childrenGroupIds.isEmpty()) {
                treeVo.setChildren(this.getGroupTree(childrenGroupIds, tSysAuthGroups, tSysAuthObjRels, editId));
            }
            //排除编辑的自身以及自身的下级  防止层级形成环
            if (editId.isEmpty() || !editId.equals(groupId)) {
                treeVos.add(treeVo);
            }
        }
        return treeVos;
    }

    @Override
    public void addUserGroup(UserGroupsVo userGroupsVo) {
        checkUserGroupCode(userGroupsVo);
        TSysAuthGroup tSysAuthGroup = new TSysAuthGroup();
        tSysAuthGroup.setObjCode(userGroupsVo.getCode());
        tSysAuthGroup.setObjName(userGroupsVo.getName());
        Map<String, String> params = Maps.newHashMap();
        params.put(ID, userGroupsVo.getEditor());
        TSysAuthUser editor = (TSysAuthUser) sysAuthUserService.query(params);
        tSysAuthGroup.setUpdateObj(editor);
        tSysAuthGroup.setCreateObj(editor);
        Timestamp d = new Timestamp(System.currentTimeMillis());
        tSysAuthGroup.setCreateTime(d);
        tSysAuthGroup.setUpdateTime(d);
        tSysAuthGroup.setObjType("1");
        sysAuthGroupService.store(tSysAuthGroup);
        this.saveRelation(tSysAuthGroup, userGroupsVo.getParent(), TWO);
    }

    private void checkUserGroupCode(UserGroupsVo roleVo) {
        Map<String, String> params = Maps.newHashMap();
        params.put("objCode", roleVo.getCode());
        List<TSysAuthGroup> sysAuthGroupList = sysAuthGroupService.queryList(params);
        if (CollUtil.isNotEmpty(sysAuthGroupList)) {
            throw new BusiException(PermissionErrorCode.GROUP_EXISTED);
        }
    }


    @Override
    public void upDataUserGroup(UserGroupsVo userGroupsVo) {
        Map<String, String> relationParams = Maps.newHashMap();
        relationParams.put("from_obj_id", userGroupsVo.getId());
        relationParams.put("relation_type", TWO);
        List<TSysAuthObjRel> sysAuthObjRels = sysAuthObjRelService.queryList(relationParams);
        for (TSysAuthObjRel tSysAuthObjRel : sysAuthObjRels) {
            sysAuthObjService.deleteAuthObj(tSysAuthObjRel);
        }

        Map<String, String> params = Maps.newHashMap();
        params.put(ID, userGroupsVo.getId());
        TSysAuthGroup group = (TSysAuthGroup) sysAuthGroupService.query(params);
        group.setObjName(userGroupsVo.getName());
        group.setObjCode(userGroupsVo.getCode());


        Map<String, String> userParams = Maps.newHashMap();
        userParams.put(ID, userGroupsVo.getEditor());
        TSysAuthUser Editor = (TSysAuthUser) sysAuthUserService.query(userParams);
        group.setUpdateObj(Editor);
        Timestamp d = new Timestamp(System.currentTimeMillis());
        group.setUpdateTime(d);
        sysAuthObjService.updateAuthObj(group);
        this.saveRelation(group, userGroupsVo.getParent(), TWO);
    }

    @Override
    public String deleteUserGroup(String id) {
        Map<String, String> params = Maps.newHashMap();
        List<TSysAuthObjRel> tSysAuthObjRels = (List<TSysAuthObjRel>) sysAuthObjRelService.queryList(params);
        //获取所有下级关系
        List<TSysAuthObjRel> belongTSysAuthObjRels = tSysAuthObjRels.stream().filter(s -> s.getToAuthObj().getId().equals(id)).collect(Collectors.toList());
        //获取组下级关系
        List<TSysAuthObjRel> belongGroupTSysAuthObjRels = belongTSysAuthObjRels.stream().filter(s -> s.getRelationType().equals(TWO)).collect(Collectors.toList());
        //获取用户下级关系
        List<TSysAuthObjRel> belongUserTSysAuthObjRels = belongTSysAuthObjRels.stream().filter(s -> s.getRelationType().equals("0")).collect(Collectors.toList());
        if (0 != belongGroupTSysAuthObjRels.size()) {
            return "暂不允许删除，请先解除用户组下用户组";
        } else if (0 != belongUserTSysAuthObjRels.size()) {
            return "暂不允许删除，请先解除用户组下用户";
        } else {
            //获取上级关系
            List<TSysAuthObjRel> fromRelations = tSysAuthObjRels.stream().filter(s -> s.getFromAuthObj().getId().equals(id)).collect(Collectors.toList());
            for (TSysAuthObjRel tSysAuthObjRel : fromRelations) {
                sysAuthObjService.deleteAuthObj(tSysAuthObjRel);
            }
            Map<String, String> param = Maps.newHashMap();
            param.put(ID, id);
            TSysAuthGroup group = (TSysAuthGroup) sysAuthGroupService.query(param);
            sysAuthObjService.deleteAuthObj(group);
            return "删除成功";
        }
    }

    @Override
    public String getGroupRandom() {
        return UserUtil.getRandom(Lists.newArrayList(), YHZ);
    }


    /**
     * 设置组与组之间的关系
     *
     * @param to           上级
     * @param from         下级
     * @param relationType 0-用户组，1-用户角色，2-组组， 3-组角色
     */
    private void saveRelation(TSysAuthObj from, String to, String relationType) {
        Map<String, String> params = Maps.newHashMap();
        params.put(ID, to);
        TSysAuthObj toObj = (TSysAuthObj) sysAuthGroupService.query(params);
        sysAuthObjRelService.store(UserUtil.setTSysAuthObjRel(toObj, relationType, from));
    }
}
