package com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.utils;

import com.alibaba.fastjson.JSON;
import com.code.cicada.thirdplugin.conditionfilter.meta.CicadaConditionFilterPlugin;
import com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.vo.ParamConfigVo;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date: 2022/06/02/下午2:47
 */
public class ConditionFilterBuildUtils {

    private static final String LIKE = "like";

    private static final String NOTLIKE = "not like";

    private static final String STARTLIKE = "开头是";

    private static final String STARTNOTLIKE = "开头不是";

    private static final String ENDLIKE = "结尾是";

    private static final String ENDNOTLIKE = "结尾不是";

    private static final String TEXT = "text";

    private static final String STRING = "string";

    private static final String IN = "in";

    private static final String NOTIN = "not in";

    private static final String NOTNULL = "is not null";

    private static final String ISNULL = "is null";

    private static final String TIMESTAMP = "Timestamp";

    private static final String EQUAL = "=";

    private static final String BETWEEN = "between";

    private static final String NOTBETWEEN = "not between";

    private static final String DATE = "Date";


    public static String toConditionSql(ParamConfigVo dataQueryParamConfigVo) {
        String relationCondition = "";
        String resultCondition = "";


        List<ConditionRelation> fileterConditionList = new ArrayList<>();
        List<ParamConfigVo.ConditionFilterMeta> encasulationJudgContion = dataQueryParamConfigVo.getEncasulationJudgContion();

        if(encasulationJudgContion.size()!=0 || encasulationJudgContion!=null){
            for (ParamConfigVo.ConditionFilterMeta encapsulationFilterCondition : encasulationJudgContion) {
                //内层的条件
                relationCondition = encapsulationFilterCondition.getRelationCondition();
                List<CicadaConditionFilterPlugin.EncapsulationFilterCondition.CicadaFilterCondition> cicadaFilterConditions = JSON.parseArray(encapsulationFilterCondition.getJudgCondition(), CicadaConditionFilterPlugin.EncapsulationFilterCondition.CicadaFilterCondition.class);
                //内层条件拼接

                String filterCondition = " ( " + cicadaFilterConditions.stream()
                        .map(s -> " " + s.getField() + " " + s.getCondition() + " " + getValue(s.getField(), s.getFieldType(), "$"+s.getField(), s.getCondition(), s.getConditionType(), s.getStartTime(), s.getEndTime(), s.getValueType()) + " ")
                        .collect(Collectors.joining(" " + relationCondition + " ")) + " ) ";

                ConditionRelation conditionRelation = new ConditionRelation();
                conditionRelation.setFilterCondition(filterCondition);
                conditionRelation.setNo(encapsulationFilterCondition.getNo());
                conditionRelation.setOperater(encapsulationFilterCondition.getOutRelationContion());

                fileterConditionList.add(conditionRelation);

            }

            List<ConditionRelation> collect = fileterConditionList.stream().sorted(Comparator.comparing(ConditionRelation::getNo)).collect(Collectors.toList());
            for (int i = 0; i < collect.size(); i++) {
                resultCondition += collect.get(i).getFilterCondition() + " " + collect.get(i).getOperater();
            }
        }

        return resultCondition;
    }


    private static String getValue(String field, String type, String value, String condition, String conditionType, String startTime, String endTime, String valueType) {
        if (null == type) {
            return value;
        }
        //支持null和空串的处理
        if (NOTNULL.equals(condition.toLowerCase())) {
            if (STRING.equalsIgnoreCase(type) || TEXT.equalsIgnoreCase(type)) {
                return " and " + field + " !=''";
            }
            return " ";
        }


        if (ISNULL.equals(condition.toLowerCase())) {
            if (STRING.equalsIgnoreCase(type) || TEXT.equalsIgnoreCase(type)) {
                return " or " + field + " =''";
            }
            return " ";
        }


        if ("field".equalsIgnoreCase(valueType)) {
            if (null != condition && (condition.toLowerCase().equals(LIKE))) {
                if (conditionType.equals(STARTLIKE)) {
                    return value + "||'%' ";
                } else if (conditionType.equals(ENDLIKE)) {
                    return " '%'||" + value;
                }
                return " '%'||" + value + "||'%' ";
            }

            if (null != condition && (condition.toLowerCase().equals(NOTLIKE))) {
                if (conditionType.equals(STARTNOTLIKE)) {
                    return value + "||'%' ";
                } else if (conditionType.equals(ENDNOTLIKE)) {
                    return " '%'||" + value;
                }
                return " '%'||" + value + "||'%' ";
            }
            return value;
        }


        if (type.equalsIgnoreCase(TIMESTAMP)) {
            if (BETWEEN.equalsIgnoreCase(condition) || NOTBETWEEN.equalsIgnoreCase(condition)) {
                return value+"1"+"#"+value+"2";
            }

            return " to_timestamp(" + value + ",'yyyy-MM-dd hh24:mi:ss') ";
        }

        if (type.equalsIgnoreCase(DATE)) {
            if (BETWEEN.equalsIgnoreCase(condition) || NOTBETWEEN.equalsIgnoreCase(condition)) {
                return value+"1"+"#"+value+"2";
            }

            return " to_date('" + value + "','yyyy-MM-dd hh24:mi:ss') ";
        }
        /**
         * 当为字段的时候有特殊符号`，直接去掉返回字段值
         */
        if (value.indexOf("`") != -1) {
            return value.replace("`", "");
        }


        if (null != condition && (condition.toLowerCase().equals(IN) || condition.toLowerCase().equals(NOTIN))) {
            List<String> conditionList = Arrays.asList(value.split(","));
            if (TEXT.equals(type.toLowerCase()) || STRING.equals(type.toLowerCase())) {
                String conditionString = conditionList.stream().map(s -> " '" + s + "' ")
                        .collect(Collectors.joining(" , "));
                return " ( " + conditionString + " ) ";
            } else {
                String conditionString = conditionList.stream().collect(Collectors.joining(" , "));
                return " ( " + conditionString + " ) ";
            }
        }

        if (null != condition && (condition.toLowerCase().equals(LIKE))) {
            if (conditionType.equals(STARTLIKE)) {
                return " '" + value + "%' ";
            } else if (conditionType.equals(ENDLIKE)) {
                return " '%" + value + "' ";
            }
            return " '%" + value + "%' ";
        }

        if (null != condition && (condition.toLowerCase().equals(NOTLIKE))) {
            if (conditionType.equals(STARTNOTLIKE)) {
                return " '" + value + "%' ";
            } else if (conditionType.equals(ENDNOTLIKE)) {
                return " '%" + value + "' ";
            }
            return " '%" + value + "%' ";
        }

        if (TEXT.equals(type.toLowerCase()) || STRING.equals(type.toLowerCase())) {
            return " '" + value + "' ";
        }
        return value;
    }

    public static class ConditionRelation {

        private int no;

        private String filterCondition;

        private String operater;

        public String getOperater() {
            return operater;
        }

        public void setOperater(String operater) {
            this.operater = operater;
        }

        public int getNo() {
            return no;
        }

        public void setNo(int no) {
            this.no = no;
        }


        public String getFilterCondition() {
            return filterCondition;
        }

        public void setFilterCondition(String filterCondition) {
            this.filterCondition = filterCondition;
        }
    }
}
