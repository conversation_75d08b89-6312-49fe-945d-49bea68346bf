package com.dragonsoft.cicada.datacenter.modules.managespace.service;

import com.code.common.paging.PageInfo;
import com.dragonsoft.cicada.datacenter.modules.system.dataportal.vo.PublishUrlVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/8/31
 */
public interface IModelMarketManageService {
    public PageInfo queryPageByType(String name, String type, int page, int pageSize);

    public void saveOrUpdataPublishUrl(String id, String type, String name, String url, String userId);

    public void changePublishState(String id, String stat);

    public List<PublishUrlVo> getReviewLaunchUrl();
}
