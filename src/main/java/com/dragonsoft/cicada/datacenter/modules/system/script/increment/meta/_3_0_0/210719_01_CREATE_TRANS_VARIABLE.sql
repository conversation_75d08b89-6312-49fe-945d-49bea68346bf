/*==============================================================*/
/* Table: T_MD_TRANS_VARIABLE                                   */
/*==============================================================*/
CREATE TABLE T_MD_TRANS_VARIABLE (
   PARAM_CODE           VARCHAR(32)          NULL,
   PARAM_VALUE          TEXT                 NULL,
   PARAM_TYPE           VARCHAR(32)          NULL,
   ID                   VARCHAR(32)          NOT NULL,
   GLOBAL               BOOL                 NULL,
   CONSTRAINT PK_T_MD_TRANS_VARIABLE PRIMARY KEY (ID)
)
INHERITS (T_MD_ELEMENT);

COMMENT ON COLUMN T_MD_TRANS_VARIABLE.PARAM_CODE IS
'参数名';

COMMENT ON COLUMN T_MD_TRANS_VARIABLE.PARAM_VALUE IS
'参数值';

COMMENT ON COLUMN T_MD_TRANS_VARIABLE.GLOBAL IS
'是否全局变量
';

/*==============================================================*/
/* Table: T_MD_TRANS_VARIABLE_RELATION                          */
/*==============================================================*/
CREATE TABLE T_MD_TRANS_VARIABLE_RELATION (
   TRANS_ID             VARCHAR(32)          NULL,
   ID                   VARCHAR(32)          NOT NULL,
   VARIABLE_ID          VARCHAR(32)          NULL,
   CONSTRAINT PK_T_MD_TRANSL_VARIABLE_RELATI PRIMARY KEY (ID)
)
INHERITS (T_MD_ELEMENT);

COMMENT ON COLUMN T_MD_TRANS_VARIABLE_RELATION.TRANS_ID IS
'方案id';

ALTER TABLE T_MD_TRANS_VARIABLE_RELATION
   ADD CONSTRAINT FK_T_MD_TRA_REFERENCE_T_MD_TRA FOREIGN KEY (VARIABLE_ID)
      REFERENCES T_MD_TRANS_VARIABLE (ID)
      ON DELETE RESTRICT ON UPDATE RESTRICT;
