package com.dragonsoft.cicada.datacenter.modules.system.schedule;

import com.code.common.schedulectr.scheduleclient.schemeconfig.SimpleJobInfo;

public interface MLSQLScheduleService {

    String createJob(String transMetaId);

    void startJob(String transMetaId);

    void killJob(String transMetaId);

    void deleteJob(String transMetaId);

    void updateJob(SimpleJobInfo jobInfo);

    String executeStatus(String transMetaId);

    String updateTransMeta(String transName, String runMode, String transId, String instanceCode, String handleMode, String classifyId, String dirType, String userId);

   String updateTransMeta(String transName, String runMode, String transId, String instanceCode, String handleMode, String classifyId, String userId);

}
