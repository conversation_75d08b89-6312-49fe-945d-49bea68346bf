package com.dragonsoft.cicada.datacenter.modules.dataplan.apitest.controller;

import com.code.metaservice.usecase.UseCaseService;
import com.dragoninfo.dfw.bean.Result;
import com.dragonsoft.cicada.datacenter.modules.dataplan.apitest.service.UseCaseManageService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.apitest.vo.TestApiParamVo;
import com.dragonsoft.cicada.datacenter.modules.dataplan.apitest.vo.TestApiResultVo;
import com.dragonsoft.cicada.datacenter.modules.dataplan.apitest.vo.UseCaseVo;
import com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.service.IServiceManagementService;
import com.fw.tenon.tree.Tree;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.ArrayList;
import java.util.List;

@CrossOrigin
@RestController
@RequestMapping("/apiTest")
public class ApiTestController {

    @Autowired
    private IServiceManagementService serviceManagementService;

    @Autowired
    private UseCaseService useCaseService;

    @Autowired
    private UseCaseManageService useCaseManageService;

    @PostMapping("/testSingleService")
    public Result testSingleService(@RequestBody TestApiParamVo paramVo, HttpServletRequest request) {

        TestApiResultVo resultVo = serviceManagementService.testServiceResult(paramVo, request);
        return Result.success(resultVo);
    }

    @GetMapping("/serviceTreeList")
    public Result serviceTreeList(HttpSession session) {
        String userId = (String) session.getAttribute("userId");
        List<Tree> trees = serviceManagementService.queryServiceTree(userId);
        return Result.success(trees);
    }

    @PostMapping("/testBatchService")
    public Result testBatchService(@RequestBody List<TestApiParamVo> paramVos, HttpServletRequest request) {

        List<TestApiResultVo> resultVos = new ArrayList<>();
        for (TestApiParamVo paramVo : paramVos) {
            TestApiResultVo resultVo = serviceManagementService.testServiceResult(paramVo, request);
            resultVos.add(resultVo);
        }
        return Result.success(resultVos);
    }

    /**
     * 导入用例   返回用例引用的api和用例的参数值
     * @param caseIds
     * @return
     */
    @PostMapping("/importUseCase")
    public Result importUseCase(@RequestBody List<String> caseIds) {

        List<UseCaseVo> importApiInfo = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(caseIds)) {
            importApiInfo = useCaseManageService.getImportApiInfo(caseIds);
        }

        return Result.success(importApiInfo);
    }
}
