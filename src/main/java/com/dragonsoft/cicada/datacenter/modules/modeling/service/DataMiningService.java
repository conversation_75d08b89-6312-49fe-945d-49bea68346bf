package com.dragonsoft.cicada.datacenter.modules.modeling.service;

import com.dragonsoft.cicada.datacenter.modules.modeling.qo.UpdateTransQo;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Wu.D.J
 * @Create : 2020.11.04
 */
public interface DataMiningService {

    String createTrans(String transName);

    void quickTransToProcessTrans(String transId,String busiClassifyId);

    void updateTrans(UpdateTransQo updateTransQo);

    void updateTransXAndY(String transId,int x,int y);

    void updateSchedulePlan(String transId, String scheduleType, String cron, String declare);

    Map<String, String> getSchedulePlan(String transId);

    Map<String, String> getSchedulePlan(String transId,String name);

    void transRun(String transId,String userId);

    void transRun(String transId,String userId,String executorService);

    void transRun(String transId, String userId, HttpServletRequest request);

    Map<String, String> transPreview(String transId);

    void transStop(String transId);

    void transStop(String transId,String flinkUrl);

    void updateJobStatus(String jobId,String status);

    String showSQL(String transId);

    String showSQL(String transId,String flinkUrl);

    String analysisSQL(String transId);

    String subTask(String subTranId);

    void updateSchedulePlanWithSubTrans(String transId, String scheduleType, String cron, String declare, List startPrograms);

    void updateSchedulePlanWithSubTrans(String transId, String scheduleType, String cron, String declare, List startPrograms, String executorServiceName);

    List getSubTrans(String transId);

    void deletaSubTrans(String transId,String subTransId);


}
