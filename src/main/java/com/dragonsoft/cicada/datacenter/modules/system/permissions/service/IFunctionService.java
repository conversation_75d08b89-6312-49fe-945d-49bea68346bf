package com.dragonsoft.cicada.datacenter.modules.system.permissions.service;

import com.dragonsoft.cicada.datacenter.modules.system.common.vo.TreeVo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/5/27
 */
public interface IFunctionService {

    /**
     *获取功能列表的树
     * @return
     */
    List<TreeVo> queryFunctionTree(String systemType);

    /**
     * 获取所有功能列表 树
     * @return
     */
    List<TreeVo> queryExpFunctionTree();

    /**
     * 删除功能 以及相关权限关系
     * @param funcId
     */
    void deleteFuncAndFuncRelation(String funcId);

    List<TreeVo> queryShowDocFuncCodes();

    Map<String,Object> queryShowDocMsg();

    boolean isAuthToAdmin();

}
