package com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import com.code.common.bean.BeanFactory;
import com.code.common.dataset.type.field.TableNameUtil;
import com.code.common.paging.PageInfo;
import com.code.common.spark.model.SparkColumnMeta;
import com.code.common.utils.StringUtils;
import com.code.common.utils.assertion.Assert;
import com.code.dragonsoft.dataquery.service.QueryDataService;
import com.code.dragonsoft.dataquery.service.querymodel.ColumnDataModel;
import com.code.dragonsoft.dataquery.service.querymodel.ColumnPageInfo;
import com.code.dragonsoft.dataquery.service.querymodel.ParamDataModel;
import com.code.meta.dml.IRow;
import com.code.metadata.base.softwaredeployment.Software;
import com.code.metadata.base.typemapping.TypeSystem;
import com.code.metadata.model.core.DataType;
import com.code.metadata.res.common.ClassifierStat;
import com.code.metadata.res.ddl.LogicDataColumn;
import com.code.metadata.res.ddl.LogicDataObj;
import com.code.metadata.res.structured.rdb.RdbSchema;
import com.code.metaservice.base.typemapping.TypeSystemService;
import com.code.metaservice.business.directory.BusiClassifyService;
import com.code.metaservice.ddl.ILogicDataColumnService;
import com.code.metaservice.ddl.ILogicDataObjService;
import com.code.metaservice.res.structured.rdb.IRdbSchemaService;
import com.code.metaservice.util.LogicDataObjectUtil;
import com.code.plugin.db.TypeMapping;
import com.code.std.types.NonStandardType;
import com.code.std.types.StandardType;
import com.dragoninfo.dfw.bean.Result;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.service.IQuickSqlAnalysisService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.util.DataSetUtils;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.vo.LogicDataSet.PreviewVo;
import com.fw.service.BaseService;
import com.fw.service.annotation.Service;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR> Wu.D.J
 * @Create : 2020.07.29
 */
@Slf4j
@Service
public class QuickSqlAnalysisServiceImpl extends BaseService implements IQuickSqlAnalysisService {

    //    @Value("${spark.dubbo.address}")
    private String sparkDubboAddress;

    @Autowired
    private BusiClassifyService busiClassifyService;

    @Autowired
    private TypeSystemService typeSystemService;

    @Autowired
    private ILogicDataObjService logicDataObjService;

    @Autowired
    private ILogicDataColumnService logicDataColumnService;

    @Autowired
    private QueryDataService queryDataService;
    @Autowired
    IRdbSchemaService rdbSchemaService;

    @Autowired
    DataSetEditServiceImpl dataSetEditService;

    @Autowired
    BeanFactory beanFactory;

    @Override
    public ColumnDataModel previewSqlData(String sql, String schemaId) {
        /*ISparkClientService sparkClient = SparkConsumer.getSparkClient(sparkDubboAddress);
        Collection<SparkRow> rows = null;
        try {
            rows = sparkClient.query(sql);
        } catch (Throwable e) {
         log.error(e.getMessage(),e);
            log.error(e.getMessage());
            Assert.fail("查询异常，请检查SQL语句是否正确!");
        }
        return rows.stream()
                .map(SparkRow::toMap)
                .collect(Collectors.toList());*/


        ParamDataModel paramDataModel = new ParamDataModel();
        paramDataModel.setScript(sql);
        ColumnDataModel columnDataModel = null;
        TypeMapping typeMapping = (TypeMapping) beanFactory.getBean("typeMapping");
        RdbSchema rdbSchema = (RdbSchema) this.baseDao.get(RdbSchema.class, schemaId);
        try {
            columnDataModel = queryDataService.queryDataBySchema(schemaId, paramDataModel);
            Map<String, String> fieldName = columnDataModel.getFieldName();
            for (Map.Entry<String, String> item : fieldName.entrySet()) {
                NonStandardType nonStandardType = DataSetUtils.changType(rdbSchema.getCatalog().getSoftware().getCode(), item.getValue());
                StandardType stdType = typeMapping.trans(nonStandardType);
                item.setValue(stdType.getCode());
            }
        } catch (Exception e) {
            log.error("查询异常，请检查SQL语句是否正确!--"+e.getMessage(),e);
            Assert.fail("查询异常，请检查SQL语句是否正确!");
        }
        return columnDataModel;
    }

    /**
     * 预览数据分页查询
     */
    @Override
    public Result previewSqlDataPage(PreviewVo previewVo) {
        Assert.notNull(previewVo, "请求参数不能为空");
        String schemaId = previewVo.getSchemaId();
        String sql = previewVo.getSql();

        Assert.notNull(schemaId, "数据源id不能为空");
        Assert.notNull(sql, "sql不能为空");

        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageIndex(previewVo.getPage());
        pageInfo.setPageSize(previewVo.getPageSize());

        ColumnPageInfo res = null;
        try {
            res = queryDataService.queryDataBySchemaPageInfo(schemaId, sql, pageInfo);
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new RuntimeException("sql执行异常", e);
        }
        pageInfo.setDataList(parseDataSet(res.getColumnDataModel().getFieldValue()));
        return Result.success(pageInfo);
    }

    private List parseDataSet(List<Map> rows) {
        List<Map> result = new ArrayList<>();
        //已知hive库存在表名.字段名的形式，需要切除表名，未知是否还有其他库存在这现象
        //同时将java.sql.Timestamp对象解析成格式化的字符串时间戳
        for (Map map : rows) {
            Map<String, Object> row = new HashMap<>();
            for (Object o : map.entrySet()) {
                Map.Entry entry = (Map.Entry) o;
                String key = (String) entry.getKey();
                Object value = entry.getValue();
                if (value instanceof Timestamp) {
                    value = value.toString();
                }
                row.put(key, value);
            }
            result.add(row);
        }
        return result;
    }


    @Override
    public List<SparkColumnMeta> getSqlColumns(PreviewVo previewVo) {
        Assert.notNull(previewVo, "请求参数不能为空");
        String schemaId = previewVo.getSchemaId();
        String sql = previewVo.getSql();

        Assert.notNull(schemaId, "数据源id不能为空");
        Assert.notNull(sql, "sql不能为空");

        TypeMapping typeMapping = (TypeMapping) beanFactory.getBean("typeMapping");
        List<SparkColumnMeta> metaList = Lists.newArrayList();

        RdbSchema rdbSchema = (RdbSchema) this.baseDao.get(RdbSchema.class, schemaId);
        Software software = rdbSchema.getCatalog().getSoftware();
        String code = software.getCode();

        LinkedHashMap<String, IRow.ColumnMeta> map = getColumnsMeta(schemaId, sql);
        for (Map.Entry<String, IRow.ColumnMeta> metaEntry : map.entrySet()) {
            IRow.ColumnMeta value = metaEntry.getValue();
            NonStandardType nonStandardType = DataSetUtils.changType(code, value.getTypeName());
            StandardType stdType = typeMapping.trans(nonStandardType);
            SparkColumnMeta columnMeta = newSparkColumnMeta(value.getName(), stdType.getCode());
            metaList.add(columnMeta);
        }
        return metaList;
    }

    public List<SparkColumnMeta> getNoStandSqlColumns(String schemaId, String sql) {
        List<SparkColumnMeta> metaList = Lists.newArrayList();
        LinkedHashMap<String, IRow.ColumnMeta> map = getColumnsMeta(schemaId, sql);
        for (Map.Entry<String, IRow.ColumnMeta> metaEntry : map.entrySet()) {
            IRow.ColumnMeta value = metaEntry.getValue();
            SparkColumnMeta columnMeta = newSparkColumnMeta(value.getName(), value.getTypeName());
            metaList.add(columnMeta);
        }
        return metaList;
    }

    @NotNull
    private SparkColumnMeta newSparkColumnMeta(String columnCode, String typeName) {
        return new SparkColumnMeta(TableNameUtil.cutScheme(columnCode), typeName, false, "");
    }

    private LinkedHashMap<String, IRow.ColumnMeta> getColumnsMeta(String schemaId, String sql) {
        ParamDataModel paramDataModel = new ParamDataModel();
        paramDataModel.setScript(sql);
        LinkedHashMap<String, IRow.ColumnMeta> resMap = null;
        try {
            resMap = queryDataService.queryColumnMeta(schemaId, true, paramDataModel);
        } catch (Exception e) {
            log.error("sql执行异常："+e.getMessage(),e);
            //Assert.fail("字段获取失败！");
            Assert.fail("sql执行异常：" + e.getMessage());
        }
        return resMap;
    }

    @Override
    public void updateQuickSqlDataObj(String dataObjId, String dataObjName, String sql) {
        LogicDataObj logicDataObj = logicDataObjService.findLogicDataObjById(dataObjId);
        Assert.notNull(logicDataObj, "id[" + dataObjId + "]获取数据集为空!");
        logicDataColumnService.deleteColumnByLogicDataObjId(dataObjId);
        String softwareId = logicDataObj.getOwner().getOwner().getOwner().getId();
        Software software = get(Software.class, softwareId);
        TypeSystem standard = software.getTypeSystem();
        Assert.notNull(standard, String.format("数据类型[%s][%s]在配置库未找到", software.getCode(), software.getSoftVersion()));
        logicDataObj.setSql(sql);
        /*ISparkClientService sparkClient = SparkConsumer.getSparkClient(sparkDubboAddress);
        Collection<SparkColumnMeta> columnMetas = sparkClient.queryStructSchema(sql);*/
        if (StringUtils.isNotBlank(sql)) {
            List<SparkColumnMeta> columnMetas = getNoStandSqlColumns(logicDataObj.getOwnerId(), sql);
            columnMetas.forEach(col -> {
                LogicDataColumn column = new LogicDataColumn();
                column.setName(col.getName());
                column.setCode(col.getName());
                String sparkDataType = col.getDataType();
                column.setFormat(LogicDataObjectUtil.getFormatByStandDataType(sparkDataType));
                column.setIndexType(LogicDataObjectUtil.getTypeByStandDataType(sparkDataType));
                DataType type = null;
                if (sparkDataType.contains("decimal")) {
                    type = null == standard.getType("BigDecimal") ? standard.getType("decimal") : standard.getType("BigDecimal");
                } else if (sparkDataType.contains("FLOAT8") && !"POSTGRESQL".equalsIgnoreCase(standard.getCode()) && !"GREENPLUM".equalsIgnoreCase(standard.getCode())) {
                    type = null == standard.getType("FLOAT") ? standard.getType("float") : standard.getType("FLOAT");
                } else {
                    type = standard.getType(StrUtil.upperFirst(sparkDataType.replaceAll("Type", "")));
                }
                if (type == null) {
                    log.warn("字段[{}]：spark类型[{}], 转换成标准类型失败，替换成String类型!", col.getName(), col.getDataType());
                    type = null == standard.getType("String") ? standard.getType("string") : standard.getType("String");
                }
                column.setBelongParentId(dataObjId);
                column.setDataType(type);
                column.setFormat(LogicDataObjectUtil.getFormatByStandDataType(column.getDataType().getCode()));
                column.setIndexType(LogicDataObjectUtil.getTypeByStandDataType(column.getDataType().getCode()));
                column.setOwner(logicDataObj);
                logicDataColumnService.saveLogicDataObjColumn(column);
//            logicDataObj.addFeature(column);
            });
        }
        logicDataObj.setOperateTime(DateUtil.format(LocalDateTime.now(), "yyyy-MM-dd HH:mm:ss"));
        logicDataObj.setName(dataObjName);
        logicDataObjService.updateLogicDataObj(logicDataObj);
        boolean isCreate = false;
        if (StringUtils.isBlank(logicDataObj.getGlobalCode())) {
            logicDataObj.setGlobalCode("v_logic_" + System.currentTimeMillis());
            isCreate = true;
        }
//        try {
//            dataSetEditService.registerTable(logicDataObj.getOwnerId(), logicDataObj,isCreate);
//        } catch (Exception e) {
//         log.error(e.getMessage(),e);
//            Assert.fail("视图创建失败！");
//        }
    }

    @Override
    public String saveQuickSqlDataObj(String userId, String classifyId, String dataObjName, String
            dataObjCode, String schemaId) {
        String sql = "SELECT COUNT(*) FROM (SELECT id FROM t_md_logic_dataobj WHERE code = :tableCode) A";
        String size = this.baseDao.sqlQueryForValue(sql, addParam("tableCode", dataObjCode).param());
        Assert.isZero(Integer.parseInt(size), "表名[" + dataObjCode + "]已存在!");

        RdbSchema rdbSchema = rdbSchemaService.get(schemaId);
        Assert.notNull(rdbSchema, "[" + rdbSchema.getCode() + "不是数据对象！]");

        LogicDataObj dataObj = new LogicDataObj();
        dataObj.setOwner(rdbSchema);
        dataObj.setName(dataObjName);
        dataObj.setCode(dataObjCode);
        dataObj.setGlobalCode("");
        dataObj.setBelongType("QUICK_SQL");
        dataObj.setIsFast("0");
        dataObj.setDbType(rdbSchema.getCatalog().getSoftware().getCode());
        dataObj.setOperateTime(DateUtil.format(LocalDateTime.now(), "yyyy-MM-dd HH:mm:ss"));
        dataObj.setUserId(userId);
        dataObj.setOperateUserId(userId);

        logicDataObjService.saveLogicDataObj(dataObj);
        busiClassifyService.saveBusiClassifyElementNotExist(classifyId, dataObj.getId());
        return dataObj.getId();
    }

    //查询同一目录下是否有同名称数据集存在
    public boolean checkLogicDataObjNameIsExist(String classifyId, String dataObjName, String dataObjId) {
        Map<String, Object> params = Maps.newHashMap();
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select a.id from t_md_logic_dataobj a, t_md_classify_element b where a.id = b.element_id ");
        sqlBuilder.append(" and b.busi_classify_id = :classifyId ");
        sqlBuilder.append(" and a.name = :dataObjName ");
        if (StringUtils.isNotBlank(dataObjId)) {
            sqlBuilder.append(" and a.id != :dataObjId ");
            params.put("dataObjId", dataObjId);
        }
        params.put("classifyId", classifyId);
        params.put("dataObjName", dataObjName);

        List<Map> resultList = baseDao.sqlQueryForList(sqlBuilder.toString(), params);

        if (CollectionUtils.isNotEmpty(resultList)) {
            return true;
        }
        return false;
    }

    /**
     * 保存即席SQL数据集
     * @return
     */
    @Override
    public Result saveQuickSqlDataObj(Map<String, Object> paramsMap) {
        String classifyId = (String) paramsMap.get("classifyId");
        String dataObjName = (String) paramsMap.get("dataObjName");
        String dataObjCode = (String) paramsMap.get("dataObjCode");
        String schemaId = (String) paramsMap.get("schemaId");
        String dsTypeClassifyId = (String) paramsMap.get("dsTypeClassifyId");
        String sql = (String) paramsMap.get("sql");
        String userId = (String) paramsMap.get("userId");

        Assert.notNull(classifyId, "所属目录位置不能为空");
        Assert.notNull(dataObjName, "数据集中文名不能为空");
        Assert.notNull(dataObjCode, "数据集英文名不能为空");
        Assert.notNull(schemaId, "数据源id不能为空");
        //Assert.notNull(sql, "sql不能为空");
        Assert.notNull(userId, "用户未登录");
        if (checkLogicDataObjNameIsExist(classifyId, dataObjName, null)) {
            Assert.fail("数据集中文名已存在！");
        }

        String dataObjId = saveQuickSqlDataObj(userId, classifyId, dataObjName, dataObjCode, schemaId);
        if (CharSequenceUtil.isNotBlank(dsTypeClassifyId)) {
            busiClassifyService.saveBusiClassifyElementNotExist(dsTypeClassifyId, dataObjId);
        }
        updateQuickSqlDataObj(dataObjId, dataObjName, sql);

        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("dataObjId", dataObjId);

        return Result.success(resultMap);
    }

    /**
     * 修改即席SQL数据集
     */
    @Override
    public Result updateQuickSqlDataObj(Map<String, Object> paramsMap) {
        String classifyId = (String) paramsMap.get("classifyId");
        String dataObjId = (String) paramsMap.get("dataObjId");
        String dataObjName = (String) paramsMap.get("dataObjName");
        String sql = (String) paramsMap.get("sql");
        Assert.notNull(classifyId, "所属目录位置不能为空");
        Assert.notNull(dataObjId, "数据集id不能为空");
        Assert.notNull(dataObjName, "数据集中文名不能为空");
        //Assert.notNull(sql, "sql不能为空");
        if (checkLogicDataObjNameIsExist(classifyId, dataObjName, dataObjId)) {
            Assert.fail("数据集中文名已存在！");
        }

        updateQuickSqlDataObj(dataObjId, dataObjName, sql);

        return Result.success();
    }

    @Override
    public List<Map<String, String>> queryRegisteredJson(String schemaId) {

        List<Map<String, String>> resList = Lists.newArrayList();
        String hql = "FROM ClassifierStat WHERE owner.id = :id";
        List<ClassifierStat> tableList = this.baseDao.queryForList(hql, addParam("id", schemaId).param());
        for (ClassifierStat classifierStat : tableList) {
            if (!"LogicDataObj".equalsIgnoreCase(classifierStat.getType())) {
                Map<String, String> resMap = Maps.newHashMap();
                resMap.put("name", classifierStat.getName());
                resMap.put("code", classifierStat.getCode());
                resList.add(resMap);
            }
        }
        return resList;

        /*ISparkClientService sparkClient = SparkConsumer.getSparkClient(sparkDubboAddress);
        String information = sparkClient.registerInformation();
        Map map = JSON.parseObject(information, Map.class);
        Boolean loadFinished = (Boolean) map.get("loadFinished");
        Map<String, List<String>> successMap = (Map<String, List<String>>) map.get("successList");
        Map<String, List<String>> errorMap = (Map<String, List<String>>) map.get("errorList");

        String physicsSql = "SELECT DISTINCT ON " +
                "( A.global_code ) A.global_code, " +
                "A.ID, " +
                "A.code, " +
                "A.NAME, " +
                "A.db_type, " +
                "b.is_fast  " +
                "FROM " +
                "t_md_classifier_stat " +
                "A LEFT JOIN t_classifier_stat_extend b ON A.ID = b.classifier_stat_id  " +
                "WHERE " +
                "A.ID IN ( SELECT DISTINCT ( classifier_stat_id ) FROM t_dw_table_mapping ) UNION " +
                "SELECT  " +
                "global_code, " +
                "ID, " +
                "code, " +
                "NAME, " +
                "db_type, " +
                "is_fast  " +
                "FROM " +
                "t_md_logic_dataobj  " +
                "WHERE " +
                "db_type != 'FILE' OR db_type IS NULL";
        List<Map<String, String>> tableList = this.baseDao.sqlQueryForList(physicsSql);

        Map<String, Map<String, String>> cacheMap = Maps.newHashMap();
        tableList.stream().filter(stringMap -> {
            String globalCode = stringMap.get("global_code");
            return globalCode != null && !"".equals(globalCode);
        }).forEach(stringMap -> {
            String globalCode = stringMap.get("global_code");
            cacheMap.put(globalCode, stringMap);
        });

        TableRegisterVo registerVo = new TableRegisterVo();
        registerVo.setRegisterFinish(loadFinished);

        List<TableRegisterVo.RegisterInfoVo> registerInfoVos = new ArrayList<>();
        for (Map.Entry<String, List<String>> successEntry : successMap.entrySet()) {
            List<String> successEntryValue = successEntry.getValue();
            for (String array : successEntryValue) {
                if (array == null) {
                    continue;
                }
                TableRegisterVo.RegisterInfoVo registerInfoVo = new TableRegisterVo.RegisterInfoVo();
                Map<String, String> infoMap = cacheMap.get(array);
                registerInfoVo.setSuccess(true);
                registerInfoVo.setTableType(successEntry.getKey());
                registerInfoVo.setGlobalCode(array);
                if (infoMap != null) {
                    String code = infoMap.get("code");
                    String name = infoMap.get("name");
                    registerInfoVo.setCode(infoMap.get("code"));
                    registerInfoVo.setLabel(StringUtils.isNotBlank(name) ? name : code);
                    String isFast = infoMap.get("is_fast");
                    registerInfoVo.setFast(!StringUtils.isBlank(isFast) && !"false".equals(isFast) && !"0".equals(isFast));
                }

                registerInfoVos.add(registerInfoVo);
            }
        }

        for (Map.Entry<String, List<String>> errorEntry : errorMap.entrySet()) {
            List<String> errorEntryValue = errorEntry.getValue();
            for (String array : errorEntryValue) {
                if (array == null) {
                    continue;
                }
                TableRegisterVo.RegisterInfoVo registerInfoVo = new TableRegisterVo.RegisterInfoVo();
                Map<String, String> infoMap = cacheMap.get(array);
                registerInfoVo.setSuccess(false);
                registerInfoVo.setTableType(errorEntry.getKey());
                registerInfoVo.setGlobalCode(array);
                if (infoMap != null) {
                    registerInfoVo.setCode(infoMap.get("code"));
                    registerInfoVo.setLabel(infoMap.getOrDefault("name", infoMap.get("code")));
                    String isFast = infoMap.get("is_fast");
                    registerInfoVo.setFast(!StringUtils.isBlank(isFast) && !"false".equals(isFast) && !"0".equals(isFast));
                }

                registerInfoVos.add(registerInfoVo);
            }
        }

        registerVo.setRegisterInfos(registerInfoVos);
        return registerVo;*/
    }

}
