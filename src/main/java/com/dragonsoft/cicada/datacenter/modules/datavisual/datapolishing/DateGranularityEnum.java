package com.dragonsoft.cicada.datacenter.modules.datavisual.datapolishing;


import com.code.common.utils.assertion.Assert;
import com.dragonsoft.cicada.datacenter.modules.datavisual.datapolishing.datapolishings.*;
import com.google.common.collect.Maps;

import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/3/16 9:50
 */
public enum DateGranularityEnum {

    YEAR("年","year",new YearDataPolishing()),
    MONTH("月","month",new MonthDataPolishing()),
    DAY("日","day",new DayDataPolishing()),

    WEEK("周","week",new WeekDataPolishing()),
    QUARTER("季度","quarter",new QuarterDataPolishing());

    private String name;
    private String code;
    private AbsDataPolishing dataPolishing;

    public String getCode() {
        return code;
    }
    public String getName() {
        return name;
    }

    public AbsDataPolishing getDataPolishing() {
        return dataPolishing;
    }

    DateGranularityEnum(String name, String code, AbsDataPolishing dataPolishing) {
        this.name = name;
        this.code = code;
        this.dataPolishing = dataPolishing;
    }

    public static Map<String,String> getAllDateGranularity(){
        Map<String,String> dateGranularityMap = Maps.newHashMap();
        for (DateGranularityEnum value : DateGranularityEnum.values()) {
            dateGranularityMap.put(value.getName(),value.getCode());
        }
        return dateGranularityMap;
    }

    public static AbsDataPolishing getDatePolishingByCode(String code){
        DateGranularityEnum dateGranularityEnum = null;
        try {
            dateGranularityEnum = DateGranularityEnum.valueOf(code.toUpperCase());
        }catch (IllegalArgumentException iae){
            Assert.fail(String.format("数值对齐选择不正确，不存在[%s]",code));
        }
        return dateGranularityEnum.getDataPolishing();
    }
}
