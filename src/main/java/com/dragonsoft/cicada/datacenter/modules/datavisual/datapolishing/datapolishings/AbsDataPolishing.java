package com.dragonsoft.cicada.datacenter.modules.datavisual.datapolishing.datapolishings;

import com.code.common.utils.StringUtils;
import com.code.common.utils.assertion.Assert;
import com.code.dragonsoft.dataquery.service.querymodel.ColumnDataModel;
import com.code.metadata.datavisual.WidgetDatasetDims;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/3/16 10:02
 * 数值对齐策略抽象类
 */
@Slf4j
public abstract class AbsDataPolishing {

    protected String minDate = "";
    protected String maxDate = "";
    protected String dimsField = "";
    protected List<String> measFields = Lists.newArrayList();
    protected List<String> dataPolishingList = Lists.newArrayList();
    protected Map<Object, Map> columnDataValueMap = Maps.newHashMap();
    private String orderBy = "";
    private final String ASC = "asc";

    /**
     * 计算所有的时间
     */
    public abstract void calculateDate();

    /**
     * 校验数据是否符合时间颗粒度
     *
     * @param data
     */
    public abstract void checkData(String data);

    /**
     * 初始化数值对齐所需的相关变量
     *
     * @param columnDataModel
     * @param dims
     */
    public void initDataPolishing(ColumnDataModel columnDataModel, WidgetDatasetDims dims) {
        //获取最大值和最小值
        orderBy = dims.getOrderBy();
        if (StringUtils.isBlank(orderBy)) {
            Assert.fail("数值对齐数据需有序，请配置排序！");
        }
        //获取维度字段
        dimsField = StringUtils.isNotBlank(dims.getFiledAlias()) ? dims.getFiledAlias() : dims.getFiledCode();
        setMinAndMax(columnDataModel, orderBy);
        //获取度量字段
        setMeasFields(columnDataModel.getFieldName());
        //调用子类的calculateDate，计算相应时间颗粒度的所有可能的时间
        calculateDate();
        //根据维度的值将list转成map
        List<Map> fieldValues = columnDataModel.getFieldValue();
        columnDataValueMap = fieldValues.stream().collect(Collectors.toMap(m -> m.get(dimsField), v -> v));
    }

    /**
     * 初始化最大时间和最小时间
     *
     * @param columnDataModel
     * @param orderBy
     */
    private void setMinAndMax(ColumnDataModel columnDataModel, String orderBy) {
        List<Map> fieldValue = columnDataModel.getFieldValue();
        Map firstValue = fieldValue.get(0);
        Map endValue = fieldValue.get(fieldValue.size() - 1);
        this.minDate = (String) firstValue.get(dimsField);
        this.maxDate = (String) endValue.get(dimsField);
        if (!ASC.equalsIgnoreCase(orderBy)) {
            this.minDate = (String) endValue.get(dimsField);
            this.maxDate = (String) firstValue.get(dimsField);
        }
    }

    /**
     * 初始化度量字段
     *
     * @param fieldName
     */
    private void setMeasFields(Map<String, String> fieldName) {
        for (String key : fieldName.keySet()) {
            if (!key.equalsIgnoreCase(dimsField)) {
                measFields.add(key);
            }
        }
    }

    /**
     * 做数值对齐操作:区分数据是降序还是升序
     *
     * @param columnDataModel
     * @param dims
     */
    public void doDataPolishing(ColumnDataModel columnDataModel, WidgetDatasetDims dims) {
        this.initDataPolishing(columnDataModel, dims);
        //数据补零操作
        List<Map> newFieldValues = Lists.newArrayList();

        if (ASC.equalsIgnoreCase(orderBy)) {
            for (int i = 0; i < dataPolishingList.size(); i++) {
                doDataPolishing(newFieldValues, i);
            }
        } else {
            for (int i = dataPolishingList.size() - 1; i >= 0; i--) {
                doDataPolishing(newFieldValues, i);
            }
        }
        columnDataModel.setFieldValue(newFieldValues);
        dataPolishingList.clear();
    }

    /**
     * 做数值对齐操作：做数值对齐
     *
     * @param newFieldValues
     * @param i
     */
    protected void doDataPolishing(List<Map> newFieldValues, int i) {
        String dateValue = dataPolishingList.get(i);
        //从map中根据时间获取数据
        Map columnData = columnDataValueMap.get(dateValue);
        //为空，说明数据中没有这个时间点的数据，需要做补零操作
        if (columnData == null) {
            Map<String, Object> dataPolishingMap = Maps.newHashMap();
            dataPolishingMap.put(this.dimsField, dateValue);
            for (String measField : measFields) {
                dataPolishingMap.put(measField, 0);
            }
            newFieldValues.add(dataPolishingMap);
        } else {
            //调用子类的checkData方法，校验数据是否符合数值对齐
            checkData((String) columnData.get(this.dimsField));
            newFieldValues.add(columnData);
        }
    }


    /**
     * 计算年、月、日的全部时间
     *
     * @param beginDate
     * @param endDate
     * @param calendarType
     * @param format
     * @return
     */
    protected List<String> getAllDatesBetweenTwoDates(String beginDate, String endDate, int calendarType, String format, String granularity) {
        List<String> days = new ArrayList<>();
        DateFormat dateFormat = new SimpleDateFormat(format);
        try {
            Date start = dateFormat.parse(beginDate);
            Date end = dateFormat.parse(endDate);
            Calendar tempStart = Calendar.getInstance();
            tempStart.setTime(start);
            Calendar tempEnd = Calendar.getInstance();
            tempEnd.setTime(end);
            tempEnd.add(Calendar.DATE, +1);
            while (tempStart.before(tempEnd)) {
                days.add(dateFormat.format(tempStart.getTime()));
                //时间根据calendarType加1，每执行一个年、月或者日往前加1
                tempStart.add(calendarType, 1);
            }
        } catch (ParseException e) {
            log.error(e.getMessage(),e);
            Assert.fail(String.format("数值对齐为%s，数据为：%s", granularity, beginDate));
        }
        return days;
    }

    protected int parseInt(String data, String format) {
        int resInt = 0;
        try {
            resInt = Integer.parseInt(data);
        } catch (Exception e) {
            Assert.fail(String.format("数据[%s]不是%s时间格式数据！", data, format));
        }
        return resInt;
    }

    protected void granularituFail(String granularitu, String format, String data) {
        Assert.fail(String.format("数值对齐为%s，支持数据格式：%s，实际数据为：%s，数据或数值对齐选择不正确！",
                granularitu, format, data));
    }
}
