package com.dragonsoft.cicada.datacenter.modules.datavisual.controller;


import com.code.common.utils.StringUtils;
import com.code.common.utils.assertion.Assert;
import com.dragoninfo.dfw.bean.Result;
import com.dragonsoft.cicada.datacenter.modules.datavisual.dashboard.IDashboardAuthBuilder;
import com.dragonsoft.cicada.datacenter.modules.datavisual.vo.DashboardAuthVo;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.service.IRoleService;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.service.IUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Controller
@CrossOrigin
@RequestMapping("/dashboardAuth")
@Api(value = "DashboardAuthController|仪表盘权限控制器")
public class DashboardAuthController {

    @Autowired
    private IUserService userService;

    @Autowired
    private IRoleService roleService;

    @Autowired
    private IDashboardAuthBuilder dashboardAuthBuilder;

    @ResponseBody
    @PostMapping("/queryAllRoleAuth")
    @ApiOperation(value = "查询已授权角色,仪表盘使用")
    public Result queryAllRoleAuth(@RequestBody String dashboardId) {
        Assert.isTrue(StringUtils.isNotBlank(dashboardId), "仪表盘ID不能为空");
        List<String> ids = roleService.queryAllRoleAuth(dashboardId);
        return Result.success(ids);
    }

    @ResponseBody
    @PostMapping("/getAllUserAuth")
    @ApiOperation(value = "查询已授权用户，仪表盘使用")
    public Result getAllUserAuth(@RequestBody String dashboardId) {
        Assert.isTrue(StringUtils.isNotBlank(dashboardId), "仪表盘ID不能为空");
        List<String> ids = userService.getAllUserAuth(dashboardId);
        return Result.success(ids);
    }

    @ResponseBody
    @PostMapping("/dashboardAuthRegister")
    @ApiOperation(value = "添加仪表盘对象到功能表")
    public Result dashboardAuthRegister(@RequestBody DashboardAuthVo dashboardAuthVo, HttpServletRequest request) {
        String userId = (String) request.getSession().getAttribute("userId");
        dashboardAuthBuilder.saveDashboardAuthRegister(dashboardAuthVo, userId);
        return Result.success();
    }

    @ResponseBody
    @PostMapping("/addDashboardAuth")
    @ApiOperation(value = "添加仪表盘授权")
    public Result addDashboardAuth(@RequestBody DashboardAuthVo dashboardAuthVo, HttpServletRequest request) {
        dashboardAuthBuilder.addDashboardAuth(dashboardAuthVo);
        return Result.success();
    }

}
