package com.dragonsoft.cicada.datacenter.modules.datavisual.dashboard.impl;

import com.code.common.utils.StringUtils;
import com.code.common.utils.assertion.Assert;
import com.dragoninfo.dfw.entity.*;
import com.dragoninfo.dfw.service.*;
import com.dragonsoft.cicada.datacenter.modules.datavisual.dashboard.IDashboardAuthBuilder;
import com.dragonsoft.cicada.datacenter.modules.datavisual.vo.DashboardAuthVo;
import com.dragonsoft.cicada.datacenter.modules.datavisual.vo.DashboardVo;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.service.IRoleService;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.vo.RoleVo;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.vo.UserVo;
import com.fw.service.annotation.Service;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class DashboardAuthBuilder implements IDashboardAuthBuilder {

    public final static String DASHBOARD_FUNC_TYPE = "5";

    public final static String ENABLE_STATE = "1";

    @Autowired
    private SysFuncService sysFuncService;

    @Autowired
    private SysAuthObjFuncService sysAuthObjFuncService;

    @Autowired
    private SysAuthUserService sysAuthUserService;

    @Autowired
    private SysAuthRoleService sysAuthRoleService;

    @Autowired
    private SysAuthObjRelService sysAuthObjRelService;

    @Autowired
    private IRoleService roleService;

    /**
     * 添加仪表盘对象到功能表
     */
    @Override
    public void saveDashboardAuthRegister(DashboardAuthVo dashboardAuthVo, String userId) {
        Assert.notNull(dashboardAuthVo, "仪表盘分享数据信息不能为空");
        List<DashboardVo> dashboardVos = dashboardAuthVo.getDashboardVos();
        Assert.isTrue(CollectionUtils.isNotEmpty(dashboardVos), "仪表盘分享数据信息不能为空");
        for (DashboardVo dashboardVo : dashboardVos) {
            Assert.isTrue(StringUtils.isNotBlank(dashboardVo.getCode()), "仪表盘ID不能为空");
            Map<String, String> params = Maps.newHashMap();
            params.put("func_code", dashboardVo.getCode());
            TSysFuncBase funcBase = (TSysFuncBase) sysFuncService.query(params);
            if(funcBase==null){
                TSysFunc tSysFuncBase = new TSysFunc();
                tSysFuncBase.setFuncCode(dashboardVo.getCode());
                tSysFuncBase.setFuncName(dashboardVo.getName());
                tSysFuncBase.setFuncType(DASHBOARD_FUNC_TYPE);
                tSysFuncBase.setEnableState(ENABLE_STATE);
                tSysFuncBase.setDescription(userId);
                sysFuncService.store(tSysFuncBase);
            }
        }
    }

    /**
     * 添加仪表盘授权
     */
    @Override
    public void addDashboardAuth(DashboardAuthVo dashboardAuthVo) {
        Assert.notNull(dashboardAuthVo, "仪表盘分享数据信息不能为空");
        Assert.isTrue(CollectionUtils.isNotEmpty(dashboardAuthVo.getDashboardVos()), "仪表盘分享数据信息不能为空");
        List<String> functionCodes = dashboardAuthVo.getDashboardVos().stream().map(s -> s.getCode()).collect(Collectors.toList());
        //设置数据用户权限
        if (null != dashboardAuthVo.getUserVos()) {
            for (UserVo user : dashboardAuthVo.getUserVos()) {
                Map<String, String> userParams = Maps.newHashMap();
                userParams.put("id", user.getId());
                TSysAuthUser tSysAuthUser = (TSysAuthUser) sysAuthUserService.query(userParams);
                if (null != tSysAuthUser) {
                    this.saveDataSetRelation(tSysAuthUser, functionCodes);
                }
            }
        }
        //设置数据角色权限
        if (null != dashboardAuthVo.getRoleVos()) {
            for (RoleVo role : dashboardAuthVo.getRoleVos()) {
                Map<String, String> roleParams = Maps.newHashMap();
                roleParams.put("id", role.getRoleId());
                TSysAuthRole tSysAuthRole = (TSysAuthRole) sysAuthRoleService.query(roleParams);
                if (null != tSysAuthRole) {
                    //授权角色
                    this.saveDataSetRelation(tSysAuthRole, functionCodes);
                    //找到角色下的所有用户
                    /*List<TSysAuthObj> userBelongRoles = roleService.getTSysUserByRoleId(tSysAuthRole.getId());
                    //授权该角色下的用户
                    for (TSysAuthObj userBelongRole : userBelongRoles) {
                        this.saveDataSetRelation(userBelongRole, functionCodes);
                    }*/
                }
            }
        }
    }

    /**
     * 保存授权关系
     */
    public void saveDataSetRelation(TSysAuthObj tSysAuthObj, List<String> functionCodes) {
        for (String functionCode : functionCodes) {
            Assert.isTrue(StringUtils.isNotBlank(functionCode), "仪表盘ID不能为空");
            Map<String, String> params = Maps.newHashMap();
            params.put("func_code", functionCode);
            TSysFuncBase tSysFuncBase = (TSysFuncBase) sysFuncService.query(params);
            TSysAuthObjFunc tSysAuthObjFunc = new TSysAuthObjFunc();
            tSysAuthObjFunc.settSysFuncBase(tSysFuncBase);
            tSysAuthObjFunc.settSysAuthObj(tSysAuthObj);
            tSysAuthObjFunc.setCreateTime(new Date());

            Map<String, String> objParams = Maps.newHashMap();
            objParams.put("obj_id", tSysAuthObj.getId());
            objParams.put("func_code", functionCode);
            TSysAuthObjFunc relation = (TSysAuthObjFunc) sysAuthObjFuncService.query(objParams);
            if (null == relation) {
                sysAuthObjFuncService.store(tSysAuthObjFunc);
            }
        }
    }

    /**
     * 查询用户是否有仪表盘权限
     */
    @Override
    public boolean hasDashboardAuth(String dashboardId, String userId) {
        Assert.notNull(dashboardId, "仪表盘ID不能为空");
        Assert.notNull(userId, "用户ID不能为空");
        Map<String, String> objParams = Maps.newHashMap();
        objParams.put("obj_id", userId);
        objParams.put("func_code", dashboardId);
        TSysAuthObjFunc relation = (TSysAuthObjFunc) sysAuthObjFuncService.query(objParams);
        if (relation != null) {
            return true;
        } else {
            objParams = Maps.newHashMap();
            objParams.put("RELATION_TYPE", "1");
            objParams.put("FROM_OBJ_ID", userId);
            List<TSysAuthObjRel> tSysAuthObjRels = (List<TSysAuthObjRel>) sysAuthObjRelService.queryList(objParams);
            if (CollectionUtils.isNotEmpty(tSysAuthObjRels)) {
                objParams = Maps.newHashMap();
                for (TSysAuthObjRel tSysAuthObjRel : tSysAuthObjRels) {
                    objParams.put("obj_id", tSysAuthObjRel.getToAuthObj().getId());
                    objParams.put("func_code", dashboardId);
                    relation = (TSysAuthObjFunc) sysAuthObjFuncService.query(objParams);
                    if (relation != null) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

}
