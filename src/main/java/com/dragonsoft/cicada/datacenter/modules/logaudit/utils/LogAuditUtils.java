package com.dragonsoft.cicada.datacenter.modules.logaudit.utils;


import com.dragonsoft.cicada.datacenter.modules.logaudit.vo.LogAuditVo;
import com.dragonsoft.cicada.datacenter.modules.logaudit.vo.TypesEnum;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/10/9
 */

public class LogAuditUtils {


    public static final String SUCCESS = "success";
    public static final String FAIL = "fail";

    public static List<LogAuditVo> changeToVo(List<Map<String, Object>> dataLists) throws ParseException {
        List<LogAuditVo> logAudits = new ArrayList<>();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        for (Map<String, Object> dataList : dataLists) {
            LogAuditVo logAuditvo = new LogAuditVo();
            logAuditvo.setId(dataList.get("id").toString());
            logAuditvo.setIpAddress(dataList.get("visit_ip").toString());
            logAuditvo.setOperateType(dataList.get("operate_type") != null ? TypesEnum.getCode(dataList.get("operate_type").toString()) : "");
            logAuditvo.setOperateTime(formatter.format(formatter.parse(dataList.get("visit_time").toString())));
            logAuditvo.setUserName(dataList.get("obj_name").toString());
            logAuditvo.setState(dataList.get("code") == null ? SUCCESS : FAIL);
            if(dataList.get("operate_condition") != null) {
                logAuditvo.setOperateCondition(dataList.get("operate_condition").toString());
            }
            if(dataList.get("certificate_number") != null) {
                logAuditvo.setCertificateNumber(dataList.get("certificate_number").toString());
            }
            if(dataList.get("code") != null) {
                logAuditvo.setMessage(dataList.get("message") != null ? dataList.get("message").toString() : dataList.get("detail_message").toString());
            }
            logAuditvo.setFunctionalModule(dataList.get("parent_func_code").toString());
            logAudits.add(logAuditvo);
        }
        return logAudits;
    }

}
