package com.dragonsoft.cicada.datacenter.modules.managespace.control;

import com.code.common.paging.PageInfo;
import com.dragoninfo.dfw.bean.Result;
import com.dragonsoft.cicada.datacenter.modules.managespace.service.IModelMarketManageService;
import com.dragonsoft.cicada.datacenter.modules.system.dataportal.vo.PublishUrlVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/8/31
 */
@RestController
@CrossOrigin
@RequestMapping("/modelMarketManage")
@Api(value = "ModelMarketManageControl|模型市场管理")
public class ModelMarketManageControl {


    @Autowired
    private IModelMarketManageService modelMarketManageService;


    @RequestMapping("/saveOrUpdataPublishUrl")
    @ApiOperation(value = "发布进入审核")
    public Result saveOrUpdataPublishUrl(String id, String type, String name,String url, HttpServletRequest request){
        String userId = (String) request.getSession().getAttribute("userId");
        modelMarketManageService.saveOrUpdataPublishUrl(id,type,name,url,userId);
        return Result.success();
    }

    @RequestMapping("/queryModelMarketPage")
    @ApiOperation(value = "获取分页")
    public Result queryModelMarketPage(String name,String type, int page, int pageSize){
        PageInfo pageInfo = modelMarketManageService.queryPageByType(name,type,page,pageSize);
    return Result.success(pageInfo);
    }

    @RequestMapping("/reviewLaunch")
    @ApiOperation(value = "上架")
    public Result reviewLaunch(String id, String stat){
        modelMarketManageService.changePublishState(id,stat);
        return Result.success();
    }

    @RequestMapping("/reviewOffShelf")
    @ApiOperation(value = "下架")
    public Result reviewOffShelf(String id, String stat){
        modelMarketManageService.changePublishState(id,stat);
        return Result.success();
    }

    @RequestMapping("/getReviewLaunchUrl")
    @ApiOperation(value = "获取所有上架可视化链接")
    public Result getReviewLaunchUrl(){
        List<PublishUrlVo> publishUrlVos =  modelMarketManageService.getReviewLaunchUrl();
        return Result.success(publishUrlVos);
    }
}
