package com.dragonsoft.cicada.datacenter.modules.modeling.service;

import java.util.List;
import java.util.Map;

/**
 * 版权所有：厦门市巨龙信息科技有限公司
 * Copyright 2018 Xiamen Dragon Information Technology Co. Ltd.
 * All right reserved.
 * ====================================================
 * 修订记录：
 * No    日期				作者(操作:具体内容)
 * 1.    2021/9/15 11:05			baijx（<EMAIL>）(创建:创建文件)
 * ====================================================
 * 类描述：
 */
public interface ITransScenarioTemplateService {
    List<Map<String, String>> queryTransByName(List<String> modelNames);
    List<Map<String, String>> queryDashboardByName(List<String> modelNames);
    List<Map<String,String>> queryPortalByName(List<String> modelNames);
    List<Map<String,String>> queryScriptInfoByName(List<String> modelNames);

    void saveScenarioType(List<Map<String,String>> stringStringMaps);
}
