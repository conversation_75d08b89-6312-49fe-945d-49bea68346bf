package com.dragonsoft.cicada.datacenter.modules.modeling.importandexport.enums;

public enum ImportFilterEnum {
    TRANS("TRANS","方案"),
    CASE("CASE","用例"),
        SERVICE_API("SERVICE_API","服务");

    public String code;
    public String name;

    ImportFilterEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static ImportFilterEnum getInstanceByCode(String code) {
        ImportFilterEnum[] var1 = values();
        int var2 = var1.length;

        for(int var3 = 0; var3 < var2; ++var3) {
            ImportFilterEnum value = var1[var3];
            if (value.code.equals(code)) {
                return value;
            }
        }

        return TRANS;
    }
}
