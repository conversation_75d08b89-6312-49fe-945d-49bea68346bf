package com.dragonsoft.cicada.datacenter.modules.modeling.rapidanalysis.service;

import com.code.common.paging.PageInfo;
import com.dragonsoft.cicada.datacenter.modules.modeling.rapidanalysis.vo.ResultRapidTreeNode;
import com.fw.service.IService;

import java.util.List;

public interface RapidAnalysisService extends IService {

    /**
     * 获取整个快速分析目录
     * @param userId
     * @param listType
     * @return
     */
    public List<ResultRapidTreeNode> getAllRapidAnaTree(String userId, String listType);

    /**
     * 添加目录
     * @param parentClassifyId
     * @param classifyName
     * @param dirType
     * @param userId
     */
    void addRapidDir(String parentClassifyId,String classifyName,String dirType,String userId);

    /**
     * 重命名
     * @param classifyId
     * @param newClassifyName
     * @param userId
     */
    void updateRapidAnaDirName(String classifyId,String newClassifyName,String userId);

    /**
     * 移动
     * @param currentClassifyId
     * @param newParentClassifyId
     */
    void moveRapidDir(String currentClassifyId,String newParentClassifyId);

    /**
     * 删除
     */
    void deleteRapidDir(String classifyId);


    /**
     * 快速分析建模列表
     * @param classifyId
     * @return
     */
    PageInfo getRapidAnalysisPage(String classifyId,String rapidAnaName,Integer pageNum,Integer pageSize,String userId);
}
