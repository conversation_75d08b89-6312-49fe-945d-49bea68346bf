

--系统功能表
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('dataModeling', '0', '数据建模', NULL, NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('dataVisualAnalysis', '0', '可视化分析', NULL, NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('systemManagement', '0', '系统管理', NULL, NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('processModeling', '0', '流程建模', 'dataModeling', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('processModelingQueryTransTree', '0', '移动', 'processModeling', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('processModelingSaveTempTrans', '0', '新建模型', 'processModeling', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('processModelingSaseSettingPage', '0', '配置', 'processModeling', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('processModelingResultReuseObj', '0', '编辑', 'processModeling', NULL, NULL, NULL, '2020-08-19 18:45:57.516', NULL, '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('processModelingGetDataSets', '0', '预览结果集', 'processModeling', NULL, NULL, NULL, '2020-08-19 18:45:57.516', NULL, '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('processModelingUpdateTransName', '0', '重命名', 'processModeling', NULL, NULL, NULL, '2020-08-19 18:45:57.516', NULL, '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('processModelingCopyTrans', '0', '复制方案', 'processModeling', NULL, NULL, NULL, '2020-08-19 18:45:57.516', NULL, '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('processModelingDeleteTrans', '0', '删除', 'processModeling', NULL, NULL, NULL, '2020-08-19 18:45:57.516', NULL, '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('processModelingStartJob', '0', '启动', 'processModeling', NULL, NULL, NULL, '2020-08-19 18:45:57.516', NULL, '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('dataAssets', '0', '数据集管理', NULL, NULL, NULL, NULL, '2020-08-19 18:45:57.516', NULL, '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('dataWarehousePlan', '0', '数据源管理', NULL, NULL, NULL, NULL, '2020-08-19 18:45:57.516', NULL, '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('dashboard', '0', '数据可视化', 'dataVisualAnalysis', NULL, NULL, NULL, '2020-08-19 18:45:57.516', NULL, '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('dashboardDelete', '0', '删除', 'dashboard', NULL, NULL, NULL, '2020-08-19 18:45:57.516', NULL, '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('dashboardCopy', '0', '复制', 'dashboard', NULL, NULL, NULL, '2020-08-19 18:45:57.516', NULL, '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('dashboardSaveOrUpdate', '0', '新建仪表盘', 'dashboard', NULL, NULL, NULL, '2020-08-19 18:45:57.516', NULL, '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('dashboardUpdateGroup', '0', '移动', 'dashboard', NULL, NULL, NULL, '2020-08-19 18:45:57.516', NULL, '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('visualEditGetNoChart', '0', '编辑', 'dashboard', NULL, NULL, NULL, '2020-08-19 18:45:57.516', NULL, '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('roleManagement', '0', '角色管理', 'systemManagement', NULL, NULL, NULL, '2020-08-19 18:45:57.516', NULL, '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('roleManagementAddRole', '0', '添加', 'roleManagement', NULL, NULL, NULL, '2020-08-19 18:45:57.516', NULL, '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('roleManagementQueryRole', '0', '详情', 'roleManagement', NULL, NULL, NULL, '2020-08-19 18:45:57.516', NULL, '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('roleManagementSetAuth', '0', '权限设置', 'roleManagement', NULL, NULL, NULL, '2020-08-19 18:45:57.516', NULL, '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('roleManagementUpdataRole', '0', '编辑', 'roleManagement', NULL, NULL, NULL, '2020-08-19 18:45:57.516', NULL, '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('roleManagementDeleteRole', '0', '删除', 'roleManagement', NULL, NULL, NULL, '2020-08-19 18:45:57.516', NULL, '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('userManagement', '0', '用户管理', 'systemManagement', NULL, NULL, NULL, '2020-08-19 18:45:57.516', NULL, '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('userManagementAddUser', '0', '添加用户', 'userManagement', NULL, NULL, NULL, '2020-08-19 18:45:57.516', NULL, '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('userManagementQueryUser', '0', '详情', 'userManagement', NULL, NULL, NULL, '2020-08-19 18:45:57.516', NULL, '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('userManagementResetPassword', '0', '重置密码', 'userManagement', NULL, NULL, NULL, '2020-08-19 18:45:57.516', NULL, '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('userManagementEditUser', '0', '编辑', 'userManagement', NULL, NULL, NULL, '2020-08-19 18:45:57.516', NULL, '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('userManagementDeleteUser', '0', '删除', 'userManagement', NULL, NULL, NULL, '2020-08-19 18:45:57.516', NULL, '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('groupManagement', '0', '用户组管理', 'systemManagement', NULL, NULL, NULL, '2020-08-19 18:45:57.516', NULL, '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('groupManagementQueryGroup', '0', '详情', 'groupManagement', NULL, NULL, NULL, '2020-08-19 18:45:57.516', NULL, '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('groupManagementAddUserGroup', '0', '添加', 'groupManagement', NULL, NULL, NULL, '2020-08-19 18:45:57.516', NULL, '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('groupManagementEditUserGroup', '0', '编辑', 'groupManagement', NULL, NULL, NULL, '2020-08-19 18:45:57.516', NULL, '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('groupManagementDeleteUserGroup', '0', '删除', 'groupManagement', NULL, NULL, NULL, '2020-08-19 18:45:57.516', NULL, '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('dataSetOperation', '0', '数据集管理', 'dataAssets', NULL, NULL, NULL, '2020-08-19 18:45:57.516', NULL, '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('dataSetOperationAccreditLogicDataObj', '0', '添加授权数据集', 'dataSetOperation', NULL, NULL, NULL, '2020-08-19 18:45:57.516', NULL, '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('dataSetOperationDeleteDataSet', '0', '删除', 'dataSetOperation', NULL, NULL, NULL, '2020-08-19 18:45:57.516', NULL, '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('dataSetOperationGetDataSetPage', '0', '创建仪表板', 'dataSetOperation', NULL, NULL, NULL, '2020-08-19 18:45:57.516', NULL, '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('dataSetOperationMoveLogicDataSet', '0', '移动', 'dataSetOperation', NULL, NULL, NULL, '2020-08-19 18:45:57.516', NULL, '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('dataSetOperationCreateSql', '0', '即席sql分析', 'dataSetOperation', NULL, NULL, NULL, '2020-08-19 18:45:57.516', NULL, '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('dataObjectManage', '0', '数据对象管理', 'dataWarehousePlan', NULL, NULL, NULL, '2020-08-19 18:45:57.516', NULL, '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('dataObjectManageDataSetsAuthOne', '0', '权限设置', 'dataObjectManage', NULL, NULL, NULL, '2020-08-19 18:45:57.516', NULL, '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('dataObjectManageDataSetsAuth', '0', '批量授权', 'dataObjectManage', NULL, NULL, NULL, '2020-08-19 18:45:57.516', NULL, '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('dataWarehouse', '0', '数据源管理', 'dataWarehousePlan', NULL, NULL, NULL, '2020-08-19 18:45:57.516', NULL, '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('dataWarehouseDeleteDWDBInstance', '0', '删除', 'dataWarehouse', NULL, NULL, NULL, '2020-08-19 18:45:57.516', NULL, '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('dataWarehousePreview', '0', '数据预览', 'dataObjectManage', NULL, NULL, NULL, '2020-08-19 18:45:57.516', NULL, '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('dataWarehouseSaveDWDBInstance', '0', '新建', 'dataWarehouse', NULL, NULL, NULL, '2020-08-19 18:45:57.516', NULL, '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('dataWarehouseDeleteTable', '0', '删除', 'dataObjectManage', NULL, NULL, NULL, '2020-08-19 18:45:57.516', NULL, '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('dataWarehouseMoveDataOrigin', '0', '移动', 'dataWarehouse', NULL, NULL, NULL, '2020-08-19 18:45:57.516', NULL, '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('dataWarehouseRenameDataSet', '0', '重命名', 'dataWarehouse', NULL, NULL, NULL, '2020-08-19 18:45:57.516', NULL, '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('dataWarehouseViewDataSet', '0', '查看', 'dataWarehouse', NULL, NULL, NULL, '2020-08-19 18:45:57.516', NULL, '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('dataWarehouseViewGetDataObject', '0', '添加数据对象', 'dataWarehouse', NULL, NULL, NULL, '2020-08-19 18:45:57.516', NULL, '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('dataWarehouseGetTableInfo', '0', '编辑', 'dataObjectManage', NULL, NULL, NULL, '2020-08-19 18:45:57.516', NULL, '1');
-- INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('dataWarehouseSetFastTable', '0', '极速表', 'dataObjectManage', NULL, NULL, NULL, '2020-08-19 18:45:57.516', NULL, '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('dataWarehouseQueryElasticsColumnst', '0', '详情', 'dataObjectManage', NULL, NULL, NULL, '2020-08-19 18:45:57.516', NULL, '1');
-- INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('dataObjectManageCreateData', '0', '文件上传', 'dataObjectManage', NULL, NULL, NULL, '2020-08-19 18:45:57.516', NULL, '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('dataObjectManageOverwrite', '0', '更新', 'dataObjectManage', NULL, NULL, NULL, '2020-08-19 18:45:57.516', NULL, '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('dataSetOperationIsIssue', '0', '服务发布', 'dataSetOperation', NULL, NULL, NULL, '2020-08-19 18:45:57.516', NULL, '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('dataSetOperationCreateLogicDataSet', '0', '快速分析', 'dataSetOperation', NULL, NULL, NULL, '2020-08-19 18:45:57.516', NULL, '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('dataSetOperationDataColumn', '0', '编辑', 'dataSetOperation', NULL, NULL, NULL, '2020-08-19 18:45:57.516', NULL, '1');
-- INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('dataSetOperationEditDataSetColumn', '0', '高级配置', 'dataSetOperation', NULL, NULL, NULL, '2020-08-19 18:45:57.516', NULL, '1');
INSERT INTO "public"."t_sys_func" ("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('dashboardGetListByGroupId', '0', '查看', 'dashboard', NULL, NULL, NULL, '2020-11-26 15:06:47.029', NULL, '1');
INSERT INTO "public"."t_sys_func" ("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('dashboardGetDashboard', '0', '高级', 'dashboard', NULL, NULL, NULL, '2020-11-26 15:06:47.029', NULL, '1');

-- INSERT INTO "public"."t_sys_func" ("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('processModelingTestService', '0', '发布', 'processModeling', NULL, NULL, NULL, '2020-12-08 11:02:47.609', NULL, '1');

--算子管理功能
INSERT INTO "public"."t_sys_func" ("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('udf', '0', '算子管理', NULL, NULL, NULL, NULL, '2020-12-18 16:05:11.181', NULL, '1');
INSERT INTO "public"."t_sys_func" ("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('udfOperatorManage', '0', '算子管理', 'udf', NULL, NULL, NULL, '2020-12-18 16:05:11.184', NULL, '1');
INSERT INTO "public"."t_sys_func" ("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('udfOperatorManageDeleteUdf', '0', '删除', 'udfOperatorManage', NULL, NULL, NULL, '2020-12-18 16:05:11.186', NULL, '1');
INSERT INTO "public"."t_sys_func" ("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('udfOperatorManageEditUdf', '0', '编辑', 'udfOperatorManage', NULL, NULL, NULL, '2020-12-18 16:05:11.185', NULL, '1');
INSERT INTO "public"."t_sys_func" ("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('udfOperatorManageCreateUdf', '0', '注册', 'udfOperatorManage', NULL, NULL, NULL, '2020-12-18 16:05:11.185', NULL, '1');
INSERT INTO "public"."t_sys_func" ("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('udfOperatorManageAuth', '0', '权限', 'udfOperatorManage', NULL, NULL, NULL, '2020-12-18 16:05:11.184', NULL, '1');


--授权对象表
INSERT INTO "public"."t_sys_auth_obj"("id", "obj_type", "obj_code", "obj_name", "create_id", "update_id", "create_time", "update_time", "enable_state", "password", "certificate_type", "certificate_number", "phone", "email", "address", "login_number") VALUES ('43f965aac06c423f871bc2ec49ea65e1', '2', 'JS202006168908', '数据源管理员', NULL, '40289754739d4e7e11729d4e682b2020', '2020-06-16 18:12:34.065', '2020-08-19 20:07:15.511', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj"("id", "obj_type", "obj_code", "obj_name", "create_id", "update_id", "create_time", "update_time", "enable_state", "password", "certificate_type", "certificate_number", "phone", "email", "address", "login_number") VALUES ('5b2ef29d86f244e69af0977acee7555f', '2', 'JS202006114535', '数据分析师', NULL, '40289754739d4e7e11729d4e682b2020', '2020-06-11 17:03:20.803', '2020-08-19 20:10:33.384', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj"("id", "obj_type", "obj_code", "obj_name", "create_id", "update_id", "create_time", "update_time", "enable_state", "password", "certificate_type", "certificate_number", "phone", "email", "address", "login_number") VALUES ('9a5502cef25f4b31ba150bbda8d6c34a', '2', 'JS202006099508', '业务人员', NULL, '40289754739d4e7e11729d4e682b2020', '2020-06-09 17:02:31.599', '2020-08-19 20:11:16.807', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj"("id", "obj_type", "obj_code", "obj_name", "create_id", "update_id", "create_time", "update_time", "enable_state", "password", "certificate_type", "certificate_number", "phone", "email", "address", "login_number") VALUES ('d6121bc4248e45019942e2cb78362500', '2', 'JSadmin', '系统管理员', '40289754739d4e7e11729d4e682b2020', '40289754739d4e7e11729d4e682b2020', '2020-08-19 18:49:18', '2020-08-19 20:11:49.267', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj"("id", "obj_type", "obj_code", "obj_name", "create_id", "update_id", "create_time", "update_time", "enable_state", "password", "certificate_type", "certificate_number", "phone", "email", "address", "login_number") VALUES ('40289754739d4e7e11729d4e682b2020', '0', 'admin', 'admin', NULL, NULL, '2020-08-19 18:49:18', NULL, NULL, 'JJi9WDE2KByPuTpdpnNiGg==', NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO "public"."t_sys_auth_obj"("id", "obj_type", "obj_code", "obj_name", "create_id", "update_id", "create_time", "update_time", "enable_state", "password", "certificate_type", "certificate_number", "phone", "email", "address", "login_number") VALUES ('26321b75c339489e9396d81bc558b26f', '1', 'YHZadmin', '用户组', '40289754739d4e7e11729d4e682b2020', NULL, '2020-08-20 09:53:15', '2020-08-20 09:53:17', NULL, 'JJi9WDE2KByPuTpdpnNiGg==', NULL, NULL, NULL, NULL, NULL, 0);


--授权对象关系表
INSERT INTO "public"."t_sys_auth_obj_rel"("id", "relation_type", "from_obj_id", "to_obj_id", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('40289853739d4e8e01729d4e6832018g', '1', '40289754739d4e7e11729d4e682b2020', 'd6121bc4248e45019942e2cb78362500', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_rel"("id", "relation_type", "from_obj_id", "to_obj_id", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('40289853739d4e8e01729d4e7842129g', '0', '40289754739d4e7e11729d4e682b2020', '26321b75c339489e9396d81bc558b26f', NULL, NULL, NULL, NULL, NULL, NULL, NULL);


--授权对象功能表
-- INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a0174069dbf44005a', '43f965aac06c423f871bc2ec49ea65e1', 'dataObjectManageCreateData', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a0174069dbf48005b', '43f965aac06c423f871bc2ec49ea65e1', 'dataObjectManageDataSetsAuth', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a0174069dbf4b005c', '43f965aac06c423f871bc2ec49ea65e1', 'dataWarehousePreview', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a0174069dbf4d005d', '43f965aac06c423f871bc2ec49ea65e1', 'dataWarehouseDeleteTable', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a0174069dbf4f005e', '43f965aac06c423f871bc2ec49ea65e1', 'dataWarehouseGetTableInfo', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
-- INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a0174069dbf51005f', '43f965aac06c423f871bc2ec49ea65e1', 'dataWarehouseSetFastTable', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a0174069dbf520060', '43f965aac06c423f871bc2ec49ea65e1', 'dataObjectManageOverwrite', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a0174069dbf540061', '43f965aac06c423f871bc2ec49ea65e1', 'dataWarehouseQueryElasticsColumnst', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a0174069dbf560062', '43f965aac06c423f871bc2ec49ea65e1', 'dataObjectManageDataSetsAuthOne', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a0174069dbf5b0063', '43f965aac06c423f871bc2ec49ea65e1', 'dataWarehouseViewGetDataObject', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a0174069dbf5e0064', '43f965aac06c423f871bc2ec49ea65e1', 'dataWarehouseViewDataSet', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a0174069dbf5f0065', '43f965aac06c423f871bc2ec49ea65e1', 'dataWarehouseRenameDataSet', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a0174069dbf610066', '43f965aac06c423f871bc2ec49ea65e1', 'dataWarehouseMoveDataOrigin', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a0174069dbf630067', '43f965aac06c423f871bc2ec49ea65e1', 'dataWarehouseDeleteDWDBInstance', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a0174069dbf640068', '43f965aac06c423f871bc2ec49ea65e1', 'dataWarehouseSaveDWDBInstance', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a0c432006a', '5b2ef29d86f244e69af0977acee7555f', 'processModelingSaveTempTrans', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a0c433006b', '5b2ef29d86f244e69af0977acee7555f', 'processModelingQueryTransTree', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a0c435006c', '5b2ef29d86f244e69af0977acee7555f', 'processModelingCopyTrans', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a0c436006d', '5b2ef29d86f244e69af0977acee7555f', 'processModelingStartJob', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a0c438006e', '5b2ef29d86f244e69af0977acee7555f', 'processModelingDeleteTrans', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a0c43a006f', '5b2ef29d86f244e69af0977acee7555f', 'processModelingSaseSettingPage', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a0c43c0070', '5b2ef29d86f244e69af0977acee7555f', 'processModelingResultReuseObj', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a0c43d0071', '5b2ef29d86f244e69af0977acee7555f', 'processModelingGetDataSets', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a0c43f0072', '5b2ef29d86f244e69af0977acee7555f', 'processModelingUpdateTransName', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a0c4430073', '5b2ef29d86f244e69af0977acee7555f', 'dashboardDelete', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a0c4450074', '5b2ef29d86f244e69af0977acee7555f', 'dashboardCopy', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a0c4480075', '5b2ef29d86f244e69af0977acee7555f', 'dashboardSaveOrUpdate', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a0c4500076', '5b2ef29d86f244e69af0977acee7555f', 'dashboardUpdateGroup', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a0c4530077', '5b2ef29d86f244e69af0977acee7555f', 'visualEditGetNoChart', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a0c4590078', '5b2ef29d86f244e69af0977acee7555f', 'dataSetOperationAccreditLogicDataObj', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a0c45b0079', '5b2ef29d86f244e69af0977acee7555f', 'dataSetOperationCreateSql', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a0c45d007a', '5b2ef29d86f244e69af0977acee7555f', 'dataSetOperationCreateLogicDataSet', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a0c45f007b', '5b2ef29d86f244e69af0977acee7555f', 'dataSetOperationGetDataSetPage', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a0c461007c', '5b2ef29d86f244e69af0977acee7555f', 'dataSetOperationIsIssue', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a0c463007d', '5b2ef29d86f244e69af0977acee7555f', 'dataSetOperationDataColumn', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a0c464007e', '5b2ef29d86f244e69af0977acee7555f', 'dataSetOperationDeleteDataSet', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
-- INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a0c465007f', '5b2ef29d86f244e69af0977acee7555f', 'dataSetOperationEditDataSetColumn', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a0c4690080', '5b2ef29d86f244e69af0977acee7555f', 'dataSetOperationMoveLogicDataSet', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a16dce0082', '9a5502cef25f4b31ba150bbda8d6c34a', 'dashboardDelete', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a16dd00083', '9a5502cef25f4b31ba150bbda8d6c34a', 'dashboardCopy', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a16dd20084', '9a5502cef25f4b31ba150bbda8d6c34a', 'dashboardSaveOrUpdate', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a16dd40085', '9a5502cef25f4b31ba150bbda8d6c34a', 'dashboardUpdateGroup', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a16dd60086', '9a5502cef25f4b31ba150bbda8d6c34a', 'visualEditGetNoChart', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a16ddb0087', '9a5502cef25f4b31ba150bbda8d6c34a', 'dataSetOperationAccreditLogicDataObj', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a16ddd0088', '9a5502cef25f4b31ba150bbda8d6c34a', 'dataSetOperationIsIssue', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a16ddf0089', '9a5502cef25f4b31ba150bbda8d6c34a', 'dataSetOperationCreateLogicDataSet', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a16de2008a', '9a5502cef25f4b31ba150bbda8d6c34a', 'dataSetOperationDeleteDataSet', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a16de5008b', '9a5502cef25f4b31ba150bbda8d6c34a', 'dataSetOperationGetDataSetPage', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a16de7008c', '9a5502cef25f4b31ba150bbda8d6c34a', 'dataSetOperationDataColumn', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
-- INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a16de9008d', '9a5502cef25f4b31ba150bbda8d6c34a', 'dataSetOperationEditDataSetColumn', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a16deb008e', '9a5502cef25f4b31ba150bbda8d6c34a', 'dataSetOperationMoveLogicDataSet', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a16dec008f', '9a5502cef25f4b31ba150bbda8d6c34a', 'dataSetOperationCreateSql', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a1eca00091', 'd6121bc4248e45019942e2cb78362500', 'processModelingDeleteTrans', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a1eca30092', 'd6121bc4248e45019942e2cb78362500', 'processModelingCopyTrans', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a1eca60093', 'd6121bc4248e45019942e2cb78362500', 'processModelingSaveTempTrans', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a1ecae0094', 'd6121bc4248e45019942e2cb78362500', 'processModelingGetDataSets', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a1ecb00095', 'd6121bc4248e45019942e2cb78362500', 'processModelingStartJob', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a1ecb20096', 'd6121bc4248e45019942e2cb78362500', 'processModelingQueryTransTree', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a1ecb50097', 'd6121bc4248e45019942e2cb78362500', 'processModelingResultReuseObj', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a1ecb70098', 'd6121bc4248e45019942e2cb78362500', 'processModelingUpdateTransName', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a1ecba0099', 'd6121bc4248e45019942e2cb78362500', 'processModelingSaseSettingPage', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a1ecbf009a', 'd6121bc4248e45019942e2cb78362500', 'dashboardUpdateGroup', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a1ecc1009b', 'd6121bc4248e45019942e2cb78362500', 'visualEditGetNoChart', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a1ecc3009c', 'd6121bc4248e45019942e2cb78362500', 'dashboardSaveOrUpdate', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a1ecc5009d', 'd6121bc4248e45019942e2cb78362500', 'dashboardDelete', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a1ecc8009e', 'd6121bc4248e45019942e2cb78362500', 'dashboardCopy', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a1eccd009f', 'd6121bc4248e45019942e2cb78362500', 'roleManagementQueryRole', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a1eccf00a0', 'd6121bc4248e45019942e2cb78362500', 'roleManagementSetAuth', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a1ecd100a1', 'd6121bc4248e45019942e2cb78362500', 'roleManagementUpdataRole', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a1ecd300a2', 'd6121bc4248e45019942e2cb78362500', 'roleManagementDeleteRole', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a1ecd500a3', 'd6121bc4248e45019942e2cb78362500', 'roleManagementAddRole', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a1ecd800a4', 'd6121bc4248e45019942e2cb78362500', 'userManagementAddUser', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a1ecdb00a5', 'd6121bc4248e45019942e2cb78362500', 'userManagementQueryUser', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a1ecdd00a6', 'd6121bc4248e45019942e2cb78362500', 'userManagementEditUser', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a1ecdf00a7', 'd6121bc4248e45019942e2cb78362500', 'userManagementDeleteUser', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a1ece000a8', 'd6121bc4248e45019942e2cb78362500', 'userManagementResetPassword', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a1ece300a9', 'd6121bc4248e45019942e2cb78362500', 'groupManagementAddUserGroup', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a1ece500aa', 'd6121bc4248e45019942e2cb78362500', 'groupManagementQueryGroup', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a1ece700ab', 'd6121bc4248e45019942e2cb78362500', 'groupManagementDeleteUserGroup', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a1ece900ac', 'd6121bc4248e45019942e2cb78362500', 'groupManagementEditUserGroup', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a1ecee00ad', 'd6121bc4248e45019942e2cb78362500', 'dataSetOperationAccreditLogicDataObj', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a1ecf000ae', 'd6121bc4248e45019942e2cb78362500', 'dataSetOperationGetDataSetPage', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a1ecf100af', 'd6121bc4248e45019942e2cb78362500', 'dataSetOperationIsIssue', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a1ecf300b0', 'd6121bc4248e45019942e2cb78362500', 'dataSetOperationDeleteDataSet', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
-- INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a1ecf500b1', 'd6121bc4248e45019942e2cb78362500', 'dataSetOperationEditDataSetColumn', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a1ecf700b2', 'd6121bc4248e45019942e2cb78362500', 'dataSetOperationCreateSql', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a1ecf900b3', 'd6121bc4248e45019942e2cb78362500', 'dataSetOperationCreateLogicDataSet', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a1ecfb00b4', 'd6121bc4248e45019942e2cb78362500', 'dataSetOperationMoveLogicDataSet', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a1ecfe00b5', 'd6121bc4248e45019942e2cb78362500', 'dataSetOperationDataColumn', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a1ed0200b6', 'd6121bc4248e45019942e2cb78362500', 'dataWarehousePreview', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a1ed0400b7', 'd6121bc4248e45019942e2cb78362500', 'dataWarehouseGetTableInfo', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a1ed0600b8', 'd6121bc4248e45019942e2cb78362500', 'dataWarehouseDeleteTable', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a1ed0900b9', 'd6121bc4248e45019942e2cb78362500', 'dataObjectManageDataSetsAuthOne', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a1ed0c00ba', 'd6121bc4248e45019942e2cb78362500', 'dataObjectManageOverwrite', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
-- INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a1ed0e00bb', 'd6121bc4248e45019942e2cb78362500', 'dataWarehouseSetFastTable', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a1ed0f00bc', 'd6121bc4248e45019942e2cb78362500', 'dataObjectManageDataSetsAuth', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a1ed1100bd', 'd6121bc4248e45019942e2cb78362500', 'dataWarehouseQueryElasticsColumnst', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
-- INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a1ed1300be', 'd6121bc4248e45019942e2cb78362500', 'dataObjectManageCreateData', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a1ed1500bf', 'd6121bc4248e45019942e2cb78362500', 'dataWarehouseDeleteDWDBInstance', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a1ed1800c0', 'd6121bc4248e45019942e2cb78362500', 'dataWarehouseRenameDataSet', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a1ed1a00c1', 'd6121bc4248e45019942e2cb78362500', 'dataWarehouseViewGetDataObject', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a1ed1c00c2', 'd6121bc4248e45019942e2cb78362500', 'dataWarehouseSaveDWDBInstance', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a1ed1e00c3', 'd6121bc4248e45019942e2cb78362500', 'dataWarehouseViewDataSet', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a801e7406520a017406a1ed2000c4', 'd6121bc4248e45019942e2cb78362500', 'dataWarehouseMoveDataOrigin', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func" ("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('40289746780j910e01840ee62054038', 'd6121bc4248e45019942e2cb78362500', 'dashboardGetListByGroupId', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func" ("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('40299241780j920e01840ee63055048', 'd6121bc4248e45019942e2cb78362500', 'dashboardGetDashboard', NULL, NULL, NULL, NULL, NULL, NULL, NULL);


-- INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a841e7606521a027416a2eca10081', 'd6121bc4248e45019942e2cb78362500', 'processModelingTestService', NULL, NULL, NULL, NULL, NULL, NULL, NULL);


INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a9a841e7616521a037416a2gca20095', 'd6121bc4248e45019942e2cb78362500', 'udfOperatorManageAuth', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a831e7606421a021416a3eco10053', 'd6121bc4248e45019942e2cb78362500', 'udfOperatorManageCreateUdf', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a141e7608521a047416a7eca43048', 'd6121bc4248e45019942e2cb78362500', 'udfOperatorManageEditUdf', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8a8a831e7606621a023416a9eca61067', 'd6121bc4248e45019942e2cb78362500', 'udfOperatorManageDeleteUdf', NULL, NULL, NULL, NULL, NULL, NULL, NULL);


--数据挖掘
INSERT INTO "public"."t_sys_func" ("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('mlSql', '0', '数据挖掘', NULL, NULL, NULL, NULL, '2020-11-11 16:37:19.121', NULL, '1');
INSERT INTO "public"."t_sys_func" ("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('dataMining', '0', '数据挖掘', 'mlSql', NULL, NULL, NULL, '2020-11-11 16:37:19.121', NULL, '1');
INSERT INTO "public"."t_sys_func" ("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('dataMiningTaskRun', '0', '启动', 'dataMining', NULL, NULL, NULL, '2020-11-11 16:37:19.122', NULL, '1');
INSERT INTO "public"."t_sys_func" ("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('dataMiningUpdateTrans', '0', '编辑', 'dataMining', NULL, NULL, NULL, '2020-11-11 16:37:19.122', NULL, '1');
INSERT INTO "public"."t_sys_func" ("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('dataMiningUpdateSchedule', '0', '配置', 'dataMining', NULL, NULL, NULL, '2020-11-11 16:37:19.122', NULL, '1');
INSERT INTO "public"."t_sys_func" ("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('dataMiningCreateTrans', '0', '新建模型', 'dataMining', NULL, NULL, NULL, '2020-11-11 16:37:19.122', NULL, '1');

INSERT INTO "public"."t_sys_func" ("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('dataMiningCopyTrans', '0', '复制方案', 'dataMining', NULL, NULL, NULL, '2020-12-01 11:12:25.588', NULL, '1');
INSERT INTO "public"."t_sys_func" ("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('dataMiningGetDataSets', '0', '预览结果集', 'dataMining', NULL, NULL, NULL, '2020-12-01 11:12:25.588', NULL, '1');
INSERT INTO "public"."t_sys_func" ("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('dataMiningUpdateTransName', '0', '重命名', 'dataMining', NULL, NULL, NULL, '2020-12-01 11:12:25.588', NULL, '1');
-- INSERT INTO "public"."t_sys_func" ("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('dataMiningCreateService', '0', '发布', 'dataMining', NULL, NULL, NULL, '2020-12-01 11:12:25.589', NULL, '1');
INSERT INTO "public"."t_sys_func" ("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('dataMiningDeleteTrans', '0', '删除', 'dataMining', NULL, NULL, NULL, '2020-12-01 11:12:25.589', NULL, '1');
INSERT INTO "public"."t_sys_func" ("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('dataMiningMoveModel', '0', '移动', 'dataMining', NULL, NULL, NULL, '2020-12-01 11:12:25.589', NULL, '1');





INSERT INTO "public"."t_sys_auth_obj_func" ("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('40289753740g900e01740ee5204403p9', 'd6121bc4248e45019942e2cb78362500', 'dataMining', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func" ("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('40289753740v900e01740ee5204403o9', 'd6121bc4248e45019942e2cb78362500', 'mlSql', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func" ("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('40289753740c900e01740ee5204403j6', 'd6121bc4248e45019942e2cb78362500', 'dataMiningCreateTrans', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func" ("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('40289753740b900e01740ee5204403j8', 'd6121bc4248e45019942e2cb78362500', 'dataMiningUpdateSchedule', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func" ("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('40289753740x900e01740ee5204403h8', 'd6121bc4248e45019942e2cb78362500', 'dataMiningUpdateTrans', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func" ("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('40289753740j900e01740ee5204403g8', 'd6121bc4248e45019942e2cb78362500', 'dataMiningTaskRun', NULL, NULL, NULL, NULL, NULL, NULL, NULL);

INSERT INTO "public"."t_sys_auth_obj_func" ("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('40289753740j900e02740ec5204403y9', 'd6121bc4248e45019942e2cb78362500', 'dataMiningCopyTrans', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func" ("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('40289753740j112e01750ee5204403l6', 'd6121bc4248e45019942e2cb78362500', 'dataMiningGetDataSets', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func" ("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('40289754740j500e01743ee5304403m0', 'd6121bc4248e45019942e2cb78362500', 'dataMiningUpdateTransName', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
-- INSERT INTO "public"."t_sys_auth_obj_func" ("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('40289751740j560e81740ee6205540v8', 'd6121bc4248e45019942e2cb78362500', 'dataMiningCreateService', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func" ("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('40289756740j970e01340ee5204403h8', 'd6121bc4248e45019942e2cb78362500', 'dataMiningDeleteTrans', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func" ("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('40289754740j980e01760ee5204403a8', 'd6121bc4248e45019942e2cb78362500', 'dataMiningMoveModel', NULL, NULL, NULL, NULL, NULL, NULL, NULL);

