package com.dragonsoft.cicada.datacenter.modules.modeling.controller;

import com.code.common.utils.io.SerializableMsgCodec;
import com.code.metadata.variable.TransVariable;
import com.code.metadata.variable.TransVariableRelation;
import com.code.metaservice.variable.ITransVariableRelationService;
import com.code.metaservice.variable.ITransVariableService;
import com.dragoninfo.dfw.bean.Result;
import com.dragonsoft.cicada.datacenter.modules.modeling.service.TransTemplateService;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StreamUtils;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.Serializable;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 版权所有：厦门市巨龙信息科技有限公司
 * Copyright 2018 Xiamen Dragon Information Technology Co. Ltd.
 * All right reserved.
 * ====================================================
 * 修订记录：
 * No    日期				作者(操作:具体内容)
 * 1.    2021/11/30 2:12			baijx（<EMAIL>）(创建:创建文件)
 * ====================================================
 * 类描述：
 */
@CrossOrigin
@RestController
@RequestMapping("/variableTemplate")
@Slf4j
public class VariableTemplateController {

    @Autowired
    private TransTemplateService transTemplateService;

    @Autowired
    private ITransVariableService transVariableService;

    @Autowired
    private ITransVariableRelationService transVariableRelationService;

    @Data
    public static class ExportVariableBean implements Serializable {
        public List<TransVariable> transVariables;
        public Map<String, List<String>> transVariableRelations;
        public Map<String, String> transMetaCodeMap;
    }

    @RequestMapping("/exportVariables")
    public Result exportDataset(HttpServletResponse response) throws IOException {
        ExportVariableBean exportVariableBean = new ExportVariableBean();
        //导出全部的参数变量
        List<TransVariable> transVariableList = transTemplateService.queryAllVariables();
        List<TransVariableRelation> transVariableRelations = transTemplateService.queryAllVariablesRelations();
        exportVariableBean.setTransVariables(transVariableList);
        Map<String, List<String>> transVariableRelationMaps = new HashMap<>();
        transVariableRelations.stream().forEach(e -> {
            if (CollectionUtils.isNotEmpty(transVariableRelationMaps.get(e.getTransId()))) {
                transVariableRelationMaps.get(e.getTransId()).add(e.getVariableId());
            } else {
                transVariableRelationMaps.put(e.getTransId(), Lists.newArrayList(e.getVariableId()));
            }
        });
        //拿到旧的方案id与参数变量之间的关系
        exportVariableBean.setTransVariableRelations(transVariableRelationMaps);
        List<String> variableIds = Lists.newArrayList();
        transVariableList.stream().forEach(e -> variableIds.add("'" + e.getId() + "'"));
        //拿到方案id与方案code
        Map<String, String> transMetaCodeMap = transTemplateService.queryTransByVariableId(variableIds);
        exportVariableBean.setTransMetaCodeMap(transMetaCodeMap);
        // 4. 将全部源数据集对象和数仓目录挂接关系SQL，组装成LogicObjBean，序列化导出成logic文件
        try {
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("variable_params.model", "UTF-8"));
            StreamUtils.copy(SerializableMsgCodec.encode(exportVariableBean), response.getOutputStream());
            return Result.success();
        } finally {
            response.getOutputStream().close();
        }
    }


    /**
     * 全局参数导入
     *
     * @param path
     * @return
     * @throws IOException
     */
    @RequestMapping("/importVariables")
    public Result buildScenarioData(String path) throws IOException {
        // 1. 反序列化 .model 模型文件
        File file = new File(path.trim());
        byte[] bytes = FileUtils.readFileToByteArray(file);
        ExportVariableBean exportVariableBean = (ExportVariableBean) SerializableMsgCodec.decode(bytes);
        List<TransVariable> transVariables = exportVariableBean.getTransVariables();
        Map<String, String> transMetaCodeMap = exportVariableBean.getTransMetaCodeMap();
        Map<String, List<String>> transVariableRelations = exportVariableBean.getTransVariableRelations();
        List<String> mapCodes = Lists.newArrayList();
        transMetaCodeMap.entrySet().forEach(s -> {
            mapCodes.add(s.getValue() + (s.getValue().contains("@_@") ? "" : "@_@"));
        });
        //通过有参数变量的旧方案的code查出导入的新方案
        List<Map<String, String>> list = transTemplateService.queryTransByCodes(mapCodes);

        //参数变量
        if (CollectionUtils.isNotEmpty(transVariables)) {
            for (TransVariable transVariable : transVariables) {
                transTemplateService.mergeObj(transVariable);
            }
        }
        //参数变量关系保存
        if (CollectionUtils.isNotEmpty(list)) {
            for (Map<String, String> trans : list) {
                String transId = trans.get("id");
                String code = trans.get("code");
                if (StringUtils.isNotBlank(code) && code.contains("@_@")) {
                    code = code.split("@_@")[0];
                    List<String> oldTransIds = getOldTransIds(transMetaCodeMap, code);
                    for (String oldTransId : oldTransIds) {
                        if (CollectionUtils.isNotEmpty(transVariableRelations.get(oldTransId))) {
                            for (String variableId : transVariableRelations.get(oldTransId)) {
                                this.saveTransVariableRelation(variableId, transId);
                            }
                        }
                    }
                }
            }
        }
        return Result.success();
    }

    List<String> getOldTransIds(Map<String, String> transMetaCodeMap, String code) {
        List<String> oldIds = Lists.newArrayList();
        transMetaCodeMap.forEach((key, value) -> {
            if (value.equals(code)) oldIds.add(key);
        });
        return oldIds;
    }

    private String buildQuerySql(String sql, String idList) {
        List ids = Lists.newArrayList();
        if (StringUtils.isNotBlank(idList)) {
            for (String s : idList.split(",")) {
                ids.add("'" + s + "'");
            }
            // 6. 通过源数据集id，找到所有源数据集与数仓目录的挂接关系
            String condition = String.join(",", ids);
            //  0 逻辑数据集与数据源关系  1 逻辑数据集与数据集关系';
            return String.format(sql, condition);

        }

        return null;
    }

    private void saveTransVariableRelation(String variableId, String transId) {
        TransVariableRelation transVariableRelation;
        transVariableRelation = transVariableRelationService.getTransVariableRelation(transId, variableId);
        transVariableRelation.setCode("0");
        transVariableRelation.setType("0");
        transVariableRelation.setTransId(transId);
        transVariableRelation.setVariableId(variableId);
        transVariableRelationService.saveOrUpdate(transVariableRelation);
    }

    public static void main(String[] args) {
        StringUtils.isBlank("");
    }
}
