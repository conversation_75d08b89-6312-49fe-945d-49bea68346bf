package com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.util;

import com.code.common.dataset.type.field.FieldInfoConverterEnum;
import com.code.common.dataset.type.field.IFieldInfoConverter;
import com.code.metaservice.ddl.vo.LogicHttpColumns;
import com.code.std.types.NonStandardType;
import com.dragonsoft.cicada.datacenter.modules.metadata.vo.ChangeColumnVO;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class DataSetUtils {

    public static int defaultPageSize = 10000;

    public static ChangeColumnVO changeLogicToCat(Map<String, List<LogicHttpColumns>> sysColumns) {
        ChangeColumnVO changeColumnVO = new ChangeColumnVO();
        changeColumnVO.setAddColumnList(new ArrayList<>());
        for (LogicHttpColumns add : sysColumns.get("ADD")) {
            changeColumnVO.getAddColumnList().add(getChange(add));
        }
        changeColumnVO.setDeleteColumnList(new ArrayList<>());
        for (LogicHttpColumns delete : sysColumns.get("DELETE")) {
            changeColumnVO.getAddColumnList().add(getChange(delete));
        }
        changeColumnVO.setUpdateColumnList(new ArrayList<>());
        for (LogicHttpColumns edit : sysColumns.get("EDIT")) {
            changeColumnVO.getAddColumnList().add(getChange(edit));
        }
        return changeColumnVO;
    }

    public static Map<String, List<LogicHttpColumns>> changeLogicToCat(ChangeColumnVO changeColumnVO) {
        Map<String, List<LogicHttpColumns>> rtValue = new HashMap<>();
        List<LogicHttpColumns> addColumns = new ArrayList<>();
        for (ChangeColumnVO.ColumnProperty columnProperty : changeColumnVO.getAddColumnList()) {
            LogicHttpColumns logicHttpColumns = new LogicHttpColumns();
            logicHttpColumns.setCode(columnProperty.getCode());
            logicHttpColumns.setName(columnProperty.getName());
            addColumns.add(logicHttpColumns);
        }
        rtValue.put("ADD", addColumns);
        List<LogicHttpColumns> editColumns = new ArrayList<>();
        for (ChangeColumnVO.ColumnProperty columnProperty : changeColumnVO.getUpdateColumnList()) {
            LogicHttpColumns logicHttpColumns = new LogicHttpColumns();
            logicHttpColumns.setCode(columnProperty.getCode());
            logicHttpColumns.setName(columnProperty.getName());
            logicHttpColumns.setDataType(columnProperty.getDataType());
            editColumns.add(logicHttpColumns);
        }
        rtValue.put("EDIT", editColumns);
        List<LogicHttpColumns> deleteColumns = new ArrayList<>();
        for (ChangeColumnVO.ColumnProperty columnProperty : changeColumnVO.getDeleteColumnList()) {
            LogicHttpColumns logicHttpColumns = new LogicHttpColumns();
            logicHttpColumns.setCode(columnProperty.getCode());
            logicHttpColumns.setName(columnProperty.getName());
            deleteColumns.add(logicHttpColumns);
        }
        rtValue.put("DELETE", deleteColumns);
        return rtValue;
    }


    public static ChangeColumnVO.ColumnProperty getChange(LogicHttpColumns columns) {
        ChangeColumnVO.ColumnProperty column = new ChangeColumnVO.ColumnProperty();
        column.setCode(columns.getCode());
        column.setName(columns.getName());
        return column;
    }

    public static NonStandardType changType(String dbType, String columnName) {
        NonStandardType type = null;
        IFieldInfoConverter converter = FieldInfoConverterEnum.getConverter(dbType);
        columnName = converter.changeOtherFieldToCurrDbFieldType(columnName);
        //todo 下面这段谁有空谁重构吧
        //为什么有这个判断，还有下面的三目判断，因为大小写的问题，本质上解决还是去改那个包，全部统一标准，但是先不折腾，后面有需要再改一波，先记一下！
        if ("Hive".equalsIgnoreCase(dbType) || "tbase".equalsIgnoreCase(dbType)) {
            type = new NonStandardType(dbType + "." + columnName, columnName,
                    0, 0);
        } else {
            if(dbType.equalsIgnoreCase("vertica")) {
                type = new NonStandardType("Vertica." + columnName, columnName,0 , 0);
            }else{
                type = new NonStandardType(
                        dbType.equals("hwmpp") ? "hwmpp." + columnName: dbType.toUpperCase() + "." + columnName, columnName,
                        0, 0
                );
            }
        }
        return type;
    }
}
