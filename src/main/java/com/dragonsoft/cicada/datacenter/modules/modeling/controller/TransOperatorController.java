package com.dragonsoft.cicada.datacenter.modules.modeling.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.code.common.mist.plugin.config.service.impl.PluginConfigService;
import com.code.common.utils.R;
import com.code.common.utils.StringUtils;
import com.code.metadata.etl.trans.TransMeta;
import com.code.metadata.res.ddl.LogicDataObj;
import com.code.metaservice.ddl.ILogicDataObjService;
import com.code.metaservice.res.structured.rdb.IRdbDataObjService;
import com.code.mist.builder.model.DubboResult;
import com.code.mist.builder.model.trans.TransPluginPropertysVo;
import com.code.mist.builder.service.ITransformService;
import com.code.mist.builder.service.dubbo.ITransformApiService;
import com.code.mlsql.utils.GraphParseHelper;
import com.code.thirdplugin.cicada.sql.meta.meta.input.CicadaStandardSqlInput;
import com.code.thirdplugin.cicada.sql.meta.meta.output.CicadaStandardSqlOutput;
import com.dragoninfo.dfw.annotation.FuncScanAnnotation;
import com.dragoninfo.dfw.annotation.ValidateAndLogAnnotation;
import com.dragoninfo.dfw.bean.Result;
import com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.controller.ServiceDcPublishController;
import com.dragonsoft.cicada.datacenter.modules.modeling.service.DataModelingService;
import com.dragonsoft.cicada.datacenter.modules.modeling.service.TransCloneService;
import com.dragonsoft.cicada.datacenter.modules.modeling.service.TransTemplateService;
import com.dragonsoft.cicada.datacenter.modules.modeling.vo.CopyPluginVo;
import com.dragonsoft.cicada.datacenter.modules.modeling.vo.QuickAnalysisTransHop;
import com.dragonsoft.cicada.datacenter.modules.supermarkmodel.service.IMyModelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/10/12
 */
@SuppressWarnings("AlibabaSwitchStatement")
@CrossOrigin
@RestController
@Api(value = "TransOperatorController|方案插件操作控制器")
@RequestMapping(value = "/transOperator")
public class TransOperatorController {

    @Autowired
    private ITransformApiService transformApiService;

    @Autowired
    private DataModelingService dataModelingService;

    @Autowired
    private ITransformService transformService;

    @Autowired
    private TransCloneService transCloneService;

    @Autowired
    private IMyModelService myModelServiceImpl;

    @Autowired
    private ServiceDcPublishController serviceDcPublishController;


    @Autowired
    ILogicDataObjService logicDataObjService;

    @Autowired
    TransTemplateService transTemplateService;

    @Autowired
    IRdbDataObjService rdbDataObjService;


    @Autowired
    private PluginConfigService pluginConfigService;
    /**
     * 添加插件步骤
     *
     * @param pluginCode
     * @param transName
     * @param x
     * @param y
     * @param parentTransId
     * @return
     */
    @GetMapping(value = "/addPluginTranStep")
    public Result addPluginTranStep(String pluginCode, String transName, String x, String y, String parentTransId) {
        DubboResult<String> result = transformApiService.createTransStep(pluginCode, transName, x, y, parentTransId);
        return Result.toResult(R.ok(result.getData()));
    }

    /**
     * 添加步骤连线
     *
     * @param fromTransName
     * @param toTransName
     * @param fromTransId
     * @param toTransId
     * @param transId
     * @return
     */
    @GetMapping(value = "/addTransHop")
    public Result addTransHop(String fromTransName, String toTransName, String fromTransId, String toTransId, String transId) {
        DubboResult result = transformApiService.createTransHop(fromTransName, toTransName, fromTransId, toTransId, transId);
        return Result.toResult(R.ok(result.getData()));
    }

    /**
     * 3.0添加步骤连线操作
     */
    @GetMapping(value = "/addTransHopWithDirection")
    public Result addTransHopWithDirection(String fromTransName, String toTransName, String fromTransId, String toTransId, String transId, String direction) {
        DubboResult result = transformApiService.createTransHop(fromTransName, toTransName, fromTransId, toTransId, transId, direction);
        return Result.toResult(R.ok(result.getData()));
    }

    /**
     * 快速分析从中间新增一个插件  A->B    在A B之间插入C  变成 A-C-B
     *
     * @param vo
     * @return
     */
    @PostMapping(value = "/addQuickAnalysisTransHop")
    public Result addQuickAnalysisTransHop(@RequestBody QuickAnalysisTransHop vo) {

        //删除A-B之间的连线
        transformApiService.deleteTransStepHos(vo.getBeforeTransId(), vo.getAfterTransId(), vo.getTransId());
        //连接A和C之间的连线
        transformApiService.createTransHop(vo.getBeforeTransId(), vo.getCurrendTransName(), vo.getBeforeTransId(), vo.getCurrendTransId(), vo.getTransId(), vo.getAfterDirection());
        //连接C和B之间的连线
        transformApiService.createTransHop(vo.getCurrendTransName(), vo.getAfterTransName(), vo.getCurrendTransId(), vo.getAfterTransId(), vo.getTransId(), vo.getAfterDirection());
        return Result.toResult(R.ok());
    }


    /**
     * 删除步骤连线
     *
     * @param fromTransId
     * @param toTransId
     * @param transId
     * @return
     */
    @GetMapping(value = "/deleteTransHop")
    public Result deleteTransHop(String fromTransId, String toTransId, String transId) {
        DubboResult result = transformApiService.deleteTransStepHos(fromTransId, toTransId, transId);
        return Result.toResult(R.ok(result.getData()));
    }


    /**
     * 移动转换步骤
     *
     * @param x
     * @param y
     * @param transStepId
     * @return
     */
    @GetMapping(value = "/moveTransStep")
    public Result moveTransStep(String x, String y, String transStepId) {
        DubboResult result = transformApiService.moveTransStep(x, y, transStepId);
        return Result.toResult(R.ok(result.getData()));
    }


    /**
     * 方案调试
     *
     * @param transId
     * @param parentTransId
     * @return
     */
    @GetMapping(value = "/debugTrans")
    public Result debugTransMeta(String transId, String parentTransId) {
        DubboResult<Map<String, String>> result = transformApiService.deBugTrans(transId, parentTransId);
        return Result.toResult(R.ok(result.getData()));
    }


    /**
     * 删除方案步骤
     *
     * @param stepId
     * @param transId
     * @return
     */
    @GetMapping(value = "/deleteTransStep")
    public Result deleteTransStep(String stepId, String transId) {
        DubboResult result = transformApiService.deleteTransStep(stepId, transId);
        return Result.toResult(R.ok(result.getData()));
    }

    /**
     * 删除方案步骤
     *
     * @param stepId
     * @param transId
     * @return
     */
    @GetMapping(value = "/deleteTransStepforQuick")
    public Result deleteTransStepforQuick(String stepId, String transId) {
        String s = transCloneService.queryDeleteRight(stepId);
        if (StringUtils.isNotBlank(s)) {
            DubboResult result = transformApiService.deleteTransStep(s, transId);
        }
        DubboResult result = transformApiService.deleteTransStep(stepId, transId);

        return Result.toResult(R.ok(result.getData()));
    }

    /**
     * 更新方案名称
     *
     * @param reqMap
     * @return
     */
    @PostMapping(value = "/updateTransName")
    @FuncScanAnnotation(code = "processModelingUpdateTransName", name = "重命名", parentCode = "processModeling")
    @ValidateAndLogAnnotation
    public Result updateTransName(@RequestBody Map reqMap) {
        String name = reqMap.get("name").toString();
        String transId = reqMap.get("transId").toString();
        DubboResult result = transformApiService.updateTransStepName(transId, name);
        return Result.toResult(R.ok(result.getMsg()));
    }


    /**
     * 更改处理模式
     *
     * @param tranStepId
     * @param handleMode
     * @return
     */
    @GetMapping(value = "/updateTransDistribute")
    public Result updateTransDistribute(String tranStepId, String handleMode) {
        DubboResult result = transformApiService.updateTransDistribute(tranStepId, handleMode);
        return Result.toResult(R.ok(result.getData()));
    }


    /**
     * 更改处理模式
     *
     * @param tranStepId
     * @param handleMode
     * @return
     */
    @GetMapping(value = "/updateHandleMode")
    public Result updateHandleMode(String tranStepId, String handleMode) {
        DubboResult result = transformApiService.updateHandleMode(tranStepId, handleMode);
        return Result.toResult(R.ok(result.getData()));
    }


    /**
     * 更新抽样模式
     *
     * @param dataSetMetaId
     * @param exp
     * @param distinctExpress
     * @return
     */
    @GetMapping(value = "/updateSample")
    public Result updateSample(String dataSetMetaId, String exp, boolean distinctExpress) {
        DubboResult result = transformApiService.updateSampleExp(dataSetMetaId, exp, distinctExpress ? "1" : "0");
        return Result.toResult(R.ok(result.getData()));
    }


    /**
     * 更新步骤名称
     *
     * @param transStepId
     * @param transName
     * @return
     */
    @GetMapping(value = "/updateTransStepName")
    public Result updateTransStepName(String transStepId, String transName) {
        DubboResult result = transformApiService.updateTransStepName(transStepId, transName);
        if(!result.isSuccess()){
            return Result.toResult(R.error(result.getMsg()));
        }else {
            return Result.toResult(R.ok(result.getData()));
        }

    }


    /**
     * 修改复制分发模式
     *
     * @param datasetCopy
     * @param tranStepId
     * @return
     */
    @GetMapping(value = "/updateDatasetCopy")
    public Result updateTransDatasetCopy(boolean datasetCopy, String tranStepId) {
        DubboResult result = transformApiService.updateTransDatasetCopy(tranStepId, datasetCopy);
        return Result.toResult(R.ok(result.getData()));
    }


    /**
     * 更新线程数
     *
     * @param threadCount
     * @param tranStepId
     * @return
     */
    @GetMapping(value = "/updateThreadCount")
    public Result updateTransThreadCount(String threadCount, String tranStepId) {
        DubboResult result = transformApiService.updateTransThreadCount(tranStepId, threadCount);
        return Result.toResult(R.ok(result.getData()));
    }


    /**
     * 方案复制
     *
     * @param transId
     * @param dirParentId
     * @return
     */
    @GetMapping(value = "/copyTrans")
    @FuncScanAnnotation(code = "processModelingCopyTrans", name = "复制方案", parentCode = "processModeling")
    @ValidateAndLogAnnotation
    public Result copyTrans(String transId, String dirParentId) {
//        Map<String, String> map = transformService.copyTransId(transId, dirParentId);
        Map<String, String> map = transCloneService.saveCopyTrans(transId, dirParentId, null, null);
        return Result.toResult(R.ok(map));
    }

    /**
     * 方案另存为
     *
     * @param
     * @param map
     * @return
     */
    @PostMapping(value = "/asSaveTrans")
    public Result asSaveTrans(@RequestBody Map<String, String> map, HttpServletRequest request) {

        String userId = (String) request.getSession().getAttribute("userId");
        String transId = map.get("transId");
        String classifyId = map.get("classifyId");
        String transName = map.get("transName");
        String memo = map.get("memo");
        Integer version = StrUtil.isNotBlank(map.get("version"))?Integer.parseInt(map.get("version")) : null;
        String productionFirm = map.get("productionFirm");
        Map<String, String> mapData = transCloneService.copy(transId, classifyId, transName, memo,userId,version,productionFirm);
        return Result.toResult(R.ok(mapData));
    }



    @GetMapping("/saveStepSql")
    public Result saveStepSql(String tranStepId) {
        transCloneService.saveStepSql(tranStepId);
        return Result.success(R.ok());
    }



    /**
     * 方案删除
     *
     * @param transId
     * @return
     */
    @GetMapping(value = "/deleteTrans")
    @FuncScanAnnotation(code = "processModelingDeleteTrans", name = "删除", parentCode = "processModeling")
    @ValidateAndLogAnnotation
    public Result deleteTrans(String transId) {
        DubboResult result = transformApiService.deleteTrans(transId);
        //删除模型市场数据
        myModelServiceImpl.deleteMarkModelByTransId(transId);
        List<Map> sourceid = myModelServiceImpl.obtainid(transId);
        if (sourceid != null) {

            String id = null;
            for (int i = 0; i < sourceid.size(); i++) {
                id = String.valueOf(sourceid.get(i).get("id"));

                serviceDcPublishController.offlineServiceByServiceMetaId(id);
            }
        }

        return Result.toResult(R.ok(result.getData()));
    }


    /**
     * 设置过程日志级别
     *
     * @param tranStepId
     * @param logLevel
     * @return
     */
    @GetMapping(value = "/updateTransLogLevel")
    public Result updateTransLogLevel(String tranStepId, String logLevel) {
        switch (logLevel) {
            case "0":
                logLevel = "TRACE";
                break;
            case "1":
                logLevel = "DEBUG";
                break;
            case "2":
                logLevel = "INFO";
                break;
            case "3":
                logLevel = "WARN";
                break;
            case "4":
                logLevel = "ERROR";
                break;
            default:
                break;
        }
        DubboResult result = transformApiService.updateTransLogLevel(tranStepId, logLevel);
        return Result.toResult(R.ok(result.getData()));
    }

    @GetMapping(value = "/getPluginInfo")
    public Result getPluginInfo() {
        Map<String, Object> pluginList = dataModelingService.getPluginInfo();
        return Result.toResult(R.ok(pluginList));
    }

    @GetMapping(value = "/updateTransMetaNameAndMemo")
    @ApiOperation(value = "更新步骤插件的属性tab页的字段")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "tranStepId", value = "步骤插件的id", required = true, dataType = "String"),
            @ApiImplicitParam(name = "name", value = "插件属性tab页别名字段", required = true, dataType = "String"),
            @ApiImplicitParam(name = "memo", value = "插件属性tab页描述字段", required = true, dataType = "String"),
    })
    public Result updateTransMetaNameAndMemo(String tranStepId, String name, String memo) {
        DubboResult dubboResult = transformApiService.updateTransNameAndMemo(tranStepId, name, memo);
        return Result.success(dubboResult.getData());
    }

    @GetMapping(value = "/getTransMetaByTranStepId")
    @ApiOperation(value = "获取步骤插件的属性tab页的字段")
    @ApiImplicitParam(name = "tranStepId", value = "步骤插件的id", required = true, dataType = "String")
    public Result getTransMetaByTranStepId(String tranStepId) {
        DubboResult<TransPluginPropertysVo> transMetaByTranStepId = transformApiService.getTransMetaByTranStepId(tranStepId);
        Map<String, Object> data = new HashMap<>();
        data.put("name", transMetaByTranStepId.getData().getName());
        data.put("id", transMetaByTranStepId.getData().getId());
        data.put("memo", transMetaByTranStepId.getData().getMemo());
        data.put("pluginTypeName", transMetaByTranStepId.getData().getPluginTypeName());
        TransMeta step = pluginConfigService.get(TransMeta.class, tranStepId);
        String logicId = "";
        if (step.getUsedPlugin().getCode().equals("cicadaStandardSqlInput")) {
            CicadaStandardSqlInput input = this.pluginConfigService.getPluginInstance(CicadaStandardSqlInput.class, tranStepId);
            logicId = input.getLogicObjId();

        } else {
            CicadaStandardSqlOutput output = this.pluginConfigService.getPluginInstance(CicadaStandardSqlOutput.class, tranStepId);
            logicId = output.getTableId();
        }
        if (StringUtils.isNotBlank(logicId)) {
            Map logicAndDatasource = logicDataObjService.findLogicAndDatasource(logicId);
            data.put("logicName", logicAndDatasource.get("logicname"));
            data.put("logicCode", logicAndDatasource.get("logiccode"));
            if(logicAndDatasource == null || logicAndDatasource.get("dbcode") == null){
                logicAndDatasource = transTemplateService.findDatasourceByRdbId(logicId);
                data.put("logicName", logicAndDatasource.get("tablename"));
                data.put("logicCode", logicAndDatasource.get("tablecode"));
            }
            data.put("tableName", logicAndDatasource.get("tablename"));
            data.put("tableCode", logicAndDatasource.get("tablecode"));
            data.put("dbName", logicAndDatasource.get("dbname"));
            data.put("dbCode", logicAndDatasource.get("dbcode"));

            if (CollectionUtil.isEmpty(logicAndDatasource)) {
                LogicDataObj logicDataObjById = logicDataObjService.findLogicDataObjById(logicId);
                if (logicDataObjById != null) {
                    data.put("logicName", logicDataObjById.getName());
                    data.put("logicCode", logicDataObjById.getCode());
                    data.put("tableName", logicDataObjById.getName());
                    data.put("tableCode", logicDataObjById.getCode());

                    String rdbId = logicDataObjById.getOwnerId();
                    Map<String, Object> schemaByRdbId = transTemplateService.getSchemaByRdbId(rdbId);
                    data.put("dbName", schemaByRdbId.get("name"));
                    data.put("dbCode", schemaByRdbId.get("code"));
                }
            }
        }

        return Result.success(data);
    }


    //插件级别复制
    @PostMapping("/copyTransMetaStep")
    public Result copyTransMetaStep(@RequestBody CopyPluginVo vo) {
        Map<String, Object> map = transCloneService.copyPlugin(vo);
        return Result.toResult(R.ok(map));
    }
}
