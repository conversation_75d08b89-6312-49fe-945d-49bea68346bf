--系统功能表
-- 1. 建模空间 ********************************************************************************************************************************************************************************************************************************************************************************************************************************》
update "public"."t_sys_func" set "func_name" =  '建模空间' where func_code = 'dataModeling';
-- 1.1 数据建模--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------》
update "public"."t_sys_func" set "func_name" =  '数据建模' where func_code = 'processModeling';
update "public"."t_sys_func" set "func_name" =  '流程建模' where func_code = 'processModelingSaveTempTrans';
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('processModelingRapidAnalysis', '0', '快速分析', 'processModeling', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1');
-- 2. 可视空间 ********************************************************************************************************************************************************************************************************************************************************************************************************************************》
update "public"."t_sys_func" set "func_name" =  '可视空间' where func_code = 'dataVisualAnalysis';
-- 2.1 仪表盘 --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------》
update "public"."t_sys_func" set "func_name" =  '仪表盘' where func_code = 'dashboard';
-- 2.2 主题门户 --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------》
update "public"."t_sys_func" set "parent_func_code" = 'dataVisualAnalysis' where func_code = 'themePortal';
-- 3. 数据空间********************************************************************************************************************************************************************************************************************************************************************************************************************************》
update "public"."t_sys_func" set "func_name" =  '数据空间' where func_code = 'dataAssets';
-- 3.1 数据准备 --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------》
update "public"."t_sys_func" set "func_name" =  '数据准备' where func_code = 'dataSetOperation';
update "public"."t_sys_func" set "func_name" =  '添加数据集' where func_code = 'dataSetOperationAccreditLogicDataObj';
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('dataSetOperationSetSingleShare', '0', '分享', 'dataSetOperation', NULL, NULL, NULL, '2020-08-19 18:45:57.516', NULL, '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('dataSetOperationSetBatchSharing', '0', '批量分享', 'dataSetOperation', NULL, NULL, NULL, '2020-08-19 18:45:57.516', NULL, '1');
-- 3.2 数据连接 --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------》
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('dataConnection', '0', '数据连接', 'dataAssets', NULL, NULL, NULL, '2020-08-19 18:45:57.516', NULL, '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('dataConnectionTestConnection', '0', '测试连接', 'dataConnection', NULL, NULL, NULL, '2020-08-19 18:45:57.516', NULL, '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('dataConnectionAddDataTable', '0', '添加数据表', 'dataConnection', NULL, NULL, NULL, '2020-08-19 18:45:57.516', NULL, '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('dataConnectionEditDataSource', '0', '编辑', 'dataConnection', NULL, NULL, NULL, '2020-08-19 18:45:57.516', NULL, '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('dataConnectionDeleteDataSource', '0', '删除', 'dataConnection', NULL, NULL, NULL, '2020-08-19 18:45:57.516', NULL, '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('dataConnectionSynchronousDataSource', '0', '同步', 'dataConnection', NULL, NULL, NULL, '2020-08-19 18:45:57.516', NULL, '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('dataConnectionAddDataSource', '0', '添加数据源', 'dataConnection', NULL, NULL, NULL, '2020-08-19 18:45:57.516', NULL, '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('dataConnectionDeleteTable', '0', '删除数据表', 'dataConnection', NULL, NULL, NULL, '2020-08-19 18:45:57.516', NULL, '1');
-- 4. 管理空间 ********************************************************************************************************************************************************************************************************************************************************************************************************************************》
update "public"."t_sys_func" set "func_name" =  '管理空间' where func_code = 'systemManagement';
-- 4.1 角色管理--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------》
-- 4.2 用户组管理--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------》
-- 4.3 用户管理--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------》
update "public"."t_sys_func" set "func_name" =  '添加' where func_code = 'userManagementAddUser';
-- 4.4 算子管理--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------》
update "public"."t_sys_func" set "parent_func_code" =  'systemManagement' where func_code = 'udfOperatorManage';

--授权对象功能表
-- 系统管理员
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('88c5ea08b0cf48d6bf134cd37675b371', 'd6121bc4248e45019942e2cb78362500', 'processModelingRapidAnalysis', NULL, NULL, NULL, NULL, NULL, NULL, NULL);




INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('88c5ea08b0cf48d6bf134cd37625b382', 'd6121bc4248e45019942e2cb78362500', 'dataSetOperationSetBatchSharing', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('88c5ea08b0cf48d6bf134cd37275b393', 'd6121bc4248e45019942e2cb78362500', 'dataSetOperationSetSingleShare', NULL, NULL, NULL, NULL, NULL, NULL, NULL);


INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('065f17c0a7f64bd79b391571c9665b81', 'd6121bc4248e45019942e2cb78362500', 'dataConnectionTestConnection', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('82d6282a15a84a00839dc07981773e5c', 'd6121bc4248e45019942e2cb78362500', 'dataConnectionAddDataTable', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('1411670160ab4c10b592ac2799eb91e4', 'd6121bc4248e45019942e2cb78362500', 'dataConnectionEditDataSource', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('731dac69503a4c6d9c8f02b7b0927443', 'd6121bc4248e45019942e2cb78362500', 'dataConnectionDeleteDataSource', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('62446e9889f34dbc9b5092868b8356b7', 'd6121bc4248e45019942e2cb78362500', 'dataConnectionSynchronousDataSource', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('40e5d1f556a04cce94f72058930fc7fb', 'd6121bc4248e45019942e2cb78362500', 'dataConnectionDeleteTable', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('60fa1612d2184179b4326beb2b19a393', 'd6121bc4248e45019942e2cb78362500', 'dataConnectionAddDataSource', NULL, NULL, NULL, NULL, NULL, NULL, NULL);


-- 数据源管理员
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('dca706f8528a42569a0b2b6b31a9fe1d', '43f965aac06c423f871bc2ec49ea65e1', 'dataConnectionTestConnection', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('deb81b83fc7c404eb8dc76f1b12bea90', '43f965aac06c423f871bc2ec49ea65e1', 'dataConnectionAddDataTable', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('3694c160da2147bebac49b220ea18edf', '43f965aac06c423f871bc2ec49ea65e1', 'dataConnectionEditDataSource', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('ee9e5cf2b6554ed9b4473d22e466d8d6', '43f965aac06c423f871bc2ec49ea65e1', 'dataConnectionDeleteDataSource', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('2583eae98b784f389c438cdacd6930fe', '43f965aac06c423f871bc2ec49ea65e1', 'dataConnectionSynchronousDataSource', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('d1f2127dd9fa46948aaf6c310245c72f', '43f965aac06c423f871bc2ec49ea65e1', 'dataConnectionDeleteTable', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('22ba66cb334344ed82b60c2dfdd01484', '43f965aac06c423f871bc2ec49ea65e1', 'dataConnectionAddDataSource', NULL, NULL, NULL, NULL, NULL, NULL, NULL);

-- 数据分析师
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('c44fc6619e1948148cba7bd01f58310d', '5b2ef29d86f244e69af0977acee7555f', 'processModelingRapidAnalysis', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func" ("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('e783315b30205a5esosnmeheqdnc7db', '5b2ef29d86f244e69af0977acee7555f', 'dataSetOperationPreviewData', NULL, NULL, NULL, NULL, NULL, NULL, NULL);


INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('a150b21f1cd0427791a01a20a50504dd', '5b2ef29d86f244e69af0977acee7555f', 'dataSetOperationSetBatchSharing', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('41a29d18828b4249a87975fa6af6a49b', '5b2ef29d86f244e69af0977acee7555f', 'dataSetOperationSetSingleShare', NULL, NULL, NULL, NULL, NULL, NULL, NULL);


-- 业务人员

INSERT INTO "public"."t_sys_auth_obj_func" ("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('4a9b112fadndndancbowemeom5d478e', '9a5502cef25f4b31ba150bbda8d6c34a', 'dataSetOperationPreviewData', NULL, NULL, NULL, NULL, NULL, NULL, NULL);

INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('4a9b112fc4aa4207b9e81db1115d478e', '9a5502cef25f4b31ba150bbda8d6c34a', 'dataSetOperationSetBatchSharing', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('94c9244fcad24b7a90191389f9d1ba27', '9a5502cef25f4b31ba150bbda8d6c34a', 'dataSetOperationSetSingleShare', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func" ("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('402897461523210e01840ee62054038', '9a5502cef25f4b31ba150bbda8d6c34a', 'dashboardGetListByGroupId', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func" ("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('4029924178265e01840e54123055048', '9a5502cef25f4b31ba150bbda8d6c34a', 'dashboardGetDashboard', NULL, NULL, NULL, NULL, NULL, NULL, NULL);