package com.dragonsoft.cicada.datacenter.modules.metadata.service;

import com.code.common.utils.StringUtils;
import com.code.metaservice.datawarehouse.model.DataSourceVO;
import com.code.metaservice.res.request.datasource.DataSourceRequest;
import com.code.metaservice.res.response.datasource.InsertDataSourceResponse;
import com.fw.service.BaseService;

/**
 * <AUTHOR> Avizii
 * @Create : 2021.03.29
 */
public abstract class DataBaseConnectService extends BaseService {

    public DataSourceVO getDataSourceVo(String schemaId, String dbType, String name) {
        DataSourceVO dataSourceInfo = getDataSourceInfo(schemaId);
        dataSourceInfo.setDbType(dbType);
        dataSourceInfo.setDbName(name);
        return dataSourceInfo;
    }

    public abstract boolean testConnection(DataSourceVO dataSourceVO);

    public abstract InsertDataSourceResponse insertDataSource(DataSourceVO dataSourceVO);

    public abstract void updateResource(DataSourceVO dataSourceVO);

    protected abstract DataSourceVO getDataSourceInfo(String schemaId);

    public final DataSourceRequest toDataSourceRequest(DataSourceVO dataSourceVO) {
        DataSourceRequest dataSourceRequest = new DataSourceRequest();
        dataSourceRequest.setConnType(dataSourceVO.getConnType());
        dataSourceRequest.setDbType(dataSourceVO.getDbType());
        dataSourceRequest.setPort(dataSourceVO.getPort());
        dataSourceRequest.setName(StringUtils.isNotBlank(dataSourceVO.getDbName())?dataSourceVO.getDbName():dataSourceVO.getDbCode());
        dataSourceRequest.setCode(StringUtils.isNotBlank(dataSourceVO.getDbCode())?dataSourceVO.getDbCode():dataSourceVO.getDbName());
        if(dataSourceVO.getDbType().equalsIgnoreCase("vertica")) {
            dataSourceRequest.setCode("");
        }
        dataSourceRequest.setIp(dataSourceVO.getIp());
        dataSourceRequest.setUsername(dataSourceVO.getUsername());
        dataSourceRequest.setPassword(dataSourceVO.getPassword());
        dataSourceRequest.setSchemeName(dataSourceVO.getDbName());
        dataSourceRequest.setSchemeCode(dataSourceVO.getDbCode());
        dataSourceRequest.setId(dataSourceVO.getId());
        dataSourceRequest.setSoftwareId(dataSourceVO.getSoftwareId());
        dataSourceRequest.setZookeeperHome(dataSourceVO.getZookeeperHome());
        dataSourceRequest.setZookeeperId(dataSourceVO.getZookeeperId());
        dataSourceRequest.setContextPath(dataSourceVO.getContextPath());
        dataSourceRequest.setJndi(dataSourceVO.getJndi());
        dataSourceRequest.setAccessId(dataSourceVO.getAccessId());
        dataSourceRequest.setAccesskey(dataSourceVO.getAccessKey());
        dataSourceRequest.setEndpoint(dataSourceVO.getEndpoint());
        dataSourceRequest.setTunnelEndpoint(dataSourceVO.getTunnelEndpoint());
        dataSourceRequest.setHbaseId(dataSourceVO.getHbaseId());
        dataSourceRequest.setSolrId(dataSourceVO.getSolrId());

        //if (DataSourceType.ORACLE.name().equals(dataSourceVO.getDbType())) {
            dataSourceRequest.setJdbcUrl(dataSourceVO.getJdbcUrl());
        //}
        return dataSourceRequest;
    }

}
