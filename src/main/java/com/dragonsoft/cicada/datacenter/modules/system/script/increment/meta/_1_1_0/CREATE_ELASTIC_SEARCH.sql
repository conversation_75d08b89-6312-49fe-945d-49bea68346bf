-- Drop table

-- DROP TABLE public.t_md_elasticsearch_alias_relation
-- DROP TABLE IF EXISTS public.t_md_elasticsearch_alias_relation;
CREATE TABLE IF NOT EXISTS public.t_md_elasticsearch_alias_relation (
	alias_id varchar(32) NOT NULL,
	index_id varchar(32) NOT NULL
)
WITH (
	OIDS=FALSE
) ;


-- Drop table

-- DROP TABLE public.t_md_elasticsearch_cluster_node
-- DROP TABLE IF EXISTS public.t_md_elasticsearch_cluster_node;
CREATE TABLE IF NOT EXISTS public.t_md_elasticsearch_cluster_node (
	CONSTRAINT pk_t_md_elasticsearch_cluster_node PRIMARY KEY (id)
)
INHERITS (public.t_md_deployed_comp)
WITH (
	OIDS=FALSE
) ;


-- Drop table

-- DROP TABLE public.t_md_elasticsearch_column
-- DROP TABLE IF EXISTS public.t_md_elasticsearch_column;
CREATE TABLE IF NOT EXISTS public.t_md_elasticsearch_column (
	json_data text NULL,
	type_id varchar(32) NULL,
	parent_column_id varchar(32) NULL,
	has_val bpchar(1) NULL DEFAULT '1'::bpchar,
	CONSTRAINT pk_t_md_elasticsearch_column PRIMARY KEY (id)
)
INHERITS (public.t_md_structural_feature)
WITH (
	OIDS=FALSE
) ;


-- Drop table

-- DROP TABLE public.t_md_elasticsearch_column__relation
-- DROP TABLE IF EXISTS public.t_md_elasticsearch_column__relation;
CREATE TABLE IF NOT EXISTS public.t_md_elasticsearch_column__relation (
	column_id varchar(32) NOT NULL,
	child_column_id varchar(32) NOT NULL
)
WITH (
	OIDS=FALSE
) ;

-- Drop table

-- DROP TABLE public.t_md_elasticsearch_index
-- DROP TABLE IF EXISTS public.t_md_elasticsearch_index;
CREATE TABLE IF NOT EXISTS public.t_md_elasticsearch_index (
	json_data text NULL,
	CONSTRAINT pk_t_md_elasticsearch_index PRIMARY KEY (id)
)
INHERITS (public.t_md_classifier_stat)
WITH (
	OIDS=FALSE
) ;


-- Drop table

-- DROP TABLE public.t_md_elasticsearch_index_type__relation
CREATE TABLE IF NOT EXISTS public.t_md_elasticsearch_index_type__relation (
	index_id varchar(32) NOT NULL,
	type_id varchar(32) NOT NULL,
	type_code text NOT NULL
)
WITH (
	OIDS=FALSE
) ;


-- Drop table

-- DROP TABLE public.t_md_elasticsearch_instance
CREATE TABLE IF NOT EXISTS public.t_md_elasticsearch_instance (
	password varchar(3000) NULL,
	username varchar(3000) NULL,
	is_authentication bpchar(1) NULL,
	CONSTRAINT pk_t_md_elasticsearch_instance PRIMARY KEY (id)
)
INHERITS (public.t_md_deployed_software)
WITH (
	OIDS=FALSE
) ;



-- Drop table

-- DROP TABLE public.t_md_elasticsearch_type
CREATE TABLE IF NOT EXISTS public.t_md_elasticsearch_type (
	json_data text NULL,
	CONSTRAINT pk_t_md_elasticsearch_type PRIMARY KEY (id)
)
INHERITS (public.t_md_element)
WITH (
	OIDS=FALSE
) ;


