package com.dragonsoft.cicada.datacenter.modules.system.schedule.job;

import lombok.extern.slf4j.Slf4j;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.*;

/**
 * drop external tables generated by greenplum-spark-connector
 *
 * <AUTHOR>
 * @date 2020-11-30 17:19
 */
@Slf4j
public class DropExternalTableJob implements Job {

    private final Logger logger = LoggerFactory.getLogger(DropExternalTableJob.class);

    public static final String PARAM_URL = "url";
    public static final String PARAM_USER = "user";
    public static final String PARAM_PASSWORD = "password";
    public static final String PARAM_SCHEMA = "schema";

    private String url;
    private String user;
    private String password;
    private String schema;


    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        Connection connection = null;
        try {
            Class.forName("org.postgresql.Driver");
            connection = DriverManager.getConnection(url, user, password);
            String sql = "{CALL prc_drop_spark_external_table(?,?,?)}";
            CallableStatement statement = connection.prepareCall(sql);
            statement.setString(1, schema);
            statement.setString(2, user);
            statement.registerOutParameter(3, Types.INTEGER);
            statement.execute();
            int count = statement.getInt(3);
            if (logger.isInfoEnabled()) {
                logger.info("Dropped spark external tables success, total: [{}]", count);
            }
        } catch (ClassNotFoundException | SQLException e) {
            log.error(e.getMessage(),e);
            throw new RuntimeException(e);
        } finally {
            try {
                if (connection != null) {
                    connection.close();
                }
            } catch (SQLException e) {
                log.error(e.getMessage(),e);
            }
        }

    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getUser() {
        return user;
    }

    public void setUser(String user) {
        this.user = user;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getSchema() {
        return schema;
    }

    public void setSchema(String schema) {
        this.schema = schema;
    }
}
