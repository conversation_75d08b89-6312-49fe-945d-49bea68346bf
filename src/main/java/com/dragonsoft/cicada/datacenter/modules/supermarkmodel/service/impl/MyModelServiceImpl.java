package com.dragonsoft.cicada.datacenter.modules.supermarkmodel.service.impl;

import com.code.common.paging.PageInfo;
import com.code.common.utils.assertion.Assert;
import com.code.metadata.aimodel.ScriptInfo;
import com.code.metadata.datavisual.Dashboard;
import com.code.metadata.etl.trans.TransMeta;
import com.code.metadata.modelsupermark.*;
import com.code.metadata.modelsupermark.enumtype.ModelEnabledStateEnum;
import com.code.metadata.modelsupermark.enumtype.ModelOperateEnum;
import com.code.metadata.modelsupermark.enumtype.ModelStateEnum;
import com.code.metadata.modelsupermark.enumtype.ModelTypeEnum;
import com.code.metadata.portal.Portal;
import com.code.metadata.standard.StandCodeVal;
import com.code.metaservice.modelsupermark.*;
import com.code.metaservice.standmb.IStandCodeValService;
import com.dragoninfo.dfw.bean.Result;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.common.UrlConfig;
import com.dragonsoft.cicada.datacenter.modules.supermarkmodel.MarkModelEnum;
import com.dragonsoft.cicada.datacenter.modules.supermarkmodel.service.IMyModelService;
import com.dragonsoft.cicada.datacenter.modules.supermarkmodel.vo.ModelLabelVo;
import com.dragonsoft.cicada.datacenter.modules.supermarkmodel.vo.ModelPictureVo;
import com.dragonsoft.cicada.datacenter.modules.supermarkmodel.vo.MyFocusQueryVo;
import com.dragonsoft.cicada.datacenter.modules.supermarkmodel.vo.SupermarkModelVo;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.service.IUserService;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.vo.UserVo;
import com.fw.service.BaseService;
import com.fw.service.annotation.Service;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.code.common.utils.StringUtils.uuid;

@Service
public class MyModelServiceImpl extends BaseService implements IMyModelService {

    @Autowired
    private IUserService userService;

    @Autowired
    private IStandCodeValService standCodeValServiceImpl;

    @Autowired
    private IModelSearchService modelSearchServiceImpl;

    @Autowired
    private IModelPublishService modelPublishServiceImpl;

    @Autowired
    private IModelBrowseService modelBrowseServiceImpl;

    @Autowired
    private IModelEvaluationService modelEvaluationServiceImpl;

    @Autowired
    private IModelFocusService modelFocusServiceImpl;



    @Autowired
    private UrlConfig urlConfig;
    /**
     * 我的模型查询待发布列表
     */
    @Override
    public PageInfo queryUnPublishModelPage(Map<String, Object> queryModelMap) {
        String type = (String) queryModelMap.get("type");
        Assert.notNull(type, "类型不能为空");

        PageInfo pageResult = null;
        if (StringUtils.equals(type, ModelTypeEnum.TRANS.getValue())) {
            pageResult = queryUnPublishTransModelPage(queryModelMap);
        } else if (StringUtils.equals(type, ModelTypeEnum.SCRIPT.getValue())) {
            pageResult = queryUnPublishScriptModelPage(queryModelMap);
        } else if (StringUtils.equals(type, ModelTypeEnum.DASHBOARDS.getValue())) {
            pageResult = queryUnPublishDashboardsModelPage(queryModelMap);
        } else if (StringUtils.equals(type, ModelTypeEnum.PORTAL.getValue())) {
            pageResult = queryUnPublishPortalModelPage(queryModelMap);
        }

        return pageResult;
    }

    /**
     * 我的模型查询数据模型待发布列表
     */
    public PageInfo queryUnPublishTransModelPage(Map<String, Object> queryModelMap) {

        String userId = (String) queryModelMap.get("userId");

        Assert.notNull(queryModelMap.get("userId"), "用户未登录");
        Assert.notNull(queryModelMap.get("pageNum"), "分页不能为空");
        Assert.notNull(queryModelMap.get("pageSize"), "分页不能为空");

        PageInfo pageResult = modelSearchServiceImpl.queryUnPublishTransModelPage(queryModelMap);

        UserVo userVo = userService.queryUserById(userId);
        List<Map> dataList = pageResult.getDataList();
        if (dataList != null) {
            for (Map transVo : dataList) {
                transVo.put("userName", userVo.getObjName());
                transVo.put("path", getTransPath((String) transVo.get("busiclassifyid")));
                //setTransJobStateAndRuntime(transVo);
            }
        }

        return pageResult;
    }

    /**
     * 获取数据模型所属目录
     */
    public String getTransPath(String busiClassifyId) {
        if (StringUtils.isEmpty(busiClassifyId)) {
            return "";
        }
        List<Map> pathList = modelSearchServiceImpl.queryTransPathList(busiClassifyId);
        if (CollectionUtils.isNotEmpty(pathList)) {
            String path = pathList.stream().map(pathMap -> (String) pathMap.get("name")).collect(Collectors.joining("/"));
            return path;
        }
        return "";
    }

    /**
     * 数据模型运行状态和时间
     */
    public void setTransJobStateAndRuntime(Map transVo) {
        Map transJobMap = modelSearchServiceImpl.queryTransJobStateAndRuntime(transVo);
        if (transJobMap != null) {
            transVo.put("executeStatus", transJobMap.get("executestatus"));
            transVo.put("startTime", transJobMap.get("starttime"));
        }
    }

    /**
     * 我的模型查询仪表盘待发布列表
     */
    public PageInfo queryUnPublishDashboardsModelPage(Map<String, Object> queryModelMap) {

        Assert.notNull(queryModelMap.get("userId"), "用户未登录");
        Assert.notNull(queryModelMap.get("pageNum"), "分页不能为空");
        Assert.notNull(queryModelMap.get("pageSize"), "分页不能为空");

        String userId = (String) queryModelMap.get("userId");

        PageInfo pageResult = modelSearchServiceImpl.queryUnPublishDashboardsModelPage(queryModelMap);

        UserVo userVo = userService.queryUserById(userId);
        List<Map> dataList = pageResult.getDataList();
        if (dataList != null) {
            for (Map dashboardsVo : dataList) {
                dashboardsVo.put("userName", userVo.getObjName());
                dashboardsVo.put("path", getDashboardsPath((String) dashboardsVo.get("groupid")));
            }
        }

        return pageResult;
    }

    /**
     * 仪表盘所属目录
     */
    public String getDashboardsPath(String groupId) {
        if (StringUtils.isEmpty(groupId)) {
            return "";
        }
        List<Map> pathList = modelSearchServiceImpl.queryDashboardsPath(groupId);
        if (CollectionUtils.isNotEmpty(pathList)) {
            String path = pathList.stream().map(pathMap -> {
                String name = (String) pathMap.get("name");
                String parentId = (String) pathMap.get("parentid");
                if (StringUtils.equals(parentId, "1")) {
                    name = "我的空间/" + name;
                } else if (StringUtils.equals(parentId, "2")) {
                    name = "标准模型/" + name;
                }
                return name;
            }).collect(Collectors.joining("/"));
            return path;
        }
        if (StringUtils.equals(groupId, "1")) {
            return  "我的空间";
        } else if (StringUtils.equals(groupId, "2")) {
            return  "标准模型";
        }
        return "";
    }

    /**
     * 我的模型查询AI模型待发布列表
     */
    public PageInfo queryUnPublishScriptModelPage(Map<String, Object> queryModelMap) {
        Assert.notNull(queryModelMap.get("userId"), "用户未登录");
        Assert.notNull(queryModelMap.get("pageNum"), "分页不能为空");
        Assert.notNull(queryModelMap.get("pageSize"), "分页不能为空");

        String userId = (String) queryModelMap.get("userId");

        PageInfo pageResult = modelSearchServiceImpl.queryUnPublishScriptModelPage(queryModelMap);

        UserVo userVo = userService.queryUserById(userId);
        List<Map> dataList = pageResult.getDataList();
        if (dataList != null) {
            for (Map transVo : dataList) {
                transVo.put("userName", userVo.getObjName());
                transVo.put("path", getTransPath((String) transVo.get("busiclassifyid")));
            }
        }

        return pageResult;
    }

    /**
     * 我的模型查询模型主题待发布列表
     */
    public PageInfo queryUnPublishPortalModelPage(Map<String, Object> queryModelMap) {
        Assert.notNull(queryModelMap.get("userId"), "用户未登录");
        Assert.notNull(queryModelMap.get("pageNum"), "分页不能为空");
        Assert.notNull(queryModelMap.get("pageSize"), "分页不能为空");

        String userId = (String) queryModelMap.get("userId");

        PageInfo pageResult = modelSearchServiceImpl.queryUnPublishPortalModelPage(queryModelMap);

        UserVo userVo = userService.queryUserById(userId);
        List<Map> dataList = pageResult.getDataList();
        if (dataList != null) {
            for (Map transVo : dataList) {
                transVo.put("userName", userVo.getObjName());
                transVo.put("path", getTransPath((String) transVo.get("busiclassifyid")));
            }
        }

        return pageResult;
    }

    /**
     * 我的模型查询已发布列表
     */
    @Override
    public PageInfo queryPublishedModelPage(Map<String, Object> queryModelMap) {
        String type = (String) queryModelMap.get("type");
        String userId = (String) queryModelMap.get("userId");

        Assert.notNull(userId, "用户未登录");
        Assert.notNull(queryModelMap.get("pageNum"), "分页不能为空");
        Assert.notNull(queryModelMap.get("pageSize"), "分页不能为空");
        Assert.notNull(type, "类型不能为空");
        Assert.state(ModelTypeEnum.isModelType(type), "类型错误");

        PageInfo pageResult = modelSearchServiceImpl.queryPublishedModelPage(queryModelMap);

        UserVo userVo = userService.queryUserById(userId);
        List<Map> dataList = pageResult.getDataList();
        if (dataList != null) {
            for (Map modelVo : dataList) {
                modelVo.put("userName", userVo.getObjName());
                modelVo.put("label", getModelLabel((String) modelVo.get("id")));
            }
        }

        return pageResult;
    }

    /**
     * 获取模型的标签
     */
    public String getModelLabel(String modelId) {
        if (StringUtils.isEmpty(modelId)) {
            return "";
        }
        List<ModelLabelRelation> labelList = modelSearchServiceImpl.getModelLabel(modelId);
        if (CollectionUtils.isNotEmpty(labelList)) {
            String label = labelList.stream().filter(labelRel -> labelRel.getModelLabel() != null)
                    .map(labelRel -> labelRel.getModelLabel().getLabelName()).collect(Collectors.joining("、"));
            return label;
        }
        return "";
    }

    /**
     * 模型发布初始化数据
     */
    @Override
    public Result publishModelInit() {
        Map<String, Object> resultMap = new HashMap<>();
        List<String> list = urlConfig.getMxscfxList();
        for(int i=0;i<list.size();i++){
            String fxcode = list.get(i);
            addTypeCode(MarkModelEnum.valueOf(fxcode).getCode(),MarkModelEnum.valueOf(fxcode).getLiscode(),resultMap);
        }
        return Result.success(resultMap);
    }

    private void addTypeCode(String type,String resKey,Map<String, Object> resultMap){
        List<StandCodeVal> codeList = standCodeValServiceImpl.getValList(type);
        if (CollectionUtils.isNotEmpty(codeList)) {
            List<Map<String, String>> codeMapList = codeList.stream().map(standCodeVal -> {
                Map<String, String> map = new HashMap<>();
                map.put("code", standCodeVal.getCode());
                map.put("name", standCodeVal.getName());
                return map;
            }).collect(Collectors.toList());
            resultMap.put(resKey, codeMapList);
        }
    }

    /**
     * 发布模型
     */
    @Override
    public Result insertPublishModel(SupermarkModelVo modelVo) {
        checkModelVo(modelVo);

        SupermarkModel model = modelSearchServiceImpl.querySupermarkModelByTransId(modelVo.getTransId());
        Assert.isNull(model, "相同方案模型已存在");
        //模型主体
        SupermarkModel markModel = new SupermarkModel();
        setModelVoToSupermarkModel(modelVo, markModel);
        markModel.setState(ModelStateEnum.DOWN.getValue());
        markModel.setEnabledState(ModelEnabledStateEnum.ENABLE.getValue());
        String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        markModel.setPublishTime(dateStr);
        markModel.setPutTime(dateStr);
        markModel.setUpdateTime(dateStr);
        markModel.setId(uuid());
        baseDao.save(markModel);
        //模型分类
        ModelAttachedInformation attachedInfo = new ModelAttachedInformation();
        setModelVoToAttachedInfo(modelVo, attachedInfo);
        attachedInfo.setId(uuid());
        attachedInfo.setSupermarkModel(markModel);
        baseDao.save(attachedInfo);
        //模型配图
        List<ModelPictureVo> modelPictureVos = modelVo.getPics();
        if (CollectionUtils.isNotEmpty(modelPictureVos)) {
            List<ModelPicture> modelPictures = modelPictureVos.stream().map(pictureVo -> {
                String pictureName = pictureVo.getPictureName();
                if (pictureName.length() > 32){
                    Assert.fail("图片名称不超过32个字符！");
                }
                ModelPicture modelPicture = new ModelPicture();
                modelPicture.setId(uuid());
                modelPicture.setModelId(markModel.getId());
                modelPicture.setPictureName(pictureVo.getPictureName());
                modelPicture.setPicture(pictureVo.getPicture());
                modelPicture.setCreateTime(dateStr);
                modelPicture.setUpdateTime(dateStr);
                return modelPicture;
            }).collect(Collectors.toList());
            baseDao.save(modelPictures);
        }
        //模型标签
        List<ModelLabelVo> labelVos = modelVo.getLabels();
        if (CollectionUtils.isNotEmpty(labelVos)) {
            Assert.isTrue(labelVos.size() <= 5, "自定义标签最多5个");
            List<ModelLabelRelation> labelRelations = new ArrayList<>();
            for (ModelLabelVo labelVo : labelVos) {
                Assert.notNull(labelVo.getLabelId(), "标签ID不能为空");
                ModelLabel modelLabel = (ModelLabel) baseDao.get(ModelLabel.class, labelVo.getLabelId());
                Assert.notNull(modelLabel, "标签不存在");
                if (modelLabel.getLabelCount() == null) {
                    modelLabel.setLabelCount(0);
                }
                modelLabel.setLabelCount(modelLabel.getLabelCount() + 1); //标签使用次数+1
                baseDao.update(modelLabel);

                ModelLabelRelation labelRelation = new ModelLabelRelation();
                labelRelation.setId(uuid());
                labelRelation.setUserId(modelVo.getUserId());
                labelRelation.setSupermarkModel(markModel);
                labelRelation.setModelLabel(modelLabel);
                labelRelations.add(labelRelation);
            }
            baseDao.save(labelRelations);
        }

        //记录发布行为
        UserOperateBehavior operateBehavior = new UserOperateBehavior();
        operateBehavior.setId(uuid());
        operateBehavior.setModelId(markModel.getId());
        operateBehavior.setUserId(modelVo.getUserId());
        operateBehavior.setOperateType(ModelOperateEnum.PUBLISH.getValue());
        operateBehavior.setOperateTime(dateStr);
        baseDao.save(operateBehavior);

        ModelPublish modelPublish = new ModelPublish();
        modelPublish.setId(uuid());
        modelPublish.setUserOperateBehavior(operateBehavior);
        modelPublish.setModelId(markModel.getId());
        modelPublish.setUserId(modelVo.getUserId());
        modelPublish.setOperateType(ModelOperateEnum.PUBLISH.getValue());
        modelPublish.setOperateContent("用户发布模型");
        modelPublish.setOperateTime(dateStr);
        baseDao.save(modelPublish);

        Map<String, Object> resultMap = Maps.newHashMap();
        resultMap.put("id", markModel.getId());
        return Result.success(resultMap);
    }

    /**
     * 发布模型数据校验
     */
    public void checkModelVo(SupermarkModelVo modelVo) {
        Assert.notNull(modelVo, "模型数据不能为空");
        Assert.notNull(modelVo.getTransId(), "方案id不能为空");
        Assert.notNull(modelVo.getType(), "方案类型不能为空");
        Assert.notNull(modelVo.getVersion(), "模型版本不能为空");
        Assert.isTrue(modelVo.getVersion().length() <= 32, "模型版本号长度不能超过32个字符");
        Assert.notNull(modelVo.getIntroduction(), "模型简介不能为空");
        Assert.isTrue(modelVo.getIntroduction().length() <= 200, "模型简介字数不能超过200字");
        Set mxsjset = urlConfig.getMxscfxList().stream().collect(Collectors.toSet());

        if(mxsjset.contains(MarkModelEnum.DXLX.getCode())){
            Assert.notNull(modelVo.getObjectTypeCode(), "模型对象类型代码不能为空");
        }
        if(mxsjset.contains(MarkModelEnum.YYLX.getCode())){
            Assert.notNull(modelVo.getApplicationTypeCode(), "模型应用类型代码不能为空");
        }
        if(mxsjset.contains(MarkModelEnum.JZFL.getCode())){
            Assert.notNull(modelVo.getPoliceTypeCode(), "模型警种类型代码不能为空");
        }
        if(mxsjset.contains(MarkModelEnum.AJLX.getCode())){
            Assert.notNull(modelVo.getCaseTypeCode(), "模型案件类型代码不能为空");
        }
        if(mxsjset.contains(MarkModelEnum.QYLX.getCode())){
            Assert.notNull(modelVo.getAreaTypeCode(), "模型区域类型代码不能为空");
        }
        if(mxsjset.contains(MarkModelEnum.DFGKSL.getCode())){
            Assert.notNull(modelVo.getControlTypeCode(), "模型打防管控类型代码不能为空");
        }
        if(mxsjset.contains(MarkModelEnum.YYLX_SJ.getCode())){
            Assert.notNull(modelVo.getYylxSjTypeCode(), "应用类型代码不能为空");
        }
        if(mxsjset.contains(MarkModelEnum.HYFL_SJ.getCode())){
            Assert.notNull(modelVo.getHyflSjTypeCode(), "应用类型代码不能为空");
        }




    }

    /**
     * 发布模型 模型数据设值
     */
    public void setModelVoToSupermarkModel(SupermarkModelVo modelVo, SupermarkModel markModel) {
        markModel.setTransId(modelVo.getTransId());
        markModel.setType(modelVo.getType());
        markModel.setLogo(modelVo.getLogo());
        markModel.setVersion(modelVo.getVersion());
        markModel.setIntroduction(modelVo.getIntroduction());
    }

    /**
     * 发布模型 模型分类设值
     */
    public void setModelVoToAttachedInfo(SupermarkModelVo modelVo, ModelAttachedInformation attachedInfo) {


        attachedInfo.setObjectTypeCode(modelVo.getObjectTypeCode());
        attachedInfo.setObjectTypeName(getTypeName(MarkModelEnum.DXLX.getCode(), modelVo.getObjectTypeCode(), "对象类型"));

        attachedInfo.setApplicationTypeCode(modelVo.getApplicationTypeCode());
        attachedInfo.setApplicationTypeName(getTypeName(MarkModelEnum.YYLX.getCode(), modelVo.getApplicationTypeCode(),"应用类型"));

        attachedInfo.setPoliceTypeCode(modelVo.getPoliceTypeCode());
        attachedInfo.setPoliceTypeName(getTypeName(MarkModelEnum.JZFL.getCode(), modelVo.getPoliceTypeCode(),"警种分类"));

        attachedInfo.setCaseTypeCode(modelVo.getCaseTypeCode());
        attachedInfo.setCaseTypeName(getTypeName(MarkModelEnum.AJLX.getCode(), modelVo.getCaseTypeCode(),"案件类型"));

        attachedInfo.setAreaTypeCode(modelVo.getAreaTypeCode());
        attachedInfo.setAreaTypeName(getTypeName(MarkModelEnum.QYLX.getCode(), modelVo.getAreaTypeCode(),"区域类型"));

        attachedInfo.setControlTypeCode(modelVo.getControlTypeCode());
        attachedInfo.setControlTypeName(getTypeName(MarkModelEnum.DFGKSL.getCode(), modelVo.getControlTypeCode(),"打防管控类"));

        attachedInfo.setMxlxSjTypeCode(modelVo.getHyflSjTypeCode());
        attachedInfo.setMxlxSjTypeName(getTypeName(MarkModelEnum.HYFL_SJ.getCode(), modelVo.getHyflSjTypeCode(),"行业分类"));

        attachedInfo.setYylxSjTypeCode(modelVo.getYylxSjTypeCode());
        attachedInfo.setYylxSjTypeName(getTypeName(MarkModelEnum.YYLX_SJ.getCode(), modelVo.getYylxSjTypeCode(),"应用类型"));

    }

    private String getTypeName(String type,String typeCode,String errorName){
        StandCodeVal standCodeVal = standCodeValServiceImpl.getStandCodeValByCode(type, typeCode);
       // Assert.notNull(standCodeVal, errorName+"码值不存在");
        if(standCodeVal==null){
            return "";
        }else {
            return standCodeVal.getName();
        }

    }

    /**
     * 新增标签
     */
    @Override
    public Result insertModelLabel(ModelLabelVo labelVo) {
        Assert.notNull(labelVo, "标签数据不能为空");
        Assert.notNull(labelVo.getLabelName(), "标签名称不能为空");
        Assert.isTrue(labelVo.getLabelName().length() <= 32, "标签名称不能超过32个字");

        ModelLabel modelLabel = modelSearchServiceImpl.queryModelLabelObject(labelVo.getLabelName());
        //Assert.isNull(modelLabel, "标签已存在");
        if (modelLabel == null) {
            modelLabel = new ModelLabel();
            modelLabel.setId(uuid());
            modelLabel.setLabelName(labelVo.getLabelName());
            modelLabel.setLabelCount(0);
            String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            modelLabel.setCreateTime(dateStr);
            modelLabel.setUpdateTime(dateStr);
            baseDao.save(modelLabel);
        }

        Map<String, Object> resultMap = Maps.newHashMap();
        resultMap.put("id", modelLabel.getId());
        resultMap.put("labelName", modelLabel.getLabelName());

        return Result.success(resultMap);
    }

    /**
     * 查询标签库列表
     */
    @Override
    public List<Map> queryModelLabelList(ModelLabelVo labelVo, String userId) {

        String labelName = labelVo == null ? null : labelVo.getLabelName();
        List<Map> labelList = modelSearchServiceImpl.queryModelLabelList(labelName, userId);

        return labelList;
    }

    /**
     * 模型上下架
     */
    @Override
    public Result updateModelState(SupermarkModelVo modelVo) {
        Assert.notNull(modelVo, "模型数据不能为空");
        Assert.notNull(modelVo.getId(), "模型id不能为空");
        Assert.notNull(modelVo.getState(), "模型状态不能为空");
        Assert.state(ModelStateEnum.isModelState(modelVo.getState()), "模型状态值错误");

        SupermarkModel markModel = (SupermarkModel) baseDao.get(SupermarkModel.class, modelVo.getId());
        Assert.notNull(markModel, "模型不存在");
        markModel.setState(modelVo.getState());
        String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        markModel.setPutTime(dateStr);
        markModel.setUpdateTime(dateStr);
        baseDao.update(markModel);

        return Result.success();
    }


    /**
     * 模型启停用
     */
    @Override
    public Result updateEnabledState(SupermarkModelVo modelVo) {
        Assert.notNull(modelVo, "模型数据不能为空");
        Assert.notNull(modelVo.getId(), "模型id不能为空");
        Assert.notNull(modelVo.getEnabledState(), "模型状态不能为空");
        Assert.state(ModelEnabledStateEnum.isModelState(modelVo.getEnabledState()), "模型状态值错误");

        SupermarkModel markModel = (SupermarkModel) baseDao.get(SupermarkModel.class, modelVo.getId());
        Assert.notNull(markModel, "模型不存在");
        markModel.setEnabledState(modelVo.getEnabledState());
        String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        markModel.setPutTime(dateStr);
        markModel.setUpdateTime(dateStr);
        baseDao.update(markModel);
        return Result.success();
    }

    /**
     * 删除模型
     */
    @Override
    public Result deleteMarkModel(SupermarkModelVo modelVo) {
        Assert.notNull(modelVo, "模型数据不能为空");
        Assert.notNull(modelVo.getId(), "模型id不能为空");

        SupermarkModel markModel = (SupermarkModel) baseDao.get(SupermarkModel.class, modelVo.getId());
        Assert.notNull(markModel, "模型不存在");
        Assert.state(StringUtils.equals(markModel.getState(), ModelStateEnum.DOWN.getValue()), "模型需先下架才能删除");

        deleteMarkModelInfo(markModel);

        return Result.success();
    }

    /**
     * 根据方案ID删除模型
     */
    @Override
    public void deleteMarkModelByTransId(String transId) {
        //Assert.notNull(transId, "模型方案ID不能为空");
        if (StringUtils.isEmpty(transId)) {
            return;
        }
        SupermarkModel markModel = modelSearchServiceImpl.querySupermarkModelByTransId(transId);
        if (markModel != null) {
            deleteMarkModelInfo(markModel);
        }
    }

    @Override
    public List<Map> obtainid(String id) {
        List<Map> sourceid = null;
        if (id != null){
            sourceid = modelSearchServiceImpl.sourceid(id);
        }


        return sourceid;
    }

    /**
     * 根据方案ID列表批量删除模型
     */
    @Override
    public void deleteMarkModelBatchByTransIdList(List<String> transIdList) {
        if (CollectionUtils.isEmpty(transIdList)) {
            return;
        }
        for (String transId : transIdList) {
            deleteMarkModelByTransId(transId);
        }
    }

    /**
     * 根据方案目录id批量删除模型
     */
    @Override
    public void deleteMarkModelByBusiClassifyId(String busiClassifyId) {
        if (StringUtils.isEmpty(busiClassifyId)) {
            return;
        }
        List<Map> transIdList = modelSearchServiceImpl.queryTransIdList(busiClassifyId);
        if (CollectionUtils.isNotEmpty(transIdList)) {
            transIdList.forEach(transIdMap -> {
                String transId = (String) transIdMap.get("transid");
                deleteMarkModelByTransId(transId);
            });
        }
    }

    /**
     * 删除模型明细
     */
    public void deleteMarkModelInfo(SupermarkModel markModel) {
        Set<ModelPicture> modelPictures = markModel.getModelPictures();
        ModelAttachedInformation attInfo = markModel.getModelAttachedInformation();
        //删除模型配图、模型分类
        if (CollectionUtils.isNotEmpty(modelPictures)) {
            baseDao.delete(modelPictures);
        }
        if (attInfo != null) {
            baseDao.delete(attInfo);
        }
        //删除模型标签关联关系、评价、操作行为、结果记录
        modelPublishServiceImpl.deleteModelLabelRelation(markModel.getId());
        modelPublishServiceImpl.deleteModelEvaluation(markModel.getId());
        modelPublishServiceImpl.deleteUserOperateBehaviorResult(markModel.getId());
        modelPublishServiceImpl.deleteUserOperateBehavior(markModel.getId());
        //删除模型
        baseDao.delete(markModel);
        String id = baseDao.sqlQueryForValue("select ");
    }

    /**
     * 模型修改初始化数据
     */
    @Override
    public Result initUpdateMarkModel(SupermarkModelVo modelVo) {
        Assert.notNull(modelVo, "模型数据不能为空");
        Assert.notNull(modelVo.getId(), "模型id不能为空");

        SupermarkModel markModel = (SupermarkModel) baseDao.get(SupermarkModel.class, modelVo.getId());
        Assert.notNull(markModel, "模型不存在");

        SupermarkModelVo modelResult = new SupermarkModelVo();
        //模型信息
        setMarkModelToModelResultVo(markModel, modelResult);
        //模型名称
        Map<String, Object> transInfoMap = getModelTransInfo(markModel.getTransId(), markModel.getType());
        modelResult.setTransName((String)transInfoMap.get("name"));
        //模型标签
        List<ModelLabelRelation> labelList = modelSearchServiceImpl.getModelLabel(markModel.getId());
        if (CollectionUtils.isNotEmpty(labelList)) {
            List<ModelLabelVo> labels = labelList.stream().map(labelRel -> {
                ModelLabelVo labelVo = new ModelLabelVo();
                if (labelRel.getModelLabel() != null) {
                    labelVo.setLabelId(labelRel.getModelLabel().getId());
                    labelVo.setLabelName(labelRel.getModelLabel().getLabelName());
                }
                return labelVo;
            }).collect(Collectors.toList());
            modelResult.setLabels(labels);
        }

        return Result.success(modelResult);
    }

    /**
     * 模型修改初始化 模型数据设值
     */
    public void setMarkModelToModelResultVo(SupermarkModel markModel, SupermarkModelVo modelResult) {
        modelResult.setId(markModel.getId());
        modelResult.setTransId(markModel.getTransId());
        modelResult.setType(markModel.getType());
        modelResult.setLogo(markModel.getLogo());
        modelResult.setVersion(markModel.getVersion());
        modelResult.setIntroduction(markModel.getIntroduction());
        modelResult.setState(markModel.getState());
        //模型配图
        Set<ModelPicture> modelPictures = markModel.getModelPictures();
        if (CollectionUtils.isNotEmpty(modelPictures)) {
            List<ModelPictureVo> pictureVos = new ArrayList<>();
            for (ModelPicture modelPicture : modelPictures) {
                ModelPictureVo pictureVo = new ModelPictureVo();
                pictureVo.setPictureName(modelPicture.getPictureName());
                pictureVo.setPicture(modelPicture.getPicture());
                pictureVos.add(pictureVo);
            }
            modelResult.setPics(pictureVos);
        }
        //模型分类
        ModelAttachedInformation attInfo = markModel.getModelAttachedInformation();
        if (attInfo != null) {
            modelResult.setObjectTypeCode(attInfo.getObjectTypeCode());
            modelResult.setObjectTypeName(attInfo.getObjectTypeName());
            modelResult.setApplicationTypeCode(attInfo.getApplicationTypeCode());
            modelResult.setApplicationTypeName(attInfo.getApplicationTypeName());
            modelResult.setPoliceTypeCode(attInfo.getPoliceTypeCode());
            modelResult.setPoliceTypeName(attInfo.getPoliceTypeName());
            modelResult.setCaseTypeCode(attInfo.getCaseTypeCode());
            modelResult.setCaseTypeName(attInfo.getCaseTypeName());
            modelResult.setAreaTypeCode(attInfo.getAreaTypeCode());
            modelResult.setAreaTypeName(attInfo.getAreaTypeName());
            modelResult.setControlTypeCode(attInfo.getControlTypeCode());
            modelResult.setControlTypeName(attInfo.getControlTypeName());
        }
    }

    /**
     * 获取模型名称、创建人
     */
    @Override
    public Map<String, Object> getModelTransInfo(String transId, String type) {
        Map<String, Object> transInfoMap = Maps.newHashMap();
        if (StringUtils.equals(type, ModelTypeEnum.TRANS.getValue())) {
            TransMeta transMeta = (TransMeta) baseDao.get(TransMeta.class, transId);
            //Assert.notNull(transMeta, "数据模型不存在");
            if (transMeta != null) {
                transInfoMap.put("name", transMeta.getName());
                transInfoMap.put("operateUserId", transMeta.getOperateUserId());
            }
        } else if (StringUtils.equals(type, ModelTypeEnum.DASHBOARDS.getValue())) {
            Dashboard dashboard = (Dashboard) baseDao.get(Dashboard.class, transId);
            //Assert.notNull(dashboard, "仪表盘不存在");
            if (dashboard != null) {
                transInfoMap.put("name", dashboard.getName());
                transInfoMap.put("operateUserId", dashboard.getOperateUserId());
            }
        } else if (StringUtils.equals(type, ModelTypeEnum.SCRIPT.getValue())) {
            ScriptInfo scriptInfo = (ScriptInfo) baseDao.get(ScriptInfo.class, transId);
            //Assert.notNull(scriptInfo, "AI模型不存在");
            if (scriptInfo != null) {
                transInfoMap.put("name", scriptInfo.getName());
                transInfoMap.put("operateUserId", scriptInfo.getOperateUserId());
            }
        } else if (StringUtils.equals(type, ModelTypeEnum.PORTAL.getValue())) {
            Portal portal = (Portal) baseDao.get(Portal.class, transId);
            //Assert.notNull(portal, "主题门户不存在");
            if (portal != null) {
                transInfoMap.put("name", portal.getName());
                transInfoMap.put("operateUserId", portal.getOperateUserId());
            }
        }
        return transInfoMap;
    }

    @Override
    public Result updateMarkModel(SupermarkModelVo modelVo) {
        Assert.notNull(modelVo, "模型数据不能为空");
        Assert.notNull(modelVo.getId(), "模型id不能为空");
        checkModelVo(modelVo);

        SupermarkModel markModel = (SupermarkModel) baseDao.get(SupermarkModel.class, modelVo.getId());
        Assert.notNull(markModel, "模型不存在");
        setModelVoToSupermarkModel(modelVo, markModel);
        String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        markModel.setUpdateTime(dateStr);
        baseDao.update(markModel);

        //修改模型分类
        ModelAttachedInformation attInfo = markModel.getModelAttachedInformation();
        setModelVoToAttachedInfo(modelVo, attInfo);
        baseDao.update(attInfo);

        //修改图片
        Set<ModelPicture> pictureSet = markModel.getModelPictures();
        if (CollectionUtils.isNotEmpty(pictureSet)) {
            baseDao.delete(pictureSet);
        }
        List<ModelPictureVo> modelPictureVos = modelVo.getPics();
        if (CollectionUtils.isNotEmpty(modelPictureVos)) {
            List<ModelPicture> modelPictures = new ArrayList<>();
            for (ModelPictureVo pictureVo : modelPictureVos) {
                ModelPicture modelPicture = new ModelPicture();
                modelPicture.setId(uuid());
                modelPicture.setModelId(markModel.getId());
                modelPicture.setPictureName(pictureVo.getPictureName());
                modelPicture.setPicture(pictureVo.getPicture());
                modelPicture.setCreateTime(dateStr);
                modelPicture.setUpdateTime(dateStr);
                modelPictures.add(modelPicture);
            }
            baseDao.save(modelPictures);
        }

        //修改模型标签
        modelPublishServiceImpl.deleteModelLabelRelation(markModel.getId());
        List<ModelLabelVo> labelVos = modelVo.getLabels();
        if (CollectionUtils.isNotEmpty(labelVos)) {
            Assert.isTrue(labelVos.size() <= 5, "自定义标签最多5个");
            List<ModelLabelRelation> labelRelations = new ArrayList<>();
            for (ModelLabelVo labelVo : labelVos) {
                Assert.notNull(labelVo.getLabelId(), "标签ID不能为空");
                ModelLabel modelLabel = (ModelLabel) baseDao.get(ModelLabel.class, labelVo.getLabelId());
                Assert.notNull(modelLabel, "标签不存在");
                if (modelLabel.getLabelCount() == null) {
                    modelLabel.setLabelCount(0);
                }
                modelLabel.setLabelCount(modelLabel.getLabelCount() + 1); //标签使用次数+1
                baseDao.update(modelLabel);

                ModelLabelRelation labelRelation = new ModelLabelRelation();
                labelRelation.setId(uuid());
                labelRelation.setUserId(modelVo.getUserId());
                labelRelation.setSupermarkModel(markModel);
                labelRelation.setModelLabel(modelLabel);
                labelRelations.add(labelRelation);
            }
            baseDao.save(labelRelations);
        }

        Map<String, Object> resultMap = Maps.newHashMap();
        resultMap.put("id", markModel.getId());
        return Result.success(resultMap);
    }

    @Override
    public PageInfo queryMyFocusPage(MyFocusQueryVo queryVo,String userId) {
        PageInfo pageInfo = modelFocusServiceImpl.queryMyFocusModelList(
                queryVo.getModelType(),
                queryVo.getModelState(),
                queryVo.getModelName(),
                queryVo.getPageNum(),
                queryVo.getPageSize(),
                userId);
        List<Map<String,Object>> dataList = pageInfo.getDataList();
        List<Map<String,Object>> results = new ArrayList<>();
        for (Map<String,Object> map : dataList) {
            Map<String,Object> result = new HashMap<>();
            String modelId = (String) map.get("id");
            result.put("id",modelId);
            result.put("transId",map.get("trans_id"));
            result.put("type",map.get("type"));
            result.put("version",map.get("version"));
            result.put("state",map.get("state"));
            result.put("introduction",map.get("introduction"));
            result.put("publishTime",map.get("publish_time"));
            result.put("name",map.get("name"));
            result.put("operateUserId",map.get("operate_user_id"));
            Map modelAttachedInformation = modelSearchServiceImpl.queryModelAttachedInformationByModelId(modelId);
            result.put("modelAttachedInformation",modelAttachedInformation);
            Map modelEvaluation = modelEvaluationServiceImpl.queryPersonalModelEvaluation(modelId, userId);
            result.put("modelEvaluation",modelEvaluation);
            List<ModelLabel> modelLabels = modelSearchServiceImpl.queryModelLabelListByModelId(modelId);
            result.put("modelLabels",modelLabels);
            results.add(result);

        }
        pageInfo.setDataList(results);
        return pageInfo;
    }

    @Override
    public void cancelMyFocus(List<String> modelsId, String userId) {
        modelFocusServiceImpl.deleteFocusByModelIds(modelsId,userId);
        modelEvaluationServiceImpl.deleteEvaluationByModelIds(modelsId,userId);
    }


}
