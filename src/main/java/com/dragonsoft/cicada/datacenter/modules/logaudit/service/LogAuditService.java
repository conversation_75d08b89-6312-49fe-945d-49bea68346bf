package com.dragonsoft.cicada.datacenter.modules.logaudit.service;

import com.code.common.paging.PageInfo;
import com.fw.service.IService;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/10/9
 */


public interface LogAuditService extends IService {

    /**
     * 获取日志审计的列表
     *
     * @param pageInfo
     * @return PageInfo
     */
    PageInfo getLogInfo(PageInfo pageInfo, Map<String, Object> params);


}
