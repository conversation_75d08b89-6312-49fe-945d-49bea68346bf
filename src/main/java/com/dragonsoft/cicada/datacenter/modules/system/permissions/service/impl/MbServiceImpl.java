package com.dragonsoft.cicada.datacenter.modules.system.permissions.service.impl;

import com.code.common.paging.PageInfo;
import com.code.common.utils.StringUtils;
import com.code.dragonsoft.dataquery.service.QueryDataService;
import com.code.dragonsoft.dataquery.service.querymodel.ColumnDataModel;
import com.code.dragonsoft.dataquery.service.querymodel.ParamDataModel;
import com.code.meta.dml.standard.StandardQuery;
import com.code.meta.dml.standard.StandardScript;
import com.code.meta.dml.standard.cdins.Select;
import com.code.meta.dml.standard.cdins.SelectCdin;
import com.code.metadata.model.core.StructuralFeature;
import com.code.metadata.res.semistructured.elasticsearch.ElasticsearchCollection;
import com.code.metadata.standard.StandCodeVal;
import com.code.metadata.standard.StandDwRelation;
import com.code.metadata.standard.StandMb;
import com.code.metaservice.common.PageData;
import com.code.metaservice.core.ModelElementService;
import com.code.metaservice.core.StructuralFeatureService;
import com.code.metaservice.res.IDataSourceService;
import com.code.metaservice.res.response.vo.datasource.DataSourceView;
import com.code.metaservice.standmb.IStandCodeValService;
import com.code.metaservice.standmb.IStandDwRelationService;
import com.code.metaservice.standmb.IStandMbService;
import com.code.metaservice.standmb.vo.VisualMbTreeVo;
import com.dragonsoft.cicada.datacenter.modules.datavisual.dataset.IDataSetBuilder;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.service.MbService;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.vo.*;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class MbServiceImpl implements MbService {
    @Autowired
    IStandMbService iStandMbService;
    @Autowired
    IDataSourceService dataSourceService;
    @Autowired
    IDataSetBuilder dataSetBuilder;
    @Autowired
    IStandCodeValService standCodeValService;
    @Autowired
    IStandDwRelationService standDwRelationService;

    @Autowired
    QueryDataService queryDataService;


    @Autowired
    private StructuralFeatureService structuralFeatureService;

    @Autowired
    private ModelElementService modelElementService;



    private static final String[] STRINGTYPE = {"VARCHAR", "CHAR", "TEXT", "VARCHAR2", "NVARCHAR2", "NCHAR", "CHARACTER", "CHARACTER VARYING", "NCHAR", "NVARCHAR", "TEXT", "STRING", "TEXT", "KEYWORD", "StringType"};

    @Override
    public PageInfo getPageInfo(SearchListRequest request) {
        PageInfo pageInfo = iStandMbService.getPageInfo(request.getSearchVal(), new PageInfo(request.getPageSize(), request.getCurrentPage()));
        List<StandMb> list = pageInfo.getDataList();
        List<MbListVo> mbVos = list.stream()
                .map(this::toMbListVo)
                .collect(Collectors.toList());
        pageInfo.setDataList(mbVos);
        return pageInfo;
    }


    private MbListVo toMbListVo(StandMb mb) {
        MbListVo vo = new MbListVo();
        StandDwRelation standDwRelation = standDwRelationService.getStandDwRelationByMbId(mb.getId());
        if (standDwRelation != null) {
            vo.setDwInstanceId(standDwRelation.getDwInstanceId());
            vo.setDwTableId(standDwRelation.getDwTableId());
        }
        vo.setCodeCnName(mb.getName());
        vo.setCodeEnName(mb.getCode());

        if (StringUtils.isNotBlank(mb.getKeyStructuralFeatureId())) {
            StructuralFeature key = iStandMbService.getStructuralFeatureById(mb.getKeyStructuralFeatureId());
            if(key !=null){
                vo.setKeyFiledName(key.getCode());
                vo.setKeyFiledId(key.getId());
                Map<String, String> map = iStandMbService.getDataSourId(key.getOwnerId());
                vo.setChartName(map.get("code"));
                vo.setSourceId(map.get("owner_id"));
                vo.setChartId(key.getOwnerId());
            }

        }
        if (StringUtils.isNotBlank(mb.getValStructuralFeatureId())) {
            StructuralFeature val = iStandMbService.getStructuralFeatureById(mb.getValStructuralFeatureId());
            if(val !=null){
                vo.setValFiledName(val.getCode());
                vo.setValFiledId(val.getId());
            }

        }
        vo.setId(mb.getId());
        return vo;
    }

    @Override
    public List<SelectLabelVo> getDataSource() {

        PageData<DataSourceView> pageData = dataSourceService.queryDataSourceByDbType(new PageInfo(1000, 1), null, null);
        List<DataSourceView> list = pageData.getDataList();
        List<SelectLabelVo> labelVos = list.stream().map(
                n -> new SelectLabelVo(n.getId(),
                        n.getDatasourceType() + "-" + n.getName() + "-" + n.getInstanceName())).collect(Collectors.toList());
        return labelVos;
    }

    @Override
    public List<DataSetVo> getDataSet(String sourceId) {
        boolean flag = Boolean.FALSE;
//        List<CommonView> tables = domainObjService.queryTableByDataSourceId(sourceId);//ownerid
//        List<CommonView> commonViews = domainService.queryDataSourceType(sourceId);//id
        List<Map> tableMaps = modelElementService.queryModelElementListByOwnerId(sourceId);
        List<Map> commonViewMaps = modelElementService.queryModelElementListById(sourceId);
        if (commonViewMaps.size() > 0) {
            flag = (commonViewMaps.get(0).get("type").toString().indexOf(DatasourceTypeEnum.Catalog.getName()) > -1);
//            flag = commonViews.get(0).getType().contains(DatasourceTypeEnum.Catalog.getName());
        }
        List<DataSetVo> tableVo = new ArrayList<>();
        for (Map map : tableMaps) {
            if (flag) {
                tableVo.addAll(getTables(map));
            } else {
                DataSetVo table = new DataSetVo();
                table.setValue(map.get("id").toString());
                table.setName(map.get("code").toString());
                tableVo.add(table);
            }
        }
        return tableVo;
    }

    private List<DataSetVo> getTables(Map map) {
        List<DataSetVo> list = new ArrayList();
        List<Map> table0 = modelElementService.queryModelElementListByOwnerId(map.get("id").toString());
        if (table0.size() > 0) {
            for (Map commonView : table0) {
                DataSetVo table1 = new DataSetVo();
                table1.setValue(commonView.get("id").toString());
                table1.setName(commonView.get("code").toString());
                table1.setCode(commonView.get("code").toString());
                list.add(table1);
            }
        }
        return list;
    }

    @Override
    public List<FieldVo> getFiled(String dataSetId) {
        List<StructuralFeature> structuralFeatures = structuralFeatureService.getTableFeatureByOwnerId(dataSetId);
        List<FieldVo> lists = structuralFeatures.stream().map(this::toFeatureViews).collect(Collectors.toList());

        lists = lists.stream().filter(s -> Arrays.asList(STRINGTYPE).contains(s.getType().toUpperCase())).collect(Collectors.toList());
        return lists;
    }

    @Override
    public List<CodeValListVo> selectCodeValList(SelectCodeListRequest request) {
        if (StringUtils.isNotBlank(request.getMbId())) { //编辑
            List<StandCodeVal> codeVals = standCodeValService.getValListByMbId(request.getMbId());
            List<CodeValListVo> listVos = Lists.newArrayList(this.getTabVal(request.getKeyFiledId(), request.getValFiledId()));
            List<CodeValListVo> mbList = codeVals.stream().map(this::toCodeValListVo).collect(Collectors.toList());
            if ("init".equals(request.getType())) {
                for (CodeValListVo codeValListVo : mbList) {
                    for (CodeValListVo listVo : listVos) {
                        if (StringUtils.isNotBlank(codeValListVo.getOrgValue()) && codeValListVo.getOrgValue().equals(listVo.getNodeValue())) {
                            codeValListVo.setNodeValue(listVo.getNodeValue());
                        }
                    }
                }
//                mbList.stream().forEach(n -> {
//                    listVos.stream().forEach(c -> {
//                        if (StringUtils.isNotBlank(n.getOrgValue()) && n.getOrgValue().equals(c.getNodeValue())) {
//                            n.setNodeValue(c.getNodeValue());
//                        }
//                    });
//                });
                return mbList;
            } else {
                listVos.addAll(mbList);
                listVos = listVos.stream().collect(
                        Collectors.collectingAndThen(
                                Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(c -> c.getCode()))), ArrayList::new)
                );
                return listVos;
            }
        } else {//新增
            List<CodeValListVo> listVos = Lists.newArrayList(this.getTabVal(request.getKeyFiledId(), request.getValFiledId()));
            return listVos;
        }
    }
    private CodeValListVo toCodeValListVo(StandCodeVal s) {
        CodeValListVo c = new CodeValListVo();
        c.setCodeValue(s.getCode());
        c.setCode(s.getName());
        c.setOrgValue(s.getOriginallyVal());
        return c;
    }

    @Override
    public String saveAndUpdateMb(SaveAndUpdateMbVo vo) {

        if (StringUtils.isNotBlank(vo.getId())) {//更新
            iStandMbService.deleteMb(vo.getId());
            return this.saveMb(vo);
        }
        return this.saveMb(vo);
    }

    private String saveMb(SaveAndUpdateMbVo vo) {
        StandMb standMb = new StandMb();
        if (StringUtils.isNotBlank(vo.getId())) {
            standMb.setId(vo.getId());
        }

        standMb.setCode(vo.getCode());
        standMb.setName(vo.getName());
        standMb.setKeyStructuralFeatureId(vo.getKeyFileId());
        standMb.setValStructuralFeatureId(vo.getValFileId());
        String id = iStandMbService.saveMb(standMb);

        saveDwRelation(id, vo.getDwInstanceId(), vo.getDwTableId());
        List<CodeValListVo> valListVos = vo.getTabData();

        List<StandCodeVal> standCodeVals = new ArrayList<>();
        valListVos.stream().forEach(n -> {
            standCodeVals.add(this.toCodeVal(n, id));
        });

        standCodeValService.deleteByMbId(id);
        standCodeValService.saveCodes(standCodeVals);
        return id;
    }

    private void saveDwRelation(String mbId, String dwInstanceId, String dwTableId) {
        StandDwRelation standDwRelation = new StandDwRelation();
        standDwRelation.setDwInstanceId(dwInstanceId);
        standDwRelation.setDwTableId(dwTableId);
        standDwRelation.setMbId(mbId);
        standDwRelationService.save(standDwRelation);
    }

    @Override
    public void deleteById(String id) {
        iStandMbService.deleteMb(id);
    }

    @Override
    public MbDetailsVo getMbDetails(String id) {
        MbDetailsVo vo = new MbDetailsVo();
        StandMb standMb = iStandMbService.getMb(id);
        vo.setCnName(standMb.getName());
        vo.setEnName(standMb.getCode());
        List<StandCodeVal> codeVals = standCodeValService.getValListByMbId(id);
        List<MbDetailsCodeListVo> codeListVos = codeVals.stream().map(this::toMbDetailsCodeListVo).collect(Collectors.toList());
        vo.setList(codeListVos);
        vo.setCodeNum(codeListVos.size());
        return vo;
    }

    @Override
    public List<MbEnumTreeVo> getAllList() {
        List<StandMb> mbs = iStandMbService.getMbAll();
        List<MbEnumTreeVo> vos = mbs.stream().map(n -> new MbEnumTreeVo(n.getName(), n.getId())).collect(Collectors.toList());
        return vos;
    }

    @Override
    public List<VisualMbTreeVo> getVisualMbAllList() {
        List<VisualMbTreeVo> visualMbAll = iStandMbService.getVisualMbAll();
        return visualMbAll;
    }

    @Override
    public List<MbEnumTreeVo> getCodesById(String id) {
        List<StandCodeVal> vals = standCodeValService.getValListByMbId(id);
        return vals.stream().map(n -> new MbEnumTreeVo(n.getName(), n.getCode())).collect(Collectors.toList());
    }


    private MbDetailsCodeListVo toMbDetailsCodeListVo(StandCodeVal standCodeVal) {
        MbDetailsCodeListVo vo = new MbDetailsCodeListVo();
        vo.setKeyCode(standCodeVal.getCode());
        vo.setValCode(standCodeVal.getName());
        return vo;
    }

    private StandCodeVal toCodeVal(CodeValListVo vo, String mbId) {
        StandCodeVal standCodeVal = new StandCodeVal();
        standCodeVal.setCode(vo.getCodeValue());
        standCodeVal.setName(vo.getCode());
        standCodeVal.setMbId(mbId);
        standCodeVal.setOriginallyVal(vo.getNodeValue());
        return standCodeVal;
    }

    private Set<CodeValListVo> getTabVal(String keyfid, String valFid) {
        Set<CodeValListVo> listVos = Sets.newLinkedHashSet();
        if (StringUtils.isBlank(keyfid) || StringUtils.isBlank(valFid)) {
            return listVos;
        }
        StructuralFeature keyfeature = iStandMbService.getStructuralFeatureById(keyfid);
        if (null == keyfeature) {
            return listVos;
        }
        StructuralFeature valFeature = iStandMbService.getStructuralFeatureById(valFid);
        if (null == valFeature) {
            return listVos;
        }
        Map<String, String> map = iStandMbService.getDataSourId(keyfeature.getOwnerId());
        String code = map.get("code");
        if (code.contains(".")) {
            code = code.split("\\.")[1];
        }
        StandardQuery query = new StandardQuery();
        if (keyfeature.getOwner().getType().equals(ElasticsearchCollection.class.getSimpleName())) {
            query.setSqlTYpe(StandardQuery.type.ES);
        }
        query.setTableName(code);
        query.setSelectCdin(new SelectCdin(new Select(keyfeature.getCode(), keyfeature.getCode()), new Select(valFeature.getCode(), valFeature.getCode())));
        StandardScript script = new StandardScript(query.toExpression().getScript());
        ParamDataModel paramDataModel = new ParamDataModel();
        paramDataModel.setScript(script.getScript());
        ColumnDataModel columnDataModel = null;
        try {
            columnDataModel = queryDataService.queryData(keyfeature.getOwnerId(), paramDataModel);
        } catch (Exception e) {
         log.error(e.getMessage(),e);
        }
        for (Map clumnData : columnDataModel.getFieldValue()) {
            CodeValListVo valListVo = new CodeValListVo();
            String key = "";
            if(clumnData.get(keyfeature.getCode()) instanceof ArrayList){
                List<String> temp = (List<String>) clumnData.get(keyfeature.getCode());
                key =  this.listToString(temp);
            }else{
                key = (String) clumnData.get(keyfeature.getCode());
            }
            String val = "";
            if(clumnData.get(valFeature.getCode()) instanceof ArrayList){
                List<String> temp = (List<String>) clumnData.get(valFeature.getCode());
                val =  this.listToString(temp);
            }else{
                val = (String) clumnData.get(valFeature.getCode());
            }
            valListVo.setNodeValue(val);
            valListVo.setCode(key);
            valListVo.setCodeValue(val);
            listVos.add(valListVo);
        }
        return listVos;
    }
    private String listToString(List<String> vlas){
        String string = "";
        for (int i = 0; i < vlas.size(); i++) {
            if(i == 0){
                string += vlas.get(i);
            }else{
                string += "," + vlas.get(i);
            }
        }
        return string;
    }

    private FieldVo toFeatureViews(StructuralFeature s) {
        FieldVo fieldVo = new FieldVo();
        fieldVo.setValue(s.getId());
        fieldVo.setName(s.getCode());
        fieldVo.setCode(s.getCode());
        fieldVo.setType(s.getType());
        return fieldVo;
    }

    @Override
    public List<String> getPeerTypesMb(String mbCode) {
        List<StandCodeVal> rytgxlx = standCodeValService.getValList(mbCode);
        List<String> resPeerTypesMb = new ArrayList<>();
        for (StandCodeVal val : rytgxlx) {
            resPeerTypesMb.add(val.getName());
        }
        return resPeerTypesMb;
    }
}
