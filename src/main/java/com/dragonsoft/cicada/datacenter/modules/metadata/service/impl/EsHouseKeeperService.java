package com.dragonsoft.cicada.datacenter.modules.metadata.service.impl;

import com.dragonsoft.cicada.datacenter.modules.metadata.service.HouseKeeperService;
import com.fw.service.annotation.Service;

import java.util.List;
import java.util.Map;
@Service(mappingName = "EsHouseKeeperService")
public class EsHouseKeeperService extends HouseKeeperService {
    @Override
    protected List<Map<String, String>> getData(String dbType) {
        StringBuffer sql = new StringBuffer();
        sql.append("select a.id as catalogId, a.name as name, a.id as id from t_md_element a left join t_sys_resources b on a.id = b.source_id where b.type = 'RESOURCES_DATASOURCE'" +
                " and upper(b.remark) = :dbtype and a.id not in (select DISTINCT f.owner_id from t_dw_table_mapping e left join  t_md_element f on e.classifier_stat_id = f.id) ");
        return this.baseDao.sqlQueryForList(sql.toString(), addParam("dbtype", dbType.toUpperCase()).param());
    }
}
