package com.dragonsoft.cicada.datacenter.modules.firstpage.resourcecount.service.impl;

import cn.hutool.core.util.StrUtil;
import com.code.common.paging.PageInfo;
import com.code.common.utils.StringUtils;
import com.code.metaservice.datavisual.IDashBoardService;
import com.code.metaservice.datawarehouse.IDataWareTreeService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.service.IDataSetOperationService;
import com.dragonsoft.cicada.datacenter.modules.datavisual.dashboard.IDashboardBuilder;
import com.dragonsoft.cicada.datacenter.modules.firstpage.resourcecount.service.MyResourceService;
import com.dragonsoft.cicada.datacenter.modules.modeling.rapidanalysis.service.RapidAnalysisService;
import com.dragonsoft.cicada.datacenter.modules.modeling.rapidanalysis.vo.ResultRapidTreeNode;
import com.dragonsoft.cicada.datacenter.modules.modeling.service.DataModelingService;
import com.dragonsoft.cicada.datacenter.modules.system.dataportal.service.IDataPortalService;
import com.fw.service.BaseService;
import com.fw.service.annotation.Service;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
@PropertySource("classpath:case-config.properties")
public class MyResourceServiceImpl extends BaseService implements MyResourceService {

    @Autowired
    private IDataSetOperationService dataSetOperationService;

    @Autowired
    private IDataWareTreeService dataWareTreeService;

    @Autowired
    private DataModelingService dataModelingService;

    @Autowired
    private IDataPortalService dataPortalService;

    @Autowired
    IDashboardBuilder dashboardBuilder;

    @Autowired
    IDashBoardService dashBoardService;

    @Value("${standModel}")
    private boolean standModel;

    @Value("${sceModel}")
    private boolean sceModel;

    @Autowired
    private RapidAnalysisService rapidAnalysisService;

    @Override
    public Map<String, Object> getMyResourceCount(String myUserId) {
        Map<String,String> map = new HashMap<>();
        map.put("myUserId",myUserId);
        String dataSetCount = getDataSetCount(myUserId);
        String dataModelCount1 = getDataModelCount(myUserId);
        String quickAnaCount =  this.baseDao.sqlQueryForValue("select count(id) from t_etl_trans where operate_user_id = :myUserId and task_group = 'QUICK_SPARK' and id in (select element_id from t_md_classify_element)", map);
        //String quickAnaCount =  getQuickAnaCount(myUserId);
        String dashboardCount = getDashBoardCount1(myUserId);
        String portalCount =  getPortalCount(myUserId);
        String serviceAPICount =  this.baseDao.sqlQueryForValue("select count(id) from t_md_service_publication where operate_user_id = :myUserId and id in (select element_id from t_md_classify_element)",map);
        Map<String,Object> resultCount = new HashMap<>();
        resultCount.put("dataSetCount",dataSetCount);
        resultCount.put("quickAnaCount",quickAnaCount);
        resultCount.put("dataModelCount",dataModelCount1);
        resultCount.put("dashboardCount",dashboardCount);
        resultCount.put("portalCount",portalCount);
        resultCount.put("serviceAPICount",serviceAPICount);
        return resultCount;
    }

    @Override
    public Map<String, Object> getAllResourceCount() {
        String dataSetCount=countDataSet();
        String dataModel=getDataModelCount(null);
        String quickAnaCount =  this.baseDao.sqlQueryForValue("select count(id) from t_etl_trans where task_group = 'QUICK_SPARK' and id in (select element_id from t_md_classify_element)");
        String dashboardCount = this.getDashBoardCount1(null);
                //this.baseDao.sqlQueryForValue("select count(id) from t_v_dashboards ");
        String portalCount= getPortalCount(null);
                //portalCount();
        String serviceAPICount =  this.baseDao.sqlQueryForValue("select count(id) from t_md_service_publication where  id in (select element_id from t_md_classify_element)");

        Map<String,Object> resultCount = new HashMap<>();
        resultCount.put("dataSetCount",dataSetCount);
        resultCount.put("quickAnaCount",quickAnaCount);
        resultCount.put("dataModelCount",dataModel);
        resultCount.put("dashboardCount",dashboardCount);
        resultCount.put("portalCount",portalCount);
        resultCount.put("serviceAPICount",serviceAPICount);
        //去掉标准的?
        return resultCount;
    }

    public String countDataSet(){
        //直接用目录获取所有的数据集,不用关心分享
        String busiDirId = this.baseDao.sqlQueryForValue("select id from t_md_busi_dir where code = 'DATA_SET_DIR' ");
        Map<String,String> map = new HashMap<>();

        map.put("busiDirId",busiDirId);
        String sql = " select c.id,c.name,e.element_id  " +
                "from t_md_busi_classify c " +
                "left join t_md_classify_element  e on c.id = e.busi_classify_id " +
                "where  busi_dir_id =:busiDirId";
        List<Map<String,String>> classifyMaps = this.baseDao.sqlQueryForList(sql, map);
        List<String> elementIds=classifyMaps.stream().filter(m->{
            if (!standModel){
                if ("标准模型".equals(m.get("name"))&&(null !=m.get("element_id"))){
                    return true;
                }
            }
            return false;
        }).map(m->m.get("element_id")).collect(Collectors.toList());

        Map<String,Object>  param=new HashMap<>();
        // 一个数据集属于多个目录，统计时要去重
        String logicCountSql="select count(distinct l.id) " +
                "from t_md_logic_dataobj l " +
                " inner join t_md_classify_element e on l.id =e.element_id  " ;
        if (CollectionUtils.isNotEmpty(elementIds)){
            logicCountSql=logicCountSql.concat(" and l.id not in(:elementIds) ");
            param.put("elementIds",elementIds);
        }
        Integer allCount=0;
        String count=baseDao.sqlQueryForValue(logicCountSql,param);
        String tableCCount=getDatasetTablec("cf681b2849a44c3da632133ef85b2c5f");
        allCount=Integer.valueOf(count)+Integer.valueOf(tableCCount);
        if (sceModel){
            String secCount=getDatasetTablec("20d8701a954249cf8cb455740738f83a");
            allCount+=Integer.valueOf(secCount);
        }
        //是否要补充下另外的两种查询,然后这边的要挂靠到目录那边
        return allCount.toString();
    }

    public String getDatasetTablec(String id){
        //写死计算id cf681b2849a44c3da632133ef85b2c5f
        String allClassifySql="select e.element_id " +
                "from t_md_busi_classify c " +
                "left join t_md_classify_element e ON  e.busi_classify_id =c.id " +
                "where busi_dir_id =:id";
        List<Map<String,String>> allClassify=baseDao.sqlQueryForList(allClassifySql,addParam("id",id).param());
        List<String> classifyIds=allClassify.stream().map(m->m.get("element_id")).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(classifyIds)){
            return "0";
        }
        //获取并收集该目录下的所有关联的elementId
        //计算
        StringBuilder sb=new StringBuilder();
        sb.append("SELECT ").append(" count(1) ").append("FROM ").append("t_md_logic_data_relation r ").append("LEFT JOIN t_md_logic_dataobj M ON M . ID = r.logic_data_obj_id ").append("WHERE ").append("r.element_id in (:dwDbId)").append("and r.relation_type = '0' ");
        String countS = "0";
        classifyIds=classifyIds.stream().filter(c->null !=c).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(classifyIds)){
             countS = baseDao.sqlQueryForValue(sb.toString(), addParam("dwDbId", classifyIds).param());
        }
        return countS;
    }

    private String getQuickAnaCount(String userId) {
        Long total = 0L;
        List<ResultRapidTreeNode> busiClassifies = rapidAnalysisService.getAllRapidAnaTree(userId, "time");
        for (ResultRapidTreeNode busiClassify : busiClassifies) {
            PageInfo rapidAnalysisPage = rapidAnalysisService.getRapidAnalysisPage(busiClassify.getId(),"",1,10,userId);
            total += rapidAnalysisPage.getTotalCount();
        }
        return total + "";
    }


    //建模个数
    private String getDataModelCount(String userId){
        Map<String,String> map = new HashMap<>();

        String dataModelCountSql="select count(id) from t_etl_trans where task_group = 'DEF' AND position('预览_preview' in name) = 0  AND position('发布_publish' in name) = 0 and id in (select element_id from t_md_classify_element)";
        if (StringUtils.isNotBlank(userId)){
            dataModelCountSql=dataModelCountSql.concat(" and operate_user_id = :userId ");
            map.put("userId",userId);
        }
        String dataModelCount =  this.baseDao.sqlQueryForValue(dataModelCountSql, map);
        int totalCount = Integer.parseInt(dataModelCount);
        //List<Map<String,Object>> treeNodes = (List) dataModelingService.getModelingTransTreeNodes("", "", userId, "TRANS_DIR_MF");
        if (!sceModel){
            Map param=new HashMap();
            String sql = "select id from t_md_busi_classify where name = '场景案例' and code = 'TRANS_DIR_MF_CASE'  ";
            if (StringUtils.isNotBlank(userId)){
                sql=sql.concat(" and operate_user_id = :userId ");
                param.put("userId",userId);
            }
            String busiClassifyId = this.baseDao.sqlQueryForValue(sql, param);
            if (StrUtil.isNotEmpty(busiClassifyId)){
                Map<String, Object> result = dataModelingService.listTransInfo("","" ,busiClassifyId, "TRANS_DIR_STANDARD", 1, 10, userId);
                String totalCount1 = result.get("totalCount").toString();
                //Integer transCount = (Integer) result.get("totalCount");
                totalCount -= Integer.parseInt(totalCount1);
            }
        }
        if (!standModel){
            Map param=new HashMap();
            String sql = "select id from t_md_busi_classify where name = '标准模型' and code = 'TRANS_DIR_STANDARD' ";
            if (StringUtils.isNotBlank(userId)){
                sql=sql.concat(" and operate_user_id = :userId  ");
                param.put("userId",userId);
            }
            String busiClassifyId = this.baseDao.sqlQueryForValue(sql, param);
            if (StringUtils.isNotBlank(busiClassifyId)){
                Map<String, Object> result = dataModelingService.listTransInfo("","", busiClassifyId, "TRANS_DIR_STANDARD", 1, 10, userId);
                String totalCount1 = result.get("totalCount").toString();
                //Long transCount = (Long) result.get("totalCount");
                totalCount -= Integer.parseInt(totalCount1);
            }

        }
     /*   for (Map<String, Object> treeNode : treeNodes) {
            Map<String, Object> result = dataModelingService.listTransInfo("", (String) treeNode.get("id"), "TRANS_DIR_MF", 1, 10, userId);
            totalCount += (Integer) result.get("totalCount");
        }*/
        return totalCount + "";
    }

    //数据集个数
    private String getDataSetCount(String myUserId){
        String busiDirId = this.baseDao.sqlQueryForValue("select id from t_md_busi_dir where code = 'DATA_SET_DIR' ");
        Map<String,String> map = new HashMap<>();
        map.put("userId",myUserId);
        map.put("busiDirId",busiDirId);
        String sql = " select id,name from t_md_busi_classify where operate_user_id = :userId and busi_dir_id = :busiDirId";
        List<Map<String,String>> classifyIds = this.baseDao.sqlQueryForList(sql, map);
        Long count = 0l;
        for (Map<String, String> classifyId : classifyIds) {
            if (!standModel){
                if ("标准模型".equals(classifyId.get("name"))){
                    continue;
                }
            }
            PageInfo id = dataSetOperationService.getDataSetPage(classifyId.get("id"), null, 1, 10, myUserId, null, true);
            count += id.getTotalCount();
        }
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageIndex(1);
        pageInfo.setPageSize(1);
        pageInfo.setSortField("name");
        pageInfo.setSortDirect("desc");
        PageInfo dataSet = dataWareTreeService.queryDataBaseTableC(pageInfo, "", "cf681b2849a44c3da632133ef85b2c5f", myUserId, "");
        if (dataSet!=null && dataSet.getTotalCount() > 0){
            count += dataSet.getTotalCount();
        }
        if (sceModel){
            PageInfo caseSet = dataWareTreeService.queryDataBaseTableC(pageInfo, "", "20d8701a954249cf8cb455740738f83a", myUserId, "");
            count += caseSet.getTotalCount();
        }
        return String.valueOf(count);
    }

    private String getDashboardCount(String myUserId){
        String sql = "select count(id) from t_v_dashboards where operate_user_id = :myUserId ";
        String parentId = this.baseDao.sqlQueryForValue("select id from t_v_dashboard_groups where code = 'BOARD_DIR_CASE' and operate_user_id = :myUserId", this.addParam("myUserId", myUserId).param());
        Map<String,Object> map1 = new HashMap<>();
        if (!sceModel){
            if (parentId!=null){
                List<Map<String,String>> list = this.baseDao.sqlQueryForList("select id from t_v_dashboard_groups where parent_id = :id", this.addParam("id", parentId).param());
                List<String> list1 = new ArrayList<>();
                list1.add(parentId);
                for (Map<String, String> map : list) {
                    list1.add(map.get("id"));
                }
                sql += " and group_id not in (:list1) ";
                map1.put("list1",list1);
            }


        }
        if (!standModel){
            List<Map<String,String>> list = this.baseDao.sqlQueryForList("select id from t_v_dashboard_groups where parent_id = '2' ");
            List<String> list2 = new ArrayList<>();
            list2.add("2");
            for (Map<String, String> map : list) {
                list2.add(map.get("id"));
            }
            sql += " and group_id not in (:list2) ";
            map1.put("list2",list2);

        }
        map1.put("myUserId",myUserId);
        return this.baseDao.sqlQueryForValue(sql, map1);
    }

    private String getDashBoardCount1(String myUserId){
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageIndex(1);
        pageInfo.setPageSize(1);
        PageInfo list = dashBoardService.getListForPage("1", "", pageInfo, myUserId);
        long totalCount = list.getTotalCount();
        if (sceModel){
            PageInfo list1 = dashBoardService.getListForPage("c1c92f3b20f2487cae20e2a40c4e60cd", "", pageInfo, myUserId);
            totalCount += list1.getTotalCount();
        }
        if (standModel){
            PageInfo list2 = dashBoardService.getListForPage("2", "", pageInfo, myUserId);
            totalCount += list2.getTotalCount();
        }
        return String.valueOf(totalCount);
    }

    private String portalCount(){
        String busiDirId = this.baseDao.sqlQueryForValue("select id from t_md_busi_dir where code = 'PORTAL_DIR' ");
        Map<String,String> map = new HashMap<>();
        map.put("busiDirId",busiDirId);
        String sql = " select c.id,c.name,e.element_id  " +
                "from t_md_busi_classify c " +
                "left join t_md_classify_element  e on c.id = e.busi_classify_id " +
                "where  busi_dir_id =:busiDirId ";
        List<Map<String,String>> classifyMaps = this.baseDao.sqlQueryForList(sql, map);
        List<String> elementIds=classifyMaps.stream().filter(m->{
            if (!standModel){
                if ("标准模型".equals(m.get("name"))&&(null !=m.get("element_id"))){
                    return true;
                }
            }
            if (!sceModel){
                if ("场景案例".equals(m.get("name"))&&(null !=m.get("element_id"))){
                    return true;
                }
            }
            return false;
        }).map(m->m.get("element_id")).collect(Collectors.toList());
        Map param=new HashMap();
        String sqlCount="select count(1) from  t_md_portal tp";
        if (CollectionUtils.isNotEmpty(elementIds)){
            sqlCount=sqlCount.concat(" and id not in(:elementIds )");
            param.put("elementIds",elementIds);
        }
        //要排除掉两种模式对应的目录的关联项
        return baseDao.sqlQueryForValue(sqlCount,param);
    }

    private String getPortalCount(String myUserId) {
        String busiDirId = this.baseDao.sqlQueryForValue("select id from t_md_busi_dir where code = 'PORTAL_DIR' ");
        Map<String,String> map = new HashMap<>();

        map.put("busiDirId",busiDirId);
        String sql = " select id,name from t_md_busi_classify where   busi_dir_id = :busiDirId ";
        if (StringUtils.isNotBlank(myUserId)){
            sql=sql.concat(" and operate_user_id = :userId ");
            map.put("userId",myUserId);
        }
        List<Map<String,String>> classifyIds = this.baseDao.sqlQueryForList(sql, map);
        Long count = 0l;
        for (Map<String, String> classifyId : classifyIds) {
            if (!standModel){
                if ("标准模型".equals(classifyId.get("name"))){
                    continue;
                }
            }
            if (!sceModel){
                if ("场景案例".equals(classifyId.get("name"))){
                    continue;
                }
            }
            PageInfo pageInfo = new PageInfo();
            pageInfo.setPageSize(10);
            pageInfo.setPageIndex(1);
            PageInfo id  = null;
            try {
                id = dataPortalService.getPortalPage(myUserId, "", classifyId.get("id"), pageInfo);
            } catch (Exception e) {
             log.error(e.getMessage(),e);
            }
            if (null !=id) {
                count += id.getTotalCount();
            }else {
                count += 0;
            }
        }

        return String.valueOf(count);

    }
}
