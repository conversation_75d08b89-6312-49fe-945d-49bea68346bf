package com.dragonsoft.cicada.datacenter.modules.datavisual.model;

import com.code.common.utils.StringUtils;
import com.code.dragonsoft.dataquery.service.querymodel.ColumnDataModel;
import com.code.metadata.datavisual.WidgetDatasetDims;
import com.code.metadata.datavisual.WidgetDatasetMeasures;
import com.dragonsoft.cicada.datacenter.modules.datavisual.model.annotation.WidgetLabel;
import com.dragonsoft.cicada.datacenter.modules.datavisual.model.enums.WidgetType;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by zhuangjp on 2020/11/17 8:24
 */
@WidgetLabel(name = "热力图", type = WidgetType.HeatMap, describe = "热力图")
public class HeatMap extends BarChartWidget {
    public Map<String, Object> builderResult(ColumnDataModel columns, String code) {
        WidgetDatasetMeasures next = this.widgetDataset.getFromListMeasures().iterator().next();
        List<WidgetDatasetDims> fromListDims = this.widgetDataset.getFromListDims();
        String xDims = StringUtils.isNotBlank(fromListDims.get(0).getFiledAlias())?fromListDims.get(0).getFiledAlias():fromListDims.get(0).getFiledCode();
        String yDims = StringUtils.isNotBlank(fromListDims.get(1).getFiledAlias())?fromListDims.get(1).getFiledAlias():fromListDims.get(1).getFiledCode();

        this.barChartData.addColumns(xDims);
        this.barChartData.addColumns(yDims);
        String mes = StringUtils.isNotBlank(next.getFiledAlias()) ? next.getFiledAlias():next.getFiledCode();
        this.barChartData.addColumns(mes);

        /*Set<String> fieldKeySet = columns.getFieldName().keySet();
        List<String> dimList = Lists.newLinkedList();
        for (String key : fieldKeySet) {
            String tempField = StringUtils.isNotBlank( next.getFiledAlias()) ?  next.getFiledAlias() : next.getFiledCode();
            if(key.equals(tempField)){
                this.barChartData.addColumns(tempField);
            }else{
                dimList.add(tempField);
            }
        }
        this.barChartData.columns.addAll(dimList);*/
//        this.barChartData.rows = columns.getFieldValue();
        List<Map> fieldValue = columns.getFieldValue();
        List<Map> resList = Lists.newArrayList();
        for (Map map : fieldValue) {
            Map<String,Object> reMap = Maps.newLinkedHashMap();
            Object xValue = map.get(xDims);
            Object yValue = map.get(yDims);
            if(xValue!= null && yValue != null){
                reMap.put(xDims,xValue);
                reMap.put(yDims,yValue);
                reMap.put(mes,map.get(mes));
                resList.add(reMap);
            }

        }
        this.barChartData.rows = resList;
        Map re = new HashMap();

        List<String> dimsCode = this.widgetDataset.getFromListDims().stream().map(s->s.getFiledName()).collect(Collectors.toList());
        this.barChartData.setDimsCodes(dimsCode);
        re.put("code", 1);
        re.put("data", this.barChartData);
        return re;
    }
}
