package com.dragonsoft.cicada.datacenter.modules.datavisual.model.annotation;


import com.dragonsoft.cicada.datacenter.modules.datavisual.model.enums.WidgetType;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 控件加载器
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
public @interface WidgetLabel {

    String code() default "";

    //中文名称
    String name();

    //类型
    WidgetType type();



    String describe() default "";

//    在分类中的排序
    int orderBy() default 0;

}
