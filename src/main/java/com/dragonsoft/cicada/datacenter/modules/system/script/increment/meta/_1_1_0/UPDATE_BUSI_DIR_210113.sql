-- 数据挖掘目录
INSERT INTO "public"."t_md_busi_dir" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id", "is_show", "busi_dir_type", "sort_no") VALUES ('fd2e6b885b954a769b1b53de54f30b7c', '我的空间', NULL, 'BusiDir', NULL, 'DIG_DIR_MF', NULL, NULL, NULL, NULL, NULL, NULL, 'DIG_DIR_MF', NULL);

INSERT INTO "public"."t_md_busi_classify" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id", "busi_dir_id", "parent_classify_id", "sort_no", "res_count", "x", "y") VALUES ('adc0de8da629478ca09683fd7244edcc', '我的空间', NULL, 'BaseBusiClassify', NULL, 'DIG_DIR_MF_MY', NULL, NULL, NULL, '2020-10-27 17:14:25', '40289754739d4e7e11729d4e682b2020', 'fd2e6b885b954a769b1b53de54f30b7c', NULL, NULL, NULL, NULL, NULL);

--数据集
INSERT INTO "public"."t_md_busi_classify" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id", "busi_dir_id", "parent_classify_id", "sort_no", "res_count", "x", "y") VALUES ('f8d4b5d89b554de2a99f8e91986a9a74', '我的空间', NULL, 'BaseBusiClassify', NULL, 'TRANS_DIR_MF_MY', NULL, NULL, NULL, '2021-01-08 17:22:06', '40289754739d4e7e11729d4e682b2020', '2c90e5a45deef37d015deef471810019', NULL, NULL, NULL, NULL, NULL);

INSERT INTO "public"."t_md_busi_classify" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id", "busi_dir_id", "parent_classify_id", "sort_no", "res_count", "x", "y") VALUES ('fd2e6b885b954a769b1b53de5f154ds3', '标准模型', NULL, 'BaseBusiClassify', NULL, 'DATA_SET_DIR_STANDARD', NULL, NULL, NULL, '2020-11-12 17:14:25', '40289754739d4e7e11729d4e682b2020', 'a06a65efd6e2476987acfcf76fdwipq', NULL, NULL, NULL, NULL, NULL);

--数据建模
INSERT INTO "public"."t_md_busi_classify" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id", "busi_dir_id", "parent_classify_id", "sort_no", "res_count", "x", "y") VALUES ('e8383342b644471b821757f2bfea6600', '我的空间', NULL, 'BaseBusiClassify', NULL, 'DATASET_DIR_MY', NULL, NULL, NULL, '2020-09-08 17:22:06', '40289754739d4e7e11729d4e682b2020', 'a06a65efd6e2476987acfcf76fdwipq', NULL, NULL, NULL, NULL, NULL);

INSERT INTO "public"."t_md_busi_classify" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id", "busi_dir_id", "parent_classify_id", "sort_no", "res_count", "x", "y") VALUES ('fd2e6b885b954a769b1b53de5fgkips0', '标准模型', NULL, 'BaseBusiClassify', NULL, 'TRANS_DIR_STANDARD', NULL, NULL, NULL, '2021-01-12 17:14:25', '40289754739d4e7e11729d4e682b2020', '2c90e5a45deef37d015deef471810019', NULL, NULL, NULL, NULL, NULL);

--数据挖掘
INSERT INTO "public"."t_md_busi_classify" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id", "busi_dir_id", "parent_classify_id", "sort_no", "res_count", "x", "y") VALUES ('fd2e6b885b954a769b1b53de5fg69spo', '标准模型', NULL, 'BaseBusiClassify', NULL, 'DIG_DIR_STANDARD', NULL, NULL, NULL, '2021-01-12 17:14:25', '40289754739d4e7e11729d4e682b2020', 'fd2e6b885b954a769b1b53de54f30b7c', NULL, NULL, NULL, NULL, NULL);

--可视化
INSERT INTO "public"."t_v_dashboard_groups" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id",  "parent_id") VALUES ('fd2e6b885b954a769b1b53de5fpomkls', '标准模型', NULL, 'BaseBusiClassify', NULL, 'BOARD_DIR_STANDARD', NULL, NULL, NULL, '2021-01-12 17:14:25', '40289754739d4e7e11729d4e682b2020', NULL);

--数据门户
INSERT INTO "public"."t_md_busi_dir"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id", "is_show", "busi_dir_type", "sort_no") VALUES ('7621843a04ab41e955361ef589a6b83c', '全部', NULL, 'BusiDir', '', 'PORTAL_DIR', '', '', '', '', '', ' ', 'PORTAL_DIR', NULL);

INSERT INTO "public"."t_md_busi_classify" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id", "busi_dir_id", "parent_classify_id", "sort_no", "res_count", "x", "y") VALUES ('fd2e6b885b954a769b1b53dedlo9', '我的空间', NULL, 'BaseBusiClassify', NULL, 'PORTAL_DIR_MY', NULL, NULL, NULL, '2021-01-25 17:14:25', '40289754739d4e7e11729d4e682b2020', '7621843a04ab41e955361ef589a6b83c', NULL, NULL, NULL, NULL, NULL);

INSERT INTO "public"."t_md_busi_classify" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id", "busi_dir_id", "parent_classify_id", "sort_no", "res_count", "x", "y") VALUES ('fd2e6b885b954a769b1b53de5fpo', '标准模型', NULL, 'BaseBusiClassify', NULL, 'POR_DIR_STANDARD', NULL, NULL, NULL, '2021-01-12 17:14:25', '40289754739d4e7e11729d4e682b2020', '7621843a04ab41e955361ef589a6b83c', NULL, NULL, NULL, NULL, NULL);
