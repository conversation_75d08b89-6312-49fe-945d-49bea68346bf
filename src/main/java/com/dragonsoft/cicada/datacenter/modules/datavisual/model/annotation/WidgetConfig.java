package com.dragonsoft.cicada.datacenter.modules.datavisual.model.annotation;


import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
public @interface WidgetConfig {

    String attrName() default "";

    String defaultVal() default "";

    String group() default "";






}
