-- 词云图
INSERT INTO public.t_v_widget_metas
(id, "name", "version", "type", memo, code, owner_id, map_key, extended_type, operate_time, operate_user_id, category_id, "describe", order_by)
VALUES('3fb0dkiwedr543d18adiu7847aswf328', '词云图', NULL, 'com.dragonsoft.cicada.datacenter.modules.datavisual.model.WordCloudWidget', NULL, 'WordCloudWidget', NULL, NULL, NULL, '2020-03-29 13:10:28', NULL, '1_10', NULL, 0);

-- 组合图
INSERT INTO public.t_v_widget_metas
(id, "name", "version", "type", memo, code, owner_id, map_key, extended_type, operate_time, operate_user_id, category_id, "describe", order_by)
VALUES('676tt5rwedr543d18adiu7847aswf328', '组合图', NULL, 'com.dragonsoft.cicada.datacenter.modules.datavisual.model.CombinationWidget', NULL, 'CombinationWidget', NULL, NULL, NULL, '2020-03-29 13:10:28', NULL, '1_11', NULL, 0);



-- 横向柱状图
INSERT INTO public.t_v_widget_metas
(id, "name", "version", "type", memo, code, owner_id, map_key, extended_type, operate_time, operate_user_id, category_id, "describe", order_by)
VALUES('sd3e4751471b4f49a4fba46sdew234r5', '横向柱状图', NULL, 'com.dragonsoft.cicada.datacenter.modules.datavisual.model.BarChartWidget', NULL, 'TransverseBarChartWidget', NULL, NULL, NULL, '2020-03-26 17:23:39', NULL, '1_1', NULL, 0);

-- 横向堆叠柱状图
INSERT INTO public.t_v_widget_metas
(id, "name", "version", "type", memo, code, owner_id, map_key, extended_type, operate_time, operate_user_id, category_id, "describe", order_by)
VALUES('sd3e4751234edf49a4fba46sdew234r5', '横向堆叠柱状图', NULL, 'com.dragonsoft.cicada.datacenter.modules.datavisual.model.BarChartWidget', NULL, 'TransverseStackBarChartWidget', NULL, NULL, NULL, '2020-03-26 17:23:39', NULL, '1_1', NULL, 0);


-- 热力图
INSERT INTO public.t_v_widget_metas
(id, "name", "version", "type", memo, code, owner_id, map_key, extended_type, operate_time, operate_user_id, category_id, "describe", order_by)
VALUES('3fb0dkiuf1d34bd18adiu7847aswf328', '热力图', NULL, 'com.dragonsoft.cicada.datacenter.modules.datavisual.model.HeatMap', NULL, 'HeatMap', NULL, NULL, NULL, '2020-03-29 13:10:28', NULL, '1_9', NULL, 0);


-- PGIS地图
INSERT INTO public.t_v_widget_metas
(id, "name", "version", "type", memo, code, owner_id, map_key, extended_type, operate_time, operate_user_id, category_id, "describe", order_by)
VALUES('3fb0dkiuf1d34bd18adiu78471c9es78', 'PGIS地图', NULL, 'com.dragonsoft.cicada.datacenter.modules.datavisual.model.PGISWidget', NULL, 'PGISWidget', NULL, NULL, NULL, '2020-03-29 13:10:28', NULL, '1_8', NULL, 0);


INSERT INTO public.t_v_widget_metas
(id, "name", "version", "type", memo, code, owner_id, map_key, extended_type, operate_time, operate_user_id, category_id, "describe", order_by)
VALUES('3fb056d0c1d34bd18adiu78471c9es78', 'Tab页签', NULL, 'com.dragonsoft.cicada.datacenter.modules.datavisual.model.TabWidget', NULL, 'TabWidget', NULL, NULL, NULL, '2020-03-29 13:10:28', NULL, '1_7', NULL, 0);

-- ******************
-- 面积图
INSERT INTO public.t_v_widget_metas
(id, "name", "version", "type", memo, code, owner_id, map_key, extended_type, operate_time, operate_user_id, category_id, "describe", order_by)
VALUES('6547c66bbceb4f80b1cf470adplrjd83', '面积图', NULL, 'com.dragonsoft.cicada.datacenter.modules.datavisual.model.LineChartWidget', NULL, 'AreaGraphWidget', NULL, NULL, NULL, '2020-03-31 10:02:15', NULL, '1_2', NULL, 0);

-- 堆叠柱状图
INSERT INTO public.t_v_widget_metas
(id, "name", "version", "type", memo, code, owner_id, map_key, extended_type, operate_time, operate_user_id, category_id, "describe", order_by)
VALUES('e932b751471b4f49a4fba46sdew234r5', '堆叠柱状图', NULL, 'com.dragonsoft.cicada.datacenter.modules.datavisual.model.BarChartWidget', NULL, 'StackBarChartWidget', NULL, NULL, NULL, '2020-03-26 17:23:39', NULL, '1_1', NULL, 0);

-- 环形饼图
INSERT INTO public.t_v_widget_metas
(id, "name", "version", "type", memo, code, owner_id, map_key, extended_type, operate_time, operate_user_id, category_id, "describe", order_by)
VALUES('3fb056d0c1d34bd18aba6bdfrfgtye32', '嵌套饼图', NULL, 'com.dragonsoft.cicada.datacenter.modules.datavisual.model.PieChartWidget', NULL, 'RingPieChartWidget', NULL, NULL, NULL, '2020-03-29 13:10:28', NULL, '1_3', NULL, 0);

-- 气泡地图
INSERT INTO public.t_v_widget_metas
(id, "name", "version", "type", memo, code, owner_id, map_key, extended_type, operate_time, operate_user_id, category_id, "describe", order_by)
VALUES('3fb0dolpd1d34bd18aba6bdoliol3245', '气泡地图', NULL, 'com.dragonsoft.cicada.datacenter.modules.datavisual.model.MapChartWidget', NULL, 'BubbleMap', NULL, NULL, NULL, '2020-03-29 13:10:28', NULL, '1_5', NULL, 0);

-- 彩色地图
INSERT INTO public.t_v_widget_metas
(id, "name", "version", "type", memo, code, owner_id, map_key, extended_type, operate_time, operate_user_id, category_id, "describe", order_by)
VALUES('3fb0dolpd1d34bd18aba6bddsjuesl34', '色彩地图', NULL, 'com.dragonsoft.cicada.datacenter.modules.datavisual.model.MapChartWidget', NULL, 'ColourMap', NULL, NULL, NULL, '2020-03-29 13:10:28', NULL, '1_5', NULL, 0);


--修改折线图、饼图、柱状图、地图的名称,原先脚本name没有加基本
UPDATE public.t_v_widget_metas
SET "name"='基本柱状图', "version"=NULL, "type"='com.dragonsoft.cicada.datacenter.modules.datavisual.model.BarChartWidget', memo=NULL, code='BarChartWidget', owner_id=NULL, map_key=NULL, extended_type=NULL, operate_time='2020-03-26 17:23:39', operate_user_id=NULL, category_id='1_1', "describe"=NULL, order_by=0
WHERE id='e932b751471b4f49a4fba466c82d811b';

UPDATE public.t_v_widget_metas
SET "name"='基本折线图', "version"=NULL, "type"='com.dragonsoft.cicada.datacenter.modules.datavisual.model.LineChartWidget', memo=NULL, code='LineChartWidget', owner_id=NULL, map_key=NULL, extended_type=NULL, operate_time='2020-03-31 10:02:15', operate_user_id=NULL, category_id='1_2', "describe"=NULL, order_by=0
WHERE id='6547c66bbceb4f80b1cf470f7e99daec';

UPDATE public.t_v_widget_metas
SET "name"='基本饼图', "version"=NULL, "type"='com.dragonsoft.cicada.datacenter.modules.datavisual.model.PieChartWidget', memo=NULL, code='PieChartWidget', owner_id=NULL, map_key=NULL, extended_type=NULL, operate_time='2020-03-29 13:10:28', operate_user_id=NULL, category_id='1_3', "describe"=NULL, order_by=0
WHERE id='3fb056d0c1d34bd18aba6b2471c9bb61';

UPDATE public.t_v_widget_metas
SET "name"='基本地图', "version"=NULL, "type"='com.dragonsoft.cicada.datacenter.modules.datavisual.model.MapChartWidget', memo=NULL, code='MapChartWidget', owner_id=NULL, map_key=NULL, extended_type=NULL, operate_time='2020-03-29 13:10:28', operate_user_id=NULL, category_id='1_5', "describe"=NULL, order_by=0
WHERE id='3fb0dolpd1d34bd18aba6b2471c9es78';



-- 钻取的维度
-- DROP TABLE public.t_v_widget_dataset_dims_drill

CREATE TABLE public.t_v_widget_dataset_dims_drill (
	id varchar(32) NOT NULL,
	widget_dataset_id varchar(32) NULL,
	filed_id varchar(32) NULL,
	filed_name varchar(32) NULL,
	filed_code varchar(32) NULL,
	"type" varchar(32) NULL,
	"index" int4 NULL DEFAULT 0,
	order_by varchar(32) NULL,
	format_decimals int4 NULL DEFAULT 0,
	is_separator int4 NULL DEFAULT 0,
	format_time varchar(32) NULL,
	filter_type varchar(32) NULL,
	is_distinct varchar(5) NULL,
	file_alias varchar(32) NULL,
	CONSTRAINT t_v_widget_dataset_dims_drill_pkey PRIMARY KEY (id)
)
WITH (
	OIDS=FALSE
) ;
