package com.dragonsoft.cicada.datacenter.modules.supermarkmodel.service;


import com.dragoninfo.dfw.bean.Result;

import java.util.Map;

public interface IMainPageModelService  {

    /**
     * 模型信息统计
     */
    Result queryModelCountInfo(Map<String, Object> queryCountMap);

    /**
     * 查询模型排行榜列表
     */
    Result queryModelRankingList(Map<String, Object> map);

    /**
     * 查询模型用户排行上架榜列表
     */
    Result queryModelUserRankingList(Map<String, Object> queryModelMap);
}
