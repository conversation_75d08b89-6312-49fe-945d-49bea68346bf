package com.dragonsoft.cicada.datacenter.modules.modeling.aimodeling.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.db.Db;
import cn.hutool.db.Entity;
import com.code.common.paging.PageInfo;
import com.code.common.utils.StringUtils;
import com.code.common.utils.assertion.Assert;
import com.code.metadata.aimodel.ScriptInfo;
import com.code.metadata.aimodel.ScriptLog;
import com.code.metadata.aimodel.ScriptLogDetail;
import com.code.metadata.business.directory.*;
import com.code.metadata.model.core.ModelElement;
import com.code.metadata.sm.ServiceMeta;
import com.code.metadata.sm.ServicePublication;
import com.code.metaservice.aimodel.IScriptInfoService;
import com.code.metaservice.aimodel.IScriptLogService;
import com.code.metaservice.business.directory.BusiClassifyService;
import com.code.metaservice.business.directory.BusiDirService;
import com.code.metaservice.sm.IServiceMetaService;
import com.code.metaservice.sm.IServicePublicationService;
import com.dragoninfo.dfw.entity.TSysAuthObj;
import com.dragoninfo.dfw.service.SysAuthObjService;
import com.dragonsoft.cicada.datacenter.ServicePublishCenterClient;
import com.dragonsoft.cicada.datacenter.modules.modeling.aimodeling.client.IJypyterCodeClient;
import com.dragonsoft.cicada.datacenter.modules.modeling.aimodeling.service.AiDataModelingService;
import com.dragonsoft.cicada.datacenter.modules.modeling.aimodeling.vo.*;
import com.dragonsoft.cicada.datacenter.modules.modeling.util.CronExpression;
import com.dragonsoft.cicada.datacenter.modules.supermarkmodel.service.IMyModelService;
import com.fw.service.BaseService;
import com.fw.service.annotation.Service;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.ParseException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.Comparator.comparing;

@Service
@Slf4j
public class AiDataModelingServiceImpl extends BaseService implements AiDataModelingService {

    @Autowired
    private BusiClassifyService busiClassifyService;

    @Autowired
    private BusiDirService busiDirService;

    @Autowired
    private IJypyterCodeClient jypyterCodeClient;

    @Autowired
    private IScriptLogService scriptLogService;

    @Autowired
    private IScriptInfoService scriptInfoService;

    @Autowired
    private SysAuthObjService sysAuthObjService;

    @Resource
    private DataSource dataSource;

    @Autowired
    private IMyModelService myModelServiceImpl;

    @Autowired
    private IServiceMetaService serviceMetaService;

    @Autowired
    private IServicePublicationService servicePublicationService;

    @Resource
    private ServicePublishCenterClient servicePublishCenterClient;

    private final static String HAND = "hand";

    @Override
    public List<ModelTreeResult> getAllAiModelTree(String userId, String listType) {
        return getTransDirRootList(userId,listType, false);
    }

    @Override
    public List<ModelTreeResult> getAllAiModelWithChildren(String userId, String listType) {
        return getTransDirRootList(userId,listType, true);
    }

    @Override
    public String addAiModelDir(String parentClassifyId, String classifyName, String dirType, String userId) {
        if (busiClassifyService.checkClassifyIsExistByUserId(parentClassifyId, classifyName, EnumBusiType.valueOf(dirType), userId)) {
            throw new RuntimeException("该目录已存在，请重新命名！");
        }
        BaseBusiClassify bsClassify = new BaseBusiClassify();
        bsClassify.setCode(dirType);
        bsClassify.setName(classifyName);
        bsClassify.setOperateUserId(userId);
        if (StringUtils.isNotBlank(parentClassifyId)) {
            long startTime = System.currentTimeMillis();
            BaseBusiClassify parentBc = (BaseBusiClassify) this.baseDao.load(BaseBusiClassify.class, parentClassifyId);
            long endTime = System.currentTimeMillis();
            log.info("新建目录耗时：" + (endTime - startTime));
            bsClassify.setParentBc(parentBc);
        }
        BaseBusiDir dir = busiDirService.findEasyBusiDirBy(EnumBusiType.valueOf(dirType));

        bsClassify.setBusiDir(dir);

        this.baseDao.save(bsClassify);
        return bsClassify.getId();
    }

    @Override
    public void updateAiModelDirName(String classifyId, String newClassifyName, String userId) {
        BaseBusiClassify bc = busiClassifyService.findBusiClassifyBy(classifyId);
        String parentBcId = null;
        if (bc.getParentBc() != null) {
            parentBcId = bc.getParentBc().getId();
        }
        if (busiClassifyService.checkClassifyIsExist(parentBcId, newClassifyName, EnumBusiType.TRANS_AI_DIR_MF)) {
            throw new RuntimeException("该目录已存在，请重新命名！");
        }
        bc.setName(newClassifyName);
        this.baseDao.update(bc);
    }

    @Override
    public void moveAiModelDir(String currentClassifyId, String newParentClassifyId) {
        BaseBusiClassify curryClassify = busiClassifyService.findBusiClassifyBy(currentClassifyId);
        BaseBusiClassify newParentClassify = busiClassifyService.findBusiClassifyBy(newParentClassifyId);

        if (busiClassifyService.checkClassifyIsExist(newParentClassifyId, curryClassify.getName(),EnumBusiType.TRANS_AI_DIR_MF)) {
            throw new RuntimeException("该目录已存在！");
        }
        curryClassify.setParentBc(newParentClassify);
        curryClassify.setBusiDir(newParentClassify.getBusiDir());
        this.baseDao.update(curryClassify);
    }

    @Override
    public void deleteAiModelDir(String classifyId) {
        BaseBusiClassify curryClassify = busiClassifyService.findBusiClassifyBy(classifyId);
        Set<BaseBusiClassify> busiClassifies = curryClassify.getBusiClassifies();
        List<String> allChildDirId = new ArrayList<>();
        if (busiClassifies.size() > 0){
            allChildDirId = getAllChildDirId(busiClassifies);
        }
        allChildDirId.add(classifyId);

        // 删除目录下所有模型
        String sql = "select * from t_md_classify_element where busi_classify_id in (:allChildDirId)";
        List<Map<String,String>> elements = this.baseDao.sqlQueryForList(sql, this.addParam("allChildDirId", allChildDirId).param());
        List<String> elementIds = new ArrayList<>();
        for (Map<String, String> element : elements) {
            String element_id = element.get("element_id");
            elementIds.add(element_id);
        }
        sql = " delete from t_md_classify_element where busi_classify_id in (:allChildDirId) ";
        this.baseDao.executeSqlUpdate(sql,this.addParam("allChildDirId",allChildDirId).param());

        String hql = "delete from ScriptInfo where id in (:elementIds) ";
        sql = "delete from BaseBusiClassify where id in (:allChildDirId) ";
        this.baseDao.executeUpdate(sql,this.addParam("allChildDirId",allChildDirId).param());
        this.baseDao.executeUpdate(hql,this.addParam("elementIds",elementIds).param());

        //删除模型市场数据
        myModelServiceImpl.deleteMarkModelBatchByTransIdList(elementIds);

        //删除发布的服务
        //删除对应生成的api服务
        if (elementIds.size() > 0){
            deleteAiServiceByScriptId(elementIds);
        }
    }

    @Override
    public String newAiModel(AiModelVo aiModelVo) {
        //判断该目录下是否重名
        String sql = "select element_id from t_md_classify_element where busi_classify_id = :dirId";
        List<Map<String,String>> mapList = this.baseDao.sqlQueryForList(sql, this.addParam("dirId", aiModelVo.getDirId()).param());
        if (mapList.size() > 0){
            List<String> allAiModelId = new ArrayList<>();
            for (Map<String, String> map : mapList) {
                allAiModelId.add(map.get("element_id"));
            }
            sql = " select * from t_script_info where name = :name and id in (:allAiModelId)" ;
            Map<String,Object> map1 = new HashMap<>();
            map1.put("name",aiModelVo.getNewModelName());
            map1.put("allAiModelId",allAiModelId);
            List list = this.baseDao.sqlQueryForList(sql, map1);
            if (list.size() > 0){
                throw new RuntimeException("该目录下已有同名模型，请重新命名！");
            }
        }
        String scriptId = this.jypyterCodeClient.initBuild(aiModelVo.getNewModelName(), aiModelVo.getDescription());
        if (StrUtil.isNotEmpty(scriptId) && StrUtil.isNotEmpty(aiModelVo.getDirId())){
            //往目录中添加
            ScriptInfo scriptInfo = scriptInfoService.getScriptInfoByScriptId(scriptId);
            scriptInfo.setOperateTime(DateUtil.format(LocalDateTime.now(), "yyyy-MM-dd HH:mm:ss"));
            scriptInfo.setOperateUserId(aiModelVo.getUserId());
            Map<String,Object> map = new HashMap<>();
            map.put("elementId",scriptId);
            map.put("classifyId",aiModelVo.getDirId());
            sql = "insert into t_md_classify_element(element_id,busi_classify_id) values(:elementId,:classifyId) ";
            this.baseDao.executeSqlUpdate(sql,map);
            this.baseDao.saveOrUpdate(scriptInfo);

            //加入定时执行入库

            try {
                String taskId = StringUtils.uuid();
                Db.use(dataSource).insert(
                        Entity.create("t_trans_task")
                                .set("id", taskId)
                                .set("trans_id", scriptInfo.getId())
                                .set("trans_name", scriptInfo.getName())
                                .set("run_param", null)
                                .set("sub_task_id", null)

                );
                String scheduleId = StringUtils.uuid();
                Db.use(dataSource).insert(
                        Entity.create("t_trans_schedule")
                                .set("id", scheduleId)
                                .set("trans_id", scriptInfo.getId())
                                .set("task_id", taskId)
                                .set("schedule_type", "hand")
                                .set("cron_expression", null)
                                .set("schedule_declare", null)
                                .set("schedule_behavior", "1")
                                .set("trigger_status", 0)
                                .set("trigger_last_time", 0)
                                .set("trigger_next_time", 0)
                );
            } catch (SQLException e) {
             log.error(e.getMessage(),e);
            }

        }
        return scriptId;
    }

    @Override
    public String getStartAiModelUrl(String scriptId) {
        return this.jypyterCodeClient.startScript(scriptId);
    }

    @Override
    public void saveScript(String scriptId) {
        this.jypyterCodeClient.saveScript(scriptId);
        ScriptInfo scriptInfo = scriptInfoService.getScriptInfoByScriptId(scriptId);
        scriptInfo.setOperateTime(DateUtil.format(LocalDateTime.now(), "yyyy-MM-dd HH:mm:ss"));
        this.baseDao.saveOrUpdate(scriptInfo);
    }

    @Override
    public String runScript(String scriptId) {
        return this.jypyterCodeClient.runScript(scriptId);
    }

    @Override
    public List<EvaluateRstItem> getEvaluateRst(String logId) {
        List<EvaluateRstItem> evaluateRstItemsC = new ArrayList<>();
        List<EvaluateRstItem> evaluateRstItems = this.jypyterCodeClient.queryEvaluateRst(logId);
        for (EvaluateRstItem evaluateRstItem : evaluateRstItems) {
            if ("trainCount".equals(evaluateRstItem.getName())){
                evaluateRstItem.setName("训练样本量");
                evaluateRstItemsC.add(evaluateRstItem);
                continue;
            }
            if ("tetsCount".equals(evaluateRstItem.getName())){
                evaluateRstItem.setName("测试样本量");
                evaluateRstItemsC.add(evaluateRstItem);
                continue;
            }
        }
        for (EvaluateRstItem evaluateRstItem : evaluateRstItems) {
            if (!"trainCount".equals(evaluateRstItem.getCode())&&!"tetsCount".equals(evaluateRstItem.getCode())){
                evaluateRstItemsC.add(evaluateRstItem);
            }
        }
        return evaluateRstItemsC;
    }

    @Override
    public PageInfo getAiModelRunHistory(AiHistoryQueryVo aiHistoryQueryVo) {

        PageInfo logs = scriptLogService.getScriptLogsByScriptIdAndRunStateAndTime(
                aiHistoryQueryVo.getScriptId(),
                aiHistoryQueryVo.getRunState(),
                aiHistoryQueryVo.getStartTime(),
                aiHistoryQueryVo.getEndTime(),
                aiHistoryQueryVo.getPageNum(),
                aiHistoryQueryVo.getPageSize());
        List<Map<String,Object>> dataList = logs.getDataList();
        List<AiRunHistoryResult> results = new ArrayList<>();
        for (Map<String, Object> map : dataList) {
            AiRunHistoryResult result = new AiRunHistoryResult();
            result.setLogId((String) map.get("id"));
            result.setAiModelName((String) map.get("name"));
            Timestamp startTime = (Timestamp) map.get("create_time");
            Timestamp endTime = (Timestamp) map.get("update_time");
            result.setStartTime(startTime);
            result.setEndTime(endTime);

            double time = 0.0;
            if (null != endTime && null != startTime)
                time = ((double) endTime.getTime() - (double) startTime.getTime()) / 1000;
            result.setRunningTime(time+"秒");
            String statusCode = (String) map.get("execute_status_code");
            if ("Error".equalsIgnoreCase(statusCode)){
                //运行异常
                List<ScriptLogDetail> detail = scriptLogService.getScriptLogDetailByLogId(result.getLogId());
                if (detail.size() > 0){
                    StringBuilder sb = new StringBuilder();
                    for (ScriptLogDetail scriptLogDetail : detail) {
                        sb.append(scriptLogDetail.getLogContent() + "\n");
                    }
                    result.setErrMsg(sb.toString());
                }else {
                    result.setErrMsg(null);
                }
            }
            if ("Init".equalsIgnoreCase(statusCode)){
                statusCode = "Running";
            }
            result.setRunState(statusCode);
            results.add(result);
        }
        logs.setDataList(results);
        return logs;
    }

    @Override
    public void deleteLogById(String logId) {
        if (StrUtil.isNotEmpty(logId)){
            scriptLogService.deleteLogById(logId);
        }
    }

    @Override
    public List<LogDetailResult> getLogDetailByLogId(String logId) {
        List<ScriptLogDetail> scriptLogDetails = scriptLogService.getScriptLogDetailByLogId(logId);
        ScriptLog scriptLog = scriptLogService.getScriptLogById(logId);
        List<LogDetailResult> results = new ArrayList<>();
        for (ScriptLogDetail scriptLogDetail : scriptLogDetails) {
            LogDetailResult result = new LogDetailResult();
            result.setLogId(logId);
            result.setId(scriptLogDetail.getId());
            result.setDesc(scriptLogDetail.getLogType());
            result.setCommitTime(scriptLog == null ? null : scriptLog.getCreateTime());
            result.setFinishTime(scriptLogDetail.getCreateTime());
            result.setRunState(scriptLog.getExecuteStatusCode());
            result.setDetail(scriptLogDetail.getLogContent());
            results.add(result);
        }
        return results;
    }

    @Override
    public PageInfo getAiModelPage(AiModelPageVo aiModelPageVo,String userId) {
        String hql = " from BusiClassify where id = :classifyId ";
        BusiClassify busiClassify = (BusiClassify) this.baseDao.queryForObject(hql, this.addParam("classifyId", aiModelPageVo.getClassifyId()).param());
        if (busiClassify == null){
            return new PageInfo();
        }
        List<String> allChildrenClassifyIds = getAllChildrenClassifyIds(busiClassify);
        String sql = "select t1.*, t2.schedule_type from t_md_classify_element t1 left join t_trans_schedule t2 on t1.element_id = t2.trans_id  where busi_classify_id in (:allChildrenClassifyIds)";
        List<Map<String,String>> elements = this.baseDao.sqlQueryForList(sql, this.addParam("allChildrenClassifyIds", allChildrenClassifyIds).param());
        List<String> elementIds = new ArrayList<>();
        for (Map<String, String> element : elements) {
            String element_id = element.get("element_id");
            elementIds.add(element_id);
        }
        PageInfo page = scriptInfoService.getScriptPageByCondition(aiModelPageVo.getModelName(), elementIds, aiModelPageVo.getPageNum(), aiModelPageVo.getPageSize());
        page.setDataList(setAllDirPath(page.getDataList(),elements,userId));

        //List list = setAllDirPath(pageInfo1.getDataList(), elements);
        //pageInfo1.setDataList(list);
        //System.out.println(allChildrenClassifyIds);
        return page;
    }

    @Override
    public EntInputInfo getEvaRstDataSet(String logId) {
        return jypyterCodeClient.queryEntInputInfo(logId);
    }

    @Override
    public void renameAiModel(String scriptId, String newModelName) {
        //查找该目录下是否已有此名
        String sql = " select busi_classify_id from t_md_classify_element where element_id = :scriptId ";
        String dirId = this.baseDao.sqlQueryForValue(sql, this.addParam("scriptId", scriptId).param());
        sql = "select element_id from t_md_classify_element where busi_classify_id = :dirId";
        List<Map<String,String>> mapList = this.baseDao.sqlQueryForList(sql, this.addParam("dirId", dirId).param());
        if (mapList.size() > 0){
            List<String> allAiModelId = new ArrayList<>();
            for (Map<String, String> map : mapList) {
                if (!scriptId.equals(map.get("element_id"))){
                    allAiModelId.add(map.get("element_id"));
                }
            }
            if (allAiModelId.size() > 0){
                sql = " select * from t_script_info where name = :name and id in (:allAiModelId)" ;
                Map<String,Object> map1 = new HashMap<>();
                map1.put("name",newModelName);
                map1.put("allAiModelId",allAiModelId);
                List list = this.baseDao.sqlQueryForList(sql, map1);
                if (list.size() > 0){
                    throw new RuntimeException("该目录下已有同名模型，请重新命名！");
                }
            }
        }
        ScriptInfo scriptInfo = scriptInfoService.getScriptInfoByScriptId(scriptId);
        scriptInfo.setName(newModelName);
        this.baseDao.saveOrUpdate(scriptInfo);
    }

    @Override
    public void copyAiModel(String scriptId, String classifyId,String name) {
        String sql = "select element_id from t_md_classify_element where busi_classify_id = :dirId";
        ScriptInfo scriptInfo = this.scriptInfoService.getScriptInfoByScriptId(scriptId);
        List<Map<String,String>> mapList = this.baseDao.sqlQueryForList(sql, this.addParam("dirId", classifyId).param());
        if (mapList.size() > 0){
            List<String> allAiModelId = new ArrayList<>();
            for (Map<String, String> map : mapList) {
                allAiModelId.add(map.get("element_id"));
            }
            if (allAiModelId.size() > 0){
                sql = " select * from t_script_info where name = :name and id in (:allAiModelId)" ;
                Map<String,Object> map1 = new HashMap<>();
                map1.put("name",name);
                map1.put("allAiModelId",allAiModelId);
                List list = this.baseDao.sqlQueryForList(sql, map1);
                if (list.size() > 0){
                    throw new RuntimeException("该名称已存在！");
                }
            }

        }
        ScriptInfo newScript = new ScriptInfo();
        newScript.setName(name);
        newScript.setOperateTime(DateUtil.format(LocalDateTime.now(), "yyyy-MM-dd HH:mm:ss"));
        //new Timestamp(new Date().getTime())
        Date date = new Date();
        Timestamp timestamp = new Timestamp(date.getTime());
        newScript.setCreateTime(timestamp);
        newScript.setUpdateTime(timestamp);
        newScript.setScript(scriptInfo.getScript());
        newScript.setModelDesc(scriptInfo.getModelDesc());
        newScript.setSrcTemplateId(scriptInfo.getSrcTemplateId());
        newScript.setOperateUserId(scriptInfo.getOperateUserId());
        newScript.setType(scriptInfo.getType());
        newScript.setCode(scriptInfo.getCode());
        this.baseDao.saveOrUpdate(newScript);
        //存进目录
        if (StrUtil.isNotEmpty(newScript.getId()) && StrUtil.isNotEmpty(classifyId)){
            //往目录中添加
            Map<String,Object> map = new HashMap<>();
            map.put("elementId",newScript.getId());
            map.put("classifyId",classifyId);
            sql = "insert into t_md_classify_element(element_id,busi_classify_id) values(:elementId,:classifyId) ";
            this.baseDao.executeSqlUpdate(sql,map);
        }

        try {
            String taskId = StringUtils.uuid();
            Db.use(dataSource).insert(
                    Entity.create("t_trans_task")
                            .set("id", taskId)
                            .set("trans_id", newScript.getId())
                            .set("trans_name", newScript.getName())
                            .set("run_param", null)
                            .set("sub_task_id", null)

            );
            String scheduleId = StringUtils.uuid();
            Db.use(dataSource).insert(
                    Entity.create("t_trans_schedule")
                            .set("id", scheduleId)
                            .set("trans_id", newScript.getId())
                            .set("task_id", taskId)
                            .set("schedule_type", "hand")
                            .set("cron_expression", null)
                            .set("schedule_declare", null)
                            .set("schedule_behavior", "1")
                            .set("trigger_status", 0)
                            .set("trigger_last_time", 0)
                            .set("trigger_next_time", 0)
            );
        } catch (SQLException e) {
         log.error(e.getMessage(),e);
        }
    }

    @Override
    public void moveAiModel(String scriptId, String classifyId) {
        String sql = "select element_id from t_md_classify_element where busi_classify_id = :dirId";
        ScriptInfo scriptInfo = this.scriptInfoService.getScriptInfoByScriptId(scriptId);
        List<Map<String,String>> mapList = this.baseDao.sqlQueryForList(sql, this.addParam("dirId", classifyId).param());
        if (mapList.size() > 0){
            List<String> allAiModelId = new ArrayList<>();
            for (Map<String, String> map : mapList) {
                allAiModelId.add(map.get("element_id"));
            }
            sql = " select * from t_script_info where name = :name and id in (:allAiModelId)" ;
            Map<String,Object> map1 = new HashMap<>();
            map1.put("name",scriptInfo.getName());
            map1.put("allAiModelId",allAiModelId);
            List list = this.baseDao.sqlQueryForList(sql, map1);
            if (list.size() > 0){
                throw new RuntimeException("该名称已存在！");
            }
        }
        //修改
        if (StrUtil.isNotEmpty(scriptInfo.getId()) && StrUtil.isNotEmpty(classifyId)){
            //往目录中添加
            Map<String,Object> map = new HashMap<>();
            map.put("elementId",scriptId);
            map.put("classifyId",classifyId);
            sql = " update t_md_classify_element set busi_classify_id = :classifyId where element_id = :elementId ";
            this.baseDao.executeSqlUpdate(sql,map);
        }
    }

    @Override
    public void deleteAiModel(String scriptId) {
        if (StrUtil.isNotEmpty(scriptId)){
            String hql = " delete from ScriptInfo where id = :scriptId ";
            this.baseDao.executeUpdate(hql,this.addParam("scriptId",scriptId).param());
            //删除目录关系
            String sql = "delete from t_md_classify_element where element_id = :scriptId ";
            this.baseDao.executeSqlUpdate(sql,this.addParam("scriptId",scriptId).param());
            //删除模型市场数据
            myModelServiceImpl.deleteMarkModelByTransId(scriptId);

            //删除对应生成的api服务
            deleteAiServiceByScriptId(Arrays.asList(scriptId));
        }
    }

    @Override
    public void editAiModel(AiModelBasicVo basicVo) {
        //查找该目录下是否已有此名
        String sql = "select element_id from t_md_classify_element where busi_classify_id = :dirId";
        List<Map<String,String>> mapList = this.baseDao.sqlQueryForList(sql, this.addParam("dirId", basicVo.getClassifyId()).param());
        if (mapList.size() > 0){
            List<String> allAiModelId = new ArrayList<>();
            for (Map<String, String> map : mapList) {
                if (!basicVo.getScriptId().equals(map.get("element_id"))){
                    allAiModelId.add(map.get("element_id"));
                }
            }
            if (allAiModelId.size() > 0){
                sql = " select * from t_script_info where name = :name and id in (:allAiModelId)" ;
                Map<String,Object> map1 = new HashMap<>();
                map1.put("name",basicVo.getModelName());
                map1.put("allAiModelId",allAiModelId);
                List list = this.baseDao.sqlQueryForList(sql, map1);
                if (list.size() > 0){
                    throw new RuntimeException("该目录下已有同名模型，请重新命名！");
                }
            }
        }
        ScriptInfo scriptInfo = scriptInfoService.getScriptInfoByScriptId(basicVo.getScriptId());
        scriptInfo.setName(basicVo.getModelName());
        scriptInfo.setModelDesc(basicVo.getDesc());
        scriptInfo.setOperateTime(DateUtil.format(LocalDateTime.now(), "yyyy-MM-dd HH:mm:ss"));
        //目录存放
        if (StrUtil.isNotEmpty(scriptInfo.getId()) && StrUtil.isNotEmpty(basicVo.getClassifyId())){
            //往目录中添加
            Map<String,Object> map = new HashMap<>();
            map.put("elementId",basicVo.getScriptId());
            map.put("classifyId",basicVo.getClassifyId());
            sql = " update t_md_classify_element set busi_classify_id = :classifyId where element_id = :elementId ";
            this.baseDao.executeSqlUpdate(sql,map);
            this.baseDao.saveOrUpdate(scriptInfo);
        }
    }

    @Override
    public void updateAiLog(String logId, String scriptId) {


        if (scriptLogService.getScriptLogById(logId) == null){
            ScriptLog scriptLog = new ScriptLog();
            scriptLog.setId(logId);
            scriptLog.setCreateTime(new Timestamp(new Date().getTime()));
            scriptLog.setScriptId(scriptId);
            scriptLog.setExecuteStatusCode("Finish");
            scriptLog.setExecuteStatusName("运行结束");
            scriptLog.setUpdateTime(new Timestamp(new Date().getTime()));
            this.baseDao.saveOrUpdate(scriptLog);
        }
    }

    @Override
    public Map<String, Object> getAiModelDetail(String scriptId) {
        ScriptInfo scriptInfo = scriptInfoService.getScriptInfoByScriptId(scriptId);
        ScriptLog newestLogByScriptId = scriptLogService.getNewestLogByScriptId(scriptId);
        String logId = newestLogByScriptId != null ? newestLogByScriptId.getId() : "";
        String classifyId = this.baseDao.sqlQueryForValue("select busi_classify_id from t_md_classify_element where element_id = :scriptId", this.addParam("scriptId", scriptId).param());
        Map<String,Object> map = new HashMap<>();
        map.put("aiModelName",scriptInfo.getName());
        map.put("aiModelDesc",scriptInfo.getModelDesc());
        map.put("classifyId",classifyId);
        map.put("logId",logId);
        return map;
    }

    @Override
    public String getNewestLogIdByScriptId(String scriptId) {
        ScriptLog logByScriptId = scriptLogService.getNewestLogByScriptId(scriptId);
        return  logByScriptId != null ? logByScriptId.getId() : "";
    }

    @Override
    public void starTaskForSchedul(String scriptId) {
        String sql = "select subtrans_id from t_trans_subtrans_relation  where trans_id = '" + scriptId +"'";
        List<String> list = this.baseDao.sqlQueryForList(sql);
    }

    @Override
    public void saveScheduledTask(TransScheduleVo transScheduleVo) {

        if (transScheduleVo.getScheduleType().equals("hand")) {
            transScheduleVo.setTriggerStatus(0);
            transScheduleVo.setTriggerLastTime(0L);
            transScheduleVo.setTriggerNextTime(0L);
        }else {
            Assert.hasText(transScheduleVo.getCron(), "cron表达式不能为空!");
            CronExpression cronExpression = null;
            try {
                cronExpression = new CronExpression(transScheduleVo.getCron());
            } catch (ParseException e) {
             log.error(e.getMessage(),e);
                Assert.fail("Cron表达式解析异常");
            }
            transScheduleVo.setTriggerStatus(1);
            transScheduleVo.setTriggerLastTime(0L);
            transScheduleVo.setTriggerNextTime(cronExpression.getNextValidTimeAfter(new Date(System.currentTimeMillis() + 5000)).getTime());
        }

        saveSchedule(transScheduleVo); //保存定时对象
        if (transScheduleVo.getStartPrograms().size() != 0) {
//            if(Objects.equals(transScheduleVo.getJobType(), "already")) {  //如果是加入到已创建队列里， 则先删除旧的所有节点
//                deleteTransRelation(transScheduleVo.getTransId());
//            }
            List startPrograms = transScheduleVo.getStartPrograms();
            for (Object obj : startPrograms) {
                String subtransId = ((HashMap) obj).get("subtransId").toString();
                int subtransDelayTime = ((HashMap) obj).get("subtransDelayTime") == null ? 10 : Integer.parseInt(((HashMap) obj).get("subtransDelayTime").toString());
                boolean subtransIsenforce = (Boolean) ((HashMap) obj).get("subtransIsenforce");
                updateTransSubTransRelation(transScheduleVo.getTransId(), subtransId, subtransDelayTime, subtransIsenforce);
            }
        }
    }

    private void updateTransSubTransRelation(String transId, String subtransId, int subtransDelayTime, boolean subtransIsenforce) {
        boolean existSubRelation = isExistSubRelation(transId, subtransId);
        int subtransIsenforced = subtransIsenforce ? 1 : 0;
        String sql = "";
        if (existSubRelation) {
            sql = "update t_trans_subtrans_relation set subtrans_delay_time='" + subtransDelayTime + "',subtrans_isenforce='" + subtransIsenforced + "' where trans_id='" + transId + "' and subtrans_id='" + subtransId + "'";
        } else {
            String id = UUID.randomUUID().toString().replaceAll("-", "");
            sql = "insert into t_trans_subtrans_relation values('" + id + "','" + subtransDelayTime + "',0,'" + subtransId + "','" + subtransIsenforced + "','" + transId + "')";
        }

        this.baseDao.executeSqlUpdate(sql);
    }


    private void deleteAiServiceByScriptId(List<String> scriptIds){
        String sql = "select id from t_script_log where script_id in (:scriptIds) ";
        List<Map<String,String>> list = this.baseDao.sqlQueryForList(sql, this.addParam("scriptIds", scriptIds).param());


        if (list.size() > 0){
            for (Map<String,String> map : list) {
                List<ServicePublication> servicePublications = servicePublicationService.queryBySourceId(map.get("id"));
                if (servicePublications.size() > 0){
                    for (ServicePublication servicePublication : servicePublications) {
                        Set<ServiceMeta> serviceMetas = servicePublication.getServiceMetas();
                        for (ServiceMeta serviceMeta : serviceMetas) {
                            servicePublishCenterClient.uninstall(serviceMeta.getId());
                        }
                    }
                }
            }
        }
    }


    private void deleteTransRelation(String transId) {
        String deleteSql = " DELETE FROM t_trans_subtrans_relation WHERE trans_id = :transId";
        this.baseDao.executeSqlUpdate(deleteSql, addParam("transId", transId).param());
    }

    private void saveSchedule(TransScheduleVo transScheduleVo) {
        try {
            Db.use(dataSource).update(
                    Entity.create()
                            .set("schedule_type", transScheduleVo.getScheduleType())
                            .set("cron_expression", transScheduleVo.getCron())
                            .set("schedule_declare", transScheduleVo.getDeclare())
                            .set("trigger_status", transScheduleVo.getTriggerStatus())
                            .set("trigger_last_time", transScheduleVo.getTriggerLastTime())
                            .set("trigger_next_time", transScheduleVo.getTriggerNextTime())
                    ,
                    Entity.create("t_trans_schedule")
                            .set("trans_id", transScheduleVo.getTransId())
            );
        } catch (SQLException e) {
         log.error(e.getMessage(),e);
        }

    }

    private int getAlreayData(String transId) {
        StringBuffer sql = new StringBuffer();
        sql.append(" select * from t_trans_schedule where trans_id = :tranId");
        List transData = this.baseDao.sqlQueryForList(sql.toString(), addParam("transId", transId).param());
        return transData.size();
    }

    private boolean isExistSubRelation(String transId, String subtransId) {
        String sql = "select count(1) as num from t_trans_subtrans_relation where trans_id='" + transId + "' and subtrans_id='" + subtransId + "'";
        List list = this.baseDao.sqlQueryForList(sql);
        return ((HashMap) list.get(0)).get("num").toString().equals("0") ? false : true;
    }

    private List setAllDirPath(List<ScriptInfo> scriptInfos, List<Map<String,String>> elements,String userId){
        Map<String,String> map = new HashMap<>();
        map.put("id",userId);
        TSysAuthObj tSysAuthObj = (TSysAuthObj) sysAuthObjService.query(map);

        List<AiModelResult> aiModelResults = new ArrayList<>();
        for (ScriptInfo scriptInfo : scriptInfos) {
            AiModelResult aiModelResult = new AiModelResult();
            aiModelResult.setId(scriptInfo.getId());
            aiModelResult.setName(scriptInfo.getName());
            aiModelResult.setTaskGroup("AI");
            aiModelResult.setReleaseTime(StrUtil.isNotEmpty(scriptInfo.getOperateTime()) ? scriptInfo.getOperateTime():scriptInfo.getUpdateTime().toString());
            aiModelResult.setUsername(tSysAuthObj.getObjName());
            aiModelResult.setDescription(scriptInfo.getModelDesc());
            ScriptLog log = scriptLogService.getNewestLogByScriptId(scriptInfo.getId());
            if (log != null){
                aiModelResult.setEndTime(log.getUpdateTime().toString());
                String executeStatusCode = log.getExecuteStatusCode();
                if ("Init".equalsIgnoreCase(executeStatusCode)){
                    executeStatusCode = "Running";
                }
                aiModelResult.setExecuteStatus(executeStatusCode);
                aiModelResult.setNewestLogId(log.getId());
            }
            //只有手工调度
            for (Map<String, String> element : elements) {
                String elementId = element.get("element_id");
                if (Objects.equals(elementId,scriptInfo.getId())){
                    String busiClassifyId = element.get("busi_classify_id");
                    BaseBusiClassify busiClassify = busiClassifyService.findBusiClassifyBy(busiClassifyId);
                    aiModelResult.setClassifyName(busiClassify.getName());
                    aiModelResult.setParentId(busiClassifyId);
                    String allPathString = getAllPathString(busiClassify);
                    aiModelResult.setPath(allPathString);
                    aiModelResult.setScheduleType(Objects.equals(element.get("schedule_type"),"hand")? "手工调度":"自动调度");
                    break;
                }
            }
            aiModelResults.add(aiModelResult);
        }
        return aiModelResults;
    }

    private String getAllPathString(BaseBusiClassify baseBusiClassify){
        StringBuilder sb = new StringBuilder();
        sb.append(baseBusiClassify.getName());
        BaseBusiClassify parentBc = baseBusiClassify.getParentBc();
        if (parentBc != null){
            sb.insert(0,getAllPathString(parentBc)+"/");
        }
        return sb.toString();
    }

    private List<String> getAllChildrenClassifyIds(BusiClassify busiClassify){
        List<String> list = new ArrayList<>();
        list.add(busiClassify.getId());
        Set<BusiClassify> busiClassifies = busiClassify.getBusiClassifies();
        if (busiClassifies.size() > 0){
            for (BusiClassify classify : busiClassifies) {
                List<String> allChildrenClassifyIds = getAllChildrenClassifyIds(classify);
                list.addAll(allChildrenClassifyIds);
            }
        }
        return list;
    }

    private List<String> getAllChildDirId(Set<BaseBusiClassify> busiClassifies){
        List<String> allClassifyIds = new ArrayList<>();
        for (BaseBusiClassify busiClassify : busiClassifies) {
            allClassifyIds.add(busiClassify.getId());
            if (busiClassify.getBusiClassifies().size() > 0){
                allClassifyIds.addAll(getAllChildDirId(busiClassify.getBusiClassifies()));
            }
        }
        return allClassifyIds;
    }


    private List<ModelTreeResult> getTransDirRootList( String userId,String listType, boolean withNodes) {
        BusiDir busiDir = (BusiDir) this.baseDao.queryForObject("from BusiDir where busiDirType = 'TRANS_AI_DIR_MF'");
        String hql = " from BusiClassify bc where bc.busiDir.id = :busiDirId and bc.operateUserId = :userId and" +
                " (bc.parentBc.id is null or bc.parentBc.id = :parentId) ";
        if ("time".equalsIgnoreCase(listType) || StrUtil.isEmpty(listType)){
            hql += " order by bc.operateTime asc ";
        }else if ("char".equalsIgnoreCase(listType)){
            hql += " order by bc.name asc ";
        }
        Map<String,Object> map = new HashMap<>();
        map.put("busiDirId",busiDir.getId());
        map.put("userId",userId);
        map.put("parentId",busiDir.getId());
        List<BusiClassify> busiClassifies = this.baseDao.queryForList(hql, map);
        if(withNodes) return setResultNodesWithNodes(busiClassifies,listType);
        return setResultNodes(busiClassifies,listType);
    }

    private List<ModelTreeResult> setResultNodesWithNodes(List<BusiClassify> busiClassifies, String listType) {
        List<ModelTreeResult> modelTreeResults = new ArrayList<>();
        for (BusiClassify busiClassify : busiClassifies) {
            ModelTreeResult modelTreeResult = new ModelTreeResult();
            modelTreeResult.setDirType(busiClassify.getType());
            modelTreeResult.setId(busiClassify.getId());
            modelTreeResult.setOperateTime(busiClassify.getOperateTime());
            modelTreeResult.setName(busiClassify.getName());
            if (busiClassify.getParentBc() != null){
                modelTreeResult.setParentId(busiClassify.getParentBc().getId());
            }
            if (busiClassify.getBusiClassifies().size() <= 0){
                modelTreeResult.setIsParent(false);
                modelTreeResult.setChildren(null);
            }else {
                modelTreeResult.setIsParent(true);
                Set<BusiClassify> classifies = busiClassify.getBusiClassifies();
                List<BusiClassify> collect;
                if ("char".equalsIgnoreCase(listType)){
                    collect = classifies.stream().sorted(comparing(ModelElement::getName)).collect(Collectors.toList());
                }else {
                    collect = classifies.stream().sorted(comparing(ModelElement::getOperateTime)).collect(Collectors.toList());
                }
                modelTreeResult.setChildren(setResultNodesWithNodes(collect,listType));
            }
            if(busiClassify.getElements().size() > 0) {
                if(modelTreeResult.getChildren() != null)
                modelTreeResult.getChildren().addAll(getElements(busiClassify.getElements()));
                else {
                    modelTreeResult.setChildren(getElements(busiClassify.getElements()));
                }
            }
            modelTreeResults.add(modelTreeResult);
        }
        return modelTreeResults;
    }

    private List<ModelTreeResult> setResultNodes(List<BusiClassify> busiClassifies, String listType){
        List<ModelTreeResult> modelTreeResults = new ArrayList<>();
        for (BusiClassify busiClassify : busiClassifies) {
            ModelTreeResult modelTreeResult = new ModelTreeResult();
            modelTreeResult.setDirType(busiClassify.getType());
            modelTreeResult.setId(busiClassify.getId());
            modelTreeResult.setOperateTime(busiClassify.getOperateTime());
            modelTreeResult.setName(busiClassify.getName());
            if (busiClassify.getParentBc() != null){
                modelTreeResult.setParentId(busiClassify.getParentBc().getId());
            }
            if (busiClassify.getBusiClassifies().size() <= 0){
                modelTreeResult.setIsParent(false);
                modelTreeResult.setChildren(null);
            }else if(busiClassify.getBusiClassifies().size() > 0){
                modelTreeResult.setIsParent(true);
                Set<BusiClassify> classifies = busiClassify.getBusiClassifies();
                List<BusiClassify> collect;
                if ("char".equalsIgnoreCase(listType)){
                    collect = classifies.stream().sorted(comparing(ModelElement::getName)).collect(Collectors.toList());
                }else {
                    collect = classifies.stream().sorted(comparing(ModelElement::getOperateTime)).collect(Collectors.toList());
                }
                modelTreeResult.setChildren(setResultNodes(collect,listType));
            }
            modelTreeResults.add(modelTreeResult);
        }
        return modelTreeResults;
    }

    private List<ModelTreeResult> getElements(Set<ModelElement> elements) {
        List<ModelTreeResult> modelList = new ArrayList<>();
        for (ModelElement element : elements) {
            ModelTreeResult modelTreeResult = new ModelTreeResult();
            modelTreeResult.setId(element.getId());
            modelTreeResult.setIsParent(false);
            modelTreeResult.setName(element.getName());
            modelList.add(modelTreeResult);
        }
        return modelList;
    }
    @Override
    public List getJobCreated() {
        String sql = " select * from t_trans_schedule where trigger_status = '0'";
        return this.baseDao.sqlQueryForList(sql);
    }

    @Override
    public Map getSchedulePlan(String transId) {
        StringBuffer sql = new StringBuffer();
        sql.append(" select id, cron_expression as cronExpression, ");
        sql.append(" schedule_declare as scheduleDeclare,");
        sql.append(" schedule_type as scheduleType, ");
        sql.append(" trans_id as transMetaId ");
        sql.append(" from t_trans_schedule ");
        sql.append(" where trans_id = :transId ");
        return this.baseDao.sqlQueryForMap(sql.toString(), addParam("transId", transId).param());
    }

    @Override
    public List getSubTrans(String transId) {
        String sql = "select subtrans_id,subtrans_delay_time,subtrans_isenforce from t_trans_subtrans_relation where trans_id= :transId";
        List subTrans = this.baseDao.sqlQueryForList(sql, addParam("transId", transId).param());
        return subTrans;
    }


}
