package com.dragonsoft.cicada.datacenter.modules.supermarkmodel.vo;

import lombok.Data;

@Data
public class ModelMarketQueryVo {

    //模型分类
    private String objType;
    private String appType;
    private String policeType;

    private String searchType;
    private String searchCond;

    private String sortType;//focus:关注 browse：浏览 score：评分  publishTime：发布时间
    private String sort; //asc:升序 desc：降序

    private Integer pageNum;
    private Integer pageSize;
    private String caseType;//案件类型
    private String areaType;//区域类型
    private String controlType;//打防管控类
    private String yylxSjType;//应用类型-审计
    private String mxflSjType;//模型类型-审计
    private String score;
    private String browseCount;
    private String publishTimeStart;
    private String publishTimeEnd;

}
