package com.dragonsoft.cicada.datacenter.modules.system.busmanagement.service.Impl;

import com.code.common.utils.StringUtils;
import com.code.metadata.bus.management.BusInfo;
import com.code.metadata.bus.management.enums.EnumBusVersion;
import com.code.metaservice.bus.management.IBusInfoService;
import com.code.metaservice.bus.management.IServiceInfoService;
import com.dragonsoft.cicada.datacenter.modules.system.busmanagement.service.IBusInfoManagementService;
import com.dragonsoft.cicada.datacenter.modules.system.busmanagement.vo.BusInfoVo;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/03/15
 */
@Service
public class BusInfoManagementServiceImpl implements IBusInfoManagementService {

    @Autowired
    private IBusInfoService busInfoService;

    @Autowired
    private IServiceInfoService serviceInfoService;

    @Override
    public List<BusInfoVo> queryBusInfo(String userId) {
        List<BusInfo> busInfos = busInfoService.queryBusInfo();
        List<BusInfoVo> busInfoVos = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(busInfos)) {
            for (BusInfo busInfo : busInfos) {
                BusInfoVo busInfoVo = new BusInfoVo();
                busInfoVo.setName(busInfo.getName());
                busInfoVo.setBusVersion(busInfo.getBusVersion());
                busInfoVo.setHttpUrl(busInfo.getHttpUrl());
                busInfoVo.setId(busInfo.getId());
                busInfoVos.add(busInfoVo);
            }
        }

        return busInfoVos;
    }

    @Override
    public String saveOrUpdateBusInfo(BusInfoVo busInfoVo, String userId) {
        BusInfo busInfo = new BusInfo();
        EnumBusVersion instanceByCode = EnumBusVersion.getInstanceByCode(busInfoVo.getBusVersion());
        if(StringUtils.isNotBlank(busInfoVo.getId()))
            busInfo =  busInfoService.get(BusInfo.class, busInfoVo.getId());
        busInfo.setName(instanceByCode.name);
        busInfo.setCode(instanceByCode.code);
        busInfo.setHttpUrl(busInfoVo.getHttpUrl());
        busInfo.setBusVersion(busInfoVo.getBusVersion());
        busInfo.setType(instanceByCode.code);
        busInfo.setOperateUserId(userId);
        busInfoService.saveOrUpdate(busInfo);
        return busInfo.getId();
    }
}
