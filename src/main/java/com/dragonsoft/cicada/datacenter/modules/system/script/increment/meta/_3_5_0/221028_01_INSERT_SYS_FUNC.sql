INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('publishService', '0', '发布api', 'serviceManagement', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1', '1');


INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('67e13fbcbfcf4786b9cef218577c9dd4', 'd6121bc4248e45019942e2cb78362500', 'publishService', NULL, NULL, NULL, NULL, NULL, NULL, NULL);


INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('serviceSpace', '0', '服务空间', NULL, NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1', '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('testOnline', '0', '在线测试', 'serviceSpace', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1', '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('useCaseManagement', '0', '用例管理', 'serviceSpace', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1', '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('testSingleService', '0', '开始测试', 'testOnline', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1', '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('testBatchService', '0', '批量测试', 'testOnline', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1', '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('importUseCase', '0', '导入用例', 'testOnline', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1', '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('addUseCase', '0', '添加用例', 'useCaseManagement', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1', '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('runUseCase', '0', '运行用例', 'useCaseManagement', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1', '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('editUseCase', '0', '编辑用例', 'useCaseManagement', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1', '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('deleteUseCase', '0', '删除用例', 'useCaseManagement', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1', '1');


INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('17c4725f9fcf49a8aaeb5097273eaf77', 'd6121bc4248e45019942e2cb78362500', 'serviceSpace', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('bea70fe8743440709f51475c455405aa', 'd6121bc4248e45019942e2cb78362500', 'testOnline', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('01d27489aa044680b6c164162fe68a50', 'd6121bc4248e45019942e2cb78362500', 'useCaseManagement', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8bc16e5af4a847ea8de2e31d84b93cb5', 'd6121bc4248e45019942e2cb78362500', 'testSingleService', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('0433923821424235956cead88f0c2472', 'd6121bc4248e45019942e2cb78362500', 'testBatchService', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('23176089fbab452583acfd821de1e62f', 'd6121bc4248e45019942e2cb78362500', 'importUseCase', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('5af9fdd5a379414086414faa1b0edd33', 'd6121bc4248e45019942e2cb78362500', 'addUseCase', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('62472d6315e74f29971f8373bb1e432c', 'd6121bc4248e45019942e2cb78362500', 'runUseCase', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('0f64bd1bdb81483680d05822b193db77', 'd6121bc4248e45019942e2cb78362500', 'editUseCase', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('630e05cbda5143eda04d96bcc98faf8b', 'd6121bc4248e45019942e2cb78362500', 'deleteUseCase', NULL, NULL, NULL, NULL, NULL, NULL, NULL);

