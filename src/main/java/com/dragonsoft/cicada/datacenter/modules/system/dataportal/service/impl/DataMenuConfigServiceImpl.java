package com.dragonsoft.cicada.datacenter.modules.system.dataportal.service.impl;

import com.code.metadata.portal.MenuConfig;
import com.code.metadata.portal.PortalConfig;
import com.code.metaservice.portal.IMenuConfigService;
import com.code.metaservice.portal.IPortalConfigService;
import com.dragonsoft.cicada.datacenter.modules.system.common.vo.TreeVo;
import com.dragonsoft.cicada.datacenter.modules.system.dataportal.service.IDataMenuConfigService;
import com.dragonsoft.cicada.datacenter.modules.system.dataportal.vo.MenuAuthTree;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.service.IUserService;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/9/2
 */
@Service
public class DataMenuConfigServiceImpl implements IDataMenuConfigService {

    @Autowired
    private IMenuConfigService menuConfigService;

    @Autowired
    private IPortalConfigService portalConfigService;

    @Autowired
    private IUserService userService;

    @Override
    public List<TreeVo> getAuthMenuTree(String portalId, String userId) {
        PortalConfig portalConfig = portalConfigService.queryPortalConfigByPortalId(portalId);
        List<MenuConfig> menuConfigs = menuConfigService.queryMenuConfigByConfigId(portalConfig.getId());
        List<String> authIds = userService.getAllAuthFunctionId(userId);
        return this.creatMenuAuthTree(menuConfigs,authIds);
    }

    private List<TreeVo> creatMenuAuthTree(List<MenuConfig> menuConfigs, List<String> authIds) {
        List<TreeVo> menuAuthTrees = Lists.newArrayList();
        for (MenuConfig menuConfig : menuConfigs) {
            MenuAuthTree menuAuthTree = new MenuAuthTree();
            menuAuthTree.setId(menuConfig.getId());
            menuAuthTree.setCode(menuConfig.getCode());
            menuAuthTree.setName(menuConfig.getName());
            menuAuthTree.setIsAuth(authIds.contains(menuConfig.getId()));
            if (null != menuConfig.getChildrenMenuConfigs()) {
                menuAuthTree.setChildren(this.creatMenuAuthTree(new ArrayList<>(menuConfig.getChildrenMenuConfigs()),authIds));
            }
            menuAuthTrees.add(menuAuthTree);
        }
        return menuAuthTrees;
    }
}
