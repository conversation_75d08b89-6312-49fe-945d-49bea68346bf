package com.dragonsoft.cicada.datacenter.modules.datavisual.model;


import com.code.common.utils.StringUtils;
import com.code.meta.dml.standard.IMultCdin;
import com.code.meta.dml.standard.StandardQuery;
import com.code.meta.dml.standard.cdins.MultCdin;
import com.code.meta.dml.standard.cdins.QueryCdins;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;

public class TimeFilter extends AbsWidgetFilter {
    private long startTime;
    private long endTime;
    private String range;
    private long value;
    private int year;
    private int month;
    private int day;

    @Override
    public IMultCdin builderCondition(QueryCdins q) {
        IMultCdin c = new MultCdin();
        DateTimeFormatter dtf2 = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        if (("daterange".equals(this.type) || "monthrange".equals(this.type)) && range != null) {
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime target = LocalDateTime.now();
            String tst = now.format(dtf2);
            String nowTs = "TO_TIMESTAMP('" + tst + "','yyyy-MM-dd hh24:mi:ss')";

            String start = null;
            String end = null;
            if ("before".equals(range)) {
                target = target.minusYears(year).minusMonths(month).minusDays(day);

                start = "TO_TIMESTAMP('" + target.format(dtf2) + "','yyyy-MM-dd hh24:mi:ss')";
                end = nowTs;
            } else {
                target = target.plusYears(year).plusMonths(month).plusDays(day);

                start = nowTs;
                end = "TO_TIMESTAMP('" + target.format(dtf2) + "','yyyy-MM-dd hh24:mi:ss')";
            }
            String fieldCode = StringUtils.isBlank(this.field.getFieldAlias()) ? this.field.getCode() : this.field.getFieldAlias();

            c.and(q.ge(fieldCode, start), q.le(fieldCode, end));

            return c;
        }
        if ("datetime".equals(this.type)) {
            String fieldCode = StringUtils.isBlank(this.field.getFieldAlias()) ? this.field.getCode() : this.field.getFieldAlias();

            if (StandardQuery.type.ES.equals(q.getSqlType())) {
                c.addCdin(q.eq(fieldCode, value));
            } else {
                String tst = LocalDateTime.ofEpochSecond(value / 1000, 0, ZoneOffset.ofHours(8)).format(dtf2);
                String ts = "TO_DATE('" + tst + "','yyyy-MM-dd hh24:mi:ss')";
                c.addCdin(q.eq(fieldCode, ts));
            }

            return c;
        }

        if (startTime == 0 || endTime == 0) {
            return c;
        }

        if (StandardQuery.type.ES.equals(q.getSqlType())) {
            c.and(q.ge(this.field.getCode(), startTime), q.le(this.field.getCode(), endTime));
        } else {
            String st = LocalDateTime.ofEpochSecond(startTime / 1000, 0, ZoneOffset.ofHours(8)).format(dtf2);
            String et = LocalDateTime.ofEpochSecond(endTime / 1000, 0, ZoneOffset.ofHours(8)).format(dtf2);
            String s = "TO_TIMESTAMP('" + st + "','yyyy-MM-dd hh24:mi:ss')";
            String e = "TO_TIMESTAMP('" + et + "','yyyy-MM-dd hh24:mi:ss')";
            c.and(q.ge(this.field.getCode(), s), q.le(this.field.getCode(), e));
        }

        return c;
    }

    public long getStartTime() {
        return startTime;
    }

    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }

    public long getEndTime() {
        return endTime;
    }

    public void setEndTime(long endTime) {
        this.endTime = endTime;
    }

    public String getRange() {
        return range;
    }

    public void setRange(String range) {
        this.range = range;
    }

    public long getValue() {
        return value;
    }

    public void setValue(long value) {
        this.value = value;
    }

    public int getYear() {
        return year;
    }

    public void setYear(int year) {
        this.year = year;
    }

    public int getMonth() {
        return month;
    }

    public void setMonth(int month) {
        this.month = month;
    }

    public int getDay() {
        return day;
    }

    public void setDay(int day) {
        this.day = day;
    }
}
