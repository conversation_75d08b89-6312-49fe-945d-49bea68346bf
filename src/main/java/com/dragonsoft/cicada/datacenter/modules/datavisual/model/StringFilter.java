package com.dragonsoft.cicada.datacenter.modules.datavisual.model;

import com.code.common.utils.StringUtils;
import com.code.meta.dml.standard.IMultCdin;
import com.code.meta.dml.standard.cdins.MultCdin;
import com.code.meta.dml.standard.cdins.QueryCdins;
import com.google.common.base.Strings;

public class StringFilter extends AbsWidgetFilter {

    private String value;

    @Override
    public IMultCdin builderCondition(QueryCdins q) {
        IMultCdin c = new MultCdin();
        if("precise".equals(this.type) && !Strings.isNullOrEmpty(this.value)){
            String fieldCode = StringUtils.isBlank(this.field.getFieldAlias()) ? this.field.getCode() : this.field.getFieldAlias();
            c.addCdin(q.eq(fieldCode,this.value));
        }
        if("prefix_fuzzy".equals(this.type) && !Strings.isNullOrEmpty(this.value)){
            String fieldCode = StringUtils.isBlank(this.field.getFieldAlias()) ? this.field.getCode() : this.field.getFieldAlias();
            c.addCdin(q.likeNoWildcard(fieldCode,"%"+this.value));
        }
        if("suffix_fuzzy".equals(this.type) && !Strings.isNullOrEmpty(this.value)){
            String fieldCode = StringUtils.isBlank(this.field.getFieldAlias()) ? this.field.getCode() : this.field.getFieldAlias();
            c.addCdin(q.likeNoWildcard(fieldCode,this.value+"%"));
        }
        if("fuzzy".equals(this.type) && !Strings.isNullOrEmpty(this.value)){
            String fieldCode = StringUtils.isBlank(this.field.getFieldAlias()) ? this.field.getCode() : this.field.getFieldAlias();
            c.addCdin(q.like(fieldCode,this.value));
        }
        if("not_equal".equals(this.type) && !Strings.isNullOrEmpty(this.value)){
            String fieldCode = StringUtils.isBlank(this.field.getFieldAlias()) ? this.field.getCode() : this.field.getFieldAlias();

            c.addCdin(q.ne(fieldCode,this.value));
        }
        return c;
    }


    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
