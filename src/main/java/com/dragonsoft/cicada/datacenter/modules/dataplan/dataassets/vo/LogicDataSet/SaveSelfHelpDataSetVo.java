package com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.vo.LogicDataSet;

import com.code.dataset.operator.filter.FilterConditionStep;
import com.code.dataset.operator.join.JoinTableStep;
import com.code.metaservice.ddl.vo.LogicDataSetColumnVo;
import lombok.Data;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/10/12 16:41
 */
@Data
public class SaveSelfHelpDataSetVo {
    /**
     * 字段列表
     */
    private List<LogicDataSetColumnVo> columns;
    /**
     * 数据对象ids，一个数据集可能对应多个表
     */
    private List<String> dataObjIds;
    /**
     * 逻辑数据集name
     */
    private String name;
    /**
     * 挂接的树节点id
     */
    private String dataSetTreeNodeId;

    /**
     * 数据来源类型树节点Id
     */
    private String dataSetDsTypeTreeNodeId;
    /**
     * join配置
     */
    private List<JoinTableStep> dataSetJoinVo;
    /**
     * 过滤条件
     */
    FilterConditionStep filterConditionStep;

    /**
     * 批量添加时，通过后台获取字段信息
     */
    private SaveTableColumnMappingVo tableMapping;


//    @Data
//    public static class DataSetJoinVo {
//        private String id;
//        private String name;
//        /**
//         * 要join的表id
//         */
//        private String sourceTableId;
//        /**
//         * join type在这个对象里
//         */
//        private JoinTableStep joinTableStep;
//    }


}
