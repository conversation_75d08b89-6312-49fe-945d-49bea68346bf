package com.dragonsoft.cicada.datacenter.modules.datavisual.function;

import com.code.dragonsoft.dataquery.service.querymodel.ColumnDataModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/1/8
 */
@Service
public class FunctionScheduler {

    @Autowired
    private FunctionExecutor functionExecutor;

    public ColumnDataModel execute(ColumnDataModel dates, ChartConfig config) {

        for (ColumnMeta columnMeta : config.getMetrics()) {
            List<ColumnMeta> metrics = new LinkedList<>();
            metrics.add(columnMeta);
            ChartConfig chartConfig = new ChartConfig(config.getDimensions(), metrics);
            if (0 != columnMeta.getColumnFunctions().length) {
                dates = functionExecutor.execute(dates, chartConfig, columnMeta.getColumnFunctions());
            }
        }
        return dates;
    }


}
