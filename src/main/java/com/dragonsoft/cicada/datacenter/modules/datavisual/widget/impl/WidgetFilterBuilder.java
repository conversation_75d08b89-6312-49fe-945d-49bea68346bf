package com.dragonsoft.cicada.datacenter.modules.datavisual.widget.impl;

import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dragonsoft.cicada.datacenter.modules.datavisual.model.AbsWidgetFilter;
import com.dragonsoft.cicada.datacenter.modules.datavisual.model.enums.FilterEnum;
import com.dragonsoft.cicada.datacenter.modules.datavisual.widget.IWidgetFilterBuilder;
import org.springframework.stereotype.Component;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;

@Component
public class WidgetFilterBuilder implements IWidgetFilterBuilder {


    @Override
    public List<AbsWidgetFilter> builder(String json) {
        List<AbsWidgetFilter> widgetFilters = new ArrayList<>();
        JSONArray jsonArray = JSONArray.parseArray(json);
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject object = (JSONObject) jsonArray.get(i);
            String type = (String) object.get("parentType");
            if (CharSequenceUtil.isBlank(type)) {
                continue;
            }
            AbsWidgetFilter filter = JSONObject.parseObject(object.toJSONString(), (Type) FilterEnum.getClassByType(type));
            if (null != filter) {
                widgetFilters.add(filter);
            }
        }
        return widgetFilters;
    }
}
