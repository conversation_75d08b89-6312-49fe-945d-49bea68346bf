package com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.common;

/**
 * <AUTHOR>
 * @date 2020.9.9 13:45
 */
public class LogicDataSetConstant {

    //数据集的type
    public static final String LOGIC_DATA_SET_TYPE = "LogicDataObj";
    //非极速表
    public static final String NOT_FAST = "0";
    //极速表
    public static final String FAST = "1";
    //数据集的BelongType
    public static final String BELONG_TYPE = "LOGIC";
    //数据集字段维度
    public static final String DIMENSION = "DIMENSION";
    //数据集字段度量
    public static final String MEASURE = "MEASURE";
    //时间转换格式
    public static final String DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";

    //数据集新增字段的字段code的前缀
    public static final String ROW = "dg_row";
    //用于判断字段类型是数值
    public static final String NUMBER = "NUMBER";
    //用于判断字段类型是文本
    public static final String TEXT = "TEXT";
    //数据集字段数值格式前端选择[无]
    public static final String NONE = "none";

    //用于判断步骤是过滤条件
    public static final String STEP_TYPE_FILTER = "filter";
    //用于判断步骤是左右关联
    public static final String STEP_TYPE_JOIN = "join";
    public static final String STEP_TYPE_UNION = "union";

    //步骤的code
    public static final String STEP_EDIT_COLUMN = "editColumn";
    public static final String STEP_INDEX_TYPE = "indexType";
    public static final String STEP_ADD_COLUMN = "addColumn";
    public static final String STEP_FILTER_CONDITION = "filterCondition";
    public static final String STEP_SYNC_COLUMN = "syncColumn";
    public static final String STEP_JOIN_TABLE = "joinTable";
    public static final String STEP_DELETE_COLUMN = "deleteColumn";
    public static final String STEP_FORMAT_COLUMN = "formatColumn";
    public static final String STEP_NUMBER_FORMAT = "numberFormat";
    public static final String STEP_UNION_TABLE = "unionTable";

    //数据集目录树BaseBusiClassify的code
    public static final String LOGIC_BUSI_CLASSIFY_CODE = "DATASET_DIR";

}
