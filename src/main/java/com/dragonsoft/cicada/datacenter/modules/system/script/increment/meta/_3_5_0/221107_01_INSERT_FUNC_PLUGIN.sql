-- 一级 建模空间

-- 二级 输入输出插件目录
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state", "is_doc_show") VALUES ('inputOutputDir', '0', '输入输出', 'dataModeling', NULL, NULL, NULL, '2022-08-01 14:21:03', NULL, '1', '1');
-- 三级 输入输出插件目录下的具体插件
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state", "is_doc_show") VALUES ('inputOutputDirCicadaFileOutputMeta', '0', '文件下载', 'inputOutputDir', NULL, NULL, NULL, '2022-08-01 14:21:03', NULL, '1', '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state", "is_doc_show") VALUES ('inputOutputDirCicadaServiceInputMeta', '0', '服务输入', 'inputOutputDir', NULL, NULL, NULL, '2022-08-01 14:21:03', NULL, '1', '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state", "is_doc_show") VALUES ('inputOutputDirCicadaFileInputMeta', '0', '文件上传', 'inputOutputDir', NULL, NULL, NULL, '2022-08-01 14:21:03', NULL, '1', '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state", "is_doc_show") VALUES ('inputOutputDirCicadaStandardSqlOutput', '0', '关系库表输出', 'inputOutputDir', NULL, NULL, NULL, '2022-08-01 14:21:03', NULL, '1', '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state", "is_doc_show") VALUES ('inputOutputDirCicadaFullTextOutput', '0', '全文库表输出', 'inputOutputDir', NULL, NULL, NULL, '2022-08-01 14:21:03', NULL, '1', '1');

-- 二级 服务组件
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state", "is_doc_show") VALUES ('dataServiceDir', '0', '服务组件', 'dataModeling', NULL, NULL, NULL, '2022-08-01 14:21:03', NULL, '1', '1');
-- 三级 服务组件下的插件
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state", "is_doc_show") VALUES ('dataServiceDirCicadaMetaServiceInput', '0', '服务输入插件', 'dataServiceDir', NULL, NULL, NULL, '2022-08-01 14:21:03', NULL, '1', '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state", "is_doc_show") VALUES ('dataServiceDirCicadaMetaServiceOutput', '0', '服务输出插件', 'dataServiceDir', NULL, NULL, NULL, '2022-08-01 14:21:03', NULL, '1', '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state", "is_doc_show") VALUES ('dataServiceDirCicadaMetaServiceCheckOutPut', '0', '信息核查服务输出插件 ', 'dataServiceDir', NULL, NULL, NULL, '2022-08-01 14:21:03', NULL, '1', '1');


-- 二级 数据筛选
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state", "is_doc_show") VALUES ('dataScreeningDir', '0', '数据筛选', 'dataModeling', NULL, NULL, NULL, '2022-08-01 14:21:03', NULL, '1', '1');
-- 三级 数据筛选下的插件
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state", "is_doc_show") VALUES ('dataScreeningDirCicadaDataDistinctMeta', '0', '数据去重', 'dataScreeningDir', NULL, NULL, NULL, '2022-08-01 14:21:03', NULL, '1', '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state", "is_doc_show") VALUES ('dataScreeningDirCicadaConditionFilterPlugin', '0', '条件过滤', 'dataScreeningDir', NULL, NULL, NULL, '2022-08-01 14:21:03', NULL, '1', '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state", "is_doc_show") VALUES ('dataScreeningDirCicadaFieldFilteringMeta', '0', '字段过滤', 'dataScreeningDir', NULL, NULL, NULL, '2022-08-01 14:21:03', NULL, '1', '1');

-- 二级 数据碰撞
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state", "is_doc_show") VALUES ('dataCollisionDir', '0', '数据碰撞', 'dataModeling', NULL, NULL, NULL, '2022-08-01 14:21:03', NULL, '1', '1');
-- 三级 数据碰撞下的插件
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state", "is_doc_show") VALUES ('dataCollisionDirCicadaCollisionPlugin', '0', '合并列', 'dataCollisionDir', NULL, NULL, NULL, '2022-08-01 14:21:03', NULL, '1', '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state", "is_doc_show") VALUES ('dataCollisionDirCicadaUnionJoinPlugin', '0', '合并行', 'dataCollisionDir', NULL, NULL, NULL, '2022-08-01 14:21:03', NULL, '1', '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state", "is_doc_show") VALUES ('dataCollisionDirCicadaSubtractByKeyPlugin', '0', '求差集', 'dataCollisionDir', NULL, NULL, NULL, '2022-08-01 14:21:03', NULL, '1', '1');

-- 二级 数据处理
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state", "is_doc_show") VALUES ('dataProcessDir', '0', '数据处理', 'dataModeling', NULL, NULL, NULL, '2022-08-01 14:21:03', NULL, '1', '1');

-- 三级 数据处理下的插件
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state", "is_doc_show") VALUES ('dataProcessDirCicadaServiceOrganization', '0', '表达式处理', 'dataProcessDir', NULL, NULL, NULL, '2022-08-01 14:21:03', NULL, '1', '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state", "is_doc_show") VALUES ('dataProcessDirCicadaObjectToJsonMeta', '0', '行数据转json', 'dataProcessDir', NULL, NULL, NULL, '2022-08-01 14:21:03', NULL, '1', '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state", "is_doc_show") VALUES ('dataProcessDirServiceOrganization', '0', '算子编排组件', 'dataProcessDir', NULL, NULL, NULL, '2022-08-01 14:21:03', NULL, '1', '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state", "is_doc_show") VALUES ('dataProcessDirCicadaFieldsSettings', '0', '字段设置', 'dataProcessDir', NULL, NULL, NULL, '2022-08-01 14:21:03', NULL, '1', '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state", "is_doc_show") VALUES ('dataProcessDirCicadaDataSortPlugin', '0', '数据排序', 'dataProcessDir', NULL, NULL, NULL, '2022-08-01 14:21:03', NULL, '1', '1');

-- 二级 分析挖掘
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state", "is_doc_show") VALUES ('dataAnalysisDir', '0', '分析挖掘', 'dataModeling', NULL, NULL, NULL, '2022-08-01 14:21:03', NULL, '1', '1');
-- 三级 分析挖掘下的插件
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state", "is_doc_show") VALUES ('dataAnalysisDirCicadaLabelMeta', '0', '数据打标', 'dataAnalysisDir', NULL, NULL, NULL, '2022-08-01 14:21:03', NULL, '1', '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state", "is_doc_show") VALUES ('dataAnalysisDirCicadaReducePlugin', '0', '分组统计', 'dataAnalysisDir', NULL, NULL, NULL, '2022-08-01 14:21:03', NULL, '1', '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state", "is_doc_show") VALUES ('dataAnalysisDirCicadaDateCicadaZipperTablePlugin', '0', '时间拉链', 'dataAnalysisDir', NULL, NULL, NULL, '2022-08-01 14:21:03', NULL, '1', '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state", "is_doc_show") VALUES ('dataAnalysisDirCicadaNumberCicadaZipperTablePlugin', '0', '数值拉链', 'dataAnalysisDir', NULL, NULL, NULL, '2022-08-01 14:21:03', NULL, '1', '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state", "is_doc_show") VALUES ('dataAnalysisDirCicadaScriptMeta', '0', 'SQL脚本', 'dataAnalysisDir', NULL, NULL, NULL, '2022-08-01 14:21:03', NULL, '1', '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state", "is_doc_show") VALUES ('dataAnalysisDirCicadaJsonParsingContent', '0', 'json解析插件', 'dataAnalysisDir', NULL, NULL, NULL, '2022-08-01 14:21:03', NULL, '1', '1');

-- 二级 业务组件
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state", "is_doc_show") VALUES ('dataBusinessDir', '0', '业务组件', 'dataModeling', NULL, NULL, NULL, '2022-08-01 14:21:03', NULL, '1', '1');

-- 三级 业务组件下的插件
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state", "is_doc_show") VALUES ('dataBusinessDirCicadaPeerContentMeta', '0', '同行解析插件', 'dataBusinessDir', NULL, NULL, NULL, '2022-08-01 14:21:03', NULL, '1', '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state", "is_doc_show") VALUES ('dataBusinessDirCicadaMarkingTimeMeta', '0', '时间离散化', 'dataBusinessDir', NULL, NULL, NULL, '2022-08-01 14:21:03', NULL, '1', '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state", "is_doc_show") VALUES ('dataBusinessDirCicadaEffectivePolice', '0', '24小时有效警情', 'dataBusinessDir', NULL, NULL, NULL, '2022-08-01 14:21:03', NULL, '1', '1');

INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state", "is_doc_show") VALUES ('dataProcessDirCicadaCodeConversion', '0', '代码转换', 'dataProcessDir', NULL, NULL, NULL, '2022-08-01 14:21:03', NULL, '1', '1');

-- 绑定系统管理员权限

INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('9fb8dc2bf0905fe28e15b0a66930c655', 'd6121bc4248e45019942e2cb78362500', 'inputOutputDir', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('2b0780e121d25ccc93e11c5fdc8e2d0a', 'd6121bc4248e45019942e2cb78362500', 'inputOutputDirCicadaFileOutputMeta', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('17f1717a8363504f918bd0dcb63813b2', 'd6121bc4248e45019942e2cb78362500', 'inputOutputDirCicadaServiceInputMeta', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('08daa640fcc359d3a19922e6e13ca534', 'd6121bc4248e45019942e2cb78362500', 'inputOutputDirCicadaFileInputMeta', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('3ed5bf5ea29c5641a5774ebd6894a7a0', 'd6121bc4248e45019942e2cb78362500', 'inputOutputDirCicadaStandardSqlOutput', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('801799103f4756e8bdfb35fcd9aec3b4', 'd6121bc4248e45019942e2cb78362500', 'inputOutputDirCicadaFullTextOutput', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('f1c9aa698d9d5d6c844881e4e2731639', 'd6121bc4248e45019942e2cb78362500', 'dataServiceDir', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('248af3c110115ac9a383a482ac9047ce', 'd6121bc4248e45019942e2cb78362500', 'dataServiceDirCicadaMetaServiceInput', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('815c117af11f58d58644193edf8e611b', 'd6121bc4248e45019942e2cb78362500', 'dataServiceDirCicadaMetaServiceOutput', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('8300a0e116c852fbb6f2d050cd294955', 'd6121bc4248e45019942e2cb78362500', 'dataServiceDirCicadaMetaServiceCheckOutPut', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('1c5f474e72405664ae54672a117e9f0c', 'd6121bc4248e45019942e2cb78362500', 'dataScreeningDir', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('f72f7470648155969bb741a157c7c8f4', 'd6121bc4248e45019942e2cb78362500', 'dataScreeningDirCicadaDataDistinctMeta', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('15a756a93794577b955351369bf230e9', 'd6121bc4248e45019942e2cb78362500', 'dataScreeningDirCicadaConditionFilterPlugin', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('477ff30786b25eab8cb4ecda023b3ab8', 'd6121bc4248e45019942e2cb78362500', 'dataScreeningDirCicadaFieldFilteringMeta', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('264d09cf9fb554668abb3e273da1d21d', 'd6121bc4248e45019942e2cb78362500', 'dataCollisionDir', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('a851e5d914fd5995958b6380491095b6', 'd6121bc4248e45019942e2cb78362500', 'dataCollisionDirCicadaCollisionPlugin', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('5e762dbd6f775d58859099e171203dcc', 'd6121bc4248e45019942e2cb78362500', 'dataCollisionDirCicadaUnionJoinPlugin', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('e79ccae13ae75648979273ec707b8c89', 'd6121bc4248e45019942e2cb78362500', 'dataCollisionDirCicadaSubtractByKeyPlugin', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('fbb0d8343b1c576081d80f65c79acb13', 'd6121bc4248e45019942e2cb78362500', 'dataProcessDir', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('9102630089df5483a6d8b1c7a32fed26', 'd6121bc4248e45019942e2cb78362500', 'dataProcessDirCicadaServiceOrganization', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('c0a5652904325f059cdbf2276df81c92', 'd6121bc4248e45019942e2cb78362500', 'dataProcessDirCicadaObjectToJsonMeta', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('20782385d13c5e6182e7dbdc3424fe12', 'd6121bc4248e45019942e2cb78362500', 'dataProcessDirServiceOrganization', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('28e31c7ee3db538fa082232764b83206', 'd6121bc4248e45019942e2cb78362500', 'dataProcessDirCicadaFieldsSettings', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('87813b2daea0565a813aac078ae35af2', 'd6121bc4248e45019942e2cb78362500', 'dataProcessDirCicadaDataSortPlugin', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('82c31592412a5b55a54e89f2d0e8d52a', 'd6121bc4248e45019942e2cb78362500', 'dataAnalysisDir', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('a015581389fd51d3ab04f45a04c88f68', 'd6121bc4248e45019942e2cb78362500', 'dataAnalysisDirCicadaLabelMeta', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('5d2a43f376b454ae99ca1a1a57a04694', 'd6121bc4248e45019942e2cb78362500', 'dataAnalysisDirCicadaReducePlugin', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('5c3d10bad2f850a7a883c4df7ebb3145', 'd6121bc4248e45019942e2cb78362500', 'dataAnalysisDirCicadaDateCicadaZipperTablePlugin', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('c157359d776b5feda761199363e86f97', 'd6121bc4248e45019942e2cb78362500', 'dataAnalysisDirCicadaNumberCicadaZipperTablePlugin', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('5f3bd024cdcc5760aaf86c50face936c', 'd6121bc4248e45019942e2cb78362500', 'dataAnalysisDirCicadaScriptMeta', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('91acb98f172f5108aeda2015104c27ca', 'd6121bc4248e45019942e2cb78362500', 'dataAnalysisDirCicadaJsonParsingContent', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('6b00e91817b15051807c0476e9129c3b', 'd6121bc4248e45019942e2cb78362500', 'dataBusinessDir', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('677dd3fc0dfa5700b62736a7e387a1a8', 'd6121bc4248e45019942e2cb78362500', 'dataBusinessDirCicadaPeerContentMeta', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('95f19c5cd49e59eebcb02d43b7ae0c8f', 'd6121bc4248e45019942e2cb78362500', 'dataBusinessDirCicadaMarkingTimeMeta', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('6ab00ff259275a38bf94ea1353fd60b3', 'd6121bc4248e45019942e2cb78362500', 'dataBusinessDirCicadaEffectivePolice', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('9102630089df5483a6d8b1c7a32fed5e', 'd6121bc4248e45019942e2cb78362500', 'dataProcessDirCicadaCodeConversion', NULL, NULL, NULL, NULL, NULL, NULL, NULL);

