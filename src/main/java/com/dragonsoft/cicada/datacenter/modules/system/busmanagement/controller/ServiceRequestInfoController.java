package com.dragonsoft.cicada.datacenter.modules.system.busmanagement.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.code.cicada.thirdplugin.service.meta.CicadaServiceInputMeta;
import com.code.cicada.thirdplugin.service.page.service.CicadaIServiceInputService;
import com.code.common.paging.PageInfo;
import com.code.common.utils.StringUtils;
import com.code.common.utils.assertion.Assert;
import com.code.metadata.bus.management.ServiceType;
import com.code.metaservice.bus.management.IHeaderAttributeService;
import com.code.metaservice.bus.management.IServiceTypeService;
import com.code.metaservice.etl.trans.TransMetaService;
import com.dragoninfo.dfw.annotation.FuncScanAnnotation;
import com.dragoninfo.dfw.annotation.ValidateAndLogAnnotation;
import com.dragoninfo.dfw.bean.Result;
import com.dragonsoft.cicada.datacenter.modules.modeling.service.MLSQLService;
import com.dragonsoft.cicada.datacenter.modules.system.busmanagement.service.IBusInfoManagementService;
import com.dragonsoft.cicada.datacenter.modules.system.busmanagement.service.IServiceInfoManagementService;
import com.dragonsoft.cicada.datacenter.modules.system.busmanagement.vo.BusInfoVo;
import com.dragonsoft.cicada.datacenter.modules.system.busmanagement.vo.InterfaceCfgVo;
import com.dragonsoft.cicada.datacenter.modules.system.busmanagement.vo.ServiceInfoVo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/03/15
 */
@RestController
@CrossOrigin
@RequestMapping("/serviceRequestInfoController")
@FuncScanAnnotation(code = "accessBusManagement", name = "接入服务管理", parentCode = "busManagement")
@Slf4j
public class ServiceRequestInfoController {
    @Autowired
    private IServiceInfoManagementService serviceInfoManagementService;


    @Autowired
    IHeaderAttributeService headerAttributeService;

    @Autowired
    IServiceTypeService serviceTypeService;

    @Autowired
    MLSQLService mlsqlService;

    @Autowired
    TransMetaService transMetaService;

    @Autowired
    CicadaIServiceInputService serviceInputService;


    @Autowired
    private IBusInfoManagementService busInfoManagementService;

    /**
     * 查询接口配置信息
     *
     * @param id
     * @return
     */
    @ResponseBody
    @RequestMapping("/getInterfaceCfgVoById")
    public Result getInterfaceCfgVoById(String id) {
        try {
            return Result.success(serviceInfoManagementService.getInterfaceCfgVoById(id));
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return Result.error("400", e.getMessage());
        }
    }

    /**
     * 查询服务配置信息
     *
     * @param id
     * @return
     */
    @ResponseBody
    @RequestMapping("/getServiceInfoById")
    @FuncScanAnnotation(code = "accessBusManagementDetails", name = "详情", parentCode = "accessBusManagement")
    @ValidateAndLogAnnotation
    public Result getServiceInfoById(String id) {
        try {
            return Result.success(serviceInfoManagementService.getServiceInfoById(id));
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return Result.error("400", e.getMessage());
        }
    }




    /**
     * 删除参数配置
     *
     * @param id
     * @return
     */
    @ResponseBody
    @RequestMapping("/deleteInterfaceBodyById")
//    @FuncScanAnnotation(code = "accessBusManagementMsgInfoDelete", name = "请求参数删除", parentCode = "accessBusManagement")
//    @ValidateAndLogAnnotation
    public Result deleteInterfaceBodyById(String id) {
        try {
            serviceInfoManagementService.deleteInterfaceBodyById(id);
            return Result.success();
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return Result.error("400", e.getMessage());
        }
    }

    /**
     * 删除服务配置
     *
     * @param id
     * @return
     */
    @ResponseBody
    @RequestMapping("/deleteServiceById")
    @FuncScanAnnotation(code = "accessBusManagementDelete", name = "删除", parentCode = "accessBusManagement")
    @ValidateAndLogAnnotation
    public Result deleteServiceById(String id) {
        try {
            serviceInfoManagementService.deleteServiceById(id);
            return Result.success();
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return Result.error("400", e.getMessage());
        }
    }

    /**
     * @param map     分页查询参数
     * @param request 请求体
     */
    @ResponseBody
    @RequestMapping("/getServiceInfoPage")
    public Result getServiceInfoPage(@RequestBody Map map, HttpServletRequest request) {
        try {
            String userId = (String) request.getSession().getAttribute("userId");
            String keyWord = (String) map.get("keyWord");
            int index = (int) map.get("index");
            int pageSize = (int) map.get("pageSize");
            PageInfo pageInfo = new PageInfo();
            pageInfo.setPageIndex(index);
            pageInfo.setPageSize(pageSize);
            pageInfo.setDataList(Lists.newArrayList());
            return Result.success(serviceInfoManagementService.getServiceInfoPage(pageInfo, userId, keyWord));
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return Result.error("400", e.getMessage());
        }
    }

    /**
     * 新增服务信息
     */
    @ResponseBody
    @RequestMapping("/saveServiceInfo")
    @FuncScanAnnotation(code = "accessBusManagementAddNewService", name = "新增服务", parentCode = "accessBusManagement")
    @ValidateAndLogAnnotation
    public Result saveServiceInfo(@RequestBody ServiceInfoVo serviceInfoVo, HttpServletRequest request) {
        try {
            String userId = (String) request.getSession().getAttribute("userId");
            serviceInfoManagementService.tSaveServiceInfo(serviceInfoVo, userId);
            return Result.success();
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            Assert.fail(e.getMessage());
            return null;
        }
    }

    /**
     * 新增接口信息
     */
    @ResponseBody
    @RequestMapping("/saveInterfaceBody")
    @FuncScanAnnotation(code = "accessBusManagementAddServiceInterface", name = "添加服务接口", parentCode = "accessBusManagement")
    @ValidateAndLogAnnotation
    public Result saveInterfaceBodyService(@RequestBody InterfaceCfgVo interfaceCfgVo, HttpServletRequest request) {
        try {
            String userId = (String) request.getSession().getAttribute("userId");
            serviceInfoManagementService.saveInterfaceBodyService(interfaceCfgVo, userId);
            return Result.success();
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            Assert.fail(e.getMessage());
            return null;
        }
    }


    /**
     * 查询接口列表
     */
    @ResponseBody
    @RequestMapping("/getInterfaceInfoListBySId")
    public Result getInterfaceInfoListBySId(String serviceId) {
        try {
            List<InterfaceCfgVo> interfaceCfgVos = serviceInfoManagementService.getInterfaceInfoListBySId(serviceId);
            return Result.success(interfaceCfgVos);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return Result.error("400", e.getMessage());
        }
    }

    /**
     * 查询服务列表
     */
    @ResponseBody
    @RequestMapping("/getServiceInfoList")
    public Result getServiceInfoList(HttpServletRequest request) {
        try {
            String userId = (String) request.getSession().getAttribute("userId");
            List<ServiceInfoVo> serviceInfoListBy = serviceInfoManagementService.getServiceInfoList(userId);
            return Result.success(serviceInfoListBy);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return Result.error("400", e.getMessage());
        }
    }

    /**
     * 查询请求方id列表
     */
    @ResponseBody
    @RequestMapping("/getSenderListId")
    public Result getSenderListId() {
        try {
            List<String> strings = headerAttributeService.querySenderId();
            return Result.success(strings);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return Result.error("400", e.getMessage());
        }
    }

    /**
     * 查询服务方类型
     */
    @ResponseBody
    @RequestMapping("/getServiceTypeList")
    public Result getServiceTypeList(HttpServletRequest request) {
        try {
            List<ServiceType> serviceTypes = serviceTypeService.queryServiceType();
            return Result.success(serviceTypes);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return Result.error("400", e.getMessage());
        }
    }

    /**
     * 查询服务方类型-》细类
     */
    @ResponseBody
    @RequestMapping("/getServiceTypeListById")
    public Result getServiceTypeListById(String id) {
        try {
            return Result.success(serviceTypeService.get(ServiceType.class, id).getChildrenTypes());
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return Result.error("400", e.getMessage());
        }
    }


    /**
     * 查询上一个请求头、响应头的数据
     */
    @ResponseBody
    @RequestMapping("/getRequestHeader")
    public Result getRequestHeader() {
        try {
            return Result.success(ServiceInfoVo.getHeaderCfgModel());
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return Result.error("400", e.getMessage());
        }
    }


    /**
     * 查询请求参数
     * @param cfgId
     * @return
     */
    @ResponseBody
    @RequestMapping("/getBodyAttributeByCfgId")
    @FuncScanAnnotation(code = "accessBusManagementMsgInfoDetails", name = "请求参数详情", parentCode = "accessBusManagement")
//    @ValidateAndLogAnnotation
    public Result getBodyAttributeByCfgId(String cfgId) {
        try {
            return Result.success(serviceInfoManagementService.getBodyAttributeByCfgId(cfgId));
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return Result.error("400", e.getMessage());
        }
    }


    /**
     * 解析输出字段
     */
    @ResponseBody
    @RequestMapping("/analyseColumn")
    public Result analyseColumn(String transId, String tranStepId, HttpSession session) {
        String userId = (String) session.getAttribute("userId");
        CicadaServiceInputMeta serviceInputMeta = serviceInputService.getPluginInstance(CicadaServiceInputMeta.class, tranStepId);
        Map<String, String> afterTable = mlsqlService.getAfterTableName(transId, tranStepId, true,null,userId);
        List<String> fieldList = Lists.newArrayList();
        Map<String, Object> resMap = Maps.newHashMap();
        String mlsql = "select * from " + afterTable.get("tableName") + " limit 10 as preview_" + afterTable.get("tableName") + ";";
        mlsql = afterTable.get("sql") + "\n" + mlsql;
        resMap.put("sql", mlsql);
        resMap.put("includeSchema", true);
        resMap.put("fetchType", "take");
        resMap.put("outputSize", 10);
        String runScript = mlsqlService.runScript(resMap);
        if (("bus_type").equals(serviceInputMeta.getRequestBodyType())) {
            JSONObject jsonObject = JSONObject.parseObject(runScript);
            JSONArray data = (JSONArray) jsonObject.get("data");
            if (CollectionUtils.isNotEmpty(data) && StringUtils.isNotBlank((String) ((JSONObject) data.get(0)).get("result"))) {
                String[] split = ((JSONObject) data.get(0)).get("result").toString().split("%");
                JSONObject jsonObject1 = checkMessage(split);
                if (null != jsonObject1) {
                    for (String field : jsonObject1.keySet()) {
                        fieldList.add(field);
                    }
                }
            }
        }
        if (CollectionUtils.isEmpty(fieldList)) {
            fieldList.add("result");
        }
        serviceInputService.saveMetaOutputColumn(tranStepId, fieldList);
        return Result.success();
    }


    private JSONObject checkMessage(String[] split) {
        try {
            JSONObject jsonObject = (JSONObject) JSONObject.parse(split[0]);
            return jsonObject;
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            Assert.fail(split[0]);
        }
        return null;
    }


    /**
     * 保存总线信息
     * @param request
     * @param busInfoVo
     * @return
     */
    @ResponseBody
    @RequestMapping("/saveOrUpdateBusInfo")
    @FuncScanAnnotation(code = "accessBusManagementSaved", name = "保存", parentCode = "accessBusManagement")
    @ValidateAndLogAnnotation
    public Result saveOrUpdateBusInfo(HttpServletRequest request, @RequestBody BusInfoVo busInfoVo) {
        try{
            String userId = (String) request.getSession().getAttribute("userId");
            return Result.success(busInfoManagementService.saveOrUpdateBusInfo(busInfoVo,userId));
        }catch (Exception e){
            log.error(e.getMessage(),e);
            return Result.error("400",e.getMessage());
        }
    }

}
