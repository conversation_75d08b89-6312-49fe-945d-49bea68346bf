package com.dragonsoft.cicada.datacenter.modules.system.dataportal.vo;

import com.dragonsoft.cicada.datacenter.modules.system.common.vo.TreeVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/9/2
 */
@Data
@ApiModel(value="菜单授权树")
public class MenuAuthTree extends TreeVo {

    @ApiModelProperty(value="是否授权" ,required=true)
    private Boolean isAuth;

//    @ApiModelProperty(value="叶子节点" ,required=true)
//    List<MenuAuthTree> childrenMenuAuthTree = Lists.newArrayList();
}
