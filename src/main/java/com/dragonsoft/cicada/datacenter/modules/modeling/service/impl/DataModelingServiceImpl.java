package com.dragonsoft.cicada.datacenter.modules.modeling.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.code.common.log.LogUtil;
import com.code.common.mist.plugin.config.service.impl.PluginConfigService;
import com.code.common.paging.PageInfo;
import com.code.common.sch.model.ScheduleModeType;
import com.code.common.schedulectr.scheduleclient.schemeconfig.JobMetaConfiger;
import com.code.common.schedulectr.scheduleclient.schemeconfig.SimpleJobInfo;
import com.code.common.utils.R;
import com.code.common.utils.StringUtils;
import com.code.metadata.business.directory.BaseBusiClassify;
import com.code.metadata.business.directory.BaseBusiDir;
import com.code.metadata.business.directory.BusiClassify;
import com.code.metadata.business.directory.EnumBusiType;
import com.code.metadata.etl.trans.*;
import com.code.metadata.model.core.BaseModelElement;
import com.code.metadata.model.core.DataType;
import com.code.metadata.model.core.ModelElement;
import com.code.metadata.res.ddl.LogicDataObj;
import com.code.metadata.sm.EnumServiceType;
import com.code.metadata.sm.ServiceMeta;
import com.code.metadata.sm.ServicePublication;
import com.code.metadata.sql.utils.SQLUtils;
import com.code.metaservice.business.directory.BusiClassifyService;
import com.code.metaservice.business.directory.BusiDirService;
import com.code.metaservice.core.ClassifierService;
import com.code.metaservice.ddl.ILogicDataObjService;
import com.code.metaservice.etl.trans.TransMetaService;
import com.code.metaservice.modelsupermark.IModelSearchService;
import com.code.metaservice.sm.IServicePublicationService;
import com.code.ms.domain.application.productionline.dto.RefreshTransStatusDTO;
import com.code.ms.domain.application.productionline.dto.RefreshTransStatusResultDTO;
import com.code.ms.domain.application.productionline.dto.TransStatusDTO;
import com.code.std.types.StandardType;
import com.code.std.types.StandardTypeFactory;
import com.dragoninfo.dfw.entity.TSysAuthObjFunc;
import com.dragoninfo.dfw.entity.TSysAuthObjRel;
import com.dragoninfo.dfw.service.SysAuthObjFuncService;
import com.dragoninfo.dfw.service.SysAuthObjRelService;
import com.dragoninfo.dfw.service.SysAuthObjService;
import com.dragonsoft.cicada.datacenter.common.token.GlobalConstant;
import com.dragonsoft.cicada.datacenter.common.utils.UserContextUtil;
import com.dragonsoft.cicada.datacenter.modules.common.service.CommonBusiClassifyService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.externaldatasources.vo.SQLModelVo;
import com.dragonsoft.cicada.datacenter.modules.firstpage.share.vo.ResourceTypeEnum;
import com.dragonsoft.cicada.datacenter.modules.modeling.aimodeling.service.AiDataModelingService;
import com.dragonsoft.cicada.datacenter.modules.modeling.aimodeling.vo.ModelTreeResult;
import com.dragonsoft.cicada.datacenter.modules.modeling.importandexport.util.DataExportUtil;
import com.dragonsoft.cicada.datacenter.modules.modeling.service.DataModelingService;
import com.dragonsoft.cicada.datacenter.modules.modeling.util.TransClassifyEnum;
import com.dragonsoft.cicada.datacenter.modules.modeling.vo.ModelTreeVo;
import com.dragonsoft.cicada.datacenter.modules.modeling.vo.PluginVo;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.service.IUserService;
import com.fw.dao.hbmimpl.BaseDaoImpl;
import com.fw.service.BaseService;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.dragonsoft.cicada.datacenter.modules.firstpage.share.vo.ResourceTypeEnum.INFORMATION_VERFICATION;

@Service
@Slf4j
@PropertySource("classpath:case-config.properties")
public class DataModelingServiceImpl extends BaseService implements DataModelingService {

    public static final String QUICK_SPARK = "QUICK_SPARK";
    private static String nodeParent = "isParent";
    private static String nodeId = "id";
    private static String nodeTime = "operateTime";
    private static String nodeName = "name";
    private static String nodeCode = "code"; // 插件导航使用
    private static String nodeType = "type";  // 插件导航使用 0：目录， 1：插件
    private static String nodeEmType = "emType";
    private static String nodeOpen = "open";
    private static String nodePid = "pId";
    private static String nodeChildren = "children";
    private static String nodeIcon = "icon";
    private static String nodeDirType = "dirType";
    private static String baseSql = "select id,name,code as dirType , parent_classify_id pid,operate_time AS operateTime from t_md_busi_classify t1 where parent_classify_id=:pid";
    private static final String currentUserId="currentUserId";

    private static SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd000000");

    @Autowired
    private BusiClassifyService busiClassifyService;

    @Autowired
    private BusiDirService busiDirService;

    @Autowired
    private ClassifierService classifierService;

    @Autowired
    private TransMetaService transMetaService;

    @Autowired
    private JobMetaConfiger jobMetaConfiger;

    @Autowired
    private IUserService userService;

    @Autowired
    private BaseDaoImpl baseDao;

    @Autowired
    private SysAuthObjFuncService sysAuthObjFuncService;

    @Autowired
    private SysAuthObjRelService sysAuthObjRelService;

    @Autowired
    private SysAuthObjService sysAuthObjService;

    @Autowired
    private IModelSearchService modelSearchServiceImpl;

    @Autowired
    private AiDataModelingService aiDataModelingService;

    @Autowired
    private PluginConfigService pluginConfigService;

    @Autowired
    private IServicePublicationService servicePublicationService;

    @Autowired
    private ILogicDataObjService logicDataObjService;

    @Autowired
    private CommonBusiClassifyService commonBusiClassifyService;

    @Value("classpath:/plugin.json")
    private Resource pluginsJSON;

    @Value("${sceModel}")
    private boolean sceModel;

    @Value("${standModel}")
    private boolean standModel;

    @Override
    public List<Map<String, Object>> getModelingTransTreeNodes(String parentId, String parentName, String userId, String dirType) {
        List<Map<String, Object>> treeNodes;
        LogUtil.Clock clock = LogUtil.getClock();
        treeNodes = getTransDirRootList(dirType, userId);
//        treeNodes.add(getTransDirRootList("TRANS_DIR_MF_MY", userId).get(0));
        log.info("获取目录结构总共花费" + clock.consume() + "毫秒");
        return treeNodes;
    }

    @Override
    public void updateOutputColumn(String stepId, String field, int length, int precsn, String valType, String filterExpress, String uniqueValue) {
        TransMeta trans = (TransMeta) this.baseDao.get(TransMeta.class, stepId);
        DataSetMeta output = trans.getOutDataSetMeta();
        if (!Strings.isNullOrEmpty(filterExpress)) {
            String[] splitParams = filterExpress.split(":");
            if (splitParams.length == 2) {
                ModelElement model = (ModelElement) this.baseDao.get(ModelElement.class, splitParams[1]);
                if (null != model) {
                    if ("RuleGraph".equals(model.getType())) {
                        output.setFilterExpress("RULE_EXPRESSION");
                    }
                }
            }

        }
//        output.setFilterExpress("RULE_EXPRESSION");
        trans.setOutDataSetMeta(output);
        DataColumnMeta dataColumnMeta = output.getDataColumnMetas().get(field);
        if (dataColumnMeta == null) {
//            throw new NullPointerException("找不到字段[" + field + "]");
            log.warn("找不到字段[{}], 请确认输入数据源是否变更！", field);
        } else {
            dataColumnMeta.setDataLength(length);
            dataColumnMeta.setFilterExpress(filterExpress);
            dataColumnMeta.setPrecsn(precsn);
            dataColumnMeta.setUniqueValue(uniqueValue);
            StandardType stdType = StandardTypeFactory.createType(valType, length, precsn);
            DataType columnType = this.classifierService.getStandardTypeClassifier(stdType);
            dataColumnMeta.setColumnType(columnType);
            this.baseDao.saveOrUpdate(trans);
        }
    }

    private List<Map<String, Object>> getTransDirRootList(String dirType, String userId) {
        List<Map<String, Object>> list = new ArrayList<>();

        /*String sql = "select t2.id as id , t2.NAME, t.BUSI_DIR_TYPE as dirtype from t_md_busi_dir t,t_md_element t2 where t.ID=t2.ID and t.BUSI_DIR_TYPE in";
        if(dirType.equals("TRANS_DIR_MF")){
            sql += "('" + dirType + "','TRANS_DIR_STANDARD')";
        }else if(dirType.equals("DIG_DIR_MF")){
            sql += "('" + dirType + "','DIG_DIR_STANDARD')";
        }*/
        String sql = "select id,name, code as dirType, operate_time AS operateTime from t_md_busi_classify where busi_dir_id =(select id from t_md_busi_dir where code ='" + dirType + "') " +
                "and operate_user_id ='" + userId + "' and code in('TRANS_DIR_MF_MY','TRANS_DIR_STANDARD','DIG_DIR_STANDARD','DIG_DIR_MF_MY','TRANS_DIR_MF_CASE')";
        List<Map> searchList = this.baseDao.sqlQueryForList(sql);
        Map<String, Object> tempMap;
        List<Map<String, Object>> childrenList;
        BusiClassify classify = new BusiClassify();
        classify.getName();
        String sonSql = baseSql;
        if (StringUtils.isNotBlank(userId)) {
            sonSql = sonSql + " AND t1.operate_user_id ='" + userId + "' ";
        }
        sonSql += " order by t1.operate_time desc";
        for (Map map : searchList) {
            if (map.get("name").equals("我的空间") || (standModel && map.get("name").equals("标准模型")) || (sceModel && "场景案例".equals(map.get("name")))) {
                String name = String.valueOf(map.get(nodeName));
                String dirTypeStr = String.valueOf(map.get("dirtype"));
                String id = String.valueOf(map.get("id"));
                String operateTime = String.valueOf(map.get("operatetime"));
                tempMap = Maps.newHashMap();
                tempMap.put(nodePid, "-1");
                tempMap.put(nodeName, name);
                tempMap.put(nodeOpen, true);
                tempMap.put(nodeId, id);
                tempMap.put(nodeDirType, dirTypeStr);
                tempMap.put(nodeTime, operateTime);
                tempMap.put(currentUserId,userId);
                childrenList = new ArrayList<>();
                Map<String, Object> param = new HashMap<>();
                List<Map<String, Object>> sonList = new ArrayList<>();
                param.put("pid", id);
                sonList = baseDao.sqlQueryForList(sonSql, param);
                List<Map<String, Object>> newSonList;
            /*if (dirType.equals("TRANS_DIR_MF")) {
                //过滤出特殊的  代表我的目录的子类  在用户第一次登陆添加 updataLoginNumber Number=0
                Map<String, Object> myMap = sonList.stream().filter(s -> s.get("code").equals("TRANS_DIR_MF_MY")).collect(Collectors.toList()).get(0);
                tempMap.put(nodeId, myMap.get("id"));
                //去掉代表我的目录的子类
            } else {
                //过滤出特殊的  代表我的目录的子类  在用户第一次登陆添加 updataLoginNumber Number=0
                Map<String, Object> myMap = sonList.stream().filter(s -> s.get("code").equals("DIG_DIR_MF_MY")).collect(Collectors.toList()).get(0);
                tempMap.put(nodeId, myMap.get("id"));
                //去掉代表我的目录的子类
                newSonList = sonList.stream().filter(s -> !s.get("code").equals("DIG_DIR_MF_MY")).collect(Collectors.toList());
            }*/


                if (sonList.size() > 0) {
                    tempMap.put(nodeParent, true);
                    this.assembleChildNodes(sonList, childrenList, tempMap, dirTypeStr, userId);
                } else {
                    tempMap.put(nodeParent, true);
                }
                tempMap.put(nodeChildren, childrenList);

                list.add(tempMap);
            }
        }
        return list;
    }

    /*组装子目录节点*/
    private void assembleChildNodes(List<Map<String, Object>> classifyList, List<Map<String, Object>> result, Map<String, Object> parentMap, String dirType, String userId) {
        if (classifyList.size() == 0) {
            return;
        }
        String sql = baseSql.concat(" AND t1.PARENT_CLASSIFY_ID =:parentId ");
        if (StringUtils.isNotBlank(userId)) {
            sql = sql + " AND t1.operate_user_id ='" + userId + "' ";
        }
        String id;
        String name;
        String dirTypeStr;
        String opereteTime;
        Map<String, Object> nodeMap;
        List<Map<String, Object>> childrenList;
        List<Map<String, Object>> sonList;
        String pId = parentMap.get(nodeId).toString();
        for (Map<String, Object> map : classifyList) {
            dirTypeStr = String.valueOf(map.get("dirtype"));
            name = String.valueOf(map.get(nodeName));
            if (!dirTypeStr.equals("DIG_DIR_STANDARD") && !name.equals("标准模型")) {
                id = String.valueOf(map.get(nodeId));
                opereteTime = String.valueOf(map.get("operatetime"));

                nodeMap = Maps.newHashMap();
                nodeMap.put(nodeId, id);
                nodeMap.put(nodeName, name);
                nodeMap.put(nodePid, pId);
                nodeMap.put(nodeOpen, false);
                nodeMap.put(nodeParent, true);
                nodeMap.put(nodeDirType, dirTypeStr);
                nodeMap.put(nodeTime, opereteTime);
                nodeMap.put(currentUserId,userId);
                childrenList = new ArrayList<>();
                Map<String, Object> param = new HashMap<>();
                param.put("parentId", id);
                param.put("pid", id);
                sonList = baseDao.sqlQueryForList(sql, param);
                if (sonList.size() > 0) {
                    nodeMap.put(nodeParent, true);
                } else {
                    nodeMap.put(nodeParent, false);
                }
                nodeMap.put(nodeChildren, childrenList);
                result.add(nodeMap);
                this.assembleChildNodes(sonList, childrenList, nodeMap, dirTypeStr, userId);
            }
        }
    }

    @Override
    public Map<String, Object> listTransInfo(String condition, String state, String dirId, String dirType, Integer pageNum, Integer pageSize, String userId) {
        Map<String, Object> result;
        String sql = "select name from t_md_busi_classify where id='" + dirId + "'";
        String s = baseDao.sqlQueryForValue(sql);
        if ("-1".equals(dirId)) {//根目录
            result = this.listRootTrans(null, state, condition, dirType, pageNum, pageSize, userId);
        } else if (("我的空间").equals(s) || ("标准模型").equals(s)) {

            result = this.listRootTrans(dirId, condition, state, dirType, pageNum, pageSize, userId);

        } else { //单个文件夹
            result = this.listTransByDirId(condition, state, dirId, pageNum, pageSize, userId);
        }
        List<Map> data = (List<Map>) result.get("data");
        String sql1 = " select count(*) from t_md_service_publication where source_id = :sourceId and id in (select element_id from t_md_classify_element)";
        data.forEach(t -> {
            t.put("isReference", false);
            String id = (String) t.get("id");
            if (StrUtil.isNotEmpty(id)) {
                String value = this.baseDao.sqlQueryForValue(sql1, this.addParam("sourceId", id).param());
                int count = Integer.parseInt(value);
                if (count > 0) {
                    t.put("isReference", true);
                }
            }
        });
        return result;
    }

    private List<String> allDirIds(List<Map<String, String>> idMap) {
        List<String> allIds = new ArrayList<>();
        if (idMap!=null&&idMap.size() > 0) {
            for (Map<String, String> map : idMap) {
                String id = map.get("id");
                allIds.add(id);
                List<Map<String, String>> list = this.baseDao.sqlQueryForList("select id from t_md_busi_classify where parent_classify_id = :dirId",
                        this.addParam("dirId", id).param());
                allIds.addAll(allDirIds(list));
            }
        }
        return allIds;
    }

    // 获取根目录下的所有过程集合
    private Map<String, Object> listRootTrans(String dirId, String condition, String state, String dirType, Integer pageNum, Integer pageSize, String userId) {
        Map<String, Object> result = Maps.newHashMap();
        Map<String, Object> params = Maps.newHashMap();


        List<Map<String, String>> secondDirId = this.baseDao.sqlQueryForList("select id from t_md_busi_classify where parent_classify_id = :dirId", this.addParam("dirId", dirId).param());
        List<String> list = allDirIds(secondDirId);
        list.add(dirId);

        //我猜当初为什么前人在这边会手写一个分页去统计，是因为sqlQueryForPage里面会把order by 替换掉，现在做了骚操作，ORDER by 大小写，就不会替换了
       /* // 数量统计

        StringBuffer countSb = new StringBuffer();
        countSb.append("SELECT COUNT(a.id) as countNum ");
        countSb.append("  FROM t_etl_trans a, t_md_classify_element b, ");
        String dirCodes = "";
        if (dirType.equals("DIG_DIR_MF") || dirType.equals("DIG_DIR_STANDARD")) {
            dirCodes = "'DIG_DIR_MF','DIG_DIR_STANDARD'";
        } else if (dirType.equals("TRANS_DIR_MF") || dirType.equals("TRANS_DIR_STANDARD")) {
            dirCodes = "'TRANS_DIR_MF','TRANS_DIR_STANDARD'";
        }
        countSb.append("(").append("SELECT t.ID as id,t.NAME as name,t2.CODE as dirtype ,t1.code as code FROM t_md_element t,  t_md_busi_classify t1, t_md_element t2  WHERE t.ID = t1.ID  AND t2.ID=t1.BUSI_DIR_ID AND t2.TYPE='BusiDir' AND t2.CODE in (" + dirCodes + ")");
        if (StringUtils.isNotBlank(dirId)) {
            countSb.append("and ( parent_classify_id='" + dirId + "'" + " or t.id='" + dirId + "'" + " )");
        }

        countSb.append(") c");
        countSb.append("  WHERE a.trans_type = 'TRANSFORM' AND a.id = b.element_id ");
        countSb.append("  AND b.busi_classify_id =c.id ");
        countSb.append("  AND a.operate_user_id =:userId ");
        countSb.append(" AND position('预览_preview' in a.name) = 0");  //排除转换插件预览产出的方案
        countSb.append(" AND position('发布_publish' in a.name) = 0");  //排除方案发布产出的方案
        if (StringUtils.isNotBlank(condition)) {
            countSb.append(" AND upper(a.name) like :condition ");
            params.put("condition", "%" + condition.toUpperCase() + "%");
        }
        params.put("userId", userId);

        int countNum = Integer.valueOf(baseDao.sqlQueryForValue(countSb.toString(), params));
        if (countNum == 0) {
            List<TransVo> list = new ArrayList<>();
            result.put("totalCount", countNum);
            result.put("pageNum", 1);
            result.put("totalPage", 1);
            result.put("data", list);
            return result;
        }
        int totalPageNum = countNum % pageSize == 0 ? countNum / pageSize : (countNum / pageSize + 1);
        pageNum = pageNum > totalPageNum ? totalPageNum : pageNum;
        int start = (pageNum - 1) * pageSize;*/

        // 查询数据
        StringBuffer sb = new StringBuffer();
        String dirCodes = "";
        if (dirType.equals("DIG_DIR_MF") || dirType.equals("DIG_DIR_STANDARD")) {
            dirCodes = "'DIG_DIR_MF','DIG_DIR_STANDARD'";
        } else if (dirType.equals("TRANS_DIR_MF") || dirType.equals("TRANS_DIR_STANDARD")) {
            dirCodes = "'TRANS_DIR_MF','TRANS_DIR_STANDARD'";
        }
        if (StringUtils.isNotBlank(state)) {
            sb.append("SELECT DISTINCT M.* FROM (");
        }
        sb.append("SELECT a.id as id, a.name as name, d.name as classifyName,  a.operate_user_id,a.operate_time as releaseTime," +
                "a.task_group as taskGroup,a.version as version,a.production_firm as productionFirm, b.busi_classify_id as parentId ");
        sb.append(" FROM t_etl_trans a, t_md_classify_element b, ");
        sb.append("(").append("SELECT t.ID as id,t.NAME as name,t2.CODE as dirtype ,t1.code as code FROM t_md_element t,  " +
                "t_md_busi_classify t1, t_md_element t2  WHERE t.ID = t1.ID  AND t2.ID=t1.BUSI_DIR_ID AND t2.TYPE='BusiDir' AND t2.CODE in (" + dirCodes + ")");
        //为什么要注释，因为在最顶层的目录要显示所有的儿子孙子目录下的方案，所以去掉这个过滤
        if (StringUtils.isNotBlank(dirId)) {
            //sb.append("and ( parent_classify_id='" + dirId + "'" + " or t.id='" + dirId + "'" + " )");
            sb.append("and ( parent_classify_id in (:list) or t.id='" + dirId + "'" + " )");
            params.put("list", list);
        }
        sb.append(") d");
        sb.append(" WHERE a.trans_type = 'TRANSFORM' AND a.id = b.element_id ");
        sb.append("  AND b.busi_classify_id =d.id  ");
        if (StringUtils.isNotBlank(userId)) {
            sb.append("  AND a.operate_user_id =:userId ");
            params.put("userId", userId);
        }

        sb.append(" AND position('预览_preview' in a.name) = 0");  //排除转换插件预览产出的方案
        sb.append(" AND position('发布_publish' in a.name) = 0");  //排除方案发布产出的方案
        if (StringUtils.isNotBlank(condition)) {
            sb.append(" AND upper(a.name) like :condition ");
            params.put("condition", "%" + condition.toUpperCase() + "%");
        }
        sb.append(" ORDER by operate_time desc");
        //方案运行状态
        if (StringUtils.isNotBlank(state)) {
            if (state.equals("FAILED")) {
                sb.append(" ) M where (select count(1) from t_trans_job k where M.ID=k.trans_id)=0");
            } else {
                sb.append(" ) M left join (select * from " +
                        "(select *,Row_Number() OVER (partition by trans_id ORDER by start_time DESC) as rank from t_trans_job )" +
                        " job where job.rank = 1) aa on M.id=aa.trans_id where aa.execute_status='" + state + "'");
            }
        }
        //sb.append(" limit " + pageSize).append(" offset " + start);

        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageSize(pageSize);
        pageInfo.setPageIndex(pageNum);

        PageInfo pageResult = baseDao.sqlQueryForPage(sb.toString(), params, pageInfo);

//        UserVo userVo = userService.queryUserById(userId);

        List<Map> dataList = pageResult.getDataList();
        List<String> userIds = new ArrayList<>();
        if (StringUtils.isNotBlank(userId)) {
            userIds.add(userId);
        } else {
            //operate_user_id
            userIds = dataList.stream().filter(m -> null != m.get("operate_user_id")).map(m -> String.valueOf(m.get("operate_user_id"))).collect(Collectors.toList());
        }
        Map<String, String> userMap = userService.getUserMap(userIds);
        for (Map transVo : dataList) {
            transVo.put("userName", "");
            if (null != transVo.get("operate_user_id")) {
                transVo.put("userName", userMap.get(transVo.get("operate_user_id")));
            }
        }

        //组装方案所属目录
        List<Map> enscList = enscapTransBelongtoDir(dataList);
        //组装结果
        result.put("totalCount", pageResult.getTotalCount());
        result.put("pageNum", pageNum);
        result.put("totalPage", pageResult.getPageCount());
        result.put("data", enscList);
        return result;
    }

    @Override
    public  Map<String, String> getEtlTransUsers(String userId) {

        String sql = "select distinct a.operate_user_id from t_etl_trans a inner join t_md_classify_element b on a.id = b.element_id " +
                " where a.task_group != 'QUICK_SPARK' and a.operate_user_id !=:userId  and a.operate_user_id is not null" ;
        List<Map<String, String>> mapList = baseDao.sqlQueryForList(sql, addParam("userId", userId).param());
        if (CollUtil.isEmpty(mapList)) {
            return Maps.newHashMap();
        }
        List<String> userIds = new ArrayList<>();
        mapList.forEach(map -> userIds.add(map.get("operate_user_id")));
        return userService.getUserMap(userIds);
    }




    //通过名称和文件夹id获取过程集合
    private Map<String, Object> listTransByDirId(String condition, String state, String dirId, Integer pageNum, Integer pageSize, String userId) {
        Map<String, Object> result = Maps.newHashMap();

        Map<String, Object> params = Maps.newHashMap(); //查询参数
        /*if (!"-2".equals(dirId)) {
            params.put("parentId", dirId);
        }*/
       /* //统计数量
        StringBuffer countSb = new StringBuffer();
        countSb.append("SELECT COUNT(a.id) as countNum ");
        countSb.append("  FROM t_etl_trans a, t_md_classify_element b,t_md_busi_classify c ");
        countSb.append("  WHERE a.trans_type = 'TRANSFORM' AND a.id = b.element_id and b.busi_classify_id=c.id");
        if (!"-2".equals(dirId)) {
            countSb.append("   AND ( b.busi_classify_id =:parentId or c.parent_classify_id =:parentId ) ");
        }
        countSb.append(" AND position('预览_preview' in a.name) = 0");  //排除转换插件预览产出的方案
        countSb.append(" AND position('发布_publish' in a.name) = 0");  //排除方案发布产出的方案
        if (StringUtils.isNotBlank(condition)) {
            countSb.append(" AND upper(a.name) like :condition ");
            params.put("condition", "%" + condition.toUpperCase() + "%");
        }
        int countNum = Integer.valueOf(baseDao.sqlQueryForValue(countSb.toString(), params));
        if (countNum == 0) {
            List<TransVo> list = new ArrayList<>();
            result.put("totalCount", countNum);
            result.put("pageNum", 1);
            result.put("totalPage", 1);
            result.put("data", list);
            return result;
        }
        int totalPageNum = countNum % pageSize == 0 ? countNum / pageSize : (countNum / pageSize + 1);
        pageNum = pageNum > totalPageNum ? totalPageNum : pageNum;
        int start = (pageNum - 1) * pageSize;*/

        List<String> classifyIdList = commonBusiClassifyService.queryBusiClassifyIdList(dirId);
        List<String> elementIdsByClassifyIds = commonBusiClassifyService.getElementIdsByClassifyIds(classifyIdList);
        if (CollectionUtil.isEmpty(elementIdsByClassifyIds)) {
            result.put("totalCount", 0);
            result.put("pageNum", pageNum);
            result.put("totalPage", 0);
            result.put("data", new ArrayList<>());
            return result;
        }
        params.put("elementIdsByClassifyIds", elementIdsByClassifyIds);
        // 查询数据
        StringBuffer sb = new StringBuffer();
        if (StringUtils.isNotBlank(state)) {
            sb.append("SELECT DISTINCT M.* FROM (");
        }
        sb.append("SELECT a.id as id, a.name as name,  a.operate_user_id,a.operate_time as releaseTime,a.task_group as taskGroup,a.version as version,a.production_firm as productionFirm, b.busi_classify_id as parentId, d.name as classifyName");
        sb.append(" FROM t_etl_trans a, t_md_classify_element b,  t_md_busi_classify d ");
        sb.append(" WHERE a.trans_type = 'TRANSFORM' AND a.id = b.element_id and a.id in (:elementIdsByClassifyIds) AND  d.id = b.busi_classify_id ");
        /*if (!"-2".equals(dirId)) {
            sb.append("  AND b.busi_classify_id =:parentId ");
        }*/
        sb.append(" AND position('预览_preview' in a.name) = 0");  //排除转换插件预览产出的方案
        sb.append(" AND position('发布_publish' in a.name) = 0");  //排除方案发布产出的方案
        if (StringUtils.isNotBlank(condition)) {
            sb.append(" AND upper(a.name) like :condition");
            params.put("condition", "%" + condition.toUpperCase() + "%");
        }
//        if (!userInfo.isAdmin()) { // 非管理员只能看到自己创建的方案
//            sb.append(" AND a.create_by = '").append(userInfo.getUserId()).append("' ");
//        }
        sb.append(" ORDER by a.operate_time desc");
        //sb.append(" limit " + pageSize).append(" offset " + start);

        if (StringUtils.isNotBlank(state)) {
            if (state.equals("FAILED")) {
                sb.append(" ) M where (select count(1) from t_trans_job k where M.ID=k.trans_id)=0");
            } else {
                sb.append(" ) M left join (select * from (select *,Row_Number() OVER (partition by trans_id ORDER by end_time DESC) as rank from t_trans_job ) job where job.rank = 1) aa on M.id=aa.trans_id where aa.execute_status='" + state + "'");
            }
        }

        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageSize(pageSize);
        pageInfo.setPageIndex(pageNum);
        PageInfo resultPage = baseDao.sqlQueryForPage(sb.toString(), params, pageInfo);
//        UserVo userVo = userService.queryUserById(userId);
        List<Map> dataList = resultPage.getDataList();
        long totalCount = resultPage.getTotalCount();
        long pageCount = resultPage.getPageCount();
        List<String> userIds = new ArrayList<>();
        if (StringUtils.isNotBlank(userId)) {
            userIds.add(userId);
        } else {
            //operate_user_id
            userIds = dataList.stream().filter(m -> null != m.get("operate_user_id")).map(m -> String.valueOf(m.get("operate_user_id"))).collect(Collectors.toList());
        }
        Map<String, String> userMap = userService.getUserMap(userIds);
        for (Map transVo : dataList) {
            transVo.put("userName", "");
            if (null != transVo.get("operate_user_id")) {
                transVo.put("userName", userMap.get(transVo.get("operate_user_id")));
            }
        }

        //这边代码太抽象了 前端如果传pageSize是10 会变成子目录和该目录都查十条拼一起
        //在本级目录同时显示子级的目录
      /*  String hql = "from BusiClassify where parentBc.id = :id";
        List<BusiClassify> bcs = this.baseDao.queryForList(hql, addParam("id", dirId).param());
        for (BusiClassify bc : bcs) {
            Map<String, Object> stringObjectMap = listTransByDirId(condition,state, bc.getId(), pageNum, pageSize, userId);
            List<Map> temp = CollectionCastUtil.castList(stringObjectMap.get("data"), Map.class);
            dataList.addAll(temp);
            totalCount += (long) stringObjectMap.get("totalCount");
            pageCount += (long) stringObjectMap.get("totalPage");
        }*/

        //组装方案所属目录
        List<Map> enscList = enscapTransBelongtoDir(dataList);

        //组装结果
        result.put("totalCount", totalCount);
        result.put("pageNum", pageNum);
        result.put("totalPage", resultPage.getPageCount());
        result.put("data", enscList);
        return result;
    }

    private List<Map> enscapTransBelongtoDir(List<Map> list) {

        for (Map map : list) {
            String transId = map.get("id").toString();
            //根据方案id，得到属于哪个目录
            BaseBusiClassify transBelongtoClassify = getTransBelongtoClassifyId(transId);
            List<String> pathList = new ArrayList<>();
            //pathList 递归去存放爷爷父亲的目录
            pathList.add(transBelongtoClassify.getName());
            getTransBelongToClassifyPath(transBelongtoClassify, pathList);
            //将集合的元素倒序 拼装
            Collections.reverse(pathList);
            String path = String.join("/", pathList);
            map.put("path", path);
        }
        return list;
    }

    private BaseBusiClassify getTransBelongtoClassifyId(String transId) {
        String sql = "select busi_classify_id from t_md_classify_element where element_id = :id";
        String busiClassifyId = this.baseDao.sqlQueryForValue(sql, addParam("id", transId).param());
        return this.busiClassifyService.findBusiClassifyBy(busiClassifyId);
    }

    private void getTransBelongToClassifyPath(BaseBusiClassify busiClassify, List<String> paths) {
        BaseBusiClassify parentBc = busiClassify.getParentBc();
        if (parentBc != null) {
            paths.add(parentBc.getName());
            getTransBelongToClassifyPath(parentBc, paths);
        }
    }

    @Override
    public String saveTempTrans(String transName, String transType, HttpServletRequest request) {
        TransMeta transMeta = new TransMeta();
        transMeta.setCode(transName);
        transMeta.setName(transName);
        transMeta.setDistributed("0");
        transMeta.setExceptionMode(TransMeta.EnumHandleMode.DEF.getCode());
        transMeta.setEtlInstanceCode("");
        transMeta.setOperateUserId(UserContextUtil.getUserIdByHttpRequest(request));
        if (StringUtils.isNotBlank(transType) && transType.equalsIgnoreCase(QUICK_SPARK)) {
            transMeta.setTaskGroup(QUICK_SPARK);
        }
        // 获取根目录id
        String dirSql = "SELECT id FROM t_md_busi_dir WHERE code='TRANS_DIR_MF' AND type = 'BusiDir'";
        String dirId = this.baseDao.sqlQueryForValue(dirSql);
        ModelElement element = new ModelElement();
        element.setId(dirId);
        transMeta.setOwner(element);
        transMeta.setTransType(EnumTransType.TRANSFORM.getName());
        this.baseDao.saveOrUpdate(transMeta);
//        transMetaService.saveTransMeta(transMeta);
//        transMetaService.saveTransMeta(transMeta);
//        mistTransformScheduleService.createJob(transMeta.getId());

        SimpleJobInfo scheduleMeta = new SimpleJobInfo();
        scheduleMeta.setPrioritie(5);
        scheduleMeta.setBeginTime(this.baseDao.getCurrentTimeString());
        scheduleMeta.setJobType("etlJob");
        scheduleMeta.setName(transMeta.getCode());
        scheduleMeta.setScheduleId(transMeta.getEtlInstanceCode());
        scheduleMeta.setJobStrategyId(transMeta.getId());
        scheduleMeta.setSchemeScheduleMode(ScheduleModeType.HAND.getValue());
       /* Message msg = jobMetaConfiger.createJobMeta(scheduleMeta);
        if (Message.ERROR.equals(msg.getCode())) {
            log.error(msg.getMsg());
        }*/
        return transMeta.getId();
    }

    @Override
    public Boolean checkExistByName(String classifyId, String transName) {
        String sql = "SELECT a.id as id FROM t_etl_trans a INNER JOIN t_md_classify_element b ON a.\"id\" = b.element_id" +
                " WHERE b.busi_classify_id = :classifyId and a.name=:transName ";
        Map<String, Object> param = new HashMap<>();
        param.put("classifyId", classifyId);
        param.put("transName", transName);
        List<Map<String, Object>> list = baseDao.sqlQueryForList(sql, param);
        return list.size() > 0;
    }

    @Override
    public Map<String, List<Map<String, String>>> listLocalData(String condition, String dataTypeId) {

        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append("select b.id,a.clssify_type \"type\",a.classfy_detail_type icon,b.\"type\" dbType,b.name,b.code from t_dmc_data_object a left join t_md_classifier_stat b on a.classifier_stat_id = b.id where a.if_enable = '1'");

        List<Map<String, String>> localList = this.baseDao.sqlQueryForList(stringBuffer.toString());
        Map<String, List<Map<String, String>>> result = localList.stream().collect(Collectors.groupingBy(c -> c.get("type")));
        return result;
    }

//    @Override
//    public List<Map<String, Object>> listExternalFile(String condition, Boolean hasAbroad) {
//        List<Map<String, Object>> result = new ArrayList<>();
//        String[] classifyCodes = new String[3];
//        classifyCodes[0] = "excelFile";
//        classifyCodes[1] = "csvFile";
//        String[] classifyNames = new String[3];
//        classifyNames[0] = "EXCEL格式";
//        classifyNames[1] = "CSV格式";
//        String[] classifyTypes = new String[3];
//        classifyTypes[0] = "EXCEL";
//        classifyTypes[1] = "CSV";
//        if (!hasAbroad) {
//            classifyCodes[2] = "sqlFile";
//            classifyNames[2] = "sql数据";
//            classifyTypes[2] = "sql";
//        }
//        Map<String, Object> classifyMap;
//        List<Map<String, Object>> childrenList;
//        for (int i = 0; i < classifyCodes.length; i++) {
//            if (StringUtils.isNotBlank(classifyCodes[i])) {
//                childrenList = new ArrayList<>();
//                classifyMap = Maps.newHashMap();
//                classifyMap.put(nodeId, classifyCodes[i]);
//                classifyMap.put(nodeCode, classifyCodes[i]);
//                classifyMap.put(nodeName, classifyNames[i]);
//                classifyMap.put(nodeEmType, classifyCodes[i]);
//                classifyMap.put(nodeParent, true);
//                classifyMap.put(nodeOpen, true);
//                classifyMap.put(nodePid, "-1");
//                classifyMap.put(nodeChildren, childrenList);
//                this.listExternalLeafList(condition, classifyTypes[i], childrenList);
//                result.add(classifyMap);
//            }
//        }
//
//        return result;
//    }

    private void localLeafList(String condition, String classifyId, List<Map<String, Object>> childrenList) {
        String sql = "SELECT c.id as id, c.name as name, c.global_code as code, c.db_type as dbtype, a.classfy_detail_type as icon FROM t_dmc_data_object a, t_md_busi_classify b, t_md_classifier_stat c, t_md_classify_element d WHERE a.classifier_stat_id = c.id AND b.id = d.busi_classify_id " +
                "AND c.id = d.element_id AND  b.id =:classifyId AND a.if_enable ='1'";
        if (StringUtils.isNotBlank(condition)) {
            sql += " AND upper(c.name) like '%" + condition.toUpperCase() + "%' ";
        }
        sql += " order by a.sort_no";
        Map<String, Object> param = new HashMap<>();
        param.put("classifyId", classifyId);
        List<Map<String, Object>> list = baseDao.sqlQueryForList(sql, param);
        Map<String, Object> tempMap;
        for (Map<String, Object> map : list) {
            tempMap = Maps.newHashMap();
            tempMap.put(nodeId, map.get(nodeId));
            tempMap.put(nodeName, map.get(nodeName));
            tempMap.put(nodeCode, map.get(nodeCode));
            tempMap.put(nodeIcon, map.get(nodeIcon));
            tempMap.put(nodePid, classifyId);
            tempMap.put(nodeType, "1");
            tempMap.put(nodeEmType, "DATASET_OBJ_" + map.get("dbtype"));
            tempMap.put(nodeParent, false);
            tempMap.put(nodeChildren, new ArrayList<Map<String, Object>>());
            childrenList.add(tempMap);
        }
    }

    /*外部文件信息组装*/
    private void listExternalLeafList(String condition, String type, List<Map<String, Object>> childrenList) {
        String sql = "";
        List<Map<String, String>> tempClassifierList;
        try {
            if ("sql".equals(type)) {
                sql = "SELECT id, name, global_code as code,db_type as emtype FROM t_md_classifier_stat WHERE type = 'SqlDataObj'";
                if (StringUtils.isNotBlank(condition)) {
                    sql += " and a.name like '%" + condition + "%'";
                }
                tempClassifierList = baseDao.sqlQueryForList(sql);
            } else {
                sql = "SELECT DISTINCT  a.id as id, a.name as name, a.global_code as code,a.db_type as emtype from t_md_compare_result_dataobj a where not  " +
                        "EXISTS(select 1 from t_md_classify_element b ," +
                        " t_md_busi_classify c,t_md_busi_dir D where b.busi_classify_id = c.id AND " +
                        "  C.busi_dir_id = D.id and D.CODE = 'DATASET_DIR' and a.id =b.element_id ) and a.file_source = '2'" +
                        "  and file_type =:type ";
//                if (StringUtils.isNotBlank(userId)) {
//                    sql += " and a.create_user_id = '" + userId + "'";
//                }
                if (StringUtils.isNotBlank(condition)) {
                    sql += " and a.name like '%" + condition + "%'";
                }
                sql += " order by code";
                Map<String, Object> param = new HashMap<>();
                param.put("type", type);
                tempClassifierList = baseDao.sqlQueryForList(sql, param);
            }

            Map<String, Object> tempMap;
            for (Map<String, String> map : tempClassifierList) {
                tempMap = Maps.newHashMap();
                tempMap.put(nodeId, map.get(nodeId));
                tempMap.put(nodeName, map.get(nodeName));
                tempMap.put(nodeCode, map.get(nodeCode));
                tempMap.put(nodePid, type.toLowerCase() + "File");
                tempMap.put(nodeParent, false);
                tempMap.put(nodeOpen, false);
                tempMap.put(nodeEmType, "DATASET_OBJ_" + map.get("emtype"));
                tempMap.put(nodeChildren, new ArrayList<>());
                childrenList.add(tempMap);
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }

    @Override
    public List<SQLModelVo> listResultReuse(String condition, String dateType) {
        List<SQLModelVo> result = new ArrayList<>();

        //查询树的一级节点
        String dirSQL = "select id from t_md_busi_dir where code = 'SQL_DATA_DIR' and name ='我的空间'";
//        List<Map> dirMap = this.baseDao.sqlQueryForList(dirSQL);
        Map dirMap = this.baseDao.sqlQueryForMap(dirSQL);
        //查询树的二级节点
        String childSQL = "select id,name,code from t_md_busi_classify where busi_dir_id = :dirId";

        String modelSQL = "select e.id,e.name,e.code,c.busi_classify_id,e.global_code from t_md_sql_dataobj e left join t_md_classify_element c on e.id = c.element_id where e.type = 'SqlDataObj'";

        List<Map> modelMap = this.baseDao.sqlQueryForList(modelSQL);
        SQLModelVo dirVo = new SQLModelVo();
        dirVo.setId(dirMap.get("id").toString());
        dirVo.setName("跨库数据");
        dirVo.setCode("TempDataSet");
        dirVo.setParentId("-1");

        Map<String, Object> param = new HashMap<>();
        param.put("dirId", dirMap.get("id"));
        List<Map> childNode = this.baseDao.sqlQueryForList(childSQL, param);

        List<SQLModelVo> childList = new ArrayList<>();
        for (Map child : childNode) {
            SQLModelVo childVo = new SQLModelVo();
            childVo.setParentId(dirVo.getId());
            childVo.setId(child.get("id").toString());
            childVo.setName(child.get("name").toString());
            childVo.setCode(child.get("code").toString());
            childList.add(childVo);

            List<SQLModelVo> modelList = new ArrayList<>();
            for (Map model : modelMap) {
                SQLModelVo modelVo = new SQLModelVo();
                if (model.get("busi_classify_id").toString().equals(childVo.getId())) {
                    modelVo.setId(model.get("id").toString());
                    modelVo.setCode(model.get("code").toString());
                    modelVo.setName(model.get("name").toString());
                    modelVo.setGlobal_code(model.get("global_code") == null ? "" : model.get("global_code").toString());
                    modelVo.setParentId(childVo.getId());
                    modelList.add(modelVo);
                }
            }
            childVo.setChildren(modelList);
        }
        dirVo.setChildren(childList);
        result.add(dirVo);
        return result;


//        Map<String, Object> map = Maps.newHashMap();
//        List<Map<String, Object>> childrenList = new ArrayList<>();
//        map.put(nodeId, "TempDataSet");
//        map.put(nodeCode, "TempDataSet");
//        map.put(nodeName, "结果复用");
//        map.put(nodeEmType, "TempDataSet");
//        map.put(nodeParent, true);
//        map.put(nodeOpen, true);
//        map.put(nodeChildren, childrenList);
//        map.put(nodePid, "-1");
//        this.listResultLeaf(condition, dateType, childrenList);
//        result.add(map);
//        return result;
    }

//    @Override
//    public void deleteTrans(String transId) {
//        String deleteLineageObject = "delete from t_etl_data_lineage_object WHERE trans_id =:transId";
//        String deleteLineageField  = "delete from t_etl_data_lineage_field WHERE trans_id =:transId";
//        this.baseDao.executeSqlUpdate(deleteLineageObject, addParam("transId", transId).param());
//        this.baseDao.executeSqlUpdate(deleteLineageField, addParam("transId", transId).param());
//    }

    /*结果复用信息获取*/
    private void listResultLeaf(String condition, String dateType, List<Map<String, Object>> childrenList) {
        // 时间范围
        String dateStr = "";
        if (!"0".equals(dateType)) {
            Calendar calendar = Calendar.getInstance();
            if ("3".equals(dateType)) {
                calendar.add(Calendar.MONTH, -1);
            }
            if ("2".equals(dateType)) {
                calendar.add(Calendar.DATE, -7);
            }
            dateStr = sdf.format(calendar.getTime());
        }
        String sql = "SELECT DISTINCT a.id as id, a.name as name, a.global_code as code, a.db_type as emtype from " +
                " t_md_compare_result_dataobj a where not  EXISTS " +
                " (select 1 from t_md_classify_element b ,t_md_busi_classify c,t_md_busi_dir D where " +
                " b.busi_classify_id = c.id AND C.busi_dir_id = D.id and a.id =b.element_id and d.code = 'DATASET_DIR' ) " +
                " and a.type = 'CompareResultDataObj'  and a.file_source = '1' ";
        if (StringUtils.isNotBlank(dateStr)) {
            sql += " and last_update_time >= '" + dateStr + "'";
        }
        if (StringUtils.isNotBlank(condition)) {
            sql += " and a.name like '%" + condition + "%'";
        }
        sql += " order by code";

        List<Map<String, Object>> list = baseDao.sqlQueryForList(sql);

        Map<String, Object> tempMap;
        String pid = "TempDataSet";
        for (Map<String, Object> map : list) {
            tempMap = Maps.newHashMap();
            tempMap.put(nodeId, map.get(nodeId));
            tempMap.put(nodeName, map.get(nodeName));
            tempMap.put(nodeCode, map.get(nodeCode));
            tempMap.put(nodeEmType, "DATASET_OBJ_" + map.get("emtype"));
            tempMap.put(nodePid, pid);
            tempMap.put(nodeParent, false);
            tempMap.put(nodeOpen, false);
            tempMap.put(nodeChildren, new ArrayList<>());
            childrenList.add(tempMap);
        }
    }

    @Override
    public List<Map<String, String>> getFromTransStepId(String transStepId) {
        String sql = "SELECT id as fromid,x,y FROM t_etl_trans WHERE id in(SELECT from_trans_id as stepid FROM t_etl_trans_hops WHERE to_trans_id =:toTransId) ";
        Map<String, Object> param = new HashMap<>();
        param.put("toTransId", transStepId);
        List<Map<String, String>> list = this.baseDao.sqlQueryForList(sql, param);
        // TODO: 2019/3/29   添加具体得步骤信息
        return list;
    }

    @Override
    public R createTransClassify(String parentBcId, String transClassifyName, String dirType, String userId) {
        return this.createClassify(parentBcId, transClassifyName, null, dirType, userId);
    }

    @Override
    public void createTransClassifyForMy(String parentBcId, String transClassifyName, String transClassifyCode, String dirType, String userId) {
        this.createClassify(parentBcId, transClassifyName, transClassifyCode, dirType, userId);
    }


    private R createClassify(String parentBcId, String transClassifyName, String transClassifyCode, String dirType, String userId) {
        if (busiClassifyService.checkClassifyIsExistByUserId(parentBcId, transClassifyName, EnumBusiType.valueOf(dirType), userId)) {
            return R.error("目录已存在!");
        }
        BaseBusiClassify bsClassify = new BaseBusiClassify();
        bsClassify.setCode(transClassifyCode == null ? dirType : transClassifyCode);
        bsClassify.setName(transClassifyName);
        bsClassify.setOperateUserId(userId);
        if (StringUtils.isNotBlank(parentBcId)) {
            long startTime = System.currentTimeMillis();
            BaseBusiClassify parentBc = (BaseBusiClassify) this.baseDao.load(BaseBusiClassify.class, parentBcId);
            long endTime = System.currentTimeMillis();
            log.info("新建目录耗时：" + (endTime - startTime));
            bsClassify.setParentBc(parentBc);
        }
        BaseBusiDir dir = busiDirService.findEasyBusiDirBy(EnumBusiType.valueOf(dirType));
        bsClassify.setBusiDir(dir);
        this.baseDao.saveOrUpdate(bsClassify);
        return R.ok(bsClassify.getId());
    }


    @Override
    public R updateTransClassify(String classifyId, String classifyName) {
        BaseBusiClassify bc = busiClassifyService.findBusiClassifyBy(classifyId);
        String parentBcId = null;
        if (bc.getParentBc() != null) {
            parentBcId = bc.getParentBc().getId();
        }
        if (busiClassifyService.checkClassifyIsExist(parentBcId, classifyName, EnumBusiType.TRANS_DIR_MF)) {
            return R.error("目录已存在!");
        }
        bc.setCode(classifyName);
        bc.setName(classifyName);
        this.baseDao.update(bc);
        return R.ok();
    }

    @Override
    public R moveDirectory(String curryClassifyId, String newParentClassifyId, String dirType) {
        BaseBusiClassify curryClassify = busiClassifyService.findBusiClassifyBy(curryClassifyId);
        BaseBusiClassify newParentClassify = busiClassifyService.findBusiClassifyBy(newParentClassifyId);
        BaseBusiDir newBaseBusiDir = busiDirService.getBaseBusiDirById(newParentClassifyId);

        if (busiClassifyService.checkClassifyIsExist(newParentClassifyId, curryClassify.getName(), EnumBusiType.valueOf(dirType))) {
            return R.error("目录已存在!");
        }
        //为什么要加这个判断，因为建模空间都是BaseBusiClassify互相移动的，但是数据空间数据连接 BaseBusiClassify可以移动到 BaseBusiDir
        if (newParentClassify != null) {
            curryClassify.setParentBc(newParentClassify);
            curryClassify.setBusiDir(newParentClassify.getBusiDir());
        } else {
            curryClassify.setBusiDir(newBaseBusiDir);
            curryClassify.setParentBc(null);

        }

        this.baseDao.update(curryClassify);
        return R.ok();
    }

    @Override
    public Map getPluginIdByCode(String pluginCode) {
        String sql = "select id from t_md_etl_trans_plugin where code = '" + pluginCode + "'";
        return this.baseDao.sqlQueryForMap(sql);
    }

    @Override
    public Map showInputColumns(String tranStepId) {
        Map result = new LinkedHashMap();
        String[] ids = getPrevStepIds(tranStepId);
        if (ids.length > 0) {
            Map<String, Object> param = new HashMap<>();
            param.put("ids", ids);
            List<TransMeta> transMetas = baseDao.queryForList("from TransMeta WHERE id IN (:ids)", param);
            DataSetMeta output = null;
            Map columnMetas = null;
            TransMeta tm = null;
            for (int i = 0; i < transMetas.size(); i++) {
                tm = transMetas.get(i);
                output = tm.getOutDataSetMeta();
                if (output != null) {
                    columnMetas = output.getDataColumnMetas();
                    Set<Map.Entry> set = columnMetas.entrySet();
                    for (Map.Entry entry : set) {
                        if (result.containsKey(entry.getKey())) {
                            DataColumnMeta dcm = (DataColumnMeta) result.get(entry.getKey());
                            DataColumnMeta eDcm = (DataColumnMeta) entry.getValue();
                            if (dcm.getDataLength() < eDcm.getDataLength()) {
                                result.put(entry.getKey(), entry.getValue());
                            }
                            continue;
                        }
                        result.put(entry.getKey(), entry.getValue());
                    }
                }
            }
        }
        return result;
    }

    @Override
    public Map showOutputColumns(String tranStepId) {
        TransMeta trans = (TransMeta) baseDao.get(TransMeta.class, tranStepId);
        DataSetMeta output = trans.getOutDataSetMeta();
        return output == null ? new LinkedHashMap(0) : output.getDataColumnMetas();
    }

    @Override
    public List getInputColumn(String stepId) {
        String sql = "SELECT DISTINCT " +
                "		 a.id	" + SQLUtils.getSQLField("stepId") + ",					" +
                "		 a.code " + SQLUtils.getSQLField("stepName") + ",		" +
                "		 d.COLUMN_NAME " + SQLUtils.getSQLField("columnName") + " , " +
                "		 e.CODE " + SQLUtils.getSQLField("dataType") + ",d.DATA_TYPE_LENGTH " + SQLUtils.getSQLField("length") + ",d.PRECSN " + SQLUtils.getSQLField("precsn") + "	" +
                "  FROM t_etl_trans a, t_etl_data_column_meta d,t_md_element e " +
                " WHERE a.id IN						" +
                "       (SELECT b.from_trans_id				" +
                "          FROM t_etl_trans_hops b			" +
                "         WHERE b.to_trans_id = :id)    	" +
                "   AND a.OUT_DATASET_ID = d.DATASET_ID		" +
                "   AND d.DATA_TYPE_ID = e.ID 	" +
                " ORDER BY " + SQLUtils.getSQLField("stepName") + ", " + SQLUtils.getSQLField("columnName") + " asc	";
        Map<String, Object> param = new HashMap<>();
        param.put("id", stepId);
        return baseDao.sqlQueryForList(sql, param);
    }

    @Override
    public List getOutputColumn(String stepId) {
        String sql = "SELECT DISTINCT " +
                "		 a.id	" + SQLUtils.getSQLField("stepId") + ",					" +
                "		 a.code " + SQLUtils.getSQLField("stepName") + ",		" +
                "		 d.COLUMN_NAME " + SQLUtils.getSQLField("columnName") + " , " +
                "		 e.CODE " + SQLUtils.getSQLField("dataType") + ",d.DATA_TYPE_LENGTH " + SQLUtils.getSQLField("length") + ",d.PRECSN " + SQLUtils.getSQLField("precsn") + "	" +
                "  FROM t_etl_trans a, t_etl_data_column_meta d,t_md_element e " +
                " WHERE a.id = :id   	" +
                "   AND a.OUT_DATASET_ID = d.DATASET_ID		" +
                "   AND d.DATA_TYPE_ID = e.ID 	" +
                " ORDER BY " + SQLUtils.getSQLField("stepName") + ", " + SQLUtils.getSQLField("columnName") + " asc	";
        Map<String, Object> param = new HashMap<>();
        param.put("id", stepId);
        return baseDao.sqlQueryForList(sql, param);
    }

    private String[] getPrevStepIds(String tranStepId) {
        String sql = "SELECT b.from_trans_id FROM t_etl_trans_hops b WHERE b.to_trans_id = :id";
        Map<String, Object> param = new HashMap<>();
        param.put("id", tranStepId);
        List<Map<String, String>> prevSteps = baseDao.sqlQueryForList(sql, param);
        Collection<String> ids = new LinkedHashSet<String>();
        for (Map<String, String> m : prevSteps) {
            ids.addAll(m.values());
        }
        return ids.toArray(new String[ids.size()]);
    }

    @Override
    @Transactional(readOnly = true)
    public Boolean hasRootTrans(String transId) {

        String sql = "select owner_id from T_ETL_TRANS where id =:transId ";
        String transId1 = this.baseDao.sqlQueryForValue(sql, addParam("transId", transId).param());
        return StringUtils.isNotBlank(transId1);
    }

    @Override
    public String getTransMetaTaskGroup(String transId) {
        String sql = "select task_group from t_etl_trans where id = '" + transId + "'";
        return this.baseDao.sqlQueryForValue(sql);
    }

    public String getFirstTransId(String transId) {
        String sql = "select id from t_etl_trans where id in (select child_trans_id from T_ETL_TRANS_STEPDETAIL  where trans_id ='" + transId + "')" +
                "and memo='isFistTrans'";
        return this.baseDao.sqlQueryForValue(sql);
    }

    @Override
    public Map<String, String> queryDmcPluginTreeNodeIdByKeyword(String keyword) {
        String sql = "select id from t_etl_trans where code = '" + keyword + "'";
        return this.baseDao.sqlQueryForMap(sql);
    }

    @Override
    public List<String> findAllInstanceList() {
        String sql = "select code from t_md_etl_instance where etl_instance_app_name !='mist-scheduler'";
        List<Map<String, Object>> codeMaps = this.baseDao.sqlQueryForList(sql);

        List<String> result = codeMaps.stream().collect(Collectors.mapping(c -> String.valueOf(c.get("code")), Collectors.toList()));
        return result;
    }

    @Override
    public JSONObject getPluginInfo() {

        //最终返回的结果集合
//       Map<String,List<PluginVo>> pluginMap = new HashMap<>();
//        获取所有的插件
        JSONObject result = new JSONObject();
        String sql = "select id,name,code from t_md_etl_trans_plugin";
        List<Map<String, Object>> plugins = this.baseDao.sqlQueryForList(sql);
        //处理插件信息
        Map<String, PluginVo> pluginVoMap = new HashMap<>();
        for (Map<String, Object> mo : plugins) {
            PluginVo pv = new PluginVo();
            pv.setCode(String.valueOf(mo.get("code")));
            pv.setLabel(String.valueOf(mo.get("name")));
            pv.setId(String.valueOf(mo.get("id")));
            pluginVoMap.put(pv.getCode(), pv);
        }

        JSONObject jsonObject = disposeJSONFile();//获取json配置文件

        //获取插件的值
        for (int i = 0; i < jsonObject.getJSONArray("plugin").size(); i++) {
            Object json = jsonObject.getJSONArray("plugin").get(i);
            JSONObject jsonObject1 = (JSONObject) json;
            String pluginType = "";
            JSONArray jsonArray = null;
            if (i == 0) {
                pluginType = "setOperation";
                jsonArray = jsonObject1.getJSONArray("集合运算");
            } else if (i == 1) {
                pluginType = "dataProcessing";
                jsonArray = jsonObject1.getJSONArray("数据处理");
            } else if (i == 2) {
                pluginType = "processControl";
                jsonArray = jsonObject1.getJSONArray("流程控制");
            } else if (i == 3) {
                pluginType = "resultsOutput";
                jsonArray = jsonObject1.getJSONArray("结果输出");
            } else {
                pluginType = "chartShows";
                jsonArray = jsonObject1.getJSONArray("图表展示");
            }

            JSONArray jsonArray1 = getPlugin(pluginVoMap, jsonArray);
            result.put(pluginType, jsonArray1);
        }
        return result;
    }

    public JSONObject disposeJSONFile() {
        try {
            String areaData = IOUtils.toString(pluginsJSON.getInputStream(), "UTF-8");
            return JSONObject.fromObject(areaData);
        } catch (IOException e) {
            log.error("配置文件错误!");
        }
        return null;
    }

    public JSONArray getPlugin(Map<String, PluginVo> pv, JSONArray jsonArray) {

        JSONArray ja = new JSONArray();

        for (Object obj : jsonArray) {
            JSONObject jb = (JSONObject) obj;
            if (pv.get(String.valueOf(jb.get("code"))) != null) {
                if ("plugin".equals(String.valueOf(jb.get("type")))) {
                    String id = pv.get(String.valueOf(jb.get("code"))).getId();
                    String label = pv.get(String.valueOf(jb.get("code"))).getLabel();
                    jb.put("id", id);
                    jb.put("label", label);
                } else {
                    jb.put("label", String.valueOf(jb.get("code")));
                }
            }
            JSONArray pl = jb.getJSONArray("children");
            if (pl.size() > 0) {
                jb.put("children", getPlugin(pv, pl));
            }
            ja.add(jb);
        }
        return ja;
    }

    @Override
    public List<Map<String, Object>> getOriginDataListByView(String condition, String pageSize, String page, boolean isStdlib, String userId) {
        StringBuffer sb = new StringBuffer();
        sb.append("select   " +
                " b.id ,  " +
                " b.name ,  " +
                " b.code ,  " +
                " b.type ,  " +
                " b.db_type ,  " +
                " b.global_code ,  " +
                " c.name as dwName   " +
                "from   " +
                " t_dw_table_mapping a,   " +
                " t_md_rdb_dataobj b,   " +
                " t_md_dw_db_instance c   " +
                "where   " +
                " a.classifier_stat_id = b.id   " +
                "and   " +
                " b.object_type = 'VIEW'   " +
                "and    " +
                " a.dw_db_id = c.id ");

        return this.execute(condition, pageSize, page, isStdlib, sb, userId);
    }

    @Override
    public List<Map<String, Object>> getOriginDataList(String condition, String pageSize, String page, boolean isStdlib, String userId) {

        StringBuffer sb = new StringBuffer();
        sb.append("select " +
                " b.id, " +
                " b.name, " +
                " ( " +
                " case " +
                "   b.\"type\" " +
                "   when 'ElasticsearchCollection' then( " +
                "    select " +
                "     s.index_name " +
                "    from " +
                "     t_md_elastics_schema s " +
                "    where " +
                "     s.id = b.owner_id " +
                "   ) " +
                "   else b.code " +
                "  end " +
                " ) as code, " +
                " b.type, " +
                " b.db_type, " +
                " b.global_code, " +
                " e.name as dwName " +
                "from " +
                " t_dw_table_mapping a, " +
                " t_md_classifier_stat b, " +
                " t_md_dw_db_instance e " +
                "where " +
                " a.classifier_stat_id = b.id " +
                " and b.type != 'CompareResultDataObj' " +
                " and a.dw_db_id = e.id");

        return this.execute(condition, pageSize, page, isStdlib, sb, userId);
    }

    private List<Map<String, Object>> execute(String condition, String pageSize, String page, boolean isStdlib, StringBuffer baseSql, String userId) {

        StringBuffer stdlibsb = new StringBuffer();
        StringBuffer sqlsb = new StringBuffer();

        Map<String, String> params = Maps.newHashMap();
        params.put("obj_id", userId);
        List<TSysAuthObjFunc> tSysAuthObjFuncs = sysAuthObjFuncService.queryList(params);
        List<String> authIds = tSysAuthObjFuncs.stream().map(s -> s.gettSysAuthObj().getId()).collect(Collectors.toList());


        Map<String, String> relationParams = Maps.newHashMap();
        relationParams.put("from_obj_id", userId);
        relationParams.put("relation_type", "1");
        List<TSysAuthObjRel> sysAuthObjRels = sysAuthObjRelService.queryList(relationParams);
        authIds.addAll(sysAuthObjRels.stream().map(t -> t.getToAuthObj().getId()).collect(Collectors.toList()));

        stdlibsb.append("select t1.id from t_md_classifier_stat t1, t_md_element t2, t_md_element t3 where t1.owner_id = t2.id and t2.owner_id = t3.id and t1.id in (  select DISTINCT a.object_id from t_md_stand_comparison_table_mapping a, t_md_stand_comparison_column_mapping b,t_md_stand_sjzyml_gongan_mapping c  where a.id=b.comparion_object_id  and b.bzsjxj_id=c.local_field_id and b.comparison_status='3' and c.mapping_status='1' and b.bzsjxj_id is not null )");
        if (isStdlib) {
            sqlsb.append("select c.id,c.name,c.code,c.db_type,c.global_code,c.type,c.dwName from ");
            sqlsb.append("(" + baseSql.toString() + ") c ");
            sqlsb.append(" right join ");
            sqlsb.append("(" + stdlibsb.toString() + ") d on c.id = d.id where c.id is not null ");
            if (StringUtils.isNotBlank(condition)) {
                sqlsb.append(" and ( upper(c.name) like '%" + condition.toUpperCase() + "%' ");
                sqlsb.append(" or upper(c.\"code\") like '%" + condition.toUpperCase() + "%' )");
            }
        } else {
            sqlsb = baseSql;
            if (StringUtils.isNotBlank(condition)) {
                sqlsb.append(" and ( upper(b.name) like '%" + condition.toUpperCase() + "%' ");
                sqlsb.append(" or upper(b.\"code\") like '%" + condition.toUpperCase() + "%' )");
            }
        }
        StringBuffer userSql = new StringBuffer();

        userSql.append("select DISTINCT k.id,k.name,k.code,k.type,k.db_type,k.global_code,k.dwName from ");

        userSql.append("(" + sqlsb.toString() + ") k  ");

        userSql.append(" join t_sys_auth_obj_func  on  k.id = t_sys_auth_obj_func.func_code where t_sys_auth_obj_func.obj_id in " + SQLUtils.foreachSqlByIn(authIds) + " ");

//        userSql.append(" LIMIT ").append(pageSize).append(" OFFSET ").append(page);


//        List<Map<String, Object>> list = new ArrayList();
//        if(null != userId){
//            list = this.baseDao.sqlQueryForList(sqlsb.toString());
//            Map<String, String> params = Maps.newHashMap();
//            params.put("obj_id", userId);
//            List<TSysAuthObjFunc> tSysAuthObjFuncs = sysAuthObjFuncService.queryList(params);
//            List<String> ids = tSysAuthObjFuncs.stream().map(s->s.gettSysFuncBase().getFuncCode()).collect(Collectors.toList());
//            list = list.stream().filter(t->ids.indexOf(t.get("id").toString()) != -1).collect(Collectors.toList());
//        }else {
//            list = this.baseDao.sqlQueryForList(sqlsb.toString());
//        }
        return this.baseDao.sqlQueryForList(userSql.toString());

    }

    @Override
    public List<ModelTreeVo> getTreeNode(String userId, String dirType) {
        List<BaseBusiClassify> busiDirs = getBaseBusiDir(userId, dirType);
        List<ModelTreeVo> trees = new ArrayList<>();
        for (BaseBusiClassify bbd : busiDirs) {
            ModelTreeVo mtv = new ModelTreeVo();
            mtv.setId(bbd.getId());
            mtv.setLabel(bbd.getName());
            mtv.setElementOrDir("1");
            List<ModelTreeVo> childrenTree = getChildrenTree(bbd.getBusiClassifies(), userId, dirType);
            Set<BaseModelElement> elements = bbd.getElements();
            List<ModelTreeVo> baseElements = new ArrayList<>();
            for (BaseModelElement element : elements) {
                ModelTreeVo modelTreeVo = new ModelTreeVo();
                modelTreeVo.setChildren(null);
                modelTreeVo.setLabel(element.getName());
                modelTreeVo.setElementOrDir("0");
                modelTreeVo.setId(element.getId());
                if (dirType.contains("RAPID")) {
                    modelTreeVo.setTaskGroup(QUICK_SPARK);
                } else {
                    modelTreeVo.setTaskGroup("DEF");
                }
                baseElements.add(modelTreeVo);
            }
            childrenTree.addAll(baseElements);
            mtv.setChildren(childrenTree);

            trees.add(mtv);
        }
        if (!standModel) {
            for (int i = 0; i < trees.size(); i++) {
                if ("标准模型".equals(trees.get(i).getLabel())) {
                    trees.remove(i);
                    break;
                }
            }
        }
        return trees;
    }

    private List<ModelTreeVo> getChildrenTree(Set<BaseBusiClassify> bbdBusiClassifys, String userId, String dirType) {
        String type = dirType.contains("RAPID") ? QUICK_SPARK : "DEF";
        List<ModelTreeVo> child = new ArrayList<>();
        for (BaseBusiClassify bc : bbdBusiClassifys) {
            if (userId.equals(bc.getOperateUserId())) {
                ModelTreeVo mtv2 = new ModelTreeVo();
                mtv2.setId(bc.getId());
                mtv2.setLabel(bc.getName());
                mtv2.setElementOrDir("1");
                List<ModelTreeVo> child2 = new ArrayList<>();
                for (BaseModelElement be : bc.getElements()) {
                    //TransMeta transMeta = transMetaService.getTransMetaById(be.getId());
                    if (!be.getName().contains("预览_preview") && !be.getName().contains("发布_publish")) {  //排除方案发布和预览克隆的方案和快速分析
//                            if (userId.equals(be.getOperateUserId())) {
                        ModelTreeVo mtv3 = new ModelTreeVo();
                        mtv3.setId(be.getId());
                        mtv3.setLabel(be.getName());
                        mtv3.setChildren(new ArrayList<>());
                        mtv3.setElementOrDir("0");
                        mtv3.setTaskGroup(type);
                        child2.add(mtv3);
//                        }
                    }
//
                }
                //如果有第三层的目录 则继续往下面构建目录
                if (bc.getBusiClassifies().size() != 0) {
                    child2.addAll(getChildrenTree(bc.getBusiClassifies(), userId, dirType));
                }
                mtv2.setChildren(child2);

                child.add(mtv2);
            }

        }
        return child;
    }

    @Override
    public List<BaseBusiClassify> getBaseBusiDir(String userId, String dirType) {
        String busiDirMY = dirType + "_MY";
        String busiStandard = "";
        if (busiDirMY.equals("DIG_DIR_MF_MY")) busiStandard = "DIG_DIR_STANDARD";
        if (busiDirMY.equals("TRANS_DIR_MF_MY")) busiStandard = "TRANS_DIR_STANDARD";
        if (busiDirMY.equals("TRANS_RAPID_DIR_MF_MY")) busiStandard = "TRANS_RAPID_DIR_STANDARD";
        String hql = "FROM BaseBusiClassify WHERE code in('" + busiDirMY + "','" + busiStandard + "')" +
                " and operateUserId = :operateUserId";
        Map<String, String> parem = new HashMap();
        parem.put("operateUserId", userId);
        return this.baseDao.queryForList(hql, parem);
    }

    @Override
    public Map dirCount(String dirId, String userId) {
        Map<String, String> countMap = new HashMap<>();
        String sql = "SELECT\n" +
                "\tCOUNT (1)\n" +
                "FROM\n" +
                "\tt_md_busi_dir A,\n" +
                "\tt_md_busi_classify b,\n" +
                "\tt_md_classify_element C,\n" +
                "\tt_etl_trans d\n" +
                "WHERE\n" +
                "\tA .code = 'TRANS_DIR_MF'\n" +
                "AND b.busi_dir_id = A . ID\n" +
                "AND C .busi_classify_id = b. ID\n" +
                "AND C.element_id = d.id";
        String dirCount = this.baseDao.sqlQueryForValue(sql);
        countMap.put("dirCount", dirCount);

        String beatLibrarySql = "SELECT count(1) FROM t_md_element\n" +
                "WHERE TYPE = 'SqlDataObj'";
        String beatLibraryCount = this.baseDao.sqlQueryForValue(beatLibrarySql);
        countMap.put("beatLibraryCount", beatLibraryCount);

        String customDataSetSql = "SELECT\n" +
                "\tcount(1)\n" +
                "FROM\n" +
                "\tt_dmc_data_object";
        String customDataSetCount = this.baseDao.sqlQueryForValue(customDataSetSql);
        countMap.put("customDataSetCount", customDataSetCount);
        return countMap;
    }

    @Override
    public String moveModel(String elementId, String classifyId) {

        String checkSql = "select count(1) from t_etl_trans where id in (select element_id from t_md_classify_element where busi_classify_id = :classifyId and element_id != :elementId" +
                ") and name = (select name from t_etl_trans where id =:elementId " +
                ")";
        Map param = new HashMap();
        param.put("classifyId", classifyId);
        param.put("elementId", elementId);
        int count = Integer.valueOf(this.baseDao.sqlQueryForValue(checkSql, param));
        if (count > 0) {
            String getNameSql = "select name from t_etl_trans where id =:elementId ";
            Map<String, String> nameParem = new HashMap();
            nameParem.put("elementId", elementId);
            String name = this.baseDao.sqlQueryForValue(getNameSql, nameParem);
            String schemaNameSql = "select name from t_md_busi_classify where id = :classifyId";
            Map<String, String> schemaNameParem = new HashMap();
            schemaNameParem.put("classifyId", classifyId);
            String schemaName = this.baseDao.sqlQueryForValue(schemaNameSql, schemaNameParem);
            return "名称[" + name + "]已在[" + schemaName + "]存在";
        }

        String moveModeSQL = "UPDATE t_md_classify_element " +
                "SET busi_classify_id= :classifyId " +
                " WHERE element_id= :elementId ";
        this.baseDao.executeSqlUpdate(moveModeSQL, param);
        return "success";
    }

    @Override
    public void changeTime(String transId) {
        String operateTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        String sql = "update T_ETL_TRANS set OPERATE_TIME =:operateTime where id =:transId";
        this.baseDao.executeSqlUpdate(sql, addParam("operateTime", operateTime).addParam("transId", transId).param());
    }

    @Override
    public List getDataSetByTrans(String transId) {

        List<String> classifierStatIds = getClassifierStatIds(transId);
        if (classifierStatIds.size() <= 0) {
            return null;
        }
        return getDataSetsInfo(classifierStatIds);
    }

    private List getDataSetsInfo(List<String> classifierStatIds) {
        String sql = "select s.id,s.code,s.name,l.id as logic_id,s.db_type from t_md_classifier_stat s inner join t_md_logic_dataobj l on s.id = l.owner_id where s.id in (:ids)";
        Map<String, List<String>> param = Maps.newHashMap();
        param.put("ids", classifierStatIds);
        return this.baseDao.sqlQueryForList(sql, param);
    }

    private List<String> getClassifierStatIds(String transId) {
        TransMeta transMeta = (TransMeta) this.baseDao.get(TransMeta.class, transId);
        Set<TransMeta> children = transMeta.getChildren();
        List<String> classifierStatIds = Lists.newArrayList();
        for (TransMeta child : children) {
            String type = child.getUsedPlugin().getTransPluginType();
            if ("OUTPUT".equals(type)) {
                Map<TransAttributeMeta, String> attributeValues = child.getAttributeValues();
                for (Map.Entry entry : attributeValues.entrySet()) {
                    TransAttributeMeta attributeMeta = (TransAttributeMeta) entry.getKey();
                    //schemaId
                    if ("tableId".equals(attributeMeta.getCode())) {
                        classifierStatIds.add(entry.getValue().toString());
                    }
                }

            }
        }
        return classifierStatIds;
    }

    @Override
    public Map<String, Object> getState(String transId) {
        Map<String, Object> transState = new HashMap<>();
        if (StringUtils.isNotBlank(transId)) {
            String hql = " SELECT DISTINCT a.id ,(case when b.exp_node_id is not null or a.OUT_DATASET_ID is not null then 'true' else 'false' end ) trans_state  FROM t_etl_trans a \n" +
                    "inner join T_ETL_TRANS_STEPDETAIL c on a.id =c.child_trans_id and c.trans_id = '%s'\n" +
                    " left join T_ETL_TRANS_EXP  b on a.id  = b.trans_id   ";
            List<Map<String,String>> list = this.baseDao.sqlQueryForList(String.format(hql, transId));
            if (CollectionUtils.isNotEmpty(list)) {
                for (Map<String,String> map : list) {
                    boolean transStateFlag = Boolean.valueOf(map.get("trans_state"));
                    transState.put(String.valueOf(map.get("id")), Boolean.valueOf(map.get("trans_state")));

                }
            }
        }

        return transState;
    }

    @Override
    public PageInfo getShareResourceByTrans(Map<String, Object> reqMap, String userId) {
        String tranId = (String) reqMap.get("tranId");
        Assert.notNull(tranId, "模型id不能为空");
        String objId = (String) reqMap.get("objId");
        String resourceName = (String) reqMap.get("resourceName");
        Integer pageNum = (Integer) reqMap.get("pageNum");
        Integer pageSize = (Integer) reqMap.get("pageSize");
        if (pageNum == null || pageNum <= 0) {
            pageNum = 1;
        }
        if (pageSize == null || pageSize <= 0) {
            pageSize = 10;
        }
        PageInfo pageInfo = new PageInfo();
        List<String> funcCodes = Lists.newArrayList();
        Map<String, Object> resourcesByTranId = this.getResourcesByTranId(tranId);
        List<Map<String, Object>> modelService = (List) resourcesByTranId.get("modelService");
        List<Map<String, Object>> dataSet = (List) resourcesByTranId.get("dataSet");
        List<Map<String, Object>> dataService = (List) resourcesByTranId.get("dataService");
        List<Map<String, Object>> compareService = (List) resourcesByTranId.get("compareService");
        List<Map<String, Object>> dataCollision = (List) resourcesByTranId.get("dataCollision");
        List<Map<String, Object>> informationVerification = (List) resourcesByTranId.get("informationVerification");
        //获取  api 服务id

        for (Map<String, Object> map : modelService) {
            funcCodes.add((String) map.get("id"));
        }
        for (Map<String, Object> map : dataSet) {
            funcCodes.add((String) map.get("id"));
        }
        for (Map<String, Object> map : dataService) {
            funcCodes.add((String) map.get("id"));
        }
        for (Map<String, Object> map : compareService) {
            funcCodes.add((String) map.get("id"));
        }
        for (Map<String, Object> map : dataCollision) {
            funcCodes.add((String) map.get("id"));
        }
        for (Map<String, Object> map : informationVerification) {
            funcCodes.add((String) map.get("id"));
        }
        if (funcCodes.size() <= 0) {
            return new PageInfo();
        }
        List<Map<String, String>> list = this.baseDao.sqlQueryForList("select id from t_sys_auth_obj_func where func_code in (:funcCodes)", this.addParam("funcCodes", funcCodes).param());
        List<String> ids = Lists.newArrayList();
        list.forEach(i -> {
            ids.add(i.get("id"));
        });
        if (ids.size() <= 0) {
            return pageInfo;
        }
        StringBuilder sbSql = new StringBuilder();
        Map<String, Object> map = Maps.newHashMap();
        sbSql.append(" select e.id,tf.func_type,e.name,tsf.obj_id,min(tsf.create_time) as create_time from t_md_element e  ");
        sbSql.append(" INNER JOIN t_sys_auth_obj_func tsf on e.id = tsf.func_code ");
        sbSql.append(" INNER JOIN t_sys_func tf on tf.func_code = e.id ");
        sbSql.append(" where tsf.id in (:ids)  ");
        map.put("ids", ids);
        if (StringUtils.isNotBlank(objId)) {
            sbSql.append(" and tsf.obj_id = :objId ");
            map.put("objId", objId);
        }
        if (StringUtils.isNotBlank(resourceName)) {
            sbSql.append(" and e.name like :resourceName ");
            map.put("resourceName", "%" + resourceName + "%");
        }
        sbSql.append(" and tsf.obj_id != :userId ");
        sbSql.append(" and e.operate_user_id = :userId ");
        map.put("userId", userId);
        sbSql.append(" GROUP BY e.id,tf.func_type,tsf.obj_id ORDER BY min(tsf.create_time) desc ");
        pageInfo.setPageSize(pageSize);
        pageInfo.setPageIndex(pageNum);
        PageInfo pageInfo1 = this.baseDao.sqlQueryForPage(sbSql.toString(), map, pageInfo);
        List<Map<String, Object>> dataList = pageInfo1.getDataList();
        List<Map<String, Object>> mapList = Lists.newArrayList();
        for (Map<String, Object> data : dataList) {
            Map<String, Object> result = new HashMap<>();
            result.put("name", data.get("name"));
            String funcType = (String) data.get("func_type");
            if (ResourceTypeEnum.MODEL_SERVICE.getCode().equals(funcType)) {
                Map<String, Object> service = this.baseDao.sqlQueryForMap("select p.name,p.id from t_md_service_publication p left join t_md_service_meta m on p.id = m.service_id where m.id = :id ",
                        this.addParam("id", data.get("id")).param());
                result.put("name", service.get("name"));
                result.put("func_type", "modelService");
                result.put("serviceId", service.get("id"));
            } else if ("1".equals(funcType.toString())) {
                result.put("func_type", "dataSet");
            } else if (ResourceTypeEnum.COMPARE_SERVICE.getCode().equals(funcType)) {
                Map<String, Object> service = this.baseDao.sqlQueryForMap("select p.name,p.id from t_md_service_publication p left join t_md_service_meta m on p.id = m.service_id where m.id = :id ",
                        this.addParam("id", data.get("id")).param());
                result.put("name", service.get("name"));
                result.put("func_type", ResourceTypeEnum.COMPARE_SERVICE.getName());
                result.put("serviceId", service.get("id"));
            } else if (ResourceTypeEnum.DATA_COLLISION.getCode().equals(funcType)) {
                Map<String, Object> service = this.baseDao.sqlQueryForMap("select p.name,p.id from t_md_service_publication p left join t_md_service_meta m on p.id = m.service_id where m.id = :id ",
                        this.addParam("id", data.get("id")).param());
                result.put("name", service.get("name"));
                result.put("func_type", ResourceTypeEnum.DATA_COLLISION.getName());
                result.put("serviceId", service.get("id"));
            } else if (INFORMATION_VERFICATION.getCode().equals(funcType)) {
                result.put("func_type", INFORMATION_VERFICATION.getName());
            }
            //result.put("func_type",data.get("func_type"));
            result.put("id", data.get("id"));

            Timestamp createTime = (Timestamp) data.get("create_time");
            DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String timeStr = sdf.format(createTime);
            result.put("create_time", timeStr);
            String authId = (String) data.get("obj_id");
            Map obj = this.baseDao.sqlQueryForMap("select id,obj_type,obj_name from t_sys_auth_obj where id = :authId",
                    this.addParam("authId", authId).param());
            Character obj_type = (Character) obj.get("obj_type");
            if ("0".equals(obj_type.toString())) {
                obj.put("obj_type", "用户");
            } else if ("2".equals(obj_type.toString())) {
                obj.put("obj_type", "角色");
            }
            result.put("obj", obj);
            mapList.add(result);
        }
        pageInfo1.setDataList(mapList);
        return pageInfo1;
    }

    @Override
    public Map<String, Object> getResourcesByTranId(String tranId) {
        Assert.notNull(tranId, "模型id不能为空");
        Map<String, Object> map = Maps.newHashMap();

        List<Map<String, Object>> dataSetByTrans = getDataSetByTrans(tranId);
        List<Map<String, Object>> dataSetResults = Lists.newArrayList();
        List<String> dataLogicIds = Lists.newArrayList();

        //关系库表输出数据集资源
        if (dataSetByTrans != null) {
            for (Map<String, Object> dataSetByTran : dataSetByTrans) {
                String id = (String) dataSetByTran.get("id");
                List<LogicDataObj> logicDataObjsByOwnerId = logicDataObjService.getLogicDataObjsByOwnerId(id);
                for (LogicDataObj logicDataObj : logicDataObjsByOwnerId) {
                    Map<String, Object> result = Maps.newHashMap();
                    result.put("name", logicDataObj.getName());
                    result.put("id", logicDataObj.getId());
                    dataSetResults.add(result);
                    dataLogicIds.add(logicDataObj.getId());
                }
            }
        }

        //模型服务资源
        List<ServicePublication> servicePublications = servicePublicationService.queryBySourceId(tranId);
        List<Map<String, Object>> results = Lists.newArrayList();
        List<Map<String, Object>> compareResults = Lists.newArrayList();
        List<Map<String, Object>> dataCoResults = Lists.newArrayList();
        for (ServicePublication servicePublication : servicePublications) {

            Set<ServiceMeta> serviceMetas = servicePublication.getServiceMetas();
            if (serviceMetas.size() <= 0) {
                continue;
            }
            List<ServiceMeta> collect = serviceMetas.stream().sorted(Comparator.comparing(ServiceMeta::getPublishTime).reversed()).collect(Collectors.toList());
            ServiceMeta serviceMeta = collect.get(0);
            if (servicePublication.getServiceType().equals(EnumServiceType.CALCULATION_SERVICE.getCode())) {
                Map<String, Object> result = Maps.newHashMap();
                result.put("name", servicePublication.getName());
                result.put("id", serviceMeta.getId());
                result.put("serviceId", servicePublication.getId());
                results.add(result);
            }
            if (servicePublication.getServiceType().equals(EnumServiceType.COMPARE_SERVICE.getCode())) {
                Map<String, Object> result = Maps.newHashMap();
                result.put("name", servicePublication.getName());
                result.put("id", serviceMeta.getId());
                result.put("serviceId", servicePublication.getId());
                compareResults.add(result);
            }
            if (servicePublication.getServiceType().equals(EnumServiceType.DATA_COLLISION.getCode())) {
                Map<String, Object> result = Maps.newHashMap();
                result.put("name", servicePublication.getName());
                result.put("id", serviceMeta.getId());
                result.put("serviceId", servicePublication.getId());
                dataCoResults.add(result);
            }
        }

        //数据查询服务资源
        List dataServiceResults = new ArrayList();
        List inServiceResults = new ArrayList();
        if (dataLogicIds.size() > 0) {
            String sql = "select p.name,m.id from t_md_service_publication p inner join t_md_service_meta m on p.id = m.service_id where p.source_id in (:dataLogicIds) and p.service_type = :serviceType";
            dataServiceResults = this.baseDao.sqlQueryForList(sql, this.addParam("dataLogicIds", dataLogicIds).addParam("serviceType", EnumServiceType.INQUIRY_SERVICE.getCode()).param());


            inServiceResults = this.baseDao.sqlQueryForList(sql, this.addParam("dataLogicIds", dataLogicIds).addParam("serviceType", EnumServiceType.INFORMATION_VERFICATION.getCode()).param());

        }

        String sql2 = "select p.name,m.id from t_md_service_publication p inner join t_md_service_meta m on p.id = m.service_id where p.source_id = :transId and p.service_type = :serviceType";
        List inModelServices = this.baseDao.sqlQueryForList(sql2, addParam("transId", tranId).addParam("serviceType", EnumServiceType.INFORMATION_VERFICATION.getCode()).param());
        if (CollectionUtil.isNotEmpty(inModelServices)) {
            inServiceResults.addAll(inModelServices);
        }
        map.put("modelService", results);
        map.put("dataSet", dataSetResults);
        map.put("dataService", dataServiceResults);
        map.put("compareService", compareResults);
        map.put("informationVerification", inServiceResults);
        map.put("dataCollision", dataCoResults);
        return map;
    }

    @Override
    public List<ModelTreeResult> getModelService(String userId) {
        List<ModelTreeResult> busiClassifies = aiDataModelingService.getAllAiModelWithChildren(userId, "time");

        return setModelServiceTree(busiClassifies, userId);
    }

    private List<ModelTreeResult> setModelServiceTree(List<ModelTreeResult> modelTreeResults, String userId) {
        List<ModelTreeResult> results = Lists.newArrayList();
        for (ModelTreeResult modelTreeResult : modelTreeResults) {
            List<ModelTreeResult> children = modelTreeResult.getChildren();
            if (children != null && children.size() > 0) {
                modelTreeResult.setChildren(setModelServiceTree(children, userId));
            } else if (!modelTreeResult.getIsParent()) {
                modelTreeResult.setIsParent(true);
                String id = modelTreeResult.getId();
                List<Map<String, Object>> logIds = this.baseDao.sqlQueryForList("select id from t_script_log where script_id = :scriptId", this.addParam("scriptId", id).param());
                List<String> sourIds = Lists.newArrayList();
                for (Map<String, Object> logId : logIds) {
                    sourIds.add((String) logId.get("id"));
                }
                Map<String, Object> map = Maps.newHashMap();
                map.put("sourceId", sourIds);
                map.put("userId", userId);
                if (sourIds.size() > 0) {
                    List<Map<String, Object>> list = this.baseDao.sqlQueryForList(" select id,name,operate_time from t_md_service_publication where source_id in (:sourceId) and " +
                            " service_type = '2' and operate_user_id = :userId ", map);
                    if (list.size() > 0) {
                        List<ModelTreeResult> services = Lists.newArrayList();
                        for (Map<String, Object> objectMap : list) {
                            ModelTreeResult service = new ModelTreeResult();
                            service.setChildren(null);
                            service.setIsParent(false);
                            service.setId((String) objectMap.get("id"));
                            service.setName((String) objectMap.get("name"));
                            service.setOperateTime((String) objectMap.get("operate_time"));
                            service.setIsService(true);
                            service.setParentId(modelTreeResult.getId());
                            services.add(service);
                        }
                        modelTreeResult.setChildren(services);
                    }
                }


            }
            results.add(modelTreeResult);
        }
        return results;
    }

    /**
     * 模型服务模型资源树
     *
     * @param userId
     * @return
     */
    @Override
    public List<ModelTreeVo> queryModelServiceSourceTree(String userId, String transClassify) {
        StringBuilder baseSqlBuilder = new StringBuilder(); //我的空间目录
        baseSqlBuilder.append(" with recursive dict as ( ");
        baseSqlBuilder.append(" select * from t_md_busi_classify where code in ('TRANS_DIR_MF_MY' ");
        if (standModel) {
            baseSqlBuilder.append(", 'TRANS_DIR_STANDARD' ");
        }
        if (sceModel) {
            baseSqlBuilder.append(", 'TRANS_DIR_MF_CASE' ");
        }
        baseSqlBuilder.append(" ) and operate_user_id = :userId union All ");
        baseSqlBuilder.append(" select t.* from t_md_busi_classify t, dict where t.parent_classify_id = dict.id) ");
        baseSqlBuilder.append(" select d.id, d.name as label, d.parent_classify_id as parent_id, '1' as element_or_dir from dict d ");


        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("userId", userId);
        List<Map<String, Object>> dataList = baseDao.sqlQueryForList(buildSQL(baseSqlBuilder, transClassify).toString(), paramsMap);
        if (CollectionUtils.isNotEmpty(dataList)) {
            Map<String, List<Map<String, Object>>> dataMap = new HashMap<>(); //子节点数据
            List<Map<String, Object>> rootDataList = new ArrayList<>(); //根节点
            for (Map<String, Object> map : dataList) {
                String parentId = map.get("parent_id") == null ? null : map.get("parent_id").toString();
                if (parentId != null) {
                    List<Map<String, Object>> childList = dataMap.get(parentId);
                    if (childList == null) {
                        childList = new ArrayList<>();
                    }
                    childList.add(map);
                    dataMap.put(parentId, childList);
                } else {
                    rootDataList.add(map);
                }
            }
            return buildModelServiceSourceTree(rootDataList, dataMap);
        }
        return null;
    }

    private StringBuilder buildSQL(StringBuilder baseSqlBuilder, String transClassify) {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append(baseSqlBuilder);
        sqlBuilder.append(" union all ");
        sqlBuilder.append(" select c.id, c.name as label, a.id as parent_id, '0' as element_or_dir from ");
        sqlBuilder.append(" ( ");
        sqlBuilder.append(baseSqlBuilder);
        sqlBuilder.append(" ) a inner join t_md_classify_element b on a.id = b.busi_classify_id ");
        sqlBuilder.append(" inner join t_etl_trans c on b.element_id = c.id ");
        sqlBuilder.append(" and EXISTS (select 1 from t_etl_trans t1, t_md_etl_trans_plugin plugin, ");
        sqlBuilder.append(" (select tc.child_trans_id from t_etl_trans t, t_etl_trans_stepdetail tc where t.id = tc.trans_id and t.id=c.id) tc1 ");
        sqlBuilder.append(" where t1.id = tc1.child_trans_id and t1.transplugin_id = plugin.id ");
        if (StringUtils.isNotBlank(transClassify)) {
            sqlBuilder.append(" and plugin.code = 'cicadaMetaServiceInput') "); //存在服务输入插件
            //存在服务输出插件
        } else {
            sqlBuilder.append(" ) ");
            return sqlBuilder;
        }
        if (TransClassifyEnum.isFilter(transClassify)) {
            sqlBuilder.append(" and EXISTS (select 1 from t_etl_trans t1, t_md_etl_trans_plugin plugin, ");
            sqlBuilder.append(" (select tc.child_trans_id from t_etl_trans t, t_etl_trans_stepdetail tc where t.id = tc.trans_id and t.id=c.id) tc1 ");
            sqlBuilder.append(" where t1.id = tc1.child_trans_id and t1.transplugin_id = plugin.id ");
            sqlBuilder.append(" and (plugin.def_plugin_dir = 'DATA_COLLISION_TYPE' or plugin.def_plugin_dir = 'DATA_INPUT_OUTPUT_TYPE' or ")
                    .append("  plugin.def_plugin_dir = 'DATA_SCREENING_TYPE' or plugin.def_plugin_dir = 'DATA_SERVICE_TYPE' )")
                    .append("  )");//存在数据碰撞插件
            sqlBuilder.append(" and not EXISTS (select 1 from t_etl_trans t1, t_md_etl_trans_plugin plugin, ");
            sqlBuilder.append(" (select tc.child_trans_id from t_etl_trans t, t_etl_trans_stepdetail tc where t.id = tc.trans_id and t.id=c.id) tc1 ");
            sqlBuilder.append(" where t1.id = tc1.child_trans_id and t1.transplugin_id = plugin.id ");
            sqlBuilder.append(" and (plugin.def_plugin_dir = 'DATA_PROCESSING_TYPE' or plugin.def_plugin_dir = 'DATA_ANALYSIS_TYPE' or ")
                    .append("  plugin.def_plugin_dir = 'DATA_BUSINESS_TYPE' )")
                    .append("  )");//存在数据碰撞插件

        }
        if (TransClassifyEnum.SUBSCRIBE.code.equals(transClassify) || TransClassifyEnum.INFO_CHECK.code.equals(transClassify)) {
            sqlBuilder.append(" and EXISTS (select count(plugin.id)  from t_etl_trans t1, t_md_etl_trans_plugin plugin, ");
            sqlBuilder.append(" (select tc.child_trans_id from t_etl_trans t, t_etl_trans_stepdetail tc where t.id = tc.trans_id and t.id=c.id) tc1 ");
            sqlBuilder.append(" where t1.id = tc1.child_trans_id and t1.transplugin_id = plugin.id ");
            sqlBuilder.append(" and plugin.code = 'cicadaMetaServiceInput'  ")
                    .append(" GROUP BY plugin.id having count(plugin.id) = 1 )");//有且只有一个服务输入插件
        }
        if (TransClassifyEnum.INFO_CHECK.code.equals(transClassify)) {
            sqlBuilder.append(" and EXISTS (select count(plugin.id)  from t_etl_trans t1, t_md_etl_trans_plugin plugin, ");
            sqlBuilder.append(" (select tc.child_trans_id from t_etl_trans t, t_etl_trans_stepdetail tc where t.id = tc.trans_id and t.id=c.id) tc1 ");
            sqlBuilder.append(" where t1.id = tc1.child_trans_id and t1.transplugin_id = plugin.id ");
            sqlBuilder.append(" and plugin.code = 'cicadaMetaServiceCheckOutPut'  ")
                    .append(" GROUP BY plugin.id having count(plugin.id) = 1 )");//有且只有一个信息核查服务输出
        }
        //if(!TransClassifyEnum.INFO_CHECK.code.equals(transClassify)){
        sqlBuilder.append(" and EXISTS (select 1 from t_etl_trans t1, t_md_etl_trans_plugin plugin, ");
        sqlBuilder.append(" (select tc.child_trans_id from t_etl_trans t, t_etl_trans_stepdetail tc where t.id = tc.trans_id and t.id=c.id) tc1 ");
        sqlBuilder.append(" where t1.id = tc1.child_trans_id and t1.transplugin_id = plugin.id ");
        sqlBuilder.append(" and plugin.code = 'cicadaMetaServiceOutput') "); //存在服务输出插件
        //}
        return sqlBuilder;

    }

    private List<ModelTreeVo> buildModelServiceSourceTree(List<Map<String, Object>> dataList, Map<String, List<Map<String, Object>>> dataMap) {
        List<ModelTreeVo> modelTreeVoList = new ArrayList<>();
        for (Map<String, Object> map : dataList) {
            String id = String.valueOf(map.get("id"));
            ModelTreeVo modelTreeVo = new ModelTreeVo();
            modelTreeVo.setId(id);
            modelTreeVo.setLabel(String.valueOf(map.get("label")));
            modelTreeVo.setElementOrDir(String.valueOf(map.get("element_or_dir")));
            if (dataMap.get(id) != null) {
                modelTreeVo.setChildren(buildModelServiceSourceTree(dataMap.get(id), dataMap));
            }
            modelTreeVoList.add(modelTreeVo);
        }
        return modelTreeVoList;
    }

    @Override
    public boolean isExistReference(List<String> transIds) {
        StringBuffer sql = new StringBuffer();
        sql.append(" select id from t_md_resource_relation  where resource_id in (" + DataExportUtil.getCollectQuotesStr(transIds) + ")");
        sql.append(" union all ");
        sql.append(" select id from t_md_service_publication where source_id in (" + DataExportUtil.getCollectQuotesStr(transIds) + ")");
        List list = this.baseDao.sqlQueryForList(sql.toString());
        return CollectionUtils.isNotEmpty(list);
    }

    @Override
    public RefreshTransStatusResultDTO refreshTransStatus(RefreshTransStatusDTO dto) {
        RefreshTransStatusResultDTO result = new RefreshTransStatusResultDTO();
        List<TransStatusDTO> transStatusList = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //根据请求的方案ID查询最新的方案job
        List<Map<String, Object>> allJob = getAllJobIds(dto.getTransIds());
        if (CollectionUtil.isNotEmpty(allJob)) {
            allJob = allJob.stream().filter(m -> !Objects.equals("ongoing", m.get("execute_status").toString())).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(allJob)) {
                TransStatusDTO transStatus = null;
                for (Map<String, Object> job : allJob) {
                    transStatus = new TransStatusDTO();
                    transStatus.setTransId(job.get("trans_id").toString());
                    transStatus.setExecuteStatus(job.get("execute_status").toString());
                    if (null != job.get("end_time")) {
                        Timestamp end_time = (Timestamp) job.get("end_time");
                        transStatus.setEndTime(sdf.format(new Date(Long.valueOf(end_time.getTime()))));
                    }
                    transStatusList.add(transStatus);
                }
            }
        }
        result.setTransStatusList(transStatusList);
        return result;
    }


    /**
     * 获取运行详情日志信息
     *
     * @param detailId
     * @return
     */
    @Override
    public String getTransJobDetail(String detailId) {
        List<Map<String, String>> list = baseDao.sqlQueryForList(
                "select job_detail from t_trans_job where id = :id",
                MapUtil.of(Pair.of("id", detailId.trim())));
        String jobDetail = StrUtil.EMPTY;
        if (ObjectUtil.isNotEmpty(list)) {
            jobDetail = Optional.ofNullable(list.get(0).get("job_detail"))
                    .map(k -> k.replace("\\n", "\n")
                            .replace("\\r", "\r")
                            .replace("\\\"", "\""))
                    .orElse(StrUtil.EMPTY);
        }
        return jobDetail;
    }

    private List<Map<String, Object>> getAllJobIds(List<String> transIds) {
        StringBuffer sb = new StringBuffer();
        sb.append(" select * from( ")
                .append(" select id,trans_id ,execute_status,end_time,")
                .append(" Row_Number() OVER (partition by trans_id ORDER by start_time DESC) as rank ")
                .append(" from t_trans_job ")
                .append(" where trans_id in(:transId) ) j ")
                .append(" where j.rank=1 ");
        return baseDao.sqlQueryForList(sb.toString(), addParam("transId", transIds).param());
    }
}
