package com.dragonsoft.cicada.datacenter.modules.modeling.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.code.cicada.thirdplugin.fieldsort.config.service.CicadaFieldsSortService;
import com.code.cicada.thirdplugin.fieldsort.meta.CicadaFieldsSortMeta;
import com.code.common.utils.StringUtils;
import com.code.metadata.etl.trans.DataColumnMeta;
import com.code.metadata.etl.trans.DataSetMeta;
import com.code.metadata.etl.trans.TransMeta;
import com.code.metaservice.etl.trans.TransMetaService;
import com.code.plugin.PluginBeanFactory;
import com.dragoninfo.dfw.bean.Result;
import com.dragonsoft.cicada.datacenter.common.config.PreviewErrorException;
import com.dragonsoft.cicada.datacenter.common.utils.UserContextUtil;
import com.dragonsoft.cicada.datacenter.modules.modeling.charts.IPreviewChartsModelCreator;
import com.dragonsoft.cicada.datacenter.modules.modeling.service.MLSQLService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpSession;
import java.lang.reflect.Constructor;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/6/8
 */
@Slf4j
@CrossOrigin
@RestController
@RequestMapping(value = "/dataPreview")
public class PreviewController {

    @Autowired
    MLSQLService mlsqlService;
    @Autowired
    TransMetaService transMetaService;
    @Autowired
    BeanFactory beanFactory;
    @Autowired
    PluginBeanFactory pluginBeanFactory;


    @Value("${getsql.hiddle:true}")
    private String isHiddenPassword;

    @Autowired
    CicadaFieldsSortService cicadaFieldsSortService;

    @GetMapping(value = "/preview")
    public Result preview(String transId, String tranStepId, String selectSize, String limitSize, String jobID, HttpSession session) throws Exception {
        if (JSONUtil.isJson(tranStepId)) {
            JSONObject jsonObject = JSON.parseObject(tranStepId);
            tranStepId = jsonObject.getString("id");
        }
        TransMeta transMeta = this.transMetaService.getTransMetaById(tranStepId);
        DataSetMeta outDataSetMeta = transMeta.getOutDataSetMeta();
        Map<String, DataColumnMeta> dataColumnMetas = new HashMap<>();
        if (outDataSetMeta != null) {
            dataColumnMetas = outDataSetMeta.getDataColumnMetas();
        }
        String userId = UserContextUtil.getUserIdByHttpSession(session);
        Map<String, String> afterTable = mlsqlService.getAfterTableName(transId, tranStepId, true, selectSize, userId);
        Map<String, Object> resMap = Maps.newHashMap();
        limitSize = StrUtil.isBlank(limitSize) ? "10" : limitSize;
        String mlsql = "select * from " + afterTable.get("tableName") + " limit " + limitSize + " as preview_" + afterTable.get("tableName") + ";";

        String code = transMeta.getUsedPlugin().getCode();
        //进行字段排序
        if ("cicadaFieldsSortMeta".equals(code)){
            CicadaFieldsSortMeta pluginMeta = cicadaFieldsSortService.getPluginInstance(CicadaFieldsSortMeta.class, tranStepId);
            if (ObjectUtil.isNotEmpty(pluginMeta) && ObjectUtil.isNotEmpty(pluginMeta.getCicadaOutPutFsSet())){
                List<CicadaFieldsSortMeta.CicadaOutSortFsMeta> pluginMetaList = pluginMeta.getCicadaOutPutFsSet().stream()
                        .sorted(Comparator.comparing(CicadaFieldsSortMeta.CicadaOutSortFsMeta::getSort)).collect(Collectors.toList());
                String sortSql = pluginMetaList.stream().map(CicadaFieldsSortMeta.CicadaOutSortFsMeta::getColumnName).collect(Collectors.joining(","));
                mlsql = "select "+sortSql+" from " + afterTable.get("tableName") + " limit " + limitSize + " as preview_" + afterTable.get("tableName") + ";";
            }
        }

        mlsql = afterTable.get("sql") + "\n" + mlsql;
        resMap.put("sql", mlsql);
        resMap.put("includeSchema", true);
        resMap.put("fetchType", "take");
        resMap.put("outputSize", limitSize);
        if (StringUtils.isNotBlank(jobID)) resMap.put("jobId", jobID);
        log.info("{}{}", "预览sql:", mlsql);
        try {
            String runScript = mlsqlService.runScript(resMap);
            JSONObject jsonObject = JSONObject.parseObject(runScript);
            JSONObject schema = jsonObject.getJSONObject("schema");
            JSONArray fields = schema.getJSONArray("fields");
            List<String> sortColumns = getSortColumns(fields);
            if (!"cicadaFieldsSortMeta".equals(code)){
                //统一排序
                sortColumns.sort(String::compareToIgnoreCase);
            }
            resetFields(sortColumns, fields, dataColumnMetas, schema, jsonObject);

            return Result.success(jsonObject.toJSONString());
        } catch (Exception e) {
            throw new PreviewErrorException(e.getMessage());
        }
    }



    private void resetFields(List<String> sortColumns, JSONArray fields, Map<String, DataColumnMeta> dataColumnMetas, JSONObject schema, JSONObject jsonObject) {
       JSONArray sortField=new JSONArray();
        for (String col : sortColumns) {
            for (int i = 0; i < fields.size(); i++) {
                JSONObject resField = fields.getJSONObject(i);
                String name = resField.getString("name");
                resetColumnName(dataColumnMetas, col, name, resField,sortField);
            }
        }
        schema.put("fields", sortField);
        jsonObject.put("schema", schema);
    }

    private void resetColumnName(Map<String, DataColumnMeta> dataColumnMetas, String col, String name, JSONObject resField,JSONArray sortFields) {
        if (col.equals(name)) {
            DataColumnMeta dataColumnMeta = dataColumnMetas.get(name);
            String columnZhName = "";
            if (dataColumnMeta != null) {
                columnZhName = dataColumnMeta.getColumnZhName();
            }
            String type = resField.getString("type");
            if (type.contains("{")) {
                resField.put("type", "object");
            }
            resField.put("name", StringUtils.isNotBlank(columnZhName) ? columnZhName : name);
            resField.put("code", name);
            sortFields.add(resField);
        }
    }

    private List<String> getSortColumns(JSONArray fields) {
        List<String> sortColumns = Lists.newArrayList();
        for(int i=0;i<fields.size();i++){
            JSONObject resField = fields.getJSONObject(i);
            String name = resField.getString("name");
            sortColumns.add(name);
        }

        return sortColumns;
    }

    @GetMapping(value = "/previewCharts")
    public Result previewCharts(String transId, String tranStepId, HttpSession session) throws Exception {
        IPreviewChartsModelCreator creator = createCreator(tranStepId);
        String userId = (String) session.getAttribute("userId");
        Map<String, String> afterTable = mlsqlService.getAfterTableName(transId, tranStepId, true, null, userId);
        Map<String, Object> resMap = new HashMap<>();
        String mlsql = afterTable.get("sql");
        resMap.put("sql", mlsql);
        log.info("{}{}", "预览sql:", mlsql);
        String lastSql = getLastSql(mlsql);
        List<String> selects = getSelects(lastSql);
        String runScript = mlsqlService.runScript(resMap);
        JSONArray values = JSONObject.parseArray(runScript);
        return Result.success(creator.create(values, selects));
    }

    @NotNull
    private IPreviewChartsModelCreator createCreator(String tranStepId) throws ClassNotFoundException, NoSuchMethodException, InstantiationException, IllegalAccessException, java.lang.reflect.InvocationTargetException {
        TransMeta transMeta = this.transMetaService.getTransMetaById(tranStepId);
        String pluginCode = transMeta.getUsedPlugin().getCode();
        pluginCode = pluginCode.substring(0, 1).toUpperCase() + pluginCode.substring(1) + "Charts";
        Class<?> aClass = Class.forName("com.dragonsoft.cicada.datacenter.modules.modeling.charts." + pluginCode);
        Constructor<?> constructor = aClass.getConstructor();
        return (IPreviewChartsModelCreator) constructor.newInstance();
    }

    private String getLastSql(String sql) {
        sql = sql.trim();
        String[] split = sql.split("\\n");
        return split[split.length - 1];
    }

    private List<String> getSelects(String sql) {
        String trim = sql.substring(sql.indexOf("select") + 6, sql.indexOf("from")).trim();
        if (trim.startsWith("distinct")) {
            trim = trim.replace("distinct", "").replace("(", "").replace(")", "").trim();
        }
        String[] split = trim.split(",");

        List<String> result = Arrays.stream(split).map((s) -> {
            s = s.trim();
            if (s.contains("as")) {
                s = s.substring(s.indexOf("as") + 2).trim();
            } else if (s.contains(" ")) {
                s = s.trim().split(" ")[0];
            }
            return s;
        }).collect(Collectors.toList());
        return result;
    }

    //    @Value("${flink.job.submit.url}")
//    private String flinkUrl;
    @GetMapping(value = "/getSQL")
    public Result getSQL(String transId, HttpSession session, String type) {
        String sql = "";
        if (StringUtils.isBlank(type)) {
            String userId = (String) session.getAttribute("userId");
            try {
                sql = mlsqlService.getSql(transId, userId);
                if (Boolean.parseBoolean(isHiddenPassword)) {
                    Pattern scriptPattern = Pattern.compile("#.*?#", Pattern.CASE_INSENSITIVE);
                    sql = scriptPattern.matcher(sql).replaceAll("******");
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        } else {
//            Assert.hasLength(transId,"方案ID不能为空");
//            Assert.hasLength(flinkUrl,"flinkUrl不能为空".concat(flinkUrl));
//            String data= HttpUtil.get(flinkUrl.concat("engine/get/script/").concat(transId));
//            Map<String,Object> dataMap= null;
//            try {
//                dataMap = JSONObject.parseObject(data, HashMap.class);
//            } catch (Exception e) {
//                log.error(e.getMessage(),e);
//                Assert.fail(String.format("获取sql异常:[]",e.getMessage()));
//            }
//            if (dataMap.containsKey("code")&&Objects.equals(dataMap.get("code"),0)){
//                sql= dataMap.get("data").toString();
//            }else if (dataMap.containsKey("code")&&Objects.equals(dataMap.get("code"),1)){
//                Assert.fail(dataMap.get("msg").toString());
//            }else {
//                log.info(data);
//                Assert.fail("获取sql异常");
//            }
        }
        return Result.success(sql);
    }
}
