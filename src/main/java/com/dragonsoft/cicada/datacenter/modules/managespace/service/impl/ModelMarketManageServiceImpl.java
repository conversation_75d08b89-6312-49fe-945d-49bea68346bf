package com.dragonsoft.cicada.datacenter.modules.managespace.service.impl;

import com.code.common.paging.PageInfo;
import com.code.metadata.portal.PublishUrl;
import com.code.metaservice.portal.IPublishUrlService;
import com.dragoninfo.dfw.entity.TSysAuthUser;
import com.dragoninfo.dfw.service.SysAuthUserService;
import com.dragonsoft.cicada.datacenter.modules.managespace.service.IModelMarketManageService;
import com.dragonsoft.cicada.datacenter.modules.system.dataportal.vo.PublishUrlVo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/8/31
 */
@Service
public class ModelMarketManageServiceImpl implements IModelMarketManageService {

    @Autowired
    private IPublishUrlService publishUrlService;
    @Autowired
    private SysAuthUserService sysAuthUserService;

    @Override
    public PageInfo queryPageByType(String name, String type, int page, int pageSize) {
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageIndex(page);
        pageInfo.setPageSize(pageSize);
        PageInfo newPageInfo = publishUrlService.queryPageByType(pageInfo,name,type);

        List<Map> mapData = pageInfo.getDataList();
        for(Map map : mapData){
            Map<String, String> params = Maps.newHashMap();
            params.put("id", map.get("operateuserid").toString());
            TSysAuthUser tSysAuthUser = (TSysAuthUser) sysAuthUserService.query(params);
            map.put("creatName",tSysAuthUser.getObjName());
        }

        newPageInfo.setDataList(mapData);
        return newPageInfo;
    }

    @Override
    public void saveOrUpdataPublishUrl(String id, String type, String name, String url, String userId) {
        PublishUrl publishUrl = publishUrlService.queryByOwnerId(id);
        Timestamp d = new Timestamp(System.currentTimeMillis());
        if(null != publishUrl){
            publishUrl.setName(name);
            publishUrl.setPublishUrl(url);
            publishUrl.setPublishState("2");
//            publishUrl.setOperateTime(d.toString());
            publishUrlService.update(publishUrl);
        }else {
            PublishUrl newPublishUrl = new PublishUrl();
            newPublishUrl.setType(type);
            newPublishUrl.setName(name);
            newPublishUrl.setOwnerId(id);
            newPublishUrl.setPublishState("2");
            newPublishUrl.setPublishUrl(userId);
            publishUrlService.save(newPublishUrl);
        }
    }

    @Override
    public void changePublishState(String id, String stat) {
        PublishUrl publishUrl = publishUrlService.get(PublishUrl.class, id);
        publishUrl.setPublishState(stat);
        publishUrlService.save(publishUrl);
    }

    @Override
    public List<PublishUrlVo> getReviewLaunchUrl() {
        List<PublishUrlVo> publishUrlVos = Lists.newArrayList();
        List<PublishUrl> publishUrls = publishUrlService.getReviewLaunchUrl();
        for(PublishUrl publishUrl : publishUrls){
            PublishUrlVo publishUrlVo = new PublishUrlVo();
            publishUrlVo.setId(publishUrl.getId());
            publishUrlVo.setName(publishUrl.getName());
            publishUrlVo.setUrl(publishUrl.getPublishUrl());
            publishUrlVos.add(publishUrlVo);
        }
        return publishUrlVos;
    }
}
