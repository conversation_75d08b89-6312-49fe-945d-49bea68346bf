package com.dragonsoft.cicada.datacenter.modules.datavisual.controller;

import com.code.common.utils.R;
import com.code.metadata.datavisual.Field;
import com.code.metaservice.dataSet.IDataSetTreeService;
import com.code.metaservice.datawarehouse.model.DatasetTreeModel;
import com.code.metaservice.ddl.ILogicDataObjService;
import com.code.metaservice.standmb.vo.VisualMbTreeVo;
import com.dragoninfo.dfw.bean.Result;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.service.IDataSetOperationService;
import com.dragonsoft.cicada.datacenter.modules.datavisual.dataset.IDataSetBuilder;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.service.IUserService;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.service.MbService;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.vo.MbEnumTreeVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@CrossOrigin
@RestController
@RequestMapping("/dataSet")
@PropertySource("classpath:case-config.properties")
public class DataSetController {

    @Autowired
    IDataSetBuilder dataSetBuilder;

    @Autowired
    private IDataSetOperationService dataSetOperationService;
    @Autowired
    private IDataSetTreeService dataSetTreeService;
    @Autowired
    private IUserService userService;
    @Autowired
    private ILogicDataObjService logicDataObjService;
    @Autowired
    private MbService mbService;

    @Value("${standModel}")
    private boolean standModel;

    @Value("${sceModel}")
    private boolean sceModel;
    /**
     * @return
     */
    @GetMapping("/list")
    public Result getDataSetList(HttpServletRequest request, @RequestParam(required = false, defaultValue = "") String currentDataSetId,String ignoreType) {
        String userId = (String) request.getSession().getAttribute("userId");
        //自定义数据集的树
        List<DatasetTreeModel> datasetTreeModels = dataSetOperationService.queryDataSetTree(userId, true, currentDataSetId);
        List<String> authIds = new ArrayList<>();
        if (null != userId) {
            authIds = userService.getAllAuthId(userId);
        }
        //源数据集的树
        List<DatasetTreeModel> treeModelList = dataSetOperationService.querySourceDatasetTree(userId, currentDataSetId,ignoreType);
        List<DatasetTreeModel> mergeTreeModels = dataSetOperationService.mergeDataSetTree(datasetTreeModels, treeModelList);
        if (!sceModel){
            DatasetTreeModel datasetTreeModel = mergeTreeModels.get(0);
            DatasetTreeModel datasetTreeModel1 = datasetTreeModel.getChildren().get(0);
            for (int i = 0; i < datasetTreeModel1.getChildren().size(); i++) {
                if ("场景案例".equals(datasetTreeModel1.getChildren().get(i).getName())){
                    datasetTreeModel1.getChildren().remove(i);
                }
            }
        }
        if (!standModel){
            DatasetTreeModel datasetTreeModel = mergeTreeModels.get(0);
            DatasetTreeModel datasetTreeModel1 = datasetTreeModel.getChildren().get(1);
            for (int i = 0; i < datasetTreeModel1.getChildren().size(); i++) {
                if ("标准模型".equals(datasetTreeModel1.getChildren().get(i).getName())){
                    datasetTreeModel1.getChildren().remove(i);
                }
            }
        }
        return Result.toResult(R.ok(mergeTreeModels));
    }

    public void childrenIgnore(List<DatasetTreeModel> model,List<String> ignoreTypes){
        List<DatasetTreeModel> ignore=new ArrayList<>();
        for (DatasetTreeModel c : model) {
            if (null == c.getInstanceType()){
                List<DatasetTreeModel> children=c.getChildren();
                childrenIgnore(children,ignoreTypes);
            }else {
                if (ignoreTypes.contains(c.getInstanceType())){
                    ignore.add(c);
                }
            }
        }
        model.removeAll(ignore);
    }

    @GetMapping("/getDataSetFields")
    public Result getDataSetFields(String dataSetId) {
        List<Field> fields = dataSetTreeService.getDataSetFields(dataSetId);
        //获取码表的所有值
        List<MbEnumTreeVo> codes = null;
        List<VisualMbTreeVo> allList = mbService.getVisualMbAllList();
        for (int i = 0; i < allList.size(); i++) {
            Map<String, String> visualMap = (Map<String, String>) allList.get(i);
            if ("周期时间".equalsIgnoreCase(visualMap.get("name"))) {
                codes = mbService.getCodesById(visualMap.get("id"));
            }
        }
        if (codes != null) {
            List<String> collect = codes.stream().map(MbEnumTreeVo::getValue).collect(Collectors.toList());
            for (Field field : fields) {
                if (collect.contains(field.getCode()) || "z_w".equalsIgnoreCase(field.getCode())) {
                    field.setMbColumn(true);
                }
            }
        }
        return Result.toResult(R.ok(fields));
    }

    @GetMapping("/listAll")
    public Result getDataSetListAll() {
        return Result.toResult(R.ok());
    }

    @RequestMapping("/isHaveDataSet")
    public Result isHaveDataSet() {
        List<String> dataSetOperationVos = dataSetOperationService.getAllLogicDataObj();
        return Result.toResult(R.ok(dataSetOperationVos));
    }

    /**
     * 获取字段枚举值
     *
     * @return
     */
    public Result getFiledEnumVal() {
        return Result.toResult(R.ok());
    }


}
