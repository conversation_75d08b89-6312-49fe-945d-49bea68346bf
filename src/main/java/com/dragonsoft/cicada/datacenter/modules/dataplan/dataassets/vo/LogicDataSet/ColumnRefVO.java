package com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.vo.LogicDataSet;

/**
 * @Description:
 * @Title: ColumnRefVO
 * @Package com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.vo.LogicDataSet
 * @Author: <EMAIL>
 * @CreateTime: 2023/4/19 13:52
 */

import lombok.Data;

@Data
public class ColumnRefVO {

    private String name;

    private String code;

    private String alias;

    private String dataTypeId;

    private String displayTypeId;

    /**
     * 数据格式(比如时间数据格式等)
     */
    private String format;

    /**
     * 数据格式转换
     */
    private String numberFormat;

    /**
     * 对应的表别称
     */
    private String objAlias;
}
