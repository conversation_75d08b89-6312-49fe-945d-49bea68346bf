UPDATE "public"."t_sys_func" set is_doc_show = '1' where func_code in
('modelServiceGenerateAPI', 'modelServiceTest', 'modelServiceGetResultTest', 'AIModelingTimedTask','processModelingSaseSettingPage','scenarioCaseAiModel','accessBusManagementMsgInfoDelete','accessBusManagementMsgInfoDetails','accessBusManagementMsgInfoEdit','accessBusManagement','AIModeling','AIModelingDelete','AIModelingMove','AIModelingSaveAs','AIModelingReName','AIModelingEdit','AIModelingStartModel','AIModelingCreateModel','accessBusManagementSaved','accessBusManagementAddNewService','accessBusManagementAddServiceInterface','accessBusManagementDelete','accessBusManagementDetails','accessBusManagementEdit','logQueryViewDetails','logQuery','scenarioCaseFunc','scenarioCaseDataModel','scenarioCaseDashboard','scenarioCaseGateway','dataConnectionMetadata','dataSetOperationSaveAs','dataSetOperationCreateAPI','serviceManagementStart','fastAnalysisDeleteTrans','fastAnalysisCopyTrans','fastAnalysisUpdateTransName','fastAnalysisEdit','fastAnalysisSaveTempTrans','fastAnalysisQueryTransTree','fastAnalysis','serviceManagementTest','serviceManagementSharing','serviceManagementDeactivate','serviceManagementUninstallg','serviceManagementBatchSharing','serviceManagementGenerateAPI','serviceManagement','dataSetOperationMoveLogicDataSet','dataConnectionMove','dataConnectionSaveAs','dataConnectionDeleteTable','processModeling','dashboard','themePortal','dataSetOperation','dataConnection','udfOperatorManage','userManagement','roleManagement','groupManagement','processModelingDeleteTrans', 'processModelingCopyTrans', 'processModelingSaveTempTrans', 'processModelingGetDataSets', 'processModelingStartJob','processModelingQueryTransTree',
            'processModelingResultReuseObj', 'processModelingUpdateTransName', 'dashboardUpdateGroup','visualEditGetNoChart','dashboardSaveOrUpdate',
            'dashboardDelete', 'dashboardCopy','dashboardGetListByGroupId', 'dashboardGetDashboard', 'roleManagementQueryRole','roleManagementSetAuth','roleManagementUpdataRole','roleManagementDeleteRole','roleManagementAddRole','userManagementAddUser','userManagementQueryUser',
            'userManagementEditUser','userManagementDeleteUser','userManagementResetPassword', 'groupManagementAddUserGroup','groupManagementQueryGroup','groupManagementDeleteUserGroup','groupManagementEditUserGroup','dataSetOperationAccreditLogicDataObj','dataSetOperationPreviewData',
            'dataSetOperationSetBatchSharing','dataSetOperationSetSingleShare','dataSetOperationDataColumn','dataSetOperationDeleteDataSet', 'dataConnectionTestConnection','dataConnectionAddDataTable', 'dataConnectionEditDataSource','dataConnectionDeleteDataSource','dataConnectionSynchronousDataSource',
            'dataWarehouseDeleteTable','dataConnectionAddDataSource','udfOperatorManageCreateUdf','udfOperatorManageEditUdf','udfOperatorManageDeleteUdf','themePortal','themePortalDeletePortal','themePortalGetPortalPageByUrl','themePortalCreatePortal','themePortalUpDataPortal','serviceManagementedit','AIModelingLookOver','dataMiningLookOver','AIModelingBuildApi','AIModelingTestApi','serviceManagementLookOver');


INSERT INTO "public"."t_sys_func_exp"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('cicadaDataCenter', '0', '数据中心', NULL, NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1','1');

INSERT INTO "public"."t_sys_func_exp"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('variableSetting', '0', '参数设置', 'processModeling', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1','1');
INSERT INTO "public"."t_sys_func_exp"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('cicadaStandardSqlInput', '0', '关系库表输入', 'processModeling', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1','1');
INSERT INTO "public"."t_sys_func_exp"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('cicadaStandardSqlOutput', '0', '关系库表输出', 'processModeling', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1','1');
INSERT INTO "public"."t_sys_func_exp"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('cicadaFullTextOutput', '0', '全文库表输出', 'processModeling', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1','1');
INSERT INTO "public"."t_sys_func_exp"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('cicadaServiceInputMeta', '0', '服务输入', 'processModeling', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1','1');
INSERT INTO "public"."t_sys_func_exp"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('cicadaFileOutputMeta', '0', '文件下载', 'processModeling', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1','1');
INSERT INTO "public"."t_sys_func_exp"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('cicadaFileInputMeta', '0', '文件上传', 'processModeling', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1','1');
INSERT INTO "public"."t_sys_func_exp"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('cicadaUnionJoinPlugin', '0', '合并行', 'processModeling', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1','1');
INSERT INTO "public"."t_sys_func_exp"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('cicadaSubtractByKeyPlugin', '0', '求差集', 'processModeling', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1','1');
INSERT INTO "public"."t_sys_func_exp"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('cicadaCollisionPlugin', '0', '合并列', 'processModeling', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1','1');
INSERT INTO "public"."t_sys_func_exp"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('cicadaFieldFilteringMeta', '0', '字段过滤', 'processModeling', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1','1');
INSERT INTO "public"."t_sys_func_exp"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('cicadaDataDistinctMeta', '0', '数据去重', 'processModeling', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1','1');
INSERT INTO "public"."t_sys_func_exp"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('cicadaConditionFilterPlugin', '0', '条件过滤', 'processModeling', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1','1');
INSERT INTO "public"."t_sys_func_exp"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('cicadaServiceOrganization', '0', '表达式处理', 'processModeling', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1','1');
INSERT INTO "public"."t_sys_func_exp"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('cicadaObjectToJsonMeta', '0', '行数据转json', 'processModeling', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1','1');
INSERT INTO "public"."t_sys_func_exp"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('cicadaFieldsSettings', '0', '字段设置', 'processModeling', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1','1');
INSERT INTO "public"."t_sys_func_exp"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('cicadaDataSortPlugin', '0', '数据排序', 'processModeling', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1','1');
INSERT INTO "public"."t_sys_func_exp"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('serviceOrganization', '0', '算子编排', 'processModeling', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1','1');
INSERT INTO "public"."t_sys_func_exp"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('cicadaDateCicadaZipperTablePlugin', '0', '时间拉链', 'processModeling', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1','1');
INSERT INTO "public"."t_sys_func_exp"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('cicadaScriptMeta', '0', 'SQL脚本', 'processModeling', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1','1');
INSERT INTO "public"."t_sys_func_exp"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('cicadaJsonParsingContent', '0', 'json解析插件', 'processModeling', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1','1');
INSERT INTO "public"."t_sys_func_exp"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('cicadaNumberCicadaZipperTablePlugin', '0', '数值拉链', 'processModeling', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1','1');
INSERT INTO "public"."t_sys_func_exp"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('cicadaLabelMeta', '0', '数据打标', 'processModeling', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1','1');
INSERT INTO "public"."t_sys_func_exp"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('cicadaReducePlugin', '0', '分组统计', 'processModeling', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1','1');
INSERT INTO "public"."t_sys_func_exp"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('cicadaMarkingTimeMeta', '0', '时间离散化', 'processModeling', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1','1');
INSERT INTO "public"."t_sys_func_exp"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('cicadaEffectivePolice', '0', '24小时有效警情', 'processModeling', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1','1');
INSERT INTO "public"."t_sys_func_exp"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('cicadaPeerContentMeta', '0', '同行插件', 'processModeling', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1','1');
INSERT INTO "public"."t_sys_func_exp"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('cicadaMetaServiceInput', '0', '服务输入插件', 'processModeling', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1','1');
INSERT INTO "public"."t_sys_func_exp"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('cicadaMetaServiceOutput', '0', '服务输出插件', 'processModeling', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1','1');
INSERT INTO "public"."t_sys_func_exp"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('cicadaModelServiceMeta', '0', '模型服务', 'processModeling', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1','1');


INSERT INTO "public"."t_sys_func_exp"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('dashboardQuery', '0', '查询', 'dashboard', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1','1');
INSERT INTO "public"."t_sys_func_exp"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('dashboardContent', '0', '文本', 'dashboard', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1','1');
INSERT INTO "public"."t_sys_func_exp"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('transverseHistogram', '0', '横向柱状图', 'dashboard', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1','1');
INSERT INTO "public"."t_sys_func_exp"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('horizontalStackedHistogram', '0', '横向堆叠柱状图', 'dashboard', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1','1');
INSERT INTO "public"."t_sys_func_exp"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('stackedColumnChart', '0', '堆叠柱状图', 'dashboard', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1','1');
INSERT INTO "public"."t_sys_func_exp"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('basicHistogram', '0', '基本柱状图', 'dashboard', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1','1');
INSERT INTO "public"."t_sys_func_exp"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('areaMap', '0', '面积图', 'dashboard', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1','1');
INSERT INTO "public"."t_sys_func_exp"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('basicLineChart', '0', '基本折线图', 'dashboard', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1','1');
INSERT INTO "public"."t_sys_func_exp"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('nestedPieChart', '0', '嵌套饼图', 'dashboard', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1','1');
INSERT INTO "public"."t_sys_func_exp"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('basicPieChart', '0', '基本饼图', 'dashboard', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1','1');
INSERT INTO "public"."t_sys_func_exp"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('dashboardTable', '0', '表格', 'dashboard', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1','1');
INSERT INTO "public"."t_sys_func_exp"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('combinationDiagram', '0', '组合图', 'dashboard', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1','1');
INSERT INTO "public"."t_sys_func_exp"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('wordCloudDiagram', '0', '词云图', 'dashboard', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1','1');
INSERT INTO "public"."t_sys_func_exp"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('thermodynamicDiagram', '0', '热力图', 'dashboard', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1','1');
INSERT INTO "public"."t_sys_func_exp"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('PGISMap', '0', 'PGIS地图', 'dashboard', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1','1');
INSERT INTO "public"."t_sys_func_exp"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('dashboardTab', '0', 'Tab标签', 'dashboard', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1','1');
INSERT INTO "public"."t_sys_func_exp"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('bubbleMap', '0', '气泡地图', 'dashboard', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1','1');
INSERT INTO "public"."t_sys_func_exp"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('colorMap', '0', '色彩地图', 'dashboard', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1','1');
INSERT INTO "public"."t_sys_func_exp"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('basicMap', '0', '基本地图', 'dashboard', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1','1');
INSERT INTO "public"."t_sys_func_exp"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('indexCard', '0', '指标卡', 'dashboard', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1','1');
INSERT INTO "public"."t_sys_func_exp"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('radarChart', '0', '雷达图', 'dashboard', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1','1');
INSERT INTO "public"."t_sys_func_exp"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('relationDiagram', '0', '关系图', 'dashboard', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1','1');
INSERT INTO "public"."t_sys_func_exp"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('dashboardForm', '0', '表单', 'dashboard', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1','1');
INSERT INTO "public"."t_sys_func_exp"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('businessRelationshipDiagram', '0', '业务关系图', 'dashboard', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1','1');