package com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.service;

import com.code.common.spark.model.SparkColumnMeta;
import com.code.dragonsoft.dataquery.service.querymodel.ColumnDataModel;
import com.dragoninfo.dfw.bean.Result;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.vo.LogicDataSet.PreviewVo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Wu.D.J
 * @Create : 2020.07.29
 */
public interface IQuickSqlAnalysisService {

    /**
     * 预览sql查询数据
     *
     * @param sql sql语句
     * @param schemaId schemaId
     * @return 数据集合
     */
    ColumnDataModel previewSqlData(String sql, String schemaId);

    /**
     * 获取sql查询字段信息
     * @return 字段集合
     */
    List<SparkColumnMeta> getSqlColumns(PreviewVo previewVo);

    /**
     * 更新即席SQL数据集
     *
     * @param dataObjId   数据集id
     * @param dataObjName 数据集名称
     * @param sql         sql语句
     */
    void updateQuickSqlDataObj(String dataObjId, String dataObjName, String sql);

    /**
     * 保存即席SQL数据集
     *
     * @param userId      用户id
     * @param classifyId  数据集挂接目录id
     * @param dataObjName 数据集中文名name
     * @param dataObjCode 数据集英文名code
     * @return 数据集id
     */
    String saveQuickSqlDataObj(String userId, String classifyId, String dataObjName, String dataObjCode,String schemaId);

    /**
     * 查询SparkTable里面全部的注册表
     *
     * @return 表信息VO
     */
    List<Map<String,String>> queryRegisteredJson(String schemaId);

    /**
     * 预览数据分页查询
     */
    Result previewSqlDataPage(PreviewVo previewVo);

    /**
     * 保存即席SQL数据集
     */
    Result saveQuickSqlDataObj(Map<String, Object> paramsMap);

    /**
     * 修改即席SQL数据集
     */
    Result updateQuickSqlDataObj(Map<String, Object> paramsMap);
}
