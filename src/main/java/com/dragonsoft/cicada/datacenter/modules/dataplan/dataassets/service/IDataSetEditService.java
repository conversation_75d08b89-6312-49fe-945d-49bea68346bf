package com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.service;

import com.code.common.model.step.BaseDataStep;
import com.code.common.paging.PageInfo;
import com.code.dataset.AbstractAtomDataSetOperator;
import com.code.dataset.operator.column.addcolumn.AddColumnStep;
import com.code.dataset.operator.column.deletecolumn.DeleteColumnStep;
import com.code.dataset.operator.column.editcolumn.EditColumnStep;
import com.code.dataset.operator.column.format.FormatStep;
import com.code.dataset.operator.column.indextype.IndexTypeStep;
import com.code.dataset.operator.column.numberformat.NumberFormatStep;
import com.code.dataset.operator.column.synccolumn.SyncColumnStep;
import com.code.dataset.operator.column.synccolumn.vo.LogicSyncColumn;
import com.code.dataset.operator.filter.FilterConditionStep;
import com.code.dataset.operator.join.JoinTableStep;
import com.code.dataset.operator.join.UnionTableStep;
import com.code.metadata.res.common.ClassifierStat;
import com.code.metadata.res.ddl.DataSetStepRelation;
import com.code.metadata.res.ddl.LogicDataColumn;
import com.code.metadata.res.ddl.LogicDataObj;
import com.code.metaservice.ddl.vo.LogicDataSetColumnVo;
import com.code.metaservice.ddl.vo.LogicHttpColumns;
import com.dragoninfo.dfw.bean.Result;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.qo.LogicDataExportQo;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.vo.ElementUrlVO;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.vo.LogicDataSet.*;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.vo.StepVo;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @date 2020/7/27
 */
public interface IDataSetEditService {
    /**
     * 创建新的数据集
     *
     * @param id
     * @param name
     * @param userId
     * @param dsTypeId
     * @return
     */
    String createLogicDataSet(String id,String dsTypeId, String name, String userId);

    /**
     * 获取数据集
     *
     * @param id
     * @return
     */
    LogicDataSetVo getLogicDataInfo(String id, String newDataSetId);


    /**
     * 编辑字段步骤
     *
     * @param editColumnStep
     * @return
     */
    void editDataSetColumn(EditColumnStep editColumnStep);

    /**
     * 新增列步骤
     *
     * @param addColumnStep
     */
    LogicSyncColumn addDataSetColumn(AddColumnStep addColumnStep);

    /**
     * 分页预览
     *
     * @return
     * @
     */
    PageInfo preview(PreviewVo previewVo);

    PageInfo previewBySql(PreviewVo previewVo);

    String transTransVar(String sql,String userId);

    PageInfo preview(SelfHelpDataSetPreviewByStepVo previewVo);

    /**
     * 预览指定字段列数据
     *
     * @param previewVo
     * @return
     */
    PageInfo prviewDataWithColumn(PreviewVo previewVo);

    /**
     * 保存
     *
     * @param saveDataSetVo
     */
    void save(SaveDataSetVo saveDataSetVo);

    /**
     * 获取操作轨迹
     *
     * @param dataSetId
     * @return
     */
    List<DataSetStepRelationVo> getDataSetStepRelation(String dataSetId);

    String getRelationId(String dataSetId);

    /**
     * 根据id获取字段
     *
     * @param columnId
     * @return
     */
    LogicSyncColumn findLogicDataColumnById(String columnId);


    void addLogicDataStepFromFather(String currentDataSetId, String addDataSetId);


    BaseDataStep getDataSetStepInfo(String id);

    /**
     * 分页查询
     *
     * @param sql            sql
     * @param pageInfo       分页信息
     * @param logicDataObjId 逻辑数据集id
     * @return
     */
    PageInfo getSqlColumnsForPageInfo(String sql, PageInfo pageInfo, String logicDataObjId);

    PageInfo getSqlColumnsForPageInfoDbType(String sql, PageInfo pageInfo, String dbType, ClassifierStat classifierStat);

    /**
     * 获取sql
     *
     * @param dataObj
     * @return
     */
    String getSqlByLogicDataObj(LogicDataObj dataObj, String addDataObjId);

    /**
     * 维度和度量的类型转换
     *
     * @param formatStep
     */
    void format(FormatStep formatStep);

    /**
     * 删除字段
     *
     * @param deleteColumnStep
     */
    void deleteColumn(DeleteColumnStep deleteColumnStep);

    /**
     * 度量和维度转换
     *
     * @param indexTypeStep
     */
    void indexType(IndexTypeStep indexTypeStep);

    /**
     * 数字格式转化
     *
     * @param numberFormatStep
     */
    void numberFormat(NumberFormatStep numberFormatStep);

    /**
     * 设置过滤条件
     *
     * @param filterConditionStep
     */
    void filter(FilterConditionStep filterConditionStep);

    /**
     * 同步数据结构步骤
     *
     * @param syncColumnStep
     */
    void syncColumnStep(SyncColumnStep syncColumnStep);

    /**
     * 同步数据结构步骤
     *
     * @param syncColumnStep
     */
    void syncColumnStepDCThree(SyncColumnStep syncColumnStep);

    /**
     * 保存同步的数据结构
     *
     * @param syncColumnStep
     */
    void saveSyncColumn(SyncColumnStep syncColumnStep);

    /**
     * @param columns   字段列表
     * @param dataSetId 数据集id
     */
    void saveOrUpdateDataSetColumn(List<LogicDataSetColumnVo> columns, String dataSetId);

    /**
     * 保存同步的数据结构 --包含数据格式转换
     *
     * @param syncColumnStep
     */
    void saveSyncColumnDCThree(SyncColumnStep syncColumnStep);


    /**
     * 左右合并
     *
     * @param joinTableStep
     */
    LogicDataSetVo join(JoinTableStep joinTableStep);

    void multiJoinAndFilter(String dataSetId, List<JoinTableStep> joinVos, FilterConditionStep filterConditionStep);

    void editSelfHelpDataSet(DataSetJoinVo dataSetJoinVo);

    /**
     * 上下合并
     *
     * @param unionTableStep
     */
    void union(UnionTableStep unionTableStep);

    /**
     * 获取函数
     *
     * @param condition
     * @return
     */
    List<FunctionVo> getFunction(String condition);

    /**
     * 获取数据集字段
     *
     * @param id
     * @param condition
     * @return
     */
    List<LogicSyncColumn> getLogicColumn(String id, String condition);

    /**
     * 删除操作步骤
     *
     * @param id
     * @param dataSetId
     */
    void deleteDataStep(String id, String dataSetId);

    /**
     * 编辑操作步骤
     *
     * @param stepVo
     */
    void editDataStep(StepVo stepVo);

    /**
     * 校验数据的父关系
     *
     * @param newDataSetId
     * @return
     */
    boolean checkHasParentId(String newDataSetId);

    /**
     * 根据type获取join或filter操作步骤
     *
     * @param type
     * @param dataSetId
     * @return
     */
    BaseDataStep getJoinAndFilter(String type, String dataSetId);

    List<BaseDataStep> getJoinAndFilters(String type, String dataSetId);

    List<BaseDataStep> getAllStep(String dataSetId);

    /**
     * 保存
     *
     * @param saveDataSetVo
     * @param userId
     */
    void saveAs(SaveDataSetVo saveDataSetVo, String userId);

    /**
     * 获取同步字段信息
     *
     * @param res
     * @return
     */
    List<ColumnDataSetVo> getSyncColumnVo(Map<String, List<LogicDataColumn>> res);

    /**
     * 根据数据集获取所有操作步骤
     *
     * @param dataSetId
     * @return
     */
    List<AbstractAtomDataSetOperator> getSetOperators(String dataSetId);

    /**
     * 根据关系获取数据集的所有步骤
     *
     * @param dataSetId
     * @param relation
     * @return
     */
    List<AbstractAtomDataSetOperator> getSetOperators(String dataSetId, DataSetStepRelation relation);

    /**
     * 创建视图
     *
     * @param setOperators
     * @param dataSetId
     * @param isCreate
     */

    void stepRegisterTable(List<AbstractAtomDataSetOperator> setOperators, String dataSetId, boolean isCreate);

    /**
     * 校验字段类型是否相同
     *
     * @param dT1
     * @param dT2
     * @return
     */
    boolean checkDataType(String dT1, String dT2);

    void createOldView(String viewName) throws Exception;

    /**
     * 创建logicdataobj的search_sql,这边主要是因为改掉原本的创建视图逻辑。
     *
     * @param datasetId
     * @throws Exception
     */
    void createLogicDatObjSearchSql(String datasetId) throws Exception;

    /**
     * 替换所有logicDataObj的search_sql，需要嵌套，因为可能一个logicdataobj是由另外一个logicdataobj派生出来的
     *
     * @throws Exception
     */
    void replaceLogicDataObjSearchsql() throws Exception;

    /**
     * 创建视图
     *
     * @param logicDataObj 数据集实体bean
     * @param sql          用于创建视图的sql
     * @param isCreate
     */
    void registerTable(LogicDataObj logicDataObj, String sql, boolean isCreate) throws Exception;

    /**
     * 创建视图
     *
     * @param schemaId
     * @param logicDataObj 数据集实体bean,sql成员变量用于创建视图
     * @param isCreate
     * @throws Exception
     */
    void registerTable(String schemaId, LogicDataObj logicDataObj, boolean isCreate) throws Exception;

    /**
     * 创建视图
     *
     * @param viewName     视图的名称
     * @param logicDataObj 数据集的实体bean，要创建实体的数据集的来源数据集
     * @param sql          用于创建视图的sql
     * @param isCreate
     * @throws Exception
     */
    void registerTable(String viewName, LogicDataObj logicDataObj, String sql, boolean isCreate) throws Exception;

    /**
     * 删除视图
     *
     * @param schemaId
     * @param logicDataObj
     * @throws Exception
     */
    void dropView(String schemaId, LogicDataObj logicDataObj) throws Exception;

    /**
     * 校验数据集是否被使用
     *
     * @param logicDataSetId
     * @return
     */
    List<Map> checkLogicDataSetUsed(String logicDataSetId);

    /**
     * 创建模型视图
     *
     * @param setOperators
     * @param dataSetId
     * @throws Exception
     */
    void registerModelView(List<AbstractAtomDataSetOperator> setOperators, String dataSetId) throws Exception;


    /**
     * 获取数据集sql
     *
     * @param dataSetId
     * @return
     */
    String getLogicSearchSQL(String dataSetId);


    /**
     * 同步数据集
     */
    void syncColumns(Map<String, List<LogicHttpColumns>> columns);

    /**
     * 获取报错字段
     *
     * @param logicId
     * @return
     */
    SyncColumnsVo getErrorColumns(String logicId);

    /**
     * 预览导出功能
     * @param qo
     * @param response
     */
    void logicDataExport(LogicDataExportQo qo, HttpServletResponse response);

    /**
     * 获取下载文件最大条数限制
     * @return
     */
    Result<Integer> getExportLimit();


    /**
     * 根据数据集预览数据字段查询
     * @param previewVo
     * @return
     */
    Map<String, List<ElementUrlVO>> getElementByPreview(PreviewVo previewVo);
}
