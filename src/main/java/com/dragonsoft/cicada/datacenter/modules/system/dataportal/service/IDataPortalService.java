package com.dragonsoft.cicada.datacenter.modules.system.dataportal.service;

import com.code.common.paging.PageInfo;
import com.code.metaservice.datawarehouse.model.DatasetTreeModel;
import com.dragonsoft.cicada.datacenter.modules.system.dataportal.vo.PortalConfigVo;

import java.lang.reflect.InvocationTargetException;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @date 2020/8/31
 */
public interface IDataPortalService {


    /**
     * 获取门户目录树
     *
     * @param userId
     * @return
     */
    List<DatasetTreeModel> queryTree(String userId);

    DatasetTreeModel getOtherSpaceTree(String userId);

    String addTreeNode(String pId, String name, String userId);

    void deleteTreeNode(String nodeId, String userId);

    void editTreeNode(String id, String pId, String name);

    void upDataSecurityModeAndTime(String portalId, String securityMode, String beginTime, String endTime);

    /**
     * 创建或修改门户配置
     *
     * @param portalConfigVo 门户配置vo
     * @param userId         登录者
     * @param dirId          目录id
     * @return 门户配置id
     */
    Map<String,String> createOrUpdatePortal(PortalConfigVo portalConfigVo, String userId, String dirId);

    /**
     * 删除门户
     *
     * @param portalId 门户id
     */
    void deletePortal(String portalId);

    /**
     * 门户查看
     *
     * @param userId   登录用户
     * @param keyWord  查询名称
     * @param pageInfo 分页信息
     * @return page页信息
     */
    PageInfo getPortalPage(String userId, String keyWord, String dirId, PageInfo pageInfo) throws InvocationTargetException, IllegalAccessException;

    /**
     * 查看门户配置
     *
     * @param portalId 门户id
     * @return 门户配置信息vo
     */
    PortalConfigVo getPortalConfig(String portalId, String userId);

    /**
     * 通过门户发布的地址访问门户
     *
     * @param userId           登录用户id
     * @param portalPublishUrl 访问的http地址
     * @return 门户配置信息vo
     */
    PortalConfigVo getPortalPageByUrl(String userId, String portalPublishUrl);

    void copy(String id,String dirId,String portalName,String userId);



}
