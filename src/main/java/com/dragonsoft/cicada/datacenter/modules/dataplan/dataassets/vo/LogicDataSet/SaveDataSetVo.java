package com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.vo.LogicDataSet;

import io.swagger.annotations.ApiParam;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020.7.27
 */
@Data
public class SaveDataSetVo implements Serializable {
    @ApiParam(value = "数据集id")
    private String logicDataSetId;
    @ApiParam(value = "数据集名称")
    private String name;
    @ApiParam(value = "原始数据集的id:[String]")
    private List<String> metaDataObjIds;

    @ApiParam(value = "另存为数据集的id")
    private String saveAsId;

    @ApiParam(value = "保存数据集的时候绑定目录id")
    private String classifyId;

//    @ApiParam(value = "[{" +
//            "name:xx" +
//            "code:xx" +
//            "id:xx" +
//            "indexType:xx" +
//            "memo:xx" +
//            "}]")
//    private List<LogicColumnInfoVo> columns;
//    @ApiParam(value = "步骤id")
//    private String relationId ;
}
