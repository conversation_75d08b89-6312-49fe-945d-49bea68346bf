package com.dragonsoft.cicada.datacenter.modules.modeling.service.impl;

import com.code.metadata.scenario.TScenarioCase;
import com.code.metadata.scenario.TScenarioCaseRel;
import com.code.metadata.scenario.TScenarioCaseType;
import com.code.metaservice.scenario.ScenarioCaseRelImpl;
import com.code.metaservice.scenario.ScenarioCaseTypeService;
import com.dragonsoft.cicada.datacenter.modules.modeling.service.ITransScenarioTemplateService;
import com.fw.service.BaseService;
import com.fw.service.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.DigestUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 版权所有：厦门市巨龙信息科技有限公司
 * Copyright 2018 Xiamen Dragon Information Technology Co. Ltd.
 * All right reserved.
 * ====================================================
 * 修订记录：
 * No    日期				作者(操作:具体内容)
 * 1.    2021/9/15 11:25			baijx（<EMAIL>）(创建:创建文件)
 * ====================================================
 * 类描述：
 */
@Service
public class TransScenarioTemplateServiceImpl extends BaseService implements ITransScenarioTemplateService {

    @Autowired
    private ScenarioCaseTypeService scenarioCaseTypeService;

    @Autowired
    private ScenarioCaseRelImpl scenarioCaseRel;

    @Override
    public List<Map<String, String>> queryTransByName(List<String> modelNames) {
        String sql = "select * from t_etl_trans where name in (%s)";
        return this.baseDao.sqlQueryForList(String.format(sql, String.join(",", modelNames)));
    }

    @Override
    public List<Map<String, String>> queryDashboardByName(List<String> modelNames) {
        String sql = "select * from  t_v_dashboards where name in (%s)";
        return this.baseDao.sqlQueryForList(String.format(sql, String.join(",", modelNames)));

    }
    @Override
    public List<Map<String, String>> queryScriptInfoByName(List<String> modelNames) {
        String sql = "select * from  t_script_info where name in (%s)";
        return this.baseDao.sqlQueryForList(String.format(sql, String.join(",", modelNames)));

    }

    @Override
    public List<Map<String, String>> queryPortalByName(List<String> modelNames) {
        String sql = "select * from t_md_portal where name in (%s)";
        return this.baseDao.sqlQueryForList(String.format(sql, String.join(",", modelNames)));
    }


    @Override
    public void saveScenarioType(List<Map<String, String>> stringStringMaps) {
        List<TScenarioCaseType> tScenarioCaseTypes = scenarioCaseTypeService.queryAllType();
        Map<String, Object> tScenarioCaseTypeMaps = new HashMap<>();
        tScenarioCaseTypes.stream().forEach(s -> tScenarioCaseTypeMaps.put(s.getCode(), s));

        for (Map<String, String> stringStringMap : stringStringMaps) {
            TScenarioCaseRel tr = new TScenarioCaseRel();
            tr.settScenarioCaseType((TScenarioCaseType) tScenarioCaseTypeMaps.get(stringStringMap.get("caseTypeCode")));
            TScenarioCase tScenarioCase = new TScenarioCase();
            tScenarioCase.setCaseId(stringStringMap.get("modelId"));
            tScenarioCase.setId(DigestUtils.md5DigestAsHex(stringStringMap.get("modelName").getBytes()));
            tScenarioCase.setCode(stringStringMap.get("modelName"));
            tScenarioCase.setName(stringStringMap.get("modelName"));
            tScenarioCase.setType("SCENARIO_CASE");
            tScenarioCase.setMemo(stringStringMap.get("modelName"));
            this.baseDao.merge(tScenarioCase);
            tr.setId(DigestUtils.md5DigestAsHex((stringStringMap.get("modelCode") + stringStringMap.get("modelName")).getBytes()));
            tr.settScenarioCase(tScenarioCase);
            tr.setCode(tScenarioCase.getCaseId());
            tr.setType("SCENARIO_CASE_REL");
            this.baseDao.merge(tr);
        }
    }
}
