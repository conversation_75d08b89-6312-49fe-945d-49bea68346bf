package com.dragonsoft.cicada.datacenter.modules.modeling.util;

import com.code.common.utils.StringUtils;

import java.util.regex.Pattern;

/**
 * 版权所有：厦门市巨龙信息科技有限公司
 * Copyright 2018 Xiamen Dragon Information Technology Co. Ltd.
 * All right reserved.
 * ====================================================
 * 修订记录：
 * No    日期				作者(操作:具体内容)
 * 1.    2021/11/11 6:01			baijx（<EMAIL>）(创建:创建文件)
 * ====================================================
 * 类描述：
 */
public class MlSqlSparkVersionFilterUtil {
    private static String spark_version = "2.3.2";

    public static String functionFilter(String mlSql,String use_spark_version) {
        if(StringUtils.isBlank(use_spark_version)) return mlSql;
        if (Integer.parseInt(spark_version.replace(".", "")) >= Integer.parseInt(use_spark_version.replace(".", ""))) {
            Pattern scriptPattern = Pattern.compile("!cache.*?;", Pattern.CASE_INSENSITIVE);
            mlSql = scriptPattern.matcher(mlSql).replaceAll("");
        }
        return mlSql;
    }
}
