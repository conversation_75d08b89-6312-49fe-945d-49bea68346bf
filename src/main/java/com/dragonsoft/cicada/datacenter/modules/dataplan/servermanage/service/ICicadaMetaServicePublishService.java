package com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.service;

import com.code.common.mist.service.structure.model.AnnotationMeta;
import com.code.common.mist.service.structure.model.ServiceClassMeta;
import com.dragoninfo.dfw.bean.Result;
import com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.vo.ModelServiceRequestVo;
import com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.vo.ParamConfigVo;
import com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.vo.SubscribeParamConfigVo;
import com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.vo.TransStepInputVo;

import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.util.List;

public interface ICicadaMetaServicePublishService {

    Result createService(SubscribeParamConfigVo paramConfigVo, String userId);

    Result updateService(SubscribeParamConfigVo paramConfigVo, String userId);

    Result updateQueryService(ParamConfigVo paramConfigVo, String userId) throws Exception;

    Result updateVerifactionService(ParamConfigVo paramConfigVo, String userId) throws Exception;

    Result updateNoPublishService(ParamConfigVo paramConfigVo, String userId) throws Exception;

    /**
     * 发布服务没有保存meta
     *
     * @param paramConfigVo
     * @return
     */
    Result publishModelServiceCreateJar(SubscribeParamConfigVo paramConfigVo, String userId);


    Result testService(ModelServiceRequestVo requestVo, HttpServletRequest servletRequest) throws UnsupportedEncodingException;

    Result testServiceGetResult(ModelServiceRequestVo requestVo, HttpServletRequest servletRequest) throws UnsupportedEncodingException;

    String createServiceTransTemp(String transName, HttpServletRequest request, String serviceType);

    List<TransStepInputVo> queryTransSteps(String transId);

    List<AnnotationMeta> buildSensitiveAnnotations(ParamConfigVo paramConfigVo);


    //模型服务构造sql
    ServiceClassMeta dynamicCreateClassMetaByVo(ParamConfigVo paramConfigVo, String userId);
}
