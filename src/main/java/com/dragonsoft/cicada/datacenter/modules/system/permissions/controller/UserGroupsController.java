package com.dragonsoft.cicada.datacenter.modules.system.permissions.controller;

import com.code.common.paging.PageInfo;
import com.dragoninfo.dfw.annotation.FuncScanAnnotation;
import com.dragoninfo.dfw.annotation.IngoreCustomResponse;
import com.dragoninfo.dfw.annotation.ValidateAndLogAnnotation;
import com.dragoninfo.dfw.bean.Result;
import com.dragonsoft.cicada.datacenter.modules.system.common.vo.TreeVo;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.service.IUserGroupsService;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.vo.PageVo;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.vo.UserGroupsVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/5/21
 */
@RestController
@CrossOrigin
@RequestMapping("/userGroups")
@Api(value = "UserGroupsController|用户组操作控制器，页面上的ID为数据库中的code")
@FuncScanAnnotation(code = "groupManagement", name = "用户组管理", parentCode = "systemManagement")
public class UserGroupsController {

    @Autowired
    private IUserGroupsService userGroupsService;


    @ResponseBody
    @RequestMapping(value ="/getGroupRandom",method = RequestMethod.POST)
    @ApiOperation(value = "获取用户组页面的随机ID")
    public Result getGroupRandom(){
        String random = userGroupsService.getGroupRandom();
        return Result.success(random);
    }

    /**
     * 通过ID获取组
     *
     * @param groupId
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/queryGroupById", method = RequestMethod.POST)
    @ApiOperation(value = "通过ID查询用户组，此ID为数据库实际ID")
    @FuncScanAnnotation(code = "groupManagementQueryGroup", name = "详情", parentCode = "groupManagement")
    @ValidateAndLogAnnotation
    public Result queryGroupById(@RequestBody String groupId) {
        UserGroupsVo group = userGroupsService.queryGroupById(groupId);
        return Result.success(group);
    }

    /**
     * 获取用户组列表 支持ID与name的模糊查询
     *
     * @param pageVo
     * @return
     */
    @ResponseBody
    @RequestMapping(value ="/queryUserGroupsList",method = RequestMethod.POST)
    @ApiOperation(value = "获取用户组列表 支持ID与name的模糊查询")
    public Result queryUserGroupsList(@RequestBody PageVo pageVo) {
        PageInfo pageInfo = userGroupsService.queryUserGroupsPage(pageVo);
        return Result.success(pageInfo);
    }

    /**
     * 查询所有分组
     *
     * @return
     */
    @ResponseBody
    @RequestMapping(value ="/queryAllUserGroups",method = RequestMethod.POST)
    @ApiOperation(value = "查询所有分组")
    public Result queryAllUserGroups(@RequestBody String editId) {
        List<TreeVo> treeVos = userGroupsService.queryAllUserGroups(editId);
        return Result.success(treeVos);
    }

    /**
     * 添加用户组
     *
     * @param userGroupsVo
     * @return
     */
    @IngoreCustomResponse
    @ResponseBody
    @RequestMapping(value ="/addUserGroup",method = RequestMethod.POST)
    @ApiOperation(value = "添加")
    @FuncScanAnnotation(code = "groupManagementAddUserGroup", name = "添加", parentCode = "groupManagement")
    @ValidateAndLogAnnotation
    public Result addUserGroup(@RequestBody UserGroupsVo userGroupsVo) {
        userGroupsService.addUserGroup(userGroupsVo);
        return Result.success();
    }

    /**
     * 编辑用户组
     *
     * @param userGroupsVo
     * @return
     */
    @ResponseBody
    @RequestMapping(value ="/editUserGroup",method = RequestMethod.POST)
    @ApiOperation(value = "编辑")
    @FuncScanAnnotation(code = "groupManagementEditUserGroup", name = "编辑", parentCode = "groupManagement")
    @ValidateAndLogAnnotation
    public Result editUserGroup(@RequestBody UserGroupsVo userGroupsVo) {
        userGroupsService.upDataUserGroup(userGroupsVo);
        return Result.success();
    }

    /**
     * 删除用户组
     *
     * @param id
     * @return
     */
    @ResponseBody
    @RequestMapping("/deleteUserGroup")
    @ApiOperation(value = "删除用户组")
    @FuncScanAnnotation(code = "groupManagementDeleteUserGroup", name = "删除", parentCode = "groupManagement")
    @ValidateAndLogAnnotation
    public Result deleteUserGroup(@RequestBody String id) {
        String msg = userGroupsService.deleteUserGroup(id);
        return Result.success(msg);
    }
}
