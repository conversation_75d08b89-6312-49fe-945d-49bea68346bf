package com.dragonsoft.cicada.datacenter.modules.modeling.util;

public enum TransClassifyEnum {
    COLLISION("collision", "数据碰撞"),
    SUBSCRIBE("subscribe", "数据订阅"),
    INFO_CHECK("informationVerification", "信息核查"),
    ANALYSIS("analysis", "数据分析");

    public String code;
    public String name;

    TransClassifyEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static boolean isFilter(String transClassify) {
        if (TransClassifyEnum.COLLISION.code.equals(transClassify) || TransClassifyEnum.SUBSCRIBE.code.equals(transClassify))
            return true;
        else return false;
    }
}
