package com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.adapter;

import java.util.Arrays;

public class IntegerAdapter extends AbstractTypeAdapter<String> {

    //存放属于数值型的类型
    {
        types.add("INTEGER");
        types.add("BigInteger BIGINTERGER");
        types.add("LONG");
        types.add("SHORT");
        String[] NUMBER_TYPE = {"INT2","INT4","INT8","LONG","BIGINT","BYTE","INTEGER","INT","SMALLINT","INT","TINYINT","INTEGER","LONG","SHORT","BYTE"};
        types.addAll(Arrays.asList(NUMBER_TYPE));
    }

    @Override
    public String handler() {
        return "Integer"; //返回转换处理过后的type
    }

    @Override
    public String getHanlderResult() {
        return "数值型";
    }
}
