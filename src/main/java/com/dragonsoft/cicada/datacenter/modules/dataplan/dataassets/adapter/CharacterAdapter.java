package com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.adapter;

public class CharacterAdapter extends AbstractTypeAdapter<String> {

    //存放属于字符型的类型
    {
        types.add("STRING");
        types.add("BYTE");
        types.add("BOOLEAN");
    }

    @Override
    public String handler() {
        return "String";
    }

    @Override
    public String getHanlderResult() {
        return "字符型";
    }

}
