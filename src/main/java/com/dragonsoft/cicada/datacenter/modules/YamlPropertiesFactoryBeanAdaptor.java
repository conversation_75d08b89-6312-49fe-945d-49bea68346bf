package com.dragonsoft.cicada.datacenter.modules;

import com.code.common.encrypt.KeyEncryptor;
import org.springframework.beans.factory.config.YamlPropertiesFactoryBean;
import org.springframework.core.CollectionFactory;

import java.util.Map;
import java.util.Properties;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2021-03-18 8:51
 */
public class YamlPropertiesFactoryBeanAdaptor extends YamlPropertiesFactoryBean {

    private Set<String> decrypts;

    @Override
    protected Properties createProperties() {
        final Properties result = CollectionFactory.createStringAdaptingProperties();
        process((properties, map) -> {
            for (Map.Entry<Object, Object> entry : properties.entrySet()) {
                if (decrypts.contains(entry.getKey().toString())) {
                    result.put(entry.getKey(), KeyEncryptor.decrypt(entry.getValue().toString()));
                } else {
                    result.put(entry.getKey(), entry.getValue());
                }
            }
        });
        return result;
    }

    public void setDecrypts(Set<String> decrypts) {
        this.decrypts = decrypts;
    }
}
