package com.dragonsoft.cicada.datacenter.modules.supermarkmodel.service.impl;

import com.code.common.paging.PageInfo;
import com.code.common.utils.assertion.Assert;
import com.code.metadata.modelsupermark.*;
import com.code.metadata.modelsupermark.enumtype.ModelFocusTypeEnum;
import com.code.metadata.modelsupermark.enumtype.ModelOperateEnum;
import com.code.metadata.modelsupermark.enumtype.ModelTypeEnum;
import com.code.metaservice.modelsupermark.*;
import com.dragoninfo.dfw.bean.Result;
import com.dragoninfo.dfw.entity.TSysAuthUser;
import com.dragoninfo.dfw.service.SysAuthUserService;
import com.dragonsoft.cicada.datacenter.modules.supermarkmodel.service.IModelMarkService;
import com.dragonsoft.cicada.datacenter.modules.supermarkmodel.service.IMyModelService;
import com.dragonsoft.cicada.datacenter.modules.supermarkmodel.vo.*;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.service.IUserService;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.vo.UserVo;
import com.fw.service.BaseService;
import com.fw.service.annotation.Service;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.code.common.utils.StringUtils.uuid;

@Service
public class ModelMarkServiceImpl extends BaseService implements IModelMarkService {

    @Autowired
    private IUserService userService;

    @Autowired
    private IMyModelService myModelServiceImpl;

    @Autowired
    private IModelSearchService modelSearchServiceImpl;

    @Autowired
    private IModelBrowseService modelBrowseServiceImpl;

    @Autowired
    private IModelEvaluationService modelEvaluationServiceImpl;

    @Autowired
    private IModelFocusService modelFocusServiceImpl;

    @Autowired
    private ISuperMarkModelingService superMarkModelingServiceImpl;

    @Autowired
    private SysAuthUserService sysAuthUserService;

    /**
     * 模型市场模型明细
     */
    @Override
    public Result initViewMarkModelDetail(SupermarkModelVo modelVo) {
        Assert.notNull(modelVo, "模型数据不能为空");
        Assert.notNull(modelVo.getId(), "模型id不能为空");

        SupermarkModel markModel = (SupermarkModel) baseDao.get(SupermarkModel.class, modelVo.getId());
        Assert.notNull(markModel, "模型不存在");

        SupermarkModelDetailVo modelDetailVo = new SupermarkModelDetailVo();
        //模型数据
        setMarkModelToModelDetailVo(markModel, modelDetailVo);
        //模型名称、创建人id
        setModelNameToModelDetailVo(markModel.getTransId(), markModel.getType(), modelDetailVo);
        //模型 关注状态
        setModelFocusTypeToModelDetailVo(markModel.getId(), modelVo.getUserId(), modelDetailVo);
        //模型标签
        setModelLabelToModelDetailVo(markModel.getId(), modelDetailVo);
        //模型平均评分、关注数、浏览数
        setCountDetailToModelDetailVo(markModel.getId(), modelDetailVo);
        //模型开发者
        setPublishUserToModelDetailVo(modelDetailVo.getTransUserId(), modelDetailVo);
        //模型数据集
        setModelDataSetToModelDetailVo(markModel.getTransId(), markModel.getType(), modelDetailVo);


        String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        //记录浏览行为
        if (modelVo.isAddView()){
            UserOperateBehavior operateBehavior = new UserOperateBehavior();
            operateBehavior.setId(uuid());
            operateBehavior.setModelId(markModel.getId());
            operateBehavior.setUserId(modelVo.getUserId());
            operateBehavior.setOperateType(ModelOperateEnum.BROWSE.getValue());
            operateBehavior.setOperateTime(dateStr);
            baseDao.save(operateBehavior);
            ModelBrowse modelBrowse = new ModelBrowse();
            modelBrowse.setId(uuid());
            modelBrowse.setUserOperateBehavior(operateBehavior);
            modelBrowse.setModelId(markModel.getId());
            modelBrowse.setUserId(modelVo.getUserId());
            modelBrowse.setOperateType(ModelOperateEnum.BROWSE.getValue());
            modelBrowse.setOperateContent("用户浏览模型");
            modelBrowse.setOperateTime(dateStr);
            baseDao.save(modelBrowse);
        }

        //判断当前用户是否已经申请过该模型并且申请处于正在审批中
        Map<String,String> param = new HashMap<>();
        param.put("applyUserId",modelVo.getUserId());
        param.put("auditState",ModelResourcesEnum.RESOURCE_AUDIT_UNAUDIT.getCode());
        param.put("resourceId",markModel.getTransId());
        List applyList = baseDao.queryForList("from ResourcesApply where applyUserId=:applyUserId and auditState=:auditState and resourceId=:resourceId",param);
        if(applyList.size() >0 ){
            modelDetailVo.setCanApply(false);
        }else{
            modelDetailVo.setCanApply(true);
        }

        //下载次数
        Map<String,String> operateParam = new HashMap<>();
        operateParam.put("funCode","modelSpaceImportAndExport");
        operateParam.put("operateType","3");
        operateParam.put("operateCondition","'%"+markModel.getTransId()+"%'");
        String count = baseDao.sqlQueryForValue("select count(1) from t_sys_operate_log   where func_code=:funCode and operate_type=:operateType and operate_condition like :operateCondition",operateParam);
        modelDetailVo.setDownloadCount(Integer.parseInt(count));

        return Result.success(modelDetailVo);
    }

    /**
     * 模型市场模型明细 设置模型数据
     */
    public void setMarkModelToModelDetailVo(SupermarkModel markModel, SupermarkModelDetailVo modelDetailVo) {
        modelDetailVo.setId(markModel.getId());
        modelDetailVo.setTransId(markModel.getTransId());
        modelDetailVo.setType(markModel.getType());
        modelDetailVo.setLogo(markModel.getLogo());
        modelDetailVo.setVersion(markModel.getVersion());
        modelDetailVo.setIntroduction(markModel.getIntroduction());
        modelDetailVo.setPutTime(markModel.getPutTime());

        //模型配图
        Set<ModelPicture> modelPictures = markModel.getModelPictures();
        if (CollectionUtils.isNotEmpty(modelPictures)) {
            List<ModelPictureVo> pictureVos = modelPictures.stream().map(modelPicture -> {
                ModelPictureVo pictureVo = new ModelPictureVo();
                pictureVo.setPictureName(modelPicture.getPictureName());
                pictureVo.setPicture(modelPicture.getPicture());
                return pictureVo;
            }).collect(Collectors.toList());
            modelDetailVo.setPics(pictureVos);
        }
        //模型分类
        ModelAttachedInformation attInfo = markModel.getModelAttachedInformation();
        if (attInfo != null) {
            modelDetailVo.setObjectTypeCode(attInfo.getObjectTypeCode());
            modelDetailVo.setObjectTypeName(attInfo.getObjectTypeName());
            modelDetailVo.setApplicationTypeCode(attInfo.getApplicationTypeCode());
            modelDetailVo.setApplicationTypeName(attInfo.getApplicationTypeName());
            modelDetailVo.setPoliceTypeCode(attInfo.getPoliceTypeCode());
            modelDetailVo.setPoliceTypeName(attInfo.getPoliceTypeName());
            modelDetailVo.setCaseTypeCode(attInfo.getCaseTypeCode());
            modelDetailVo.setCaseTypeName(attInfo.getCaseTypeName());
            modelDetailVo.setAreaTypeCode(attInfo.getAreaTypeCode());
            modelDetailVo.setAreaTypeName(attInfo.getAreaTypeName());
            modelDetailVo.setControlTypeCode(attInfo.getControlTypeCode());
            modelDetailVo.setControlTypeName(attInfo.getControlTypeName());
            modelDetailVo.setYylxSjTypeCode(attInfo.getYylxSjTypeCode());
            modelDetailVo.setYylxSjTypeName(attInfo.getYylxSjTypeName());
            modelDetailVo.setHyflSjTypeCode(attInfo.getMxlxSjTypeCode());
            modelDetailVo.setHyflSjTypeName(attInfo.getMxlxSjTypeName());
            StringBuffer typeCodeName = new StringBuffer();

            if(StringUtils.isNotBlank(attInfo.getObjectTypeName())){
                typeCodeName.append("、").append(attInfo.getObjectTypeName());
            }
            if(StringUtils.isNotBlank(attInfo.getApplicationTypeName())){
                typeCodeName.append("、").append(attInfo.getApplicationTypeName());
            }
            if(StringUtils.isNotBlank(attInfo.getPoliceTypeName())){
                typeCodeName.append("、").append(attInfo.getPoliceTypeName());
            }
            if(StringUtils.isNotBlank(attInfo.getCaseTypeName())){
                typeCodeName.append("、").append(attInfo.getCaseTypeName());
            }
            if(StringUtils.isNotBlank(attInfo.getAreaTypeName())){
                typeCodeName.append("、").append(attInfo.getAreaTypeName());
            }
            if(StringUtils.isNotBlank(attInfo.getControlTypeName())){
                typeCodeName.append("、").append(attInfo.getControlTypeName());
            }
            if(StringUtils.isNotBlank(attInfo.getYylxSjTypeName())){
                typeCodeName.append("、").append(attInfo.getYylxSjTypeName());
            }
            if(StringUtils.isNotBlank(attInfo.getMxlxSjTypeName())){
                typeCodeName.append("、").append(attInfo.getMxlxSjTypeName());
            }


            modelDetailVo.setTypeCodeName(typeCodeName.toString().replaceFirst("、",""));
        }
    }

    /**
     * 模型市场模型明细 设置模型名称、创建人
     */
    public void setModelNameToModelDetailVo(String transId, String type, SupermarkModelDetailVo modelDetailVo) {
        Map<String, Object> transInfoMap = myModelServiceImpl.getModelTransInfo(transId, type);
        modelDetailVo.setTransName((String) transInfoMap.get("name"));
        modelDetailVo.setTransUserId((String) transInfoMap.get("operateUserId"));
    }

    /**
     * 模型市场模型明细 设置模型关注状态
     */
    public void setModelFocusTypeToModelDetailVo(String modelId, String userId, SupermarkModelDetailVo modelDetailVo) {
        UserOperateBehavior oldUserOperBeh = superMarkModelingServiceImpl.queryUserOperateBehaviorObject(modelId, userId, ModelOperateEnum.FOCUS.getValue());
        if (oldUserOperBeh != null) {
            modelDetailVo.setFocusType(ModelFocusTypeEnum.FOCUS.getValue());
        } else {
            modelDetailVo.setFocusType(ModelFocusTypeEnum.UNFOCUS.getValue());
        }
    }

    /**
     * 模型市场模型明细 设置模型标签
     */
    public void setModelLabelToModelDetailVo(String modelId, SupermarkModelDetailVo modelDetailVo) {
        //模型标签
        List<ModelLabelRelation> labelList = modelSearchServiceImpl.getModelLabel(modelId);
        if (CollectionUtils.isNotEmpty(labelList)) {
            List<ModelLabelVo> labelVos = labelList.stream().filter(labelRel -> labelRel.getModelLabel() != null).map(labelRel -> {
                ModelLabelVo modelLabelVo = new ModelLabelVo();
                modelLabelVo.setLabelName(labelRel.getModelLabel().getLabelName());
                return  modelLabelVo;
            }).collect(Collectors.toList());
            modelDetailVo.setLabels(labelVos);
        }
    }

    /**
     * 模型市场模型明细 设置平均评分、关注数、浏览数
     */
    public void setCountDetailToModelDetailVo(String modelId, SupermarkModelDetailVo modelDetailVo) {
        //平均评分
        String avgScoreStr = modelEvaluationServiceImpl.queryModelAvgScore(modelId);
        if (avgScoreStr != null) {
            BigDecimal avgScore = new BigDecimal(avgScoreStr);
            avgScore.setScale(2, RoundingMode.HALF_UP);
            modelDetailVo.setAvgScore(avgScore);
            double score = Math.ceil(avgScore.floatValue());
            if(score <=2 ){
                modelDetailVo.setScoreLevel("低");
            }else if(score>2 && score<=4){
                modelDetailVo.setScoreLevel("中");
            }else {
                modelDetailVo.setScoreLevel("高");
            }
        }else{
            modelDetailVo.setScoreLevel("低");
        }
        //关注数
        String focusCount = modelFocusServiceImpl.queryModelFocusCount(modelId);
        modelDetailVo.setFocusCount(Integer.valueOf(focusCount));
        //浏览数
        String browseCount = modelBrowseServiceImpl.queryModelBrowseCount(modelId);
        modelDetailVo.setBrowseCount(Integer.valueOf(browseCount));
        modelDetailVo.setBrowseLevel(calculateBrowseLevel(Integer.valueOf(browseCount)));
    }

    private String calculateBrowseLevel(Integer browseCount){
        String sql = " select COALESCE(max(browse_count),0) as max_browse,COALESCE(min(browse_count),0) as min_browse from " +
                "(SELECT  COUNT ( * ) AS browse_count FROM t_user_operate_behavior_result " +
                " WHERE operate_type = 'Browse' GROUP BY model_id ) a";
        Map<String,Object> map = baseDao.sqlQueryForMap(sql);
        BigInteger max = (BigInteger) map.get("max_browse");
        BigInteger min = (BigInteger) map.get("min_browse");

        Integer minLevel = (max.intValue()+min.intValue())/3;
        Integer middleLevel = (max.intValue()+min.intValue())/3+(max.intValue()-min.intValue())/3;
        if(browseCount < minLevel){
            return "低";
        }else if(browseCount >= minLevel && browseCount <=middleLevel){
            return "中";
        }else{
            return "高";
        }
    }

    /**
     * 模型市场模型明细 设置创建人明细
     */
    public void setPublishUserToModelDetailVo(String transUserId, SupermarkModelDetailVo modelDetailVo) {
        if (StringUtils.isEmpty(transUserId)) {
            return;
        }
        UserVo userVo = userService.queryUserById(transUserId);
        ModelPublishUserVo publishUser = new ModelPublishUserVo();
        publishUser.setUserId(userVo.getId());
        publishUser.setUserName(userVo.getObjName());
        publishUser.setPhone(userVo.getPhone());
        publishUser.setEmail(userVo.getEmail());
        modelDetailVo.setPublishUser(publishUser);
    }

    /**
     * 模型市场模型明细 设置数据集
     */
    public void setModelDataSetToModelDetailVo(String transId, String type, SupermarkModelDetailVo modelDetailVo) {
        List<Map> dataSetList = null;
        if (StringUtils.equals(type, ModelTypeEnum.TRANS.getValue())) {
            dataSetList = modelSearchServiceImpl.queryModelTransDataSet(transId);
        } else if (StringUtils.equals(type, ModelTypeEnum.DASHBOARDS.getValue())) {
            dataSetList = modelSearchServiceImpl.queryModelDashboardsDataSet(transId);
        } else if (StringUtils.equals(type, ModelTypeEnum.SCRIPT.getValue())) {

        } else if (StringUtils.equals(type, ModelTypeEnum.PORTAL.getValue())) {
            dataSetList = modelSearchServiceImpl.queryModelPortalDataSet(transId);
        }
        if (CollectionUtils.isNotEmpty(dataSetList)) {
            List<ModelDataSetVo> dataSetVos = dataSetList.stream().map(dataSetMap -> {
                ModelDataSetVo dataSetVo = new ModelDataSetVo();
                dataSetVo.setId((String) dataSetMap.get("id"));
                dataSetVo.setName((String) dataSetMap.get("name"));
                return dataSetVo;
            }).collect(Collectors.toList());
            modelDetailVo.setDataSets(dataSetVos);
        }
    }

    /**
     * 推荐同类型其它模型查询
     */
    @Override
    public Result querySameTypeMarkModelPage(Map<String, Object> queryModelMap) {
        Assert.notNull(queryModelMap, "查询参数不能为空");
        Assert.notNull(queryModelMap.get("type"), "类型不能为空");
        Assert.notNull(queryModelMap.get("objTypeCode"), "对象类型不能为空");
        Assert.notNull(queryModelMap.get("appTypeCode"), "应用类型不能为空");
        Assert.notNull(queryModelMap.get("polTypeCode"), "警种分类不能为空");
        Assert.notNull(queryModelMap.get("id"), "模型id不能为空");

        Assert.notNull(queryModelMap.get("pageNum"), "分页不能为空");
        Assert.notNull(queryModelMap.get("pageSize"), "分页不能为空");

        PageInfo pageResult = modelSearchServiceImpl.querySameTypeMarkModelPage(queryModelMap);

        List<Map> dataList = pageResult.getDataList();
        if (CollectionUtils.isNotEmpty(dataList)) {
            for (Map modelVo : dataList) {
                String transId = (String) modelVo.get("transid");
                String type = (String) modelVo.get("type");
                modelVo.put("name", myModelServiceImpl.getModelTransInfo(transId, type).get("name"));
            }
        }

        return Result.success(pageResult);
    }

    /**
     * 已关注用户列表查询
     */
    @Override
    public Result queryFocusedUserList(Map<String, Object> queryModelMap) {
        Assert.notNull(queryModelMap, "查询参数不能为空");
        Assert.notNull(queryModelMap.get("id"), "模型id不能为空");
        Assert.notNull(queryModelMap.get("pageNum"), "分页不能为空");
        Assert.notNull(queryModelMap.get("pageSize"), "分页不能为空");

        PageInfo pageResult = modelFocusServiceImpl.queryFocusedUserList(queryModelMap);

        return Result.success(pageResult);
    }

    /**
     * 关注或取消关注模型
     */
    @Override
    public Result updateFocusOrUnFocusModel(SupermarkModelVo modelVo) {
        Assert.notNull(modelVo.getUserId(), "用户未登录");
        Assert.notNull(modelVo, "模型数据不能为空");
        Assert.notNull(modelVo.getId(), "模型id不能为空");
        Assert.notNull(modelVo.getFocusType(), "关注类型不能为空");

        SupermarkModel markModel = (SupermarkModel) baseDao.get(SupermarkModel.class, modelVo.getId());
        Assert.notNull(markModel, "模型不存在");

        UserOperateBehavior oldUserOperBeh = superMarkModelingServiceImpl.queryUserOperateBehaviorObject(markModel.getId(), modelVo.getUserId(), ModelOperateEnum.FOCUS.getValue());

        if (StringUtils.equals(modelVo.getFocusType(), ModelFocusTypeEnum.FOCUS.getValue())) {

            Assert.isNull(oldUserOperBeh, "已关注该模型，不能重复关注");

            String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            //记录关注行为
            UserOperateBehavior operateBehavior = new UserOperateBehavior();
            operateBehavior.setId(uuid());
            operateBehavior.setModelId(markModel.getId());
            operateBehavior.setUserId(modelVo.getUserId());
            operateBehavior.setOperateType(ModelOperateEnum.FOCUS.getValue());
            operateBehavior.setOperateTime(dateStr);
            baseDao.save(operateBehavior);

            ModelFocus modelFocus = new ModelFocus();
            modelFocus.setId(uuid());
            modelFocus.setUserOperateBehavior(operateBehavior);
            modelFocus.setModelId(markModel.getId());
            modelFocus.setUserId(modelVo.getUserId());
            modelFocus.setOperateType(ModelOperateEnum.FOCUS.getValue());
            modelFocus.setOperateContent("用户关注模型");
            modelFocus.setOperateTime(dateStr);
            baseDao.save(modelFocus);

        } else if (StringUtils.equals(modelVo.getFocusType(), ModelFocusTypeEnum.UNFOCUS.getValue())) {
            Assert.notNull(oldUserOperBeh, "未关注该模型");

            Set<UserOperateBehaviorResult> userOperBehResSet = oldUserOperBeh.getUserOperateBehaviorResultSets();
            if (CollectionUtils.isNotEmpty(userOperBehResSet)) {
                baseDao.delete(userOperBehResSet);
            }
            baseDao.delete(oldUserOperBeh);
            //取消关注删除对应评价和评分
            UserOperateBehavior oldUserOperBehEva = superMarkModelingServiceImpl.queryUserOperateBehaviorObject(markModel.getId(), modelVo.getUserId(),
                    ModelOperateEnum.EVALUATION.getValue());
            if (oldUserOperBehEva != null){
                Set<UserOperateBehaviorResult> evaResultSets = oldUserOperBehEva.getUserOperateBehaviorResultSets();
                if (CollectionUtils.isNotEmpty(evaResultSets)) {
                    baseDao.delete(evaResultSets);
                }
                baseDao.delete(oldUserOperBehEva);

            }

            //modelEvaluationServiceImpl.deleteEvaluationByModelIdAndUserId(markModel.getId(),modelVo.getUserId());
        }

        return Result.success();
    }

    /**
     * 查询累计评价列表
     */
    @Override
    public Result queryModelEvaluationList(Map<String, Object> queryModelMap) {
        Assert.notNull(queryModelMap, "查询参数不能为空");
        Assert.notNull(queryModelMap.get("id"), "模型id不能为空");

        Assert.notNull(queryModelMap.get("pageNum"), "分页不能为空");
        Assert.notNull(queryModelMap.get("pageSize"), "分页不能为空");
        Assert.notNull(queryModelMap.get("operateType"), "评价类型不能为空");
        PageInfo pageResult = null;
        if(ModelOperateEnum.EVALUATION.getValue().equalsIgnoreCase(String.valueOf(queryModelMap.get("operateType")))){
            pageResult = modelEvaluationServiceImpl.queryModelEvaluationList(queryModelMap);
        }else {
            pageResult = modelEvaluationServiceImpl.queryUserEvaluationList(queryModelMap);
        }


        return Result.success(pageResult);
    }

    @Override
    public PageInfo queryMarketPage(ModelMarketQueryVo queryVo) {
        PageInfo pageInfo = superMarkModelingServiceImpl.queryModelMarketByCondition(
                queryVo.getObjType(),
                queryVo.getAppType(),
                queryVo.getPoliceType(),
                queryVo.getCaseType(),
                queryVo.getAreaType(),
                queryVo.getControlType(),
                queryVo.getYylxSjType(),
                queryVo.getMxflSjType(),
                queryVo.getScore(),
                queryVo.getBrowseCount(),
                queryVo.getPublishTimeStart(),
                queryVo.getPublishTimeEnd(),
                queryVo.getSearchType(),
                queryVo.getSearchCond(),
                queryVo.getSortType(),
                queryVo.getSort(),
                queryVo.getPageNum(),
                queryVo.getPageSize()
        );
        List<Map<String,Object>> dataList = pageInfo.getDataList();
        if (dataList == null || dataList.size() <= 0){
            return pageInfo;
        }
        List<ModelMarketResult> results = new ArrayList<>();
        for (Map<String, Object> map : dataList) {
            ModelMarketResult result = new ModelMarketResult();
            String modelId = (String) map.get("id");
            result.setId(modelId);
            result.setType((String) map.get("type"));
            result.setResourceName((String) map.get("name"));
            result.setLogo((String) map.get("logo"));
            result.setReleaseTime((String) map.get("publish_time"));
            result.setModelDesc((String) map.get("introduction"));
            String operateUserId = (String) map.get("operate_user_id");
            Map<String,String> map1 = new HashMap<>();
            map1.put("id", operateUserId);
            TSysAuthUser tSysAuthUser = (TSysAuthUser) sysAuthUserService.query(map1);
            result.setReleaseUser(tSysAuthUser == null ? null : tSysAuthUser.getObjName());

            result.setBrowserCount((BigInteger) map.get("count_browse"));
            result.setFocusCount((BigInteger) map.get("count_focus"));
            result.setScore((Double) map.get("avg_sc"));
            List<ModelLabel> modelLabels = modelSearchServiceImpl.queryModelLabelListByModelId(modelId);
            result.setLabels(modelLabels);
            results.add(result);
        }
        //List<ModelMarketResult> sortResults = new ArrayList<>();
        /*if ("focus".equalsIgnoreCase(queryVo.getSortType())){
            if ("asc".equalsIgnoreCase(queryVo.getSort())){
                sortResults = results.stream().sorted(Comparator.comparing(ModelMarketResult::getFocusCount)).collect(Collectors.toList());
            }else {
                sortResults = results.stream().sorted(Comparator.comparing(ModelMarketResult::getFocusCount).reversed()).collect(Collectors.toList());
            }
        }else if ("browse".equalsIgnoreCase(queryVo.getSortType())){
            if ("asc".equalsIgnoreCase(queryVo.getSort())){
                sortResults = results.stream().sorted(Comparator.comparing(ModelMarketResult::getBrowserCount)).collect(Collectors.toList());
            }else {
                sortResults = results.stream().sorted(Comparator.comparing(ModelMarketResult::getBrowserCount).reversed()).collect(Collectors.toList());
            }
        }else if ("score".equalsIgnoreCase(queryVo.getSortType())){
            if ("asc".equalsIgnoreCase(queryVo.getSort())){
                sortResults = results.stream().sorted(Comparator.comparing(ModelMarketResult::getBrowserCount)).collect(Collectors.toList());
            }else {
                sortResults = results.stream().sorted(Comparator.comparing(ModelMarketResult::getBrowserCount).reversed()).collect(Collectors.toList());
            }
        }else {
            sortResults.addAll(results);
        }*/
        pageInfo.setDataList(results);
        return pageInfo;
    }

    /**
     *评价模型
     */
    @Override
    public Result updateEvaluationModel(SupermarkModelVo modelVo) {
        Assert.notNull(modelVo, "模型数据不能为空");
        Assert.notNull(modelVo.getUserId(), "用户未登录");
        Assert.notNull(modelVo.getId(), "模型id不能为空");
        Assert.notNull(modelVo.getScore(), "评分不能为空");
        Assert.notNull(modelVo.getOperateContent(), "评价内容不能为空");
        Assert.isTrue(modelVo.getOperateContent().length() <= 500, "评价内容字数过长");

        SupermarkModel markModel = (SupermarkModel) baseDao.get(SupermarkModel.class, modelVo.getId());
        Assert.notNull(markModel, "模型不存在");

        String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        UserOperateBehavior oldUserOperBeh = superMarkModelingServiceImpl.queryUserOperateBehaviorObject(markModel.getId(), modelVo.getUserId(),
                ModelOperateEnum.EVALUATION.getValue());

        if (oldUserOperBeh == null) {
            //记录评价行为
            UserOperateBehavior operateBehavior = new UserOperateBehavior();
            operateBehavior.setId(uuid());
            operateBehavior.setModelId(markModel.getId());
            operateBehavior.setUserId(modelVo.getUserId());
            operateBehavior.setOperateType(ModelOperateEnum.EVALUATION.getValue());
            operateBehavior.setOperateTime(dateStr);
            baseDao.save(operateBehavior);

            ModelEvaluation modelEvaluation = new ModelEvaluation();
            modelEvaluation.setId(uuid());
            modelEvaluation.setUserOperateBehavior(operateBehavior);
            modelEvaluation.setModelId(markModel.getId());
            modelEvaluation.setUserId(modelVo.getUserId());
            modelEvaluation.setOperateType(ModelOperateEnum.EVALUATION.getValue());
            modelEvaluation.setScore(modelVo.getScore()); //评分
            modelEvaluation.setOperateContent(modelVo.getOperateContent()); //评价内容
            modelEvaluation.setOperateTime(dateStr);
            baseDao.save(modelEvaluation);
        } else {
            //修改评价
            Set<UserOperateBehaviorResult> userOperBehResSet = oldUserOperBeh.getUserOperateBehaviorResultSets();
            if (CollectionUtils.isNotEmpty(userOperBehResSet)) {
                for (UserOperateBehaviorResult userOperBehRes : userOperBehResSet) {
                    ModelEvaluation modelEvaluation = (ModelEvaluation) baseDao.get(ModelEvaluation.class, userOperBehRes.getId());
                    modelEvaluation.setScore(modelVo.getScore());
                    modelEvaluation.setOperateContent(modelVo.getOperateContent());
                    modelEvaluation.setOperateTime(dateStr);
                    baseDao.update(modelEvaluation);
                }
            }

            oldUserOperBeh.setOperateTime(dateStr);
            baseDao.update(oldUserOperBeh);
        }

        return Result.success();
    }

    /**
     *评价模型
     */
    @Override
    public Result updateEvaluationUser(SupermarkModelVo modelVo) {
        Assert.notNull(modelVo.getUserId(), "用户未登录");
        Assert.notNull(modelVo.getOperateContent(), "评价内容不能为空");
        Assert.notNull(modelVo.getAppraisedUserId(), "被评价者不能为空");
        Assert.isTrue(modelVo.getOperateContent().length() <= 500, "评价内容字数过长");


        String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        UserOperateBehavior oldUserOperBeh = superMarkModelingServiceImpl.queryUserOperateBehaviorObject(modelVo.getAppraisedUserId(), modelVo.getUserId(),
                modelVo.getEvaluationType());

        if (oldUserOperBeh == null) {
            //记录评价行为
            UserOperateBehavior operateBehavior = new UserOperateBehavior();
            operateBehavior.setId(uuid());
            operateBehavior.setAppraisedUserId(modelVo.getAppraisedUserId());
            operateBehavior.setUserId(modelVo.getUserId());
            operateBehavior.setOperateType(modelVo.getEvaluationType());
            operateBehavior.setOperateTime(dateStr);
            baseDao.save(operateBehavior);

            UserEvaluation modelEvaluation = new UserEvaluation();
            modelEvaluation.setId(uuid());
            modelEvaluation.setUserOperateBehavior(operateBehavior);
            modelEvaluation.setAppraisedUserId(modelVo.getAppraisedUserId());
            modelEvaluation.setUserId(modelVo.getUserId());
            modelEvaluation.setOperateType(modelVo.getEvaluationType());
            modelEvaluation.setOperateContent(modelVo.getOperateContent()); //评价内容
            modelEvaluation.setOperateTime(dateStr);
            baseDao.save(modelEvaluation);
        } else {
            //修改评价
            Set<UserOperateBehaviorResult> userOperBehResSet = oldUserOperBeh.getUserOperateBehaviorResultSets();
            if (CollectionUtils.isNotEmpty(userOperBehResSet)) {
                for (UserOperateBehaviorResult userOperBehRes : userOperBehResSet) {
                    UserEvaluation modelEvaluation = (UserEvaluation) baseDao.get(UserEvaluation.class, userOperBehRes.getId());
                    modelEvaluation.setOperateContent(modelVo.getOperateContent());
                    modelEvaluation.setOperateTime(dateStr);
                    baseDao.update(modelEvaluation);
                }
            }

            oldUserOperBeh.setOperateTime(dateStr);
            baseDao.update(oldUserOperBeh);
        }

        return Result.success();
    }


    /**
     * 查看模型评价详情
     */
    @Override
    public Result viewModelEvaluationDetail(SupermarkModelVo modelVo) {
        Assert.notNull(modelVo, "模型数据不能为空");
        Assert.notNull(modelVo.getUserId(), "用户未登录");
        Assert.notNull(modelVo.getId(), "模型id不能为空");

        SupermarkModel markModel = (SupermarkModel) baseDao.get(SupermarkModel.class, modelVo.getId());
        Assert.notNull(markModel, "模型不存在");

        //查询评价记录
        UserOperateBehavior userOperBeh = superMarkModelingServiceImpl.queryUserOperateBehaviorObject(markModel.getId(), modelVo.getUserId(),
                ModelOperateEnum.EVALUATION.getValue());
        if (userOperBeh != null) {
            Set<UserOperateBehaviorResult> userOperBehResSet = userOperBeh.getUserOperateBehaviorResultSets();
            if (CollectionUtils.isNotEmpty(userOperBehResSet)) {
                for (UserOperateBehaviorResult userOperBehRes : userOperBehResSet) {
                    ModelEvaluation modelEvaluation = (ModelEvaluation) baseDao.get(ModelEvaluation.class, userOperBehRes.getId());
                    //评分和评价内空
                    modelVo.setScore(modelEvaluation.getScore());
                    modelVo.setOperateContent(modelEvaluation.getOperateContent());
                }
            }
        }

        return Result.success(modelVo);
    }


    /**
     * 查看人员评价详情
     */
    @Override
    public Result viewUserEvaluationBasicDetail(SupermarkModelVo modelVo) {
        Assert.notNull(modelVo, "模型数据不能为空");
        Assert.notNull(modelVo.getUserId(), "用户未登录");
        Assert.notNull(modelVo.getAppraisedUserId(), "被评价者id不能为空");


       //建模分析
        UserVo userVo = userService.queryUserById(modelVo.getAppraisedUserId());
        UserEvaluationVo userEvaluationVo = new UserEvaluationVo();
        userEvaluationVo.setAppraisedUserName(userVo.getObjName());
        userEvaluationVo.setAppraisedUserTelphone(userVo.getPhone());
        userEvaluationVo.setAppraisedUserEmail(userVo.getEmail());
        String searchSql =" \n" +
                "     SELECT count(distinct case when e.operate_type ='Publish' then  d.id end) modelCount,    \n" +
                "    sum(case when g.id is not null  then 1 else 0 end ) modelDowloadCount,\n" +
                "    sum(case when e.operate_type = 'Browse' then 1 else 0 end) modelUseCount,\n" +
                "    sum(case when e.operate_type = 'Focus' then 1 else 0 end) modelUpvoteCount,\n" +
                "    avg(f.score) modelEvaluationScore ,\n" +
                "    sum(case when TO_TIMESTAMP(d.publish_time, 'YYYY-MM-DD HH24:MI:SS') >= CURRENT_DATE - INTERVAL '7 days'  then 1 else 0 end) modelCreateSpeed\n" +
                "      FROM  public.t_supermark_model d \n" +
                "    left join public.t_user_operate_behavior  e on e.model_id = d.id\n" +
                "    left join public.t_user_model_evaluation f on d.id = f.model_id\n" +
                "    left join public.t_resources_apply g on g.resource_id = d.id and g.audit_result_state ='1'\n" +
                "    where d.type='TRANS' and  e.user_id  = :user_id" +
                "    ";
        Map<String,Object> paramM = new HashMap<>();
        paramM.put("user_id",modelVo.getAppraisedUserId());
        Map<String ,Object> returnMap = this.baseDao.sqlQueryForMap(searchSql,paramM);
        userEvaluationVo.setModelCount(Integer.parseInt(transNullValue(returnMap.get("modelcount"))));
        userEvaluationVo.setModelDowloadCount(Integer.parseInt(transNullValue(returnMap.get("modeldowloadcount"))));
        userEvaluationVo.setModelUseCount(Integer.parseInt(transNullValue(returnMap.get("modelusecount"))));
        userEvaluationVo.setModelUpvoteCount(Integer.parseInt(transNullValue(returnMap.get("modelupvotecount"))));
        userEvaluationVo.setModelEvaluationScore(Float.parseFloat(transNullValue(returnMap.get("modelevaluationscore"))));
        userEvaluationVo.setModelCreateSpeed(Integer.parseInt(transNullValue(returnMap.get("modelcreatespeed"))));



        //使用分析
        String searchSqlUse =" SELECT (select count(1)  from public.t_sys_operate_log a where  a.visit_id =:user_id GROUP by a.visit_id ) loginCount,   \n" +
                "     (select count(distinct model_id ) from public.t_user_operate_behavior  b where      b.user_id =:user_id ) userModelSpeed,\n" +
                "    (select count(id)  from public.t_user_operate_behavior  b where      b.user_id =:user_id \n" +
                "     and TO_TIMESTAMP(b.operate_time, 'YYYY-MM-DD HH24:MI:SS') >= CURRENT_DATE - INTERVAL '7 days'  ) modelCreateFrequency,\n" +
                "    COUNT(distinct e.id) needSubmitCount,   sum(case when e.audit_state = '1' then 1 else 0 end) needRejectCount,\n" +
                "     sum(case when e.audit_state = '2' then 1 else 0 end) needPassCount\n" +
                "    , sum( h.pll ) commentCount,      sum( f.pll )  declaredBattleCount\n" +
                "      FROM  public.t_supermark_model d \n" +
                "    INNER JOIN public.t_resources_apply e on e.resource_id = d.trans_id \n" +
                "    left  JOIN (select count(1) pll, f.model_id from t_user_operate_behavior F WHERE f.operate_type='Evaluation' GROUP BY f.model_id )F ON f.model_id = d.id \n" +
                "     left JOIN (select count(1) pll, f.user_id from t_user_operate_behavior F WHERE f.operate_type='Evaluation'GROUP BY F.user_id ) h ON h.user_id = e.apply_user_id \n" +
                "      where e.apply_user_id =:user_id";

        Map<String ,Object> returnMapUse = this.baseDao.sqlQueryForMap(searchSqlUse,paramM);
        userEvaluationVo.setLoginCount(Integer.parseInt(transNullValue(returnMapUse.get("logincount"))));
        userEvaluationVo.setUserModelSpeed(Integer.parseInt(transNullValue(returnMapUse.get("usermodelspeed"))));
        userEvaluationVo.setModelCreateFrequency(Integer.parseInt(transNullValue(returnMapUse.get("modelcreatefrequency"))));
        userEvaluationVo.setNeedSubmitCount(Integer.parseInt(transNullValue(returnMapUse.get("needsubmitcount"))));
        userEvaluationVo.setNeedRejectCount(Integer.parseInt(transNullValue(returnMapUse.get("needrejectcount"))));
        userEvaluationVo.setNeedPassCount(Integer.parseInt(transNullValue(returnMapUse.get("needpasscount"))));
        userEvaluationVo.setCommentCount(Integer.parseInt(transNullValue(returnMapUse.get("commentcount"))));
        userEvaluationVo.setDeclaredBattleCount(Integer.parseInt(transNullValue(returnMapUse.get("declaredbattlecount"))));

        return Result.success(userEvaluationVo);
    }

    private String transNullValue(Object value){
        String transValue = String.valueOf(value);
        if(StringUtils.isBlank(transValue)||"null".equalsIgnoreCase(transValue)){
            transValue="0";
        }
           return transValue;
    }


    /**
     * 查看人员评价详情
     */
    @Override
    public Result viewUserEvaluationDetail(SupermarkModelVo modelVo) {
        Assert.notNull(modelVo, "模型数据不能为空");
        Assert.notNull(modelVo.getUserId(), "用户未登录");
        Assert.notNull(modelVo.getAppraisedUserId(), "被评价者id不能为空");


        //查询评价记录
        UserOperateBehavior userOperBeh = superMarkModelingServiceImpl.queryUserOperateBehaviorObject(modelVo.getAppraisedUserId(), modelVo.getUserId(),
                modelVo.getOperateType());
        if (userOperBeh != null) {
            Set<UserOperateBehaviorResult> userOperBehResSet = userOperBeh.getUserOperateBehaviorResultSets();
            if (CollectionUtils.isNotEmpty(userOperBehResSet)) {
                for (UserOperateBehaviorResult userOperBehRes : userOperBehResSet) {
                    UserEvaluation modelEvaluation = (UserEvaluation) baseDao.get(UserEvaluation.class, userOperBehRes.getId());
                    modelVo.setOperateContent(modelEvaluation.getOperateContent());
                }
            }
        }

        return Result.success(modelVo);
    }
}
