package com.dragonsoft.cicada.datacenter.modules.datavisual.model;

import cn.hutool.core.date.StopWatch;
import com.code.common.utils.StringUtils;
import com.code.dragonsoft.dataquery.service.querymodel.ColumnDataModel;
import com.code.meta.dml.core.BaseRow;
import com.code.meta.dml.standard.Dimension;
import com.code.meta.dml.standard.IMultCdin;
import com.code.meta.dml.standard.Measure;
import com.code.meta.dml.standard.StandardQuery;
import com.code.meta.dml.standard.cdins.Order;
import com.code.meta.dml.standard.cdins.QueryCdins;
import com.code.metadata.datavisual.WidgetDatasetDims;
import com.code.metadata.datavisual.WidgetDatasetDimsDrill;
import com.code.metadata.datavisual.WidgetDatasetMeasures;
import com.code.metaservice.standmb.vo.VisualMbTreeVo;
import com.dragonsoft.cicada.datacenter.modules.datavisual.datapolishing.DateGranularityEnum;
import com.dragonsoft.cicada.datacenter.modules.datavisual.datapolishing.datapolishings.AbsDataPolishing;
import com.dragonsoft.cicada.datacenter.modules.datavisual.dataset.IDataSetBuilder;
import com.dragonsoft.cicada.datacenter.modules.datavisual.function.ChartConfig;
import com.dragonsoft.cicada.datacenter.modules.datavisual.model.annotation.WidgetLabel;
import com.dragonsoft.cicada.datacenter.modules.datavisual.model.enums.FuncTypeEnum;
import com.dragonsoft.cicada.datacenter.modules.datavisual.model.enums.RapidCalculationEnum;
import com.dragonsoft.cicada.datacenter.modules.datavisual.model.enums.WidgetType;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.vo.MbEnumTreeVo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Slf4j
@WidgetLabel(name = "柱状图", describe = "简单单维柱状图", type = WidgetType.BAR_CHART)
public class BarChartWidget extends AbsChartWidget {

    protected BarChartData barChartData = new BarChartData();

    private List<String> codeList = Arrays.asList("r", "z", "n", "y", "jidu");

    protected Map<String, Object> builderSingleDimData(ColumnDataModel result) {
        Map<String, Object> rMap = new HashMap<>();
        if (null == result || result.getFieldName().isEmpty()) {
            rMap.put("code", -1);
            return rMap;
        }
        WidgetDatasetDims dim = this.widgetDataset.getWidgetDatasetDims().iterator().next();

        WidgetDatasetDimsDrill drillDim = null;
        if (this.widgetDataset.getWidgetDatasetDimsDrill().size() > 0) {
            drillDim = this.widgetDataset.getWidgetDatasetDimsDrill().iterator().next();
        }
        WidgetDatasetMeasures m = null;

        for (WidgetDatasetMeasures measures : this.widgetDataset.getWidgetDatasetMeasures()) {
            if (null != measures.getIsDivisor() && "true".equals(measures.getIsDivisor())) {
                m = measures;
                break;
            } else if (null == measures.getIsDivisor()) {
                m = measures;
                break;
            }
        }


        String dname = "";
        if (drillDim == null) {
            dname = dim.getFiledName();
        } else {
            dname = drillDim.getFiledName();
        }
        this.barChartData.addColumns(dname);
        String mname = "";
        String filedCode = "";
        if (m != null) {
            mname = m.getFiledName() != null ? m.getFiledName() : m.getFiledCode();
            filedCode = m.getFiledCode();
        } else {
            mname = filedCode = "default";
        }
        this.barChartData.addColumns(mname);
        for (Map rowMap : result.getFieldValue()) {
//            BaseRow b = (BaseRow) o;
            Map map = new LinkedHashMap();
            String val = this.getDimsFilterVal(dim, drillDim, rowMap);
            if (StringUtils.isNotBlank(val)) {
                map.put(dname, val);
                if (null == rowMap.get(filedCode)) {
                    map.put(mname, 0);
                } else {
                    map.put(mname, this.getMeasuresFilterVal(m, rowMap));
                }
                this.barChartData.rows.add(map);
            }
        }
        rMap.put("code", 1);
        rMap.put("data", this.barChartData);
        return rMap;
    }

    @Override
    public Map<String, Object> loadingData(IDataSetBuilder dataSetBuilder, int mode, String code, int timers) {

        for (WidgetDatasetDims fromListDim : this.widgetDataset.getFromListDims()) {
            //用于格式化周和季度的数据
            String dateGranularity = getMbNameByCode(fromListDim.getFiledCode());
            fromListDim.setFormatDateGranularity(dateGranularity);
        }
        String newQuery = getSQL(dataSetBuilder, mode);
        StopWatch stopWatch = new StopWatch("可视化柱状图");
        stopWatch.start("统一访问层查询");
        //查询数据
        ColumnDataModel originalColumns = getColumnDataModel(newQuery);
        stopWatch.stop();

        stopWatch.start("前端对象封装");

        //数据补零
        dataPolishing(originalColumns);
        Map<String, Object> datas = resultInfo(code, this.doFunction(originalColumns));
        stopWatch.stop();
        if (stopWatch.getTotalTimeSeconds() >= timers) {
            log.info(stopWatch.prettyPrint());
        }
        return datas;
    }

    private void dataPolishing(ColumnDataModel originalColumns) {
        List<WidgetDatasetDims> fromListDims = this.widgetDataset.getFromListDims();
        WidgetDatasetDims dmis = null;
        if(fromListDims.size() <= 0){
            return;
        }
        if (fromListDims.size() > 1){
            for (WidgetDatasetDims fromListDim : fromListDims) {
                if("date".equalsIgnoreCase(fromListDim.getType())){
                    dmis = fromListDim;
                    break;
                }
            }
        } else{
            dmis = fromListDims.get(0);
        }
        if(dmis == null) return;
        String dateGranularity = dmis.getDataPolishingDateGranularity();
        if (StringUtils.isBlank(dateGranularity) || "NONE".equalsIgnoreCase(dateGranularity)) return;
        AbsDataPolishing dataPolishing = DateGranularityEnum.getDatePolishingByCode(dateGranularity);
        dataPolishing.doDataPolishing(originalColumns, dmis);
    }

    private String getMbNameByCode(String filedCode) {
        List<VisualMbTreeVo> visualMbAll = mbService.getVisualMbAllList();
        for (int i = 0; i < visualMbAll.size(); i++) {
            Map<String, String> visualMbTreeVo = (Map<String, String>) visualMbAll.get(i);
            if ("周期时间".equalsIgnoreCase(visualMbTreeVo.get("name"))) {
                List<MbEnumTreeVo> codes = mbService.getCodesById(visualMbTreeVo.get("id"));
                for (MbEnumTreeVo code : codes) {
                    if (code.getValue().equals(filedCode)) {
                        return code.getLabel();
                    }
                }
            }
        }
        //todo 码表周和当年周的标准未确定
        if ("z_w".equalsIgnoreCase(filedCode)) {
            return "周";
        }
        return null;
    }

    /**
     * 是否执行快速分析
     *
     * @param originalColumns
     * @return
     */
    private ColumnDataModel doFunction(ColumnDataModel originalColumns) {
        ColumnDataModel columns;
        if (originalColumns == null) return null;

        List<WidgetDatasetMeasures> functionMeasures = this.widgetDataset.getFromListMeasures().stream()
                .filter(s -> null != s.getFunctionsJson() && !"".equals(s.getFunctionsJson()) && !"[]".equals(s.getFunctionsJson())).collect(Collectors.toList());


        if (functionMeasures.size() != 0) {
            ChartConfig config = super.builderChartConfig(this.widgetDataset.getFromListDims(), this.widgetDataset.getFromListMeasures(), originalColumns);
            columns = functionScheduler.execute(originalColumns, config);
        } else {
            columns = originalColumns;
        }
        return columns;
    }

    protected Map<String, Object> resultInfo(String code, ColumnDataModel columns) {
        if (null == columns || columns.getFieldName().isEmpty()) {
            Map map = new HashMap();
            map.put("code", -1);
            return map;
        }
        List<WidgetDatasetDims> dlist = this.widgetDataset.getFromListDims();
        this.widgetDataset.setWidgetDatasetDims(this.getNewWidgetDatasetDims(dlist));
        return builderResult(columns, code);
    }


    private Set<WidgetDatasetDims> getNewWidgetDatasetDims(List<WidgetDatasetDims> dlist) {
        List<WidgetDatasetDims> datasetDims = dlist.stream().filter(s -> "date".equals(s.getType())).collect(Collectors.toList());
        if (datasetDims.size() != 0) {
            Set<WidgetDatasetDims> notDatasetDims = dlist.stream().filter(s -> !"date".equals(s.getType())).collect(Collectors.toSet());
            Set<WidgetDatasetDims> widgetDatasetDims = notDatasetDims.stream().filter(s -> codeList.indexOf(s.getFiledCode()) == -1).collect(Collectors.toSet());
            Set<WidgetDatasetDims> notHavewidgetDatasetDims = dlist.stream().filter(s -> codeList.indexOf(s.getFiledCode()) == -1).collect(Collectors.toSet());

            //type不是date 的维度数量 不等 不需要替换的维度数量 需要替换维度
            if (notDatasetDims.size() != notHavewidgetDatasetDims.size()) {
                //找出被替换的维度下标
                AtomicInteger index = new AtomicInteger(0);
                //找出被替换的维度的index
                int datasetDimsIndex = dlist.stream().filter(s -> {
                    //每比对一个元素，数值加1
                    index.getAndIncrement();
                    return codeList.indexOf(s.getFiledCode()) != -1;
                }).findFirst().get().getIndex();
                //被替换是第一个维度 且只有一个替换维度 替换维度index替换
                if (1 == index.get() && 1 == notHavewidgetDatasetDims.size()) {
                    datasetDims.get(index.get() - 1).setIndex(datasetDimsIndex);
                }
                datasetDims.addAll(widgetDatasetDims);
                return datasetDims.stream().collect(Collectors.toSet());
            } else {
                return notHavewidgetDatasetDims.stream().collect(Collectors.toSet());
            }
        } else {
            return dlist.stream().collect(Collectors.toSet());
        }
    }

    @Override
    public String getSQL(IDataSetBuilder dataSetBuilder, int mode) {
        StandardQuery query = new StandardQuery(this.widgetDataset.getDbType());
        if (this.getWidgetDataset().getDbType() != null && this.getWidgetDataset().getDbType().toLowerCase().equals(DB_TYPE_ES)) {
            query.setSqlTYpe(StandardQuery.type.ES);
        }

        List<WidgetDatasetDims> dlist = this.widgetDataset.getFromListDims();

        Order[] orders = this.getOrders(dlist, this.widgetDataset.getFromListMeasures());
        if (orders != null) {
            query.setOrderCdins(orders);
        }

        QueryCdins queryCdins = query.getQueryCdins();
        query.setTableName(getSearchSQL(this.widgetDataset.getDatasetId()));
        IMultCdin cd = this.getCondition(queryCdins);
        this.addMeasures(query);

        List<WidgetDatasetDimsDrill> drillList = this.widgetDataset.getFromListDimsDrill();
        this.addDimensions(query, queryCdins, cd, dlist, drillList);

        /*if (null != this.widgetDataset.getDataArticleNumber()) {
            LimitCdin limitCdin = new LimitCdin(this.widgetDataset.getDataArticleNumber());
            query.setLimitCdin(limitCdin);
        }*/

        query.setConditions(cd);
        String newQuery = query.toExpression().getScript();
        if (this.getWidgetDataset().getDbType() != null && this.getWidgetDataset().getDbType().equals(DB_TYPE_ES)) {
            // 统一访问层ES限制问题,此解决 ********
            newQuery += " limit " + Integer.MAX_VALUE;
        }
        if (mode != PREVIEW_MODE) {
            this.previewLine = -1;
        }
        if (Objects.equals(this.widgetDataset.getDbType().toUpperCase(),"MYSQL")){
            return newQuery.replaceAll("`", "");
        }
        return newQuery.replaceAll("`", "\"");
    }

    private void addDimensions(StandardQuery query, QueryCdins queryCdins, IMultCdin cd, List<WidgetDatasetDims> dlist, List<WidgetDatasetDimsDrill> drillList) {
        if (drillList.isEmpty()) {
            List<WidgetDatasetDims> datasetDims = dlist.stream().filter(s -> "date".equals(s.getType())).collect(Collectors.toList());
            List<WidgetDatasetDims> notDatasetDims = dlist.stream().filter(s -> !"date".equals(s.getType())).collect(Collectors.toList());

            if (datasetDims.size() != 0) {
                List<WidgetDatasetDims> widgetDatasetDims = notDatasetDims.stream().filter(s -> codeList.indexOf(s.getFiledCode()) == -1).collect(Collectors.toList());
                List<WidgetDatasetDims> notHavewidgetDatasetDims = dlist.stream().filter(s -> codeList.indexOf(s.getFiledCode()) == -1).collect(Collectors.toList());
                List<WidgetDatasetDims> orderWidgetDatasetDims = notDatasetDims.stream().filter(s -> codeList.indexOf(s.getFiledCode()) != -1).collect(Collectors.toList());

                //有码表字段替换一个  没有全部替换
                if (notDatasetDims.size() != notHavewidgetDatasetDims.size()) {
                    for (WidgetDatasetDims dims : datasetDims) {
                        //获取第一个被替换字段的排序规则
                        dims.setOrderBy(orderWidgetDatasetDims.get(0).getOrderBy());
                    }

                    datasetDims.addAll(widgetDatasetDims);
                    //重新设置排序
                    Order[] orders = this.getOrders(datasetDims, this.widgetDataset.getFromListMeasures());
                    if (orders != null) {
                        query.setOrderCdins(orders);
                    }
                    for (WidgetDatasetDims datasetDim : datasetDims) {
                        query.addDimensions(new Dimension(datasetDim.getFiledCode(), datasetDim.getFiledCode()));
                    }
                } else {
                    for (WidgetDatasetDims datasetDim : notDatasetDims) {
                        query.addDimensions(new Dimension(datasetDim.getFiledCode(), datasetDim.getFiledCode()));
                    }
                }
            } else {
                for (WidgetDatasetDims datasetDim : dlist) {
                    query.addDimensions(new Dimension(datasetDim.getFiledCode(), datasetDim.getFiledCode()));
                }
            }

        } else {
            for (WidgetDatasetDimsDrill datasetDims : drillList) {
                query.addDimensions(new Dimension(datasetDims.getFiledCode(), datasetDims.getFiledCode()));
                cd.addCdin(queryCdins.isNotNull(datasetDims.getFiledCode()));
            }
        }

    }

    protected void addMeasures(StandardQuery query) {
        //度量字段相同的话，别名修改
        opinionAndChangeMeasures();
        if (this.widgetDataset.getWidgetDatasetMeasures().size() > 0) {
            Iterator<WidgetDatasetMeasures> iterator = this.widgetDataset.getWidgetDatasetMeasures().iterator();
            while (iterator.hasNext()) {
                WidgetDatasetMeasures m = iterator.next();
                query.addMeasures(new Measure(m.getFiledCode(), this.getFunc(m.getFuncType(), m.getIsDistinct()), m.getFiledAlias(), m.getIsDivisor() == null ? null : Boolean.valueOf(m.getIsDivisor())));
            }
        }
    }

    private void opinionAndChangeMeasures() {
        for (WidgetDatasetMeasures measure : this.widgetDataset.getWidgetDatasetMeasures()) {
            String opinionCode = StringUtils.isBlank(measure.getFiledAlias()) ? measure.getFiledCode() : measure.getFiledAlias();
            for (WidgetDatasetMeasures temMea : this.widgetDataset.getWidgetDatasetMeasures()) {
                String temCode = StringUtils.isBlank(measure.getFiledAlias()) ? measure.getFiledCode() : measure.getFiledAlias();
                //两个不是同一个度量
                if (measure != temMea && opinionCode.equalsIgnoreCase(temCode)) {
                    changeMeasures(measure, temMea);
                }
            }
        }
    }

    /**
     * 相同度量时需要改变别名，用于区分两个度量
     * @param measure
     * @param temMea
     */
    private void changeMeasures(WidgetDatasetMeasures measure, WidgetDatasetMeasures temMea) {
        String funcType = measure.getFuncType();
        String functionsJson = measure.getFunctionsJson();
        if (funcType != null && funcType.equalsIgnoreCase(temMea.getFuncType()) && functionsJson!=null && functionsJson.equalsIgnoreCase(temMea.getFunctionsJson())) {
            return;
        }
//        functionsJson默认值是"[]"
        functionsJson = functionsJson == null || functionsJson.length() <= 2 ? "" : functionsJson;
        if (StringUtils.isBlank(funcType)) {
            measure.setFiledAlias(measure.getFiledAlias() + "_" + RapidCalculationEnum.getAliasByCode(functionsJson));
            measure.setFiledName(measure.getFiledName() + "(" + RapidCalculationEnum.getNameByCode(functionsJson) + ")");
        } else {
            String json = StringUtils.isNotBlank(functionsJson) ? "_" + RapidCalculationEnum.getAliasByCode(functionsJson) : "";
            String name = StringUtils.isNotBlank(functionsJson) ? "-" + RapidCalculationEnum.getNameByCode(functionsJson) : "";
            measure.setFiledAlias(measure.getFiledAlias() + "_" + funcType + json);
            measure.setFiledName(measure.getFiledName() + "(" + FuncTypeEnum.getNameByCode(funcType) + name + ")");
        }

    }


    private void setIsNullCondition(QueryCdins queryCdins, IMultCdin cd, List<WidgetDatasetDims> dlist) {
        dlist.forEach(t -> {
            cd.addCdin(queryCdins.isNotNull(t.getFiledCode()));
        });
    }

    @Nullable
    protected ColumnDataModel getColumnDataModel(String newQuery) {
        ColumnDataModel columns = null;
        columns = query(newQuery);
        return columns;
    }

    public Map<String, Object> builderResult(ColumnDataModel columns, String code) {
        Map re = new HashMap();
        List<WidgetDatasetDims> dlist = this.widgetDataset.getFromListDims();
        List<WidgetDatasetDimsDrill> drilllist = this.widgetDataset.getFromListDimsDrill();
        boolean isTowDim = false;
        if (drilllist.isEmpty() && dlist.size() > 1) {
            isTowDim = true;
        } else if (drilllist.size() > 1) {
            isTowDim = true;
        }
        if (isTowDim) {
            this.builderFistDimData(columns);
            this.builderTwoDimData(columns);
            //构建第二维度的数据
        } else {
            this.builderSingleDimData(columns);
        }
        List<String> dimsCode = dlist.stream().map(s -> s.getFiledName()).collect(Collectors.toList());
        this.barChartData.setDimsCodes(dimsCode);
        getLimitData(this.widgetDataset.getDataArticleNumber());
        re.put("code", 1);
        re.put("data", this.barChartData);
        return re;
    }
    private void getLimitData(Long limit){
        if(limit == null) return;
        List<Map> rows = this.barChartData.getRows();
        List<Map> newResROw = Lists.newArrayList();

        if(limit > rows.size()){
            limit = Long.valueOf(rows.size());
        }

        for (long i = rows.size() - limit; i < rows.size(); i++) {
            newResROw.add(rows.get((int) i));
        }
        this.barChartData.setRows(newResROw);

    }


    private void builderFistDimData(ColumnDataModel columns) {
        List<WidgetDatasetDims> dlist = this.widgetDataset.getFromListDims();
        List<WidgetDatasetDimsDrill> drilllist = this.widgetDataset.getFromListDimsDrill();
        String filedName = "";
        if (drilllist.isEmpty()) {
            filedName = dlist.get(0).getFiledName();
        } else {
            filedName = drilllist.get(0).getFiledName();
        }
        this.barChartData.addColumns(filedName);
        //取出第一维度的X数据
        String finalFiledName = filedName;
        columns.getFieldValue().forEach(r -> {
//            BaseRow b = (BaseRow) r;
            Map map = new LinkedHashMap();
            WidgetDatasetDims dim = dlist.isEmpty() ? null : dlist.get(0);
            WidgetDatasetDimsDrill drill = drilllist.isEmpty() ? null : drilllist.get(0);
            String val = this.getDimsFilterVal(dim, drill, r);
            if (StringUtils.isNotBlank(val)) {
                map.put(finalFiledName, val);
                if (map.size() > 0 && !this.barChartData.rows.contains(map)) {
                    this.barChartData.rows.add(map);
                }
            }

        });
    }

    private void builderTwoDimData(ColumnDataModel rs) {
        List<WidgetDatasetDims> dlist = this.widgetDataset.getFromListDims();
        List<WidgetDatasetDimsDrill> drilllist = this.widgetDataset.getFromListDimsDrill();
        WidgetDatasetMeasures m = this.widgetDataset.getWidgetDatasetMeasures().iterator().next();
        WidgetDatasetDims fist = dlist.isEmpty() ? null : dlist.get(0);
        WidgetDatasetDims two = dlist.isEmpty() ? null : dlist.get(1);
        WidgetDatasetDimsDrill fistDrill = drilllist.isEmpty() ? null : drilllist.get(0);
        WidgetDatasetDimsDrill twoDrill = drilllist.isEmpty() ? null : drilllist.get(1);

        String dimFiledName = "";
        if (drilllist.isEmpty()) {
            dimFiledName = StringUtils.isNotBlank(fist.getFiledCode()) ? fist.getFiledCode() : fist.getFiledName();
        } else {
            dimFiledName = StringUtils.isNotBlank(fistDrill.getFiledCode()) ? fistDrill.getFiledCode() : fistDrill.getFiledName();
        }

        String twoName = "";
        if (drilllist.isEmpty()) {
            twoName = StringUtils.isNotBlank(two.getFiledCode()) ? two.getFiledCode() : two.getFiledName();
        } else {
            twoName = StringUtils.isNotBlank(twoDrill.getFiledCode()) ? twoDrill.getFiledCode() : twoDrill.getFiledName();
        }

        List<Map> rowList = Lists.newArrayList();
        String meaName = StringUtils.isNotBlank(m.getFiledCode()) ? m.getFiledCode() : m.getFiledName();
        Map<String, Map> towDimMap = getTowDimMap(rs.getFieldValue(), dimFiledName, twoName, meaName);
        for (Map.Entry<String, Map> entry : towDimMap.entrySet()) {
            Map value = entry.getValue();
            String filedName = StringUtils.isNotBlank(fist.getFiledName()) ? fist.getFiledName() : fist.getFiledCode();
            value.put(filedName, entry.getKey());
            rowList.add(value);
        }
        this.barChartData.rows = rowList;
    }

    private Map<String, Map> getTowDimMap(List<Map> fieldValue, String dimFiledName, String towName, String meaName) {
        Map<String, Map> resMap = Maps.newLinkedHashMap();
        for (Map map : fieldValue) {
            String firstName = String.valueOf(map.get(dimFiledName));
            if (firstName != null) {
                Map<Object, Object> towMap = resMap.get(firstName);
                if (towMap == null) {
                    towMap = Maps.newHashMap();
                }
                this.barChartData.addColumns(String.valueOf(map.get(towName)));
                if (map.get(towName) != null && !map.get(towName).equals("")) {
                    towMap.put(map.get(towName), map.get(meaName));
                    resMap.put(firstName, towMap);
                }
            }
        }
        return resMap;
    }

    private String getVal(WidgetDatasetDims d, BaseRow b) {
        String val;
        if ("time".equals(d.getType())) {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            val = simpleDateFormat.format(b.getVal(d.getFiledCode()));
        } else {
            val = b.getVal(d.getFiledCode()).toString();
        }
        return val;
    }

    class BarChartData {
        List<String> columns = new LinkedList<>();
        List<Map> rows = new LinkedList<>();
        List<String> dimsCodes = new LinkedList<>();
        //用于组合图前端联动的原始时间格式和转换后的时间格式之间的映射
        Map<String, Object> formatMapping = new LinkedHashMap<>();

        public void addColumns(String c) {
            for (String s : columns) {
                if (s.equals(c)) {
                    return;
                }
            }
            this.columns.add(c);
        }

        public Map<String, Object> getFormatMapping() {
            return formatMapping;
        }

        public void setFormatMapping(Map<String, Object> formatMapping) {
            this.formatMapping = formatMapping;
        }

        public List<String> getColumns() {
            return columns;
        }

        public void setColumns(List<String> columns) {
            this.columns = columns;
        }

        public List<Map> getRows() {
            return rows;
        }

        public void setRows(List<Map> rows) {
            this.rows = rows;
        }

        public List<String> getDimsCodes() {
            return dimsCodes;
        }

        public void setDimsCodes(List<String> dimsCodes) {
            this.dimsCodes = dimsCodes;
        }
    }

    public BarChartData getBarChartData() {
        return barChartData;
    }

    public void setBarChartData(BarChartData barChartData) {
        this.barChartData = barChartData;
    }
}
