package com.dragonsoft.cicada.datacenter.modules.datavisual.datapolishing.datapolishings;


import com.code.common.utils.assertion.Assert;

import java.util.Calendar;


/**
 * <AUTHOR>
 * @Date 2021/3/16 10:05
 */
public class MonthDataPolishing extends AbsDataPolishing {
    private final String MONTH_FORMAT = "yyyyMM";
    private final String MONTH= "月";

    @Override
    public void calculateDate() {
        if (minDate.length() != 6) {
            granularituFail(MONTH,MONTH_FORMAT,minDate);
        }
        if (maxDate.length() != 6) {
            granularituFail(MONTH,MONTH_FORMAT,maxDate);
        }
        this.dataPolishingList = getAllDatesBetweenTwoDates(minDate, maxDate, Calendar.MONTH, MONTH_FORMAT, MONTH);

    }

    @Override
    public void checkData(String data) {
        int temData = parseInt(data,MONTH_FORMAT);
        int monthData = temData % 100;
        if (monthData > 12 || monthData == 0) {
            Assert.fail("月数最大为12月，最小为1月！");
        }
    }


}
