package com.dragonsoft.cicada.datacenter.modules.dataplan.externaldatasources.service.impl;

import com.code.dragonsoft.dataquery.service.DDLOperationService;
import com.code.metadata.res.common.ClassifierStat;
import com.code.metaservice.core.ModelElementService;
import com.code.metaservice.ddl.ILogicDataObjService;
import com.code.metaservice.res.common.ClassifierStatService;
import com.dragoninfo.dfw.entity.*;
import com.dragoninfo.dfw.service.*;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.service.IDataSetOperationService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.externaldatasources.service.IDataSetAuthService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.externaldatasources.vo.DataObjectVo;
import com.dragonsoft.cicada.datacenter.modules.dataplan.externaldatasources.vo.DataSetAuthVo;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.service.IRoleService;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.service.IUserService;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.vo.RoleVo;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.vo.UserVo;
import com.fw.service.DfwBaseService;
import com.fw.service.annotation.Service;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.Session;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/6/8
 */
@Service
@Slf4j
public class DataSetAuthServiceImpl extends DfwBaseService implements IDataSetAuthService {

    public static final String ONE = "1";
    public static final String ID = "id";


    @Autowired
    private SysAuthUserService sysAuthUserService;
    @Autowired
    private SysFuncService sysFuncService;
    @Autowired
    private SysAuthObjFuncService sysAuthObjFuncService;
    @Autowired
    private SysAuthRoleService sysAuthRoleService;
    @Autowired
    private SysAuthObjService sysAuthObjService;
    @Autowired
    private IUserService userService;
    @Autowired
    private IRoleService roleService;
    @Autowired
    private IDataSetOperationService dataSetOperationService;

    @Autowired
    private ClassifierStatService classifierStatService;

    @Autowired
    private DDLOperationService ddlOperationService;

    @Autowired
    private ILogicDataObjService logicDataObjService;

    @Autowired
    private ModelElementService modelElementService;


    @Transactional
    @Override
    public void saveDataSetAuth(List<DataObjectVo> dataObjectVos, String userId) {
        for (DataObjectVo dataObjectVo : dataObjectVos) {
            TSysFunc tSysFuncBase = new TSysFunc();
            tSysFuncBase.setFuncCode(dataObjectVo.getCode());
            tSysFuncBase.setFuncName(dataObjectVo.getName());
            tSysFuncBase.setFuncType(ONE);
            tSysFuncBase.setEnableState(ONE);
            tSysFuncBase.setDescription(userId);
            sysFuncService.store(tSysFuncBase);
        }
    }

    @Override
    public void saveFirstAuth(DataSetAuthVo dataSetAuthVo) {
        //数据源第一次注册需要将数据源注册
//        this.saveDataSetAuth(dataSetAuthVo.getDataSetVos());
        //设置权限
        this.saveManyToManyDataSetRelation(dataSetAuthVo);
    }

    @Override
    public void saveBatchDataSetAuth(DataSetAuthVo dataSetAuthVo, Boolean hasOldUserOrRole) {
        //有取消旧授权用户时执行
        if (hasOldUserOrRole) {
            for (DataObjectVo dataObjectVo : dataSetAuthVo.getDataObjectVos()) {
                List<String> allId = userService.getAllUserAuth(dataObjectVo.getCode());

                List<String> deleteIds = allId.stream().filter(s -> dataSetAuthVo.getUserVos().stream().map(t -> t.getId()).collect(Collectors.toList()).indexOf(s) == -1).collect(Collectors.toList());

                for (String id : deleteIds) {
                    this.deleteDataSetRelation(dataObjectVo.getCode(), id);
                }
            }
        }

        this.saveManyToManyDataSetRelation(dataSetAuthVo);
    }


    @Override
    public void saveOneDataSetUserAuth(DataSetAuthVo dataSetAuthVo) {
        //获取此功能的所有授权用户
        List<String> allId = userService.getAllUserAuth(dataSetAuthVo.getDataObjectVos().get(0).getCode());
        //筛选出前端取消勾选的用户
        List<String> deleteIds = allId.stream().filter(s -> dataSetAuthVo.getUserVos().stream().map(t -> t.getId()).collect(Collectors.toList()).indexOf(s) == -1).collect(Collectors.toList());

        //删除取消用户与此功能关系
        for (DataObjectVo dataObjectVo : dataSetAuthVo.getDataObjectVos()) {
            for (String id : deleteIds) {
                this.deleteDataSetRelation(dataObjectVo.getCode(), id);
            }
        }
        this.saveManyToManyDataSetRelation(dataSetAuthVo);
    }

    @Override
    public void saveOneDataSetRoleAuth(DataSetAuthVo dataSetAuthVo) {
        List<String> allId = roleService.queryAllRoleAuth(dataSetAuthVo.getDataObjectVos().get(0).getCode());
        List<String> deleteIds = allId.stream().filter(s -> dataSetAuthVo.getRoleVos().stream().map(t -> t.getRoleId()).collect(Collectors.toList()).indexOf(s) == -1).collect(Collectors.toList());

        for (DataObjectVo dataObjectVo : dataSetAuthVo.getDataObjectVos()) {
            for (String id : deleteIds) {
                this.deleteDataSetRelation(dataObjectVo.getCode(), id);
            }
        }
        this.saveManyToManyDataSetRelation(dataSetAuthVo);
    }

    @Override
    public void deletDataSetAuthRelation(DataSetAuthVo dataSetAuthVo) {
        List<String> functionCodes = dataSetAuthVo.getDataObjectVos().stream().map(s -> s.getCode()).collect(Collectors.toList());
        //删除数据集用户关系
        if (null != dataSetAuthVo.getUserVos()) {
            for (UserVo user : dataSetAuthVo.getUserVos()) {
                this.deleteRelation(user.getId(), functionCodes);
            }
        }


        //删除数据集角色关系
        if (null != dataSetAuthVo.getRoleVos()) {
            for (RoleVo role : dataSetAuthVo.getRoleVos()) {
                this.deleteRelation(role.getRoleId(), functionCodes);
            }
        }

        Session session = this.dfwBaseDao.getSession();
        session.flush();
        deletePhysicalToLogicDataSet(dataSetAuthVo);

    }


    @Override
    public void deleteDCThreeDataSetAuthRelation(DataSetAuthVo dataSetAuthVo, String userId) {
        List<String> functionCodes = dataSetAuthVo.getDataObjectVos().stream().map(s -> s.getCode()).collect(Collectors.toList());
        //删除数据集用户关系
        if (null != dataSetAuthVo.getUserVos()) {
            for (UserVo user : dataSetAuthVo.getUserVos()) {
                this.deleteRelation(user.getId(), functionCodes);
            }
        }

        //删除数据集角色关系
        if (null != dataSetAuthVo.getRoleVos()) {
            for (RoleVo role : dataSetAuthVo.getRoleVos()) {
                this.deleteRelation(role.getRoleId(), functionCodes);
                //找到角色下的所有用户
                List<TSysAuthObj> userBelongRoles = roleService.getTSysUserByRoleId(role.getRoleId());
                //删除该角色下的用户关系
                for (TSysAuthObj userBelongRole : userBelongRoles) {
                    //避免伤害到自己
                    if (!userId.equals(userBelongRole.getId())) {
                        this.deleteRelation(userBelongRole.getId(), functionCodes);
                    }
                }
            }
        }

    }

    @Transactional
    @Override
    public void saveDataSetAuthDCThree(List<DataObjectVo> dataObjectVos, String userId) {
        for (DataObjectVo dataObjectVo : dataObjectVos) {
            Map<String, String> params = Maps.newHashMap();
            params.put("func_code", dataObjectVo.getCode());
            TSysFuncBase funcBase = (TSysFuncBase) sysFuncService.query(params);
            if (funcBase == null) {
                TSysFunc tSysFuncBase = new TSysFunc();
                tSysFuncBase.setFuncCode(dataObjectVo.getCode());
                tSysFuncBase.setFuncName(dataObjectVo.getName());
                tSysFuncBase.setFuncType(ONE);
                tSysFuncBase.setEnableState(ONE);
                tSysFuncBase.setDescription(userId);
                sysFuncService.store(tSysFuncBase);
            }
        }
    }

    @Override
    public boolean checkLogicDataObj(String datasetId) {
        String sql = "select type from t_md_element where id='" + datasetId + "'";
        List<Map> list = this.dfwBaseDao.sqlQueryForList(sql);
        if (list.size() == 0) {
            return false;
        }
        return list.get(0).get("type").toString().equalsIgnoreCase("LogicDataObj") ? true : false;
    }

    private void deletePhysicalToLogicDataSet(DataSetAuthVo dataSetAuthVo) {
        List<String> functionCodes = dataSetAuthVo.getDataObjectVos().stream().map(s -> s.getCode()).collect(Collectors.toList());
        List<String> userIds = getAllUserIdsByRoleId(dataSetAuthVo);
        for (String userId : userIds) {
            for (String code : functionCodes) {
                Integer num = userService.userAuthDataNumber(userId, code);
                if (num == 0) {
                    deleteAccreditDataSet(code, userId);
                }
            }
        }
    }

    @Override
    public void deleteAccreditDataSet(String code, String userID) {
        String classifierId = logicDataObjService.getClassifierIdByPhysicalIdAndUserId(code, userID);//根据物理表id和用户id 拿到对应的logicDataset id
        ClassifierStat classifierStat = classifierStatService.getClassifierStat(ClassifierStat.class, classifierId);
        if (null != classifierStat) {
            String vName = classifierStat.getGlobalCode();//拿到视图名
            String schemaId = classifierStat.getOwner().getOwnerId();//拿到schcemaId,及logic的ownid,的ownid就是schemaid
            try {
                ddlOperationService.dropView(schemaId, vName, true);
                logicDataObjService.deleteLogicDataSetAfterDropView(classifierId);//删除视图后，将对应的logicDataset删掉
            } catch (Exception e) {
               log.error(e.getMessage(),e);
            }
        }
    }


    /**
     * 3.0 设置数据集权限  相比2.0少了将物理表准换成逻辑表的步骤
     *
     * @param dataSetAuthVo
     */
    private void saveDCThreeManyToManyDataSetRelation(DataSetAuthVo dataSetAuthVo) {
        List<String> functionCodes = dataSetAuthVo.getDataObjectVos().stream().map(DataObjectVo::getCode).collect(Collectors.toList());
        //设置数据用户权限
        if (null != dataSetAuthVo.getUserVos()) {
            for (UserVo user : dataSetAuthVo.getUserVos()) {
                Map<String, String> userParams = Maps.newHashMap();
                userParams.put(ID, user.getId());
                TSysAuthUser tSysAuthUser = (TSysAuthUser) sysAuthUserService.query(userParams);
                if (null != tSysAuthUser) {
                    this.saveDataSetRelation(tSysAuthUser, functionCodes);
                }
            }
        }

        //设置数据角色权限
        if (null != dataSetAuthVo.getRoleVos()) {
            for (RoleVo role : dataSetAuthVo.getRoleVos()) {
                Map<String, String> roleParams = Maps.newHashMap();
                roleParams.put(ID, role.getRoleId());
                TSysAuthRole tSysAuthRole = (TSysAuthRole) sysAuthRoleService.query(roleParams);
                if (null != tSysAuthRole) {
                    //授权角色
                    this.saveDataSetRelation(tSysAuthRole, functionCodes);
                    //只需授权角色
                   /* //找到角色下的所有用户
                    List<TSysAuthObj> userBelongRoles = roleService.getTSysUserByRoleId(tSysAuthRole.getId());
                    //授权该角色下的用户
                    for (TSysAuthObj userBelongRole : userBelongRoles) {
                        this.saveDataSetRelation(userBelongRole, functionCodes);
                    }*/
                }
            }
        }
    }

    @Override
    public void addDCThreeDataSetAuth(DataSetAuthVo dataSetAuthVo) {
        this.saveDCThreeManyToManyDataSetRelation(dataSetAuthVo);
    }

    @Override
    public void saveDCThreeBatchDataSetAuth(DataSetAuthVo dataSetAuthVo, Boolean hasOldUserOrRole) {
        //有取消旧授权用户时执行
        if (hasOldUserOrRole) {
            for (DataObjectVo dataObjectVo : dataSetAuthVo.getDataObjectVos()) {
                List<String> allId = userService.getAllUserAuth(dataObjectVo.getCode());

                List<String> deleteIds = allId.stream().filter(s -> {
                    List<String> stringList = dataSetAuthVo.getUserVos().stream().map(UserVo::getId).collect(Collectors.toList());
                    return !stringList.contains(s);
                }).collect(Collectors.toList());

                for (String id : deleteIds) {
                    this.deleteDataSetRelation(dataObjectVo.getCode(), id);
                }
            }
        }

        this.saveDCThreeManyToManyDataSetRelation(dataSetAuthVo);
    }

    @Override
    public void deleteDataSetRelation(String DataSetId, String id) {
        Map<String, String> params = Maps.newHashMap();
        params.put("func_code", DataSetId);
        params.put("obj_id", id);
        List<TSysAuthObjFunc> tSysAuthObjFuncs = sysAuthObjFuncService.queryList(params);

        Map<String, String> funcParams = Maps.newHashMap();
        funcParams.put("func_code", DataSetId);
        TSysFunc tSysFunc = (TSysFunc) sysFuncService.query(funcParams);

        if (!id.equals(tSysFunc.getDescription())) {
            for (TSysAuthObjFunc tSysAuthObjFunc : tSysAuthObjFuncs) {
                sysAuthObjService.deleteAuthObj(tSysAuthObjFunc);
            }
        }
    }

    @Override
    public Boolean IsAuthDataset(String tableId, String userId, List<TSysAuthRole> roleList) {
        Map<String, String> params = Maps.newHashMap();
        params.put("func_code", tableId);
        params.put("obj_id", userId);
        List<TSysAuthObjFunc> tSysAuthObjFuncs = sysAuthObjFuncService.queryList(params);

        for (TSysAuthRole role : roleList) {
            Map<String, String> roleParams = Maps.newHashMap();
            roleParams.put("func_code", tableId);
            roleParams.put("obj_id", role.getId());
            List<TSysAuthObjFunc> roleTSysAuthObjFuncs = sysAuthObjFuncService.queryList(roleParams);
            tSysAuthObjFuncs.addAll(roleTSysAuthObjFuncs);
        }
        return tSysAuthObjFuncs.size() == 0 ? false : true;
    }

    @Override
    public void saveLibraryUserRelation(String userId, String libraryId) {
        Map<String, String> params = Maps.newHashMap();
        params.put("func_code", libraryId);
        TSysFuncBase tSysFuncBase = (TSysFuncBase) sysFuncService.query(params);

        Map<String, String> userParams = Maps.newHashMap();
        userParams.put(ID, userId);
        TSysAuthUser tSysAuthUser = (TSysAuthUser) sysAuthUserService.query(userParams);

        TSysAuthObjFunc tSysAuthObjFunc = new TSysAuthObjFunc();
        tSysAuthObjFunc.settSysFuncBase(tSysFuncBase);
        tSysAuthObjFunc.settSysAuthObj(tSysAuthUser);
        sysAuthObjFuncService.store(tSysAuthObjFunc);
    }

    private void deleteRelation(String objectId, List<String> functionCodes) {
        for (String functionCode : functionCodes) {
            Map<String, String> params = Maps.newHashMap();
            params.put("obj_id", objectId);
            params.put("func_code", functionCode);
            TSysAuthObjFunc tSysAuthObjFunc = (TSysAuthObjFunc) sysAuthObjFuncService.query(params);

            Map<String, String> funcParams = Maps.newHashMap();
            funcParams.put("func_code", functionCode);
            TSysFunc tSysFunc = (TSysFunc) sysFuncService.query(funcParams);


            if (null != tSysAuthObjFunc && !objectId.equals(tSysFunc.getDescription())) {
                sysAuthObjService.deleteAuthObj(tSysAuthObjFunc);
            }
        }
    }

    /**
     * 设置权限
     *
     * @param dataSetAuthVo
     */
    private void saveManyToManyDataSetRelation(DataSetAuthVo dataSetAuthVo) {
        List<String> functionCodes = dataSetAuthVo.getDataObjectVos().stream().map(s -> s.getCode()).collect(Collectors.toList());
        physicalToLogicDataset(dataSetAuthVo);
        //设置数据用户权限
        if (null != dataSetAuthVo.getUserVos()) {
            for (UserVo user : dataSetAuthVo.getUserVos()) {
                Map<String, String> userParams = Maps.newHashMap();
                userParams.put(ID, user.getId());
                TSysAuthUser tSysAuthUser = (TSysAuthUser) sysAuthUserService.query(userParams);
                if (null != tSysAuthUser) {
                    this.saveDataSetRelation(tSysAuthUser, functionCodes);
                }
            }
        }

        //设置数据角色权限
        if (null != dataSetAuthVo.getRoleVos()) {
            for (RoleVo role : dataSetAuthVo.getRoleVos()) {
                Map<String, String> roleParams = Maps.newHashMap();
                roleParams.put(ID, role.getRoleId());
                TSysAuthRole tSysAuthRole = (TSysAuthRole) sysAuthRoleService.query(roleParams);
                if (null != tSysAuthRole) {
                    this.saveDataSetRelation(tSysAuthRole, functionCodes);
                }
            }
        }
    }

    private void saveManyToManyDataSetRelationNew(DataSetAuthVo dataSetAuthVo) {
        List<String> functionCodes = dataSetAuthVo.getDataObjectVos().stream().map(s -> s.getCode()).collect(Collectors.toList());
        //创建逻辑表
        physicalToLogicDatasetNew(dataSetAuthVo);
        //设置数据用户权限
        if (null != dataSetAuthVo.getUserVos()) {
            for (UserVo user : dataSetAuthVo.getUserVos()) {
                Map<String, String> userParams = Maps.newHashMap();
                userParams.put(ID, user.getId());
                TSysAuthUser tSysAuthUser = (TSysAuthUser) sysAuthUserService.query(userParams);
                if (null != tSysAuthUser) {
                    this.saveDataSetRelationNew(tSysAuthUser, functionCodes);
                }
            }
        }

        //设置数据角色权限
        if (null != dataSetAuthVo.getRoleVos()) {
            for (RoleVo role : dataSetAuthVo.getRoleVos()) {
                Map<String, String> roleParams = Maps.newHashMap();
                roleParams.put(ID, role.getRoleId());
                TSysAuthRole tSysAuthRole = (TSysAuthRole) sysAuthRoleService.query(roleParams);
                if (null != tSysAuthRole) {
                    this.saveDataSetRelationNew(tSysAuthRole, functionCodes);
                }
            }
        }
    }

    private void physicalToLogicDatasetNew(DataSetAuthVo dataSetAuthVo) {
        List<String> functionCodes = dataSetAuthVo.getDataObjectVos().stream().map(s -> s.getCode()).collect(Collectors.toList());
        List<String> userIds = getAllUserIdsByRoleId(dataSetAuthVo);
        for (String userId : userIds) {
            for (String code : functionCodes) {
                Integer num = userService.userAuthDataNumber(userId, code);
                if (num == 0) {
                    dataSetOperationService.saveDataSetNew(code,dataSetAuthVo.getDwId(),userId);
                }
            }
        }
    }

    /**
     * 物理表转换成逻辑表
     */
    private void physicalToLogicDataset(DataSetAuthVo dataSetAuthVo) {
        List<String> functionCodes = dataSetAuthVo.getDataObjectVos().stream().map(s -> s.getCode()).collect(Collectors.toList());
        List<String> userIds = getAllUserIdsByRoleId(dataSetAuthVo);
        for (String userId : userIds) {
            for (String code : functionCodes) {
                Integer num = userService.userAuthDataNumber(userId, code);
                if (num == 0) {
                    String dataSetDbType = dataSetOperationService.getDataSetDbType(code);
                    if (!"".equals(dataSetDbType) && !"ElasticSearch".equalsIgnoreCase(dataSetDbType) && !"Hbase".equalsIgnoreCase(dataSetDbType) && !"FILE".equalsIgnoreCase(dataSetDbType)) {
                        List<String> codeList = new ArrayList<String>();
                        codeList.add(code);
                        dataSetOperationService.accreditDataSet(codeList, dataSetAuthVo.getDwId(), userId);//物理表转换成logic表
                    }

                }
            }
        }
    }

    private List<String> getAllUserIdsByRoleId(DataSetAuthVo dataSetAuthVo) {
        List<String> userIds = new ArrayList<String>();
        if (null != dataSetAuthVo.getRoleVos() && dataSetAuthVo.getRoleVos().size() != 0) {//如果对角色授权
            List<String> roleIds = dataSetAuthVo.getRoleVos().stream().map(s -> s.getRoleId()).collect(Collectors.toList());
            for (String roleId : roleIds) {
                //根据角色id查用户
                List<String> userIdsByRoleId = roleService.getUserIdsByRoleId(roleId);
                userIdsByRoleId.stream().forEach(s -> {
                    userIds.add(s);
                });
            }
        }
        if (null != dataSetAuthVo.getUserVos() && dataSetAuthVo.getUserVos().size() != 0) {
            //直接获取用户跟某条授权数据的关系条数
            List<String> useridList = dataSetAuthVo.getUserVos().stream().map(s -> s.getId()).collect(Collectors.toList());
            for (String userId : useridList) {
                if (!userIds.contains(userId)) {
                    userIds.add(userId);
                }
            }
        }
        return userIds;
    }

    /**
     * 设置数据与对象（角色或者用户）关系
     *
     * @param tSysAuthObj
     * @param functionCodes
     */
    @Override
    public void saveDataSetRelation(TSysAuthObj tSysAuthObj, List<String> functionCodes) {
        for (String functionCode : functionCodes) {
            Map<String, String> params = Maps.newHashMap();
            params.put("func_code", functionCode);
            TSysFuncBase tSysFuncBase = (TSysFuncBase) sysFuncService.query(params);
            TSysAuthObjFunc tSysAuthObjFunc = new TSysAuthObjFunc();
            tSysAuthObjFunc.settSysFuncBase(tSysFuncBase);
            tSysAuthObjFunc.settSysAuthObj(tSysAuthObj);
            tSysAuthObjFunc.setCreateTime(new Date());

            Map<String, String> objParams = Maps.newHashMap();
            objParams.put("obj_id", tSysAuthObj.getId());
            objParams.put("func_code", functionCode);
            TSysAuthObjFunc relation = (TSysAuthObjFunc) sysAuthObjFuncService.query(objParams);
            if (null == relation) {
                sysAuthObjFuncService.store(tSysAuthObjFunc);
            }
        }
    }

    @Override
    public void saveDataSetRelationNew(TSysAuthObj tSysAuthObj, List<String> functionCodes) {
        for (String functionCode : functionCodes) {
            Map<String, String> params = Maps.newHashMap();
            params.put("func_code", functionCode);
            TSysFuncBase tSysFuncBase = (TSysFuncBase) dfwBaseDao.get(TSysFunc.class,functionCode);
            TSysAuthObjFunc tSysAuthObjFunc = new TSysAuthObjFunc();
            tSysAuthObjFunc.settSysFuncBase(tSysFuncBase);
            tSysAuthObjFunc.settSysAuthObj(tSysAuthObj);
            tSysAuthObjFunc.setCreateTime(new Date());

            Map<String, String> objParams = Maps.newHashMap();
            objParams.put("obj_id", tSysAuthObj.getId());
            objParams.put("func_code", functionCode);
            TSysAuthObjFunc relation = (TSysAuthObjFunc) sysAuthObjFuncService.query(objParams);
            if (null == relation) {
                sysAuthObjFuncService.store(tSysAuthObjFunc);
            }
        }
    }

    @Override
    public void saveFirstAuthNew(DataSetAuthVo dataSetAuthVo) {
        this.saveManyToManyDataSetRelationNew(dataSetAuthVo);
    }
}
