package com.dragonsoft.cicada.datacenter.modules.metadata.service;

/**
 * <AUTHOR> Avizii
 * @Create : 2021.03.29
 */
public class DbTypeValidator {

    public static boolean isRdb(String dbType) {
        return DataBaseFactory.RDB_LIST.contains(dbType);
    }

    public static boolean isEs(String dbType) {
        return dbType.equalsIgnoreCase(DataBaseFactory.DataSourceEnum.ELASTICSEARCH.name());
    }

    public static boolean haveZooKeeper(String dbType) {
        return DataBaseFactory.DataSourceEnum.HBASE.name().equalsIgnoreCase(dbType);
    }

}
