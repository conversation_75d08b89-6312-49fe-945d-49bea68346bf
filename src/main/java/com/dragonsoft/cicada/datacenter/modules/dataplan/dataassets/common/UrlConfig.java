package com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.common;

import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.vo.ElementUrlVO;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

import java.util.List;

@Data
@Component
@PropertySource(value = "classpath:/business-element.properties",encoding = "UTF-8")
@ConfigurationProperties(prefix = "urlConfig")
public class UrlConfig {

    /**
     * 要素类型字段（人）
     */
    private List<String> personStrList;

    /**
     * 要素类型字段（车）
     */
    private List<String> carStrList;

    /**
     * 要素类型字段（案件）
     */
    private List<String> ajStrList;

    /**
     * 人字段,数据穿透url集合
     */
    private List<ElementUrlVO> person;

    /**
     * 车字段,数据穿透url集合
     */
    private List<ElementUrlVO> car;

    /**
     * 案件字段,数据穿透url集合
     */
    private List<ElementUrlVO> aj;


    /**
     * 模型市场分类
     */
    private List<String> mxscfxList;
}
