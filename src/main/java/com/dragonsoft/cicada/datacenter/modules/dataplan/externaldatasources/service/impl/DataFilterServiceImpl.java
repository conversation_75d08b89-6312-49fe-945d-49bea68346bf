package com.dragonsoft.cicada.datacenter.modules.dataplan.externaldatasources.service.impl;


import com.code.meta.dml.standard.IMultCdin;
import com.code.meta.dml.standard.StandardQuery;
import com.code.meta.dml.standard.cdins.MultCdin;
import com.code.meta.dml.standard.cdins.QueryCdins;
import com.dragonsoft.cicada.datacenter.modules.dataplan.externaldatasources.service.IDataFilterService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.externaldatasources.vo.datawarehouse.ReleaseOutParamVo;
import com.dragonsoft.cicada.datacenter.modules.datavisual.model.AbsConditionWidget;
import com.dragonsoft.cicada.datacenter.modules.datavisual.model.AbsWidgetFilter;
import com.dragonsoft.cicada.datacenter.modules.datavisual.widget.IWidgetFilterBuilder;
import com.google.common.base.Preconditions;
import com.google.common.base.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.StringJoiner;

@Service
public class DataFilterServiceImpl implements IDataFilterService {
    @Autowired
    IWidgetFilterBuilder widgetFilterBuilder;

    private final String REPLACE_VAL = " SELECT %s ";

    private final String REPLACE_SOR = "SELECT";

    private final String rdbType = "RdbDataObj";


    protected IMultCdin getCondition(QueryCdins queryCdins, List<AbsWidgetFilter> filters, List<AbsConditionWidget> selectFilter) {
        IMultCdin all = new MultCdin();
        IMultCdin filter = new MultCdin();
        IMultCdin condition = new MultCdin();
        if (null != filters && !filters.isEmpty()) {
            filters.forEach(w -> {
                IMultCdin m = w.builderCondition(queryCdins);
                if (!m.isEmpty()) {
                    filter.addCdin(m);
                }
            });
        }

        if (!filter.isEmpty()) {
            all.addCdin(filter);
        }
        if (!condition.isEmpty()) {
            all.addCdin(condition);
        }
        return all;
    }


    @Override
    public String getQueryExp(String dbType, String tableName, String filterJson, List<ReleaseOutParamVo> selectModel) {
        Preconditions.checkNotNull(tableName, "表名不能为空");
        Preconditions.checkNotNull(dbType, "数据库类型不能为空");
        List<AbsWidgetFilter> filters = widgetFilterBuilder.builder(filterJson);
        StandardQuery query = new StandardQuery(dbType);

        if ("elasticsearch".equals(dbType.toLowerCase())) {
            query.setSqlTYpe(StandardQuery.type.ES);
        }
        QueryCdins queryCdins = query.getQueryCdins();
        query.setTableName(tableName);
        IMultCdin filterCondition = this.getCondition(queryCdins, filters, null);
        query.setConditions(filterCondition);

        String sql = query.toExpression().getScript();
        return replaceSQL(dbType, sql, selectModel);
    }

    @Override
    public String getVercationQueryExp(String dbType, String tableName, String filterJson, List<ReleaseOutParamVo> selectModel) {
        Preconditions.checkNotNull(tableName, "表名不能为空");
        Preconditions.checkNotNull(dbType, "数据库类型不能为空");
        List<AbsWidgetFilter> filters = widgetFilterBuilder.builder(filterJson);
        StandardQuery query = new StandardQuery(dbType);

        if ("elasticsearch".equals(dbType.toLowerCase())) {
            query.setSqlTYpe(StandardQuery.type.ES);
        }
        QueryCdins queryCdins = query.getQueryCdins();
        query.setTableName(tableName);
        IMultCdin filterCondition = this.getCondition(queryCdins, filters, null);
        query.setConditions(filterCondition);

        String sql = query.toExpression().getScript();
        return replaceVercationSQL(dbType, sql, selectModel);
    }

    private String replaceSQL(String dbType, String sql, List<ReleaseOutParamVo> releaseOutParamVos) {
        String replaceStr;
        if (null == releaseOutParamVos || releaseOutParamVos.isEmpty()) {
            replaceStr = String.format(REPLACE_VAL, "*");
        } else {
            StringJoiner sj = new StringJoiner(",");
            for (ReleaseOutParamVo releaseOutParamVo : releaseOutParamVos) {
                String fileCode = releaseOutParamVo.getFiledCode();
                if (!Strings.isNullOrEmpty(fileCode)) {
                    if (!"elasticsearch".equals(dbType.toLowerCase())) {
                        sj.add(releaseOutParamVo.getFiledName() +" AS "+ releaseOutParamVo.getFiledCode());
                    } else {
                        sj.add("_MAP['" + releaseOutParamVo.getFiledCode() + "']");
                    }
                }
            }
            replaceStr = String.format(REPLACE_VAL, sj.toString());
        }
        return sql.replaceAll(REPLACE_SOR, replaceStr);
    }

    private String replaceVercationSQL(String dbType, String sql, List<ReleaseOutParamVo> releaseOutParamVos) {
        String replaceStr;
        if (null == releaseOutParamVos || releaseOutParamVos.isEmpty()) {
            replaceStr = String.format(REPLACE_VAL, "*");
        } else {
            String sj = "";
            for (ReleaseOutParamVo releaseOutParamVo : releaseOutParamVos) {
                String fileCode = releaseOutParamVo.getFiledCode();
                if (!Strings.isNullOrEmpty(fileCode)) {
                    if (!"elasticsearch".equals(dbType.toLowerCase())) {
                        sj+="count(1) AS "+fileCode;
                    } else {
                        sj+="_MAP[count(1) as '" + releaseOutParamVo.getFiledCode() + "']";
                    }
                }
            }
            replaceStr = String.format(REPLACE_VAL, sj);
        }
        return sql.replaceAll(REPLACE_SOR, replaceStr);
    }


}
