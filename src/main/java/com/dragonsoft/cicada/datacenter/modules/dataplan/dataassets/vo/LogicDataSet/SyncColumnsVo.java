package com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.vo.LogicDataSet;

import com.code.metaservice.ddl.vo.LogicHttpColumns;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
@Data
public class SyncColumnsVo {
    private List<LogicHttpColumns>  addColumns = new ArrayList<>();
    private List<LogicHttpColumns>  deleteColumns = new ArrayList<>();
    private List<LogicHttpColumns>  editColumns = new ArrayList<>();
}
