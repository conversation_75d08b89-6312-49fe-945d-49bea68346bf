package com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.adapter;

import java.util.Arrays;

public class FloatAdapter extends AbstractTypeAdapter<String> {

    //存放属于浮点型的类型
    {
        types.add("FLOAT");
        types.add("BIGDECIMAL");
        String[] list = {"DOUBLE","BIGDECIMAL","NUMBER","DECIMAL","NUMERIC","FLOAT4","FLOAT8","SERIAL4","SERIAL8","NUMBER","BINARY_FLOAT","BINARY_DOUBLE","SERIAL","SERIAL8","SMALLFLOAT","BIGSERIAL","REAL","DEC","DECIMAL","DOUBLE PRECISION","FLOAT","HALF_FLOAT"};
        types.addAll(Arrays.asList(list));
    }

    @Override
    public String handler() {
        return "Double"; //返回转换处理过后的type
    }

    @Override
    public String getHanlderResult() {
        return "浮点型";
    }
}
