package com.dragonsoft.cicada.datacenter.modules.firstpage.share.service;

import com.code.common.paging.PageInfo;
import com.code.metaservice.datawarehouse.model.DatasetTreeModel;
import com.dragoninfo.dfw.entity.TSysAuthObj;
import com.dragonsoft.cicada.datacenter.modules.firstpage.share.vo.ResultResource;
import com.dragonsoft.cicada.datacenter.modules.firstpage.share.vo.ResultUserVo;
import com.dragonsoft.cicada.datacenter.modules.firstpage.share.vo.ShareVo;

import java.util.List;

public interface ShareService {

    /**
     * 来自分享
     * @param fromShareVo 来自分享信息
     * @param userId  我的id
     * @return
     */
    PageInfo getSharesByCondition(ShareVo fromShareVo, String userId);

    /**
     * 获取其他用户或角色
     * @param myUserId
     * @param objType
     * @return
     */
    List<ResultUserVo> getOtherUsers(String myUserId,String objType);

    /**
     * 我分享的
     * @param shareVo
     * @param myUserId
     * @return
     */
    PageInfo getMyShares(ShareVo shareVo,String myUserId);

    /**
     * 添加分享
     * @param shareVo
     * @param myUserId
     */
    void addShares(ShareVo shareVo,String myUserId);

    /**
     * 获取我拥有的资源
     * @param shareVo
     * @param myUserId
     * @return
     */
    List<ResultResource> getResourceByCondition(ShareVo shareVo,String myUserId);

    /**
     * 取消分享
     * @param shareVo
     */
    void cancelShare(ShareVo shareVo);

    List<DatasetTreeModel> getMyDataSetResource(String userId);

    List<TSysAuthObj> getObjsByResourceId(String resourceId);


}
