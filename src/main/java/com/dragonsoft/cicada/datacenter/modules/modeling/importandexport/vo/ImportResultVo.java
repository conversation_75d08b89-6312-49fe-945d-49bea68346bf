package com.dragonsoft.cicada.datacenter.modules.modeling.importandexport.vo;

import com.code.metadata.business.directory.BaseBusiClassify;
import com.dragonsoft.cicada.datacenter.modules.modeling.importandexport.controller.DataTransImportExportController;
import com.google.common.collect.Lists;
import lombok.Data;

import java.util.List;
@Data
public class ImportResultVo {
    //方案目录，还有其他目录，名字暂时不改
    List<BaseBusiClassify> transClassify = Lists.newArrayList();
    //方案跟目录的关系
    List<DataTransImportExportController.ClassifyElement> transClassifyRelation = Lists.newArrayList();

    private String importType ="TRANS";
    //方案
    List<ImportResulObjVo> transMetas = Lists.newArrayList();

    //数据集
    List<ImportResulObjVo> logicInfos = Lists.newArrayList();

    //数据源
    List<ImportResulObjVo> dwInstances = Lists.newArrayList();

    //模型服务
    List<ImportResulObjVo> serviceApis = Lists.newArrayList();

    //用例管理
    List<ImportResulObjVo> useCases = Lists.newArrayList();

    public void setTransClassify(List<BaseBusiClassify> transClassify) {
        List<BaseBusiClassify> baseBusiClassifyLists = transClassify;
        for(BaseBusiClassify baseBusiClassify : transClassify){
            baseBusiClassify.setElements(null);
            baseBusiClassify.setBusiDir(null);
        }
        this.transClassify = baseBusiClassifyLists;
    }

    public void setTransClassifyRelation(List<DataTransImportExportController.ClassifyElement> transClassifyRelation) {
        this.transClassifyRelation = Lists.newArrayList();
        List<String> str = Lists.newArrayList();
        for(DataTransImportExportController.ClassifyElement classifyElement: transClassifyRelation){
            if(!str.contains(classifyElement.getElementId() + classifyElement.getBusiClassifyId())){
                this.transClassifyRelation.add(classifyElement);
                str.add(classifyElement.getElementId() + classifyElement.getBusiClassifyId());
            }
        }
    }
}
