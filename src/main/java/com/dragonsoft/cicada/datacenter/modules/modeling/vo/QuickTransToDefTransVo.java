package com.dragonsoft.cicada.datacenter.modules.modeling.vo;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2021/08/06/上午11:37
 *
 */
public class QuickTransToDefTransVo {

    /**
     * 方案id
     */
    private String transId;

    /**
     * 需要更新x,Y轴信息的插件
     */
    private List<QuickTrans> plugins;

    private String busiClassifyId;

    public String getBusiClassifyId() {
        return busiClassifyId;
    }

    public void setBusiClassifyId(String busiClassifyId) {
        this.busiClassifyId = busiClassifyId;
    }

    public String getTransId() {
        return transId;
    }

    public void setTransId(String transId) {
        this.transId = transId;
    }

    public List<QuickTrans> getPlugins() {
        return plugins;
    }

    public void setPlugins(List<QuickTrans> plugins) {
        this.plugins = plugins;
    }

    public static class QuickTrans{

        /**
         * 插件id
         */
        private String pluginId;

        /**
         * 插件x
         */
        private int x;

        /**
         * 插件y
         */
        private int y;

        public String getPluginId() {
            return pluginId;
        }

        public void setPluginId(String pluginId) {
            this.pluginId = pluginId;
        }

        public int getX() {
            return x;
        }

        public void setX(int x) {
            this.x = x;
        }

        public int getY() {
            return y;
        }

        public void setY(int y) {
            this.y = y;
        }
    }


}
