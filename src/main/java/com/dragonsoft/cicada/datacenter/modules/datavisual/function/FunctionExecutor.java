package com.dragonsoft.cicada.datacenter.modules.datavisual.function;

import com.code.dragonsoft.dataquery.service.querymodel.ColumnDataModel;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR> Jiebin
 * @date 2020-12-30 11:14
 */
@Service
public class FunctionExecutor {

    private final FunctionManager manager = new FunctionManager();

    public ColumnDataModel execute(ColumnDataModel dates, ChartConfig config, String... functions) {
        return this.getFunction(functions).calculate(dates, config);
    }

    public void addFunction(MemoryFunction function) {
        manager.addFunction(function);
    }

    public MemoryFunction getFunction(String...functionNames) {
        return manager.getFunction(functionNames);
    }

    public Map<String, String> getFunctionList() {
        return manager.getFunctionList();
    }
}
