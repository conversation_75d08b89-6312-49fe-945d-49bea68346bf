update t_sys_func set func_name ='删除' where func_code ='serviceManagementUninstallg';
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('serviceManagementMove', '0', '移动到', 'serviceManagement', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1');



INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('2b4ffe0bdyuiadfgi56gre3c1c46', '5b2ef29d86f244e69af0977acee7555f', 'serviceManagementMove', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('b6gregerhgerwerfer177e506a9f', 'd6121bc4248e45019942e2cb78362500', 'serviceManagementMove', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
