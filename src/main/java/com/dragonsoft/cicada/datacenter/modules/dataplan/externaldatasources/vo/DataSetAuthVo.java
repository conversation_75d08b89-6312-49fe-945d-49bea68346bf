package com.dragonsoft.cicada.datacenter.modules.dataplan.externaldatasources.vo;

import com.dragonsoft.cicada.datacenter.modules.system.permissions.vo.RoleVo;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.vo.UserVo;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/6/9
 */
@Data
public class DataSetAuthVo {
    /**
     * 被授权数据集
     */
    List<DataObjectVo> dataObjectVos;
    /**
     * 授权角色
     */
    List<RoleVo> roleVos;
    /**
     * 授权用户
     */
    List<UserVo> userVos;
    /**
     * 被授权数据集的数据源id
     */
   String dwId;

   String type;
}
