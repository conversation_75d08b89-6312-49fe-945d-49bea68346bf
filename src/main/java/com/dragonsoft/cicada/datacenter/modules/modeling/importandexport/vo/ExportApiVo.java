package com.dragonsoft.cicada.datacenter.modules.modeling.importandexport.vo;

import lombok.Data;

import java.util.List;

@Data
public class ExportApiVo {

    private String exportResourceType;//api，测试用例

    private String exportScopeType;//全部，指定资源，指定目录

    private List<String> specifiedApiServiceIdList;//指定模型，方案得集合

    private List<String> specifiedApiClassifyIdList;//指定目录时id得集合

    private String exportContentType;//仅导出api，导出api依赖得资源，导出关联得测试用例

    private String exportContentCaseType;//仅导出用例，包含api

    private String userId;//导出得用户id

}
