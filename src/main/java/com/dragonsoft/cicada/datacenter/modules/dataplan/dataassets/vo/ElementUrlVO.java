package com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "数据穿透转跳地址")
public class ElementUrlVO {

    @ApiModelProperty(value = "地址名称")
    private String name;

    @ApiModelProperty(value = "地址url")
    private String url;

    @ApiModelProperty(value = "标识 person 人 car 车 case 案件")
    private String mark;



}
