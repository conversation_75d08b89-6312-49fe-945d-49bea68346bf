package com.dragonsoft.cicada.datacenter.modules.system.permissions.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/5/22
 */
@Data
@ApiModel(value="用户组对象模型")
public class UserGroupsVo {
    @ApiModelProperty(value="数据库唯一标识ID" ,required=true)
    private String id;
    /**
     * 页面上的ID
     */
    @ApiModelProperty(value="code,页面上的ID" ,required=true)
    private String code;
    @ApiModelProperty(value="用户组名称" ,required=true)
    private String name;
    @ApiModelProperty(value="父用户组ID" ,required=true)
    private String parent;
    @ApiModelProperty(value="编辑人" ,required=true)
    private String Editor;

}
