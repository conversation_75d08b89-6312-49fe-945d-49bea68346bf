package com.dragonsoft.cicada.datacenter.modules.datavisual.model;

import com.code.metadata.datavisual.Widget;
import com.dragonsoft.cicada.datacenter.modules.datavisual.model.annotation.WidgetConfig;
import com.dragonsoft.cicada.datacenter.modules.datavisual.model.annotation.WidgetLabel;
import com.dragonsoft.cicada.datacenter.modules.datavisual.model.enums.WidgetType;
import org.springframework.stereotype.Component;

@Component
@WidgetLabel(name = "文本", type = WidgetType.SMALL, orderBy = 1,describe = "其他小控件")
public class TextWidget extends Widget {
    @WidgetConfig
    String content;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
