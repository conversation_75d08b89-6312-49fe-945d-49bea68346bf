package com.dragonsoft.cicada.datacenter.modules.datavisual.function;


import com.code.metadata.model.core.DataType;

/**
 * <AUTHOR> Jiebin
 * @date 2020-12-30 9:31
 */
public class ColumnMeta {
    private String name;
    private String code;
    private String[] columnFunctions;
    private DataType dataType;
    private String timeFormat;
    private String dateGranularity;
    private boolean isQueryColumnl;

    public ColumnMeta(String name, String code, String[] columnFunctions, DataType dataType) {
        this.name = name;
        this.code = code;
        this.columnFunctions = columnFunctions;
        this.dataType = dataType;
    }

    public boolean isQueryColumnl() {
        return isQueryColumnl;
    }

    public void setQueryColumnl(boolean queryColumnl) {
        isQueryColumnl = queryColumnl;
    }

    public ColumnMeta(String name, String code, DataType dataType, String timeFormat, String dateGranularity) {
        this.name = name;
        this.code = code;
        this.dataType = dataType;
        this.dateGranularity = dateGranularity;
        this.timeFormat = timeFormat;
    }

    public String getTimeFormat() {
        return timeFormat;
    }

    public void setTimeFormat(String timeFormat) {
        this.timeFormat = timeFormat;
    }

    public String getDateGranularity() {
        return dateGranularity;
    }

    public void setDateGranularity(String dateGranularity) {
        this.dateGranularity = dateGranularity;
    }

    public String[] getColumnFunctions() {
        return columnFunctions;
    }

    public void setColumnFunctions(String[] columnFunctions) {
        this.columnFunctions = columnFunctions;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public DataType getDataType() {
        return dataType;
    }

    public void setDataType(DataType dataType) {
        this.dataType = dataType;
    }
}
