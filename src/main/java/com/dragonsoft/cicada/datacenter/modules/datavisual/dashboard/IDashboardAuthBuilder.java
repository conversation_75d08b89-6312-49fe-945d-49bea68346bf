package com.dragonsoft.cicada.datacenter.modules.datavisual.dashboard;

import com.dragonsoft.cicada.datacenter.modules.datavisual.vo.DashboardAuthVo;

public interface IDashboardAuthBuilder {

    /**
     * 添加仪表盘对象到功能表
     */
    void saveDashboardAuthRegister(DashboardAuthVo dashboardAuthVo, String userId);

    /**
     * 添加仪表盘授权
     */
    void addDashboardAuth(DashboardAuthVo dashboardAuthVo);

    /**
     * 查询用户是否有仪表盘权限
     */
    boolean hasDashboardAuth(String dashboardId, String userId);
}
