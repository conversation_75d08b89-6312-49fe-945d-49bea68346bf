package com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.vo.LogicDataSet;

import com.code.dataset.operator.filter.FilterConditionStep;
import com.code.dataset.operator.join.JoinTableStep;
import com.code.metaservice.ddl.vo.LogicDataSetColumnVo;
import lombok.Data;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/10/12 16:41
 */
@Data
public class SelfHelpDataSetPreviewByStepVo {
    private int page;
    private int pageSize;
    /**
     * 库类型
     */
    private String dbType;
    /**
     * 页面上任意一个表id
     */
    private String classifyStatId;
    /**
     * 字段列表
     */
    private List<LogicDataSetColumnVo> columns;
    /**
     * join配置
     */
    private List<JoinTableStep> dataSetJoinVo;
    /**
     * 过滤条件
     */
    FilterConditionStep filterConditionStep;

    String userId;
}
