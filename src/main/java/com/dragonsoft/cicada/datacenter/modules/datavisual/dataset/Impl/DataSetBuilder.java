package com.dragonsoft.cicada.datacenter.modules.datavisual.dataset.Impl;

import com.code.common.utils.StringUtils;
import com.code.metadata.business.dataobject.DmcDataColumns;
import com.code.metadata.business.dataobject.DmcDataObject;
import com.code.metadata.datavisual.DataSet;
import com.code.metadata.datavisual.Field;
import com.code.metadata.model.core.StructuralFeature;
import com.code.metadata.res.common.ClassifierStat;
import com.code.metaservice.datavisual.IDatasetService;
import com.code.metaservice.datawarehouse.IDataWareTreeService;
import com.code.metaservice.res.common.ClassifierStatService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.externaldatasources.service.IDataWarehousePlanService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.externaldatasources.vo.datawarehouse.ElasticColumnVo;
import com.dragonsoft.cicada.datacenter.modules.datavisual.dataset.DataSetFilter;
import com.dragonsoft.cicada.datacenter.modules.datavisual.dataset.IDataSetBuilder;
import com.dragonsoft.cicada.datacenter.modules.datavisual.model.enums.FiledJSTypeEnum;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Component
@Slf4j
public class DataSetBuilder implements IDataSetBuilder {

    @Autowired
    ClassifierStatService classifierStatService;
    @Autowired
    IDatasetService datasetService;
    @Autowired
    IDataWareTreeService dataWareTreeService;
    @Autowired
    IDataWarehousePlanService dataWarehouseService;

    private List<DataSet> getSelfHelpDataSet() {
        List<DataSet> dataSets = new ArrayList<>();
        List<DmcDataObject> dmcDataObjects = datasetService.getDmcDataObjs();
        List<String> ids = dmcDataObjects.stream().map(DmcDataObject::getId).collect(Collectors.toList());
        Map<String, List<DmcDataColumns>> columnsMap = datasetService.getDmcDataColumns(ids);
        dmcDataObjects.stream().forEach(d -> {
            try {
                ClassifierStat c = d.getClassifierStat();
                DataSet dataSet = new DataSet();
                dataSet.setDataType(DataSet.DataType.CUSTOM);
                dataSet.setId(c.getId());
                dataSet.setDataSourceId(c.getOwnerId());
                String tempName = "";
                if(StringUtils.isBlank(c.getName())){
                    tempName = c.getCode();
                }else{
                    tempName = c.getCode()+"("+c.getName()+")";
                }
                dataSet.setName(tempName);

             dataSet.setDbType(c.getDbType());
            String code = "";
            if (c.getCode().contains(".")) {
                code = c.getCode().split("\\.")[1];
            } else {
                code = c.getCode();
            }
            dataSet.setDataModel("自助建模");
            dataSet.setCode(code);
            List<Field> fields = new ArrayList<>();
            List<DmcDataColumns> dataColumns = columnsMap.get(d.getId());
            dataColumns.stream().forEach(cm -> {
                StructuralFeature s = cm.getStructuralFeature();
                Field f = new Field();
                f.setId(s.getId());
                String name;
                if (StringUtils.isBlank(s.getName())) {
                    name = s.getCode();
                } else {
                    name = s.getName();
                }
                f.setName(name);
                f.setCode(s.getCode());
                f.setType(getFieldType(s.getDataType().getCode()));
                f.setDBType(s.getDataType().getCode());
                String jsType = getJsType(s.getDataType().getCode());
                f.setJsType(jsType);
                if (!Strings.isNullOrEmpty(jsType)) {
                    fields.add(f);
                }
                });
                dataSet.setFields(fields);
                dataSets.add(dataSet);
            } catch (Exception e) {
                log.error(e.getMessage(),e);
            }
        });
        return dataSets;
    }

    private List<DataSet> getDataWarehouse() {

        //获取所有的表
        List<Map<String, String>> tables = dataWarehouseService.getAllTableMapping();
        List<Map<String,String>> tableMappings = Lists.newArrayList();
        for (Map<String, String> table : tables) {
            if (!"hbase".equals(table.get("db_type"))) {
                if ("elasticsearch".equalsIgnoreCase(table.get("db_type"))) {
                    String esCode = table.get("code");
                    table.put("code", esCode.contains("/") ? esCode.substring(0, esCode.indexOf("/")) : esCode);
                }
                tableMappings.add(table);
            }
        }
        List<String> tableIds = Lists.newArrayList();
        for (Map map : tableMappings) {
            tableIds.add(String.valueOf(map.get("id")));
        }
        ;
        List<ElasticColumnVo> elasticColumnVos = dataWarehouseService.getAllColumn(tableIds);
        Map<String, List<ElasticColumnVo>> columnGroupMap = elasticColumnVos.stream().collect(Collectors.groupingBy(ElasticColumnVo::getOwnerId));

        return this.builderDataWarehouseDataSet(tableMappings, columnGroupMap);
    }

    private List<DataSet> builderDataWarehouseDataSet(List<Map<String, String>> tableMappings, Map<String, List<ElasticColumnVo>> columnGroupMap) {
        List<DataSet> dataSets = Lists.newArrayList();
        //挂在数仓数据源的表
        for (Map<String, String> map : tableMappings) {
            DataSet mappingTable = new DataSet();
            mappingTable.setDataType(DataSet.DataType.DW);
            mappingTable.setId(map.get("dwid"));
            mappingTable.setClassifierStatId(map.get("id"));
            mappingTable.setDbType(map.get("db_type"));
            mappingTable.setCode(map.get("code"));
            String name = "";
            if (StringUtils.isBlank(map.get("name"))) {
                name = map.get("code");
            } else {
                name = map.get("code")+"("+map.get("name")+")";
            }
            mappingTable.setName(name);
            mappingTable.setDataModel("数仓规划:"+map.get("dataname"));
            mappingTable.setDataSourceId(map.get("owner_id"));
            if (!"hbase".equals(mappingTable.getDbType())) {
                mappingTable.setFields(this.getFields(columnGroupMap, map.get("id")));
            }
            dataSets.add(mappingTable);
        }
        return dataSets;
    }

    private List<Field> getFields(Map<String, List<ElasticColumnVo>> columnGroupMap, String tableId) {

//        List<ElasticColumnVo> fields = dataWarehouseService.queryElasticsColumns(null, "", "", "", tableId, dbType);
        List<ElasticColumnVo> fields = columnGroupMap.get(tableId);
        List<Field> resField = Lists.newArrayList();
        if (fields != null) {
            for (ElasticColumnVo v : fields) {
                Field f = new Field();
                f.setCode(v.getCode());
                // es同步_id字段 不应该做为可视化参数
                if ("_id".equals(v.getCode())) {
                    continue;
                }
                f.setDBType(v.getDataType());
                f.setName(v.getName());
                if (StringUtils.isBlank(f.getName())) {
                    f.setName(v.getCode());
                }
                f.setId(v.getId());
                f.setType(getFieldType(v.getDataType()));
                String jsType = getJsType(v.getDataType());
                f.setJsType(jsType);
                if (!Strings.isNullOrEmpty(jsType)) {
                    resField.add(f);
                }
            }
        }

        return resField;
    }

    @Override
    @Transactional
    public List<DataSet> builderDataSet() {

        List<DataSet> resList = this.getSelfHelpDataSet();
//        resList.addAll(this.getSQLDataSet());
        resList.addAll(this.getDataWarehouse());
        return DataSetFilter.filterEsText(resList);
    }

    private int getFieldType(String code) {
        for (String s : Arrays.asList(FiledJSTypeEnum.NUMBER.getDataBaseTypes())) {
            if (null != code) {
                code = code.toUpperCase();
            }
            if (s.equals(code)) {
                return Field.MEASURES;
            }
        }
        return Field.DIM_TYPE;

    }

    private String getJsType(String code) {
        List<FiledJSTypeEnum> jsTypeEnums = Arrays.asList(FiledJSTypeEnum.values());
        for (FiledJSTypeEnum f : jsTypeEnums) {
            for (String ft : f.getDataBaseTypes()) {
                if (null != code) {
                    code = code.toUpperCase();
                }
                if (ft.equals(code)) {
                    return f.getCode();
                }

            }

        }
        return null;
    }



}
