package com.dragonsoft.cicada.datacenter.modules.dataplan.apitest.vo;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Data
public class UseCaseVo {

    private String useCaseId;
    private String useCaseName;
    private String classifyId;
    private String memo;

    private String caseType;

    private List<UseCaseApiVo> useCaseApis = new ArrayList<>();

    @Data
    public static class UseCaseApiVo {
        private String servicePublicationId;
        private String apiType;//接口类型 postRequest:发送请求  getValue:获取结果

        private String requestUrl;
        private String apiName;

        private String apiStatus;
        private String serviceType;

        private List<UseCaseApiParam> useCaseApiParams = new ArrayList<>();
    }


    @Data
    public static class UseCaseApiParam {

        private String parentParamCode;


        private List<Map<String,Object>> params = new ArrayList<>();
    }

}
