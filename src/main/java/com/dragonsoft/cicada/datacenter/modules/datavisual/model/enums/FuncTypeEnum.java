package com.dragonsoft.cicada.datacenter.modules.datavisual.model.enums;

/**
 * <AUTHOR>
 * @Date 2021/3/9 11:07
 */
public enum FuncTypeEnum {
    NONE(""),
    SUM("总和"),
    COUNT("计数"),
    <PERSON><PERSON>("平均值"),
    MAX("最大值"),
    MIN("最小值");
    private String name;

    FuncTypeEnum(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }


    public static String getNameByCode(String code) {
        FuncTypeEnum anEnum = FuncTypeEnum.valueOf(code.toUpperCase());
        return anEnum.getName();
    }
}
