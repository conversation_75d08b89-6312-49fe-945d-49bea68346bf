package com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.adapter;

import java.util.ArrayList;
import java.util.List;

public class SupportHandler {

    List<TypesAdapter> types = new ArrayList<>();

    {
        types.add(new CharacterAdapter());
        types.add(new IntegerAdapter());
        types.add(new FloatAdapter());
        types.add(new DateAdapter());
    }

    public TypesAdapter getAdapter(String type) {
        for (TypesAdapter typesAdapter : types) {  //职责搜索适合的适配器
            if(typesAdapter.support(type)) {
                return typesAdapter;
            }
        }
        return null;
    }

}
