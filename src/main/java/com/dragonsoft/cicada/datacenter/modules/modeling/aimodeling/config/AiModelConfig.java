package com.dragonsoft.cicada.datacenter.modules.modeling.aimodeling.config;

import com.dragonsoft.cicada.datacenter.modules.modeling.aimodeling.client.IJypyterCodeClient;
import com.dragonsoft.cicada.datacenter.modules.modeling.aimodeling.client.JupyterCodeClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class AiModelConfig {

    @Autowired
    private AiModelIpAndPort aiModelIpAndPort;

    @Bean
    public IJypyterCodeClient jypyterCodeClient(){
        return new JupyterCodeClient(
                aiModelIpAndPort.getInitBuildServiceIp(),
                aiModelIpAndPort.getStartAndSaveServiceIp(),
                aiModelIpAndPort.getRunScriptServiceIp(),
                aiModelIpAndPort.getEvaluateRstIp(),
                aiModelIpAndPort.getRunOpServiceIp(),
                aiModelIpAndPort.getServiceReleaseIp()
        );
    }
}
