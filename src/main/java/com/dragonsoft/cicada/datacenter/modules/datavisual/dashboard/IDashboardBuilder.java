package com.dragonsoft.cicada.datacenter.modules.datavisual.dashboard;

import com.code.common.paging.PageInfo;
import com.code.metadata.datavisual.Dashboard;
import com.dragoninfo.dfw.bean.Result;

import java.util.List;
import java.util.Map;

public interface IDashboardBuilder {

    List<Dashboard> builderList(String group, String keyWord);

    Dashboard builder(String id);

    void configScheduled(Dashboard dashboard,String taskCode,String taskJson,boolean taskType);

    String saveOrUpdate(Dashboard dashboard);

    PageInfo builderListForPage(String groupId, String keyWord, int index, int pageSize, String userId);

    /**
     * 获取目录下所有方案
     * @param groupId
     * @param userId
     * @return
     */
    List<Dashboard> getListAll(String groupId, String userId);

    void delete(String id);

    void copy(String id,String dirId,String dashboardName,String userId);

    void copyDashboard(Dashboard dashboard, String groupId);

    List<Map<String, Object>> getLinkageDashboard(String id);

    Result getDashboardAllByUserId(Map<String, Object> queryMap);
}
