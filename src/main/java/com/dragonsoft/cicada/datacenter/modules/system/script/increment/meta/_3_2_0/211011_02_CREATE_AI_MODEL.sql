CREATE TABLE "t_script_info" (
  "model_desc" text COLLATE "pg_catalog"."default",
  "script" text COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "update_time" timestamp(6),
  "src_template_id" char(1) COLLATE "pg_catalog"."default"
)
INHERITS ("t_md_element")
;
ALTER TABLE "t_script_info" OWNER TO "postgres";
COMMENT ON COLUMN "t_script_info"."id" IS '脚本id，主键';
COMMENT ON COLUMN "t_script_info"."name" IS '模型名称';
COMMENT ON COLUMN "t_script_info"."model_desc" IS '模型描述';
COMMENT ON COLUMN "t_script_info"."script" IS '脚本';
COMMENT ON COLUMN "t_script_info"."create_time" IS '创建时间';
COMMENT ON COLUMN "t_script_info"."update_time" IS '更新时间';
COMMENT ON COLUMN "t_script_info"."src_template_id" IS '来源模板id';


CREATE TABLE "t_script_log" (
  "id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
  "script_id" varchar(32) COLLATE "pg_catalog"."default",
  "execute_status_code" varchar(100) COLLATE "pg_catalog"."default",
  "execute_status_name" varchar(100) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "update_time" timestamp(6)
)
;
ALTER TABLE "t_script_log" OWNER TO "postgres";
COMMENT ON COLUMN "t_script_log"."id" IS '日志id';
COMMENT ON COLUMN "t_script_log"."script_id" IS '脚本id';
COMMENT ON COLUMN "t_script_log"."execute_status_code" IS '执行状态代码';
COMMENT ON COLUMN "t_script_log"."execute_status_name" IS '执行状态名称';
COMMENT ON COLUMN "t_script_log"."create_time" IS '创建时间';
COMMENT ON COLUMN "t_script_log"."update_time" IS '更新时间';

-- ----------------------------
-- Primary Key structure for table t_script_log
-- ----------------------------
ALTER TABLE "t_script_log" ADD CONSTRAINT "pk_t_script_log" PRIMARY KEY ("id");


CREATE TABLE "t_script_log_detail" (
  "log_id" varchar(32) COLLATE "pg_catalog"."default",
  "log_type" varchar(100) COLLATE "pg_catalog"."default",
  "log_content" text COLLATE "pg_catalog"."default",
  "log_level" int4,
  "log_src_subject_name" varchar(100) COLLATE "pg_catalog"."default",
  "log_src_subject_code" varchar(100) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL
)
;
ALTER TABLE "t_script_log_detail" OWNER TO "postgres";
COMMENT ON COLUMN "t_script_log_detail"."log_id" IS '日志id';
COMMENT ON COLUMN "t_script_log_detail"."log_type" IS '日志明细类型';
COMMENT ON COLUMN "t_script_log_detail"."log_content" IS '日志明细内容';
COMMENT ON COLUMN "t_script_log_detail"."log_level" IS '日志明细层级';
COMMENT ON COLUMN "t_script_log_detail"."log_src_subject_name" IS '日志生成主体名称';
COMMENT ON COLUMN "t_script_log_detail"."log_src_subject_code" IS '日志生成主体code';

-- ----------------------------
-- Primary Key structure for table t_script_log_detail
-- ----------------------------
ALTER TABLE "t_script_log_detail" ADD CONSTRAINT "t_script_log_detail_pkey" PRIMARY KEY ("id");