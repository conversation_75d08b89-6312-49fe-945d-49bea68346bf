package com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.enums;

/**
 * <AUTHOR>
 * @Date: 2022/06/01/下午2:02
 */
public enum JoinOpEnum {

    INNERJOIN("交集","INNER JOIN"),

    LEFTJOIN("左合并","LEFT JOIN"),

    RIGHTJOIN("右合并","RIGHT JOIN"),

    FULLJOIN("左右合并","FULL JOIN"),

    LEFTOUTJOIN("左差集","LEFT OUTER JOIN"),

    RIGHTOUTJOIN("右差集","RIGHT OUTER JOIN");

    public String name;

    public String code;


    JoinOpEnum(String name, String code){
        this.name=name;
        this.code=code;
    }

    public static String getCodeByName(String name){
        JoinOpEnum[] values = JoinOpEnum.values();
        for (JoinOpEnum value : values) {
            if(value.name.equals(name)){
                return value.code;
            }
        }
        return null;
    }
}
