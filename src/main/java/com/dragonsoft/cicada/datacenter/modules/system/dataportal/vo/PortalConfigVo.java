package com.dragonsoft.cicada.datacenter.modules.system.dataportal.vo;

import com.dragonsoft.cicada.datacenter.modules.system.common.vo.TreeVo;
import com.google.common.collect.Lists;
import lombok.Data;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2020/9/01
 */
@Data
public class PortalConfigVo {

    private String configName;//配置名称

    private String portalId;//门户id

    private String portalConfigId;//门户配置Id

    private String portalName;//门户名称

    private String layoutScheme;//布局方案

    private String themeSettings;//主题设置

    private String logo;//门户logo

    private String portalAlias;//门户别名

    private Boolean isMenuCache;//是否缓存

    private String portalUrlParams;//门户链接参数

    private String defaultHomePage;//默认主页

    private String securityMode = "private";

    private List<PortalMenuTreeVo> portalMenuList = Lists.newArrayList();

    private List<TreeVo> deleteMenuList = Lists.newArrayList();

    public String getPublishUrl() {
        return this.getPortalAlias() + "/" + this.getPortalUrlParams();
    }
}
