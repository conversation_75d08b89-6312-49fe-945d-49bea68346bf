package com.dragonsoft.cicada.datacenter.modules.metadata.controller;

import com.code.common.encrypt.DragonEncryptor;
import com.code.common.paging.PageInfo;
import com.code.common.utils.R;
import com.code.common.utils.StringUtils;
import com.code.common.utils.assertion.Assert;
import com.code.dragonsoft.dataquery.service.QueryDataService;
import com.code.dragonsoft.dataquery.service.querymodel.ColumnDataModel;
import com.code.dragonsoft.dataquery.service.querymodel.ColumnPageInfo;
import com.code.dragonsoft.dataquery.service.querymodel.ParamDataModel;
import com.code.dragonsoft.dataquery.util.ChangeColumnNameUtil;
import com.code.metadata.model.core.StructuralFeature;
import com.code.metadata.res.common.ClassifierStat;
import com.code.metaservice.core.ClassifierService;
import com.code.metaservice.datawarehouse.IDataWareTreeService;
import com.code.metaservice.datawarehouse.model.DataSourceDTO;
import com.code.metaservice.datawarehouse.model.DataSourceVO;
import com.code.metaservice.datawarehouse.model.DatasetTreeModel;
import com.code.metaservice.datawarehouse.model.SchemaVo;
import com.code.metaservice.res.response.datasource.InsertDataSourceResponse;
import com.code.metaservice.res.response.vo.dataobject.DataObjectView;
import com.dragoninfo.dfw.annotation.FuncScanAnnotation;
import com.dragoninfo.dfw.annotation.ValidateAndLogAnnotation;
import com.dragoninfo.dfw.bean.Result;
import com.dragoninfo.dfw.entity.TSysAuthObjFunc;
import com.dragoninfo.dfw.entity.TSysFuncBase;
import com.dragoninfo.dfw.service.SysAuthObjFuncService;
import com.dragoninfo.dfw.service.SysAuthObjRelService;
import com.dragoninfo.dfw.service.SysAuthObjService;
import com.dragoninfo.dfw.service.SysFuncService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.service.IDataSetOperationService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.externaldatasources.service.IDataSetAuthService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.externaldatasources.service.IDataWarehousePlanService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.externaldatasources.vo.DataObjectVo;
import com.dragonsoft.cicada.datacenter.modules.dataplan.externaldatasources.vo.DataSetAuthVo;
import com.dragonsoft.cicada.datacenter.modules.dataplan.externaldatasources.vo.datawarehouse.DataWarehouseVo;
import com.dragonsoft.cicada.datacenter.modules.metadata.service.DataBaseConnectService;
import com.dragonsoft.cicada.datacenter.modules.metadata.service.DataBaseFactory;
import com.dragonsoft.cicada.datacenter.modules.metadata.service.DbTypeValidator;
import com.dragonsoft.cicada.datacenter.modules.metadata.service.MetadataService;
import com.dragonsoft.cicada.datacenter.modules.metadata.vo.*;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.service.IUserService;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.vo.UserVo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Wu.D.J
 * @Create : 2021.03.26
 */
@RestController
@CrossOrigin
@RequestMapping("/metadata")
@PropertySource("classpath:case-config.properties")
@FuncScanAnnotation(code = "dataConnection", name = "数据连接", parentCode = "dataAssets")
@Slf4j
public class MetadataController {
    @Autowired
    private SysAuthObjRelService sysAuthObjRelService;

    @Autowired
    private IDataSetAuthService dataSetAuthService;

    @Autowired
    private DataBaseFactory dataBaseFactory;

    @Autowired
    private MetadataService metadataService;

    @Autowired
    private IDataWarehousePlanService dataWarehouseService;

    @Autowired
    private SysAuthObjFuncService sysAuthObjFuncService;

    @Autowired
    private SysAuthObjService sysAuthObjService;

    @Autowired
    private SysFuncService sysFuncService;

    @Autowired
    private IDataWareTreeService dataWareTreeService;

    @Autowired
    private ClassifierService classifierService;

    @Autowired
    private QueryDataService queryDataService;

    @Autowired
    private IUserService userService;

    @Autowired
    private IDataSetOperationService operationService;

    @Value("${sceModel}")
    private boolean sceModel;

    private DragonEncryptor dragonEncryptor = new DragonEncryptor();

    private static final List<String> JDBC_TYPE = Arrays.asList("POSTGRESQL", "GREENPLUM", "ORACLE", "hbase", "hwmpp", "Hive");

    /**
     * 查询相应数据源的版本列表
     *
     * @param dbType 数据源类型
     * @return
     */
    @ApiOperation(value = "查询相应数据源的版本列表")
    @GetMapping(value = "/datasource/list")
    public Result dataSourceList(String dbType) {
        Map<String, Object> versionMap = new HashMap<>();
        if (DbTypeValidator.haveZooKeeper(dbType)) {
            versionMap.put("zkList", metadataService.findZookeepers());
            versionMap.put("zkVersionList", metadataService.findSoftwareVersions("zookeeper"));
        }
        versionMap.put("versionList", metadataService.findSoftwareVersions(dbType));
        return Result.success(versionMap);
    }

    /**
     * 校验ip地址对于的机器信息是否存在
     *
     * @param ip ip地址
     * @return
     */
    @GetMapping("/datasource/machine")
    public Result checkMachine(String ip) {
        return Result.success(metadataService.checkMachineIpExist(ip));
    }

    /**
     * 连接测试：检查数据源信息是否正常连接
     *
     * @param dataSourceVO 数据源信息
     * @return
     */
    @PostMapping("/datasource/check")
    public Result checkConnect(@RequestBody DataSourceVO dataSourceVO) {
        DataBaseConnectService connectService = dataBaseFactory.matchDb(dataSourceVO.getDbType()); //根据type创造不同工厂
        boolean connectionStatus = connectService.testConnection(dataSourceVO);
        return Result.success(connectionStatus);
    }

    /**
     * 链接测试（最外层的数据连接）
     * 缝合怪
     */
    @RequestMapping("/datasource/checkOut")
    @FuncScanAnnotation(code = "dataConnectionTestConnection", name = "测试连接", parentCode = "dataConnection")
    @ValidateAndLogAnnotation
    public Result checkConnect(String instanceId) {
        Map<String, Object> res = new HashMap<>();
        DataSourceDTO dsVo = metadataService.findDataSourceById(instanceId);
        List<SoftwareVo> softwareVersions = metadataService.findSoftwareVersions(dsVo.getDbType());
        DataSourceVO dataSourceVO = new DataSourceVO();
        dataSourceVO.setDbCode(dsVo.getDbCode());
        dataSourceVO.setDbName(dsVo.getDbName());
        dataSourceVO.setDbType(dsVo.getDbType());
        dataSourceVO.setIp(dsVo.getIp());
        dataSourceVO.setPort(dsVo.getPort());
        SoftwareVo softwareVo = softwareVersions.stream().filter(item -> dsVo.getVersion().equals(item.getVersion())).collect(Collectors.toList()).get(0);
        dataSourceVO.setSoftwareId(softwareVo.getId());
        dataSourceVO.setZookeeperId(dsVo.getZookeeperId());
        dataSourceVO.setZookeeperVersion(dsVo.getZookeeperVersion());
        if (isToEncrypt(dsVo.getDbType())) {
            dataSourceVO.setPassword(dragonEncryptor.encrypt(dsVo.getPassword()));
        } else {
            dataSourceVO.setPassword(dsVo.getPassword());
        }
        dataSourceVO.setUsername(dsVo.getUsername());
        dataSourceVO.setConnType(JDBC_TYPE.contains(dsVo.getDbType()) ? "jdbc" : "");
        DataBaseConnectService connectService = dataBaseFactory.matchDb(dataSourceVO.getDbType()); //根据type创造不同工厂
        boolean connectionStatus = connectService.testConnection(dataSourceVO);
        return Result.success(connectionStatus);
    }

    @ApiOperation(value = "元数据导入数据源时校验用户名密码")
    @PostMapping("/datasource/checkImportAuth")
    public Result checkImportAuth(@RequestBody CheckAuthImportDTO dto){
        if (StringUtils.isBlank(dto.getCatalogId())){
            Assert.fail("请选择目标库");
        }
        if (StringUtils.isBlank(dto.getUsername())||StringUtils.isBlank(dto.getPassword())){
            Assert.fail("用户名/密码不能为空");
        }
        DataSourceDTO dsVo = metadataService.findDataSourceById(dto.getCatalogId());
        if (null == dsVo){
            Assert.fail("目标实例不存在");
        }
        if (isToEncrypt(dsVo.getDbType())) {
            //都统一按加密来处理(redis这种可以不设置密码的,按空来?目前列表是没有redis的选项的)
            dsVo.setPassword(dragonEncryptor.encrypt(dsVo.getPassword()));
        }
        if (!Objects.equals(dsVo.getUsername(),dto.getUsername())){
            Assert.fail("用户名错误");
        }
        if (!Objects.equals(dsVo.getPassword(),dto.getPassword())){
            Assert.fail("密码错误");
        }
        return Result.success();
    }

    private boolean isToEncrypt(String dbType) {
        if (dbType.equalsIgnoreCase("REDIS")
                || dbType.equalsIgnoreCase("KAFKA")
        ) {
            return true;
        }
        return false;
    }


    @GetMapping("/datasource/getZkMachine")
    public Result getZkMachine(String instanceId, String dataSourceType) {
        if (dataSourceType.equalsIgnoreCase(DataBaseFactory.DataSourceEnum.HBASE.name())) {
            dataSourceType = DataBaseFactory.DataSourceEnum.ZOOKEEPER.name();
        }
        return Result.success(metadataService.findMachineByInstanceId(instanceId, dataSourceType));
    }

    @PostMapping("/datasource/save")
    @FuncScanAnnotation(code = "dataConnectionAddDataSource", name = "添加数据源", parentCode = "dataConnection")
    @ValidateAndLogAnnotation
    public Result saveConnect(@RequestBody DataSourceVO dataSourceVO, HttpServletRequest request) {
        dataSourceVO.setUserId((String) request.getSession().getAttribute("userId"));
        InsertDataSourceResponse rvalue = metadataService.insertDataSource(dataSourceVO);
        return Result.success(rvalue);
    }

    @GetMapping("/datasource/edit")
    @FuncScanAnnotation(code = "dataConnectionEditDataSource", name = "编辑", parentCode = "dataConnection")
    @ValidateAndLogAnnotation
    public Result editConnect(String instanceId) {
        Map<String, Object> res = new HashMap<>();
        DataSourceDTO dsVo = metadataService.findDataSourceById(instanceId);
        if (isToEncrypt(dsVo.getDbType())) {
            dsVo.setPassword(dragonEncryptor.encrypt(dsVo.getPassword()));
        }
        res.put("versionList", metadataService.findSoftwareVersions(dsVo.getDbType()));
        res.put("zkVersionList", metadataService.findSoftwareVersions("zookeeper"));
        res.put("machineList", metadataService.findMachineByInstanceId(instanceId, dsVo.getDbType()));
        res.put("datasourceVo", dsVo);
        return Result.success(res);
    }

    @PostMapping("/datasource/update")
    public Result updateConnect(@RequestBody DataSourceVO dataSourceVO) {
        //更新数仓
        metadataService.updateDwbName(dataSourceVO.getId(), dataSourceVO.getDbType(), dataSourceVO.getDbName());
        metadataService.updateDataSource(dataSourceVO); //更新数据源
        refreshDbCache(dataSourceVO);
        return Result.success();
    }

    private void refreshDbCache(DataSourceVO dataSourceVO) {
        String schemaIdByCatalogId = metadataService.getSchemaIdByCatalogId(dataSourceVO.getId());
        queryDataService.refreshDataSourceCache(Lists.newArrayList(schemaIdByCatalogId));
    }

    @ApiOperation(value = "获取schema用户列表")
    @GetMapping("/dataobject/schema")
    public Result schemaList(String instanceId) {
        DataSourceDTO dataSourceVo = metadataService.findDataSourceById(instanceId);
        Map<String, Object> data = new HashMap<>();
        data.put("dbType", dataSourceVo.getDbType());
        data.put("version", dataSourceVo.getVersion());
        if (DbTypeValidator.isRdb(dataSourceVo.getDbType().toUpperCase()) || DbTypeValidator.isEs(dataSourceVo.getDbType())) {
            List<SchemaVo> schemaVoList = metadataService.findUserByDataSourceId(instanceId, dataSourceVo.getDbType());
            data.put("list", schemaVoList);
        }
        return Result.success(data);
    }

    @ApiOperation(value = "获取待注册的数据对象列表(此功能的分页和搜索需在前端做)")
    @GetMapping("/dataobject/queryPendingRegisterDataObj")
    public Result queryPendingRegisterDataObj(String schemaId, String dbType) {
        Map<String, Object> data = new HashMap<>();
        DbTableVO dbTableVO = metadataService.findDatabaseTableList(schemaId, dbType);
        data.put("key", dbTableVO.getKey());
        data.put("tableList", dbTableVO.getDatabaseTableVoList());
        return Result.success(data);
    }

    @ApiOperation(value = "注册数据对象")
    @PostMapping("/dataobject/save")
    @FuncScanAnnotation(code = "dataConnectionAddDataTable", name = "添加数据表", parentCode = "dataConnection")
    @ValidateAndLogAnnotation
    public Result saveDataObj(@RequestBody DataObjectVO dataObjectVO) {
        List<DataObjectView> rtvalue = metadataService.saveDataObject(dataObjectVO);
        return Result.success(rtvalue);
    }

    /**
     * 数据源同步
     */
    @GetMapping("/dataobject/column")
    @ApiOperation(value = "获取已注册数据对象的列变更信息")
    @FuncScanAnnotation(code = "dataConnectionSynchronousDataSource", name = "同步", parentCode = "dataConnection")
    @ValidateAndLogAnnotation
    public Result findChangeColumn(String dataObjId, String dbType) {
        ChangeColumnVO columnVO = metadataService.findChangeColumns(dataObjId, dbType);
        return Result.success(columnVO);
    }


    @GetMapping(value = "/dataobject/deleteDWDBInstance")
    @FuncScanAnnotation(code = "dataConnectionDeleteDataSource", name = "删除", parentCode = "dataWarehouse")
    @ValidateAndLogAnnotation
    public Result deleteDWDBInstance(String elementId, String classifyId) {

        if (dataWarehouseService.checkTable(elementId)) {
            return Result.toResult(R.error("该数据源存在数据对象，无法删除"));
        }
        boolean deletePhysics = dataWarehouseService.deleteDataWarehouseDBInstance(elementId, classifyId);


        //删除此表跟角色和用户关系
        Map<String, String> functionRelationParams = Maps.newHashMap();
        functionRelationParams.put("func_code", elementId);
        List<TSysAuthObjFunc> tSysAuthObjFuncs = sysAuthObjFuncService.queryList(functionRelationParams);
        for (TSysAuthObjFunc tSysAuthObjFunc : tSysAuthObjFuncs) {
            sysAuthObjService.deleteAuthObj(tSysAuthObjFunc);
        }

        //删除注册到功能表的数据对象
        Map<String, String> functionParams = Maps.newHashMap();
        functionParams.put("func_code", elementId);
        TSysFuncBase tSysFuncBase = (TSysFuncBase) sysFuncService.query(functionParams);
        if (null != tSysFuncBase) {
            sysAuthObjService.deleteAuthObj(tSysFuncBase);
        }

        return Result.success(deletePhysics);
    }

    /**
     * 获取管家的类型树
     */
    @GetMapping(value = "/dataobject/getHouseKeepTree")
    public Result getHouseKeepTree() {
        return Result.success(metadataService.getAllHouseKeeperType());
    }


    /**
     * 从元数据导入， 通过数据类型找到对应的库
     *
     * @param dbtype
     * @return
     */
    @GetMapping(value = "/dataobject/findHouseKeepDataObjByType")
    public Result findHouseKeepDataObjByType(String dbtype) {
        return Result.success(metadataService.findDataObjByType(dbtype));
    }


    /**
     * 删除数据实例的表
     *
     * @param tableId
     * @param dbType
     * @return
     */
    @GetMapping(value = "/deleteTable")
    @FuncScanAnnotation(code = "dataConnectionDeleteTable", name = "删除", parentCode = "dataConnection")
    @ValidateAndLogAnnotation
    public Result deleteTable(String classifyId, String dbType, String tableId, String judgeType) {
        List<String> list = Lists.newArrayList();
        list.add(tableId);
        if (dataWarehouseService.isDataWareHouse(list)) {
            return Result.toResult(R.ok("其他仓库有使用到该表，无法删除！"));
        }
        if (dataWareTreeService.isBuildModel(list)) {
            return Result.toResult(R.ok("该表已被用于流程建模，无法删除！"));
        }
        if (dataWareTreeService.isBuildDMCModel(list)) {
            return Result.toResult(R.ok("该表已被用于自助建模，无法删除！"));
        }
        if (dataWareTreeService.isBuildVisualModel(list)) {
            return Result.toResult(R.ok("该表已被用于可视化建模，无法删除！"));
        }
        //删除此表跟角色和用户关系
        Map<String, String> functionRelationParams = Maps.newHashMap();
        functionRelationParams.put("func_code", tableId);
        List<TSysAuthObjFunc> tSysAuthObjFuncs = sysAuthObjFuncService.queryList(functionRelationParams);
        for (TSysAuthObjFunc tSysAuthObjFunc : tSysAuthObjFuncs) {
            sysAuthObjService.deleteAuthObj(tSysAuthObjFunc);
        }

        //删除注册到功能表的数据对象
        Map<String, String> functionParams = Maps.newHashMap();
        functionParams.put("func_code", tableId);
        TSysFuncBase tSysFuncBase = (TSysFuncBase) sysFuncService.query(functionParams);
        if (null != tSysFuncBase) {
            sysAuthObjService.deleteAuthObj(tSysFuncBase);
        }
        if (Objects.equals("大数据管家", judgeType)) {
            //删除此表跟角色和用户关系
            Map<String, String> functionRelationParamsCode = Maps.newHashMap();
            functionRelationParamsCode.put("func_code", classifyId);
            List<TSysAuthObjFunc> tSysAuthObjFuncsCode = sysAuthObjFuncService.queryList(functionRelationParamsCode);
            for (TSysAuthObjFunc tSysAuthObjFunc : tSysAuthObjFuncsCode) {
                sysAuthObjService.deleteAuthObj(tSysAuthObjFunc);
            }

            Map<String, String> functionParamsCode = Maps.newHashMap();
            functionParamsCode.put("func_code", classifyId);
            TSysFuncBase tSysFuncBaseCode = (TSysFuncBase) sysFuncService.query(functionParamsCode);
            if (null != tSysFuncBaseCode) {
                sysAuthObjService.deleteAuthObj(tSysFuncBaseCode);
            }
        }
        dataWarehouseService.deleteTable(tableId, dbType, judgeType);
        return Result.toResult(R.ok());
    }

    @RequestMapping("/getDataSourceTree")
    @FuncScanAnnotation(code = "dataConnectionDataWarehouse", name = "数据仓库", parentCode = "dataConnection")
//    @ValidateAndLogAnnotation
    public Result getDataSourceTree(@RequestParam(required = false, defaultValue = "false") boolean isLogic, @RequestParam(required = false, defaultValue = "false") boolean dataSpace, HttpServletRequest request) {
        String userId = (String) request.getSession().getAttribute("userId");
        List<String> authIds = new ArrayList<>();

        String dataSpaceStr = dataSpace ? "dataSpace" : "";
        if (null != userId) {
            authIds = userService.getAllAuthId(userId);
        }
        List<DatasetTreeModel> treeModelList = null;
        if (isLogic) {
            treeModelList = operationService.querySourceDatasetTree(userId, "", null);
        } else {
            treeModelList = dataWareTreeService.getDataWarehouseTreeCopy("", false, true, userId, authIds, dataSpaceStr);
        }
        if (!sceModel) {
            for (int i = 0; i < treeModelList.size(); i++) {
                if ("场景案例".equals(treeModelList.get(i).getName())) {
                    treeModelList.remove(i);
                    break;
                }
            }
        }
        return Result.success(treeModelList);
    }

    public static void main(String[] args) {
        DragonEncryptor dragonEncryptor=new DragonEncryptor();
        System.out.println(dragonEncryptor.encrypt("123456"));
        System.out.println(dragonEncryptor.decrypt("200000000000625D55"));
    }

    /**
     * 3.0 数据连接 表预览数据
     * 原本存在注释掉的文件类型预览删除
     */
    @PostMapping("/preview")
    public Result preview(@RequestBody Map resMap) {
        String tableId = resMap.get("tableId").toString();
        String pageSize = resMap.get("pageSize").toString();
        String pageIndex = resMap.get("pageIndex").toString();
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageSize(StringUtils.isNotBlank(pageSize) ? Integer.parseInt(pageSize) : 10);
        pageInfo.setPageIndex(StringUtils.isNotBlank(pageIndex) ? Integer.parseInt(pageIndex) : 1);
        ColumnPageInfo rows = new ColumnPageInfo();
        ClassifierStat obj = classifierService.getClassifierObj(ClassifierStat.class, tableId);
        String dbType = obj.getDbType();
//        tableId = "LogicDataObj".equals(obj.getType()) ? tableId : obj.getOwnerId();
        ColumnDataModel columnDataModel = null;
        try {
            String sql = completeSql(obj);
            //原本在内存中分页，现在在sql里分页
            sql = buildPageSql(sql, pageInfo, dbType);
            ParamDataModel paramDataModel = new ParamDataModel();
           
            
            String countSql = "select count(*) from " + getTableName(obj);
            paramDataModel.setScript(countSql);
            ColumnDataModel count = queryDataService.queryData(tableId, paramDataModel);
            Map map = count.getFieldValue().get(0);
            for (Object value : map.values()) {
                if (value instanceof BigDecimal) {
                    rows.setTotalCount(((BigDecimal) value).longValue());
                } else {
                    rows.setTotalCount((Long) value);
                }
                break;
            }
           
            paramDataModel.setScript(sql);
            columnDataModel = queryDataService.queryData(tableId, paramDataModel);
            
        } catch (Exception e) {
            log.error(e.getMessage(),e);
        }
        if (columnDataModel != null) {
            columnDataModel = ChangeColumnNameUtil.changeName(tableId, columnDataModel);
        }
        rows.setPageCount(pageInfo.getPageCount());
        rows.setPageIndex((long)pageInfo.getPageIndex());
        rows.setPageSize((long)pageInfo.getPageSize());
        rows.setColumnDataModel(columnDataModel);

        return Result.success(rows);
    }

    private String buildPageSql(String sql, PageInfo pageInfo, String dbType) {
        switch (dbType.toLowerCase()) {
            case "mysql":
                return buildMysqlPageSql(sql, pageInfo);
            case "oracle":
                return buildOraclePageSql(sql, pageInfo);
            case "postgresql":
                return buildPostgreSqlPageSql(sql, pageInfo);
            case "greenplum":
                return buildGreenplumPageSql(sql, pageInfo);
            case "hive":
                return buildHivePageSql(sql, pageInfo);
            case "vertica":
                return buildVerticaPageSql(sql, pageInfo);
            // 添加其他数据库类型的分页SQL构建方法
            default:
                //throw new IllegalArgumentException("Unsupported database type: " + dbType);
                return buildNormalPageSql(sql, pageInfo);
        }
    }

    private String buildNormalPageSql(String sql, PageInfo pageInfo) {
        int offset = (pageInfo.getPageIndex() - 1) * pageInfo.getPageSize();
        return sql + " LIMIT " + offset + ", " + pageInfo.getPageSize();
    }


    private String buildVerticaPageSql(String sql, PageInfo pageInfo) {
        int offset = (pageInfo.getPageIndex() - 1) * pageInfo.getPageSize();
        return sql + " OFFSET " + offset + " LIMIT " + pageInfo.getPageSize();
    }
    
    private String buildHivePageSql(String sql, PageInfo pageInfo) {
        int offset = (pageInfo.getPageIndex() - 1) * pageInfo.getPageSize();
        return sql + " LIMIT " + offset + ", " + pageInfo.getPageSize();
    }

    private String buildMysqlPageSql(String sql, PageInfo pageInfo) {
        int offset = (pageInfo.getPageIndex() - 1) * pageInfo.getPageSize();
        return sql + " LIMIT " + offset + ", " + pageInfo.getPageSize();
    }

    private String buildOraclePageSql(String sql, PageInfo pageInfo) {
        int offset = (pageInfo.getPageIndex() - 1) * pageInfo.getPageSize() + 1;
        int end = pageInfo.getPageIndex() * pageInfo.getPageSize();
        return "SELECT * FROM (SELECT A.*, ROWNUM RN FROM (" + sql + ") A WHERE ROWNUM <= " + end + ") WHERE RN >= " + offset;
    }

    private String buildPostgreSqlPageSql(String sql, PageInfo pageInfo) {
        int offset = (pageInfo.getPageIndex() - 1) * pageInfo.getPageSize();
        return sql + " OFFSET " + offset + " LIMIT " + pageInfo.getPageSize();
    }

    private String buildGreenplumPageSql(String sql, PageInfo pageInfo) {
        int offset = (pageInfo.getPageIndex() - 1) * pageInfo.getPageSize();
        return sql + " OFFSET " + offset + " LIMIT " + pageInfo.getPageSize();
    }
    
    

    private String completeSql(ClassifierStat obj) {
        StringBuffer sql = new StringBuffer();
        Map<String, StructuralFeature> features = obj.getFeatures();
        for (Map.Entry<String, StructuralFeature> item : features.entrySet()) {
            if (sql.length() > 0) {
                sql.append(",");
            }
            sql.append(item.getValue().getCode());
        }
        if (sql.length()==0){
            sql.append(" * ");
        }
        return "select  " + sql.toString() + " from " + getTableName(obj);
    }

    private String getTableName(ClassifierStat stat) {
        StringBuffer tableName = new StringBuffer();
        if (stat.getDbType().equals("GREENPLUM") && !stat.getCode().contains(".")) {
            return tableName.append("\"").append(stat.getCode()).append("\"").toString();
        }
        return stat.getCode();
    }


    /**
     * 3.0 获取数据连接表字段
     */
    @GetMapping("/getDataObjColumn")
    public Result getDataObjColumn(String dataObjId, String dbType) {
        return Result.success(metadataService.getColumn(dbType, dataObjId));
    }

    /**
     * 3.0 移动
     */
    @GetMapping("/moveDwbInstance")
    @FuncScanAnnotation(code = "dataConnectionMove", name = "移动", parentCode = "dataConnection")
    @ValidateAndLogAnnotation
    public Result moveDwbInstance(String dirId, String dwbId) {
        metadataService.moveDwbInstance(dirId, dwbId);
        return Result.success();
    }

    /**
     * 3.0 另存为   恶心的缝合
     */
    @GetMapping("/saveAs")
    @FuncScanAnnotation(code = "dataConnectionSaveAs", name = "另存为", parentCode = "dataConnection")
    @ValidateAndLogAnnotation
    public Result saveAs(String name,
                         String dwbId,
                         String schemaId,
                         String dbType,
                         String classifyId,
                         String busiDirId, HttpServletRequest request) {
        String userId = (String) request.getSession().getAttribute("userId");  //判断重名
        int count = dataWarehouseService.checkDataSourceName(classifyId, name);
        Assert.isZero(count, "数据源名称重复，请重新输入！");
        // 1. 创建新数据源
        DataSourceVO dataSourceVO = metadataService.saveAs(schemaId, dbType, name);  //先把信息捞出来
        dataSourceVO.setClassifyId(classifyId);
        dataSourceVO.setUserId(userId);
        InsertDataSourceResponse rvalue = metadataService.insertDataSource(dataSourceVO);

        // 2. 创建新数仓
        DataWarehouseVo vo = new DataWarehouseVo();
        vo.setName(name);
        vo.setDomainDbType(dbType);
        vo.setDataSourceName(name);
        vo.setOtherElementId(rvalue.getSchemaId());
        vo.setSoftwareId(rvalue.getCatalogId());
        String instanceId = dataWarehouseService.addDataWarehouseInstance(vo, classifyId, busiDirId, userId);
        dataWarehouseService.addDWRelationDb(vo, instanceId);
        dataWarehouseService.addTreeNode(instanceId, classifyId);

        // 3. 第一次授权
        DataSetAuthVo dataSetAuthVo = new DataSetAuthVo();
        UserVo userVo = new UserVo();
        userVo.setId(userId);
        DataObjectVo dataObjectVo = new DataObjectVo();
        dataObjectVo.setName(name);
        dataObjectVo.setCode(instanceId);
        List<DataObjectVo> dataObjectVos = new ArrayList<>();
        dataObjectVos.add(dataObjectVo);
        dataSetAuthVo.setDataObjectVos(dataObjectVos);
        List<UserVo> userVos = new ArrayList<>(1);
        userVos.add(userVo);
        dataSetAuthVo.setUserVos(userVos);
        dataSetAuthService.saveDataSetAuth(dataSetAuthVo.getDataObjectVos(), userId);
        // 4. 第一次授权
        dataSetAuthService.saveFirstAuth(dataSetAuthVo);
        return Result.success();
    }

    @GetMapping("/getDataTableById")
    public Result getDataTableById(String dataObjId, String type) {
        return Result.success(metadataService.getDbTableByCode(dataObjId, type));
    }

    @GetMapping("/overlaySynchronization")
    public Result overlaySynchronization(String dataObjId) {
        metadataService.overlaySynchronization(dataObjId);
        return Result.success();
    }

    @GetMapping("/dataobject/deleteDwFromMetdata")
    public Result deleteDwFromMetdata(String id) {
        metadataService.deleteDwFromMetdata(id);
        return Result.success();
    }

    @GetMapping("/syncColumns")
    public Result syncColumnsDcThree(String logicId) {
        //向上同步的逻辑
        metadataService.syncFromChildrenToParent(logicId);
        metadataService.syncChidren(logicId);
        return Result.success();
    }

    /**
     * 合并添加数据集的操作为一个接口221107
     */
    @ApiOperation(value = "新的注册数据对象接口")
    @PostMapping("/dataobject/newSave")
    @FuncScanAnnotation(code = "dataConnectionAddDataTable", name = "添加数据表", parentCode = "dataConnection")
    @ValidateAndLogAnnotation
    public Result newSaveDataObj(@RequestBody NewDataObjectVO dataObjectVO, HttpServletRequest request) {
        String userId = (String) request.getSession().getAttribute("userId");
        dataObjectVO.setUserId(userId);
        metadataService.saveDataObjNew(dataObjectVO);
        return Result.success();
    }

    /**
     * @Description: 校验是否连接名重复(可以不用鉴权的吧)
     * <AUTHOR>
     * @CreateTime
     */
    @PostMapping("/datasource/checkConnectRepeat")
    public Result checkConnectRepeat(String classifyId, String dataSourceName) {
        int count = dataWarehouseService.checkDataSourceName(classifyId, dataSourceName);
        return Result.success(count > 0 ? false : true);
    }


}
