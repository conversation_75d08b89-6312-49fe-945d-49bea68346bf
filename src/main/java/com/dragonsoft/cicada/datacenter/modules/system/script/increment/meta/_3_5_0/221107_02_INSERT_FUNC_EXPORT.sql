-- 一级 建模空间
-- 二级 数据导出
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state", "is_doc_show") VALUES ('dataExport', '0', '数据导出', 'dataModeling', NULL, NULL, NULL, '2022-08-01 14:21:03', NULL, '1', '1');
-- 三级 文件下载插件导出和预览数据导出
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state", "is_doc_show") VALUES ('dataExportFileDownload', '0', '文件下载插件导出', 'dataExport', NULL, NULL, NULL, '2022-08-01 14:21:03', NULL, '1', '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state", "is_doc_show") VALUES ('dataExportPreview', '0', '插件预览导出', 'dataExport', NULL, NULL, NULL, '2022-08-01 14:21:03', NULL, '1', '1');

-- 绑定系统管理员权限
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('9984ca93f53951dba4814106f215f1a8', 'd6121bc4248e45019942e2cb78362500', 'dataExport', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('b1485fddbd1052bcab2f5eaf6b78ad7d', 'd6121bc4248e45019942e2cb78362500', 'dataExportFileDownload', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('67c73006cbc35c7e9cc37ec5603c18fc', 'd6121bc4248e45019942e2cb78362500', 'dataExportPreview', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
