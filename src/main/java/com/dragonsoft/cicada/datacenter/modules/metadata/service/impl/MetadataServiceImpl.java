package com.dragonsoft.cicada.datacenter.modules.metadata.service.impl;

import com.code.common.bean.BeanFactory;
import com.code.common.utils.StringUtils;
import com.code.common.utils.assertion.Assert;
import com.code.metadata.base.softwaredeployment.DeployedComp;
import com.code.metadata.base.softwaredeployment.Machine;
import com.code.metadata.base.softwaredeployment.Software;
import com.code.metadata.model.core.ModelElement;
import com.code.metadata.model.core.StructuralFeature;
import com.code.metadata.res.app.zk.ZkCluster;
import com.code.metadata.res.app.zk.ZkInstance;
import com.code.metadata.res.common.ClassifierStat;
import com.code.metadata.res.ddl.LogicDataColumn;
import com.code.metadata.res.ddl.LogicDataObj;
import com.code.metadata.res.semistructured.fulltext.elasticsearch.ElasticSearchInstance;
import com.code.metadata.res.semistructured.hbase.HbaseInstance;
import com.code.metadata.res.semistructured.kafka.KafkaInstance;
import com.code.metadata.res.structured.rdb.RdbSchema;
import com.code.metaservice.base.softwaredeployment.MachineService;
import com.code.metaservice.base.softwaredeployment.SoftwareService;
import com.code.metaservice.business.directory.BusiDirService;
import com.code.metaservice.datawarehouse.model.DataSourceDTO;
import com.code.metaservice.datawarehouse.model.DataSourceMachineVo;
import com.code.metaservice.datawarehouse.model.DataSourceVO;
import com.code.metaservice.datawarehouse.model.SchemaVo;
import com.code.metaservice.ddl.IDataSetSyncService;
import com.code.metaservice.ddl.ILogicDataColumnService;
import com.code.metaservice.ddl.ILogicDataObjService;
import com.code.metaservice.ddl.vo.LogicHttpColumns;
import com.code.metaservice.res.DataSourceType;
import com.code.metaservice.res.IDataObjectService;
import com.code.metaservice.res.IDataSourceService;
import com.code.metaservice.res.app.zk.IZkInstanceService;
import com.code.metaservice.res.app.zk.ZkClusterService;
import com.code.metaservice.res.common.ClassifierStatService;
import com.code.metaservice.res.request.dataobject.DataObjectRequest;
import com.code.metaservice.res.request.vo.InsertDataObjectView;
import com.code.metaservice.res.response.dataobject.FindChangeColumnResponse;
import com.code.metaservice.res.response.dataobject.FindDatabaseTableResponse;
import com.code.metaservice.res.response.datasource.InsertDataSourceResponse;
import com.code.metaservice.res.response.vo.dataobject.ChangeColumnView;
import com.code.metaservice.res.response.vo.dataobject.DataObjectColumnView;
import com.code.metaservice.res.response.vo.dataobject.DataObjectView;
import com.code.metaservice.res.response.vo.dataobject.DatabaseTableView;
import com.code.metaservice.res.response.vo.datasource.DataSourceSchemaView;
import com.code.metaservice.res.response.vo.datasource.DataSourceView;
import com.code.metaservice.res.semistructured.fulltext.FullTextElasticSearchService;
import com.code.metaservice.res.semistructured.kafka.IKafkaInstanceService;
import com.code.metaservice.res.structured.rdb.IRdbCatalogService;
import com.code.plugin.db.TypeMapping;
import com.dragonsoft.cicada.datacenter.common.utils.StreamUtils;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.service.IDataSetEditService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.util.DataSetUtils;
import com.dragonsoft.cicada.datacenter.modules.dataplan.externaldatasources.service.IDataSetAuthService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.externaldatasources.service.IDataWarehousePlanService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.externaldatasources.vo.DataObjectVo;
import com.dragonsoft.cicada.datacenter.modules.metadata.service.*;
import com.dragonsoft.cicada.datacenter.modules.metadata.vo.*;
import com.fw.service.BaseService;
import com.fw.service.annotation.Service;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Wu.D.J
 * @Create : 2021.03.29
 */
@Slf4j
@Service
public class MetadataServiceImpl extends BaseService implements MetadataService {

    @Autowired
    private DataBaseFactory dataBaseFactory;

    @Autowired
    private IDataSourceService dataSourceService;

    @Autowired
    private MachineService machineService;

    @Autowired
    private IZkInstanceService zkInstanceService;

    @Autowired
    private SoftwareService softwareService;

    @Autowired
    private BusiDirService busiDirService;

    @Autowired
    private IRdbCatalogService rdbCatalogService;

    @Autowired
    private IDataObjectService dataObjectService;

    @Autowired
    private FullTextElasticSearchService fullTextElasticSearchService;

    @Autowired
    private IKafkaInstanceService kafkaInstanceService;
 @Autowired
    private ZkClusterService zkClusterService;

    @Autowired
    private ClassifierStatService classifierStatService;

    @Autowired
    private ILogicDataObjService logicDataObjService;

    @Autowired
    private ILogicDataColumnService logicDataColumnService;

    @Autowired
    private BeanFactory beanFactory;

    @Autowired
    IDataSetSyncService dataSetSyncService;

    @Autowired
    IDataSetEditService editService;

    @Autowired
    private IDataSetAuthService dataSetAuthService;

    @Autowired
    private IDataWarehousePlanService dataWarehousePlanService;

    @Autowired
    private IDataWarehousePlanService dataWarehouseService;

    @Override
    public InsertDataSourceResponse insertDataSource(DataSourceVO dataSourceVO) {
        if (StringUtils.isNotBlank(dataSourceVO.getId()) &&
                DataBaseFactory.DataSourceEnum.ZOOKEEPER.name().equalsIgnoreCase(dataSourceVO.getDbType())) {
            DataSourceView dataSourceView = dataSourceService.queryByResourceId(dataSourceVO.getId());
            if (dataSourceView != null && !dataSourceView.getName().equals(dataSourceVO.getDbName())) {
                // 判断ZOOKEEPER类型数据源的集群名是否存在重复
                if (dataSourceService.isExitZkInstanceBySoftWareIdAndIpAndPostAndName(dataSourceVO.getSoftwareId(),
                        dataSourceVO.getIp(), dataSourceVO.getPort(), dataSourceVO.getDbName())) {
                    Assert.fail("已存在相同属性的数据源，无法重复集群名");
                }
            }
        }

        DataBaseConnectService connectService = dataBaseFactory.matchDb(dataSourceVO.getDbType());
        return connectService.insertDataSource(dataSourceVO);
    }

    @Override
    public void updateDataSource(DataSourceVO dataSourceVO) {
        if (dataSourceVO.getIsUpdate() != null && !dataSourceVO.getIsUpdate()) {
//            if (dataSourceService.isExitName(dataSourceVO.getDbName(), dataSourceVO.getDbType())) {
//                Assert.fail("已存在相同属性的数据源，无法重复数据库名称");
//            }
        }

        if (StringUtils.isNotBlank(dataSourceVO.getId()) &&
                DataBaseFactory.DataSourceEnum.ZOOKEEPER.name().equalsIgnoreCase(dataSourceVO.getDbType())) {
            DataSourceView dataSourceView = dataSourceService.queryByResourceId(dataSourceVO.getId());
            if (dataSourceView != null && !dataSourceView.getName().equals(dataSourceVO.getDbName())) {
                // 判断ZOOKEEPER类型数据源的集群名是否存在重复
                if (dataSourceService.isExitZkInstanceBySoftWareIdAndIpAndPostAndName(dataSourceVO.getSoftwareId(),
                        dataSourceVO.getIp(), dataSourceVO.getPort(), dataSourceVO.getDbName())) {
                    Assert.fail("已存在相同属性的数据源，无法重复集群名");
                }
            }
        }

        DataBaseConnectService connectService = dataBaseFactory.matchDb(dataSourceVO.getDbType());
        connectService.updateResource(dataSourceVO);
    }

    @Override
    public MachineVo checkMachineIpExist(String ip) {
        Machine machine = machineService.getMachineByIp(ip);
        if (machine == null) {
            return new MachineVo();
        }
        MachineVo machineVo = new MachineVo();
        machineVo.setId(machine.getId());
        machineVo.setMachineName(machine.getName());
        machineVo.setHostName(machine.getCode());
        machineVo.setIpAddress(machine.getIpAddress());
        machineVo.setMacAddress(machine.getMacAddress());
        machineVo.setUserName(machine.getUsername());
        machineVo.setPassword(machine.getPassword());
        machineVo.setCpuNum(machine.getCpuNum());
        machineVo.setCpuCoreNum(machine.getCpuCoreNum());
        machineVo.setCpuCoreSpeed(machine.getCpuCoreSpeed());
        machineVo.setMemorySize(machine.getMemorySize());
        machineVo.setDiskSize(machine.getDiskSize());
        machineVo.setCreateTime(machine.getOperateTime());
        machineVo.setMemo(machine.getMemo());
        return machineVo;
    }

    @Override
    public List<StandardCodeVo> findZookeepers() {
        List<ZkInstance> zkInstanceList = zkInstanceService.queryAll();
        return zkInstanceList.stream()
                .map(zkInstance -> new StandardCodeVo(zkInstance.getId(), zkInstance.getName()))
                .sorted(Comparator.comparing(StandardCodeVo::getName))
                .collect(Collectors.toList());
    }

    @Override
    public List<SoftwareVo> findSoftwareVersions(String dbType) {
        List<Software> softwareList = softwareService.querySoftwareByCode(dbType);
        return softwareList.stream()
                .filter(StreamUtils.distinctByKey(s->s.getSoftVersion()))
                .map(software -> new SoftwareVo(software.getId(), software.getSoftVersion()))
                .collect(Collectors.toList());
    }

    @Override
        public DataSourceDTO findDataSourceById(String instanceId) {
        DataSourceView dataSourceView;
        String type = busiDirService.getBaseModelById(instanceId);
        if (ElasticSearchInstance.class.getSimpleName().equalsIgnoreCase(type)) {
            dataSourceView = fullTextElasticSearchService.queryByInstanceId(instanceId);
            dataSourceView.setDatasourceType("Elasticsearch");
        } else if (KafkaInstance.class.getSimpleName().equalsIgnoreCase(type)) {
            dataSourceView = kafkaInstanceService.queryById(instanceId);
        } else if (ZkCluster.class.getSimpleName().equalsIgnoreCase(type)) {
            dataSourceView = zkClusterService.getZkClusterViewById(instanceId);
        } else {
            dataSourceView = dataSourceService.queryByResourceId(instanceId);
        }
        DataSourceDTO sourceVo = toDataSourceVo(dataSourceView);

        if (HbaseInstance.class.getSimpleName().equalsIgnoreCase(type)) {
            DataSourceView zkView = zkInstanceService.queryById(dataSourceView.getZookeeperId());
            String zkVersion = zkView.getDatasourceVersion();
            sourceVo.setZookeeperVersion(zkVersion);
        }
        return sourceVo;
    }

    @Override
    public List<SchemaVo> findUserByDataSourceId(String instanceId, String dbType) {
        if (DbTypeValidator.isRdb(dbType.toUpperCase())) {
            List<RdbSchema> rdbSchemaList = rdbCatalogService.querySchemaByCatalogId(instanceId);
            if (rdbSchemaList != null && !rdbSchemaList.isEmpty()) {
                List<SchemaVo> schemaVoList = new ArrayList<>(rdbSchemaList.size());
                for (RdbSchema rdbSchema : rdbSchemaList) {
                    SchemaVo schemaVo = new SchemaVo();
                    schemaVo.setCatalogCode(rdbSchema.getCatalog().getCode());
                    schemaVo.setCatalogId(rdbSchema.getCatalog().getId());
                    schemaVo.setCatalogName(rdbSchema.getCatalog().getName());
                    schemaVo.setMasterIp(rdbSchema.getCatalog().getMaster().getIPAddress());
                    schemaVo.setPassword(rdbSchema.getPassword());
                    schemaVo.setPort(String.valueOf(rdbSchema.getCatalog().getPort()));
                    schemaVo.setSchemaCode(rdbSchema.getCode());
                    schemaVo.setSchemaId(rdbSchema.getId());
                    schemaVo.setSchemaName(rdbSchema.getName());
                    schemaVo.setUserName(rdbSchema.getUserName());
                    schemaVo.setVersionId(rdbSchema.getCatalog().getSoftware().getId());
                    schemaVo.setVersionType(rdbSchema.getCatalog().getSoftware().getSoftVersion());
                    schemaVoList.add(schemaVo);
                }
                return schemaVoList;
            }
        }
        return null;
    }

    @Override
    public DbTableVO findDatabaseTableList(String schemaId, String dbType) {
        FindDatabaseTableResponse response = null;
        try {
            if(!"hbase".equalsIgnoreCase(dbType)){
                response = dataObjectService.queryRemoteDataObjectByFlag(schemaId, dbType, true); // true表示使用新版es表结构
            }

        } catch (Throwable e) {
            throw new RuntimeException("待注册数据对象列表查询失败!", e);
        }
        DbTableVO tableVO = new DbTableVO();
        if (response != null) {
            tableVO.setKey(response.getKey());
            List<DatabaseTableView> viewList = response.getDatabaseTableViewList();
            List<DbTableDTO> tableList = viewList.stream()
                    .filter(databaseTableView -> "ADD".equalsIgnoreCase(databaseTableView.getCheckStatus()))
                    .map(databaseTableView -> {
                        DbTableDTO databaseTableVo = new DbTableDTO();
                        databaseTableVo.setName(databaseTableView.getName());
                        databaseTableVo.setCode(databaseTableView.getCode());
                        databaseTableVo.setRealUserName(databaseTableView.getRealUserName());
                        return databaseTableVo;
                    }).collect(Collectors.toList());
            tableVO.setDatabaseTableVoList(tableList);

        }
        return tableVO;
    }

    @Override
    public List<DataObjectView> saveDataObject(DataObjectVO dataObjectVO) {
        return dataObjectService.insertDataObject(toDataObjectRequest(dataObjectVO));
    }

    @Override
    public ChangeColumnVO findChangeColumns(String dbObjId, String dbType) {
        FindChangeColumnResponse findChangeColumnResponse = dataObjectService.queryChangeColumnByFlag(dbObjId, dbType, true);// true表示使用新版es表结构
        findChangeColumnResponse.setRdbObjId(dbObjId);
        findChangeColumnResponse.setRdbType(dbType);

        ChangeColumnVO changeColumnVO = new ChangeColumnVO();
        changeColumnVO.setRdbType(findChangeColumnResponse.getRdbType());
        changeColumnVO.setRdbObjId(findChangeColumnResponse.getRdbObjId());
        changeColumnVO.setAddSize(Integer.parseInt(findChangeColumnResponse.getAddSize()));
        changeColumnVO.setUpdateSize(Integer.parseInt(findChangeColumnResponse.getUpdateSize()));
        changeColumnVO.setDeleteSize(Integer.parseInt(findChangeColumnResponse.getDeleteSize()));
        changeColumnVO.setKey(findChangeColumnResponse.getKey());
        changeColumnVO.setAddColumnList(toColumnProperty(findChangeColumnResponse.getResultTable(), "ADD"));
        changeColumnVO.setUpdateColumnList(toColumnProperty(findChangeColumnResponse.getResultTable(), "UPDATE"));
        changeColumnVO.setDeleteColumnList(toColumnProperty(findChangeColumnResponse.getResultTable(), "DELETE"));
        return changeColumnVO;
    }

    @Override
    public void syncColumn(String key, String dataObjId, String dbType) {
        dataObjectService.syncColumnByFlag(key, dataObjId, dbType, true); // true表示使用新版elasticsearch表结构
    }

    @Override
    public List<DataSourceMachineVo> findMachineByInstanceId(String instanceId, String dataSourceType) {
        if (StringUtils.isNotBlank(instanceId)) {
            List<DeployedComp> deployedCompList = dataSourceService.queryClusterByInstanceId(instanceId);
            return toDataSourceMachineViewFromDeployedComp(deployedCompList);
        } else {
            return null;
        }
    }

    @Override
    public void updateDwbName(String id, String dbType, String name) {

        String dwbId = getDWBId(id, dbType);
        if (isExitNameDw(name, dwbId)) {
            Assert.fail("数据源名称重复，请重新输入！");
        }
        updateDWBId(dwbId, name);
    }

    @Override
    public void syncColumnsLogicDataSet(String dataObjId) {
        //找到业务主题下的logic
        LogicDataObj logicDataObj = getLogicobjBelongtoRdb(dataObjId);
        //获取物理表classifierstat
        ClassifierStat classifierStat = (ClassifierStat) this.baseDao.get(ClassifierStat.class, dataObjId);
        syncLogicDataSetColumns(logicDataObj, classifierStat);

    }

    /**
     * // 示例： 原本 物理表 : A B C  logic: A B C  现在 物理表 做了 A更新了 叫做D  B删掉了 C不变，新增了E  现在物理表 D C E  ,现在logic 要 删掉A 新增D 删除B C不变 新增E  变成 D C E
     * //那其实logic 其实只需要做 保留 不变的C 新增原本没有的D E  删除 原本的A B
     *
     * @param
     * @param
     * @return
     */
    @Override
    public void syncLogicDataSetColumns(LogicDataObj logicDataObj, ClassifierStat classifierStat) {


        //拿到logic的字段信息
        List<LogicDataColumn> logicDataColumns = logicDataColumnService.findLogicDataColumnsByLogicDataObjId(logicDataObj.getId());
        //过滤掉非来源本数据对象的字段
        logicDataColumns = logicDataColumns.stream().filter(s -> s.getBelongParentId().equals(classifierStat.getId())).collect(Collectors.toList());

        //拿到物理表的字段信息
        Map<String, StructuralFeature> classifierStatFeatures = classifierStat.getFeatures();

        List<String> addColumns = new ArrayList<>();
        List<String> deleteColumns = new ArrayList<>();
        Set<String> ClassifyStatCodes = classifierStatFeatures.keySet();
        List<String> logicColunmCodes = logicDataColumns.stream().map(s -> s.getCode()).collect(Collectors.toList());
        //找出 新增的 字段
        for (String classifyStatCode : ClassifyStatCodes) {
            //如果在logic的字段里面找不到classifierstat的字段 说明这个字段物理表新增了，如果找到了代表这个不变
            if (!logicColunmCodes.contains(classifyStatCode)) {
                addColumns.add(classifyStatCode);
            }
        }
        //找出要删除的字段
        for (String logicColunmCode : logicColunmCodes) {
            //如果在物理表的字段里面找不到logic的字段，则代表着这个字段被删了
            if (!ClassifyStatCodes.contains(logicColunmCode)) {
                deleteColumns.add(logicColunmCode);
            }
        }

        //删除物理表已经删除的字段
        for (String deleteColumn : deleteColumns) {
            List<LogicDataColumn> collect = logicDataColumns.stream().filter(s -> s.getCode().equals(deleteColumn)).collect(Collectors.toList());
            logicDataColumnService.deleteLogicDataColumnById(collect.get(0).getId());
        }
        //新增物理表新增的字段
        for (String addColumn : addColumns) {
            StructuralFeature structuralFeature = classifierStatFeatures.get(addColumn);
            logicDataColumnService.saveColumns(structuralFeature, logicDataObj, classifierStat.getId());
        }


    }

    @Override
    public List<DataObjectColumnView> getColumn(String dbType, String dataObjId) {
        return dataSourceService.getDataSourceColumn(dbType, dataObjId);
    }

    @Override
    public void moveDwbInstance(String dirId, String dwbId) {
        if (StringUtils.isBlank(dirId)) {
            Assert.fail("目录节点不能为空");
        }
        if (StringUtils.isBlank(dwbId)) {
            Assert.fail("数据源不能为空");
        }
        if (checkName(dirId, dwbId)) {
            Assert.fail("移动的目的目录存在同名数据源,请修改");
        }
        Map<String, String> params = new HashMap<>();  //更新目录节点
        params.put("dirId", dirId);
        params.put("dwbId", dwbId);
        String sql = "update t_md_classify_element set busi_classify_id = :dirId where element_id = :dwbId";
        this.baseDao.executeSqlUpdate(sql, params);
        sql = "update t_md_dw_db_instance set operate_time = :operateTime where id = :dwbId";
        this.baseDao.executeSqlUpdate(sql, addParam("dwbId", dwbId).addParam("operateTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).param());
    }

    private boolean checkName(String dirId, String dwbId) {
        String sql = "        select * from t_md_dw_db_instance a left join t_md_classify_element b on a.id = b.element_id \n" +
                "        where b.busi_classify_id = :dirId and a.name = \n" +
                "                (select name from t_md_element where id = :dwbId)";
        List list = this.baseDao.sqlQueryForList(sql, addParam("dirId", dirId).addParam("dwbId", dwbId).param());
        return list.size() > 0 ? true : false;
    }

    @Override
    public DataSourceVO saveAs(String schemaId, String dbType, String name) {
        DataBaseConnectService connectService = dataBaseFactory.matchDb(dbType);
        return connectService.getDataSourceVo(schemaId, dbType, name);
    }


    /**
     * 从rdb的ID 去找出所生出来的logic集合  在这些logic里面区分出哪一个是最初派生出来的logic，而不是走添加数据集而派生出来的。
     *
     * @param dataObjId
     * @return
     */
    @Override
    public LogicDataObj getLogicobjBelongtoRdb(String dataObjId) {
        List<LogicDataObj> logicDataObjs = logicDataObjService.getLogicDataObjsByOwnerId(dataObjId);

        for (LogicDataObj logicDataObj : logicDataObjs) {
            //为什么能这样子查，因为业务主题那边不是走t_md_classify_element目录这一套，其他都是走这一套
            String sql = "select busi_classify_id from t_md_classify_element where element_id='" + logicDataObj.getId() + "'";
            List<Map> list = this.baseDao.sqlQueryForList(sql);
            if (list.size() == 0) {
                return logicDataObj;
            }
        }
        return null;
    }

    private void updateDWBId(String id, String name) {
        Map<String, String> params = new HashMap<>();
        params.put("id", id);
        params.put("name", name);
        params.put("operateTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        StringBuffer sql = new StringBuffer("UPDATE t_md_dw_db_instance set name = :name , operate_time = :operateTime where id = :id");
        this.baseDao.executeSqlUpdate(sql.toString(), params);
    }

    private String getDWBId(String id, String dbType) {
        Map<String, String> map = new HashMap<>();
        StringBuffer sql = new StringBuffer("select a.dw_db_instance_id from t_dw_db_mapping a,t_md_element b where a.deployed_software = b.id ");
        if (dbType.equalsIgnoreCase("Elasticsearch")
        || dbType.equalsIgnoreCase("kafka")||dbType.equalsIgnoreCase("hbase")) {
            sql.append(" and deployed_software = :id");
        } else {
            sql.append(" and owner_id = :id");
        }
        map.put("id", id);
        return this.baseDao.sqlQueryForValue(sql.toString(), map);
    }

    private List<DataSourceMachineVo> toDataSourceMachineViewFromDeployedComp(List<DeployedComp> deployedCompList) {
        List<DataSourceMachineVo> dataSourceMachineVoList = new ArrayList<>(deployedCompList.size());
        for (DeployedComp deployedComp : deployedCompList) {
            dataSourceMachineVoList.add(toDataSourceMachineViewFromDeployedCom(deployedComp));
        }
        return dataSourceMachineVoList;
    }

    public DataSourceMachineVo toDataSourceMachineViewFromDeployedCom(DeployedComp deployedComp) {
        DataSourceMachineVo dataSourceMachineVo = new DataSourceMachineVo();
        dataSourceMachineVo.setClusterName(deployedComp.getName());
        dataSourceMachineVo.setIsMaster(deployedComp.getIsMaster());
        dataSourceMachineVo.setMachineId(deployedComp.getMachine().getId());
        dataSourceMachineVo.setIp(deployedComp.getMachine().getIpAddress());
        dataSourceMachineVo.setName(deployedComp.getName());
        dataSourceMachineVo.setPort(String.valueOf(deployedComp.getPort()));
        dataSourceMachineVo.setClusterId(deployedComp.getId());
        if ("1".equals(dataSourceMachineVo.getIsMaster())) {
            if (StringUtils.isNotBlank(dataSourceMachineVo.getName()) && !"null".equals(dataSourceMachineVo.getName())) {
                dataSourceMachineVo.setName(dataSourceMachineVo.getName());
            } else {
                dataSourceMachineVo.setName(dataSourceMachineVo.getIp());
            }
        } else {
            dataSourceMachineVo.setName(dataSourceMachineVo.getName());
        }
        return dataSourceMachineVo;
    }

    private List<ChangeColumnVO.ColumnProperty> toColumnProperty(List<ChangeColumnView> changeColumnViewList, final String checkStatus) {
        return changeColumnViewList.stream()
                .filter(changeColumnView -> changeColumnView.getCheckStatus().equals(checkStatus))
                .map(changeColumnView -> {
                    ChangeColumnVO.ColumnProperty columnProperty = new ChangeColumnVO.ColumnProperty();
                    columnProperty.setName(changeColumnView.getName());
                    columnProperty.setCheckStatus(changeColumnView.getCheckStatus());
                    columnProperty.setCode(changeColumnView.getCode());
                    columnProperty.setId(changeColumnView.getId());
                    columnProperty.setType(changeColumnView.getType());
                    columnProperty.setDataType(changeColumnView.getDataType());
                    return columnProperty;
                }).collect(Collectors.toList());
    }

    private DataObjectRequest toDataObjectRequest(DataObjectVO dataObjectVO) {
        DataObjectRequest dataObjectRequest = new DataObjectRequest();
        dataObjectRequest.setKey(dataObjectVO.getKey());
        List<InsertDataObjectView> dataSourceTableList = new ArrayList<>();
        for (String dataObject : dataObjectVO.getDataObjectTableList()) {
            InsertDataObjectView insertDataObjectView = new InsertDataObjectView();
            String[] tmp = dataObject.split(",");
            if (tmp.length >= 2) {
                insertDataObjectView.setCode(tmp[0]);
                insertDataObjectView.setRealUserName(tmp[1]);
            } else {
                insertDataObjectView.setCode(tmp[0]);
            }
            dataSourceTableList.add(insertDataObjectView);
        }
        dataObjectRequest.setDataSourceTableList(dataSourceTableList);
        dataObjectRequest.setInstanceId(dataObjectVO.getInstanceId());
        dataObjectRequest.setDbType(dataObjectVO.getDbType());
        dataObjectRequest.setZhName(dataObjectVO.getZhName());
        dataObjectRequest.setDbType(dataObjectVO.getDbType());
        if (dataObjectVO.getDbType().equalsIgnoreCase(DataSourceType.ELASTICSEARCH.name())) {
            dataObjectRequest.setIndexName(dataObjectVO.getIndexName());
            dataObjectRequest.setInstanceId(dataObjectVO.getDataSourceId());
        } else if (dataObjectVO.getDbType().equalsIgnoreCase(DataSourceType.HBASE.name())) {
            dataObjectRequest.sethBaseInstanceId(dataObjectVO.getDataSourceId());
        }
        dataObjectRequest.setElasticsIsNew(true);
        dataObjectRequest.setRegisterScript(dataObjectVO.getRegisterScript());
        dataObjectRequest.setObjectType(dataObjectVO.getObjectType());
        return dataObjectRequest;
    }

    private DataSourceDTO toDataSourceVo(DataSourceView dataSourceView) {
        DataSourceDTO dataSourceVo = new DataSourceDTO();
        dataSourceVo.setId(dataSourceView.getId());
        dataSourceVo.setDbType(dataSourceView.getDatasourceType());
        dataSourceVo.setExistsSchema(DbTypeValidator.isRdb(dataSourceView.getDatasourceType()));
        dataSourceVo.setDbName(dataSourceView.getName());
        dataSourceVo.setInstanceName(dataSourceView.getInstanceName());
        dataSourceVo.setIp(dataSourceView.getHostMachineIp());
        dataSourceVo.setUsername(dataSourceView.getUsername());
        dataSourceVo.setPassword(dataSourceView.getPassword());
        dataSourceVo.setAccessId(dataSourceView.getAccessId());
        dataSourceVo.setAccessKey(dataSourceView.getAccessKey());
        dataSourceVo.setPort(dataSourceView.getPort());
        dataSourceVo.setDbCode(dataSourceView.getInstanceName());
        dataSourceVo.setVersion(dataSourceView.getDatasourceVersion());
        dataSourceVo.setZookeeperId(dataSourceView.getZookeeperId());
        dataSourceVo.setZookeeperHome(dataSourceView.getZookeeperHome());
        dataSourceVo.setContextPath(dataSourceView.getContextPath());
        dataSourceVo.setAlias(dataSourceView.getAlias());
        dataSourceVo.setEndpoint(dataSourceView.getEndpoint());
        dataSourceVo.setTunnelEndpoint(dataSourceView.getTunnelEndpoint());
        dataSourceVo.setCreateTime(dataSourceView.getCreateTime());
        dataSourceVo.setSolrId(dataSourceView.getSolrId());
        dataSourceVo.setHbaseId(dataSourceView.getHbaseId());
        List<SchemaVo> schemaVoList = toSimpleSchemaVoList(dataSourceView.getSchemaViewList());
        for (SchemaVo schemaVo : schemaVoList) {
            if (schemaVo.getUserName().equals(dataSourceVo.getUsername())) {
                dataSourceVo.setJndiName(schemaVo.getJndiName());
            }
        }
        for (DataSourceSchemaView dataSourceSchemaView : dataSourceView.getSchemaViewList()) {
            String jdbcUrl = dataSourceSchemaView.getJdbcUrl();
            if (dataSourceVo.getUsername().equals(dataSourceSchemaView.getUsername())) {
                dataSourceVo.setJdbcUrl(jdbcUrl);
            }
        }
        dataSourceVo.getSchemaVoList().addAll(schemaVoList);
        return dataSourceVo;
    }

    private List<SchemaVo> toSimpleSchemaVoList(List<DataSourceSchemaView> dataSourceSchemaViewList) {
        return dataSourceSchemaViewList.stream().map(dataSourceSchemaView -> {
            SchemaVo schemaVo = new SchemaVo();
            schemaVo.setUserName(dataSourceSchemaView.getUsername());
            schemaVo.setPassword(dataSourceSchemaView.getPassword());
            schemaVo.setSchemaId(dataSourceSchemaView.getSchemaId());
            schemaVo.setJndiName(dataSourceSchemaView.getJndiName());
            return schemaVo;
        }).collect(Collectors.toList());
    }

    @Override
    public Map<String, String> getAllHouseKeeperType() {
        return HouseKeeperEnum.getAllTypes();
    }

    @Override
    public List<DataSourceKeepVo> findDataObjByType(String dbType) {
        HouseKeeperService bean = getBean(dbType);
        return bean.templateAssembly(dbType.toUpperCase());

    }

    @Override
    public List<DbTableDTO> getDbTableByCode(String id, String type) {
        List<DbTableDTO> rtValue = new ArrayList<>();
        StringBuffer sql = new StringBuffer();
        sql.append("select id, name, code from t_md_element  ");
        sql.append("where owner_id = :id ");
        ModelElement modelElement = (ModelElement) this.baseDao.get(ModelElement.class, id);
        if (Objects.equals(modelElement.getType(), "ElasticSearchInstance")) {
            sql.append(" and type = 'ElasticSearchIndex'");
        }
        if (Objects.equals(type, "addTable")) {
            sql.append(" and id not in (select classifier_stat_id from t_dw_table_mapping) ");
        }

        List<Map<String, String>> elements = this.baseDao.sqlQueryForList(sql.toString(), addParam("id", id).param());
        for (Map<String, String> element : elements) {
            DbTableDTO dbTableDTO = new DbTableDTO();
            dbTableDTO.setCode(element.get("code"));
            dbTableDTO.setName(element.get("name"));
            dbTableDTO.setId(element.get("id"));
            rtValue.add(dbTableDTO);
        }
        return rtValue;
    }

    @Override
    public void overlaySynchronization(String id) {
        TypeMapping typeMapping = (TypeMapping) beanFactory.getBean("typeMapping");
        List<Map<String, String>> childrenLogic = getChildrenLogic(id);
        for (Map<String, String> stringStringMap : childrenLogic) {
            Map<String, List<LogicHttpColumns>> syncColumns = dataSetSyncService.getSyncColumns(stringStringMap.get("id"), typeMapping);
            editService.syncColumns(syncColumns);
        }
    }

    @Override
    public void deleteDwFromMetdata(String elementId) {
        if (judgeChildren(elementId)) {
            Assert.fail("该数据源存在数据对象，无法删除");
        } else {
            String deleteClassifyElementSQL = "DELETE FROM T_MD_CLASSIFY_ELEMENT a WHERE a.element_id = '" + elementId + "'";
            this.baseDao.executeSqlUpdate(deleteClassifyElementSQL);

            String deleteDWDBMappingSQL = "DELETE FROM T_DW_DB_MAPPING WHERE DW_DB_INSTANCE_ID='" + elementId + "'";
            this.baseDao.executeSqlUpdate(deleteDWDBMappingSQL);

//        //删除与标准表的关联中间表：datacenter少一张表
            String deleteStandTableMapSQL = " DELETE FROM t_dw_table_mapping WHERE DW_DB_ID='" + elementId + "'";
            this.baseDao.executeSqlUpdate(deleteStandTableMapSQL);

            String deleteDWDBInstanceSQL = " DELETE FROM T_MD_DW_DB_INSTANCE WHERE ID='" + elementId + "'";
            this.baseDao.executeSqlUpdate(deleteDWDBInstanceSQL);

            String deleteSysfuncodeSQL = " DELETE FROM t_sys_auth_obj_func WHERE FUNC_CODE = '" + elementId + "'";
            this.baseDao.executeSqlUpdate(deleteSysfuncodeSQL);

            String deleteSysfunc = " DELETE FROM T_SYS_FUNC WHERE FUNC_CODE = '" + elementId + "'";
            this.baseDao.executeSqlUpdate(deleteSysfunc);

        }
    }

    @Override
    public void syncFromChildrenToParent(String logicId) {
        LogicDataObj logicDataObj = (LogicDataObj) this.baseDao.get(LogicDataObj.class, logicId);
        String dbType = logicDataObj.getDbType();
        String logicType = getLogicType(logicDataObj.getOwnerId());
        if (Objects.equals(logicType, "数据建模中心")) {
            ChangeColumnVO columnVO = findChangeColumns(logicDataObj.getOwnerId(), dbType);
            String key = columnVO.getKey();
            syncColumn(key, logicDataObj.getOwnerId(), dbType);
        }

    }

    @Override
    public Map<String, List<LogicHttpColumns>> findChangeColumnsForParents(String dbObjId, String dbType) {
        Map<String, List<LogicHttpColumns>> rtValue = new HashMap<>();
        FindChangeColumnResponse findChangeColumnResponse = dataObjectService.queryChangeColumnByFlag(dbObjId, dbType, true);// true表示使用新版es表结构
        rtValue.put("ADD", changeToLogic(toColumnProperty(findChangeColumnResponse.getResultTable(), "ADD")));
        rtValue.put("DELETE", changeToLogic(toColumnProperty(findChangeColumnResponse.getResultTable(), "DELETE")));
        rtValue.put("EDIT", changeToLogic(toColumnProperty(findChangeColumnResponse.getResultTable(), "UPDATE")));
        return rtValue;
    }

    @Override
    public Map<String, List<LogicHttpColumns>> getSyncColumns(String logicId) {
        LogicDataObj logicDataObj = (LogicDataObj) this.baseDao.get(LogicDataObj.class, logicId);
        String dbType = logicDataObj.getDbType();
        String logicType = getLogicType(logicDataObj.getOwnerId());
        TypeMapping typeMapping = (TypeMapping) beanFactory.getBean("typeMapping");
        if (Objects.equals(logicType, "数据建模中心")) {
            ChangeColumnVO columnVO = findChangeColumns(logicDataObj.getOwnerId(), dbType);
            return dataSetSyncService.getSysColumnsForParents(logicId, DataSetUtils.changeLogicToCat(columnVO), typeMapping);
        } else if (Objects.equals(logicType, "大数据管家")) {
            Map<String, List<LogicHttpColumns>> syncColumns = dataSetSyncService.getSyncColumns(logicId, typeMapping);
            editService.syncColumns(syncColumns);
            return syncColumns;
        }
        return null;
    }

    @Override
    public void syncChidren(String logicId) {
        TypeMapping typeMapping = (TypeMapping) beanFactory.getBean("typeMapping");
        Map<String, List<LogicHttpColumns>> syncColumns = dataSetSyncService.getSyncColumns(logicId, typeMapping);
        editService.syncColumns(syncColumns);
    }

    private String getLogicType(String classId) {
        String sql = "select source from t_dw_table_mapping a left join t_md_dw_db_instance b on a.dw_db_id = b.id where classifier_stat_id = :id";
        return this.baseDao.sqlQueryForValue(sql, addParam("id", classId).param());
    }

    private List<LogicHttpColumns> changeToLogic(List<ChangeColumnVO.ColumnProperty> columns) {
        List<LogicHttpColumns> logicHttpColumns = new ArrayList<>();
        for (ChangeColumnVO.ColumnProperty column : columns) {
            LogicHttpColumns logicHttpColumn = new LogicHttpColumns();
            logicHttpColumn.setName(column.getName());
            logicHttpColumn.setCode(column.getCode());
            logicHttpColumns.add(logicHttpColumn);
        }
        return logicHttpColumns;
    }

    private boolean judgeChildren(String id) {
        String sql = " SELECT COUNT(*) FROM T_DW_TABLE_MAPPING WHERE DW_DB_ID ='" + id + "'";
        return Integer.valueOf(this.baseDao.sqlQueryForValue(sql)) > 0 ? true : false;
    }

    private List<Map<String, String>> getChildrenLogic(String id) {
        StringBuffer sql = new StringBuffer();
        sql.append("select id from t_md_logic_dataobj where owner_id = :id");
        return this.baseDao.sqlQueryForList(sql.toString(), addParam("id", id).param());
    }

    private HouseKeeperService getBean(String dbType) {
        HouseKeeperEnum houseKeeperEnum = HouseKeeperEnum.getPreByType(dbType);
        return beanFactory.getBean(houseKeeperEnum.getPre() + "HouseKeeperService", HouseKeeperService.class);
    }

    private boolean isExitNameDw(String name, String dwid) {
        String sql = "select * from t_md_dw_db_instance t1 left join t_md_classify_element t2 on t1.id = t2.element_id \n" +
                "where t1.name = :name  and t2.busi_classify_id =(select busi_classify_id from t_md_classify_element where element_id = :id )\n" +
                "and t1.id != :id";
        if (this.baseDao.sqlQueryForList(sql, addParam("id", dwid).addParam("name", name).param()).size() > 0) {
            return true;
        }
        return false;
    }

    @Override
    public void saveDataObjNew(NewDataObjectVO newDataObjectVO) {

        //直接用之前的接口来拼接
        List<DataObjectView> dataObjectViews= dataObjectService.insertDataObject(toDataObjectRequest(newDataObjectVO.getDataObjectVO()));

        List<String> dataObjects = dataObjectViews.stream().map(DataObjectView::getDataObjId).collect(Collectors.toList());
        dataWarehousePlanService.addTableNew(newDataObjectVO.getDataBaseId(), dataObjects, newDataObjectVO.getUserId());

        //组装name、code
        List<DataObjectVo> dataObjectVos=new ArrayList<>();
        DataObjectVo vo;
        for (String s : dataObjects) {
            vo=new DataObjectVo();
            vo.setCode(s);
            vo.setName(s);
            dataObjectVos.add(vo);
        }

        newDataObjectVO.getDataSetAuthVo().setDataObjectVos(dataObjectVos);

        dataSetAuthService.saveDataSetAuth(newDataObjectVO.getDataSetAuthVo().getDataObjectVos(), newDataObjectVO.getUserId());

        dataSetAuthService.saveFirstAuthNew(newDataObjectVO.getDataSetAuthVo());

    }

    @Override
    public String getSchemaIdByCatalogId(String catalogId) {
        String sql = " select id from t_md_rdb_schema where catalog_id =:catalogId ";
      return  this.baseDao.sqlQueryForValue(sql,addParam("catalogId",catalogId).param());
    }
}
