package com.dragonsoft.cicada.datacenter.modules.metadata.service;

import com.code.common.utils.StringUtils;
import com.code.metaservice.datawarehouse.model.DataSourceVO;
import com.code.metaservice.res.app.zk.IZkInstanceService;
import com.code.metaservice.res.request.datasource.DataSourceRequest;
import com.code.metaservice.res.response.datasource.InsertDataSourceResponse;
import com.code.metaservice.res.semistructured.hbase.HbaseInstanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> Avizii
 * @Create : 2021.03.29
 */
@Service("hbaseConnectService")
public class HbaseConncetService extends DataBaseConnectService {

    @Autowired
    private HbaseInstanceService hbaseInstanceService;

    @Autowired
    private IZkInstanceService zkInstanceService;

    @Override
    public boolean testConnection(DataSourceVO dataSourceVO) {
        return hbaseInstanceService.testConnection(toDataSourceRequest(dataSourceVO));
    }

    @Override
    public InsertDataSourceResponse insertDataSource(DataSourceVO dataSourceVO) {
        String hbaseVersion = dataSourceVO.getSoftwareId();

        // insert zookeeper
        dataSourceVO.setSoftwareId(dataSourceVO.getZookeeperVersion());

        InsertDataSourceResponse zkInstance = zkInstanceService._saveZkInstance(toDataSourceRequest(dataSourceVO));// 新开一个事务
        dataSourceVO.setZookeeperId(zkInstance.getInstanceId());
        dataSourceVO.setSoftwareId(hbaseVersion);
        return hbaseInstanceService.insertResource(toDataSourceRequest(dataSourceVO));
    }

    @Override
    public void updateResource(DataSourceVO dataSourceVO) {
        DataSourceRequest ds = toDataSourceRequest(dataSourceVO);
        if(ds.getZookeeperId()!=null&& StringUtils.isNotBlank(ds.getZookeeperId())){
            ds.setId(ds.getZookeeperId());
            zkInstanceService._updateResourceInfo(ds);
            ds.setId(dataSourceVO.getId());
            hbaseInstanceService.updateResourceInfo(ds);
        }


    }

    @Override
    protected DataSourceVO getDataSourceInfo(String schemaId) {
        return null;
    }

    public String _saveZkInstance(DataSourceVO dataSourceVO) {
        InsertDataSourceResponse zkSource = zkInstanceService.insertResource(toDataSourceRequest(dataSourceVO));
        return zkSource.getInstanceId();
    }
}
