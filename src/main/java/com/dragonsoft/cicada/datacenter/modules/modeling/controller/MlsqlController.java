package com.dragonsoft.cicada.datacenter.modules.modeling.controller;

import com.code.common.utils.R;
import com.code.metaservice.variable.ITransVariableService;
import com.dragoninfo.dfw.bean.Result;
import com.dragonsoft.cicada.datacenter.modules.log.service.IMlsqlElasticLogSerivce;
import com.dragonsoft.cicada.datacenter.modules.modeling.service.MLSQLService;
import com.dragonsoft.cicada.datacenter.modules.modeling.service.MlsqlLogManageService;
import com.dragonsoft.cicada.datacenter.modules.modeling.vo.JobExecuteInfo;
import com.dragonsoft.cicada.datacenter.modules.modeling.vo.TransStatus;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpSession;
import java.sql.Timestamp;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@CrossOrigin
@RestController
@Api(value = "MlsqlController|MLSQL执行控制器")
@RequestMapping(value = "/mlsql")
@Slf4j
public class MlsqlController {

    @Resource
    private IMlsqlElasticLogSerivce elasticseatchSerivce;

    @Autowired
    MLSQLService mlsqlService;

    @Autowired
    private MlsqlLogManageService mlsqlLogManageService;

    @Autowired
    ITransVariableService transVariableService;

    @Value("${script.sqlLimit:10}")
    private String scriptLimit;

    @ApiOperation(value = "脚本补全校验")
    @ApiImplicitParam(name = "map", value = "", required = true)
    @PostMapping("/complementSQL")
    public Result complementSQL(@RequestParam Map map) {
        try {
            map.put("executeMode", "autoSuggest");
            return Result.toResult(R.ok(mlsqlService.runScript(map)));
        } catch (Exception e) {
            return Result.toResult(R.error(e.getMessage()));
        }
    }

    @ApiOperation(value = "执行mlsql脚本")
    @ApiImplicitParam(name = "mlsql", value = "mlsql脚本", required = true, dataType = "String")
    @PostMapping("/runScript")
    public Result runScript(@RequestBody Map mlsql, HttpSession session) {
        String userId = (String) session.getAttribute("userId");
        String sqlBak = (String) mlsql.get("sql");
        String sql = "";
        String result = "";
        sql = (String) mlsql.get("sql");
        String limitSql = getLimitSql(sql);

        String transStepId = (String) mlsql.get("transStepId");
        limitSql = transVariableService.conversionVariable(transStepId, limitSql, userId);
        mlsql.put("sql", limitSql);
        mlsql.remove("transStepId");
        try {
            System.out.println("limitSql:" + limitSql);
            result = mlsqlService.runScript(mlsql);
            return Result.toResult(R.ok(result));
        } catch (Exception e) {
            log.error("sqlBak:" + sqlBak+" \n"+e.getMessage(),e);
            sql = transVariableService.conversionVariable(transStepId, sqlBak, userId);
            mlsql.put("sql", sql);
            result = mlsqlService.runScript(mlsql);
            return Result.toResult(R.ok(result));
        }
    }

    private String getLimitSql(String sql) {
        String[] split = sql.split(";");
        StringBuilder newSql = new StringBuilder();
        for (String lineSql : split) {
            // lineSql = lineSql.replaceAll("\n", " ");
            lineSql = lineSql.replaceAll(" AS ", " as ").replaceAll(" As ", " as ").replaceAll(" aS ", " as ");
            String lineSqlNew = getFromAsLimitSql(lineSql);
            // lineSqlNew = getLoadLimitSql(lineSqlNew);

            lineSqlNew += ";";
            newSql.append(lineSqlNew);
        }
        return newSql.toString();
    }

    private String getLoadLimitSql(String lineSql) {
        Pattern scriptPattern = Pattern.compile("(?<=load ).*?(?= as) ", Pattern.CASE_INSENSITIVE);
        return matchingOption(scriptPattern, lineSql);

    }

    private String getFromAsLimitSql(String lineSql) {
        Pattern scriptPattern = Pattern.compile("(?<=from ).*?(?= as) ", Pattern.CASE_INSENSITIVE);
        return matchingOption(scriptPattern, lineSql);
    }

    private String matchingOption(Pattern scriptPattern, String lineSql) {
        Matcher matcher = scriptPattern.matcher(lineSql);
        String matchingValue = "";
        while (matcher.find()) {
            matchingValue = matcher.group();
        }
        if(matchingValue.contains(" limit ")){
            return matcher.replaceAll(" "+matchingValue);
        }
        return matcher.replaceAll(" " + matchingValue + " limit " + scriptLimit + " ");
    }


    @ApiOperation(value = "查看正在运行的任务")
    @GetMapping("/runningJob")
    public Result runningJob() {
        return Result.toResult(R.ok(mlsqlService.runningJobs()));
    }

    @ApiOperation(value = "获取表字段")
    @ApiImplicitParam(name = "tableName", value = "表名", required = true, dataType = "String")
    @GetMapping("/getSchema")
    public Result getSchema(String tableName) {
        return Result.toResult(R.ok(mlsqlService.runScript("!desc " + tableName + ";")));
    }

    @ApiOperation(value = "查看正在任务列表")
    @GetMapping("/showJobs")
    public Result showJobs() {
        return Result.toResult(R.ok(mlsqlService.runScript("!show jobs;")));
    }

    @ApiOperation(value = "杀死正在执行的任务")
    @GetMapping("/killJob")
    public Result killJob(String id) {
        return Result.toResult(R.ok(mlsqlService.killJob(id)));
    }

    @GetMapping("/showResource")
    public Result showResource() {
        return Result.toResult(R.ok(mlsqlService.runScript("!show resource;")));
    }

    @GetMapping("/showResourceById")
    public Result showResourceById(String id) {
        return Result.toResult(R.ok(mlsqlService.runScript("!show \"resource/" + id + "\";")));
    }

    @GetMapping("/showJobDetail")
    public Result showJobDetail(String id) {
        return Result.toResult(R.ok(mlsqlService.runScript("!show \"job/" + id + "\";")));
    }


    @GetMapping("/run")
    @ApiImplicitParam(name = "id", value = "方案id", required = true, dataType = "String")
    public Result run(String id) {
        mlsqlService.runJob(id);
        return Result.success();
    }

    @GetMapping("/runTest")
    public void test01() {
        mlsqlService.runJob("e8807d8efe884b609ed2134cd65198fe");
    }

    @GetMapping("getAfterTableName")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "方案id", required = true, dataType = "String"),
            @ApiImplicitParam(name = "stepId", value = "当前步骤id", required = true, dataType = "String")
    })
    public Result getAfterTableName(String id, String stepId, HttpSession session) {
        try {
            String userId = (String) session.getAttribute("userId");
            Map<String, String> resultMap = mlsqlService.getAfterTableName(id, stepId, false, scriptLimit, userId);
            Map<String, Object> paramMap = Maps.newHashMap();
            paramMap.put("sql", resultMap.get("sql"));
            paramMap.put("includeSchema", true);
            paramMap.put("onlySchema", true);
            List columns = Lists.newArrayList();
            String tableColumn = mlsqlService.runScript(paramMap);
            JSONObject obj = (JSONObject) JSONObject.fromObject(tableColumn).get("schema");
            JSONArray jsonArray = (JSONArray) obj.get("fields");
            jsonArray.forEach(e -> {
                JSONObject objFields = JSONObject.fromObject(e);
                columns.add(objFields.get("name"));
            });
            resultMap.put("columns", columns.toString());
            return Result.toResult(R.ok(resultMap));
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return Result.toResult(R.error(e.getMessage()));
        }
    }

    @GetMapping("/test")
    public Result test() {
        return Result.success();
    }

    @ApiOperation(value = "根据taskId和组id查找日志")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "开始时间，yyyy-MM-dd HH:mm:ss", value = "startTime", required = true, dataType = "String"),
            @ApiImplicitParam(name = "结束时间，yyyy-MM-dd HH:mm:ss", value = "endTime", required = true, dataType = "String"),
            @ApiImplicitParam(name = "任务id，逗号隔开", value = "tasksString", required = true, dataType = "String"),
            @ApiImplicitParam(name = "组id", value = "groupId", required = true, dataType = "String")
    })
    @GetMapping("/getLogsByTastsId")
    public Result getLogsByTastsId(String startTime, String endTime, String tasksString, String groupId) {
        List<Map<String, Object>> search = elasticseatchSerivce.searchLog(startTime, endTime, tasksString, groupId);
        return Result.toResult(R.ok(search));
    }

    @ApiOperation(value = "根据组id查找日志")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "组id", value = "groupId", required = true, dataType = "String")
    })
    @GetMapping("/getLogsByGroupId")
    public Result getLogsByGroupId(String groupId) {
        List<Map<String, Object>> search = elasticseatchSerivce.searchLog(groupId);
        return Result.toResult(R.ok(search));
    }

    //旧版本日志,可以删，20210316
    @Deprecated
    @ApiOperation(value = "根据jobid查找子任务")
    @ApiImplicitParam(name = "groupId", value = "groupId", required = true, dataType = "String")
    @GetMapping("/getSubtasksByJobId")
    public Result getSubtasksByJobId(String groupId) {
        JSONArray jsonArray = JSONArray.fromObject(mlsqlService.getSubtasksByJobId(groupId));
        List<JobExecuteInfo> list = JSONArray.toList(jsonArray, JobExecuteInfo.class);
        return Result.toResult(R.ok(list));
    }

    //旧版本日志，可以删，20210316
    @Deprecated
    @ApiOperation(value = "查找具体子任务跳转的Url")
    @ApiImplicitParam(name = "jobId", value = "jobId", required = true, dataType = "String")
    @GetMapping("/getJumpLogUrl")
    public Result getJumpLogUrl(String jobId,String groupId) {
        return Result.toResult(R.ok(mlsqlService.getJumpLogUrl(jobId,groupId)));
    }

    /**
     * 查看方案执行状态
     *
     * @param transId
     * @return
     */
    @GetMapping(value = "/jobStatus")
    public Result getTransStatusById(String transId) {
        TransStatus status = null;
        try {
            double time = 0.0;
            status = mlsqlService.execStatus(transId);
            Timestamp end_time = status.getEndTime();
            Timestamp start_time = status.getBeginTime();
            if (null != end_time && null != start_time)
                time = ((double) end_time.getTime() - (double) start_time.getTime()) / 1000;
            Map<String, Object> hashMap = Maps.newHashMap();
            hashMap.put("runTime", time + "秒");
            hashMap.put("status", status);
            return Result.toResult(R.ok(hashMap));
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return Result.toResult(R.error());
        }
    }

    /**
     * 删除任务日志
     *
     * @param taskId
     * @return
     */
    @GetMapping(value = "/transDeleteHistoryTaskLog")
    public Result transDeleteHistoryTaskLog(String taskId) {
        mlsqlService.transDeleteHistoryTaskLog(taskId);
        return Result.success();
    }
}
