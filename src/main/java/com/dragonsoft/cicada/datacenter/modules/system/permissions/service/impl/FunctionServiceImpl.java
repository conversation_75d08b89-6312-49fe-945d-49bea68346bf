package com.dragonsoft.cicada.datacenter.modules.system.permissions.service.impl;

import cn.hutool.core.util.StrUtil;
import com.dragoninfo.dfw.entity.TSysAuthObjFunc;
import com.dragoninfo.dfw.entity.TSysFunc;
import com.dragoninfo.dfw.entity.TSysFuncBase;
import com.dragoninfo.dfw.service.SysAuthObjFuncService;
import com.dragoninfo.dfw.service.SysAuthObjService;
import com.dragoninfo.dfw.service.SysFuncService;
import com.dragonsoft.cicada.datacenter.modules.system.common.vo.TreeVo;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.service.IFunctionService;
import com.fw.service.BaseService;
import com.fw.service.annotation.Service;
import com.google.common.collect.Maps;
import org.apache.avro.specific.AvroGenerated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/5/27
 */
@Service
public class FunctionServiceImpl extends BaseService implements IFunctionService {

    private static final List<String> PARENT_NODE = Arrays.asList("dataWarehousePlan", "udf", "mlSql", "busManagement", "portal");

    //inputOutputDirCicadaServiceInputMeta
    private static final List<String> CHILDREN_NODE = Arrays.asList("modelServiceGenerateAPI", "modelServiceTest", "modelServiceGetResultTest", "AIModelingTimedTask","processModelingSaseSettingPage","scenarioCaseAiModel","accessBusManagementMsgInfoDelete","accessBusManagementMsgInfoDetails","accessBusManagementMsgInfoEdit","accessBusManagement","AIModeling","AIModelingDelete","AIModelingMove","AIModelingSaveAs","AIModelingReName","AIModelingEdit","AIModelingStartModel","AIModelingCreateModel","accessBusManagementSaved","accessBusManagementAddNewService","accessBusManagementAddServiceInterface","accessBusManagementDelete","accessBusManagementDetails","accessBusManagementEdit","logQueryViewDetails","logQuery","scenarioCaseFunc","scenarioCaseDataModel","scenarioCaseDashboard","scenarioCaseGateway","dataConnectionMetadata","dataSetOperationSaveAs","dataSetOperationCreateAPI","serviceManagementStart","fastAnalysisDeleteTrans","fastAnalysisCopyTrans","fastAnalysisUpdateTransName","fastAnalysisEdit","fastAnalysisSaveTempTrans","fastAnalysisQueryTransTree","fastAnalysis","serviceManagementTest","serviceManagementSharing","serviceManagementDeactivate","serviceManagementUninstallg","serviceManagementBatchSharing","serviceManagementGenerateAPI","serviceManagement","dataSetOperationMoveLogicDataSet","dataConnectionMove","dataConnectionSaveAs","dataConnectionDeleteTable","processModeling","dashboard","themePortal","dataSetOperation","dataConnection","udfOperatorManage","userManagement","roleManagement","groupManagement","processModelingDeleteTrans", "processModelingCopyTrans", "processModelingSaveTempTrans", "processModelingGetDataSets", "processModelingStartJob","processModelingQueryTransTree",
            "processModelingResultReuseObj", "processModelingUpdateTransName", "dashboardUpdateGroup","visualEditGetNoChart","dashboardSaveOrUpdate",
            "dashboardDelete", "dashboardCopy","dashboardGetListByGroupId", "dashboardGetDashboard", "roleManagementQueryRole","roleManagementSetAuth","roleManagementUpdataRole","roleManagementDeleteRole","roleManagementAddRole","userManagementAddUser","userManagementQueryUser",
            "userManagementEditUser","userManagementDeleteUser","userManagementResetPassword", "groupManagementAddUserGroup","groupManagementQueryGroup","groupManagementDeleteUserGroup","groupManagementEditUserGroup","dataSetOperationAccreditLogicDataObj","dataSetOperationPreviewData",
            "dataSetOperationSetBatchSharing","dataSetOperationSetSingleShare","dataSetOperationDataColumn","dataSetOperationDeleteDataSet", "dataConnectionTestConnection","dataConnectionAddDataTable", "dataConnectionEditDataSource","dataConnectionDeleteDataSource","dataConnectionSynchronousDataSource",
            "dataWarehouseDeleteTable","dataConnectionAddDataSource","udfOperatorManageCreateUdf","udfOperatorManageEditUdf","udfOperatorManageDeleteUdf","themePortal","themePortalDeletePortal","themePortalGetPortalPageByUrl","themePortalCreatePortal","themePortalUpDataPortal","serviceManagementedit","AIModelingLookOver","dataMiningLookOver","AIModelingBuildApi","AIModelingTestApi","serviceManagementLookOver",
            "inputOutputDirCicadaFileOutputMeta","inputOutputDirCicadaFileInputMeta","inputOutputDirCicadaStandardSqlOutput","inputOutputDirCicadaFullTextOutput","dataServiceDirCicadaMetaServiceInput","dataServiceDirCicadaMetaServiceOutput","dataScreeningDirCicadaDataDistinctMeta","dataScreeningDirCicadaConditionFilterPlugin","dataScreeningDirCicadaFieldFilteringMeta","dataCollisionDirCicadaCollisionPlugin","dataCollisionDirCicadaUnionJoinPlugin","dataCollisionDirCicadaSubtractByKeyPlugin","dataProcessDirCicadaServiceOrganization","dataProcessDirCicadaObjectToJsonMeta","dataProcessDirServiceOrganization","dataProcessDirCicadaFieldsSettings","dataProcessDirCicadaDataSortPlugin","dataAnalysisDirCicadaLabelMeta","dataAnalysisDirCicadaReducePlugin","dataAnalysisDirCicadaDateCicadaZipperTablePlugin","dataAnalysisDirCicadaNumberCicadaZipperTablePlugin","dataAnalysisDirCicadaScriptMeta","dataAnalysisDirCicadaJsonParsingContent","dataBusinessDirCicadaPeerContentMeta","dataBusinessDirCicadaMarkingTimeMeta","dataBusinessDirCicadaEffectivePolice","cicadaLlmPluginMeta","dataExportFileDownload","dataExportPreview","serviceTypeDataQuery","serviceTypeInformationVerification","serviceTypeCompareService","serviceTypeDataCollision","serviceTypeDataCollision","serviceTypeAiService","serviceType","dataExport","dataBusinessDir","dataAnalysisDir","dataProcessDir","dataCollisionDir","dataScreeningDir","dataServiceDir","inputOutputDir","dataServiceDirCicadaMetaServiceCheckOutPut","dataProcessDirCicadaCodeTableConversion",
            "publishService","testOnline","useCaseManagement","serviceTypeModelAnalysis",
            "testOnlineTestSingleService","testOnlineTestBatchService","testOnlineImportUseCase",
            "useCaseManagementAddUseCase","useCaseManagementRunUseCase","useCaseManagementEditUseCase","useCaseManagementDeleteUseCase",
            "dataSetOperationAddBatchLogicObj","serviceSpaceImportAndExport","serviceSpaceImport","serviceSpaceExport","modelSpaceImportAndExport",
            "modelSpaceImport","modelSpaceExport","serviceManagementMove","dataBusinessDirCicadaSpecialBusinessMeta","cicadaFieldsSortMeta","analysisResultLibraryOutPutMeta");

    //纯功能代码  无权限关系
    private static final List<String> EXP_NODE = Arrays.asList();

    @Autowired
    private SysFuncService sysFuncService;

    @Autowired
    private SysAuthObjFuncService sysAuthObjFuncService;

    @Autowired
    private SysAuthObjService sysAuthObjService;

    @Value("${help-doc.ip}")
    private String helpDocIp;

    @Value("${help-doc.ip-front}")
    private String helpDocFrontIp;

    @Value("${dataCenter.lastVersion}")
    private String dcVersion;

    @Value("${system.otherSpace.toAdmin:false}")
    private boolean otherSpaceToAdmin;

    @Override
    public List<TreeVo> queryFunctionTree(String systemType) {
        Map<String, String> params = Maps.newHashMap();
        params.put("FUNC_TYPE", "0");
        params.put("ENABLE_STATE", "1");
        List<TSysFunc> allTSysFunctions = sysFuncService.queryList(params);
        List<TSysFunc> firstFunctions = allTSysFunctions.stream().filter(s -> null == s.gettSysParentFunc()
                && !PARENT_NODE.contains(s.getFuncCode())).collect(Collectors.toList());
        List<TSysFunc> notFirstFunctions = allTSysFunctions.stream().filter(s -> null != s.gettSysParentFunc()
                && CHILDREN_NODE.contains(s.getFuncCode())).collect(Collectors.toList());
        List<TreeVo> treeVos = setTree(firstFunctions, notFirstFunctions, systemType);

        for (TreeVo treeVo : treeVos) {
            if (treeVo.getCode().equals("systemManagement")) {
                Iterator<TreeVo> iterator = treeVo.getChildren().iterator();
                while (iterator.hasNext()) {
                    TreeVo next = iterator.next();
                    if (next.getCode().equals("accessBusManagement")) {
                        iterator.remove();
                    }
                }

            }
        }

        return treeVos;
    }

    @Override
    public List<TreeVo> queryExpFunctionTree() {
        Map<String, String> params = Maps.newHashMap();
        params.put("FUNC_TYPE", "0");
        List<TSysFunc> allTSysFunctions = sysFuncService.queryList(params);
        List<String> allFuncCodes = new ArrayList<>();
        allFuncCodes.addAll(CHILDREN_NODE);
        List<Map<String,String>> list = this.baseDao.sqlQueryForList("select func_code from t_sys_func_exp ");
        for (Map<String, String> map : list) {
            allFuncCodes.add(map.get("func_code"));
        }
        //allFuncCodes.addAll(EXP_NODE);
        List<TSysFunc> firstFunctions = allTSysFunctions.stream().filter(s -> null == s.gettSysParentFunc() && !PARENT_NODE.contains(s.getFuncCode())).collect(Collectors.toList());
        List<TSysFunc> notFirstFunctions = allTSysFunctions.stream().filter(s -> null != s.gettSysParentFunc() && allFuncCodes.contains(s.getFuncCode())).collect(Collectors.toList());
        return setTree(firstFunctions, notFirstFunctions, "standard");
    }

    @Override
    public void deleteFuncAndFuncRelation(String funcId) {
        Map<String, String> params = Maps.newHashMap();
        params.put("func_code", funcId);
        List<TSysAuthObjFunc> tSysAuthObjFuncs = sysAuthObjFuncService.queryList(params);
        for (TSysAuthObjFunc tSysAuthObjFunc : tSysAuthObjFuncs) {
            sysAuthObjService.deleteAuthObj(tSysAuthObjFunc);
        }

        Map<String, String> functionParams = Maps.newHashMap();
        functionParams.put("func_code", funcId);
        List<TSysFuncBase> funcBases = sysFuncService.queryList(functionParams);
        for (TSysFuncBase funcBase : funcBases) {
            sysAuthObjService.deleteAuthObj(funcBase);
        }
    }

    @Override
    public List<TreeVo> queryShowDocFuncCodes() {
        Map<String,String> params = Maps.newHashMap();
        //params.put("isDocShow", "1");
        params.put("FUNC_TYPE", "0");
        List<TSysFunc> funcBases =  sysFuncService.queryList(params);
        List<TSysFunc> firstFunctions = funcBases.stream().filter(s -> null == s.gettSysParentFunc() && !PARENT_NODE.contains(s.getFuncCode())).collect(Collectors.toList());
        List<TSysFunc> notFirstFunctions = funcBases.stream().filter(s -> null != s.gettSysParentFunc() && "1".equals(s.getIsDocShow())).collect(Collectors.toList());
        return setTree(firstFunctions, notFirstFunctions, "standard");
    }

    @Override
    public Map<String, Object> queryShowDocMsg() {
        Map<String,Object> map = new HashMap<>();
        map.put("helpDocIp",helpDocIp);
        map.put("helpDocFrontIp",helpDocFrontIp);
        String[] split = dcVersion.split(",");
        String lastVersion = split[split.length - 1];
        map.put("dcVersion",lastVersion);
        Map<String,String> params = Maps.newHashMap();
        params.put("isDocShow", "1");
        List<TSysFunc> funcBases =  sysFuncService.queryList(params);
        //List<String> showFuncCodes = funcBases.stream().map(TSysFuncBase::getFuncCode).collect(Collectors.toList());
        List<String> list = queryUsedFuncCode(dcVersion);
        List<String> collect = list.stream().distinct().filter(str -> StrUtil.isNotEmpty(str)).collect(Collectors.toList());
        map.put("showFuncCodes",collect);
        return map;
    }

    @Override
    public boolean isAuthToAdmin() {
        return this.otherSpaceToAdmin;
    }

    private List<String> queryUsedFuncCode(String dcVersion){
        String[] split = dcVersion.split(",");
        String lastVersion = split[split.length - 1];
        String sql = "select r.func_code from wiki_page_func_relation r inner join wiki_page " +
                " p on p.id = r.page_id inner join wiki_space s on s.id = p.space_id and s.dc_version = :dcVersion";
        List<Map<String,Object>> list = this.baseDao.sqlQueryForList(sql, this.addParam("dcVersion", lastVersion).param());
        return list.stream().map(data -> {
            return (String) data.get("func_code");
        }).collect(Collectors.toList());
    }

    /**
     * 生成功能树
     *
     * @param funcs
     * @param notFirstFunctions
     * @return
     */
    private List<TreeVo> setTree(List<TSysFunc> funcs, List<TSysFunc> notFirstFunctions, String systemType) {
        List<TreeVo> treeVos = new ArrayList<>();
        for (TSysFunc func : funcs) {
            TreeVo treeVo = new TreeVo();
            treeVo.setId(func.getFuncCode());
            treeVo.setCode(func.getFuncCode());
            treeVo.setName(func.getFuncName());
            List<TSysFunc> childrenFuncs = notFirstFunctions.stream().filter(s -> func.getFuncCode().equals(s.gettSysParentFunc().getFuncCode())).collect(Collectors.toList());
            if (0 != childrenFuncs.size()) {
                treeVo.setChildren(this.setTree(childrenFuncs, notFirstFunctions, systemType));
            }

            Boolean is = !("mlSql".equals(func.getFuncCode()) || "udf".equals(func.getFuncCode()));

            if (!"basic".equals(systemType) || is) {
                treeVos.add(treeVo);
            }

        }
        return treeVos;
    }
}
