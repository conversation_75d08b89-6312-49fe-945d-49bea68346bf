package com.dragonsoft.cicada.datacenter.modules.modeling.vo;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <AUTHOR> Wu<PERSON>D.J
 * @Create : 2020.11.20
 */
@Data
@AllArgsConstructor
public class TransStatus implements Serializable {

    private String status;

    private String statusName;

    private Timestamp beginTime;

    private Timestamp endTime;
}
