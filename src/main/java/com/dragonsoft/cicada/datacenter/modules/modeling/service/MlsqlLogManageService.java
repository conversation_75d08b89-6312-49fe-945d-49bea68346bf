package com.dragonsoft.cicada.datacenter.modules.modeling.service;

import com.code.common.paging.PageInfo;

import java.util.List;
import java.util.Map;

/**
 * Created by yecc on 2020/11/10 15:51
 */
public interface MlsqlLogManageService {

    //新的mlsql 查找日志列表接口
    PageInfo getTaskLogListPage(String transMetaId, PageInfo pageInfo);

    //新的mlsql 根据查找日志列表接口
    PageInfo getTaskLogListPage(String transMetaId, PageInfo pageInfo, String state, String startTime,String endTime);

    //新的mlsql 查找日志列表状态
    List<String> getTaskLogListStatus(String transMetaId);

    Map<String,Object> getTaskLogListInfo(List<String> transMetaIds);

}
