package com.dragonsoft.cicada.datacenter.modules.modeling.service.impl;

import com.code.common.paging.PageInfo;
import com.code.common.utils.StringUtils;
import com.dragonsoft.cicada.datacenter.modules.modeling.service.MlsqlLogManageService;
import com.fw.service.BaseService;
import com.fw.service.annotation.Service;
import com.google.common.collect.Lists;

import java.sql.Timestamp;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * Created by yecc on 2020/11/10 15:53
 */
@Service
public class MlsqlLogManageServiceImpl extends BaseService implements MlsqlLogManageService {
    @Override
    public PageInfo getTaskLogListPage(String transMetaId, PageInfo pageInfo) {
        String sql = "select * from t_trans_job where trans_id=:transMetaId ORDER BY end_time desc";
        Map<String, Object> params = new HashMap<>();
        params.put("transMetaId", transMetaId);
        return this.baseDao.sqlQueryForPage(sql, params, pageInfo);
    }

    public Map<String,Object> getTaskLogListInfo(List<String> transMetaIds) {
        Iterator<String> iterator = transMetaIds.iterator();
        List<Map<String,Object>> list = Lists.newArrayList();
        while (iterator.hasNext()) {
            StringBuffer sql = new StringBuffer();
            String transId = iterator.next();
            sql.append(" select end_time,execute_status,trans_id from t_trans_job where ").
                    append(" trans_id= '").
                    append(transId).append("'").
                    append(" ORDER BY start_time desc").
                    append(" limit 1").
                    append("\n");
            list.addAll(this.baseDao.sqlQueryForList(sql.toString()));
        }
        return  buildTransInfo(list);
    }

    private Map<String,Object> buildTransInfo(List<Map<String,Object>> list){
        Map<String,Object> transExecuteInfo = new HashMap<>();
        for(Map<String,Object> map : list ){
            transExecuteInfo.put((String) map.get("trans_id"),map);
        }
        return transExecuteInfo;
    }




    @Override
    public PageInfo  getTaskLogListPage(String transMetaId, PageInfo pageInfo, String state, String startTime, String endTime) {
        String sql = "select * from t_trans_job where trans_id=:transMetaId ";
        Map<String, Object> params = new HashMap<>();
        if (StringUtils.isNotBlank(state)) {
            sql = sql + "and  execute_status ='" + state + "' ";
        }
        if (StringUtils.isNotBlank(startTime) && StringUtils.isNotBlank(endTime)) {
            Timestamp start = new Timestamp(Long.parseLong(startTime));

            Timestamp end = new Timestamp(Long.parseLong(endTime));
            sql = sql + " and (start_time <='" + end + "'  and end_time>='" + start + "')";
        }
        sql = sql + " ORDER BY start_time desc";
        params.put("transMetaId", transMetaId);

        return this.baseDao.sqlQueryForPage(sql, params, pageInfo);
    }

    @Override
    public List<String> getTaskLogListStatus(String transMetaId) {
        String sql = "select DISTINCT execute_status from t_trans_job where trans_id=:transMetaId";
        Map<String, Object> params = new HashMap<>();
        params.put("transMetaId", transMetaId);
        return this.baseDao.sqlQueryForList(sql, params);
    }
}
