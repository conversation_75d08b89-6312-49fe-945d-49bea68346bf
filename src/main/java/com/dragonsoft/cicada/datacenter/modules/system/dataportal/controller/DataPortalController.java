package com.dragonsoft.cicada.datacenter.modules.system.dataportal.controller;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.code.common.paging.PageInfo;
import com.code.common.utils.R;
import com.code.metadata.portal.Portal;
import com.code.metadata.portal.enums.EnumContentType;
import com.code.metadata.portal.enums.EnumLayoutType;
import com.code.metaservice.datawarehouse.model.DatasetTreeModel;
import com.code.metaservice.portal.IPortalConfigService;
import com.code.metaservice.portal.IPortalService;
import com.dragoninfo.dfw.annotation.FuncScanAnnotation;
import com.dragoninfo.dfw.bean.Result;
import com.dragoninfo.dfw.entity.TSysAuthUser;
import com.dragoninfo.dfw.service.SysAuthUserService;
import com.dragonsoft.cicada.datacenter.common.token.GlobalConstant;
import com.dragonsoft.cicada.datacenter.common.utils.UserContextUtil;
import com.dragonsoft.cicada.datacenter.modules.dataplan.externaldatasources.service.IDataSetAuthService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.externaldatasources.vo.DataSetAuthVo;
import com.dragonsoft.cicada.datacenter.modules.system.dataportal.service.IDataPortalService;
import com.dragonsoft.cicada.datacenter.modules.system.dataportal.service.IDataPublishUrlService;
import com.dragonsoft.cicada.datacenter.modules.system.dataportal.vo.PortalConfigVo;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.service.IFunctionService;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.service.IUserService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

/**
 * <AUTHOR>
 * @date 2020/8/31
 */
@RestController
@CrossOrigin
@RequestMapping("/portal")
@FuncScanAnnotation(code = "themePortal", name = "主题门户", parentCode = "dataVisualAnalysis")
@Slf4j
public class DataPortalController {

    @Autowired
    private IDataPortalService dataPortalService;

    @Autowired
    private IPortalConfigService portalConfigService;

    @Autowired
    private IPortalService portalService;

    @Autowired
    private SysAuthUserService sysAuthUserService;

    @Autowired
    private IDataPublishUrlService dataPublishUrlService;

    @Autowired
    private IDataSetAuthService dataSetAuthService;

    @Autowired
    private IUserService userService;

    @Autowired
    private IFunctionService functionService;

    @ResponseBody
    @RequestMapping("/queryTree")
    public Result queryTree(HttpServletRequest request) {
        String userId =UserContextUtil.getUserIdByHttpRequest(request);
        List<DatasetTreeModel> datasetTreeModels = dataPortalService.queryTree(userId);
        return Result.success(datasetTreeModels);
    }


    @ResponseBody
    @RequestMapping("/addTreeNode")
    public Result addTreeNode(String pId, String name, HttpServletRequest request) {
        String userId = (String) request.getSession().getAttribute("userId");
        return Result.success(dataPortalService.addTreeNode(pId, name, userId));
    }


    @ResponseBody
    @RequestMapping("/deleteTreeNode")
    public Result deleteTreeNode(String nodeId, HttpServletRequest request) {
        String userId = (String) request.getSession().getAttribute("userId");
        dataPortalService.deleteTreeNode(nodeId, userId);
        return Result.success();
    }


    @ResponseBody
    @RequestMapping("/editTreeNode")
    public Result editTreeNode(String id, String pId, String name) {
        dataPortalService.editTreeNode(id, pId, name);
        return Result.success();
    }


    @ResponseBody
    @RequestMapping("/upDataSecurityModeAndTime")
    public Result upDataSecurityModeAndTime(String portalId, String securityMode, String beginTime, String endTime) {
        dataPortalService.upDataSecurityModeAndTime(portalId, securityMode, beginTime, endTime);
        return Result.success();
    }


    @ResponseBody
    @RequestMapping("/deletePortal")
    @FuncScanAnnotation(code = "themePortalDeletePortal", name = "删除", parentCode = "themePortal")
    public Result deletePortal(String portalId) {
        try {
            dataPortalService.deletePortal(portalId);
            return Result.toResult(R.ok());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.toResult(R.error(e.getMessage()));
        }
    }

    @ResponseBody
    @PostMapping("/getPortalList")
    public Result getPortalList(@RequestBody Map<String, Object> dataMap, HttpServletRequest request) {
        try {
            String userId = UserContextUtil.getUserIdByHttpRequest(request);
            Assert.hasLength(userId, "用户不能为空");
            int pageSize = MapUtil.getInt(dataMap, "pageSize");
            int pageIndex = MapUtil.getInt(dataMap, "pageNum");
            String keyWord = MapUtil.getStr(dataMap, "keyWord");
            String dirId = MapUtil.getStr(dataMap, "dirId");
            Assert.isTrue(!GlobalConstant.CommonProperties.FROM_PERSONAL_TOP_NODE_ID.equals(dirId),
                    "请选择他人空间以下的目录");
            String operatorId = MapUtil.getStr(dataMap, "operatorId");
            if (userId.equals(operatorId) || CharSequenceUtil.isBlank(operatorId)) {
                operatorId = userId;
            }
            PageInfo pageInfo = new PageInfo();
            pageInfo.setPageIndex(pageIndex);
            pageInfo.setPageSize(pageSize);
            pageInfo.setDataList(Lists.newArrayList());
            PageInfo portalList = dataPortalService.getPortalPage(operatorId, keyWord, dirId, pageInfo);
            return Result.toResult(R.ok(portalList));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.toResult(R.error(e.getMessage()));
        }
    }


    @ResponseBody
    @RequestMapping("/portalAuth")
    @ApiOperation(value = "门户发布")
    public Result dataSetsAuth(@RequestBody DataSetAuthVo dataSetAuthVo) {
        dataSetAuthService.saveBatchDataSetAuth(dataSetAuthVo, false);
        return Result.success();
    }


    @ResponseBody
    @PostMapping("/createOrUpdatePortal")
    @FuncScanAnnotation(code = "themePortalCreatePortal", name = "新建", parentCode = "themePortal")
    public Result createPortal(@RequestBody PortalConfigVo portalConfigVo, @RequestParam("dirId") String dirId, HttpServletRequest request) {
        try {
            String userId = (String) request.getSession().getAttribute("userId");
            Map<String, String> maps = dataPortalService.createOrUpdatePortal(portalConfigVo, userId, dirId);
            return Result.toResult(R.ok(maps));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.toResult(R.error(e.getMessage()));
        }
    }


    @ResponseBody
    @PostMapping("/copy")
    @FuncScanAnnotation(code = "themePortalCopyPortal", name = "复制", parentCode = "themePortal")
    public Result copyPortal(@RequestBody Map<String, String> map, HttpServletRequest request) {
        String userId = (String) request.getSession().getAttribute("userId");
        String id = map.get("id");
        String classifyId = map.get("classifyId");
        String portalName = map.get("portalName");
        dataPortalService.copy(id, classifyId, portalName, userId);
        return Result.toResult(R.ok());
    }


  /*  @PostMapping("/saveLogo")
    @ResponseBody
    public Result saveLogo(@RequestParam("file") MultipartFile file, @RequestParam("portalConfigId") String portalConfigId) throws IOException {
        try {
            String resultStr = Base64.encodeBase64String(file.getBytes());
            byte[] resultData = Base64.decodeBase64(resultStr);
            PortalConfig portalConfig = portalConfigService.get(PortalConfig.class, portalConfigId);
            portalConfig.setLogo(resultData);
            portalConfigService.saveOrUpdate(portalConfig);
            return Result.toResult(R.ok());
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return Result.toResult(R.error(e.getMessage()));
        }
    }*/


    @ResponseBody
    @GetMapping("/getPortalConfig")
    public Result getPortalConfig(String portalId, HttpServletRequest request) {
        try {
            String userId = (String) request.getSession().getAttribute("userId");
            PortalConfigVo portalConfig = dataPortalService.getPortalConfig(portalId, userId);
            return Result.toResult(R.ok(portalConfig));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.toResult(R.error(e.getMessage()));
        }
    }

    /**
     * @param portalPublishUrl 门户地址
     * @param request          session信息
     * @return 门户信息
     */
    @ResponseBody
    @GetMapping("/getPortalPageByUrl")
    @FuncScanAnnotation(code = "themePortalGetPortalPageByUrl", name = "查看", parentCode = "themePortal")
    public Result getPortalPageByUrl(String portalPublishUrl, HttpServletRequest request) {
        try {
            String userId = (String) request.getSession().getAttribute("userId");
            PortalConfigVo portalConfig = dataPortalService.getPortalPageByUrl(userId, portalPublishUrl);
            return Result.toResult(R.ok(portalConfig));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.toResult(R.error(e.getMessage()));
        }
    }

    /**
     * 数据类型
     */
    @ResponseBody
    @GetMapping("getEnumContentType")
    @FuncScanAnnotation(code = "themePortalUpDataPortal", name = "编辑", parentCode = "themePortal")
    public Result getEnumContentType() {
        try {
            return Result.toResult(R.ok(EnumContentType.getEnumTypeList()));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.toResult(R.error(e.getMessage()));
        }
    }

    /**
     * 布局方案
     */
    @ResponseBody
    @GetMapping("getEnumLayoutType")
    public Result getEnumLayoutType() {
        try {
            return Result.toResult(R.ok(EnumLayoutType.getEnumTypeList()));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.toResult(R.error(e.getMessage()));
        }
    }

    private static volatile Lock lock = new ReentrantLock();

    @GetMapping("/getPortalConcurrentUserId")
    public Result getPortalConcurrentUserId(String id, HttpServletRequest request) {
        String userId = (String) request.getSession().getAttribute("userId");
        lock.lock();
        try {
            Portal portal = portalService.get(Portal.class, id);
            if (portal.getEditUserId() == null) {
                portal.setEditUserId(userId);
                portalService.update(portal);
                return Result.success(true);
            } else if (!userId.equals(portal.getEditUserId())) {
                Map<String, String> params = Maps.newHashMap();
                params.put("id", portal.getEditUserId());
                TSysAuthUser tSysAuthUser = (TSysAuthUser) sysAuthUserService.query(params);
                return Result.success(tSysAuthUser.getObjName());
            } else {
                return Result.success(true);
            }
        } finally {
            lock.unlock();
        }
    }


    @GetMapping("/deleteConcurrentUserId")
    public Result deleteConcurrentUserId(String id, HttpServletRequest request) {
        String userId = (String) request.getSession().getAttribute("userId");
        Portal portal = portalService.get(Portal.class, id);
        portal.setEditUserId(null);
        portalService.update(portal);
        return Result.success();
    }


    @GetMapping("/savePublishUrl")
    public Result savePublishUrl(String url, String name, String stat, String portalId, HttpServletRequest request) {
        String userId = (String) request.getSession().getAttribute("userId");
        dataPublishUrlService.savePublishUrl(url, name, userId, stat, portalId);
        return Result.success();
    }
}
