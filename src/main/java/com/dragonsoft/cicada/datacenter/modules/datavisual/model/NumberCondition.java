package com.dragonsoft.cicada.datacenter.modules.datavisual.model;

import com.code.common.utils.StringUtils;
import com.code.meta.dml.standard.IMultCdin;
import com.code.meta.dml.standard.cdins.IQueryCdin;
import com.code.meta.dml.standard.cdins.MultCdin;
import com.code.meta.dml.standard.cdins.QueryCdins;

public class NumberCondition extends AbsConditionWidget {
    Double[] value;
    String logicalVal;

    @Override
    IMultCdin builderCondition(QueryCdins queryCdins) {
        IMultCdin multCdin = new MultCdin();
        if (null == value||value.length==0) {
            return multCdin;
        }
        String fieldCode = StringUtils.isBlank(this.field.getFieldAlias()) ? this.field.getCode() : this.field.getFieldAlias();

        IQueryCdin queryCdin=   this.getIQueryCdin(fieldCode, queryCdins);
        if(queryCdin==null){
            return multCdin;
        }
        multCdin.addCdin(queryCdin);
        return multCdin;
    }

    private IQueryCdin getIQueryCdin(String filed, QueryCdins queryCdins) {

        if ("=".equals(logicalVal)) {
            if(null==value[0]){
                return null;
            }
            return queryCdins.eq(filed, value[0]);
        } else if ("<".equals(logicalVal)) {
            if(null==value[0]){
                return null;
            }
            return queryCdins.lt(filed, value[0]);
        } else if ("<=".equals(logicalVal)) {
            if(null==value[0]){
                return null;
            }
            return queryCdins.le(filed, value[0]);
        } else if ("!=".equals(logicalVal)) {
            if(null==value[0]){
                return null;
            }
            return queryCdins.ne(filed, value[0]);
        } else if (">".equals(logicalVal)) {
            if(null==value[0]){
                return null;
            }
            return queryCdins.gt(filed, value[0]);
        } else if (">=".equals(logicalVal)) {
            if(null==value[0]){
                return null;
            }
            return queryCdins.ge(filed, value[0]);
        } else if ("IN".equals(logicalVal)) {
            if(value.length!=2||value[0]==null||value[1]==null){
                return null;
            }
            IMultCdin multCdin = new MultCdin();
            multCdin.and(queryCdins.ge(filed, value[0]), queryCdins.le(filed, value[1]));
            return multCdin;
        }
        return null;
    }


    public Double[] getValue() {
        return value;
    }

    public void setValue(Double[] value) {
        this.value = value;
    }

    public String getLogicalVal() {
        return logicalVal;
    }

    public void setLogicalVal(String logicalVal) {
        this.logicalVal = logicalVal;
    }
}
