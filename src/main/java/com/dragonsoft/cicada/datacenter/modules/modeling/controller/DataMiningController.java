package com.dragonsoft.cicada.datacenter.modules.modeling.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.code.common.utils.R;
import com.code.common.utils.StringUtils;
import com.code.common.utils.assertion.Assert;
import com.code.mist.builder.model.DubboResult;
import com.code.mist.builder.service.dubbo.ITransformApiService;
import com.dragoninfo.dfw.annotation.FuncScanAnnotation;
import com.dragoninfo.dfw.bean.Result;
import com.dragonsoft.cicada.datacenter.common.config.PropertiesListenerConfig;
import com.dragonsoft.cicada.datacenter.modules.modeling.qo.UpdateTransQo;
import com.dragonsoft.cicada.datacenter.modules.modeling.service.DataMiningService;
import com.dragonsoft.cicada.datacenter.modules.modeling.service.DataModelingService;
import com.dragonsoft.cicada.datacenter.modules.modeling.service.TransCloneService;
import com.dragonsoft.cicada.datacenter.modules.modeling.vo.QuickTransToDefTransVo;
import com.dragonsoft.cicada.datacenter.modules.modeling.vo.UpdateStatusDTO;
import io.swagger.annotations.Api;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Wu.D.J
 * @Create : 2020.11.04
 */
@CrossOrigin
@RestController
@Api(value = "数据挖掘模块")
@RequestMapping(value = "/dataMining")
//@FuncScanAnnotation(code = "dataMining", name = "数据挖掘", parentCode = "mlSql")
public class DataMiningController {

    @Autowired
    private DataMiningService dataMiningService;

    @Autowired
    private ITransformApiService transformApiService;

    @Autowired
    private TransCloneService transCloneService;

    @Autowired
    private DataModelingService dataModelingService;

    @Value("${mlsql.executor.address}")
    private String executorServer;

    @Value("${mlsql.isMultiple}")
    private boolean isMultiple;

    @GetMapping(value = "/createTrans")
//    @FuncScanAnnotation(code = "dataMiningCreateTrans", name = "新建模型", parentCode = "dataMining")
//    @ValidateAndLogAnnotation
    public Result createTrans(String transName) {
        return Result.success(dataMiningService.createTrans(transName));
    }

    @GetMapping("/updateSchedule")
    public Result updateSchedule(String transId, String scheduleType, String cron, String declare) {
        dataMiningService.updateSchedulePlan(transId, scheduleType, cron, declare);
        return Result.success();
    }

    @PostMapping("/updateScheduleWithSubTrans")
    @FuncScanAnnotation(code = "dataMiningUpdateSchedule", name = "配置", parentCode = "dataMining")
//    @ValidateAndLogAnnotation
    public Result updateScheduleWithSubTrans(@RequestBody Map map) {
        String transId = map.get("transId")==null?null:map.get("transId").toString();//本方案id
        String scheduleType = map.get("scheduleType")==null?null:map.get("scheduleType").toString();
        String cron = map.get("cron")==null?null:map.get("cron").toString();
        String declare = map.get("declare")==null?null:map.get("declare").toString();

        String executorServiceName = map.get("executorServerName")==null?null:map.get("executorServerName").toString();

        List startPrograms = (List) map.get("startPrograms");
        dataMiningService.updateSchedulePlanWithSubTrans(transId, scheduleType, cron, declare,startPrograms,executorServiceName);
        return Result.success();
    }

    private void deleteSchedulerWithSubTrans(String transId) {
        List subTrans = dataMiningService.getSubTrans(transId);
        for (Object object : subTrans) {
            Map map = (Map) object;
            String subTrans_id = (String) map.get("subtrans_id");
            if (StringUtils.isNotBlank(subTrans_id)) transformApiService.deleteTrans(subTrans_id);
        }
    }

    @GetMapping("/getSchedule")
    public Result getSchedule(String transId) {
        Map<String, String> map = dataMiningService.getSchedulePlan(transId);
        return Result.success(map);
    }

    /**
     * 快速分析 转换 ===》》》流程建模
     * @param vo
     * @return
     */
    @PostMapping(value = "/quickTransToProcessTrans")
    public Result quickTransToProcessTrans(@RequestBody QuickTransToDefTransVo vo) {
        //更新方案信息为数据建模
        dataMiningService.quickTransToProcessTrans(vo.getTransId(),vo.getBusiClassifyId());
        //将方案下的插件 x,y轴都更新
        for (QuickTransToDefTransVo.QuickTrans plugin : vo.getPlugins()) {
            dataMiningService.updateTransXAndY(plugin.getPluginId(),plugin.getX(),plugin.getY());
        }
        return Result.success();
    }

    @PostMapping(value = "/updateTrans")
    @FuncScanAnnotation(code = "processModelingResultReuseObj", name = "编辑", parentCode = "processModeling")
    public Result updateTrans(@RequestBody Map saveMap, HttpServletRequest request) {
        UpdateTransQo updateTransQo = new UpdateTransQo();
        updateTransQo.setUserId((String) request.getSession().getAttribute("userId"));
        updateTransQo.setTransName((String) saveMap.get("transName"));
        updateTransQo.setTransId((String) saveMap.get("transId"));
        updateTransQo.setClassifyId((String) saveMap.get("classifyId"));
        updateTransQo.setDirType((String) saveMap.get("dirType"));
        updateTransQo.setMemo((String) saveMap.get("memo"));
        updateTransQo.setVersion(ObjectUtil.isNotEmpty(saveMap.get("version"))?(Integer) saveMap.get("version") : null);
        updateTransQo.setProductionFirm(ObjectUtil.isNotEmpty(saveMap.get("productionFirm"))? StrUtil.toString(saveMap.get("productionFirm")):StrUtil.EMPTY);
        if (StrUtil.isEmpty(updateTransQo.getUserId())){
            throw new RuntimeException("请先登录后再操作！");
        }
        dataMiningService.updateTrans(updateTransQo);
        return Result.success();
    }


    @GetMapping("/engineList")
    public Result engineList() {
        List<Map<String,String>> engines = new ArrayList<>();


        if (isMultiple) {
           /* Map<String,String> engine2 = new HashMap<>();
            engine2.put(executorServer2,"address2");
            engines.add(engine2);*/
            Map<String, String> engineConfigMap = PropertiesListenerConfig.getAllProperties();
            for (Map.Entry<String, String> configItem : engineConfigMap.entrySet()) {
                Map<String,String> engineConfig = new HashMap<>();
                engineConfig.put(configItem.getValue(),configItem.getKey());
                engines.add(engineConfig);
            }
        } else {
            Map<String,String> engineDefault = new HashMap<>();
            engineDefault.put(executorServer,"address");
            engines.add(engineDefault);
        }
        return Result.success(engines);
    }

//    @Value("${flink.job.submit.url:}")
    private String flinkUrl;

    @GetMapping("/taskRun")
    @FuncScanAnnotation(code = "processModelingStartJob ", name = "启动", parentCode = "processModeling")
//    @ValidateAndLogAnnotation
    public Result taskRun(String transId, HttpSession session, String executorServerName,String type,HttpServletRequest request) {
        if (StringUtils.isBlank(type)) {
            String userId = (String) session.getAttribute("userId");
            if (!isMultiple) {
                executorServerName = "address";
            }
            dataMiningService.transRun(transId, userId, executorServerName);


            return Result.success();
        }else {
            //走四系的
            Assert.hasLength(flinkUrl,"flinkUrl不能为空".concat(flinkUrl));
            dataMiningService.transRun(transId,flinkUrl,request);
            return Result.success();
        }
    }


    @PostMapping("/updateJobStatus")
    public Result updateJobStatus(@RequestBody UpdateStatusDTO dto){
        dataMiningService.updateJobStatus(dto.getJobId(),dto.getStatus());
        return Result.success();
    }

    @GetMapping("/taskRun4")
    public Result taskRun4(String transId, String userId) {
//        String userId = (String) session.getAttribute("userId");
        dataMiningService.transRun(transId,userId);
        return Result.success();
    }

    @GetMapping("/taskStop")
    public Result taskStop(String transId,String type) {
        if (StringUtils.isBlank(type)) {
            dataMiningService.transStop(transId);
        }else {
            dataMiningService.transStop(transId,flinkUrl);
        }
        return Result.success();
    }


    /**
     * 该方法由调度引擎调用，来获取所执行的方案的mlsql语法
     *
     * @param transId 方案id
     * @return 执行SQL
     */
    @GetMapping("/showSQL")
    public Result showTaskSQL(String transId) {
        return Result.success(dataMiningService.showSQL(transId));
    }

    @GetMapping("/subTask")
    public Result subTask(String subTranId) {
        return Result.success(dataMiningService.subTask(subTranId));
    }

    @GetMapping("/taskPreview")
    public Result taskPreview(String transId) {
        Map<String, String> response = dataMiningService.transPreview(transId);
        return Result.success(response);
    }

    @GetMapping("/analysisSQL")
    public Result SQLAnalysis(String transId) {
        return Result.success(dataMiningService.analysisSQL(transId));
    }


    /**
     * 更新方案名称
     *
     * @param reqMap
     * @return
     */
    @PostMapping(value = "/updateTransName")
    @FuncScanAnnotation(code = "dataMiningUpdateTransName", name = "重命名", parentCode = "dataMining")
//    @ValidateAndLogAnnotation
    public Result updateTransName(@RequestBody Map reqMap) {
        String name = reqMap.get("name").toString();
        String transId = reqMap.get("transId").toString();
        DubboResult result = transformApiService.updateTransStepName(transId, name);
        return Result.toResult(R.ok(result.getMsg()));
    }


    /**
     * 方案复制
     *
     * @param transId
     * @param dirParentId
     * @return
     */
    @GetMapping(value = "/copyTrans")
//    @FuncScanAnnotation(code = "dataMiningCopyTrans", name = "复制方案", parentCode = "dataMining")
//    @ValidateAndLogAnnotation
    public Result copyTrans(String transId, String dirParentId) {
        Map<String, String> map = transCloneService.saveCopyTrans(transId, dirParentId, null, null);
        return Result.toResult(R.ok(map));
    }


    /**
     * 获取数据挖掘的数据集
     *
     * @param transId
     * @return
     */
    @GetMapping("/getDataSets")
//    @FuncScanAnnotation(code = "processModelingGetDataSets", name = "预览结果集", parentCode = "processModeling")
//    @ValidateAndLogAnnotation
    public Result getDataSets(String transId) {
        List dataSetByTrans = dataModelingService.getDataSetByTrans(transId);
        if (dataSetByTrans == null) {
            return Result.error("400", "该流程没有输出");
        }
        return Result.success(dataSetByTrans);
    }


    @GetMapping(value = "/moveModel")
//    @FuncScanAnnotation(code = "dataMiningMoveModel", name = "移动", parentCode = "dataMining")
//    @ValidateAndLogAnnotation
    public Result moveModel(String elementId, String classifyId) {
        String msg = dataModelingService.moveModel(elementId, classifyId);
        if ("success".equals(msg)) {
            return Result.toResult(R.ok());
        } else {
            return Result.toResult(R.error(msg));
        }
    }


    /**
     * 数据挖掘方案删除
     *
     * @param transId
     * @return
     */
    @GetMapping(value = "/deleteTrans")
    @FuncScanAnnotation(code = "dataMiningDeleteTrans", name = "删除", parentCode = "dataMining")
//    @ValidateAndLogAnnotation
    public Result deleteTrans(String transId) {
        DubboResult result = transformApiService.deleteTrans(transId);
        return Result.toResult(R.ok(result.getData()));
    }

    @GetMapping("/getSubTrans")
    public Result getSubTrans(String transId) {
        List subTrans = dataMiningService.getSubTrans(transId);
        return Result.success(subTrans);
    }

    @GetMapping("/deletaSubTrans")
    public Result deletaSubTrans(String transId, String subTransId) {
        dataMiningService.deletaSubTrans(transId, subTransId);
        return Result.success();
    }

    @PostMapping("/deleteBatchSubTrans")
    @ResponseBody
    public Result deleteBatchSubTrans( @RequestBody Map<String,Object> maps ) {
         String transId = (String) maps.get("transId");
         List<String> subTransIds = (List<String>) maps.get("subTransIds");
        if(CollectionUtils.isNotEmpty(subTransIds)){
            for(String subTransId: subTransIds ){
                dataMiningService.deletaSubTrans(transId, subTransId);
            }
        }
        return Result.success();
    }
}
