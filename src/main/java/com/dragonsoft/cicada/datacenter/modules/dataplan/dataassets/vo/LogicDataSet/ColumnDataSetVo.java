package com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.vo.LogicDataSet;

import com.code.dataset.operator.column.synccolumn.vo.LogicSyncColumn;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ColumnDataSetVo implements Serializable {

    private String name;
    private String code;
    private String id;

    //维度
    private List<LogicSyncColumn> dimension;
    //度量
    private List<LogicSyncColumn> measure;

    //同步新增的字段
    private List<LogicSyncColumn> newColumn;
    private List<LogicSyncColumn> pks;
}
