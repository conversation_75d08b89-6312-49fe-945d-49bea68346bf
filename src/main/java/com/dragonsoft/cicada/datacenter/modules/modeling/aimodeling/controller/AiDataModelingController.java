package com.dragonsoft.cicada.datacenter.modules.modeling.aimodeling.controller;

import com.code.common.paging.PageInfo;
import com.code.common.utils.R;
import com.code.common.utils.StringUtils;
import com.code.common.utils.assertion.Assert;
import com.dragoninfo.dfw.annotation.FuncScanAnnotation;
import com.dragoninfo.dfw.annotation.ValidateAndLogAnnotation;
import com.dragoninfo.dfw.bean.Result;
import com.dragonsoft.cicada.datacenter.modules.modeling.aimodeling.service.AiDataModelingService;
import com.dragonsoft.cicada.datacenter.modules.modeling.aimodeling.vo.*;
import com.dragonsoft.cicada.datacenter.modules.modeling.rapidanalysis.vo.ClassifyVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpSession;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * ai建模
 */
@RestController
@RequestMapping("/transOperator/ai")
@CrossOrigin
@Slf4j
@FuncScanAnnotation(code = "AIModeling", name = "AI建模", parentCode = "dataModeling")
@PropertySource("classpath:case-config.properties")
public class AiDataModelingController {

    @Autowired
    private AiDataModelingService aiDataModelingService;

    @Value("${sceModel}")
    private boolean sceModel;

    @Value("${ai.model.editorWebserviceIp}")
    private String editorWebserviceIp;

    @GetMapping("/getAiModelDir")
    public Result getAiModelDir(HttpSession session){

        String userId = (String) session.getAttribute("userId");
        if (StringUtils.isBlank(userId)){
            throw new RuntimeException("请先登录！");
        }
        try {
            List<ModelTreeResult> busiClassifies = aiDataModelingService.getAllAiModelTree(userId, "time");
            if (!sceModel){
                for (int i = 0; i < busiClassifies.size(); i++) {
                    if ("场景案例".equals(busiClassifies.get(i).getName())){
                        busiClassifies.remove(i);
                        break;
                    }
                }
            }
            return Result.toResult(R.ok(busiClassifies));
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return Result.toResult(R.error(e.getMessage()));
        }
    }

    /**
     * ai建模新增目录
     * @param session
     * @return
     */
    @PostMapping("/addAiModelDir")
    public Result addAiModelDir(HttpSession session, @RequestBody ClassifyVo classifyVo){
        try {
            String userId = (String) session.getAttribute("userId");
            if (StringUtils.isBlank(userId)){
                throw new RuntimeException("请先登录！");
            }
            String classifyName = classifyVo.getClassifyName();
            String parentClassifyId = classifyVo.getParentClassifyId();
            String dirType = "TRANS_AI_DIR_MF";
            String dirId = aiDataModelingService.addAiModelDir(parentClassifyId, classifyName, dirType, userId);
            return Result.toResult(R.ok(dirId));
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return Result.toResult(R.error(e.getMessage()));
        }
    }

    @GetMapping("/renameAiModelDir")
    public Result renameAiModelDir(String classifyId,String newClassifyName,HttpSession session){
        try {
            String userId = (String) session.getAttribute("userId");
            if (StringUtils.isBlank(userId)){
                throw new RuntimeException("请先登录！");
            }
            aiDataModelingService.updateAiModelDirName(classifyId,newClassifyName,userId);
            return Result.toResult(R.ok());
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return Result.toResult(R.error(e.getMessage()));
        }
    }

    @GetMapping("/moveAiModelDir")
    public Result moveAiModelDir(String curryClassifyId, String newParentClassifyId) {
        try {
            aiDataModelingService.moveAiModelDir(curryClassifyId,newParentClassifyId);
            return Result.toResult(R.ok());
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return Result.toResult(R.error(e.getMessage()));
        }
    }

    @GetMapping("/deleteAiModelDir")
    public Result deleteAiModelDir(String classifyId){
        try {
            aiDataModelingService.deleteAiModelDir(classifyId);
            return Result.toResult(R.ok());
        } catch (Exception e) {
            Assert.fail(e.getMessage());
            log.error(e.getMessage(),e);
            return Result.toResult(R.error(e.getMessage()));
        }
    }

    /**
     * 新建ai建模
     * @param
     * @return
     */
    @PostMapping("/newAiModel")
    @FuncScanAnnotation(code = "AIModelingCreateModel", name = "新建模型", parentCode = "AIModeling")
    @ValidateAndLogAnnotation
    public Result newAiModel(@RequestBody AiModelVo aiModelVo,HttpSession session){
        try {
            String userId = (String) session.getAttribute("userId");
            if (StringUtils.isBlank(userId)){
                Assert.fail("请先登录！");
            }
            aiModelVo.setUserId(userId);
            String scriptId = aiDataModelingService.newAiModel(aiModelVo);
            return Result.toResult(R.ok(scriptId));
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return Result.toResult(R.error(e.getMessage()));
        }
    }

    /**
     * 启动模型脚本
     * @param scriptId
     * @return
     */
    @GetMapping("/startScript")
    public Result startScript(String scriptId){
        try {
            Map<String,Object> map = new HashMap<>();
            String startAiModelUrl = aiDataModelingService.getStartAiModelUrl(scriptId);
            if (StringUtils.isNotBlank(editorWebserviceIp)) {
                String str = startAiModelUrl.substring(0,startAiModelUrl.indexOf("notebooks"));
                startAiModelUrl = startAiModelUrl.replace(str,"http://" + editorWebserviceIp + "/");
            }
            map.put("startAiModelUrl",startAiModelUrl);
            return Result.toResult(R.ok(startAiModelUrl));
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return Result.toResult(R.error(e.getMessage()));
        }
    }

    /**
     * 保存模型
     * @param scriptId
     * @return
     */
    @GetMapping("/saveScript")
    public Result saveScript(String scriptId){
        try {
            aiDataModelingService.saveScript(scriptId);
            return Result.toResult(R.ok());
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return Result.toResult(R.error(e.getMessage()));
        }
    }

    /**
     * 开始训练
     * @param scriptId
     * @return
     */
    @GetMapping("/runScript")
    public Result runScript(String scriptId){
        try {
            Map<String,Object> map = new HashMap<>();
            String logId = aiDataModelingService.runScript(scriptId);
            map.put("logId",logId);
            return Result.toResult(R.ok(map));
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return Result.toResult(R.error(e.getMessage()));
        }
    }

    /**
     * 模型启动
     * @param scriptId
     * @return
     */
    @GetMapping("/startAndRun")
    @FuncScanAnnotation(code = "AIModelingStartModel", name = "启动", parentCode = "AIModeling")
//    @ValidateAndLogAnnotation
    public Result startAndRun(String scriptId){
        try {
            String startAiModelUrl = aiDataModelingService.getStartAiModelUrl(scriptId);
            aiDataModelingService.saveScript(scriptId);
            String logId = aiDataModelingService.runScript(scriptId);
            //aiDataModelingService.updateAiLog(logId,scriptId);
            return Result.toResult(R.ok(logId));
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return Result.toResult(R.error(e.getMessage()));
        }
    }

    /**
     * 获取训练结果
     * @param logId
     * @return
     */
    @GetMapping("/getEvaluateRst")
    public Result getEvaluateRst(String logId){
        try {
            Map<String,Object> map = new HashMap<>();
            List<EvaluateRstItem> evaluateRst = aiDataModelingService.getEvaluateRst(logId);
            map.put("evaluateRst",evaluateRst);
            return Result.toResult(R.ok(map));
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return Result.toResult(R.error(e.getMessage()));
        }
    }

    /**
     * 获取训练数据集
     * @param logId
     * @return
     */
    @GetMapping("/getEvaRstDataSet")
    public Result getEvaRstDataSet(String logId){
        try {
            EntInputInfo evaRstDataSet = aiDataModelingService.getEvaRstDataSet(logId);
            return Result.toResult(R.ok(evaRstDataSet));
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return Result.toResult(R.error(e.getMessage()));
        }
    }

    /**
     * 获取训练历史
     * @param
     * @return
     */
    @PostMapping("/getAiModelRunHistory")
    public Result getAiModelRunHistory(@RequestBody AiHistoryQueryVo aiHistoryQueryVo){
        try {
            PageInfo history = aiDataModelingService.getAiModelRunHistory(aiHistoryQueryVo);
            return Result.toResult(R.ok(history));
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return Result.toResult(R.error(e.getMessage()));
        }
    }

    /**
     * 查看训练历史详情
     * @param logId
     * @return
     */
    @GetMapping("/getLogDetail")
    public Result getLogDetail(String logId){
        try {
            List<LogDetailResult> logDetailByLogId = aiDataModelingService.getLogDetailByLogId(logId);
            return Result.toResult(R.ok(logDetailByLogId));
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return Result.toResult(R.error(e.getMessage()));
        }
    }

    /**
     * 删除日志
     * @param logId
     * @return
     */
    @GetMapping("/deleteLogById")
    public Result deleteLogById(String logId){
        try {
            aiDataModelingService.deleteLogById(logId);
            return Result.toResult(R.ok());
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return Result.toResult(R.error(e.getMessage()));
        }
    }


    /**
     * ai建模列表
     * @param
     * @return
     */
    @PostMapping("/getAiModelList")
    public Result getAiModelList(@RequestBody AiModelPageVo aiModelPageVo,HttpSession session){
        try {
            String userId = (String) session.getAttribute("userId");
            if(StringUtils.isBlank(userId)){
                throw new RuntimeException("请先登录！");
            }
            PageInfo aiModelPage = aiDataModelingService.getAiModelPage(aiModelPageVo,userId);
            return Result.toResult(R.ok(aiModelPage));
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return Result.toResult(R.error(e.getMessage()));
        }
    }

    /**
     * ai模型重命名
     * @param scriptId
     * @param newModelName
     * @return
     */
    @GetMapping("/renameAiModel")
    @FuncScanAnnotation(code = "AIModelingReName", name = "重命名", parentCode = "AIModeling")
    @ValidateAndLogAnnotation
    public Result renameAiModel(String scriptId,String newModelName){
        try {
            aiDataModelingService.renameAiModel(scriptId,newModelName);
            return Result.toResult(R.ok());
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return Result.toResult(R.error(e.getMessage()));
        }
    }

    /**
     * 另存为
     * @param scriptId
     * @param classifyId
     * @return
     */
    @GetMapping("/copyAiModel")
    @FuncScanAnnotation(code = "AIModelingSaveAs", name = "另存为", parentCode = "AIModeling")
    @ValidateAndLogAnnotation
    public Result copyAiModel(String scriptId,String classifyId,String name){
        try {
            aiDataModelingService.copyAiModel(scriptId,classifyId,name);
            return Result.toResult(R.ok());
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return Result.toResult(R.error(e.getMessage()));
        }
    }

    /**
     * 移动
     * @param scriptId
     * @param classifyId
     * @return
     */
    @GetMapping("/moveAiModel")
    @FuncScanAnnotation(code = "AIModelingMove", name = "移动到", parentCode = "AIModeling")
    @ValidateAndLogAnnotation
    public Result moveAiModel(String scriptId,String classifyId){
        try {
            aiDataModelingService.moveAiModel(scriptId,classifyId);
            return Result.toResult(R.ok());
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return Result.toResult(R.error(e.getMessage()));
        }
    }

    /**
     * 删除
     * @param scriptId
     * @param
     * @return
     */
    @GetMapping("/deleteAiModel")
    @FuncScanAnnotation(code = "AIModelingDelete", name = "删除", parentCode = "AIModeling")
    @ValidateAndLogAnnotation
    public Result deleteAiModel(String scriptId){
        try {
            aiDataModelingService.deleteAiModel(scriptId);
            return Result.toResult(R.ok());
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return Result.toResult(R.error(e.getMessage()));
        }
    }


    @PostMapping("/editAiModelMsg")
    @FuncScanAnnotation(code = "AIModelingEdit", name = "编辑", parentCode = "AIModeling")
    @ValidateAndLogAnnotation
    public Result editAiModelMsg(@RequestBody AiModelBasicVo aiModelBasicVo){
        try {
            aiDataModelingService.editAiModel(aiModelBasicVo);
            return Result.toResult(R.ok());
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return Result.toResult(R.error(e.getMessage()));
        }
    }


    @GetMapping("/getAiModelDetail")
    public Result GetMapping(String scriptId){
        try {
            Map<String, Object> aiModelDetail = aiDataModelingService.getAiModelDetail(scriptId);
            return Result.toResult(R.ok(aiModelDetail));
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return Result.toResult(R.error(e.getMessage()));
        }
    }

    @GetMapping("/getNewestLogId")
    public Result getNewestLogId(String scriptId){
        try {

            return Result.toResult(R.ok(aiDataModelingService.getNewestLogIdByScriptId(scriptId)));
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return Result.toResult(R.error(e.getMessage()));
        }
    }


    /**
     * 调度作业启动，包括其子孩子调度
     * @param scriptId
     * @return
     */
    @GetMapping("/starTaskForSchedul")
    public Result starTaskForSchedul(String scriptId){
        try {
            String startAiModelUrl = aiDataModelingService.getStartAiModelUrl(scriptId);
            aiDataModelingService.saveScript(scriptId);
            String logId = aiDataModelingService.runScript(scriptId);
            return Result.toResult(R.ok(logId));
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return Result.toResult(R.error(e.getMessage()));
        }
    }

    /**
     * 保存定时任务
     * @param map
     * @return
     */
    @PostMapping("/saveScheduledTask")
    public Result saveScheduledTask(@RequestBody Map map){
        TransScheduleVo vo = new TransScheduleVo();
        vo.setTransId((String) map.get("transId"));
        vo.setScheduleType((String) map.get("scheduleType"));
        vo.setCron((String) map.get("cron"));
        vo.setDeclare((String) map.get("declare"));
        vo.setStartPrograms((List) map.get("startPrograms"));
        aiDataModelingService.saveScheduledTask(vo);
        return Result.success();
    }

    /**
     * 获取已创建的作业
     * @param
     * @return
     */
    @GetMapping("/getJobCreated")
    public Result getJobCreated(){
        aiDataModelingService.getJobCreated();
        return Result.success();
    }

    @GetMapping("/getSchedule")
    public Result getSchedule(String transId) {
        return Result.success(aiDataModelingService.getSchedulePlan(transId));
    }

    @GetMapping("/getSubTrans")
    public Result getSubTrans(String transId) {
        List subTrans = aiDataModelingService.getSubTrans(transId);
        return Result.success(subTrans);
    }

    @GetMapping("/getAiModelDirWithChildren")
    public Result getAiModelDirWithChildren(HttpSession session){

        String userId = (String) session.getAttribute("userId");
        if (StringUtils.isBlank(userId)){
            throw new RuntimeException("请先登录！");
        }
        try {
            List<ModelTreeResult> busiClassifies = aiDataModelingService.getAllAiModelWithChildren(userId, "time");
            return Result.toResult(R.ok(busiClassifies));
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return Result.toResult(R.error(e.getMessage()));
        }
    }

}
