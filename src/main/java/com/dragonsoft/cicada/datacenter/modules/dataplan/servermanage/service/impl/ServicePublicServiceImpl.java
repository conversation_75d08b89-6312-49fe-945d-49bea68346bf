package com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.service.impl;

import com.alibaba.fastjson.JSON;
import com.code.common.mist.service.publish.IServicePublisher;
import com.code.common.mist.service.structure.model.ClassMeta;
import com.code.common.mist.service.structure.model.ServiceClassMeta;
import com.code.common.utils.R;
import com.code.common.utils.assertion.Assert;
import com.code.metadata.sm.EnumServiceType;
import com.code.metadata.sm.ServiceMeta;
import com.code.metadata.sm.ServiceParams;
import com.code.metadata.sm.ServicePublication;
import com.code.metaservice.sm.IServiceMetaService;
import com.code.metaservice.sm.IServiceParamsService;
import com.dragoninfo.dfw.bean.Result;
import com.dragonsoft.cicada.datacenter.ServicePublishCenterClient;
import com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.service.DynamicStructureService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.service.ICicadaMetaServicePublishService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.service.IServicePublicService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.utils.ClientAccesstIpUtil;
import com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.utils.ServicePusblishUtil;
import com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.vo.BatchTestVo;
import com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.vo.ParamConfigVo;
import com.fw.service.BaseService;
import com.fw.service.annotation.Service;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;
import org.springframework.cloud.netflix.feign.EnableFeignClients;
import org.springframework.util.ClassUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.beans.Introspector;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;


@Service
// 添加注解声明是注册中心客户端
@EnableEurekaClient
// 实现不同子服务调用
@EnableFeignClients
@Slf4j
public class ServicePublicServiceImpl extends BaseService implements IServicePublicService {


    @Autowired
    private ServicePublishCenterClient servicePublishCenterClient;

    @Value("${dc.publish.path}")
    private String testPublishPath;

    @Value("${ai.dir.basicPath}")
    private String dirBasicPath;

    @Value("${service.publish.time}")
    private long servicePublishTime;

    private IServicePublisher servicePublisher;

    @Autowired
    private DynamicStructureService dynamicStructureService;
    @Autowired
    private IServiceMetaService serviceMetaService;

    @Autowired
    private IServiceParamsService serviceParamsService;


    @Autowired
    private ICicadaMetaServicePublishService cicadaMetaServicePublishService;

    public static final MediaType MEDIAJSON = MediaType.parse("application/json; charset=utf-8");


    @Override
    public R createService(ParamConfigVo paramConfigVo, String userId) {
        Assert.isTrue(StringUtils.isNotEmpty(paramConfigVo.getImplChineseName()), "ImplChineseName不能为空!");
        Assert.isTrue(StringUtils.isNotEmpty(paramConfigVo.getInterfaceChineseName()), "InterfaceChineseName不能为空!");
        //封装要发布的接口和实现类
        /*
        限制数据集只能发布一次
        String serviceId = this.isIssue(paramConfigVo.getModelId());
        if(StringUtils.isNotBlank(serviceId)) Assert.fail("该数据集已经发布过！");
        */
        //修改服务先卸载再重新发布
        //checkChineseName(paramConfigVo.getInterfaceChineseName(),paramConfigVo.getSaveOrUpdate());
//        if ("update".equals(paramConfigVo.getSaveOrUpdate())) {
//           /* ServiceMeta serviceMeta = serviceMetaService.queryByMetaId(paramConfigVo.getServiceMetaId());
//            String id = serviceMeta.getServicePublication().getId();
//            deleteElementAndClassify(id);*/
//            offlineServiceByServiceMetaId(paramConfigVo.getServiceMetaId());
//        }

        if (StringUtils.isBlank(paramConfigVo.getServiceMetaId())) {
            paramConfigVo.setInterfaceEnglishName(ServicePusblishUtil.buildImplClassName(paramConfigVo.getInterfaceChineseName()));
            paramConfigVo.setImplEnglishName(ServicePusblishUtil.buildImplClassName(paramConfigVo.getImplChineseName()));
        } else {
            paramConfigVo.setImplEnglishName(ServicePusblishUtil.getOldImplClassName(paramConfigVo.getImplEnglishName()));
        }
        ServiceClassMeta serviceClassMeta = dynamicStructureService.dynamicCreateClassMetaByVo(paramConfigVo);
        checkServiceName(serviceClassMeta.getImplMeta(), paramConfigVo);
        dirBasicPath = dirBasicPath.replace("\\", "/");
        if (dirBasicPath.toCharArray()[dirBasicPath.length() - 1] != '/') {
            dirBasicPath += '/';
        }
        serviceClassMeta.setUserId(userId);
        serviceClassMeta.setServiceMetaId(paramConfigVo.getServiceMetaId());
        serviceClassMeta.setServicePublicationId(paramConfigVo.getServicePublicationId());
        ServicePublishCenterClient.ServicePublishVo servicePublishVo = new ServicePublishCenterClient.ServicePublishVo();
        servicePublishVo.setServiceClassMeta(serviceClassMeta);
        servicePublishVo.setFilePath(ServicePusblishUtil.buildFilepath(dirBasicPath, paramConfigVo.getInterfaceEnglishName(), paramConfigVo.getImplVersion()));
        String serviceMetaId = servicePublishCenterClient.publish(servicePublishVo);
        //发布
        ServiceMeta serviceMeta = serviceMetaService.get(ServiceMeta.class, serviceMetaId);
        String id = serviceMeta.getServicePublication().getId();
        String classifyId = paramConfigVo.getClassifyId();
        Assert.hasLength(classifyId, "目录不能为空！");
        saveElementAndClassify(id, classifyId);
        return R.ok(serviceMetaId);
    }


    @Override
    public Result createDataQueryService(ParamConfigVo paramConfigVo, String userId) throws Exception {
        Assert.isTrue(StringUtils.isNotEmpty(paramConfigVo.getImplChineseName()), "ImplChineseName不能为空!");
        Assert.isTrue(StringUtils.isNotEmpty(paramConfigVo.getInterfaceChineseName()), "InterfaceChineseName不能为空!");

        if (StringUtils.isBlank(paramConfigVo.getServiceMetaId())) {
            paramConfigVo.setInterfaceEnglishName(ServicePusblishUtil.buildImplClassName(paramConfigVo.getInterfaceChineseName()));
            paramConfigVo.setImplEnglishName(ServicePusblishUtil.buildImplClassName(paramConfigVo.getImplChineseName()));
        } else {
            paramConfigVo.setImplEnglishName(ServicePusblishUtil.getOldImplClassName(paramConfigVo.getImplEnglishName()));
        }
        ServiceClassMeta serviceClassMeta = dynamicStructureService.dynamicQueryCreateClassMetaByVo(paramConfigVo,userId);

        dirBasicPath = dirBasicPath.replace("\\", "/");
        if (dirBasicPath.toCharArray()[dirBasicPath.length() - 1] != '/') {
            dirBasicPath += '/';
        }
        serviceClassMeta.setUserId(userId);
        ServicePublishCenterClient.ServicePublishVo servicePublishVo = new ServicePublishCenterClient.ServicePublishVo();
        servicePublishVo.setServiceClassMeta(serviceClassMeta);
        servicePublishVo.setFilePath(ServicePusblishUtil.buildFilepath(dirBasicPath, paramConfigVo.getInterfaceEnglishName(), paramConfigVo.getImplVersion()));
        String serviceMetaId = "";
        //保存并发布
        serviceMetaId = servicePublishCenterClient.publish(servicePublishVo);
        ServiceMeta serviceMeta = serviceMetaService.get(ServiceMeta.class, serviceMetaId);
        String id = serviceMeta.getServicePublication().getId();
        String classifyId = paramConfigVo.getClassifyId();
        Assert.hasLength(classifyId, "目录不能为空！");
        saveElementAndClassify(id, classifyId);
        return Result.success(serviceMeta.getServicePublication().getId());
    }

    @Override
    public Result createInformationVerificationService(ParamConfigVo paramConfigVo, String userId) throws Exception {
        Assert.isTrue(StringUtils.isNotEmpty(paramConfigVo.getImplChineseName()), "ImplChineseName不能为空!");
        Assert.isTrue(StringUtils.isNotEmpty(paramConfigVo.getInterfaceChineseName()), "InterfaceChineseName不能为空!");
        if (StringUtils.isBlank(paramConfigVo.getServiceMetaId())) {
            paramConfigVo.setInterfaceEnglishName(ServicePusblishUtil.buildImplClassName(paramConfigVo.getInterfaceChineseName()));
            paramConfigVo.setImplEnglishName(ServicePusblishUtil.buildImplClassName(paramConfigVo.getImplChineseName()));
        } else {
            paramConfigVo.setImplEnglishName(ServicePusblishUtil.getOldImplClassName(paramConfigVo.getImplEnglishName()));
        }

        // todo 这边需要修改成两种不同的构造方式
        ServiceClassMeta serviceClassMeta = new ServiceClassMeta();
        if ("2".equals(paramConfigVo.getSingleOrManyTable())) {
            serviceClassMeta = cicadaMetaServicePublishService.dynamicCreateClassMetaByVo(paramConfigVo, userId);
        } else {
            serviceClassMeta = dynamicStructureService.dynamicVerifcationCreateClassMetaByVo(paramConfigVo,userId);
        }
        dirBasicPath = dirBasicPath.replace("\\", "/");
        if (dirBasicPath.toCharArray()[dirBasicPath.length() - 1] != '/') {
            dirBasicPath += '/';
        }
        serviceClassMeta.setUserId(userId);
//        serviceClassMeta.setServiceMetaId(paramConfigVo.getServiceMetaId());
//        serviceClassMeta.setServicePublicationId(paramConfigVo.getServicePublicationId());
        ServicePublishCenterClient.ServicePublishVo servicePublishVo = new ServicePublishCenterClient.ServicePublishVo();
        servicePublishVo.setServiceClassMeta(serviceClassMeta);
        servicePublishVo.setFilePath(ServicePusblishUtil.buildFilepath(dirBasicPath, paramConfigVo.getInterfaceEnglishName(), paramConfigVo.getImplVersion()));
        String serviceMetaId = servicePublishCenterClient.publish(servicePublishVo);
        //发布
        ServiceMeta serviceMeta = serviceMetaService.get(ServiceMeta.class, serviceMetaId);
        String id = serviceMeta.getServicePublication().getId();
        String classifyId = paramConfigVo.getClassifyId();
        Assert.hasLength(classifyId, "目录不能为空！");
        saveElementAndClassify(id, classifyId);
        return Result.success(serviceMeta.getServicePublication().getId());
    }

    @Override
    public Result publishServiceNoSaveMeta(ParamConfigVo paramConfigVo, String userId) throws Exception {
        Assert.isTrue(StringUtils.isNotEmpty(paramConfigVo.getImplChineseName()), "ImplChineseName不能为空!");
        Assert.isTrue(StringUtils.isNotEmpty(paramConfigVo.getInterfaceChineseName()), "InterfaceChineseName不能为空!");
        if (StringUtils.isBlank(paramConfigVo.getServiceMetaId())) {
            paramConfigVo.setInterfaceEnglishName(ServicePusblishUtil.buildImplClassName(paramConfigVo.getInterfaceChineseName()));
            paramConfigVo.setImplEnglishName(ServicePusblishUtil.buildImplClassName(paramConfigVo.getImplChineseName()));
        } else {
            paramConfigVo.setImplEnglishName(ServicePusblishUtil.getOldImplClassName(paramConfigVo.getImplEnglishName()));
        }
        ServiceClassMeta serviceClassMeta = new ServiceClassMeta();
        if("6".equals(EnumServiceType.getServiceTypeByName(paramConfigVo.getServiceType()).getCode())){
            serviceClassMeta = dynamicStructureService.dynamicVerifcationCreateClassMetaByVo(paramConfigVo,userId);
        }else{
            serviceClassMeta = dynamicStructureService.dynamicQueryCreateClassMetaByVo(paramConfigVo,userId);
        }
        dirBasicPath = dirBasicPath.replace("\\", "/");
        if (dirBasicPath.toCharArray()[dirBasicPath.length() - 1] != '/') {
            dirBasicPath += '/';
        }

        serviceClassMeta.setUserId(userId);
        serviceClassMeta.setServiceMetaId(paramConfigVo.getServiceMetaId());
        serviceClassMeta.setServiceType(EnumServiceType.getServiceTypeByName(paramConfigVo.getServiceType()));
        serviceClassMeta.setServicePublicationId(paramConfigVo.getServiceId());
        ServicePublishCenterClient.ServicePublishVo servicePublishVo = new ServicePublishCenterClient.ServicePublishVo();
        servicePublishVo.setServiceClassMeta(serviceClassMeta);
        servicePublishVo.setFilePath(ServicePusblishUtil.buildFilepath(dirBasicPath, paramConfigVo.getInterfaceEnglishName(), paramConfigVo.getImplVersion()));
        String serviceMetaId = servicePublishCenterClient.publishWithoutMeta(servicePublishVo);
        ServiceMeta serviceMeta = serviceMetaService.get(ServiceMeta.class, serviceMetaId);
//        String id = serviceMeta.getServicePublication().getId();
//        String classifyId = paramConfigVo.getClassifyId();
//        Assert.hasLength(classifyId, "目录不能为空！");
//        saveElementAndClassify(id, classifyId);
        return Result.success(serviceMeta.getServicePublication().getId());
    }

    public void checkChineseName(ParamConfigVo paramConfigVo, String userId) {

        /*String sql = "select id from t_md_service_meta where name =:name";
        String id = this.baseDao.sqlQueryForValue(sql, addParam("name", paramConfigVo.getImplChineseName()).param());
        Assert.isNull(id, "请检查模型中文名称是否已存在！");*/
        Map<String, Object> paramsMap = Maps.newHashMap();
        String sql0 = "select m.id from t_md_service_meta m, t_md_service_publication p where m.service_id=p.id and p.name =:name and p.operate_user_id = :userId and p.id in (select element_id from t_md_classify_element)";
        if ("update".equals(paramConfigVo.getSaveOrUpdate())) {
            sql0 += " and m.id != :serviceMetaId";
            paramsMap.put("serviceMetaId", paramConfigVo.getServiceMetaId());
        }
        String classifyId = paramConfigVo.getClassifyId();
        /*if (StringUtils.isNotBlank(classifyId)) {
            sql0 += " and p.id in (select element_id from t_md_classify_element where busi_classify_id = :classifyId) ";
            paramsMap.put("classifyId",classifyId);
        }*/
        paramsMap.put("name", paramConfigVo.getInterfaceChineseName());
        paramsMap.put("userId", userId);
        String id1 = this.baseDao.sqlQueryForValue(sql0, paramsMap);
        Assert.isNull(id1, "API名称已存在！");
    }


    public void checkServiceName(ClassMeta classMeta, ParamConfigVo paramConfigVo) {
        String beanName = classMeta.getPromulgator().getCode() + "_" + classMeta.getVersion() + "_" + Introspector.decapitalize(ClassUtils.getShortName(classMeta.getFullName()));
        ServiceMeta serviceMeta = serviceMetaService.queryByCode(beanName);
        Assert.isNull(serviceMeta, "已存在相同的模型英文名称：" + paramConfigVo.getImplEnglishName() + "--->或相同的版本：" + paramConfigVo.getImplVersion());
        String sql = "select id from t_md_service_meta where implement =:implementName";
        String id = this.baseDao.sqlQueryForValue(sql, addParam("implementName", classMeta.getFullName()).param());
        Assert.isNull(id, "API接口名已存在！");
    }


    @Override
    public R testService(ParamConfigVo paramConfigVo, HttpServletRequest servletRequest) throws UnsupportedEncodingException {
        Assert.notNull(paramConfigVo, "请求参数不能为空");
        Assert.notNull(paramConfigVo.getServiceMetaId(), "服务ID不能为空");
        ServiceMeta serviceMeta = serviceMetaService.queryByMetaId(paramConfigVo.getServiceMetaId());
        Assert.notNull(serviceMeta, "服务不存在");
        String requestPath = serviceMeta.getRequestPath();
        //获取参数名称
        List<ServiceParams> serviceParams = serviceParamsService.queryParamByOwnerId(serviceMeta.getServicePublication().getId());
        Map<String, Object> paramMap = new HashMap<>();
        serviceParams.forEach(s -> {
            if (s.getIsOutput().equals("0"))
                paramMap.put(s.getCode(), s.getIsMust().equalsIgnoreCase("t") ? "true" : "false");
        });
        FormBody.Builder builder = new FormBody.Builder();
        paramConfigVo.getParamList().forEach(x -> {
            if (Boolean.parseBoolean((paramMap.get(x.getParamCode())).equals("t") ? "true" : "false") && StringUtils.isBlank(x.getDefaultValue()))
                Assert.fail(x.getParamCode() + " 该字段为必填项");
            builder.add(x.getParamCode(), x.getDefaultValue() == null ? "" : x.getDefaultValue());
        });
        RequestBody body = builder.build();
        OkHttpClient httpClient = new OkHttpClient().newBuilder().connectTimeout(servicePublishTime * 2, TimeUnit.MILLISECONDS)
                .readTimeout(servicePublishTime * 2, TimeUnit.MILLISECONDS)
                .build();
        /**
         * Reg_ID 是客户给的，客户的规范，初始化默认000001000001
         * 在一些系统参数建议抽个脚本去管理
         */
        Request request = new Request.Builder()
                .url(testPublishPath + requestPath)
                .addHeader("Accept", "application/json; charset=UTF-8")
                .addHeader("Reg_ID", "000001000001")
                .addHeader("Requester", URLEncoder.encode("数据建模分析", "UTF-8"))
                .addHeader("Terminal_ID", ClientAccesstIpUtil.getRemoteIpAddress(servletRequest))
                .addHeader("token", paramConfigVo.getToken())
                .post(body)
                .build();
        try {
            Response response = httpClient.newCall(request).execute();
            if (response.code() > 200 || response.code() < 200) {
                return R.error("code:" + response.code() + " message:" + response.message());
            }
            return R.ok(response.body().string());
        } catch (IOException e) {
         log.error(e.getMessage(),e);
            return R.error(e.getMessage());
        }
    }

    @Override
    public R testBatchService(BatchTestVo paramConfigVo, HttpServletRequest servletRequest) throws UnsupportedEncodingException {
        Assert.notNull(paramConfigVo, "请求参数不能为空");
        Assert.notNull(paramConfigVo.getServiceMetaId(), "服务ID不能为空");
        ServiceMeta serviceMeta = serviceMetaService.queryByMetaId(paramConfigVo.getServiceMetaId());
        Assert.notNull(serviceMeta, "服务不存在");
        String requestPath = serviceMeta.getRequestPath();
        //获取参数名称
        List<ServiceParams> serviceParams = serviceParamsService.queryParamByOwnerId(serviceMeta.getServicePublication().getId());
        Map<String, Object> paramMap = new HashMap<>();
        serviceParams.forEach(s -> {
            if (s.getIsOutput().equals("0"))
                paramMap.put(s.getCode(), s.getIsMust().equalsIgnoreCase("t") ? "true" : "false");
        });
        // FormBody.Builder builder = new FormBody.Builder();
//        paramConfigVo.getParamList().forEach(x -> {
//            if (Boolean.parseBoolean((paramMap.get(x.getParamCode())).equals("t") ? "true" : "false") && StringUtils.isBlank(x.getDefaultValue()))
//                Assert.fail(x.getParamCode() + " 该字段为必填项");
//            builder.add(x.getParamCode(), x.getDefaultValue() == null ? "" : x.getDefaultValue());
//        });

        //校验必填
        for (Map<String, Object> stringObjectMap : paramConfigVo.getParamVoList()) {
            Set<Map.Entry<String, Object>> entries = stringObjectMap.entrySet();
            for (Map.Entry<String, Object> entry : entries) {
                if (Boolean.parseBoolean(paramMap.get(entry.getKey()).toString()) && entry.getValue() == null) {
                    Assert.fail(entry.getKey() + " 该字段为必填项");
                }
            }
        }

        //builder.add("paramBatchParamVo", "{ \"paramVoList\":"+JSON.toJSONString(paramConfigVo.getParamVoList())+"}");
        RequestBody body = RequestBody.create(MEDIAJSON, "{ \"paramVoList\":" + JSON.toJSONString(paramConfigVo.getParamVoList()) + "}");
        //RequestBody body = builder.build();
        OkHttpClient httpClient = new OkHttpClient().newBuilder().connectTimeout(servicePublishTime * 2, TimeUnit.MILLISECONDS)
                .readTimeout(servicePublishTime * 2, TimeUnit.MILLISECONDS)
                .build();
        /**
         * Reg_ID 是客户给的，客户的规范，初始化默认000001000001
         * 在一些系统参数建议抽个脚本去管理
         */
        Request request = new Request.Builder()
                .url(testPublishPath + requestPath)
                .addHeader("Accept", "application/json; charset=UTF-8")
                .addHeader("Reg_ID", "000001000001")
                .addHeader("Requester", URLEncoder.encode("数据建模分析", "UTF-8"))
                .addHeader("Terminal_ID", ClientAccesstIpUtil.getRemoteIpAddress(servletRequest))
                .addHeader("token", paramConfigVo.getToken())
                .post(body)
                .build();
        try {
            Response response = httpClient.newCall(request).execute();
            if (response.code() > 200 || response.code() < 200) {
                return R.error("code:" + response.code() + " message:" + response.message());
            }
            return R.ok(response.body().string());
        } catch (IOException e) {
         log.error(e.getMessage(),e);
            return R.error(e.getMessage());
        }
    }

    private String getRemoteIpAddress() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();
        return request.getRemoteAddr();
    }

    @Override
    public String isIssue(String sourceId) {
        String sql = "select id from t_md_service_publication where source_id =:sourceId";
        String id = this.baseDao.sqlQueryForValue(sql, addParam("sourceId", sourceId).param());
        return id;
    }

    @Override
    public void offlineService(String sourceId) {
        String serviceId = this.isIssue(sourceId);
        if (StringUtils.isBlank(serviceId)) Assert.fail("该服务未发布，无法下线");
        String sql = "select id from t_md_service_meta where service_id =:serviceId";
        String id = this.baseDao.sqlQueryForValue(sql, addParam("serviceId", serviceId).param());
        servicePublishCenterClient.uninstall(id);
    }

    @Override
    public void offlineServiceByServiceMetaId(String serviceMetaId) {
        if (StringUtils.isBlank(serviceMetaId))
            Assert.fail("该服务未发布，无法下线");
        servicePublishCenterClient.uninstall(serviceMetaId);
    }

    @Override
    public void offlineServiceByServiceMetaIdComitSession(String serviceMetaId) {
        if (StringUtils.isBlank(serviceMetaId))
            Assert.fail("该服务未发布，无法下线");
        servicePublishCenterClient.uninstall(serviceMetaId);
    }

    @Override
    public void disableService(String serviceMetaId) {
        servicePublishCenterClient.offlineExport(serviceMetaId);
    }

    @Override
    public void redistributionService(String serviceMetaId) {
        servicePublishCenterClient.export(serviceMetaId);
    }

    @Override
    public void delServiceMetaId(String serviceMetaId) {
        servicePublishCenterClient.uninstall(serviceMetaId);
    }

    @Override
    public void checkPublishName(String serviceMetaId, String name, boolean isUpdate, String serviceType, String userId) {
        Map<String, Object> map = Maps.newHashMap();
        map.put("name", name);
        map.put("userId", userId);
        //map.put("serviceType",serviceType);
        String sql0 = "select p.id from t_md_service_publication p inner join t_md_service_meta m on p.id = m.service_id where p.name =:name and p.operate_user_id = :userId ";
        if (isUpdate) {
            sql0 += " and m.id != :serviceMetaId ";
            map.put("serviceMetaId", serviceMetaId);
        }

        String id1 = this.baseDao.sqlQueryForValue(sql0, map);
        Assert.isNull(id1, "请检查服务中文名称是否已存在！");
    }

    @Override
    public void checkPublishNameByClass(String serviceMetaId, String name, boolean isUpdate, String serviceType, String classifyId) {
        Map<String, Object> map = Maps.newHashMap();
        map.put("name", name);
        map.put("serviceType", serviceType);
        String sql0 = "select p.id from t_md_service_publication p inner join t_md_service_meta m on p.id = m.service_id where p.name =:name and p.service_type = :serviceType ";
        if (isUpdate) {
            sql0 += " and m.id != :serviceMetaId ";
            map.put("serviceMetaId", serviceMetaId);
        }
        if (StringUtils.isNotBlank(classifyId)) {
            sql0 += " and p.id in (select element_id from t_md_classify_element where busi_classify_id = :classifyId) ";
            map.put("classifyId", classifyId);
        }
        String id1 = this.baseDao.sqlQueryForValue(sql0, map);
        Assert.isNull(id1, "请检查服务中文名称是否已存在！");
    }

    @Override
    public void deleteElementAndClassify(String elementId) {
        String sql = "delete from t_md_classify_element where element_id = :elementId";
        this.baseDao.executeSqlUpdate(sql, addParam("elementId", elementId).param());
    }

    @Override
    public void deleteElementAndClassifyByMetaId(String serviceMetaId) {
        ServiceMeta serviceMeta = serviceMetaService.queryByMetaId(serviceMetaId);
        if (serviceMeta == null) Assert.fail("找不到该服务！");
        ServicePublication servicePublication = serviceMeta.getServicePublication();
        String id = servicePublication.getId();
        deleteElementAndClassify(id);
    }

    @Override
    public void saveElementAndClassifyByMetaId(String serviceMetaId, String classifyId) {
        Assert.hasLength(classifyId, "目录id不能为空！");
        ServiceMeta serviceMeta = serviceMetaService.queryByMetaId(serviceMetaId);
        if (serviceMeta == null) Assert.fail("找不到该服务！");
        ServicePublication servicePublication = serviceMeta.getServicePublication();
        String id = servicePublication.getId();
        saveElementAndClassify(id, classifyId);
    }

    @Override
    public void updateElementAndClassifyByMetaId(String serviceMetaId, String classifyId) {
        Assert.hasLength(classifyId, "目录id不能为空！");
        ServiceMeta serviceMeta = serviceMetaService.queryByMetaId(serviceMetaId);
        if (serviceMeta == null) Assert.fail("找不到该服务！");
        ServicePublication servicePublication = serviceMeta.getServicePublication();
        String id = servicePublication.getId();
        String sql = "update t_md_classify_element set busi_classify_id = :classifyId where element_id = :id";
        this.baseDao.executeSqlUpdate(sql, this.addParam("classifyId", classifyId).addParam("id", id).param());
    }

    @Override
    public void saveElementAndClassify(String elementId, String classifyId) {
        String sql = "insert into t_md_classify_element(element_id,busi_classify_id) values (:elementId,:classifyId)";
        this.baseDao.executeSqlUpdate(sql, addParam("elementId", elementId).addParam("classifyId", classifyId).param());
    }

    @Override
    public void checkTheSameNameInClassify(String id, String classifyId) {
        ServicePublication servicePublication = (ServicePublication) this.baseDao.get(ServicePublication.class, id);
        String name = servicePublication.getName();
        Map<String, Object> params = new HashMap<>();
        String sql = "select count(*) from t_md_service_publication where name = :name and id in (select element_id from t_md_classify_element where busi_classify_id = :classifyId)";
        params.put("name", name);
        params.put("classifyId", classifyId);

        if (StringUtils.isNotBlank(id)) {
            sql += " and id != :id ";
            params.put("id", id);
        }
        String count = this.baseDao.sqlQueryForValue(sql, params);
        if (Integer.parseInt(count) > 0) {
            Assert.fail("该目录下存在同名服务！");
        }
    }
}
