package com.dragonsoft.cicada.datacenter.modules.modeling.importandexport.enums;

//导出内容枚举
public enum ExportContentCaseEnum {
    CASE("CASE","仅导出用例"),
    API_CASE("API_CASE","包含api");

    public String code;
    public String name;

    ExportContentCaseEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static ExportContentCaseEnum getInstanceByName(String name) {
        ExportContentCaseEnum[] var1 = values();
        int var2 = var1.length;

        for(int var3 = 0; var3 < var2; ++var3) {
            ExportContentCaseEnum value = var1[var3];
            if (value.name.equals(name)) {
                return value;
            }
        }

        return null;
    }

    public static ExportContentCaseEnum getInstanceByCode(String code) {
        ExportContentCaseEnum[] var1 = values();
        int var2 = var1.length;

        for(int var3 = 0; var3 < var2; ++var3) {
            ExportContentCaseEnum value = var1[var3];
            if (value.code.equals(code)) {
                return value;
            }
        }

        return API_CASE;
    }
}
