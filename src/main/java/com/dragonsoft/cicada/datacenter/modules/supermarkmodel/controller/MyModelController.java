package com.dragonsoft.cicada.datacenter.modules.supermarkmodel.controller;


import com.code.common.paging.PageInfo;
import com.code.common.utils.R;
import com.code.common.utils.StringUtils;
import com.code.common.utils.assertion.Assert;
import com.dragoninfo.dfw.bean.Result;
import com.dragonsoft.cicada.datacenter.modules.supermarkmodel.service.IMyModelService;
import com.dragonsoft.cicada.datacenter.modules.supermarkmodel.vo.ModelLabelVo;
import com.dragonsoft.cicada.datacenter.modules.supermarkmodel.vo.MyFocusQueryVo;
import com.dragonsoft.cicada.datacenter.modules.supermarkmodel.vo.SupermarkModelVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.List;
import java.util.Map;

@CrossOrigin
@RestController
@RequestMapping("/supermarkmodel/mymodel")
@Api(value = "MYMODEL我的模型")
@Slf4j
public class MyModelController {

    @Autowired
    private IMyModelService myModelServiceImpl;


    @PostMapping("/unPublishModelList")
    @ApiOperation(value = "MYMODEL待发布的模型")
    public Result queryUnPublishModelList(HttpServletRequest request, @RequestBody Map<String, Object> queryModelMap) {
        PageInfo pageResult = null;
        try {
            String userId = (String) request.getSession().getAttribute("userId");

            queryModelMap.put("userId", userId);

            pageResult = myModelServiceImpl.queryUnPublishModelPage(queryModelMap);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            Assert.fail("待发布的模型查询失败：" + e.getMessage());
        }

        return Result.success(pageResult);
    }


    @PostMapping("/publishedModelList")
    @ApiOperation(value = "MYMODEL已发布的模型")
    public Result queryPublishedModelList(HttpServletRequest request, @RequestBody Map<String, Object> queryModelMap) {
        PageInfo pageResult = null;
        try {
            String userId = (String) request.getSession().getAttribute("userId");

            queryModelMap.put("userId", userId);

            pageResult = myModelServiceImpl.queryPublishedModelPage(queryModelMap);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            Assert.fail("已发布的模型查询失败：" + e.getMessage());
        }

        return Result.success(pageResult);
    }

    @PostMapping("/initPublishModel")
    @ApiOperation(value = "MYMODEL模型发布数据初始化")
    public Result publishModelInit() {
        Result result = myModelServiceImpl.publishModelInit();
        return result;
    }

    @PostMapping("/publishModel")
    @ApiOperation(value = "MYMODEL发布模型")
    public Result publishModel(@RequestBody SupermarkModelVo modelVo, HttpServletRequest request) {
        Result result = null;
        try {
            String userId = (String) request.getSession().getAttribute("userId");
            modelVo.setUserId(userId);

            result = myModelServiceImpl.insertPublishModel(modelVo);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            Assert.fail("模型发布失败：" + e.getMessage());
        }

        return result;
    }

    @PostMapping("/addModelLabel")
    @ApiOperation(value = "MYMODEL新增标签")
    public Result addModelLabel(@RequestBody ModelLabelVo labelVo) {
        Result result = null;
        try {
            result = myModelServiceImpl.insertModelLabel(labelVo);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            Assert.fail("新增标签失败：" + e.getMessage());
        }
        return result;
    }

    @PostMapping("/queryModelLabelList")
    @ApiOperation(value = "MYMODEL查询标签")
    public Result queryModelLabelList(@RequestBody ModelLabelVo labelVo, HttpServletRequest request) {
        List<Map> labelList = null;
        try {
            String userId = (String) request.getSession().getAttribute("userId");
            labelList = myModelServiceImpl.queryModelLabelList(labelVo, userId);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            Assert.fail("查询标签失败：" + e.getMessage());
        }
        return Result.success(labelList);
    }

    @PostMapping("/initUpdateMarkModel")
    @ApiOperation(value = "MYMODEL编辑模型查询数据")
    public Result initUpdateMarkModel(@RequestBody SupermarkModelVo modelVo) {
        Result result = null;
        try {
            result = myModelServiceImpl.initUpdateMarkModel(modelVo);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            Assert.fail("编辑模型查询数据失败：" + e.getMessage());
        }

        return result;
    }

    @PostMapping("/updateMarkModel")
    @ApiOperation(value = "MYMODEL编辑模型")
    public Result updateMarkModel(@RequestBody SupermarkModelVo modelVo, HttpServletRequest request) {
        Result result = null;
        try {
            String userId = (String) request.getSession().getAttribute("userId");
            modelVo.setUserId(userId);

            result = myModelServiceImpl.updateMarkModel(modelVo);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            Assert.fail("编辑模型失败：" + e.getMessage());
        }

        return result;
    }

    @PostMapping("/updateModelState")
    @ApiOperation(value = "MYMODEL模型上下架")
    public Result updateModelState(@RequestBody SupermarkModelVo modelVo) {
        Result result = null;
        try {
            result = myModelServiceImpl.updateModelState(modelVo);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            Assert.fail("模型上下架失败：" + e.getMessage());
        }
        return result;
    }

    @PostMapping("/updateEnabledState")
    @ApiOperation(value = "MYMODEL模型启停用")
    public Result updateEnabledState(@RequestBody SupermarkModelVo modelVo) {
        Result result = null;
        try {
            result = myModelServiceImpl.updateEnabledState(modelVo);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            Assert.fail("模型启停用失败：" + e.getMessage());
        }
        return result;
    }

    @PostMapping("/deleteModel")
    @ApiOperation(value = "MYMODEL删除模型")
    public Result deleteMarkModel(@RequestBody SupermarkModelVo modelVo) {
        Result result = null;
        try {
            result = myModelServiceImpl.deleteMarkModel(modelVo);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            Assert.fail("删除模型失败：" + e.getMessage());
        }
        return result;
    }


    @PostMapping("/myFocusModelPage")
    @ApiOperation(value = "我的关注列表")
    public Result myFocusModelPage(@RequestBody MyFocusQueryVo queryVo, HttpSession session) {
        try {
            String userId = (String) session.getAttribute("userId");
            //String userId = "40289754739d4e7e11729d4e682b2020";
            if (StringUtils.isBlank(userId)){
                throw new RuntimeException("请先登录！");
            }
            PageInfo pageInfo = myModelServiceImpl.queryMyFocusPage(queryVo, userId);
            return Result.toResult(R.ok(pageInfo));
        } catch (Exception e) {
            Assert.fail("查询我的关注列表异常:" + e.getMessage());
            log.error(e.getMessage(),e);
            return Result.toResult(R.error(e.getMessage()));
        }
    }

    /**
     * 批量取消关注
     * @param modelIds
     * @param session
     * @return
     */
    @PostMapping("/cancelMyFocusList")
    @ApiOperation(value = "批量取消关注")
    public Result myFocusModelPage(@RequestBody List<String> modelIds, HttpSession session) {
        try {
            //String userId = (String) session.getAttribute("userId");
            String userId = "40289754739d4e7e11729d4e682b2020";
            if (StringUtils.isBlank(userId)){
                throw new RuntimeException("请先登录！");
            }
            myModelServiceImpl.cancelMyFocus(modelIds,userId);
            return Result.toResult(R.ok());
        } catch (Exception e) {
            Assert.fail("取消我的关注异常:" + e.getMessage());
            log.error(e.getMessage(),e);
            return Result.toResult(R.error(e.getMessage()));
        }
    }
}
