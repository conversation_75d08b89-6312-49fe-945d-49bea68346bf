package com.dragonsoft.cicada.datacenter.modules.system.permissions.controller;


import com.dragoninfo.dfw.bean.Result;
import com.dragonsoft.cicada.datacenter.modules.system.common.vo.TreeVo;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.service.IFunctionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/5/27
 */
@Slf4j
@Controller
@CrossOrigin
@RequestMapping("/function")
public class FunctionController {

    @Autowired
    private IFunctionService functionService;

    @ResponseBody
    @RequestMapping(value ="/queryFunctionTree",method = RequestMethod.POST)
    public Result queryFunctionTree(String systemType) {
        List<TreeVo> treeVos = functionService.queryFunctionTree(systemType);
        return Result.success(treeVos);
    }

    @ResponseBody
    @RequestMapping(value ="/queryExpFunctionTree",method = RequestMethod.POST)
    public Result queryExpFunctionTree() {
        List<TreeVo> treeVos = functionService.queryExpFunctionTree();
        return Result.success(treeVos);
    }

    @ResponseBody
    @RequestMapping(value ="/queryShowDocFuncCode",method = RequestMethod.GET)
    public Result queryShowDocFuncCode() {
        List<TreeVo> treeVos = functionService.queryShowDocFuncCodes();
        return Result.success(treeVos);
    }

    @ResponseBody
    @RequestMapping(value ="/queryShowDocMsg",method = RequestMethod.GET)
    public Result queryShowDocMsg() {
        Map<String, Object> map = new HashMap<>();
        try {
            map = functionService.queryShowDocMsg();
            return Result.success(map);
        } catch (Exception e) {
            log.error("查询帮助文档详情异常！",e);
            return Result.success(map);
        }
    }
}
