package com.dragonsoft.cicada.datacenter.modules.system.busmanagement.service;

import com.code.common.paging.PageInfo;
import com.dragonsoft.cicada.datacenter.modules.system.busmanagement.vo.InterfaceCfgVo;
import com.dragonsoft.cicada.datacenter.modules.system.busmanagement.vo.ServiceInfoVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/03/15
 */
public interface IServiceInfoManagementService {
    PageInfo getServiceInfoPage(PageInfo pageInfo,String userId,String keyWord);

    ServiceInfoVo getServiceInfoById(String id);

    String tSaveServiceInfo(ServiceInfoVo serviceInfoVo,String userId);

    void deleteServiceById(String id);

    InterfaceCfgVo getInterfaceCfgVoById(String id);

    void saveInterfaceBodyService(InterfaceCfgVo interfaceCfgVo,String userId);

    void deleteInterfaceBodyById(String id);


    /**
     * 查询所有服务
     * @return
     */
    List<ServiceInfoVo> getServiceInfoList(String userId);
    /**
     * 通过服务id查询所有接口
     * @param serviceId
     * @return
     */
    List<InterfaceCfgVo> getInterfaceInfoListBySId(String serviceId);


    /**
     * 通过接口id即接口配置id查询请求体
     * @param configId
     * @return
     */
    InterfaceCfgVo getBodyAttributeByCfgId(String configId);
    /**
     * 获取上一个请求体的数据
     * @param userId
     * @return
     */
    ServiceInfoVo getRequestHeader(String userId);


}
