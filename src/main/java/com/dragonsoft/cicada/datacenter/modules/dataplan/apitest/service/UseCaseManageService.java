package com.dragonsoft.cicada.datacenter.modules.dataplan.apitest.service;

import com.code.common.paging.PageInfo;
import com.dragonsoft.cicada.datacenter.modules.dataplan.apitest.vo.UseCaseVo;

import java.util.List;

public interface UseCaseManageService {

    PageInfo getUseCasePage(String classifyId,String name,Integer pageSize,Integer pageIndex);

    void saveUseCase(UseCaseVo useCaseVo,String userId);

    List<UseCaseVo> getImportApiInfo(List<String> caseIds);

    UseCaseVo getUseCaseDetailById(String useCaseId);
}
