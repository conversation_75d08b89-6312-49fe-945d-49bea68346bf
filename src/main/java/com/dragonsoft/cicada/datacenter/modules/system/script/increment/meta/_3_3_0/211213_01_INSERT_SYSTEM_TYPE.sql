DELETE FROM
    t_md_element
WHERE
    TYPE = 'DataType'
  AND owner_id IN ( SELECT ID FROM t_md_type_system WHERE code IN ( 'GREENPLUM', 'hwmpp','POSTGRESQL' ) );

DELETE FROM
    t_md_software
WHERE
        type_system_id IN ( SELECT ID FROM t_md_type_system WHERE code IN ( 'GREENPLUM', 'hwmpp','POSTGRESQL' ) );

DELETE FROM
    t_md_type_system
WHERE
        code IN ( 'GREENPLUM', 'hwmpp','POSTGRESQL' );




INSERT INTO "public"."t_md_type_system"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id", "type_version") VALUES ('********************************', 'GREENPLUM数据类型', NULL, 'TypeSystem', NULL, 'GREENPLUM', NULL, NULL, NULL, NULL, NULL, '4.3.7.3');
INSERT INTO "public"."t_md_type_system"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id", "type_version") VALUES ('********************************', 'hwmpp数据类型', NULL, 'TypeSystem', NULL, 'hwmpp', NULL, NULL, NULL, '2020-11-11 15:18:03', NULL, 'hw9.4');
INSERT INTO "public"."t_md_type_system" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id", "type_version") VALUES ('********************************', 'POSTGRESQL数据类型', NULL, 'TypeSystem', NULL, 'POSTGRESQL', NULL, NULL, NULL, NULL, NULL, '9.4');


INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028f9fc59a545110159a5455ebd0063', 'GREENPLUM.BIT', NULL, 'DataType', NULL, 'BIT', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028f9fc59a545110159a5455eba004b', 'GREENPLUM.BOOL', NULL, 'DataType', NULL, 'BOOL', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028f9fc59a545110159a5455ebc005c', 'GREENPLUM.BOX', NULL, 'DataType', NULL, 'BOX', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028f9fc59a545110159a5455ebb0054', 'GREENPLUM.BYTEA', NULL, 'DataType', NULL, 'BYTEA', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028f9fc59a545110159a5455eba004c', 'GREENPLUM.CHAR', NULL, 'DataType', NULL, 'CHAR', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028f9fc59a545110159a5455ebe0066', 'GREENPLUM.CIDR', NULL, 'DataType', NULL, 'CIDR', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028f9fc59a545110159a5455ebd005f', 'GREENPLUM.CIRCLE', NULL, 'DataType', NULL, 'CIRCLE', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028f9fc59a545110159a5455ebe0065', 'GREENPLUM.DATE', NULL, 'DataType', NULL, 'DATE', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028f9fc59a545110159a5455ebd005e', 'GREENPLUM.DECIMAL', NULL, 'DataType', NULL, 'DECIMAL', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028f9fc59a545110159a5455ebc0056', 'GREENPLUM.DOMAIN', NULL, 'DataType', NULL, 'DOMAIN', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028f9fc59a545110159a5455ebb0050', 'GREENPLUM.FLOAT4', NULL, 'DataType', NULL, 'FLOAT4', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028f9fc59a545110159a5455ebb0052', 'GREENPLUM.FLOAT8', NULL, 'DataType', NULL, 'FLOAT8', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028f9fc59a545110159a5455ebc005d', 'GREENPLUM.INET', NULL, 'DataType', NULL, 'INET', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028f9fc59a545110159a5455ebb0053', 'GREENPLUM.INT2', NULL, 'DataType', NULL, 'INT2', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028f9fc59a545110159a5455ebc0057', 'GREENPLUM.INT4', NULL, 'DataType', NULL, 'INT4', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028f9fc59a545110159a5455eba004d', 'GREENPLUM.INT8', NULL, 'DataType', NULL, 'INT8', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028f9fc59a545110159a5455ebc0058', 'GREENPLUM.INTERVAL', NULL, 'DataType', NULL, 'INTERVAL', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('35f72d5c4bc949e2a012c4f5c3c53960', 'GREENPLUM.JSON', NULL, 'DataType', NULL, 'JSON', '********************************', NULL, NULL, '2020-05-14 10:08:31', NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('77c5abda458d4724a223ea23bca65cb4', 'GREENPLUM.JSONB', NULL, 'DataType', NULL, 'JSONB', '********************************', NULL, NULL, '2020-05-14 10:08:31', NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028f9fc59a545110159a5455ebc0055', 'GREENPLUM.LINE', NULL, 'DataType', NULL, 'LINE', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028f9fc59a545110159a5455ebd0062', 'GREENPLUM.LSEG', NULL, 'DataType', NULL, 'LSEG', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028f9fc59a545110159a5455ebb004e', 'GREENPLUM.MACADDR', NULL, 'DataType', NULL, 'MACADDR', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028f9fc59a545110159a5455ebe0064', 'GREENPLUM.MONEY', NULL, 'DataType', NULL, 'MONEY', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('a34fb5e1d65549118cdb2baa232b4d04', 'GREENPLUM.NUMERIC', NULL, 'DataType', NULL, 'NUMERIC', '********************************', NULL, NULL, '2020-05-14 10:08:31', NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028f9fc59a545110159a5455ebd0060', 'GREENPLUM.PATH', NULL, 'DataType', NULL, 'PATH', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028f9fc59a545110159a5455eba0048', 'GREENPLUM.POINT', NULL, 'DataType', NULL, 'POINT', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028f9fc59a545110159a5455eba004a', 'GREENPLUM.POLYGON', NULL, 'DataType', NULL, 'POLYGON', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('250ffc4fccec4a5984d515a73241a04d', 'GREENPLUM.SERIAL2', NULL, 'DataType', NULL, 'SERIAL2', '********************************', NULL, NULL, '2020-05-14 10:08:31', NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028f9fc59a545110159a5455eba0049', 'GREENPLUM.SERIAL4', NULL, 'DataType', NULL, 'SERIAL4', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028f9fc59a545110159a5455eba0047', 'GREENPLUM.SERIAL8', NULL, 'DataType', NULL, 'SERIAL8', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028f9fc59a545110159a5455ebb0051', 'GREENPLUM.TEXT', NULL, 'DataType', NULL, 'TEXT', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028f9fc59a545110159a5455ebc005b', 'GREENPLUM.TIME', NULL, 'DataType', NULL, 'TIME', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028f9fc59a545110159a5455ebe0067', 'GREENPLUM.TIMESTAMP', NULL, 'DataType', NULL, 'TIMESTAMP', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028f9fc59a545110159a5455ebd0061', 'GREENPLUM.TIMESTAMPTZ', NULL, 'DataType', NULL, 'TIMESTAMPTZ', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028f9fc59a545110159a5455eba0046', 'GREENPLUM.TIMETZ', NULL, 'DataType', NULL, 'TIMETZ', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('ff95ffbea867442caf0f682eea3abdb0', 'GREENPLUM.TSQUERY', NULL, 'DataType', NULL, 'TSQUERY', '********************************', NULL, NULL, '2020-05-14 10:08:31', NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('9451a722b1804f4b9b38c0595dea1ad3', 'GREENPLUM.TSVECTOR', NULL, 'DataType', NULL, 'TSVECTOR', '********************************', NULL, NULL, '2020-05-14 10:08:31', NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('7f0396b5010441909bbd28168b311657', 'GREENPLUM.TXID_SNAPSHOT', NULL, 'DataType', NULL, 'TXID_SNAPSHOT', '********************************', NULL, NULL, '2020-05-14 10:08:31', NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028f9fc59a545110159a5455ebc0059', 'GREENPLUM.TYPE', NULL, 'DataType', NULL, 'TYPE', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('ef2c496f3d4d414882ee62435de59915', 'GREENPLUM.UNDEFINED', NULL, 'DataType', NULL, 'UNDEFINED', '********************************', NULL, NULL, '2020-05-14 10:08:31', NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('6020e9b2df6843d18f69889d0340429f', 'GREENPLUM.UUID', NULL, 'DataType', NULL, 'UUID', '********************************', NULL, NULL, '2020-05-14 10:08:31', NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028f9fc59a545110159a5455ebc005a', 'GREENPLUM.VARBIT', NULL, 'DataType', NULL, 'VARBIT', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028f9fc59a545110159a5455ebb004f', 'GREENPLUM.VARCHAR', NULL, 'DataType', NULL, 'VARCHAR', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4e7cf8d075e74731a23c12cd92babdf5', 'GREENPLUM.XML', NULL, 'DataType', NULL, 'XML', '********************************', NULL, NULL, '2020-05-14 10:08:31', NULL);

INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('023f23de95ab4f85ab8c2064f0a680c7', 'hwmpp.BIT', NULL, 'DataType', NULL, 'BIT', '********************************', NULL, NULL, '2020-11-11 15:18:03', NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('d3a4fc0fcac94b01ba994c7d81486e5a', 'hwmpp.BOOL', NULL, 'DataType', NULL, 'BOOL', '********************************', NULL, NULL, '2020-11-11 15:18:03', NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('b90f1b8bf7504ac09f9bbdb7e33c4144', 'hwmpp.BOX', NULL, 'DataType', NULL, 'BOX', '********************************', NULL, NULL, '2020-11-11 15:18:03', NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('7d9142e7a2444cb889350f36f8121085', 'hwmpp.BYTEA', NULL, 'DataType', NULL, 'BYTEA', '********************************', NULL, NULL, '2020-11-11 15:18:03', NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('6983c62f3ba54e72ad7ab36221f370b5', 'hwmpp.CHAR', NULL, 'DataType', NULL, 'CHAR', '********************************', NULL, NULL, '2020-11-11 15:18:03', NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('ea4307625a614c4d9edcd45d7017cfeb', 'hwmpp.CIDR', NULL, 'DataType', NULL, 'CIDR', '********************************', NULL, NULL, '2020-11-11 15:18:03', NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('2fd7c5297cd5481ca3ca627d3712ac73', 'hwmpp.CIRCLE', NULL, 'DataType', NULL, 'CIRCLE', '********************************', NULL, NULL, '2020-11-11 15:18:03', NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('8b93435d85554a9d8cc14376e28b856b', 'hwmpp.DATE', NULL, 'DataType', NULL, 'DATE', '********************************', NULL, NULL, '2020-11-11 15:18:03', NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('efcce6ea5d22412ea40637fed4f59a21', 'hwmpp.DECIMAL', NULL, 'DataType', NULL, 'DECIMAL', '********************************', NULL, NULL, '2020-11-11 15:18:03', NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('8823022811eb42d89b0007bdbcc91f16', 'hwmpp.DOMAIN', NULL, 'DataType', NULL, 'DOMAIN', '********************************', NULL, NULL, '2020-11-11 15:18:03', NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('09b64e70c3de47bd821a0f2d19b962b1', 'hwmpp.FLOAT4', NULL, 'DataType', NULL, 'FLOAT4', '********************************', NULL, NULL, '2020-11-11 15:18:03', NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('e482f056800f42f78364e122bcb787f8', 'hwmpp.FLOAT8', NULL, 'DataType', NULL, 'FLOAT8', '********************************', NULL, NULL, '2020-11-11 15:18:03', NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('dee4e0558e9243448600c94c3f11f353', 'hwmpp.INET', NULL, 'DataType', NULL, 'INET', '********************************', NULL, NULL, '2020-11-11 15:18:03', NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('cf21f3550c694d3e9f571db1b25a181a', 'hwmpp.INT2', NULL, 'DataType', NULL, 'INT2', '********************************', NULL, NULL, '2020-11-11 15:18:03', NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('d314a79f15ae4473b9c5f0d4063211cb', 'hwmpp.INT4', NULL, 'DataType', NULL, 'INT4', '********************************', NULL, NULL, '2020-11-11 15:18:03', NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('a2478673fd3e447682ba913b2a5b51d6', 'hwmpp.INT8', NULL, 'DataType', NULL, 'INT8', '********************************', NULL, NULL, '2020-11-11 15:18:03', NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('e1d5876e21d547809d34a284a23de358', 'hwmpp.INTERVAL', NULL, 'DataType', NULL, 'INTERVAL', '********************************', NULL, NULL, '2020-11-11 15:18:03', NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4b08b9fd6826487185e70749b75d97f1', 'hwmpp.JSON', NULL, 'DataType', NULL, 'JSON', '********************************', NULL, NULL, '2020-11-11 15:18:03', NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('ba68b76321aa4476a5d9e39fd2209d85', 'hwmpp.JSONB', NULL, 'DataType', NULL, 'JSONB', '********************************', NULL, NULL, '2020-11-11 15:18:03', NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('672d4c531c2b4a368085fa0b35da0e6f', 'hwmpp.LINE', NULL, 'DataType', NULL, 'LINE', '********************************', NULL, NULL, '2020-11-11 15:18:03', NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('2677d0c7c9fb4628bac977df9866d8f2', 'hwmpp.LSEG', NULL, 'DataType', NULL, 'LSEG', '********************************', NULL, NULL, '2020-11-11 15:18:03', NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('5727fe4f201f4ac084a42f0480e04cf6', 'hwmpp.MACADDR', NULL, 'DataType', NULL, 'MACADDR', '********************************', NULL, NULL, '2020-11-11 15:18:03', NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('b57534b25a714a629def819d45b139ca', 'hwmpp.MONEY', NULL, 'DataType', NULL, 'MONEY', '********************************', NULL, NULL, '2020-11-11 15:18:03', NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('45a4072ab8fc48cabb64899cc856b646', 'hwmpp.NUMERIC', NULL, 'DataType', NULL, 'NUMERIC', '********************************', NULL, NULL, '2020-11-11 15:18:03', NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('f83a4c8d5cfc42e0a88ea392dd491618', 'hwmpp.PATH', NULL, 'DataType', NULL, 'PATH', '********************************', NULL, NULL, '2020-11-11 15:18:03', NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('da44e2bec1ab4556a18e4ce2e8329828', 'hwmpp.POINT', NULL, 'DataType', NULL, 'POINT', '********************************', NULL, NULL, '2020-11-11 15:18:03', NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('dc573f1eea3d42a2bc213cb1894958c5', 'hwmpp.POLYGON', NULL, 'DataType', NULL, 'POLYGON', '********************************', NULL, NULL, '2020-11-11 15:18:03', NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('225c7151c0e140bcbbed6c61651361c9', 'hwmpp.SERIAL2', NULL, 'DataType', NULL, 'SERIAL2', '********************************', NULL, NULL, '2020-11-11 15:18:03', NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('09f6a640a1ff42aa92ae52945d16a418', 'hwmpp.SERIAL4', NULL, 'DataType', NULL, 'SERIAL4', '********************************', NULL, NULL, '2020-11-11 15:18:03', NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('9de5c79f64804fb08cfd1552e7527b00', 'hwmpp.SERIAL8', NULL, 'DataType', NULL, 'SERIAL8', '********************************', NULL, NULL, '2020-11-11 15:18:03', NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('a7c8fbffc35e41c192ad5266be31598f', 'hwmpp.TEXT', NULL, 'DataType', NULL, 'TEXT', '********************************', NULL, NULL, '2020-11-11 15:18:03', NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('47d7d7c2fff84629ad22f3030c4b9229', 'hwmpp.TIME', NULL, 'DataType', NULL, 'TIME', '********************************', NULL, NULL, '2020-11-11 15:18:03', NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('b8d63719eb384ad6a441feae643313bb', 'hwmpp.TIMESTAMP', NULL, 'DataType', NULL, 'TIMESTAMP', '********************************', NULL, NULL, '2020-11-11 15:18:03', NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('0454b0e6eef94796933066394de8ceef', 'hwmpp.TIMESTAMPTZ', NULL, 'DataType', NULL, 'TIMESTAMPTZ', '********************************', NULL, NULL, '2020-11-11 15:18:03', NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('a5ba9fc144cb458c88abf98d367c0125', 'hwmpp.TIMETZ', NULL, 'DataType', NULL, 'TIMETZ', '********************************', NULL, NULL, '2020-11-11 15:18:03', NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('6aafb5aa86ae46839d06c370c0e17717', 'hwmpp.TSQUERY', NULL, 'DataType', NULL, 'TSQUERY', '********************************', NULL, NULL, '2020-11-11 15:18:03', NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('9812974283f14d829ae4146d63375bf6', 'hwmpp.TSVECTOR', NULL, 'DataType', NULL, 'TSVECTOR', '********************************', NULL, NULL, '2020-11-11 15:18:03', NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('1a298b0144aa43cd91d2e6161102195d', 'hwmpp.TXID_SNAPSHOT', NULL, 'DataType', NULL, 'TXID_SNAPSHOT', '********************************', NULL, NULL, '2020-11-11 15:18:03', NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('04f1907b13a14a41aa7e97e38faed37c', 'hwmpp.TYPE', NULL, 'DataType', NULL, 'TYPE', '********************************', NULL, NULL, '2020-11-11 15:18:03', NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('28b97077d52f4ceab2d298775e14136c', 'hwmpp.UNDEFINED', NULL, 'DataType', NULL, 'UNDEFINED', '********************************', NULL, NULL, '2020-11-11 15:18:03', NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('126c61ccf1064df2b86cfc295924eb2d', 'hwmpp.UUID', NULL, 'DataType', NULL, 'UUID', '********************************', NULL, NULL, '2020-11-11 15:18:03', NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('79bdb9c2db364424906ed1db2a15b2d5', 'hwmpp.VARBIT', NULL, 'DataType', NULL, 'VARBIT', '********************************', NULL, NULL, '2020-11-11 15:18:03', NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('ec9b726ad84f405aa4126481cf6cec6e', 'hwmpp.VARCHAR', NULL, 'DataType', NULL, 'VARCHAR', '********************************', NULL, NULL, '2020-11-11 15:18:03', NULL);
INSERT INTO "public"."t_md_element"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('a15c68232dff4f80be81fb7387706307', 'hwmpp.XML', NULL, 'DataType', NULL, 'XML', '********************************', NULL, NULL, '2020-11-11 15:18:03', NULL);


INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('f37dc52189934eb5bc139f00bb52956d', 'POSTGRESQL.INT1', NULL, 'DataType', NULL, 'INT1', '********************************', NULL, NULL, '2020-08-19 11:18:37', NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('d46778ad8ee74c908e0aed64d4d42910', 'POSTGRESQL.INT1', NULL, 'DataType', NULL, 'INT1', '********************************', NULL, NULL, '2021-09-02 14:53:27', NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028b8855c3e5aef015c3e5b95b801f5', 'POSTGRESQL.CIDR', NULL, 'DataType', NULL, 'CIDR', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('f36fdbee1ec94d04b702406db23d5664', 'POSTGRESQL.INT1', NULL, 'DataType', NULL, 'INT1', '********************************', NULL, NULL, '2020-08-19 09:35:32', NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('acef9244e5ad45d1a920b06eea28f616', 'POSTGRESQL.INT1', NULL, 'DataType', NULL, 'INT1', '********************************', NULL, NULL, '2020-08-19 11:10:38', NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4b6dd6116a17451983038e4f53b80e34', 'POSTGRESQL.INT1', NULL, 'DataType', NULL, 'INT1', '********************************', NULL, NULL, '2020-08-19 11:11:38', NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4368588afc7d4c0eb7d5eeb90edee0ba', 'POSTGRESQL.INT1', NULL, 'DataType', NULL, 'INT1', '********************************', NULL, NULL, '2021-03-17 09:10:21', NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('7a0619bda62748cdad97ad3926a0460d', 'POSTGRESQL.INT1', NULL, 'DataType', NULL, 'INT1', '********************************', NULL, NULL, '2021-03-17 09:10:52', NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('1eae8fe9137c43dd86349e80eef54385', 'POSTGRESQL.INT1', NULL, 'DataType', NULL, 'INT1', '********************************', NULL, NULL, '2021-08-06 16:07:46', NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028b8855c3e5aef015c3e5b95b701ee', 'POSTGRESQL.PATH', NULL, 'DataType', NULL, 'PATH', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028b8855c3e5aef015c3e5b95b601e0', 'POSTGRESQL.DOMAIN', NULL, 'DataType', NULL, 'DOMAIN', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028b8855c3e5aef015c3e5b95b701f3', 'POSTGRESQL.MONEY', NULL, 'DataType', NULL, 'MONEY', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028b8855c3e5aef015c3e5b95b501d2', 'POSTGRESQL.POLYGON', NULL, 'DataType', NULL, 'POLYGON', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028b8855c3e5aef015c3e5b95b801f6', 'POSTGRESQL.TSQUERY', NULL, 'DataType', NULL, 'TSQUERY', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028b8855c3e5aef015c3e5b95b601e6', 'POSTGRESQL.UNDEFINED', NULL, 'DataType', NULL, 'UNDEFINED', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028b8855c3e5aef015c3e5b95b501d5', 'POSTGRESQL.CHAR', NULL, 'DataType', NULL, 'CHAR', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028b8855c3e5aef015c3e5b95b501d1', 'POSTGRESQL.SERIAL4', NULL, 'DataType', NULL, 'SERIAL4', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028b8855c3e5aef015c3e5b95b701ec', 'POSTGRESQL.DECIMAL', NULL, 'DataType', NULL, 'DECIMAL', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028b8855c3e5aef015c3e5b95b501da', 'POSTGRESQL.TEXT', NULL, 'DataType', NULL, 'TEXT', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028b8855c3e5aef015c3e5b95b701ea', 'POSTGRESQL.JSON', NULL, 'DataType', NULL, 'JSON', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028b8855c3e5aef015c3e5b95b701f0', 'POSTGRESQL.LSEG', NULL, 'DataType', NULL, 'LSEG', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028b8855c3e5aef015c3e5b95b701ef', 'POSTGRESQL.TIMESTAMPTZ', NULL, 'DataType', NULL, 'TIMESTAMPTZ', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028b8855c3e5aef015c3e5b95b401cf', 'POSTGRESQL.SERIAL8', NULL, 'DataType', NULL, 'SERIAL8', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028b8855c3e5aef015c3e5b95b601df', 'POSTGRESQL.LINE', NULL, 'DataType', NULL, 'LINE', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028b8855c3e5aef015c3e5b95b801f8', 'POSTGRESQL.TIMESTAMP', NULL, 'DataType', NULL, 'TIMESTAMP', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028b8855c3e5aef015c3e5b95b501d7', 'POSTGRESQL.MACADDR', NULL, 'DataType', NULL, 'MACADDR', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028b8855c3e5aef015c3e5b95b701f2', 'POSTGRESQL.SERIAL2', NULL, 'DataType', NULL, 'SERIAL2', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028b8855c3e5aef015c3e5b95b501d8', 'POSTGRESQL.VARCHAR', NULL, 'DataType', NULL, 'VARCHAR', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028b8855c3e5aef015c3e5b95b501db', 'POSTGRESQL.FLOAT8', NULL, 'DataType', NULL, 'FLOAT8', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028b8855c3e5aef015c3e5b95b501d0', 'POSTGRESQL.POINT', NULL, 'DataType', NULL, 'POINT', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028b8855c3e5aef015c3e5b95b601e5', 'POSTGRESQL.VARBIT', NULL, 'DataType', NULL, 'VARBIT', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028b8855c3e5aef015c3e5b95b501d9', 'POSTGRESQL.FLOAT4', NULL, 'DataType', NULL, 'FLOAT4', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('ba5db61bccd3499dad6728cac3e67c4c', 'POSTGRESQL.INT1', NULL, 'DataType', NULL, 'INT1', '********************************', NULL, NULL, '2021-09-26 10:32:11', NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028b8855c3e5aef015c3e5b95b401ce', 'POSTGRESQL.UUID', NULL, 'DataType', NULL, 'UUID', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028b8855c3e5aef015c3e5b95b601e3', 'POSTGRESQL.TYPE', NULL, 'DataType', NULL, 'TYPE', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028b8855c3e5aef015c3e5b95b601e2', 'POSTGRESQL.INTERVAL', NULL, 'DataType', NULL, 'INTERVAL', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028b8855c3e5aef015c3e5b95b601dd', 'POSTGRESQL.INT2', NULL, 'DataType', NULL, 'INT2', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028b8855c3e5aef015c3e5b95b601dc', 'POSTGRESQL.NUMERIC', NULL, 'DataType', NULL, 'NUMERIC', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('134370b978f9469b5407dfe052992f18', 'POSTGRESQL.NUMBER', NULL, 'DataType', NULL, 'NUMBER', '********************************', NULL, NULL, '2020-08-19 09:35:48', NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028b8855c3e5aef015c3e5b95b501d3', 'POSTGRESQL.JSONB', NULL, 'DataType', NULL, 'JSONB', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028b8855c3e5aef015c3e5b95b601e1', 'POSTGRESQL.INT4', NULL, 'DataType', NULL, 'INT4', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028b8855c3e5aef015c3e5b95b601e4', 'POSTGRESQL.TXID_SNAPSHOT', NULL, 'DataType', NULL, 'TXID_SNAPSHOT', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028b8855c3e5aef015c3e5b95b701e7', 'POSTGRESQL.TIME', NULL, 'DataType', NULL, 'TIME', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028b8855c3e5aef015c3e5b95b701e8', 'POSTGRESQL.BOX', NULL, 'DataType', NULL, 'BOX', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028b8855c3e5aef015c3e5b95b501d6', 'POSTGRESQL.INT8', NULL, 'DataType', NULL, 'INT8', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('c1deaccd94e849a382c8ca9c3d878fb4', 'POSTGRESQL.INT1', NULL, 'DataType', NULL, 'INT1', '********************************', NULL, NULL, '2021-08-06 16:17:16', NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028b8855c3e5aef015c3e5b95b401cd', 'POSTGRESQL.TIMETZ', NULL, 'DataType', NULL, 'TIMETZ', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028b8855c3e5aef015c3e5b95b701f1', 'POSTGRESQL.BIT', NULL, 'DataType', NULL, 'BIT', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028b8855c3e5aef015c3e5b95b42222', 'POSTGRESQL.BOOLEAN', NULL, 'DataType', NULL, 'BOOLEAN', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028b8855c3e5aef015c3e5b95b701ed', 'POSTGRESQL.CIRCLE', NULL, 'DataType', NULL, 'CIRCLE', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028b8855c3e5aef015c3e5b95b701f4', 'POSTGRESQL.DATE', NULL, 'DataType', NULL, 'DATE', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028b8855c3e5aef015c3e5b95b601de', 'POSTGRESQL.BYTEA', NULL, 'DataType', NULL, 'BYTEA', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028b8855c3e5aef015c3e5b95b501d4', 'POSTGRESQL.BOOL', NULL, 'DataType', NULL, 'BOOL', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028b8855c3e5aef015c3e5b95b801f7', 'POSTGRESQL.XML', NULL, 'DataType', NULL, 'XML', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028b8855c3e5aef015c3e5b95b701e9', 'POSTGRESQL.INET', NULL, 'DataType', NULL, 'INET', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028b8855c3e5aef015c3e5b95b701eb', 'POSTGRESQL.TSVECTOR', NULL, 'DataType', NULL, 'TSVECTOR', '********************************', NULL, NULL, NULL, NULL);



INSERT INTO "public"."t_md_software"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id", "type_system_id", "soft_version", "supplier") VALUES ('4028f9fc59a545110159a54563310068', 'GREENPLUM4.3.7.3', NULL, 'Software', NULL, 'GREENPLUM', NULL, NULL, NULL, NULL, NULL, '********************************', '4.3.7.3', NULL);
INSERT INTO "public"."t_md_software"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id", "type_system_id", "soft_version", "supplier") VALUES ('3671e90a64ba40919c32dba4bc92ddd8', 'hwmpp.hw9.4', NULL, 'Software', NULL, 'hwmpp', NULL, NULL, NULL, '2020-11-11 15:18:03', NULL, '********************************', 'hw9.4', NULL);
INSERT INTO "public"."t_md_software" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id", "type_system_id", "soft_version", "supplier") VALUES ('4028b8855c3e5aef015c3e5b962401f9', 'POSTGRESQL.9.4', NULL, 'Software', NULL, 'POSTGRESQL', NULL, NULL, NULL, NULL, NULL, '********************************', '9.4', NULL);
