package com.dragonsoft.cicada.datacenter.modules.modeling.service.impl;

import com.code.common.bean.BeanFactory;
import com.code.common.mist.plugin.config.service.impl.PluginConfigService;
import com.code.dataset.IStepRelationService;
import com.code.metaservice.base.typemapping.TypeSystemService;
import com.code.metaservice.bus.management.IServiceInfoService;
import com.code.metaservice.core.StructuralFeatureService;
import com.code.metaservice.ddl.ILogicDataColumnService;
import com.code.metaservice.ddl.ILogicDataObjService;
import com.code.metaservice.etl.trans.TransMetaService;
import com.code.metaservice.python.task.IPyTaskService;
import com.code.metaservice.res.common.ClassifierStatService;
import com.code.metaservice.sm.IServiceMetaService;
import com.code.metaservice.sm.IServiceParamsService;
import com.code.metaservice.udf.management.IUdfGraphService;
import com.code.metaservice.variable.ITransVariableService;
import com.code.mlsql.parse.DefaultSqlOperatorGraphParser;
import com.code.mlsql.utils.GraphParseHelper;
import com.code.plugin.IPluginRegistry;
import org.springframework.beans.factory.FactoryBean;

import java.util.Map;

/**
 * <AUTHOR> Jie Bin
 * @date 2020-10-30 10:43
 */
public class GraphParseHelperFactoryBean implements FactoryBean<GraphParseHelper> {
    private ClassifierStatService classifierStatService;
    private PluginConfigService pluginConfigService;
    private StructuralFeatureService structuralFeatureService;
    private TransMetaService transMetaService;
    private BeanFactory beanFactory;
    private IPluginRegistry pluginRegistry;
    private IPyTaskService pyTaskService;
    private IUdfGraphService udfGraphService;
    private Map<String, String> pluginOperatorMapping;
    private TypeSystemService typeSystemService;
    private IServiceInfoService serviceInfoService;
    protected ILogicDataColumnService logicDataColumnService;

    private ILogicDataObjService logicDataObjService;
    private IStepRelationService stepRelationService;
    private ITransVariableService transVariableService;
    private IServiceMetaService serviceMetaService;
    private IServiceParamsService serviceParamsService;





    private String code = "mlsqlOperator";
    private String version = "def";

    @Override
    public GraphParseHelper getObject() throws Exception {
        DefaultSqlOperatorGraphParser parser = new DefaultSqlOperatorGraphParser();
        GraphParseHelper helper = new GraphParseHelper(parser, pluginOperatorMapping, pluginRegistry.getClassLoader(code, version));
        richHelper(helper);
        return helper;
    }

    private void richHelper(GraphParseHelper helper) {
        helper.setClassifierStatService(classifierStatService);
        helper.setPluginConfigService(pluginConfigService);
        helper.setStructuralFeatureService(structuralFeatureService);
        helper.setTransMetaService(transMetaService);
        helper.setBeanFactory(beanFactory);
        helper.setUdfGraphService(udfGraphService);
        helper.setTypeSystemService(typeSystemService);
        helper.setServiceInfoService(serviceInfoService);
        helper.setLogicDataColumnService(logicDataColumnService);
        helper.setPyTaskService(pyTaskService);
        helper.setLogicDataObjService(logicDataObjService);
        helper.setStepRelationService(stepRelationService);
        helper.setTransVariableService(transVariableService);
        helper.setServiceMetaService(serviceMetaService);
        helper.setServiceParamsService(serviceParamsService);
    }

    @Override
    public Class<?> getObjectType() {
        return GraphParseHelper.class;
    }

    @Override
    public boolean isSingleton() {
        return true;
    }

    public void setClassifierStatService(ClassifierStatService classifierStatService) {
        this.classifierStatService = classifierStatService;
    }

    public void setPluginConfigService(PluginConfigService pluginConfigService) {
        this.pluginConfigService = pluginConfigService;
    }

    public void setStructuralFeatureService(StructuralFeatureService structuralFeatureService) {
        this.structuralFeatureService = structuralFeatureService;
    }

    public void setTransMetaService(TransMetaService transMetaService) {
        this.transMetaService = transMetaService;
    }

    public void setBeanFactory(BeanFactory beanFactory) {
        this.beanFactory = beanFactory;
    }

    public void setPluginOperatorMapping(Map<String, String> pluginOperatorMapping) {
        this.pluginOperatorMapping = pluginOperatorMapping;
    }

    public void setPluginRegistry(IPluginRegistry pluginRegistry) {
        this.pluginRegistry = pluginRegistry;
    }

    public void setPyTaskService(IPyTaskService pyTaskService) {
        this.pyTaskService = pyTaskService;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public void setUdfGraphService(IUdfGraphService udfGraphService) {
        this.udfGraphService = udfGraphService;
    }

    public void setTypeSystemService(TypeSystemService typeSystemService) {
        this.typeSystemService = typeSystemService;
    }

    public void setLogicDataObjService(ILogicDataObjService logicDataObjService) {
        this.logicDataObjService = logicDataObjService;
    }

    public void setStepRelationService(IStepRelationService stepRelationService) {
        this.stepRelationService = stepRelationService;
    }

    public void setServiceInfoService(IServiceInfoService serviceInfoService) {
        this.serviceInfoService = serviceInfoService;
    }

    public ILogicDataColumnService getLogicDataColumnService() {
        return logicDataColumnService;
    }

    public void setLogicDataColumnService(ILogicDataColumnService logicDataColumnService) {
        this.logicDataColumnService = logicDataColumnService;
    }

    public void setTransVariableService(ITransVariableService transVariableService) {
        this.transVariableService = transVariableService;
    }

    public void setServiceMetaService(IServiceMetaService serviceMetaService) {
        this.serviceMetaService = serviceMetaService;
    }

    public void setServiceParamsService(IServiceParamsService serviceParamsService) {
        this.serviceParamsService = serviceParamsService;
    }
}
