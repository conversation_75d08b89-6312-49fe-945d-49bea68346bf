package com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.service;

import com.code.common.mist.service.structure.model.ClassMeta;
import com.code.common.utils.R;
import com.dragoninfo.dfw.bean.Result;
import com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.vo.BatchTestVo;
import com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.vo.ParamConfigVo;

import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;

public interface IServicePublicService {

    /**
     * 发布新的服务
     * @param paramConfigVo
     * @return
     */
    R createService(ParamConfigVo paramConfigVo,String userId);


    /**
     * 发布数据查询新的服务
     * @param paramConfigVo
     * @return
     */
    Result createDataQueryService(ParamConfigVo paramConfigVo, String userId) throws Exception;


    /**
     * 发布信息核查新的服务
     * @param paramConfigVo
     * @return
     */
    Result createInformationVerificationService(ParamConfigVo paramConfigVo, String userId) throws Exception;


    /**
     * 发布服务没有保存meta
     * @param paramConfigVo
     * @return
     */
    Result publishServiceNoSaveMeta(ParamConfigVo paramConfigVo, String userId) throws Exception;

    /**
     * 检查服务API名称是否已存在
     * @param paramConfigVo
     */
    void checkChineseName(ParamConfigVo paramConfigVo,String userId);

    /**
     * 检查服务名是否已存在
     * @param classMeta
     * @param paramConfigVo
     */
    void checkServiceName(ClassMeta classMeta, ParamConfigVo paramConfigVo);

    /**
     * 服务测试
     * @param paramConfigVo
     * @return
     */
    R testService(ParamConfigVo paramConfigVo, HttpServletRequest servletRequest) throws UnsupportedEncodingException;

    /**
     * 服务测试
     * @param paramConfigVo
     * @return
     */
    R testBatchService(BatchTestVo paramConfigVo, HttpServletRequest servletRequest) throws UnsupportedEncodingException;


    /**
     * 是否发布过
     * @param sourceId
     * @return
     */
    String isIssue(String sourceId);

    /**
     * 服务卸载
     * @param sourceId 资源id
     */
    void offlineService(String sourceId);

    /**
     * 服务卸载通过serviceMetaId
     * @param serviceMetaId
     */
    void offlineServiceByServiceMetaId(String serviceMetaId);

    void offlineServiceByServiceMetaIdComitSession(String serviceMetaId);

    /**
     *服务停用
     * 是serviceMetaId
     */
    void disableService(String serviceMetaId);

    /**
     * 重新发布服务
     * @param serviceMetaId
     * @return
     */
    void redistributionService(String serviceMetaId);

    void delServiceMetaId(String serviceMetaId);

    void checkPublishName(String serviceMetaId,String name,boolean isUpdate,String serviceType,String userId);

    void checkPublishNameByClass(String serviceMetaId,String name,boolean isUpdate,String serviceType,String classifyId);

    void deleteElementAndClassify(String elementId);

    void saveElementAndClassify(String elementId,String classifyId);

    void deleteElementAndClassifyByMetaId(String serviceMetaId);

    void saveElementAndClassifyByMetaId(String serviceMetaId,String classifyId);

    void updateElementAndClassifyByMetaId(String serviceMetaId,String classifyId);

    void checkTheSameNameInClassify(String id,String classifyId);
}
