package com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.vo;

import lombok.Data;

import java.util.List;

@Data
public class ServicePublicationVo {

    private String serviceId;
    private String apiName;
    private String serviceType;
    private String sourceId; //训练版本
    private Integer version;
    private String memo;

    private String classifyId;

    private List<Params> requestParams;
    private List<Params> responseParams;

    @Data
    public static class Params{
        private String paramName;
        private String paramMemo;
        private String paramType;
        private Boolean isMust;
        private String desensitization;
        private String exampleValue;
        private List<Params> children;
    }
}
