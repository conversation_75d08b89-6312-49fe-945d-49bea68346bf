package com.dragonsoft.cicada.datacenter.modules.supermarkmodel.service;

import com.code.common.paging.PageInfo;
import com.dragoninfo.dfw.bean.Result;
import com.dragonsoft.cicada.datacenter.modules.supermarkmodel.vo.ModelLabelVo;
import com.dragonsoft.cicada.datacenter.modules.supermarkmodel.vo.MyFocusQueryVo;
import com.dragonsoft.cicada.datacenter.modules.supermarkmodel.vo.SupermarkModelVo;

import java.util.List;
import java.util.Map;

public interface IMyModelService {

    /**
     * 我的模型查询待发布列表
     */
    PageInfo queryUnPublishModelPage(Map<String, Object> queryModelMap);

    /**
     * 我的模型查询已发布列表
     */
    PageInfo queryPublishedModelPage(Map<String, Object> queryModelMap);

    /**
     * 发布模型初始化数据
     */
    Result publishModelInit();

    /**
     * 发布模型
     */
    Result insertPublishModel(SupermarkModelVo modelVo);

    /**
     * 新增标签
     */
    Result insertModelLabel(ModelLabelVo labelVo);

    /**
     * 查询标签库列表
     */
    List<Map> queryModelLabelList(ModelLabelVo labelVo, String userId);

    /**
     * 模型上下架
     */
    Result updateModelState(SupermarkModelVo modelVo);

    /**
     * 模型启停用
     */
    Result updateEnabledState(SupermarkModelVo modelVo);

    /**
     * 删除模型
     */
    Result deleteMarkModel(SupermarkModelVo modelVo);

    /**
     * 根据方案ID删除模型
     */
    void deleteMarkModelByTransId(String transId);



    List<Map> obtainid(String id);
    /**
     * 根据方案ID列表批量删除模型
     */
    void deleteMarkModelBatchByTransIdList(List<String> transIdList);

    /**
     * 根据方案目录id批量删除模型
     */
    void deleteMarkModelByBusiClassifyId(String busiClassifyId);

    /**
     * 模型修改初始化数据
     */
    Result initUpdateMarkModel(SupermarkModelVo modelVo);

    /**
     * 获取模型名称、创建人
     */
    Map<String, Object> getModelTransInfo(String transId, String type);

    /**
     * 模型修改
     */
    Result updateMarkModel(SupermarkModelVo modelVo);

    /**
     * 查询我的关注列表
     * @param queryVo
     * @return
     */
    PageInfo queryMyFocusPage(MyFocusQueryVo queryVo,String userId);


    void cancelMyFocus(List<String> modelsId,String userId);

}
