package com.dragonsoft.cicada.datacenter.modules.modeling.vo;

/**
 * <AUTHOR>
 *
 * @Date: 2021/07/22/下午1:37
 */
public class QuickAnalysisTransHop {
    /**
     * A->B    在A B之间插入C  变成 A-C-B
     *
     * A - C
     *     -C  插 D
     * B-
     */


    /**
     *  A 插件名字
     */
    private String beforeTransName;
    /**
     *  A 插件id
     */
    private String beforeTransId;
    /**
     * C 插件名字
      */
    private String currendTransName;
    /**
     * C 插件id
     */
    private String currendTransId;
    /**
     * B 插件名字
     */
    private String afterTransName;
    /**
     * B 插件id
     */
    private String afterTransId;
    /**
     * 当前方案的id
     */
    private String transId;

    /**
     * A 方案 左右点 方向  主要是为了碰撞插件
     * @return
     */

    private String beforeDirection;

    /**
     *  C 方案 左右点方向，主要是为了碰撞插件
     * @return
     */
    private String afterDirection;

    public String getBeforeTransName() {
        return beforeTransName;
    }

    public void setBeforeTransName(String beforeTransName) {
        this.beforeTransName = beforeTransName;
    }

    public String getBeforeTransId() {
        return beforeTransId;
    }

    public void setBeforeTransId(String beforeTransId) {
        this.beforeTransId = beforeTransId;
    }

    public String getCurrendTransName() {
        return currendTransName;
    }

    public void setCurrendTransName(String currendTransName) {
        this.currendTransName = currendTransName;
    }

    public String getCurrendTransId() {
        return currendTransId;
    }

    public void setCurrendTransId(String currendTransId) {
        this.currendTransId = currendTransId;
    }

    public String getAfterTransName() {
        return afterTransName;
    }

    public void setAfterTransName(String afterTransName) {
        this.afterTransName = afterTransName;
    }

    public String getAfterTransId() {
        return afterTransId;
    }

    public void setAfterTransId(String afterTransId) {
        this.afterTransId = afterTransId;
    }

    public String getTransId() {
        return transId;
    }

    public void setTransId(String transId) {
        this.transId = transId;
    }

    public String getBeforeDirection() {
        return beforeDirection;
    }

    public void setBeforeDirection(String beforeDirection) {
        this.beforeDirection = beforeDirection;
    }

    public String getAfterDirection() {
        return afterDirection;
    }

    public void setAfterDirection(String afterDirection) {
        this.afterDirection = afterDirection;
    }
}
