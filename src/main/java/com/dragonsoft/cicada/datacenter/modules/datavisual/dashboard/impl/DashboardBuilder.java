package com.dragonsoft.cicada.datacenter.modules.datavisual.dashboard.impl;


import com.code.common.paging.PageInfo;
import com.code.common.utils.assertion.Assert;
import com.code.metadata.datavisual.*;
import com.code.metadata.res.ddl.LogicDataColumn;
import com.code.metaservice.datavisual.IDashBoardService;
import com.code.metaservice.datavisual.IDashboardGroupService;
import com.code.metaservice.ddl.ILogicDataColumnService;
import com.dragoninfo.dfw.bean.Result;
import com.dragoninfo.dfw.entity.TSysAuthUser;
import com.dragoninfo.dfw.service.SysAuthUserService;
import com.dragonsoft.cicada.datacenter.modules.datavisual.dashboard.IDashboardBuilder;
import com.dragonsoft.cicada.datacenter.modules.datavisual.dataset.IDataSetBuilder;
import com.dragonsoft.cicada.datacenter.modules.datavisual.widget.IChartWidgetBuilder;
import com.dragonsoft.cicada.datacenter.modules.datavisual.widgetmeta.IWidgetMetaBuild;
import com.dragonsoft.cicada.datacenter.modules.supermarkmodel.service.IMyModelService;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.vo.FieldVo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Service
@Slf4j
@EnableCaching
@CacheConfig(cacheNames = {"chartWidgetBuilder"})
public class DashboardBuilder implements IDashboardBuilder {

    @Autowired
    IDashboardGroupService dashboardGroupService;
    @Autowired
    IDashBoardService dashBoardService;
    @Autowired
    IWidgetMetaBuild widgetMetaBuild;
    @Autowired
    IDataSetBuilder dataSetBuilder;
    @Autowired
    private SysAuthUserService sysAuthUserService;

    @Autowired
    private DashboardScheduled dashboardScheduled;

    @Autowired
    private IChartWidgetBuilder chartWidgetBuilder;

    @Autowired
    ILogicDataColumnService logicDataColumnService;

    @Autowired
    private IMyModelService myModelServiceImpl;



    @Override
    public List<Dashboard> builderList(String groupId, String keyWord) {
        List<Dashboard> list = dashBoardService.getListByGroupId(groupId, keyWord);
        return list;
    }

    @Override
    public Dashboard builder(String id) {
        Dashboard dashboard = dashBoardService.getDashboard(id);
        return dashboard;
    }


    @Override
    public void configScheduled(Dashboard dashboard, String taskCode, String taskJson, boolean taskType) {
        if (dashboard == null) return;
        List<DashboardScheduled.TaskConstant> taskConstants = dashboardScheduled.getTaskConstants();
        if (dashboard.getIsCache() && StringUtils.isNotEmpty(dashboard.getCron())) {
            DashboardScheduled.TaskConstant taskConstantNew = new DashboardScheduled.TaskConstant();
            taskConstantNew.setCron(dashboard.getCron());
            taskConstantNew.setTaskId(dashboard.getId() + taskCode + taskJson + taskType);
            taskConstantNew.setTaskCode(taskCode);
            taskConstantNew.setTaskJson(taskJson);
            taskConstantNew.setType(taskType);
            if (CollectionUtils.isEmpty(taskConstants) || !taskConstants.contains(taskConstantNew)) {
                taskConstants.add(taskConstantNew);
            } else {
                for (DashboardScheduled.TaskConstant taskConstant : taskConstants) {
                    if (taskConstant.getTaskId().equals(taskConstantNew.getTaskId())) {
                        taskConstant = taskConstantNew;
                    }
                }
            }
        } else if (!dashboard.getIsCache()) {
            for (int i = 0; i < taskConstants.size(); i++) {
                if (taskConstants.get(i).getTaskId().equals(dashboard.getId())) {
                    taskConstants.remove(i);
                    i--;
                }
            }
        }
    }

    @Override
    public String saveOrUpdate(Dashboard dashboard) {
        return dashBoardService.saveOrUpdate(dashboard);
    }

    @Override
    public PageInfo builderListForPage(String groupId, String keyWord, int index, int pageSize, String userId) {
        PageInfo pageInfo = new PageInfo(pageSize, index);
        pageInfo = dashBoardService.getListForPage(groupId, keyWord, pageInfo, userId);
        List<Dashboard> dashboards = pageInfo.getDataList();
        Map<String, String> params = Maps.newHashMap();
        params.put("id", userId);
        TSysAuthUser tSysAuthUser = (TSysAuthUser) sysAuthUserService.query(params);
        for (Dashboard dashboard : dashboards) {
            dashboard.setOperateUserId(tSysAuthUser.getObjName());
        }
        pageInfo.setDataList(dashboards);
        return pageInfo;
    }

    @Override
    public List<Dashboard> getListAll(String groupId, String userId) {
        return dashBoardService.getListByGroupIdAndUserId(groupId,userId);
    }

    @Override
    @CacheEvict(value = "dashBoards")
    public void delete(String id) {
        dashBoardService.deleteDashBoard(id);
        //删除模型市场数据
        myModelServiceImpl.deleteMarkModelByTransId(id);
    }

    private String getNewName(String name, List<Map<String, String>> nameList) {
        for (Map<String, String> nameStr : nameList) {
            if (name.equals(nameStr.get("name"))) {
                name += "(副本)";
                name = getNewName(name, nameList);
            }
        }
        return name;
    }

    @Override
    public void copy(String id,String dirId,String dashboardName,String userId) {
        Dashboard dbs = this.builder(id);
        Dashboard dashboard = new Dashboard();
        BeanUtils.copyProperties(dbs, dashboard);
        dashboard.setId(null);
        //添加判断目录中是否已存在重复名称的
        if(com.code.common.utils.StringUtils.isNotBlank(dashboardName)){
            if(dashBoardService.checkName(null,dirId,dashboardName,userId)){
                Assert.fail("该名称已存在");
            }
            dashboard.setName(dashboardName);
        }else{
            List<Map<String, String>> nameList = dashBoardService.getNameList(id);
            String newName = dashboard.getName();
            dashboard.setName(getNewName(newName, nameList));
        }
        dashboard.setOperateUserId(userId);
        Set<Widget> widgets = new HashSet<>();
        for (Widget w : dashboard.getWidgets()) {
            Widget widget = new Widget();
            BeanUtils.copyProperties(w, widget);
            widget.setId(null);
            WidgetDataset widgetDataset = widget.getWidgetDataset();
            if (widget.getWidgetDataset() == null){
                widgets.add(widget);
                continue;
            }
            BeanUtils.copyProperties(w.getWidgetDataset(), widgetDataset);
            widgetDataset.setId(null);
            widget.setWidgetDataset(widgetDataset);
            Set<WidgetDatasetDims> wdds = new HashSet<>();
            for (WidgetDatasetDims d : w.getWidgetDataset().getWidgetDatasetDims()) {
                WidgetDatasetDims wdd = new WidgetDatasetDims();
                BeanUtils.copyProperties(d, wdd);
                wdd.setId(null);
                wdds.add(wdd);
            }
            widgetDataset.setWidgetDatasetDims(wdds);
            Set<WidgetDatasetMeasures> wdms = new HashSet<>();
            for (WidgetDatasetMeasures d : w.getWidgetDataset().getWidgetDatasetMeasures()) {
                WidgetDatasetMeasures m = new WidgetDatasetMeasures();
                BeanUtils.copyProperties(d, m);
                m.setId(null);
                wdms.add(m);
            }
            widgetDataset.setWidgetDatasetMeasures(wdms);
            widgets.add(widget);
        }
        dashboard.setWidgets(widgets);
//        String json = JSONObject.toJSONString(dashboard);
//        Dashboard save = JSONObject.parseObject(json, Dashboard.class);

        dashboard.setGroupId(dirId);
        dashBoardService.saveOrUpdate(dashboard);
    }

    @Override
    public void copyDashboard(Dashboard dbs, String groupId) {
        Dashboard dashboard = new Dashboard();
        BeanUtils.copyProperties(dbs, dashboard);
        dashboard.setGroupId(groupId);
        Set<Widget> widgets = new HashSet<>();
        for (Widget w : dashboard.getWidgets()) {
            Widget widget = new Widget();
            BeanUtils.copyProperties(w, widget);
            widget.setId(null);
            WidgetDataset widgetDataset = widget.getWidgetDataset();
            BeanUtils.copyProperties(w.getWidgetDataset(), widgetDataset);
            widgetDataset.setId(null);
            widget.setWidgetDataset(widgetDataset);
            Set<WidgetDatasetDims> wdds = new HashSet<>();
            for (WidgetDatasetDims d : w.getWidgetDataset().getWidgetDatasetDims()) {
                WidgetDatasetDims wdd = new WidgetDatasetDims();
                BeanUtils.copyProperties(d, wdd);
                wdd.setId(null);
                wdds.add(wdd);
            }
            widgetDataset.setWidgetDatasetDims(wdds);
            Set<WidgetDatasetMeasures> wdms = new HashSet<>();
            for (WidgetDatasetMeasures d : w.getWidgetDataset().getWidgetDatasetMeasures()) {
                WidgetDatasetMeasures m = new WidgetDatasetMeasures();
                BeanUtils.copyProperties(d, m);
                m.setId(null);
                wdms.add(m);
            }
            widgetDataset.setWidgetDatasetMeasures(wdms);
            widgets.add(widget);
        }
        dashboard.setWidgets(widgets);
        dashBoardService.mergeDashboard(dashboard);
    }

    @Override
    public List<Map<String, Object>> getLinkageDashboard(String id) {
        Dashboard dashboard = dashBoardService.getDashboard(id);
        Set<Widget> widgets = dashboard.getWidgets();

        return distinguishWidgets(widgets);
    }

    private List<Map<String, Object>> distinguishWidgets(Set<Widget> widgets) {
        List<Map<String, Object>> widgetDataSet = Lists.newArrayList();

        for (Widget widget : widgets) {
            String widgetDataSetId = widget.getWidgetDataset().getDatasetId();
            Map<String, Object> widgetMap = Maps.newHashMap();
            widgetMap.put("code",widget.getWidgetMeta().getCode());
            widgetMap.put("dataSetId", widgetDataSetId);
            widgetMap.put("widgetName", widget.getTitle());
            widgetMap.put("widgetId", widget.getId());
            widgetMap.put("tableCode", widget.getWidgetDataset().getTableCode());
            //获取数据集的字段id
            List<LogicDataColumn> columns = logicDataColumnService.findLogicDataColumnsByLogicDataObjId(widgetDataSetId);
            List<FieldVo> resColumns = Lists.newArrayList();
            for (LogicDataColumn column : columns) {
                FieldVo fieldVo = new FieldVo();
                fieldVo.setValue(column.getId());
                fieldVo.setCode(column.getCode());
                fieldVo.setName(column.getName());
                fieldVo.setType(column.getIndexType());
                resColumns.add(fieldVo);
            }
            widgetMap.put("columns", resColumns);

            widgetDataSet.add(widgetMap);
        }

        return widgetDataSet;

    }

    @Override
    public Result getDashboardAllByUserId(Map<String, Object> queryMap) {
        String userId = (String) queryMap.get("userId");
        Assert.notNull(userId, "用户未登录");

        List<Map> dashboards = dashBoardService.getDashboardAllByUserId(queryMap);

        return Result.success(dashboards);

        /*String userId = (String) paramsMap.get("userId");
        Integer pageIndex = (Integer) paramsMap.get("pageIndex");
        Integer pageSize = (Integer) paramsMap.get("pageSize");
        String keyWord = (String) paramsMap.get("keyWord");

        Assert.notNull(userId, "用户未登录");
        Assert.notNull(pageIndex, "分页不能为空");
        Assert.notNull(pageSize, "分页不能为空");

        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageIndex(pageIndex);
        pageInfo.setPageSize(pageSize);
        PageInfo pageResult = dashBoardService.getListForPage("1", keyWord, pageInfo, userId);

        List<Dashboard> dashboards = pageResult.getDataList();
        if (CollectionUtils.isNotEmpty(dashboards)) {
            List<Map<String, Object>> resultList = dashboards.stream().map(dashboard -> {
                Map<String, Object> map = new HashMap<>();
                map.put("id", dashboard.getId());
                map.put("name", dashboard.getName());
                return map;
            }).collect(Collectors.toList());
            pageResult.setDataList(resultList);
        }

        return Result.success(pageResult);*/
    }
}
