package com.dragonsoft.cicada.datacenter.modules.modeling.aimodeling.client;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dragonsoft.cicada.datacenter.modules.modeling.aimodeling.vo.EntInputInfo;
import com.dragonsoft.cicada.datacenter.modules.modeling.aimodeling.vo.EvaluateRstItem;

import java.util.List;

/**
 * <AUTHOR>
 * @description：jupyter脚本相关操作客户端接口
 * @date ：2021/10/9 10:42
 */
public interface IJypyterCodeClient {

    /**
     * 初始文件创建接口
     * @param modelName 模型名称
     * @param modelDescription 模型描述
     * @return 脚本id
     */
    String initBuild(String modelName, String modelDescription);


    /**
     * 脚本启动接口
     * @param scriptId 脚本id
     * @return 脚本编辑页面url
     */
    String startScript(String scriptId);

    /**
     * 脚本保存接口
     * @param scriptId 脚本id
     */
    void saveScript(String scriptId);

    /**
     * 脚本执行接口
     * @param scriptId 脚本id
     * @return 日志id
     */
    String runScript(String scriptId);


    /**
     * 指标评估结果查询接口
     * @param logId 日志id
     * @return
     */
    List<EvaluateRstItem> queryEvaluateRst(String logId);

    /**
     * 实体数据接入信息查询接口
     * @param logId 日志id
     * @return
     */
    EntInputInfo queryEntInputInfo(String logId);

    /**
     * 发布日志查询接口
     * @param scriptId
     * @return
     */
    JSONArray queryReleaseRst(String scriptId);

    JSONObject testAiService(String op_input,String url);

    /**
     * 服务发布
     * @param logId
     * @param releasePath
     * @param serviceCode
     * @return
     */
    String serviceRelease(String logId, String releasePath, String serviceCode);

    /**
     * 服务取消
     * @param releasePath
     * @param serviceCode
     * @return
     */
    String serviceCancel(String releasePath,String serviceCode);
}
