package com.dragonsoft.cicada.datacenter.modules.datavisual.businessrelation;

/**
 * <AUTHOR>
 * @Date 2021/5/11 17:34
 */
public enum BusinessRelationEnum {
    PEER_RELATION("PEER_RELATION","同行关系",new PeerRelation());

    BusinessRelationEnum(String code,String businessRelationType, AbsBusinessRelation businessRelation) {
        this.code = code;
        this.businessRelationType = businessRelationType;
        this.businessRelation = businessRelation;
    }

    private String code;
    private String businessRelationType;
    private AbsBusinessRelation businessRelation;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getBusinessRelationType() {
        return businessRelationType;
    }

    public void setBusinessRelationType(String businessRelationType) {
        this.businessRelationType = businessRelationType;
    }

    public AbsBusinessRelation getBusinessRelation() {
        return businessRelation;
    }

    public void setBusinessRelation(AbsBusinessRelation businessRelation) {
        this.businessRelation = businessRelation;
    }
}
