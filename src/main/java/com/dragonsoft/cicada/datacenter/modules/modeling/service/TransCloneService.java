package com.dragonsoft.cicada.datacenter.modules.modeling.service;

import com.code.common.mist.plugin.config.service.impl.PluginConfigService;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.code.common.mist.plugin.config.service.impl.PluginConfigService;
import com.code.common.utils.StringUtils;
import com.code.common.utils.assertion.Assert;
import com.code.metadata.base.expression.ElementNode;
import com.code.metadata.business.directory.BaseBusiClassify;
import com.code.metadata.etl.trans.*;
import com.code.metadata.model.core.BaseModelElement;
import com.code.metadata.udf.management.UdfEdge;
import com.code.metadata.udf.management.UdfGraph;
import com.code.metadata.udf.management.UdfNode;
import com.code.metadata.variable.TransVariable;
import com.code.metadata.variable.TransVariableRelation;
import com.code.metaservice.business.directory.BusiClassifyService;
import com.code.metaservice.etl.trans.TransMetaService;
import com.code.metaservice.udf.management.IUdfGraphService;
import com.code.metaservice.variable.ITransVariableRelationService;
import com.code.metaservice.variable.ITransVariableService;
import com.code.mlsql.utils.GraphParseHelper;
import com.dragonsoft.cicada.datacenter.modules.modeling.vo.CopyPluginVo;
import com.fw.dao.hbmimpl.BaseDaoImpl;
import com.fw.tenon.bean.TenonBeanUtil;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import com.code.mlsql.utils.GraphParseHelper;
import com.dragoninfo.dfw.bean.Result;
import javax.annotation.Resource;


/**
 * <AUTHOR> Wu.D.J
 * @Create : 2020.11.20
 */
@Slf4j
@Service
public class TransCloneService {

    private static final List<String> JOIN = Arrays.asList("collisionPlugin", "innerJoinPlugin", "leftOrRightJoinPlugin",
            "unionJoinPlugin", "subtractByKeyPlugin", "fullJoinPlugin", "cicadaCollisionPlugin", "cicadaSubtractByKeyPlugin",
            "cicadaUnionJoinPlugin", "cicadaPeerContentMeta");

    private static final List<String> SERVICE = Arrays.asList("cicadaAnalysisModelService", "cicadaCompareServiceModelService",
            "cicadaDataCollisionModelService", "cicadaInfoCheckModelService");
    private static final List<String> CHECK_SERVICE = Collections.singletonList("cicadaMetaServiceCheckOutPut");

    @Autowired
    private ITransVariableRelationService transVariableRelationService;

    @Autowired
    private ITransVariableService transVariableService;

    @Autowired
    private TransMetaService transMetaService;

    @Autowired
    private BusiClassifyService busiClassifyService;

    @Autowired
    private IUdfGraphService udfGraphService;

    @Autowired
    private BaseDaoImpl baseDao;

    @Autowired
    private MLSQLService mlsqlService;



    @Resource(name = "graphParseHelper")
    private GraphParseHelper helper;


    @Autowired
    private PluginConfigService pluginConfigService;



    public Map<String, String> copy(String transId, String dirId, String transName, String memo, String userId, Integer version, String productionFirm) {
        Map<String, String> map = saveCopyTrans(transId, dirId, transName, memo);
        String newTransId = map.get("id");
        TransMeta transMeta = transMetaService.getTransMetaById(newTransId);
        transMeta.setOperateUserId(userId);
        transMeta.setVersion(version);
        transMeta.setProductionFirm(productionFirm);
        this.baseDao.save(transMeta);
        return map;
    }

    public Map<String, String> saveCopyTrans(String transId, String dirId, String transName, String memo) {
        Map<String, String> map = new HashMap<>();
        TransMeta oldTransMeta = transMetaService.getTransMetaById(transId);
        BaseBusiClassify busiClassify = this.busiClassifyService.findBusiClassifyBy(dirId);
        Set<String> el = new HashSet<>();
        for (BaseModelElement baseModelElement : busiClassify.getElements()) {
            el.add(baseModelElement.getName());
        }
        if (StringUtils.isNotBlank(transName)) {
            String checkSQL = "SELECT\n" +
                    "\td.id\n" +
                    "FROM\n" +
                    "\tt_md_busi_dir A,\n" +
                    "\tt_md_busi_classify b,\n" +
                    "\tt_md_classify_element C,\n" +
                    "\tt_etl_trans d\n" +
                    "WHERE\n" +
                    " b.busi_dir_id = A.ID\n" +
                    "AND C.busi_classify_id = b.ID\n" +
                    "AND C.element_id = d.id\n" +
                    "AND b.id='" + dirId + "'\n" +
                    "and d.name='" + transName + "'";
            List transList = this.baseDao.sqlQueryForList(checkSQL);
            Assert.isZero(transList.size(), "该名称已存在");
        }
        if (StringUtils.isBlank(transName)) {
            transName = getTranName(el, oldTransMeta.getName() + "_副本", 0);
        }
        String operateTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        // key sourceId, value target
        Map<String, TransMeta> transMapping = new HashMap<>();
        // 克隆数据
        TransMeta newTransMeta = _clone(oldTransMeta, transMapping);
        newTransMeta.setOperateTime(operateTime);
        newTransMeta.setName(transName);
        newTransMeta.setCode(transName);
        if (StringUtils.isBlank(memo)) {
            memo = oldTransMeta.getMemo();
        }
        newTransMeta.setMemo(memo);
        this.baseDao.save(newTransMeta);
        Map<String, String> idMapping = getTransMetaIdMap(oldTransMeta, newTransMeta);
        Map<String, String> tableNameMap = getTableNameMap(getTableNameMap(oldTransMeta.getHops()), transMapping);
        //插件处理
        processPlugin(newTransMeta, idMapping, tableNameMap);
        map.put("id", newTransMeta.getId());
        map.put("name", transName);
        Set<BaseModelElement> baseModelElements = busiClassify.getElements();
        baseModelElements.add(new BaseModelElement(newTransMeta.getId()));
        busiClassify.setElements(baseModelElements);
        saveTransVariableRelation(newTransMeta.getId(), transId);
        this.baseDao.save(busiClassify);

        return map;
    }

    private Map<String, String> getTableNameMap(Set<TransHopMeta> hopMetaSet) {
        Map<String, String> map = Maps.newHashMap();
        for (TransHopMeta hopMeta : hopMetaSet) {
            TransMeta fromTrans = hopMeta.getFromTrans();
            map.putIfAbsent(fromTrans.getId(), getTableName(fromTrans));
            TransMeta toTrans = hopMeta.getToTrans();
            map.putIfAbsent(toTrans.getId(), getTableName(toTrans));
        }
        return map;
    }

    private Map<String, String> getTableNameMap(Map<String, String> sourceTableNameMap, Map<String, TransMeta> transMetaMap) {
        Map<String, String> map = Maps.newHashMap();
        for (Map.Entry<String, String> entry : sourceTableNameMap.entrySet()) {
            TransMeta meta = transMetaMap.get(entry.getKey());
            String tableName = getTableName(meta);
            map.put(entry.getValue(), tableName);
        }
        return map;
    }

    private String getTableName(TransMeta transMeta) {
        TransPluginMeta transPluginMeta = transMeta.getUsedPlugin();
        TransAttributeMeta tableNameMeta = transPluginMeta.getFeature("tableName");
        String suffix = DigestUtils.md5DigestAsHex(transMeta.getId().getBytes()).substring(8, 24).toLowerCase();
        if (tableNameMeta != null) {
            return String.format("%s_%s", transMeta.getAttributeValue(tableNameMeta), suffix).toLowerCase();
        } else {
            return String.format("t_%s", suffix);
        }
    }

    private void processPlugin(TransMeta target, Map<String, String> idMapping, Map<String, String> tableNameMap) {
        for (TransMeta meta : target.getChildren()) {
            if (meta.getTransPartition() != null) {
                this.baseDao.saveOrUpdate(meta.getTransPartition());
            }

            TransPluginMeta usedPlugin = meta.getUsedPlugin();
            if (JOIN.contains(usedPlugin.getCode())) {
                // 处理碰撞插件
                buildJoin(idMapping, meta, usedPlugin);
            }
            if (CHECK_SERVICE.contains(usedPlugin.getCode())) {
                // 处理信息核查插件
                buildCheckService(idMapping, meta, usedPlugin);
            }
            if (SERVICE.contains(usedPlugin.getCode())) {
                buildService(idMapping, meta);
            }
            if ("serviceOrganization".equals(usedPlugin.getCode())) {
                // 处理算子编排
                buildServiceOrganization(meta);
            }
            if ("samplingAndShuntingPlugin".equals(usedPlugin.getCode())) {
                // 处理采样分流
                buildSamplingAndShunting(meta, usedPlugin);
            }
            if ("cicadaScriptMeta".equals(usedPlugin.getCode())) {
                // sql脚本
                buildCicadaScriptMeta( meta, usedPlugin, tableNameMap);
            }

        }
    }

    private void buildCicadaScriptMeta( TransMeta meta, TransPluginMeta usedPlugin, Map<String, String> tableNameMap) {
        TransAttributeMeta mlSqlScript = usedPlugin.getFeature("mlSqlScript");
        String sql = meta.getAttributeValue(mlSqlScript);
        if (MapUtil.isNotEmpty(tableNameMap)&&StrUtil.isNotBlank(sql)) {
            for (Map.Entry<String, String> entry : tableNameMap.entrySet()) {
                sql= StrUtil.replace(sql,entry.getKey(),entry.getValue(),true);
            }
            meta.addTransAttribute(mlSqlScript, sql);
            this.baseDao.saveOrUpdate(meta);
        }
    }


    private void buildService(Map<String, String> idMapping, TransMeta meta) {
        Set<TransExpMeta> transExps = meta.getTransExps();
        for (TransExpMeta exp : transExps) {
            ElementNode targetStep = (ElementNode) exp.getExpression().getArgument("inputStepId");
            String expValue = exp.getExpValue(targetStep);
            exp.addExpValue(targetStep, idMapping.get(expValue));
        }
        this.baseDao.saveOrUpdate(meta);
    }

    /**
     * 处理信息核查插件
     *
     * @param idMapping
     * @param meta
     * @param usedPlugin
     */
    private void buildCheckService(Map<String, String> idMapping, TransMeta meta, TransPluginMeta usedPlugin) {
        TransAttributeMeta leftAttribute = usedPlugin.getFeature("serviceInputStepId");
        String leftStepId = meta.getAttributeValue(leftAttribute);
        String newLeftId = idMapping.get(leftStepId);
        meta.addTransAttribute(leftAttribute, newLeftId);

        //右侧数据源,需要解析json的属性，然后重新保存
        Iterator<TransExpMeta> iterator = meta.getTransExps().iterator();
        while (iterator.hasNext()) {
            TransExpMeta next = iterator.next();
            Map<ElementNode, String> expValues = next.getExpValues();
            for (Map.Entry<ElementNode, String> entry : expValues.entrySet()) {
                if (idMapping.get(entry.getValue()) != null) {
                    entry.setValue(idMapping.get(entry.getValue()));
                }
                String value = entry.getValue();
                if (value.contains("dataSourceStepName") && value.contains("dataSourcesStepId")) {
                    for (Map.Entry<String, String> idEntry : idMapping.entrySet()) {
                        value = value.replaceAll(idEntry.getKey(), idEntry.getValue());
                    }
                    entry.setValue(value);
                }
            }
        }
        this.baseDao.saveOrUpdate(meta);
    }

    private Map<String, String> getTransMetaIdMap(TransMeta oldTransMeta, TransMeta newTransMeta) {
        Map<String, String> idMapping = new HashMap<>();
        for (TransMeta old : oldTransMeta.getChildren()) {
            String oldId = old.getId();
            String newId = newTransMeta.getChildren().stream()
                    .filter(t -> t.getCode().equals(old.getCode()))
                    .map(TransMeta::getId)
                    .collect(Collectors.toList())
                    .get(0);

            idMapping.put(oldId, newId);
        }
        return idMapping;
    }

    private void buildJoin(Map<String, String> idMapping, TransMeta meta, TransPluginMeta usedPlugin) {
        TransAttributeMeta leftAttribute = usedPlugin.getFeature("leftStepId");
        String leftStepId = meta.getAttributeValue(leftAttribute);
        String newLeftId = idMapping.get(leftStepId);
        meta.addTransAttribute(leftAttribute, newLeftId);

        TransAttributeMeta rightAttribute = usedPlugin.getFeature("rightStepId");
        String rightStepId = meta.getAttributeValue(rightAttribute);
        String newRightId = idMapping.get(rightStepId);
        meta.addTransAttribute(rightAttribute, newRightId);

        // aggregatorFieldExps
        Set<TransExpMeta> transExps = meta.getTransExps();
        for (TransExpMeta exp : transExps) {
            ElementNode nodeObjId = (ElementNode) exp.getExpression().getArgument("objId");
            if (nodeObjId == null) continue;
            String expValue = exp.getExpValue(nodeObjId);
            if (expValue == null) continue;
            exp.addExpValue(nodeObjId, idMapping.get(expValue));
        }
        this.baseDao.saveOrUpdate(meta);
    }

    private void saveTransVariableRelation(String transId, String oldTransId) {
//        List<TransVariableRelation> transVariableRelations = transVariableRelationService.queryTransVariableRelationByTransId(oldTransId);
        List<Map<String, String>> transVariableRelations = queryTransVariableRelationByTransId(oldTransId);
        if (!CollectionUtils.isEmpty(transVariableRelations)) {
            for (Map<String, String> transVariableRelation : transVariableRelations) {
                //如果是全局参数直接引用  如果是局部，复制出一个局部参数
                String variableId = transVariableRelation.get("variable_id");
                TransVariableRelation transVariableRelationNew = transVariableRelationService.getTransVariableRelation(transId, variableId);
                transVariableRelationNew.setCode("0");
                transVariableRelationNew.setType("0");
                transVariableRelationNew.setTransId(transId);
                TransVariable transVariable = transVariableService.get(TransVariable.class, variableId);
                if (transVariable.isGlobal()) {
                    transVariableRelationNew.setVariableId(variableId);
                } else {
                    TransVariable newTransVariable = TenonBeanUtil.toBean(transVariable, TransVariable.class);
                    newTransVariable.setId(null);
                    transVariableService.saveOrUpdate(newTransVariable);
                    transVariableRelationNew.setVariableId(newTransVariable.getId());
                }
                transVariableRelationService.saveOrUpdate(transVariableRelationNew);
            }
        }
    }

    private List<Map<String, String>> queryTransVariableRelationByTransId(String transId) {
        String sql = "select variable_id from T_MD_TRANS_VARIABLE_RELATION where trans_id =:transId";
        Map<String, Object> param = new HashMap<>();
        param.put("transId", transId);
        return baseDao.sqlQueryForList(sql, param);
    }


    public TransMeta _clone(TransMeta source, Map<String, TransMeta> transMapping) {

        TransMeta meta = new TransMeta();
        String[] ignoreProperties = {"id", "parent", "parentId", "transExps", "outDataSetMeta", "inDataSetMeta",
                "transPartition", "children", "hops", "startTrans", "parents", "attributeValues", "labelSet"};
        BeanUtils.copyProperties(source, meta, ignoreProperties);
        transMapping.put(source.getId(), meta);
        // 复制插件
        clonePluginMeta(source, meta);
        // 表达式
        cloneTransExpMeta(source, meta);
        //分区处理
        cloneTransPartitionMeta(source, meta);
        for (TransMeta child : source.getChildren()) {
            TransMeta newChild = _clone(child, transMapping);
            meta.addChild(newChild);
            newChild.setParents(Collections.singleton(meta));
        }
        //数据集
        cloneDataSetMeta(source, meta);
        //连线
        cloneTransHopMeta(source, transMapping, meta);
        return meta;
    }

    /**
     * 克隆插件meta
     *
     * @param source
     * @param meta
     */
    private void clonePluginMeta(TransMeta source, TransMeta meta) {
        TransPluginMeta usedPlugin = source.getUsedPlugin();
        if (usedPlugin != null) {
            Map<String, TransAttributeMeta> features = usedPlugin.getFeatures();
            for (Map.Entry<String, TransAttributeMeta> entry : features.entrySet()) {
                meta.addTransAttribute(entry.getKey(), source.getAttributeValue(entry.getValue()));
            }
        }
    }

    /**
     * 克隆表达式meta
     *
     * @param source
     * @param meta
     */
    private void cloneTransExpMeta(TransMeta source, TransMeta meta) {
        if (source.getTransExps() != null) {
            Set<TransExpMeta> newTransExps = new HashSet<>();
            String[] ignorePropertiesForExp = {"transExpId", "transMeta", "expValues"};
            for (TransExpMeta transExp : source.getTransExps()) {
                TransExpMeta newTransExp = new TransExpMeta();
                newTransExp.setTransMeta(meta);
                Map<ElementNode, String> expValues = new HashMap<>();
                for (ElementNode elementNode : transExp.getExpValues().keySet()) {
                    expValues.put(elementNode, transExp.getExpValues().get(elementNode));
                }
                BeanUtils.copyProperties(transExp, newTransExp, ignorePropertiesForExp);
                newTransExp.setExpValues(expValues);
                newTransExps.add(newTransExp);
            }
            meta.setTransExps(newTransExps);
        }
    }

    /**
     * 克隆分区meta
     *
     * @param source
     * @param meta
     */
    private void cloneTransPartitionMeta(TransMeta source, TransMeta meta) {
        String[] ignorePropertiesForPartition = {"id", "transMeta", "ranges"};
        String[] ignorePropertiesForPartitionRange = {"id"};
        TransPartitionMeta transpartitionmeta = new TransPartitionMeta();
        if (source.getTransPartition() != null) {
            BeanUtils.copyProperties(source.getTransPartition(), transpartitionmeta, ignorePropertiesForPartition);
            source.getTransPartition().getPartitionParam();
            for (TransPartitionRangeMeta transPartitionRangeMeta : source.getTransPartition().getRanges()) {
                TransPartitionRangeMeta range = new TransPartitionRangeMeta();
                BeanUtils.copyProperties(transPartitionRangeMeta, range, ignorePropertiesForPartitionRange);
                transpartitionmeta.addRange(range);
            }
            transpartitionmeta.setTransMeta(meta);
            meta.setTransPartition(transpartitionmeta);
        }
    }

    /**
     * 克隆数据集meta
     *
     * @param source
     * @param meta
     */
    private void cloneDataSetMeta(TransMeta source, TransMeta meta) {
        String[] ignorePropertiesForDataSetMeta = {"dataSetId", "dataColumnMetas"};
        String[] ignorePropertiesForDataColumnMeta = {"dataColumnId"};
        // 输出数据集
        cloneOutDataSetMeta(source, meta, ignorePropertiesForDataSetMeta, ignorePropertiesForDataColumnMeta);
        //输入数据集
        cloneInDataSetMeta(source, meta, ignorePropertiesForDataSetMeta, ignorePropertiesForDataColumnMeta);
    }

    /**
     * 克隆 输出数据集meta
     *
     * @param source
     * @param meta
     * @param ignorePropertiesForDataSetMeta
     * @param ignorePropertiesForDataColumnMeta
     */
    private void cloneOutDataSetMeta(TransMeta source, TransMeta meta, String[] ignorePropertiesForDataSetMeta, String[] ignorePropertiesForDataColumnMeta) {
        if (source.getOutDataSetMeta() != null) {
            DataSetMeta newOutDataSetMeta = new DataSetMeta();
            BeanUtils.copyProperties(source.getOutDataSetMeta(), newOutDataSetMeta, ignorePropertiesForDataSetMeta);
            Map<String, DataColumnMeta> oldDataColumnMetas = source.getOutDataSetMeta().getDataColumnMetas();
            for (Map.Entry<String, DataColumnMeta> entry : oldDataColumnMetas.entrySet()) {
                DataColumnMeta newDataColumnMeta = new DataColumnMeta();
                BeanUtils.copyProperties(entry.getValue(), newDataColumnMeta, ignorePropertiesForDataColumnMeta);
                newDataColumnMeta.setColumnName(entry.getKey());
                newOutDataSetMeta.addColumnMeta(newDataColumnMeta);
            }
            meta.setOutDataSetMeta(newOutDataSetMeta);
        }
    }

    /**
     * 克隆 输入数据集meta
     *
     * @param source
     * @param meta
     * @param ignorePropertiesForDataSetMeta
     * @param ignorePropertiesForDataColumnMeta
     */
    private void cloneInDataSetMeta(TransMeta source, TransMeta meta, String[] ignorePropertiesForDataSetMeta, String[] ignorePropertiesForDataColumnMeta) {
        if (source.getInDataSetMeta() != null) {
            DataSetMeta newInDataSetMeta = new DataSetMeta();
            BeanUtils.copyProperties(source.getInDataSetMeta(), newInDataSetMeta, ignorePropertiesForDataSetMeta);
            Map<String, DataColumnMeta> oldDataColumnMetas = source.getInDataSetMeta().getDataColumnMetas();
            for (Map.Entry<String, DataColumnMeta> entry : oldDataColumnMetas.entrySet()) {
                DataColumnMeta newDataColumnMeta = new DataColumnMeta();
                BeanUtils.copyProperties(entry.getValue(), newDataColumnMeta, ignorePropertiesForDataColumnMeta);
                newDataColumnMeta.setColumnName(entry.getKey());
                newInDataSetMeta.addColumnMeta(newDataColumnMeta);
            }
            meta.setInDataSetMeta(newInDataSetMeta);
        }
    }


    /**
     * 克隆HopMeta
     *
     * @param source
     * @param transMapping
     * @param meta
     */
    private void cloneTransHopMeta(TransMeta source, Map<String, TransMeta> transMapping, TransMeta meta) {
        String[] ignorePropertiesForHop = {"id", "fromTrans", "toTrans", "belongToTrans"};
        Set<TransHopMeta> newHops = new HashSet<>();
        for (TransHopMeta hop : source.getHops()) {
            TransHopMeta newHop = new TransHopMeta();
            BeanUtils.copyProperties(hop, newHop, ignorePropertiesForHop);
            for (TransMeta transMeta : meta.getChildren()) {
                newHop.setFromTrans(transMapping.get(hop.getFromTrans().getId()));
                newHop.setToTrans(transMapping.get(hop.getToTrans().getId()));
                newHop.setBelongToTrans(meta);
            }
            newHops.add(newHop);
        }
        meta.setHops(newHops);
    }


    public UdfGraph _clone(UdfGraph source) {
        UdfGraph graph = new UdfGraph();
        BeanUtils.copyProperties(source, graph, "id");
        graph.setId(StringUtils.uuid());
        this.baseDao.save(graph);
        Map<String, String> idMapping = cloneUdfNodeReturnMap(source, graph);
        cloneUdfEdge(source, graph, idMapping);
        return graph;
    }


    /**
     * 克隆节点
     *
     * @param source
     * @param graph
     * @return
     */
    private Map<String, String> cloneUdfNodeReturnMap(UdfGraph source, UdfGraph graph) {
        Set<UdfNode> nodes = new HashSet<>();
        Map<String, String> idMapping = new HashMap<>();
        for (UdfNode node : source.getUdfNodeSet()) {
            UdfNode newNode = new UdfNode();
            BeanUtils.copyProperties(node, newNode, "id", "udfOperator", "graphId");
            newNode.setId(StringUtils.uuid());
            newNode.setGraphId(graph.getId());
            newNode.setUdfOperator(node.getUdfOperator());
            this.baseDao.save(newNode);
            nodes.add(newNode);
            idMapping.put(node.getId(), newNode.getId());
        }
        graph.setUdfNodeSet(nodes);
        return idMapping;
    }

    /**
     * 克隆边缘
     *
     * @param source
     * @param graph
     * @param idMapping
     */
    private void cloneUdfEdge(UdfGraph source, UdfGraph graph, Map<String, String> idMapping) {
        Set<UdfEdge> edges = new HashSet<>();
        for (UdfEdge edge : source.getUdfEdgeSet()) {
            UdfEdge newEdge = new UdfEdge();
            BeanUtils.copyProperties(edge, newEdge, "id", "graphId", "inNodeId", "outNodeId");
            newEdge.setGraphId(graph.getId());
            newEdge.setInNodeId(idMapping.get(edge.getInNodeId()));
            newEdge.setOutNodeId(idMapping.get(edge.getOutNodeId()));
            this.baseDao.save(newEdge);
            edges.add(newEdge);
        }
        graph.setUdfEdgeSet(edges);
    }

    private String getTranName(Set<String> el, String name, int count) {
        Assert.notNull(el, "目录不能为空");
        String newName = name;
        if (el.contains(name)) {
            if (count == 0) {
                newName = getTranName(el, name + "(" + 1 + ")", count + 1);
            } else {
                String temp = name.replaceAll("\\Q(\\E[^()]+\\Q)\\E$", "(" + count + ")");
                newName = getTranName(el, temp, count + 1);
            }
        }
        return newName;
    }


    public Map<String, Object> copyPlugin(CopyPluginVo vo) {
        String transId = vo.getTransId();
        TransMeta transMeta = transMetaService.getTransMetaById(transId);
        TransMeta oldTransStep = transMetaService.getTransMetaById(vo.getStepId());
        String operateTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        Map<String, TransMeta> transMapping = new HashMap<>();
        TransMeta newTransStep = _clone(oldTransStep, transMapping);
        newTransStep.setOperateTime(operateTime);
        newTransStep.setName(vo.getStepName());
        newTransStep.setCode(vo.getStepName());
        newTransStep.setX(vo.getX());
        newTransStep.setY(vo.getY());
        TransMeta parent = new TransMeta();
        parent.setId(transId);
        Set<TransMeta> parents = new HashSet<>();
        parents.add(parent);
        newTransStep.setParents(parents);
        this.transMetaService.saveTransMeta(newTransStep);
        if (newTransStep.getTransPartition() != null) {
            this.baseDao.saveOrUpdate(newTransStep.getTransPartition());
        }

        TransPluginMeta usedPlugin = newTransStep.getUsedPlugin();
        if (JOIN.contains(usedPlugin.getCode())) {   // 处理碰撞插件
            buildJoin(newTransStep, usedPlugin);
        }
        if ("serviceOrganization".equals(usedPlugin.getCode())) {  // 处理算子编排
            buildServiceOrganization(newTransStep);
        }
        if ("samplingAndShuntingPlugin".equals(usedPlugin.getCode())) {  // 处理采样分流
            buildSamplingAndShunting(newTransStep, usedPlugin);
        }
        Set<TransMeta> children = transMeta.getChildren();
        children.add(newTransStep);
        transMeta.setChildren(children);
        this.baseDao.saveOrUpdate(transMeta);
        Map<String, Object> map = Maps.newHashMap();
        map.put("id", newTransStep.getId());
        map.put("label", newTransStep.getName());
        map.put("pluginDir", newTransStep.getUsedPlugin().getDefPluginDir());
        map.put("keyWord", newTransStep.getUsedPlugin().getCode());
        map.put("x", newTransStep.getX());
        map.put("y", newTransStep.getY());
        return map;
    }

    /**
     * 处理采样分流
     *
     * @param newTransStep
     * @param usedPlugin
     */
    private void buildSamplingAndShunting(TransMeta newTransStep, TransPluginMeta usedPlugin) {
        TransAttributeMeta attribute = usedPlugin.getFeature("defaultTargetStep");
        String attributeValue = newTransStep.getAttributeValue(attribute);
        newTransStep.addTransAttribute(attribute, attributeValue);
        Set<TransExpMeta> transExps = newTransStep.getTransExps();
        for (TransExpMeta exp : transExps) {
            ElementNode targetStep = (ElementNode) exp.getExpression().getArgument("targetStep");
            String expValue = exp.getExpValue(targetStep);
            exp.addExpValue(targetStep, expValue);
        }
        this.baseDao.saveOrUpdate(newTransStep);
    }

    /**
     * 处理算子编排
     *
     * @param newTransStep
     */
    private void buildServiceOrganization(TransMeta newTransStep) {
        Set<TransExpMeta> transExps = newTransStep.getTransExps();
        for (TransExpMeta exp : transExps) {
            ElementNode serviceOrgId = (ElementNode) exp.getExpression().getArgument("serviceOrgId");
            String expValue = exp.getExpValue(serviceOrgId);
            UdfGraph oldGraph = udfGraphService.get(UdfGraph.class, expValue);
            UdfGraph graph = _clone(oldGraph);
            this.baseDao.save(graph);
            exp.addExpValue(serviceOrgId, graph.getId());
        }

        this.baseDao.saveOrUpdate(newTransStep);
    }

    /**
     * 处理碰撞插件
     *
     * @param newTransStep
     * @param usedPlugin
     */
    private void buildJoin(TransMeta newTransStep, TransPluginMeta usedPlugin) {
        TransAttributeMeta leftAttribute = usedPlugin.getFeature("leftStepId");
        String leftStepId = newTransStep.getAttributeValue(leftAttribute);

        newTransStep.addTransAttribute(leftAttribute, leftStepId);

        TransAttributeMeta rightAttribute = usedPlugin.getFeature("rightStepId");
        String rightStepId = newTransStep.getAttributeValue(rightAttribute);
        newTransStep.addTransAttribute(rightAttribute, rightStepId);

        // aggregatorFieldExps
        Set<TransExpMeta> transExps = newTransStep.getTransExps();
        for (TransExpMeta exp : transExps) {
            ElementNode nodeObjId = (ElementNode) exp.getExpression().getArgument("objId");
            if (nodeObjId == null) continue;
            String expValue = exp.getExpValue(nodeObjId);
            if (expValue == null) continue;
            exp.addExpValue(nodeObjId, expValue);
        }

        this.baseDao.saveOrUpdate(newTransStep);
    }

    public String queryDeleteRight(String transStepId) {
        String sql = "select from_trans_id from T_ETL_TRANS_HOPS where to_trans_id = :id and code like '%right%'";
        Map<String, Object> map = Maps.newHashMap();

        map.put("id", transStepId);
        return baseDao.sqlQueryForValue(sql, map);
    }


    public TransMeta saveStepSql(String tranStepId) {
        TransMeta trans = pluginConfigService.get(TransMeta.class, tranStepId);

        String transsql = helper.parseStepSql(trans,false,null);
        String sql = "UPDATE t_etl_trans set TRANS_SQL =:transsql where id =:tranStepId";
        Map param=new HashMap();
        param.put("transsql",transsql);
        param.put("tranStepId",tranStepId);
        this.baseDao.executeSqlUpdate(sql, param);

        return  trans;
    }
}
