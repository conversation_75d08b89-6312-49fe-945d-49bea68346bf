package com.dragonsoft.cicada.datacenter.modules.datavisual.function;

import java.util.ArrayList;
import java.util.List;

/**
 * chart configuration
 *
 * <AUTHOR>
 * @date 2020-12-30 9:30
 */
public class ChartConfig {
    private List<ColumnMeta> dimensions;
    private List<ColumnMeta> metrics;

    public ChartConfig() {
    }

    public ChartConfig(List<ColumnMeta> dimensions, List<ColumnMeta> metrics) {
        this.dimensions = dimensions;
        this.metrics = metrics;
    }

    public ChartConfig addDimension(ColumnMeta dimension) {
        if (this.dimensions == null) {
            this.dimensions = new ArrayList<>();
        }
        this.dimensions.add(dimension);
        return this;
    }

    public ChartConfig addMetric(ColumnMeta metric) {
        if (this.metrics == null) {
            this.metrics = new ArrayList<>();
        }
        this.metrics.add(metric);
        return this;
    }

    public List<ColumnMeta> getDimensions() {
        return dimensions;
    }

    public void setDimensions(List<ColumnMeta> dimensions) {
        this.dimensions = dimensions;
    }

    public List<ColumnMeta> getMetrics() {
        return metrics;
    }

    public void setMetrics(List<ColumnMeta> metrics) {
        this.metrics = metrics;
    }
}
