package com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.service.impl;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.code.common.mist.service.structure.model.ClassMeta;
import com.code.common.mist.service.structure.model.ServiceClassMeta;
import com.code.common.utils.R;
import com.code.common.utils.StringUtils;
import com.code.common.utils.assertion.Assert;
import com.code.metadata.aimodel.ScriptInfo;
import com.code.metadata.aimodel.ScriptLog;
import com.code.metadata.sm.EnumServiceType;
import com.code.metadata.sm.ServiceMeta;
import com.code.metadata.sm.ServiceParams;
import com.code.metadata.sm.ServicePublication;
import com.code.metaservice.aimodel.IScriptLogService;
import com.code.metaservice.sm.IServiceMetaService;
import com.code.metaservice.sm.IServiceParamsService;
import com.code.metaservice.sm.IServicePublicationService;
import com.dragonsoft.cicada.datacenter.ServicePublishCenterClient;
import com.dragonsoft.cicada.datacenter.common.utils.ConvertToPinyinUtils;
import com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.service.IAiServicePublishService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.vo.ParamConfigVo;
import com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.vo.ServicePublicationVo;
import com.dragonsoft.cicada.datacenter.modules.modeling.aimodeling.client.IJypyterCodeClient;
import com.dragonsoft.cicada.datacenter.modules.modeling.aimodeling.config.AiModelIpAndPort;
import com.fw.service.BaseService;
import com.fw.service.annotation.Service;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.ClassUtils;

import java.beans.Introspector;
import java.sql.Timestamp;
import java.util.*;

@Service
public class AiServicePublishServiceImpl extends BaseService implements IAiServicePublishService {


    @Value("${ai.dir.aiServicePath}")
    private String dirBasicPath;

    @Autowired
    private DynamicStructureAiServiceImpl dynamicStructureAiService;
    @Autowired
    private IServiceMetaService serviceMetaService;

    @Autowired
    private IServiceParamsService serviceParamsService;

    @Autowired
    private IJypyterCodeClient jupyterCodeClient;

    @Autowired
    private IServicePublicationService servicePublicationService;

    @Autowired
    private IScriptLogService scriptLogService;

    @Autowired
    private AiModelIpAndPort aiModelIpAndPort;

    @Override
    public JSONArray getLogIdAndParams(String scriptId) {
        JSONArray jsonArray = jupyterCodeClient.queryReleaseRst(scriptId);
        //AtomicInteger index = new AtomicInteger(1);
        jsonArray.sort(Comparator.comparing(e-> ((JSONObject)e).getString("create_time")).reversed());
        for (Object o : jsonArray) {
            JSONObject object = (JSONObject) o;
            object.put("practiceVersion",object.get("create_time"));
        }
        return jsonArray;
    }

    @Override
    public R publishService(ServicePublicationVo vo, String userId) {
        /*ServiceClassMeta serviceClassMeta = dynamicStructureAiService.dynamicCreateAiClassMetaByVo(vo);
        serviceClassMeta.setUserId(userId);*/
        //servicePublication.set
        //String beanName = ClassBuildUtils.buildClassName(serviceClassMeta.getInterfaceMeta(), serviceClassMeta.getImplMeta());
        //if (StringUtils.isBlank(vo.getServiceId())){
            checkChineseName(vo.getApiName());
        //}
        EnumServiceType serviceType = EnumServiceType.getInstanceByCode(vo.getServiceType());
        String code = ConvertToPinyinUtils.convertChineseName(vo.getApiName());
        String rspMsg = jupyterCodeClient.serviceRelease(vo.getSourceId(), dirBasicPath, code);
        if (!rspMsg.contains("成功")){
            Assert.fail("发布失败:"+rspMsg);
        }
        ServiceMeta serviceMeta = new ServiceMeta();
        serviceMeta.setPublishTime(new Timestamp(new Date().getTime()));
        serviceMeta.setRequestPath("/run_op/"+code+"/call");
        serviceMeta.setStatus("1");
        serviceMeta.setUpdateTime(new Timestamp(new Date().getTime()));
        serviceMeta.setIsDefault("1");
        serviceMeta.setMemo(vo.getMemo());
        serviceMeta.setFunctionMethod(code);
        serviceMeta.setImplementName(code);
        serviceMeta.setLoadPath(dirBasicPath);
        serviceMeta.setName(code);
        serviceMeta.setCode(code);
        serviceMeta.setInterfaceName(code);
        serviceMeta.setOriginMethod(code);
        serviceMeta.setOperateUserId(userId);
        serviceMeta.setType("ServiceMeta");
        serviceMeta.setVersion(vo.getVersion());

        ServicePublication servicePublication = new ServicePublication();
        servicePublication.setName(vo.getApiName());
        servicePublication.setType("ServicePublication");
        servicePublication.setCode(code);
        servicePublication.setOperateUserId(userId);
        servicePublication.setServiceType(serviceType.getCode());
        servicePublication.setStatus("1");
        servicePublication.setSourceId(vo.getSourceId());
        servicePublication.setPromulgator("DC");

        ServiceParams serviceParamsReq = new ServiceParams();
        serviceParamsReq.setName("op_input");
        serviceParamsReq.setType("ServiceParams");
        serviceParamsReq.setCode("op_input");
        serviceParamsReq.setMemo("请求参数");
        serviceParamsReq.setIsMust("t");
        serviceParamsReq.setIsOutput("0");
        serviceParamsReq.setExtendedType("String");
        serviceParamsReq.setDataType("java.lang.String");
        serviceParamsReq.setPos(0);


        List<ServiceParams> childrenParamsReq = getChildrenParams(vo.getRequestParams(), "0",null);
        serviceParamsReq.setChildrenParam(new HashSet<>(childrenParamsReq));

        ServiceParams serviceParamsOut = new ServiceParams();
        serviceParamsOut.setName("result");
        serviceParamsOut.setType("ServiceParams");
        serviceParamsOut.setCode("result");
        serviceParamsOut.setMemo("返回结果");
        serviceParamsOut.setIsMust("t");
        serviceParamsOut.setIsOutput("1");
        serviceParamsOut.setExtendedType("String");
        serviceParamsOut.setDataType("java.lang.String");
        serviceParamsOut.setPos(0);

        List<ServiceParams> childrenParamsOut = getChildrenParams(vo.getResponseParams(), "1",null);
        serviceParamsOut.setChildrenParam(new HashSet<>(childrenParamsOut));

        List<ServiceParams> serviceParams = Lists.newArrayList();
        serviceParams.add(serviceParamsReq);
        serviceParams.add(serviceParamsOut);



        servicePublication.setParams(new HashSet<>(serviceParams));

        this.baseDao.saveOrUpdate(servicePublication);

        for (ServiceParams serviceParam : serviceParams) {
            serviceParam.setOwner(servicePublication);
            Set<ServiceParams> childrenParam = serviceParam.getChildrenParam();
            for (ServiceParams params : childrenParam) {
                params.setParentParam(serviceParam);
                this.baseDao.saveOrUpdate(params);
            }
            this.baseDao.save(serviceParam);
        }

        serviceMeta.setServicePublication(servicePublication);

        this.baseDao.saveOrUpdate(serviceMeta);
        return R.ok();
    }

    private List<ServiceParams> getChildrenParams(List<ServicePublicationVo.Params> params,String isOutPut,ServiceParams parent){
        List<ServiceParams> paramsList = Lists.newArrayList();
        int i = 0;
        for (ServicePublicationVo.Params param : params) {
            ServiceParams serviceParam = new ServiceParams();
            serviceParam.setName(param.getParamName());
            serviceParam.setType("ServiceParams");
            serviceParam.setMemo(param.getParamMemo());
            serviceParam.setCode(param.getParamName());
            serviceParam.setExtendedType(param.getParamType());
            serviceParam.setDataType(dynamicStructureAiService.changeTypePath(param.getParamType()));
            serviceParam.setIsOutput(isOutPut);
            serviceParam.setIsMust(param.getIsMust() ? "t" : "f");
            serviceParam.setPos(i++);
            serviceParam.setParentParam(parent);
            if (param.getChildren().size() > 0){
                List<ServiceParams> childrenParams = getChildrenParams(param.getChildren(), isOutPut,serviceParam);
                serviceParam.setChildrenParam(new HashSet<>(childrenParams));
            }
            paramsList.add(serviceParam);
        }

        return paramsList;
    }

    @Override
    public Map testAiService(String serviceId, Map params) {
        String jsonString = JSONObject.toJSONString(params);
        ServiceMeta serviceMeta = serviceMetaService.queryDefault(serviceId);
        if (!"1".equals(serviceMeta.getStatus())){
            Assert.fail("该服务已下线，请重新上线！");
        }
        JSONObject jsonObject = jupyterCodeClient.testAiService(jsonString, serviceMeta.getRequestPath());
        return jsonObject;
    }

    @Override
    public Map<String,Object> publishTestService(ServicePublicationVo vo, String userId) {
        if (StringUtils.isBlank(vo.getApiName())){
            Assert.fail("服务名称不能为空!");
        }
        if (StringUtils.isBlank(vo.getServiceType())){
            Assert.fail("服务类型不能为空!");
        }
        if (StringUtils.isBlank(vo.getSourceId())){
            Assert.fail("训练版本号不能为空!");
        }
        Preconditions.checkNotNull(vo.getVersion(), "版本号不能为空!");
        //checkChineseName(vo.getApiName());
        boolean isUpdate = StringUtils.isNotBlank(vo.getServiceId());
        //String code = ConvertToPinyinUtils.convertChineseName(vo.getApiName());
        ServiceClassMeta serviceClassMeta = dynamicStructureAiService.dynamicCreateAiClassMetaByVo(vo);
        String code = buildClassName(serviceClassMeta.getInterfaceMeta(), serviceClassMeta.getImplMeta());
        if (isUpdate){
            ServiceMeta serviceMeta = serviceMetaService.queryDefault(vo.getServiceId());
            code = serviceMeta.getCode();

            ClassMeta interfaceMeta = serviceClassMeta.getInterfaceMeta();
            interfaceMeta.setId(vo.getServiceId());
            serviceClassMeta.setInterfaceMeta(interfaceMeta);
        }
        if (dirBasicPath.endsWith("/")){
            dirBasicPath = dirBasicPath.substring(0,dirBasicPath.length() - 1);
        }


        String path = dirBasicPath + "/" + code + "/" + vo.getVersion();
        serviceClassMeta.setUserId(userId);
        ServicePublishCenterClient.ServicePublishVo servicePublishVo = new ServicePublishCenterClient.ServicePublishVo();
        servicePublishVo.setFilePath(dirBasicPath + "/" + code + "/" + vo.getVersion());
        serviceClassMeta.setAiRequestPath("/run_op/"+code+"/call");
        servicePublishVo.setServiceClassMeta(serviceClassMeta);
        String rspMsg = jupyterCodeClient.serviceRelease(vo.getSourceId(), path, code);
        if (!rspMsg.contains("成功")){
            Assert.fail("发布失败:"+rspMsg);
        }
        Map<String,Object> map = Maps.newHashMap();
        map.put("servicePublishVo",servicePublishVo);
        map.put("code",code);
        return map;
    }

    @Override
    public R uninstall(String serviceMetaId) {
        /*ServicePublication servicePublication = (ServicePublication) this.baseDao.get(ServicePublication.class, serviceId);
        if (servicePublication == null || servicePublication.getServiceMetas().size() <= 0){
            Assert.fail("该服务已被卸载");
        }*/
        ServiceMeta serviceMeta = serviceMetaService.queryByMetaId(serviceMetaId);
        //String s = jupyterCodeClient.serviceCancel(serviceMeta.getLoadPath(), serviceMeta.getCode());
        if (serviceMeta == null){
            Assert.fail("该服务已被卸载");
        }
        ServicePublication servicePublication = serviceMeta.getServicePublication();
        this.baseDao.delete(serviceMeta);
        this.baseDao.delete(servicePublication);
        return R.ok();
    }

    @Override
    public void offline(String serviceMetaId) {
        ServiceMeta serviceMeta = serviceMetaService.queryByMetaId(serviceMetaId);
        if ("0".equals(serviceMeta.getStatus())){
            Assert.fail("请勿重新下线！");
        }
        serviceMeta.setStatus("0");
        this.baseDao.saveOrUpdate(serviceMeta);
    }

    @Override
    public void export(String serviceMetaId) {
        ServiceMeta serviceMeta = serviceMetaService.queryByMetaId(serviceMetaId);
        if ("1".equals(serviceMeta.getStatus())){
            Assert.fail("请勿重新上线！");
        }
        serviceMeta.setStatus("1");
        this.baseDao.saveOrUpdate(serviceMeta);
    }

    @Override
    public Map updateRequestPath(String serviceMetaId, String code) {
        ServiceMeta serviceMeta = serviceMetaService.queryByMetaId(serviceMetaId);
        String sourceId = serviceMeta.getServicePublication().getSourceId();
        if (StringUtils.isNotBlank(sourceId)){
            ScriptLog scriptLogById = scriptLogService.getScriptLogById(sourceId);
            ScriptInfo scriptInfo = (ScriptInfo) this.baseDao.get(ScriptInfo.class, scriptLogById.getScriptId());
            serviceMeta.setName(scriptInfo.getName());
        }
        serviceMeta.setRequestPath("/run_op/"+code+"/call");
        serviceMeta.setCode(code);
        this.baseDao.saveOrUpdate(serviceMeta);
        Map map1 = Maps.newHashMap();
        map1.put("serviceMetaId",serviceMeta.getId());
        map1.put("serviceId",serviceMeta.getServicePublication().getId());
        map1.put("name",serviceMeta.getServicePublication().getName());
        map1.put("requestPath",aiModelIpAndPort.getRunOpServiceIp() + serviceMeta.getRequestPath());
        return map1;
    }


    @Override
    public void deleteServiceMetaById(String serviceMetaId) {
        String sql = "delete from t_md_service_meta where id = :serviceMetaId";
        this.baseDao.executeSqlUpdate(sql,addParam("serviceMetaId",serviceMetaId).param());
    }

    private void checkChineseName(String apiName) {
        String sql0 = "select id from t_md_service_publication where name =:name and service_type = '2' ";
        String id1 = this.baseDao.sqlQueryForValue(sql0, addParam("name", apiName).param());
        Assert.isNull(id1, "请检查服务中文名称是否已存在！");
    }

    private String buildImplClassName(String chineseName) {
        return ConvertToPinyinUtils.convertChineseName(chineseName);
    }

    private void checkServiceName(ClassMeta classMeta, ParamConfigVo paramConfigVo) {
        String beanName = classMeta.getPromulgator().getCode() + "_" + classMeta.getVersion() + "_" + Introspector.decapitalize(ClassUtils.getShortName(classMeta.getFullName()));
        ServiceMeta serviceMeta = serviceMetaService.queryByCode(beanName);
        Assert.isNull(serviceMeta, "已存在相同的模型英文名称：" + paramConfigVo.getImplEnglishName() + "--->或相同的版本：" + paramConfigVo.getImplVersion());
        String sql = "select id from t_md_service_meta where implement =:implementName";
        String id = this.baseDao.sqlQueryForValue(sql, addParam("implementName", classMeta.getFullName()).param());
        Assert.isNull(id, "请检查服务英文名称与模型英文名称是否已存在！");
    }

    public static String buildClassName(ClassMeta interfaceMeta, ClassMeta classMeta) {
        return classMeta.getPromulgator().getCode() + "_"
                + classMeta.getVersion() + "_"
                + Introspector.decapitalize(ClassUtils.getShortName(interfaceMeta.getFullName())) + "_"
                + Introspector.decapitalize(ClassUtils.getShortName(classMeta.getFullName()));
    }
}
