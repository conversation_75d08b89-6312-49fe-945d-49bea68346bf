 
/*==============================================================*/
/* Table: T_MD_SERVICE_CONTAINER      服务容器                          */
/*==============================================================*/
CREATE TABLE if not exists T_MD_SERVICE_CONTAINER (
   ID                   VARCHAR(32)          NOT NULL,
   IP                   VARCHAR(15)          NULL,
   MIRROR_VERSION       VARCHAR(100)         NULL, 
   CONTAINER_INNER_PORT INT8                 NULL,
   CONTAINER_OUTER_PORT INT8                 NULL,
   CONTAINER_PATH       VARCHAR(300)         NULL,
   EXPANSION_STRATEGY   VARCHAR(32)          NULL,
   CONSTRAINT PK_T_MD_SERVICE_CONTAINER PRIMARY KEY (ID)
)
INHERITS (T_MD_ELEMENT);

COMMENT ON COLUMN T_MD_SERVICE_CONTAINER.ID IS
'容器ID';

COMMENT ON COLUMN T_MD_SERVICE_CONTAINER.IP IS
'物理机ip';

COMMENT ON COLUMN T_MD_SERVICE_CONTAINER.MIRROR_VERSION IS
'镜像版本号'; 

COMMENT ON COLUMN T_MD_SERVICE_CONTAINER.CONTAINER_INNER_PORT IS
'容器内部端口';

COMMENT ON COLUMN T_MD_SERVICE_CONTAINER.CONTAINER_OUTER_PORT IS
'容器对外映射端口';

COMMENT ON COLUMN T_MD_SERVICE_CONTAINER.CONTAINER_PATH IS
'容器持久化目录';

COMMENT ON COLUMN T_MD_SERVICE_CONTAINER.EXPANSION_STRATEGY IS
'扩容策略';


/*==============================================================*/
/* Table: T_MD_SERVICE                                          */
/*==============================================================*/
CREATE TABLE T_MD_SERVICE (
   ID                   VARCHAR(32)          NOT NULL,
   SERVICE_TYPE         VARCHAR(2)           NULL,
   STATUS               VARCHAR(2)           NULL,
   URL                  VARCHAR(500)          NULL,
   PUBLISH_TIME         TIMESTAMP            NULL,
   UPDATE_TIME          TIMESTAMP            NULL,
   CONSTRAINT PK_T_MD_SERVICE PRIMARY KEY (ID)
)
INHERITS (T_MD_ELEMENT);

COMMENT ON COLUMN T_MD_SERVICE.SERVICE_TYPE IS
'服务类型';

COMMENT ON COLUMN T_MD_SERVICE.STATUS IS
'发布状态';

COMMENT ON COLUMN T_MD_SERVICE.PUBLISH_TIME IS
'发布时间';

COMMENT ON COLUMN T_MD_SERVICE.UPDATE_TIME IS
'更新时间';

COMMENT ON COLUMN T_MD_SERVICE.URL IS
'对外暴露的url';




/*==============================================================*/
/* Table: T_SERVICE_CONTAINER_RELATION   服务容器关系                       */
/*==============================================================*/
CREATE TABLE if not exists T_SERVICE_CONTAINER_RELATION (
   ID                   VARCHAR(32)          NOT NULL,
   SERVICE_ID           VARCHAR(32)          NULL,
   SERVICE_NAME         VARCHAR(100)         NULL,
   CONTAINER_ID         VARCHAR(32)          NULL,
   CONTAINER_NAME       VARCHAR(100)         NULL,
   CONSTRAINT PK_T_SERVICE_CONTAINER_RELATIO PRIMARY KEY (ID)
)
INHERITS (T_MD_ELEMENT);

COMMENT ON COLUMN T_SERVICE_CONTAINER_RELATION.SERVICE_ID IS
'服务id';

COMMENT ON COLUMN T_SERVICE_CONTAINER_RELATION.SERVICE_NAME IS
'服务名';

COMMENT ON COLUMN T_SERVICE_CONTAINER_RELATION.CONTAINER_ID IS
'容器id';

COMMENT ON COLUMN T_SERVICE_CONTAINER_RELATION.CONTAINER_NAME IS
'容器名';

ALTER TABLE T_SERVICE_CONTAINER_RELATION
   ADD CONSTRAINT FK_T_SERVIC_REFERENCE_T_MD_S11 FOREIGN KEY (SERVICE_ID)
      REFERENCES T_MD_SERVICE (ID)
      ON DELETE RESTRICT ON UPDATE RESTRICT;

ALTER TABLE T_SERVICE_CONTAINER_RELATION
   ADD CONSTRAINT FK_T_SERVIC_REFERENCE_T_MD_S22 FOREIGN KEY (CONTAINER_ID)
      REFERENCES T_MD_SERVICE_CONTAINER (ID)
      ON DELETE RESTRICT ON UPDATE RESTRICT;





/*==============================================================*/
/* Table: T_MD_COMPOSITE_SERVICE     组合服务                           */
/*==============================================================*/
 CREATE TABLE if not exists T_MD_COMPOSITE_SERVICE (
   ID                   VARCHAR(32)          NOT NULL,
   CONSTRAINT PK_T_MD_COMPOSITE_SERVICE PRIMARY KEY (ID)
)
INHERITS (T_MD_SERVICE);

 

/*==============================================================*/
/* Table: T_SERVICE_COMPOSITE_NODE        组合服务节点                      */
/*==============================================================*/
 
CREATE TABLE if not exists T_SERVICE_COMPOSITE_NODE (
   ID                   VARCHAR(32)          NOT NULL,
   COMPOSITE_SERVICE_ID VARCHAR(32)          NOT NULL,
   SON_SERVICE_ID       VARCHAR(32)          NULL,
   SON_SERVICE_NAME     VARCHAR(32)          NULL,
   COMPOSITE_SERVICE_NAME VARCHAR(200)         NULL,
   CONSTRAINT PK_T_SERVICE_COMPOSITE_NODE PRIMARY KEY (ID)
)
INHERITS (T_MD_ELEMENT);

ALTER TABLE T_SERVICE_COMPOSITE_NODE
   ADD CONSTRAINT FK_T_SERVIC_REFERENCE_T_MD_c11 FOREIGN KEY (COMPOSITE_SERVICE_ID)
      REFERENCES T_MD_COMPOSITE_SERVICE (ID)
      ON DELETE RESTRICT ON UPDATE RESTRICT;

ALTER TABLE T_SERVICE_COMPOSITE_NODE
   ADD CONSTRAINT FK_T_SERVIC_REFERENCE_T_MD_S55 FOREIGN KEY (SON_SERVICE_ID)
      REFERENCES T_MD_SERVICE (ID)
      ON DELETE RESTRICT ON UPDATE RESTRICT;


/*==============================================================*/
/* Table: T_SERVICE_COMPOSITE_RELATION          组合服务边                */
/*==============================================================*/
 
CREATE TABLE if not exists T_SERVICE_COMPOSITE_RELATION (
   ID                   VARCHAR(32)          NOT NULL,
   COMPOSITE_SERVICE_ID VARCHAR(32)          NULL,
   COMPOSITE_SERVICE_NAME VARCHAR(200)         NULL,
   START_SON_NODE_ID    VARCHAR(32)          NULL,
   START_SON_SERVICE_NAME VARCHAR(32)          NULL,
   END_SON_NODE_ID      VARCHAR(32)          NULL,
   END_SON_SERVICE_NAME VARCHAR(200)         NULL,
   CONSTRAINT PK_T_SERVICE_COMPOSITE_RELATIO PRIMARY KEY (ID)
)
INHERITS (T_MD_ELEMENT);

COMMENT ON COLUMN T_SERVICE_COMPOSITE_RELATION.START_SON_NODE_ID IS
'开始子服务节点id';

COMMENT ON COLUMN T_SERVICE_COMPOSITE_RELATION.END_SON_NODE_ID IS
'结束子服务节点id';

ALTER TABLE T_SERVICE_COMPOSITE_RELATION
   ADD CONSTRAINT FK_T_SERVIC_REFERENCE_T_MD_666 FOREIGN KEY (COMPOSITE_SERVICE_ID)
      REFERENCES T_MD_COMPOSITE_SERVICE (ID)
      ON DELETE RESTRICT ON UPDATE RESTRICT;

ALTER TABLE T_SERVICE_COMPOSITE_RELATION
   ADD CONSTRAINT FK_T_SERVIC_REFERENCE_T_1 FOREIGN KEY (START_SON_NODE_ID)
      REFERENCES T_SERVICE_COMPOSITE_NODE (ID)
      ON DELETE RESTRICT ON UPDATE RESTRICT;

ALTER TABLE T_SERVICE_COMPOSITE_RELATION
   ADD CONSTRAINT FK_T_SERVIC_REFERENCE_T_2 FOREIGN KEY (END_SON_NODE_ID)
      REFERENCES T_SERVICE_COMPOSITE_NODE (ID)
      ON DELETE RESTRICT ON UPDATE RESTRICT;



 
/*==============================================================*/
/* Table: T_SERVICE_PARAM_RELATION    服务参数关系                          */
/*==============================================================*/
 
CREATE TABLE if not exists T_SERVICE_PARAM_RELATION (
   ID                   VARCHAR(32)          NOT NULL,
   COMPOSITE_SERVICE_NAME VARCHAR(200)         NULL,
   START_PARAM_NAME     VARCHAR(200)         NULL,
   START_PARAM_ID       VARCHAR(32)          NULL,
   END_PARAM_NAME       VARCHAR(200)         NULL,
   END_PARAM_ID         VARCHAR(32)          NULL,
   CONSTRAINT PK_T_SERVICE_PARAM_RELATION PRIMARY KEY (ID)
)
INHERITS (T_MD_ELEMENT);

COMMENT ON COLUMN T_SERVICE_PARAM_RELATION.COMPOSITE_SERVICE_NAME IS
'组合服务的名称，冗余字段';

COMMENT ON COLUMN T_SERVICE_PARAM_RELATION.START_PARAM_ID IS
'开始的参数id';

COMMENT ON COLUMN T_SERVICE_PARAM_RELATION.END_PARAM_ID IS
'结束的参数id';

ALTER TABLE T_SERVICE_PARAM_RELATION
   ADD CONSTRAINT FK_T_SERVIC_REFERENCE_T_MD_S66 FOREIGN KEY (START_PARAM_ID)
      REFERENCES T_MD_SERVICE_PARAMS (ID)
      ON DELETE RESTRICT ON UPDATE RESTRICT;

ALTER TABLE T_SERVICE_PARAM_RELATION
   ADD CONSTRAINT FK_T_SERVIC_REFERENCE_T_MD_1 FOREIGN KEY (END_PARAM_ID)
      REFERENCES T_MD_SERVICE_PARAMS (ID)
      ON DELETE RESTRICT ON UPDATE RESTRICT;

/*==============================================================*/
/* Table: T_MD_SERVICE_GATEWAY           服务网关                       */
/*==============================================================*/
CREATE TABLE T_MD_SERVICE_GATEWAY (
   ID                   VARCHAR(32)          NOT NULL,
   GATEWAY_IP           VARCHAR(15)          NULL,
   GATEWAY_M_PORT       INT8                 NULL,
   GATEWAY_HTTP_PORT    INT8                 NULL,
   GATEWAY_HTTPS_PORT   INT8                 NULL,
   CONSTRAINT PK_T_MD_SERVICE_GATEWAY PRIMARY KEY (ID)
)
INHERITS (T_MD_ELEMENT);

COMMENT ON COLUMN T_MD_SERVICE_GATEWAY.GATEWAY_IP IS
'网关ip';

COMMENT ON COLUMN T_MD_SERVICE_GATEWAY.GATEWAY_M_PORT IS
'网关管理端口';

COMMENT ON COLUMN T_MD_SERVICE_GATEWAY.GATEWAY_HTTP_PORT IS
'http服务访问端口';

COMMENT ON COLUMN T_MD_SERVICE_GATEWAY.GATEWAY_HTTPS_PORT IS
'https服务访问端口';



/*==============================================================*/
/* Table: T_SERVICE_GATEWAY_RELATION     服务和网关关系                       */
/*==============================================================*/
CREATE TABLE T_SERVICE_GATEWAY_RELATION (
   ID                   VARCHAR(32)          NOT NULL,
   SERVICE_ID           VARCHAR(32)          NULL,
   SERVICE_NAME         VARCHAR(100)         NULL,
   GATEWAY_ID           VARCHAR(32)          NULL,
   GATEWAY_NAME         VARCHAR(100)         NULL,
   ROUTE_URL            VARCHAR(32)          NULL,
   CONSTRAINT PK_T_SERVICE_GATEWAY_RELATION PRIMARY KEY (ID)
)
INHERITS (T_MD_ELEMENT);

COMMENT ON COLUMN T_SERVICE_GATEWAY_RELATION.SERVICE_ID IS
'服务id';

COMMENT ON COLUMN T_SERVICE_GATEWAY_RELATION.SERVICE_NAME IS
'服务名';

COMMENT ON COLUMN T_SERVICE_GATEWAY_RELATION.GATEWAY_ID IS
'网关id';

COMMENT ON COLUMN T_SERVICE_GATEWAY_RELATION.GATEWAY_NAME IS
'网关名';

COMMENT ON COLUMN T_SERVICE_GATEWAY_RELATION.ROUTE_URL IS
'路由url';

ALTER TABLE T_SERVICE_GATEWAY_RELATION
   ADD CONSTRAINT FK_T_SERVIC_REFERENCE_T_MD_S33 FOREIGN KEY (SERVICE_ID)
      REFERENCES T_MD_SERVICE (ID)
      ON DELETE RESTRICT ON UPDATE RESTRICT;

ALTER TABLE T_SERVICE_GATEWAY_RELATION
   ADD CONSTRAINT FK_T_SERVIC_REFERENCE_T_MD_S44 FOREIGN KEY (GATEWAY_ID)
      REFERENCES T_MD_SERVICE_GATEWAY (ID)
      ON DELETE RESTRICT ON UPDATE RESTRICT;


/*==============================================================*/
/* Table: T_SERVICE_POSTOFFICE                                  */
/*==============================================================*/
CREATE TABLE T_SERVICE_POSTOFFICE (
   ID                   VARCHAR(32)          NULL,
   SUB_PATH             VARCHAR(500)         NULL
)
INHERITS (T_MD_ELEMENT);

COMMENT ON COLUMN T_SERVICE_POSTOFFICE.SUB_PATH IS
'订阅路径';



ALTER TABLE t_md_service_publication ADD publish_time TIMESTAMP   NULL ;
ALTER TABLE t_md_service_publication ADD update_time TIMESTAMP   NULL ; 
ALTER TABLE t_md_service_publication ADD container_id VARCHAR(32)   NULL ; 
ALTER TABLE t_md_service_publication ADD url VARCHAR(500)   NULL ; 
--修改发布服务的继承关系
ALTER TABLE t_md_service_publication INHERIT t_md_service;




--新增参数表字段
ALTER TABLE t_md_service_params ADD parent_param_id VARCHAR(32)   NULL ;
COMMENT ON COLUMN t_md_service_params.parent_param_id IS '父参数';
ALTER TABLE t_md_service_params ADD is_operator_param BOOL ;
COMMENT ON COLUMN t_md_service_params.is_operator_param IS '是否算子参数';