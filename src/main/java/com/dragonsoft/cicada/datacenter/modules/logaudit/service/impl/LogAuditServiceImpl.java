package com.dragonsoft.cicada.datacenter.modules.logaudit.service.impl;

import com.code.common.paging.PageInfo;
import com.code.common.utils.assertion.Assert;
import com.dragonsoft.cicada.datacenter.modules.logaudit.service.LogAuditService;
import com.dragonsoft.cicada.datacenter.modules.logaudit.utils.LogAuditUtils;
import com.fw.service.BaseService;
import com.fw.service.annotation.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/10/9
 */
@Service
public class LogAuditServiceImpl extends BaseService implements LogAuditService {

    private static Logger logger= LoggerFactory.getLogger(LogAuditServiceImpl.class);

    @Override
    public PageInfo getLogInfo(PageInfo pageInfo, Map<String, Object> info) {
        StringBuffer sql = new StringBuffer();
        sql.append("SELECT\n" +
                "\ta .*, b.code,\n" +
                "\tb.message,\n" +
                "\tb.detail_message,\n" +
                "\tc.func_name,\n" +
                "\tc.parent_func_code,\n" +
                "\td.obj_name\n" +
                "FROM\n" +
                "\tt_sys_operate_log a\n" +
                "LEFT JOIN t_sys_exception_log b ON a . ID = b.operate_id\n" +
                "LEFT JOIN t_sys_func c ON a .func_code = c.func_code\n" +
                "LEFT JOIN t_sys_auth_obj d ON a .visit_id = d. ID " +
                " where 1 = 1 ");
        if(info.get("userName") != null && info.get("userName").toString().length() > 0) {
            sql.append(" and  d.obj_name like '%" + info.get("userName") + "%'");
        }
        if(info.get("ipAddress") != null && info.get("ipAddress").toString().length() > 0) {
            sql.append(" and visit_ip like '%" + info.get("ipAddress") + "%'");
        }
        if(info.get("state") != null && info.get("state").toString().length() > 0) {
            if (Objects.equals(info.get("state"), LogAuditUtils.SUCCESS)) {
                sql.append(" and b.code is null ");
            } else if (Objects.equals(info.get("state"), LogAuditUtils.FAIL)){
                sql.append(" and b.code is not null ");
            }
        }
        if(info.get("functionalModule") != null && info.get("functionalModule").toString().length() > 0) {
            sql.append(" and parent_func_code = '" + info.get("functionalModule") + "'");
        }
        if(info.get("beginTime") != null && info.get("endTime") != null && info.get("beginTime").toString().length() > 0 && info.get("beginTime").toString().length() > 0 ) {
            sql.append(" and visit_time between to_timestamp('" +  Long.valueOf(String.valueOf(info.get("beginTime"))) / 1000 +"') and to_timestamp('" + Long.valueOf(String.valueOf(info.get("endTime"))) /1000+"')");
        }

        if(info.get("operateType") != null && info.get("operateType").toString().length() > 0) {
            List<String> operateType = (ArrayList<String>)info.get("operateType");
            sql.append(getAllLikes(operateType));
        }
        sql.append("order by visit_time desc");
        PageInfo rtValue = this.baseDao.sqlQueryForPage(sql.toString(), pageInfo);

        try {
            rtValue.setDataList(LogAuditUtils.changeToVo(rtValue.getDataList()));
        } catch (ParseException e) {
            logger.error(e.getMessage(),e);
            Assert.fail("转换失败");
        }
        return rtValue;
    }

    private String getAllLikes(List<String> types) {
        StringBuffer appendSql = new StringBuffer();
        for (String type : types) {
            if(appendSql.length() == 0) {
                appendSql.append(" and ( a.operate_type = '" + type + "'");
            }
            else {
                appendSql.append(" or a.operate_type = '"+ type + "'");
            }
        }
        appendSql.append(" ) ");
        return appendSql.toString();
    }


}
