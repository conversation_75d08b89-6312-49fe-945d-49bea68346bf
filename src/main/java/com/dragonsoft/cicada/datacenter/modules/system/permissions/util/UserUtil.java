package com.dragonsoft.cicada.datacenter.modules.system.permissions.util;

import cn.hutool.core.codec.Base64;
import com.dragoninfo.dfw.entity.TSysAuthObj;
import com.dragoninfo.dfw.entity.TSysAuthObjRel;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.vo.PageVo;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;

import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/5/29
 */
@Slf4j
public class UserUtil {
    /**
     * 分页模糊查询条件
     *
     * @param pageVo
     * @return
     */
    public static Map<String, String> setPageParams(PageVo pageVo) {
        Map<String, String> params = Maps.newHashMap();
        if (!pageVo.getCode().isEmpty()) {
            params.put("objCode", pageVo.getCode());
        }
        if (!pageVo.getCode().isEmpty()) {
            params.put("objName", pageVo.getName());
        }
        return params;
    }

    /**
     * md5加密
     *
     * @param oldPassword
     * @return
     * @throws NoSuchAlgorithmException
     * @throws UnsupportedEncodingException
     */
    public static String encoderByMd5(String oldPassword) {
        MessageDigest md5 = null;
        //加密后的字符串
        String newPassword = null;
        try {
            md5 = MessageDigest.getInstance("MD5");
            newPassword = Base64.encode(md5.digest(oldPassword.getBytes(StandardCharsets.UTF_8)));
        } catch (NoSuchAlgorithmException e) {
            log.error(e.getMessage(), e);
        }
        return newPassword;
    }

    /**
     * 关系设置  to上级  form下级
     *
     * @param to           上
     * @param relationType 0-用户组，1-用户角色，2-组组， 3-组角色
     * @param fromObj      下
     * @return
     */
    public static TSysAuthObjRel setTSysAuthObjRel(TSysAuthObj to, String relationType, TSysAuthObj fromObj) {
        TSysAuthObjRel sysAuthObjRel = new TSysAuthObjRel();
        sysAuthObjRel.setRelationType(relationType);
        sysAuthObjRel.setToAuthObj(to);
        sysAuthObjRel.setFromAuthObj(fromObj);
        sysAuthObjRel.setCreateTime(new Date(System.currentTimeMillis()));
        sysAuthObjRel.setUpdateTime(new Date(System.currentTimeMillis()));
        sysAuthObjRel.setEndDate(new Date(System.currentTimeMillis()));
        return sysAuthObjRel;
    }

    public static String getRandom(List<String> ids, String head) {
        String newId = head;
        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        newId = newId + sdf.format(date) + Math.round(((Math.random() * 9 + 1) * 1000));
        for (String id : ids) {
            if (id.equals(newId)) {
                newId = UserUtil.getRandom(ids, head);
            }
        }
        return newId;
    }
}
