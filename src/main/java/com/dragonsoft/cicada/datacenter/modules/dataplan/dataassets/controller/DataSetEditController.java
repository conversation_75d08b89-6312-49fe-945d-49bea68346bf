package com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.controller;


import com.alibaba.fastjson.JSON;
import com.code.common.paging.PageInfo;
import com.code.common.utils.R;
import com.code.common.utils.StringUtils;
import com.code.common.utils.assertion.Assert;
import com.code.dataset.operator.column.addcolumn.AddColumnStep;
import com.code.dataset.operator.column.deletecolumn.DeleteColumnStep;
import com.code.dataset.operator.column.editcolumn.EditColumnStep;
import com.code.dataset.operator.column.format.FormatStep;
import com.code.dataset.operator.column.indextype.IndexTypeStep;
import com.code.dataset.operator.column.numberformat.NumberFormatStep;
import com.code.dataset.operator.column.synccolumn.SyncColumnStep;
import com.code.dataset.operator.column.synccolumn.vo.LogicSyncColumn;
import com.code.dataset.operator.filter.FilterConditionStep;
import com.code.dataset.operator.join.JoinTableStep;
import com.code.dataset.operator.join.UnionTableStep;
import com.code.metadata.res.ddl.LogicDataColumn;
import com.code.metadata.res.ddl.LogicDataObj;
import com.code.metaservice.datawarehouse.IDataWarehouseService;
import com.code.metaservice.ddl.IDataSetStepService;
import com.code.metaservice.ddl.IDataSetSyncService;
import com.code.metaservice.ddl.ILogicDataColumnService;
import com.code.metaservice.ddl.ILogicDataObjService;
import com.code.metaservice.ddl.vo.LogicDataSetColumnVo;
import com.code.metaservice.ddl.vo.LogicHttpColumns;
import com.dragoninfo.dfw.annotation.FuncScanAnnotation;
import com.dragoninfo.dfw.annotation.ValidateAndLogAnnotation;
import com.dragoninfo.dfw.bean.Result;
import com.dragonsoft.cicada.datacenter.common.utils.UserContextUtil;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.qo.LogicDataExportQo;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.service.IDataSetEditService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.service.IDataSetOperationService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.util.RemoveOuterParenthesesUtil;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.vo.ElementUrlVO;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.vo.LogicDataSet.*;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.vo.StepVo;
import com.dragonsoft.cicada.datacenter.modules.metadata.service.MetadataService;
import com.google.common.collect.Maps;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/7/27
 */
@Controller
@CrossOrigin
@RequestMapping("/editDataSet")
@Api(value = "DataSetEditController|数据集编辑和新建控制器")
//多例
@Scope("prototype")
@Slf4j
public class DataSetEditController {

    @Autowired
    ILogicDataColumnService logicDataColumnService;
    @Autowired
    IDataSetEditService editService;
    @Autowired
    private IDataSetOperationService dataSetOperationService;
    @Autowired
    IDataSetSyncService dataSetSyncService;
    @Autowired
    IDataWarehouseService dataWarehouseService;
    @Autowired
    ILogicDataObjService logicDataObjService;

    @Autowired
    IDataSetStepService setStepService;

    @Autowired
    IDataSetStepService dataSetStepService;

    @Autowired
    BeanFactory beanFactory;

    @Autowired
    MetadataService metadataService;

    @ResponseBody
    @GetMapping("/createLogicDataSet")
    @ApiOperation(value = "3.6.1 快速分析：新建数据集")
    @FuncScanAnnotation(code = "dataSetOperationCreateLogicDataSet", name = "快速分析", parentCode = "dataSetOperation")
    public Result createLogicDataSet(@ApiParam(value = "目录树id") @RequestParam("id") String id,
                                     @ApiParam(value = "数据集名称") @RequestParam("name") String name,
                                     @ApiParam(value = "数据来源Id") @RequestParam("dsTypeId") String dsTypeId,
                                     HttpServletRequest request) {
        String userId = (String) request.getSession().getAttribute("userId");
        while (dataSetOperationService.isRepeatName(name, userId)) {
            name = name + "_1";
        }

        while (dataSetOperationService.checkDataSetName(name, id)) {
            name = name + "_1";
        }
        String dataSetId = editService.createLogicDataSet(id, dsTypeId, name, userId);
        Map<String, String> res = Maps.newHashMap();
        res.put("name", name);
        res.put("id", dataSetId);
        return Result.success(res);
    }


    @ResponseBody
    @PostMapping("/deleteJoinDataSetStep")
    @ApiOperation(value = "删除自助数据集join的数据集的操作步骤")
    public Result deleteJoinDataSetStep(@RequestBody List<String> stepRelationIds) {
        try {
            for (String stepRelationId : stepRelationIds) {
                dataSetStepService.deleteByRelation(stepRelationId);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return Result.success();
    }

    @ResponseBody
    @GetMapping("/createOldView")
    @ApiOperation(value = "创建丢失的视图")
    @ApiImplicitParam(name = "viewName", value = "视图名称", required = true, dataType = "String")
    public Result createOldView(String viewName) {
        try {
            editService.createOldView(viewName);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        return Result.success();
    }

    @ResponseBody
    @GetMapping("/replaceLogicDataObjSearchsql")
    @ApiOperation(value = "替换logicdataobj的search_sql")
    public Result replaceLogicDataObjSearchsql() {
        try {
            editService.replaceLogicDataObjSearchsql();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return Result.success();
    }

    @ResponseBody
    @GetMapping("/getErrorColumns")
    @ApiOperation(value = "获取错误的同步字段")
    public Result getErrorColumns(String logicId) {
        return Result.success(R.ok(editService.getErrorColumns(logicId)));
    }


    @ResponseBody
    @GetMapping("/getLogicDataColumn")
    @ApiOperation(value = "添加数据集到编辑面板：编辑时addDataSetId是null,快速分析添加数据集才有id")
    @FuncScanAnnotation(code = "dataSetOperationDataColumn", name = "编辑", parentCode = "dataSetOperation")
    @ValidateAndLogAnnotation
    public Result getLogicDataColumn(@ApiParam(value = "要添加的数据集Id") String addDataSetId, @ApiParam(value = "当前的数据集Id") String currentDataSetId) {

        if (StringUtils.isBlank(currentDataSetId)) {
            return Result.error("400", "请选择数据集");
        }
        LogicDataSetVo info = editService.getLogicDataInfo(addDataSetId, currentDataSetId);

        //快速分析-添加数据集
        if (StringUtils.isNotBlank(addDataSetId)) {
            if (info == null) {
                Assert.fail("添加的数据集无数据集信息！");
            }
            //快速分析保存数据集相关信息
            logicDataObjService.saveLogicDataObjRelation(currentDataSetId, Arrays.asList(addDataSetId));
            LogicDataObj currentObj = logicDataObjService.findLogicDataObjById(currentDataSetId);
            LogicDataObj addObj = logicDataObjService.findLogicDataObjById(addDataSetId);

            String globalCode = "v_%s_%s";
            globalCode = String.format(globalCode, addObj.getCode(), System.currentTimeMillis());
            globalCode = globalCode.replaceAll("\\.", "_");
            currentObj.setGlobalCode(globalCode);
            currentObj.setSql("select * from " + addObj.getGlobalCode());
            currentObj.setDbType(addObj.getDbType());
            currentObj.setOwner(addObj);
            logicDataObjService.updateLogicDataObj(currentObj);
            try {
                //editService.registerTable(currentObj, currentObj.getSql(), true);
                //判断父级logic是否有步骤sql,如果有，则当前这个数据集也要加
                editService.addLogicDataStepFromFather(currentDataSetId, addDataSetId);

                //将原本的创建视图的逻辑改为嵌套sql
                editService.createLogicDatObjSearchSql(currentObj.getId());
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        return Result.success(info);
    }


    @ResponseBody
    @GetMapping("/getSyncLogicDataColumn")
    @ApiOperation(value = "获取同步新增的字段")
//    @FuncScanAnnotation(code = "dataSetOperationGetLogicDataColumn", name = "编辑", parentCode = "dataSetOperation")
    public Result getSyncLogicDataColumn(String logicDataObjId) {

        if (StringUtils.isBlank(logicDataObjId)) {
            return Result.error("400", "数据集id不能为空！");
        }
        Map<String, List<LogicDataColumn>> res = dataSetSyncService.syncDataSet(logicDataObjId);

        List<ColumnDataSetVo> columnDataSetVos = editService.getSyncColumnVo(res);

        return Result.success(columnDataSetVos);
    }

    @ResponseBody
    @GetMapping("/getSyncLogicDataColumns")
    @ApiOperation(value = "3.0获取同增的字段")
//    @FuncScanAnnotation(code = "dataSetOperationGetLogicDataColumn", name = "编辑", parentCode = "dataSetOperation")
    public Result getSyncLogicDataColumns(String logicDataObjId) {
        return Result.success(metadataService.getSyncColumns(logicDataObjId));
//        columnVO.getAddColumnList().addAll(changeColumnVO.getAddColumnList());
//        columnVO.getDeleteColumnList().addAll(changeColumnVO.getDeleteColumnList());
//        columnVO.getUpdateColumnList().addAll(changeColumnVO.getUpdateColumnList());

    }

    @ResponseBody
    @PostMapping("/previewLogicDataSet")
    @ApiOperation(value = "预览")
    public Result previewLogicDataSet(@RequestBody PreviewVo previewVo, HttpSession session) {
        String userId = (String) session.getAttribute("userId");
        previewVo.setUserId(userId);
        PageInfo preview = null;
        try {
            if (StringUtils.isBlank(previewVo.getDataSetId())) {
                //走自定义sql查询
                preview = editService.previewBySql(previewVo);
            } else {
                preview = editService.preview(previewVo);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return Result.success(preview);
    }

    @ResponseBody
    @PostMapping("/selfHelpDataSetPreviewByStep")
    @ApiOperation(value = "预览")
    public Result previewLogicDataSet(@RequestBody SelfHelpDataSetPreviewByStepVo previewVo, HttpSession session) {
        String userId = (String) session.getAttribute("userId");
        previewVo.setUserId(userId);
        PageInfo preview = null;
        try {
            preview = editService.preview(previewVo);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return Result.success(preview);
    }

    @ResponseBody
    @PostMapping("/prviewDataWithColumn")
    @ApiOperation(value = "预览指定字段数据")
    public Result prviewDataWithColumn(@RequestBody PreviewVo previewVo, HttpSession session) {
        String userId = (String) session.getAttribute("userId");
        previewVo.setUserId(userId);
        if (StringUtils.isBlank(previewVo.getDataSetId())) {
            return Result.error("400", "添加数据对象未保存，或者数据对象不存在");
        }
        PageInfo preview = null;
        try {
            preview = editService.prviewDataWithColumn(previewVo);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            Assert.fail(e.getMessage());
        }
        return Result.success(preview);
    }


    @ResponseBody
    @PostMapping("/saveAs")
    @ApiOperation(value = "另存为操作")
    public Result saveAs(@RequestBody SaveDataSetVo saveDataSetVo, HttpServletRequest request) {
        String userId = (String) request.getSession().getAttribute("userId");
        if (dataSetOperationService.isRepeatName(saveDataSetVo.getName(), userId)) {
            return Result.error("400", "[" + saveDataSetVo.getName() + "]已存在，请重新输入！");
        }
        editService.saveAs(saveDataSetVo, userId);
        return Result.success();
    }

    @ResponseBody
    @PostMapping("/save")
    @ApiOperation(value = "保存操作")
    public Result save(@RequestBody SaveDataSetVo saveDataSetVo, HttpServletRequest request) {
        String userId = (String) request.getSession().getAttribute("userId");
        if (StringUtils.isBlank(saveDataSetVo.getName())) Assert.fail("数据集名称不能为空！");
        if (dataSetOperationService.isRepeatName(saveDataSetVo.getName(), userId)) {
            return Result.error("400", "[" + saveDataSetVo.getName() + "]已存在，请重新输入！");
        }
        editService.save(saveDataSetVo);
        return Result.success();
    }

    @ResponseBody
    @PostMapping("/editDataSetColumn")
    @ApiOperation(value = "操作步骤：字段编辑操作")
//    @FuncScanAnnotation(code = "dataSetOperationEditDataSetColumn", name = "极速表", parentCode = "dataSetOperation")
    public Result editDataSetColumn(@RequestBody EditColumnStep editColumnStep) {
        editService.editDataSetColumn(editColumnStep);
        return Result.success();
    }

    @ResponseBody
    @PostMapping("/addDataSetColumn")
    @ApiOperation(value = "操作步骤：字段新增操作")
    public Result addDataSetColumn(@RequestBody AddColumnStep addColumnStep) {
        LogicSyncColumn columnInfoVo = editService.addDataSetColumn(addColumnStep);
        return Result.success(columnInfoVo);
    }

    @ResponseBody
    @GetMapping("/getFunction")
    @ApiOperation(value = "获取函数")
    public Result getFunction(@ApiParam(value = "搜索名称") String condition) {
        return Result.success(editService.getFunction(condition));
    }

    @ResponseBody
    @GetMapping("/getLogicColumn")
    @ApiOperation(value = "查询字段")
    public Result getLogicColumn(@ApiParam(value = "数据集id") String id, @ApiParam(value = "搜索名称") String condition) {
        return Result.success(editService.getLogicColumn(id, condition));
    }

    @ResponseBody
    @PostMapping("/format")
    @ApiOperation(value = "操作步骤：数据格式转换")
    public Result format(@RequestBody FormatStep formatStep) {
        editService.format(formatStep);
        return Result.success();
    }

    @ResponseBody
    @PostMapping("/deleteColumn")
    @ApiOperation(value = "操作步骤：删除字段")
    public Result deleteColumn(@RequestBody DeleteColumnStep deleteColumnStep) {
        editService.deleteColumn(deleteColumnStep);
        return Result.success();
    }

    @ResponseBody
    @PostMapping("/indexType")
    @ApiOperation(value = "操作步骤：度量和维度转换")
    public Result indexType(@RequestBody IndexTypeStep indexTypeStep) {
        editService.indexType(indexTypeStep);
        return Result.success();
    }

    @ResponseBody
    @PostMapping("/numberFormat")
    @ApiOperation(value = "操作步骤：度量数字格式化")
    public Result numberFormat(@RequestBody NumberFormatStep numberFormatStep) {
        editService.numberFormat(numberFormatStep);
        return Result.success();
    }

    @ResponseBody
    @PostMapping("/filter")
    @ApiOperation(value = "操作步骤：设置过滤条件步骤")
    public Result filter(@RequestBody FilterConditionStep filterConditionStep) {
        editService.filter(filterConditionStep);
        return Result.success();
    }

    @ResponseBody
    @PostMapping("/syncColumn")
    @ApiOperation(value = "操作步骤：同步数据结构")
    public Result syncColumn(@RequestBody SyncColumnStep syncColumnStep) {

        editService.syncColumnStep(syncColumnStep);
        return Result.success();

    }

    @ResponseBody
    @PostMapping("/syncColumnDCThree")
    @ApiOperation(value = "3.0数据集同步操作步骤：同步数据结构(包含字段格式转换)")
    public Result syncColumnDCThree(@RequestBody SyncColumnStep syncColumnStep) {

        editService.syncColumnStepDCThree(syncColumnStep);
        return Result.success();

    }

    @ResponseBody
    @PostMapping("/syncColumnDataSet")
    @ApiOperation(value = "3.1数据集同步操作步骤：同步数据结构")
    public Result syncColumnDCThreeCopy(@RequestBody Map<String, List<LogicHttpColumns>> columns) {
        editService.syncColumns(columns);
        return Result.success();

    }

    @ResponseBody
    @GetMapping("/getLogicDataObjSQL")
    @ApiOperation(value = "获取数据集sql展示")
    public Result getLogicDataObjSQL(String dataSetId) {
        String logicSearchSQL = editService.getLogicSearchSQL(dataSetId);
        String sql = RemoveOuterParenthesesUtil.removeOuterParentheses(logicSearchSQL);
        return Result.success(sql);
    }


    @ResponseBody
    @PostMapping("/join")
    @ApiOperation(value = "操作步骤：左右关联")
    public Result join(@RequestBody JoinTableStep joinTableStep) {
        LogicDataSetVo join = editService.join(joinTableStep);
        return Result.success(join);
    }

    @ResponseBody
    @PostMapping("/editSelfHelpDataSet")
    @ApiOperation(value = "编辑自助数据集")
    public Result editSelfHelpDataSet(@RequestBody DataSetJoinVo dataSetJoinVo, HttpServletRequest request) {
        if (dataSetJoinVo.getJoinTableSteps() == null) {
            dataSetJoinVo.setJoinTableSteps(Collections.emptyList());
        }
        if (dataSetOperationService.checkDataSetNameByDataSetId(dataSetJoinVo.getDataSetName(), dataSetJoinVo.getDataSetId(),
                UserContextUtil.getUserIdByHttpRequest(request))) {
            return Result.error("400", "[" + dataSetJoinVo.getDataSetName() + "]已存在，请重新输入！");
        }
        editService.editSelfHelpDataSet(dataSetJoinVo);
        return Result.success();
    }

    @ResponseBody
    @GetMapping("/getJoinAndFilter")
    @ApiOperation(value = "左右关联和过滤条件回显信息")
    public Result getJoinAndFilter(@ApiParam(value = "type:join 左右关联,filter 过滤") String type, String dataSetId) {
        return Result.success(editService.getJoinAndFilter(type, dataSetId));
    }

    @ResponseBody
    @GetMapping("/getJoinAndFilters")
    @ApiOperation(value = "左右关联和过滤条件回显信息")
    public Result getJoinAndFilters(@ApiParam(value = "type:join 左右关联,filter 过滤") String type, String dataSetId) {
        return Result.success(editService.getJoinAndFilters(type, dataSetId));
    }

    @ResponseBody
    @GetMapping("/getAllStep")
    @ApiOperation(value = "所有步骤")
    public Result getAllStep(@ApiParam(value = "所有步骤") String type, String dataSetId) {
        return Result.success(editService.getAllStep(dataSetId));
    }

    @ResponseBody
    @PostMapping("/union")
    @ApiOperation(value = "上下合并")
    public Result union(@RequestBody UnionTableStep unionTableStep) {
        editService.union(unionTableStep);
        return Result.success();
    }

    @ResponseBody
    @GetMapping("/getDataSetStepRelation")
    @ApiOperation(value = "获取步骤")
    public Result getDataSetStepRelation(@ApiParam(value = "数据集id") String id) {
        List<DataSetStepRelationVo> relationVos = this.editService.getDataSetStepRelation(id);
        return Result.success(relationVos);
    }

    @ResponseBody
    @GetMapping("/getDataSetStepInfo")
    @ApiOperation(value = "获取步骤详细信息")
    public Result getDataSetStepInfo(@ApiParam(value = "步骤id") String id) {
        return Result.success(editService.getDataSetStepInfo(id));
    }

    @ResponseBody
    @PostMapping("/deleteDataStep")
    @ApiOperation(value = "步骤删除操作")
    public Result deleteDataStep(@ApiParam(value = "删除") String id, String dataSetId) {
        editService.deleteDataStep(id, dataSetId);
        return Result.success();
    }

    @ResponseBody
    @PostMapping("/editDataStep")
    @ApiOperation(value = "步骤编辑操作")
    public Result editDataStep(@RequestBody StepVo stepVo) {
        editService.editDataStep(stepVo);
        return Result.success();
    }


    @ResponseBody
    @GetMapping("/checkDataType")
    @ApiOperation(value = "数据类型校验")
    public Result checkDataType(String dT1, String dT2) {
        return Result.success(editService.checkDataType(dT1, dT2));
    }

    @ResponseBody
    @PostMapping("/editColumns")
    @ApiOperation(value = "编辑3.0数据集")
    public Result eidtColumns(@RequestBody Map<String, Object> dataMap, String dataSetId, String name, HttpServletRequest request) {
        LogicDataObj logicDataObj = logicDataObjService.findLogicDataObjById(dataSetId);
        //1.名字的编辑
        String userId = (String) request.getSession().getAttribute("userId");
        if (StringUtils.isBlank(name)) Assert.fail("数据集名称不能为空！");
        if (!name.equals(logicDataObj.getName())){
            if (dataSetOperationService.isRepeatName(name, userId)) {
                return Result.error("400", "[" + name + "]已存在，请重新输入！");
            }

            if (dataSetOperationService.checkDataSetNameByDataSetId(name, dataSetId, userId)) {
                return Result.error("400", "[" + name + "]已存在，请重新输入！");
            }
        }
        logicDataObj.setName(name);
        logicDataObj.setOperateTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        logicDataObjService.updateLogicDataObj(logicDataObj);
        //2.列的编辑
        List<Map<String, String>> column = (List<Map<String, String>>) dataMap.get("column");
        List<LogicDataSetColumnVo> collect = column.stream().map(
                c -> {
                    LogicDataSetColumnVo logicDataSetColumnVo = JSON.parseObject(JSON.toJSONString(c), LogicDataSetColumnVo.class);
                    if (StringUtils.isBlank(logicDataSetColumnVo.getName())) {
                        logicDataSetColumnVo.setName(null);
                    }
                    return logicDataSetColumnVo;
                }
        ).collect(Collectors.toList());
        logicDataColumnService.editColumn(collect, logicDataObj);

        return Result.success();
    }


    @ResponseBody
    @PostMapping("/logicDataExport")
    @ApiOperation(value = "模型预览结果下载")
    public void logicDataExport(@RequestBody LogicDataExportQo qo, HttpSession session, HttpServletResponse response) {
        String userId = (String) session.getAttribute("userId");
        qo.setUserId(userId);
        editService.logicDataExport(qo, response);
    }


    @ResponseBody
    @GetMapping("/getExportLimit")
    @ApiOperation(value = "获取下载文件最大条数限制")
    public Result<Integer> getExportLimit() {
        return editService.getExportLimit();
    }


    /**
     * 数据穿透url获取
     *
     * @param previewVo
     * @param session
     * @return
     */
    @ResponseBody
    @PostMapping("/loadElementColumn")
    @ApiOperation(value = "数据穿透url获取")
    public Result<Map<String, List<ElementUrlVO>>> loadElementColumn(@RequestBody PreviewVo previewVo, HttpSession session) {
        return Result.success(editService.getElementByPreview(previewVo));
    }


}

