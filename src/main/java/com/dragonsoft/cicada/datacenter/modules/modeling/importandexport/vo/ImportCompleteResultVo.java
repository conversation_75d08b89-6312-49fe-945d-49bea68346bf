package com.dragonsoft.cicada.datacenter.modules.modeling.importandexport.vo;

import lombok.Data;

import java.util.Map;

@Data
public class ImportCompleteResultVo {
    private String id;
    private String name;
    private String type;
    private String importState = SUCCESS;//导入后状态
    private String memo;//备注

    private static String SUCCESS = "成功";
    private static String FAIL = "失败";
    public static final String LOGIC_TYPE= "数据集";
    public static final String DW_INSTANCE_TYPE = "数据源";
    public static final String TRANS_TYPE = "模型方案";
    public static final String SERVICE_API_TYPE = "服务Api";
    public static final String USE_CASE_TYPE = "测试用例";
    public static ImportCompleteResultVo success(String id, String name, String type) {
        ImportCompleteResultVo importCompleteResultVo = new ImportCompleteResultVo();
        importCompleteResultVo.id = id;
        importCompleteResultVo.name = name;
        importCompleteResultVo.type = type;
        importCompleteResultVo.importState = SUCCESS;
        return importCompleteResultVo;
    }

    public static ImportCompleteResultVo success(Map<String, Object> mapObj, String type) {
        ImportCompleteResultVo importCompleteResultVo = new ImportCompleteResultVo();
        if(mapObj == null) return importCompleteResultVo;
        importCompleteResultVo.id = (String) mapObj.get("id");
        importCompleteResultVo.name = (String) mapObj.get("name");
        importCompleteResultVo.type = type;
        importCompleteResultVo.importState = SUCCESS;
        return importCompleteResultVo;
    }

    public static ImportCompleteResultVo fail(Map<String, Object> mapObj, String type, String memo) {
        ImportCompleteResultVo importCompleteResultVo = new ImportCompleteResultVo();
        if(mapObj == null) return importCompleteResultVo;
        importCompleteResultVo.id = (String) mapObj.get("id");
        importCompleteResultVo.name = (String) mapObj.get("name");
        importCompleteResultVo.type = type;
        importCompleteResultVo.memo = memo;
        importCompleteResultVo.importState = FAIL;
        return importCompleteResultVo;
    }

    public static ImportCompleteResultVo fail(String id, String name, String type, String memo) {
        ImportCompleteResultVo importCompleteResultVo = new ImportCompleteResultVo();
        importCompleteResultVo.id = id;
        importCompleteResultVo.name = name;
        importCompleteResultVo.type = type;
        importCompleteResultVo.memo = memo;
        importCompleteResultVo.importState = FAIL;
        return importCompleteResultVo;
    }

    //如果是单个插入，直接try获取相关信息

    //如果是批量插入
    //1.先全部插入
    //2.把当前得id去库里面查询，如果查不到，则是失败
    //3.查询到了，就是成功的
}
