package com.dragonsoft.cicada.datacenter.modules.dataplan.externaldatasources.service;

import com.code.metaservice.base.vo.ClassifierPartitionVo;
import com.code.metaservice.base.vo.PartitionInfoVo;
import com.code.metaservice.base.vo.PartitionRangeVo;

import java.util.List;
import java.util.Map;

public interface DatasetPartitionService {

    /*获取数据集分区信息*/
    List<ClassifierPartitionVo> queryPartitionList(String statId, String keyword);

    /*删除数据集分区*/
    Map<String, String> deleteDatasetPartitionById(String id);

    /*保存或更新数据集分区信息*/
    Map<String, String> saveOrUpdatePartition(PartitionInfoVo partition);

    /*获取分区范围*/
    PartitionInfoVo queryPartitionRange(String partitionId);

    /*删除分区范围*/
    void deletePartitionRange(String partitionId, List<PartitionRangeVo> range);

    List<String> findColumnInfomationByClassifierId(String id);

    List<String> findColumnInfomationByPartitionId(String id);

    /*更新分区是否执行*/
    Map<String, String> updateExecutePartition(String id, String classifierId, String str);

}
