package com.dragonsoft.cicada.datacenter.modules.datavisual.function;

import com.dragonsoft.cicada.datacenter.modules.datavisual.function.functions.*;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020-12-30 14:49
 */
public class FunctionManager {

    private final Map<String, String> functionList = new HashMap<>();
    private final Map<String, MemoryFunction> functions = new HashMap<>();

    {
        init();
    }

    public void init() {
        this.addFunction(new YearOnYearFunction());
        this.addFunction(new YearOnYearProportionFunction());
        this.addFunction(new ChainComparisonFunction());
        this.addFunction(new ChainComparisonProportionFunction());
    }

    public void addFunction(MemoryFunction function) {
        this.functionList.put(function.getName(), function.getCode());
        this.functions.put(function.getCode(), function);
    }

    public MemoryFunction getFunction(String functionName) {
        MemoryFunction function = this.functions.get(functionName);
        this.functions.get(functionName);
        return function;
    }


    public MemoryFunction getFunction(String...functionNames) {
        CompositeFunction cf = new CompositeFunction();
        for (String name : functionNames) {
            cf.addFunction(this.getFunction(name));
        }
        return cf;
    }

    public Map<String, String> getFunctionList() {
        return functionList;
    }
}
