package com.dragonsoft.cicada.datacenter.modules.supermarkmodel.service;

import com.code.common.paging.PageInfo;
import com.code.metadata.modelsupermark.ResourcesApply;
import com.dragoninfo.dfw.bean.Result;
import com.dragonsoft.cicada.datacenter.modules.supermarkmodel.vo.ResourcesApplyAuditVo;
import com.dragonsoft.cicada.datacenter.modules.supermarkmodel.vo.ResourcesQueryVo;

public interface IResourcesService {

    void addResourcesApply(ResourcesApply resourcesApply);

    PageInfo getListByType(ResourcesQueryVo vo);

    Result auditDetail(ResourcesQueryVo vo);

    void audit(ResourcesApplyAuditVo vo);

    void auditResultConfirm(String id);

}
