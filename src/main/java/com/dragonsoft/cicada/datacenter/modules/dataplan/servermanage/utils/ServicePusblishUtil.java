package com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.utils;

import com.code.cicadas.datacenter.model.BatchParamVo;
import com.code.common.utils.assertion.Assert;
import com.dragonsoft.cicada.datacenter.common.utils.ConvertToPinyinUtils;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.Blob;
import java.sql.Time;
import java.sql.Timestamp;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ServicePusblishUtil {

    public static final Map<String, String> dataTypeMap = new HashMap<>();

    static {
        dataTypeMap.put("Object", Object.class.getName());
        dataTypeMap.put("String", String.class.getName());
        dataTypeMap.put("STRING", String.class.getName());
        dataTypeMap.put("LONG", Long.class.getName());
        dataTypeMap.put("Long", Long.class.getName());
        dataTypeMap.put("FLOAT", Float.class.getName());
        dataTypeMap.put("Float", Float.class.getName());
        dataTypeMap.put("Double", Double.class.getName());
        dataTypeMap.put("DOUBLE", Double.class.getName());

        dataTypeMap.put("INTEGER", Integer.class.getName());
        dataTypeMap.put("Integer", Integer.class.getName());

        dataTypeMap.put("List", List.class.getName());
        dataTypeMap.put("Map", Map.class.getName());

        dataTypeMap.put("Time", Time.class.getName());
        dataTypeMap.put("Timestamp", Timestamp.class.getName());
        dataTypeMap.put("Boolean", Boolean.class.getName());
        dataTypeMap.put("Short", Short.class.getName());
        dataTypeMap.put("Blob", Blob.class.getName());
        dataTypeMap.put("Text", String.class.getName());
        dataTypeMap.put("Date", Date.class.getName());
        dataTypeMap.put("BigDecimal", BigDecimal.class.getName());
        dataTypeMap.put("Byte", Byte.class.getName());
        dataTypeMap.put("BigInteger", BigInteger.class.getName());
        dataTypeMap.put("BatchParamVo", BatchParamVo.class.getName());
    }

    public static String buildImplClassName(String chineseName) {
        return ConvertToPinyinUtils.convertChineseName(chineseName);
    }

    public static String getOldImplClassName(String implEnglishName){
        String str = implEnglishName.substring(implEnglishName.lastIndexOf("_")-1);
        return str;
    }

    /**
     * 转换成输出的类型全路径
     *
     * @param type 类型
     * @return 算法组说目前只会有double 和 String 所以先已这两个适配...
     */
    public static String changeTypePath(String type) {
        if (dataTypeMap.get(type) == null) {
            Assert.fail("出参类型无法对应标准");
        }
        return dataTypeMap.get(type);
    }

    public static String buildFilepath(String basePath, String interfaceName, int imlVersion) {
        return basePath + (basePath.endsWith("/") ? "" : "/")  + interfaceName.toLowerCase() + "/" + imlVersion;
    }
}
