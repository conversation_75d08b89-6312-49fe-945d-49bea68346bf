package com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.util;

/**
 * <AUTHOR>
 * @Date: 2021/07/26/下午3:22
 */
public class RemoveOuterParenthesesUtil {

    /**
     *  去掉字符串最外层的（） 例如 （select * from (select * from A)）改成 select * from (select * from A)
     * @param S
     * @return
     */
    public static String removeOuterParentheses(String S) {
        StringBuilder sb = new StringBuilder();
        int num = 0;
        int index = 0;

        for(int i = 0;i < S.length();i++){
            if(S.charAt(i) == '('){
                num++;
            }
            if(S.charAt(i) == ')'){
                num--;
            }
            if(num == 1 && S.charAt(i) == '('){
                index = i;
            }
            if(num == 0&&S.charAt(i) ==')'){
                sb.append(S.substring(index + 1,i));
            }
        }
        return sb.toString();
    }

}
