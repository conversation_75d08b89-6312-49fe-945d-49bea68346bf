INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state", "is_doc_show") VALUES ('dataBusinessDirCicadaSpecialBusinessMeta', '0', '特征离线计算', 'dataBusinessDir', NULL, NULL, NULL, '2022-08-01 14:21:03', NULL, '1', '1');

INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('c889d1b5ee985dd5ac85cd0f53165224', 'd6121bc4248e45019942e2cb78362500', 'dataBusinessDirCicadaSpecialBusinessMeta', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
