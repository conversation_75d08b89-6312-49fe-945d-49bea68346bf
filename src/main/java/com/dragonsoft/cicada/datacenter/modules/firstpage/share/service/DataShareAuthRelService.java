package com.dragonsoft.cicada.datacenter.modules.firstpage.share.service;

import com.code.common.paging.PageInfo;
import com.dragoninfo.dfw.entity.TSysAuthUser;
import com.dragonsoft.cicada.datacenter.modules.firstpage.share.vo.ShareVo;
import com.fw.service.IService;

import java.util.List;

public interface DataShareAuthRelService extends IService {

    PageInfo getSharesFromOthers(int pageNum, int pageSize, String userId, String resourceName, String resourceType,List<String> fromUserIds);

    List<TSysAuthUser> getOtherUsers(String myUserId,String objType);

    PageInfo getMySharesByPage(ShareVo shareVo, String myUserId);

    List<String> getDataAuthByUserId(String userId);

    List<String> getDataFuncIdByUserId(String userId);


}
