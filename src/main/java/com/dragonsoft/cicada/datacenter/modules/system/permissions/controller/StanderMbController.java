package com.dragonsoft.cicada.datacenter.modules.system.permissions.controller;

import com.code.common.paging.PageInfo;
import com.code.common.utils.R;
import com.code.common.utils.StringUtils;
import com.code.metaservice.datawarehouse.IDataWareTreeService;
import com.code.metaservice.datawarehouse.model.DatasetTreeModel;
import com.code.metaservice.lab.cache.CodeMapperCache;
import com.code.metaservice.standmb.IStandMbService;
import com.code.metaservice.standmb.vo.VisualMbTreeVo;
import com.dragoninfo.dfw.bean.Result;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.service.IStanderMbCodeService;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.service.MbService;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.text.Collator;
import java.util.Comparator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;

@CrossOrigin
@RestController
@RequestMapping("/standermb")
@Api(value = "StanderMbController|可视化码表操作")
public class StanderMbController {
    @Autowired
    MbService mbService;
    @Autowired
    IStandMbService standMbService;
    @Autowired
    private CodeMapperCache codeMapperCache;
    @Autowired
    private IDataWareTreeService dataWareTreeService;
    @Autowired
    private IStanderMbCodeService mbCodeService;


    @GetMapping(value = "/queryDirTree")
    public Result queryDirTree(String id, String pId, String keyword) {
        if (StringUtils.isNotBlank(id)) {
            return Result.toResult( R.ok());
        }
        List<DatasetTreeModel> tree = dataWareTreeService.getDataWarehouseTreeCopy(keyword);
        tree = tree.stream().sorted(Comparator.comparing(DatasetTreeModel::getName, Collator.getInstance(Locale.CHINA))).collect(Collectors.toList());
        return Result.toResult(R.ok(tree));
    }

    @PostMapping("/list")
    public Result searchStanderMbList(@RequestBody SearchListRequest request) {
        PageInfo pageInfo = mbService.getPageInfo(request);
        return Result.toResult(R.ok(pageInfo));
    }


    @GetMapping("/allMbEnumTreeList")
    public Result getStanderMbList() {
        List<VisualMbTreeVo> vos = mbService.getVisualMbAllList();
        return Result.toResult(R.ok(vos));
    }

    @GetMapping("/allMbCodeEnumTreeList")
    public Result getStanderMbCodeList(String id) {
        List<MbEnumTreeVo> vos = mbService.getCodesById(id);
        return Result.toResult(R.ok(vos));
    }

    @GetMapping("/getStanderMbCodeTreeList")
    public Result getStanderMbCodeTreeList(String id) {
        List<MbEnumTreeVo> vos = mbCodeService.getTreeById(id);
        return Result.toResult(R.ok(vos));
    }

    /**
     * 分局-派出所-警务区 联动
     * @param map
     * @return
     */
    @ApiOperation(value = "分局-派出所-警务区联动")
    @PostMapping("/linkagePoliceStation")
    @ResponseBody
    public Result linkagePoliceStation(@RequestBody Map map) {
        String id = map.get("id").toString();
        String level=map.get("level").toString();
        List<MbEnumTreeVo> vos = mbCodeService.linkagePoliceStation(id,level);
        return Result.toResult(R.ok(vos));
    }

    @ApiOperation(value = "在派出所填上所属上级分局的ownerid")
    @PostMapping("/dealPoliceStationOwnerId")
    @ResponseBody
    public Result dealPoliceStationOwnerId() {
        mbCodeService.dealPoliceStationOwnerId();
        return Result.toResult(R.ok());
    }

    @GetMapping("/getSourceList")
    public Result getDataSource() {
        return Result.toResult(R.ok(mbService.getDataSource()));
    }

    @GetMapping("/getDataSetList")
    public Result getDataSet(String id) {
        return Result.toResult(R.ok(mbService.getDataSet(id)));
    }

    @GetMapping("/getFiledList")
    public Result getFiled(String id) {
        List<FieldVo> fieldVos = mbService.getFiled(id);
        return Result.toResult(R.ok(fieldVos));
    }

    @PostMapping("/selectCodeValList")
    public Result selectCodeValList(@RequestBody SelectCodeListRequest request) {
        List<CodeValListVo> codeValListVos = mbService.selectCodeValList(request);
        return Result.toResult(R.ok(codeValListVos));
    }

    @PostMapping("/saveAndUpdate")
    public Result saveAndUpdate(@RequestBody SaveAndUpdateMbVo vo) {
        boolean checkCn = standMbService.checkCnName(vo.getId(), vo.getName());
        boolean checkEn = standMbService.checkEnName(vo.getId(), vo.getCode());
        if (checkCn) {
            return Result.toResult(R.error("中文名称重复"));
        }
        if (checkEn) {
            return Result.toResult(R.error("英文名称重复"));
        }
        String id = mbService.saveAndUpdateMb(vo);
        return Result.toResult(R.ok(id));
    }

    @PostMapping("/delete")
    public Result delete(String id) {
        mbService.deleteById(id);
        return Result.toResult(R.ok());
    }

    @GetMapping("/details")
    public Result getMbDetails(String id) {
        MbDetailsVo detailsVo = mbService.getMbDetails(id);
        return Result.toResult(R.ok(detailsVo));
    }
    @GetMapping("/flushCache")
    public Result flushCache(){
        codeMapperCache.reload();
        return Result.toResult(R.ok());
    }



}
