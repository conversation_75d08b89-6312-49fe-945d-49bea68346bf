package com.dragonsoft.cicada.datacenter.modules.modeling.importandexport.util;

import com.code.common.utils.StringUtils;
import com.code.common.utils.assertion.Assert;
import com.dragonsoft.cicada.datacenter.common.token.GlobalConstant;
import com.dragonsoft.cicada.datacenter.modules.modeling.importandexport.vo.ImportCompleteResultVo;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class DataExportUtil {

    public static String ID = "id";

    public static List<ImportCompleteResultVo> importCompleteSuccessResultVoList = Lists.newArrayList();

    public static List<ImportCompleteResultVo> importCompleteFailResultVoList = Lists.newArrayList();


    public static List<Map<String,Object>> checkPluginRelation = Lists.newArrayList();

    public static String buildImportSql(String tableName, Map<String, Object> param, String key, String priKeyValue) {
        if (isDeleteCatalogResources) {
            return insertSQL(tableName, param, key, priKeyValue);
        } else {
            return mergeSql(tableName, param, key, priKeyValue);
        }
    }

    public static String insertClassifyElementSQL(String tableName, Map<String, Object> param) {
        String sql = "INSERT INTO %s (%s) select %s where not exists (select * from " + tableName + " where element_id = '" + param.get("element_id") + "' and  busi_classify_id  ='" + param.get("busi_classify_id") + "');";

        return String.format(sql, tableName, String.join(",", param.keySet()), String.join(",", getParamList(param)));
    }

    public static String insertClassifyElementSQL(String tableName, String classify_id, String element_id) {
        Map<String, Object> param = new HashMap<>();
        param.put("element_id", element_id);
        param.put("busi_classify_id", classify_id);
        return insertClassifyElementSQL(tableName, param);
    }


    public static String insertSQL(String tableName, Map<String, Object> param, String key, String priKeyValue) {
        String sql = "INSERT INTO %s (%s) VALUES (%s);";
        if (StringUtils.isNotBlank(key) && StringUtils.isNotBlank(priKeyValue)) {
            sql = "INSERT INTO %s (%s) select %s where not exists (select * from " + tableName + " where " + key + " = '" + priKeyValue + "');";
        }
        return String.format(sql, tableName, String.join(",", param.keySet()), String.join(",", getParamList(param)));
    }


    public static String mergeSql(String tableName, Map<String, Object> param, String priKey, String priKeyValue) {
        if (StringUtils.isBlank(priKeyValue))
            return insertSQL(tableName, param, priKey, priKeyValue);
        Assert.notNull(priKey, "插入或更新方式必须要主键或者是区分唯一的标识");
        //c插入的值
        String valueList = String.join(",", getParamList(param));

        //想要插入的key
        String keyList = String.join(",", param.keySet());
        if (StringUtils.isBlank(priKeyValue)) {
            priKeyValue = (String) param.get(priKey);
        }
        List<String> keyAndValue = Lists.newArrayList();
        //想要更新的数据
        for (Map.Entry<String, Object> mapEntry : param.entrySet()) {
            if (mapEntry.getValue() != null) {
                String val = mapEntry.getKey() + "=" + "'" + mapEntry.getValue() + "'";
                keyAndValue.add(val);
            }
        }

        //条件值
        String conditionStr = priKey + " = '" + priKeyValue + "' ";

        String sql = "update %s set  %s where %s ;\n" +
                "INSERT INTO %s (%s) select %s where not exists (select 1 from  %s where  %s );";
        String mergeSql = String.format(sql, tableName, String.join(",", keyAndValue), conditionStr,
                tableName, keyList, valueList, tableName, conditionStr);

        return mergeSql;
    }

    private static List<String> getParamList(Map<String, Object> param) {
        List<String> list = new ArrayList<>();
        for (String s : param.keySet()) {
            Object val = param.get(s);
            String value = val != null ? "'" + val.toString() + "'" : "NULL";
            if (s.equals("relation_type")) value = value.trim();
            list.add(value);
        }
        return list;
    }

    public static void addPluginMetaData(List<Map<String, Object>> dataObj, Object pluginId, List<Map<String, Object>> resultData) {
        if (CollectionUtils.isNotEmpty(dataObj)) {
            dataObj.stream().forEach(map -> {
                List<Map<String, Object>> o = (List<Map<String, Object>>) map.get(pluginId);
                if (null != o) resultData.addAll(o);
            });
        }
    }

    public static void addElementUserIds(List<Map<String, Object>> listMaps) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(listMaps)) return;
        for (Map<String, Object> map : listMaps) {
            addElementUserId(map);
        }
    }

    public static void addElementUserId(Map<String, Object> map) {
        String userId = (String) map.get("id");
        addElementUserId(userId);
    }

    public static void addElementUserId(String userId) {
        if (!elementUserIds.contains(userId)) {
            elementUserIds.add(userId);
        }
    }

    public static void addStepUserIds(List<Map<String, Object>> listMaps) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(listMaps)) return;
        for (Map<String, Object> map : listMaps) {
            addStepUserId(map);
        }
    }

    public static void addStepUserId(Map<String, Object> map) {
        String userId = (String) map.get("id");
        addStepUserId(userId);
    }

    public static void addStepUserId(String userId) {
        if (!stepUserIds.contains(userId)) {
            stepUserIds.add(userId);
        }
    }


    public static Map<String,String> classifyElementMap = new HashMap<>();//我的空间目录树，导出与导入之间的关系
    //需要修改的t_md_element 表信息
    public static List<String> elementUserIds = Lists.newArrayList();

    //需要修改的T_DATASET_STEP 表信息
    public static List<String> stepUserIds = Lists.newArrayList();

    /**
     * #模型导入否删除再导入,默认不删除
     * #true表示删除从新建
     * #false表示不删除，做merge
     */
    public static boolean isDeleteCatalogResources = false;


    public static void init(String isDelete) {
        init();
        if (StringUtils.isNotBlank(isDelete))
            isDeleteCatalogResources = Boolean.parseBoolean(isDelete);
    }

    public static void init() {
        elementUserIds = Lists.newArrayList();
        stepUserIds = Lists.newArrayList();
        classifyElementMap = new HashMap<>();
    }

    public static String getCollectQuotesStr(List<String> list) {
        if(CollectionUtils.isEmpty(list)) return "''";
        return String.join(",", getCollectQuotesList(list));
    }

    public static List<String> getCollectQuotesList(List<String> list) {
        List<String> resultList = Lists.newArrayList();
        for (String id : list) {
            String result = "'" + id + "'";
            resultList.add(result);
        }
        return resultList;
    }

    public static void addMapList(Map<String, List<String>> listMap, String key, String value) {
        if (listMap.get(key) != null) {
            List<String> strings = listMap.get(key);
            if (!strings.contains(value)) {
                listMap.get(key).add(value);
            }
        } else {
            List<String> values = Lists.newArrayList();
            values.add(value);
            listMap.put(key, values);
        }
    }


}
