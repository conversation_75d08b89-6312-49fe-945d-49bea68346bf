--修改服务管理至服务空间下
update "public"."t_sys_func" set "parent_func_code" = 'serviceSpace' where func_code = 'serviceManagement';

--数据空间 批量添加数据集
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state") VALUES ('dataSetOperationAddBatchLogicObj', '0', '批量添加', 'dataSetOperation', NULL, NULL, NULL, '2020-08-19 18:45:57.516', NULL, '1');
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('69c44add14da4e53b82294622a92996a', 'd6121bc4248e45019942e2cb78362500', 'dataSetOperationAddBatchLogicObj', NULL, NULL, NULL, NULL, NULL, NULL, NULL);

--服务空间导入导出
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('serviceSpaceImportAndExport', '0', '导入导出', 'serviceSpace', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1', '1');
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('802cb982f2204b8f9a35f7bab3804e58', 'd6121bc4248e45019942e2cb78362500', 'serviceSpaceImportAndExport', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('serviceSpaceImport', '0', '导入', 'serviceSpaceImportAndExport', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1', '1');
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('78c1af62e75042848af81790e26a0c7e', 'd6121bc4248e45019942e2cb78362500', 'serviceSpaceImport', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('serviceSpaceExport', '0', '导出', 'serviceSpaceImportAndExport', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1', '1');
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('22f71f254e5d4de6886537f2a73b29f0', 'd6121bc4248e45019942e2cb78362500', 'serviceSpaceExport', NULL, NULL, NULL, NULL, NULL, NULL, NULL);

--建模空间导入导出
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('modelSpaceImportAndExport', '0', '导入导出', 'dataModeling', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1', '1');
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('e8affa516d9c42f59d79ad610ba2d4bf', 'd6121bc4248e45019942e2cb78362500', 'modelSpaceImportAndExport', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('modelSpaceImport', '0', '导入', 'modelSpaceImportAndExport', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1', '1');
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('551d6a2b63e84c2aac5f01ed6c1426cd', 'd6121bc4248e45019942e2cb78362500', 'modelSpaceImport', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state","is_doc_show") VALUES ('modelSpaceExport', '0', '导出', 'modelSpaceImportAndExport', NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, '1', '1');
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('d0ee2b4eaa7e4be5bb2b93f573a8c00f', 'd6121bc4248e45019942e2cb78362500', 'modelSpaceExport', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
