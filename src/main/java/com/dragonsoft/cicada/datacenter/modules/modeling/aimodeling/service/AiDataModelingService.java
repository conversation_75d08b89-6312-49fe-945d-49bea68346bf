package com.dragonsoft.cicada.datacenter.modules.modeling.aimodeling.service;

import com.code.common.paging.PageInfo;
import com.dragonsoft.cicada.datacenter.modules.modeling.aimodeling.vo.*;
import com.fw.service.IService;

import java.util.List;
import java.util.Map;

/**
 * ai建模
 */

public interface AiDataModelingService extends IService {


    /**
     * 获取整个ai建模目录
     * @param userId
     * @param listType
     * @return
     */
    List<ModelTreeResult> getAllAiModelTree(String userId, String listType);

    /**
     * 获取目录加孩子
     * @param userId
     * @param listType
     * @return
     */
    List<ModelTreeResult> getAllAiModelWithChildren(String userId, String listType);
    /**
     * 添加目录
     * @param parentClassifyId
     * @param classifyName
     * @param dirType
     * @param userId
     */
    String addAiModelDir(String parentClassifyId,String classifyName,String dirType,String userId);

    /**
     * 重命名
     * @param classifyId
     * @param newClassifyName
     * @param userId
     */
    void updateAiModelDirName(String classifyId,String newClassifyName,String userId);

    /**
     * 移动
     * @param currentClassifyId
     * @param newParentClassifyId
     */
    void moveAiModelDir(String currentClassifyId,String newParentClassifyId);

    /**
     * 删除
     * @param classifyId
     */
    void deleteAiModelDir(String classifyId);

    /**
     * 新建模型 返回脚本id
     * @param aiModelVo
     * @return
     */
    String newAiModel(AiModelVo aiModelVo);

    /**
     * 获得脚本编辑页面的url
     * @param scriptId
     * @return
     */
    String getStartAiModelUrl(String scriptId);

    /**
     * 保存模型
     * @param scriptId
     */
    void saveScript(String scriptId);

    /**
     * 开始训练  返回logId
     * @param scriptId
     * @return
     */
    String runScript(String scriptId);

    /**
     * 获取训练结果
     * @param logId
     * @return
     */
    List<EvaluateRstItem> getEvaluateRst(String logId);

    /**
     * 训练历史
     * @param aiHistoryQueryVo
     * @return
     */
    PageInfo getAiModelRunHistory(AiHistoryQueryVo aiHistoryQueryVo);

    /**
     * 删除日志
     * @param logId
     */
    void deleteLogById(String logId);

    /**
     * 查看日志详情
     * @param logId
     * @return
     */
    List<LogDetailResult> getLogDetailByLogId(String logId);

    /**
     * 查询ai建模列表
     * @param aiModelPageVo
     * @return
     */
    PageInfo getAiModelPage(AiModelPageVo aiModelPageVo,String userId);

    /**
     * 获取训练数据集
     * @param logId
     * @return
     */
    EntInputInfo getEvaRstDataSet(String logId);


    /**
     * 重命名
     * @param scriptId
     * @param newModelName
     */
    void renameAiModel(String scriptId,String newModelName);


    /**
     * 另存为
     * @param scriptId
     * @param classifyId
     */
    void copyAiModel(String scriptId,String classifyId,String name);

    /**
     * 移动模型
     * @param scriptId
     * @param classifyId
     */
    void moveAiModel(String scriptId,String classifyId);

    /**
     * 删除模型
     * @param scriptId
     */
    void deleteAiModel(String scriptId);


    /**
     * 编辑模型
     * @param basicVo
     */
    void editAiModel(AiModelBasicVo basicVo);


    void updateAiLog(String logId,String scriptId);


    Map<String,Object> getAiModelDetail(String scriptId);

    /**
     * 调度作业启动，包括其子孩子调度
     * @param scriptId
     */
    void starTaskForSchedul(String scriptId);


    /**
     * 保存定时任务
     * @param
     */
    void saveScheduledTask(TransScheduleVo transScheduleVo);

    /**
     * 获取已创建的作业树
     * @param
     */
    List getJobCreated();


    Map getSchedulePlan(String transId);

    List getSubTrans(String transId);


    String getNewestLogIdByScriptId(String scriptId);

}
