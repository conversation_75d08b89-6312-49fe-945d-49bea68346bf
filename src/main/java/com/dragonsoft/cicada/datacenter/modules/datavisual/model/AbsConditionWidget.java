package com.dragonsoft.cicada.datacenter.modules.datavisual.model;

import com.code.meta.dml.standard.IMultCdin;
import com.code.meta.dml.standard.cdins.QueryCdins;
import com.code.metadata.datavisual.Field;


public abstract class AbsConditionWidget  {

    Field field;
    String label;
    String type;


    abstract IMultCdin builderCondition(QueryCdins queryCdins);

    public Field getField() {
        return field;
    }

    public void setField(Field field) {
        this.field = field;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}
