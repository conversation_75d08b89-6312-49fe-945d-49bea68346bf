package com.dragonsoft.cicada.datacenter.modules.dataplan.externaldatasources.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.code.common.encrypt.DragonEncryptor;
import com.code.common.paging.PageInfo;
import com.code.common.spark.SparkConsumer;
import com.code.common.spark.service.ISparkClientService;
import com.code.common.utils.StringUtils;
import com.code.common.utils.assertion.Assert;
import com.code.metadata.base.softwaredeployment.DeployedSoftware;
import com.code.metadata.business.directory.*;
import com.code.metadata.datawarehouse.DwDbInstance;
import com.code.metadata.datawarehouse.DwLayer;
import com.code.metadata.model.core.ModelElement;
import com.code.metadata.res.common.ClassifierStat;
import com.code.metadata.res.ddl.LogicDataObj;
import com.code.metadata.res.semistructured.fulltext.elasticsearch.ElasticSearchIndex;
import com.code.metadata.res.semistructured.fulltext.elasticsearch.ElasticSearchInstance;
import com.code.metadata.res.semistructured.hbase.HbaseHtable;
import com.code.metadata.res.structured.rdb.RdbCatalog;
import com.code.metadata.res.structured.rdb.RdbSchema;
import com.code.metaservice.base.softwaredeployment.DeployedSoftwareService;
import com.code.metaservice.business.directory.BusiClassifyService;
import com.code.metaservice.business.directory.BusiDirService;
import com.code.metaservice.datawarehouse.IDataWareTreeService;
import com.code.metaservice.datawarehouse.model.DatasetTableModel;
import com.code.metaservice.datawarehouse.model.DatasetTreeModel;
import com.code.metaservice.ddl.ILogicDataObjService;
import com.code.metaservice.ftp.IFtpInstanceService;
import com.code.metaservice.res.app.zk.IZkInstanceService;
import com.code.metaservice.res.semistructured.fulltext.FullTextElasticSearchService;
import com.code.metaservice.res.semistructured.hbase.HbaseInstanceService;
import com.code.metaservice.res.semistructured.kafka.IKafkaInstanceService;
import com.code.metaservice.res.semistructured.redis.IRedisInstanceService;
import com.code.metaservice.res.spark.GlobalCodeService;
import com.code.metaservice.res.structured.rdb.IRdbCatalogService;
import com.code.metaservice.res.structured.rdb.IRdbDataObjService;
import com.code.metaservice.res.structured.rdb.IRdbSchemaService;
import com.dragoninfo.dfw.bean.Result;
import com.dragoninfo.dfw.entity.TSysAuthObjFunc;
import com.dragoninfo.dfw.entity.TSysAuthObjRel;
import com.dragoninfo.dfw.entity.TSysFuncBase;
import com.dragoninfo.dfw.service.SysAuthObjFuncService;
import com.dragoninfo.dfw.service.SysAuthObjRelService;
import com.dragoninfo.dfw.service.SysAuthObjService;
import com.dragoninfo.dfw.service.SysFuncService;
import com.dragonsoft.cicada.datacenter.common.token.GlobalConstant;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.common.UserConstant;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.service.IDataSetEditService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.service.IDataSetOperationService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.externaldatasources.service.IDataWarehousePlanService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.externaldatasources.vo.datawarehouse.*;
import com.dragonsoft.cicada.datacenter.modules.modeling.qo.AnalysisResultLibraryQO;
import com.dragonsoft.cicada.datacenter.modules.modeling.vo.AnalysisResultLibraryVO;
import com.fw.service.BaseService;
import com.fw.service.annotation.Service;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019/3/15
 */
@Service
@Slf4j
public class DataWarehousePlanServiceImpl extends BaseService implements IDataWarehousePlanService {

    private static final String[] NUMBER = {"INT2", "INT4", "INT8", "INTEGER", "INT", "FLOAT4"};

    private static final String[] TIMETYPE = {"TIMESTAMP", "DATE", "DATETIME", "TIME", "INTERVAL", "DATATYPE"};

    private static final String HBASE_INSTANCE = "HbaseInstance";
    private static final String KAFKA_INSTANCE = "KafkaInstance";

    //    @Value("${spark.dubbo.address}")
    protected String dubboUrl;
    /**
     * 虚拟的SQL模型树的根节点ID
     * 在前台界面使用该ID作为根节点的ID，在数据库中没有根节点，一级节点的父节点ID为null
     */
    private static String VIRTUAL_SQL_TREE_ROOT_ID = "-1";

    @Autowired
    private IDataSetEditService dataSetEditService;
    @Autowired
    private BusiClassifyService busiClassifyService;

    @Autowired
    private ILogicDataObjService logicDataObjService;
    @Autowired
    private GlobalCodeService globalCodeService;

    @Autowired
    private BusiDirService busiDirService;

    @Autowired
    private DeployedSoftwareService deployedSoftwareService;

    @Autowired
    private IRdbCatalogService iRdbCatalogService;

    @Autowired
    private HbaseInstanceService hbaseInstanceService;

    @Autowired
    private IRedisInstanceService redisInstanceService;

    @Autowired
    private IKafkaInstanceService kafkaInstanceService;

    @Autowired
    private BaseService baseService;

    @Autowired
    private IRdbDataObjService iRdbDataObjService;

    @Autowired
    private FullTextElasticSearchService fullTextElasticSearchService;

    @Autowired
    private IRdbSchemaService rdbSchemaService;

    @Autowired
    IFtpInstanceService ftpInstanceService;

    @Autowired
    IZkInstanceService zkInstanceService;

    @Autowired
    private IDataSetOperationService dataSetOperationService;

    @Autowired
    private SysAuthObjService sysAuthObjService;

    @Autowired
    private SysFuncService sysFuncService;

    @Autowired
    private SysAuthObjFuncService sysAuthObjFuncService;

    @Override
    public void deleteTable(String tableId, String dbType, String judgeType) {
        String deleteSQL = "select id , type ,owner_id from t_md_classifier_stat where id = (select classifier_stat_id from t_dw_table_mapping where id = :tableId)";

        Map<String, String> dataInfo = this.baseDao.sqlQueryForMap(deleteSQL, addParam("tableId", tableId).param());

        List statList = new ArrayList();

        String schemaId = dataInfo.get("owner_id");
        if (dataInfo.size() != 0) {
            String statSql = "select * from t_dw_table_mapping where classifier_stat_id = :statId ";
            statList = this.baseDao.sqlQueryForList(statSql, addParam("statId", dataInfo.get("id")).param());
        }
        if (!Objects.equals(judgeType, "大数据管家")) {
            if ("RdbDataObj".equals(dataInfo.get("type"))) {
                iRdbDataObjService.deleteRdbDataObj(dataInfo.get("id"));
            } else if ("ElasticSearchIndex".equals(dataInfo.get("type")) && statList.size() == 1) {
                ElasticSearchIndex e = (ElasticSearchIndex) this.baseDao.get(ElasticSearchIndex.class, dataInfo.get("id"));
                fullTextElasticSearchService.deleteIndex(e);
            } else if ("FileDataObj".equals(dataInfo.get("type"))) {
//            fileDataSetService.deleteFileDataset(dataInfo.get("id"));
            } else if ("HbaseHtable".equals(dataInfo.get("type"))) {
                HbaseHtable h = (HbaseHtable) this.baseDao.get(HbaseHtable.class, dataInfo.get("id"));
                this.baseDao.delete(h);
            }
        }
        String sql = "delete from t_dw_table_mapping where id = '" + tableId + "'";
        this.baseDao.executeSqlUpdate(sql);

        //删除数据集
        if (StringUtils.isNotBlank(dataInfo.get("id"))) {
            List<LogicDataObj> logicDataObjs = logicDataObjService.getLogicDataObjsByOwnerId(dataInfo.get("id"));

            List<LogicDataObj> logicIds = recursiveGetLogicData(logicDataObjs);
            for (LogicDataObj logic : logicIds) {
                dataSetOperationService.deleteDataSet(logic.getId());
            }
        }

    }

    private List<LogicDataObj> recursiveGetLogicData(List<LogicDataObj> logicDataObjs) {
        List<LogicDataObj> logicIds = Lists.newArrayList();
        for (LogicDataObj logicDataObj : logicDataObjs) {
            logicIds.add(logicDataObj);
            List<LogicDataObj> child = logicDataObjService.getLogicDataObjsByOwnerId(logicDataObj.getId());
            if (child.size() <= 0) continue;
            List<LogicDataObj> list = recursiveGetLogicData(child);
            logicIds.addAll(list);
        }
        return logicIds;
    }

    /**
     * 添加数据仓库
     *
     * @param elementId
     * @param classifyId
     * @return
     */
    @Override
    public String addTreeNode(String elementId, String classifyId) {

        String sql = "INSERT INTO t_md_classify_element VALUES ('" + elementId + "','" + classifyId + "')";
        this.baseDao.executeSqlUpdate(sql);
        return "";
    }

    @Override
    public List<ElasticColumnVo> queryElasticsColumns(String objId, String dbType) {
        String sql = "";
        if ("elasticsearch".equals(dbType.toLowerCase())) {
            sql = "SELECT" +
                    " es.id as id," +
                    " es.type as dbtype ," +
                    " es.code AS code," +
                    " es.name AS NAME," +
                    " el.code AS TYPE," +
                    "es.memo as memo " +
                    " FROM" +
                    " t_md_elasticsearch_column es" +
                    " LEFT JOIN t_md_element el ON es.data_type_id = el. ID" +
                    " WHERE" +
                    " es.owner_Id = :objId";
        } else if ("hbase".equals(dbType.toLowerCase())) {
            sql = "SELECT" +
                    " hb.id as id," +
                    " hb.type as dbtype ," +
                    " hb.code AS code," +
                    " hb.name AS NAME," +
                    " el.code AS TYPE," +
                    "hb.memo as memo " +
                    " FROM" +
                    " t_md_hbase_column_family hb" +
                    " LEFT JOIN t_md_element el ON hb.data_type_id = el. ID" +
                    " WHERE" +
                    " hb.owner_Id = :objId";
        } else {
            sql = "select " +
                    " f.type as dbtype ," +
                    " f.id as id, " +
                    " f.name as NAME, " +
                    " f.code as code, " +
                    " el.code as TYPE," +
                    "f.memo as memo " +
                    "from " +
                    " t_md_structural_feature f " +
                    "left join t_md_element el on " +
                    " f.data_type_id = el.id " +
                    "where " +
                    " f.owner_id = :objId";
        }

        List<Map<String, Object>> esData = this.baseDao.sqlQueryForList(sql, addParam("objId", objId).param());


        List<ElasticColumnVo> list = new ArrayList<ElasticColumnVo>();
        for (Map<String, Object> esCol : esData) {
            String jsType = this.getJsType(String.valueOf(esCol.get("type")));
            ElasticColumnVo vo = new ElasticColumnVo();
            vo.setJsType(jsType);
            vo.setId(String.valueOf(esCol.get("id")));
            if (dbType.toLowerCase().equals("filedataobj")
                    && StringUtils.isNotBlank((String) esCol.get("name"))) {
                vo.setCode((String) esCol.get("name"));
            } else {
                vo.setCode(String.valueOf(esCol.get("code")));
            }
            vo.setName(String.valueOf(esCol.get("name")));
            vo.setDataType(String.valueOf(esCol.get("type")));
            vo.setMemo(String.valueOf(esCol.get("memo")));
            vo.setDbType("ElasticSearchColumn".equals(String.valueOf(esCol.get("dbtype"))) ? "elasticsearch" : String.valueOf(esCol.get("dbtype")));
            list.add(vo);
        }

        return list;
    }

    private String getJsType(String val) {
        if (Arrays.asList(NUMBER).contains(val.toUpperCase())) {
            return "number";
        } else if (Arrays.asList(TIMETYPE).contains(val.toUpperCase())) {
            return "time";
        } else {
            return "string";
        }
    }

    @Override
    public String addDataWarehouseInstance(DataWarehouseVo vo, String classifyId, String dirId, String userId) {
        DwDbInstance dwDB = new DwDbInstance();
        try {
            if (StringUtils.isNotBlank(vo.getId())) {
                dwDB.setId(vo.getId());
            } else {
                dwDB.setId(null);
            }
            String hql = "from DwLayer where classifierStatId='" + classifyId + "'";
            DwLayer owner = (DwLayer) this.baseDao.queryForObject(hql);

            dwDB.setName(vo.getDataSourceName());
            dwDB.setCode(vo.getDataSourceName());
            dwDB.setOwner(owner);
            dwDB.setDbType(vo.getDbType());
            dwDB.setDirId(dirId);
            dwDB.setDbInstanceType(vo.getDomainDbType());
            dwDB.setOperateUserId(userId);
            dwDB.setSource("数据建模中心");
            String[] dbType = {"zk", "zookeeper", "elasticsearch", "hbase"};
            if (Arrays.asList(dbType).contains(vo.getDomainDbType().toLowerCase())) {
                dwDB.setSoftwareId(vo.getSoftwareId()); // /t_md_dw_db_instance 的 deployed_software_id存的是 DeployedSoftware 的子类
            } else {
                RdbSchema schema = rdbSchemaService.get(vo.getOtherElementId());
                String softWareId = schema.getOwnerId();
                dwDB.setSoftwareId(softWareId);
            }
            this.baseDao.saveOrUpdate(dwDB);
        } catch (Exception e) {
         log.error(e.getMessage(),e);
        }
        return dwDB.getId();
    }

    @Override
    public String addDataWarehouseInstance(DataWarehouseVo vo, String ownerId, String dirId, String userId, String source) {
        DwDbInstance dwDB = new DwDbInstance();
        try {
            if (StringUtils.isNotBlank(vo.getId())) {
                dwDB.setId(vo.getId());
            } else {
                dwDB.setId(null);
            }
            String hql = "from DwLayer where classifierStatId='" + ownerId + "'";
            DwLayer owner = (DwLayer) this.baseDao.queryForObject(hql);

            dwDB.setName(vo.getDataSourceName());
            dwDB.setCode(vo.getDataSourceName());
            dwDB.setOwner(owner);
            dwDB.setDbType(vo.getDbType());
            dwDB.setDirId(dirId);
            dwDB.setDbInstanceType(vo.getDomainDbType());
            dwDB.setOperateUserId(userId);
            dwDB.setSource(Objects.equals("大数据管家", source) ? source : "数据建模中心");
            String[] dbType = {"zk", "zookeeper", "elasticsearch", "hbase", "redis", "kafka"};
            if (Arrays.asList(dbType).contains(vo.getDomainDbType().toLowerCase())) {
                dwDB.setSoftwareId(vo.getSoftwareId()); // /t_md_dw_db_instance 的 deployed_software_id存的是 DeployedSoftware 的子类
            } else {
                RdbSchema schema = rdbSchemaService.get(vo.getOtherElementId());
                String softWareId = schema.getOwnerId();
                dwDB.setSoftwareId(softWareId);
            }
            this.baseDao.saveOrUpdate(dwDB);
        } catch (Exception e) {
         log.error(e.getMessage(),e);
            throw new RuntimeException(e);
        }
        return dwDB.getId();
    }

    @Override
    public void addDWRelationDb(DataWarehouseVo res, String id) {
        List<Object[]> paramList = new ArrayList<>();
        if ("Elasticsearch".equals(res.getDomainDbType())) {
            paramList.add(new Object[]{id, res.getSoftwareId()});
        } else {
            paramList.add(new Object[]{id, res.getOtherElementId()});
        }
        batchInsertDWRelationDb(paramList);

    }

    @Override
    public boolean deleteDataWarehouseDBInstance(String elementId, String classifyId) {
        boolean deletePhysics = false;
        // 1物理1逻辑：全部删除
        // 1物理n逻辑：删除当前逻辑
        if (!multiLogicDataSource(elementId)) {
            deleteDataSet(elementId);
            deletePhysics = true;
        }
        String deleteClassifyElementSQL = "DELETE FROM T_MD_CLASSIFY_ELEMENT a WHERE a.element_id = '" + elementId + "'";
        this.baseDao.executeSqlUpdate(deleteClassifyElementSQL);

        String deleteDWDBMappingSQL = "DELETE FROM T_DW_DB_MAPPING WHERE DW_DB_INSTANCE_ID='" + elementId + "'";
        this.baseDao.executeSqlUpdate(deleteDWDBMappingSQL);

//        //删除与标准表的关联中间表：datacenter少一张表
        String deleteStandTableMapSQL = " DELETE FROM t_dw_table_mapping WHERE DW_DB_ID='" + elementId + "'";
        this.baseDao.executeSqlUpdate(deleteStandTableMapSQL);

        String deleteDWDBInstanceSQL = " DELETE FROM T_MD_DW_DB_INSTANCE WHERE ID='" + elementId + "'";
        this.baseDao.executeSqlUpdate(deleteDWDBInstanceSQL);


        //删除此表跟角色和用户关系
        Map<String, String> functionRelationParams = Maps.newHashMap();
        functionRelationParams.put("func_code", elementId);
        List<TSysAuthObjFunc> tSysAuthObjFuncs = sysAuthObjFuncService.queryList(functionRelationParams);
        for (TSysAuthObjFunc tSysAuthObjFunc : tSysAuthObjFuncs) {
            sysAuthObjService.deleteAuthObj(tSysAuthObjFunc);
        }

        //删除注册到功能表的数据对象
        Map<String, String> functionParams = Maps.newHashMap();
        functionParams.put("func_code", elementId);
        TSysFuncBase tSysFuncBase = (TSysFuncBase) sysFuncService.query(functionParams);
        if (null != tSysFuncBase) {
            sysAuthObjService.deleteAuthObj(tSysFuncBase);
        }
        return deletePhysics;
    }

    private boolean multiLogicDataSource(String elementId) {
        String sql = "select a.deployed_software_id as instance_id, b.deployed_software as schema_id from t_md_dw_db_instance a, t_dw_db_mapping b where a.id = :dw_id and b.dw_db_instance_id = :dw_id";
        Map<String, String> map = this.baseDao.sqlQueryForMap(sql, addParam("dw_id", elementId).param());

        String checkSql = "select b.deployed_software as schema_id from t_md_dw_db_instance a, t_dw_db_mapping b where a.deployed_software_id = :instance_id and a.id != :dw_id and a.id = b.dw_db_instance_id";
        List<Map<String, String>> list = this.baseDao.sqlQueryForList(checkSql, addParam("instance_id", map.get("instance_id")).addParam("dw_id", elementId).param());
        if (list == null || list.size() == 0) return false;
        return list.stream()
                .map(sc -> sc.get("schema_id"))
                .anyMatch(s -> s.equals(map.get("schema_id")));
    }

    @Override
    public JSONObject getDWDBInstanceForLook(String dwDbId) {
        JSONObject jsonObject = new JSONObject();

        DataWarehouseVo vo = lookDataWarehouseVo(dwDbId);
        String sql = "select name,code,type from t_md_element where id = :elementId";
        Map<String, String> param = new HashMap<>();
        param.put("elementId", vo.getElementId());
        Map<String, String> elementMap = this.baseDao.sqlQueryForMap(sql, param);
        jsonObject.put("instance", elementMap);

        jsonObject.put("data", vo);
        return jsonObject;
    }

    private DataWarehouseVo lookDataWarehouseVo(String dwDbId) {
        if (org.apache.commons.lang.StringUtils.isEmpty(dwDbId)) {
            return null;
        }
        DataWarehouseVo vo = new DataWarehouseVo();
        DwDbInstance dwDbInstance = (DwDbInstance) this.baseDao.get(DwDbInstance.class, dwDbId);
        String queryRelationDBSQL = "select DEPLOYED_SOFTWARE from T_DW_DB_MAPPING where DW_DB_INSTANCE_ID=:dwDbId";
        Map<String, Object> params = new HashMap<>();
        params.put("dwDbId", dwDbId);
        List<Map<String, String>> dbidList = this.baseDao.sqlQueryForList(queryRelationDBSQL, params);
        vo.setId(dwDbId);
        vo.setName(dwDbInstance.getName());
        vo.setDomainDbType(dwDbInstance.getDbInstanceType());
        vo.setDbType(dwDbInstance.getDbType());

        for (Map<String, String> db : dbidList) {
            String id = db.get("deployed_software");
            vo.setElementId(id);
        }
        return vo;
    }

    @Override
    public void editDWRelationDb(DataWarehouseVo res) {

        String deleteDomainRelationSQL = "delete from T_DW_DB_MAPPING where DW_DB_INSTANCE_ID='" + res.getId() + "'";
        this.baseDao.executeSqlUpdate(deleteDomainRelationSQL);
        List<Object[]> paramList = new ArrayList<>();

        if (StringUtils.isNotBlank(res.getHbaseElementId())) {
            paramList.add(new Object[]{res.getId(), res.getHbaseElementId()});
        }
        if (StringUtils.isNotBlank(res.getEsElementId())) {
            paramList.add(new Object[]{res.getId(), res.getEsElementId()});
        }

        batchInsertDWRelationDb(paramList);

    }

    private void batchInsertDWRelationDb(List<Object[]> paramList) {

        // Object //T_DW_DB_MAPPING get  DW_DB_INSTANCE_ID DEPLOYED_SOFTWARE
        String insertSql = "insert into T_DW_DB_MAPPING(DW_DB_INSTANCE_ID, DEPLOYED_SOFTWARE) values(?, ?)";

        this.baseDao.batchInsert(insertSql, paramList);
    }

    @Override
    public boolean checkDWIsExist(String name, String dirId) {
        String hql = "\tfrom DwDbInstance\t" +
                "\twhere\t" +
                "\tname =:name\t" +
                "\tand dir_id = :dirId\t";

        DwDbInstance es = (DwDbInstance) this.baseDao.queryForObject(hql, addParam("name", name).
                addParam("dirId", dirId).param());

        return es != null;
    }

    /**
     * 保存子数据仓库节点
     *
     * @param parentId
     * @param nodeName
     * @param nodeCode
     * @return
     */
    @Override
    public Result addClassifyTreeNode(String parentId, String nodeName, String nodeCode) {
        if (StringUtils.isBlank(parentId)) {
            return Result.error("400", "父节点ID不能为空!");
        }
        if (StringUtils.isBlank(nodeName)) {
            return Result.error("400", "新增节点的中文名称不能为空!");
        }

        String sql = "select id from t_md_busi_classify where busi_dir_id =:parentId and name=:nodeName ";
        List<String> list = this.baseDao.sqlQueryForList(sql, addParam("parentId", parentId).addParam("nodeName", nodeName).param());
        if (!CollectionUtils.isEmpty(list)) {
            return Result.error("400", "该节点的子节点已存在相同节点中文名称或英文名称!");
        }

        BaseBusiDir baseBusiDir = (BaseBusiDir) this.baseDao.get(BaseBusiDir.class, parentId);
        BaseBusiClassify busiClassify = new BaseBusiClassify();
        busiClassify.setBusiDir(baseBusiDir);
        busiClassify.setName(nodeName);
        busiClassify.setCode(nodeCode);
        //保存了二级目录，类似“数据仓库”底下的“天津数据仓库”
        busiClassifyService.saveBaseBusiClassify(busiClassify);
        return Result.success(busiClassify.getId());
    }

    @Override
    public Result addClassifyTreeNode(String parentId, String nodeName, String nodeCode, String userId) {
        if (StringUtils.isBlank(parentId)) {
            return Result.error("400", "父节点ID不能为空!");
        }
        if (StringUtils.isBlank(nodeName)) {
            return Result.error("400", "新增节点的中文名称不能为空!");
        }

        String sql = "select id from t_md_busi_classify where busi_dir_id =:parentId and name=:nodeName ";
        List<String> list = this.baseDao.sqlQueryForList(sql, addParam("parentId", parentId).addParam("nodeName", nodeName).param());
        if (!CollectionUtils.isEmpty(list)) {
            return Result.error("400", "该节点的子节点已存在相同节点中文名称或英文名称!");
        }

        BaseBusiDir baseBusiDir = (BaseBusiDir) this.baseDao.get(BaseBusiDir.class, parentId);
        BaseBusiClassify busiClassify = new BaseBusiClassify();
        busiClassify.setBusiDir(baseBusiDir);
        busiClassify.setName(nodeName);
        busiClassify.setCode(nodeCode);
        busiClassify.setOperateUserId(userId);
        //保存了二级目录，类似“数据仓库”底下的“天津数据仓库”
        busiClassifyService.saveBaseBusiClassify(busiClassify);
        return Result.success(busiClassify.getId());
    }


    private void deleteDataSet(String elementId) {
        //删除管家注册
        String catalogSQL = "select deployed_software_id from t_md_dw_db_instance where id = :elementId";
        String catalogId = this.baseDao.sqlQueryForValue(catalogSQL, addParam("elementId", elementId).param());
        String catalogTypeSql = "select type from t_md_element where id= :catalogId";
        String catalogType = this.baseDao.sqlQueryForValue(catalogTypeSql, addParam("catalogId", catalogId).param());

        if ("RdbCatalog".equals(catalogType)) {
            //删除rdb
            iRdbCatalogService.deleteRdbCatalog(catalogId);
        } else if ("ElasticSearchInstance".equals(catalogType)) {
            //删除es
            this.baseDao.delete(ElasticSearchInstance.class, new String[]{catalogId});
        } else if ("FtpInstance".equals(catalogType)) {
            ftpInstanceService.deleteById(catalogId);
        } else if ("ZkInstance".equals(catalogType)) {
            zkInstanceService.deleteById(catalogId);
        } else if ("HbaseInstance".equals(catalogType)) {
            //删除hbase
            hbaseInstanceService.deleteById(catalogId);
        } else if ("RedisInstance".equals(catalogType)) {
            //删除hbase
            redisInstanceService.deleteById(catalogId);
        } else if ("KafkaInstance".equals(catalogType)) {
            //删除hbase
            kafkaInstanceService.deleteById(catalogId);
        } else {
            Assert.fail("未找到对应的数据源！");
        }
    }

    /**
     * 删除数仓目录树的子仓库或子层次
     *
     * @param classifyId
     */
    @Override
    public void deleteDwInstance(String classifyId) {
        //删除T_DW_DB_MAPPING表
        String deleteDWDBMappingSQL = "delete from t_dw_db_mapping where dw_db_instance_id in  " +
                "(select id from t_md_dw_db_instance where owner_id in  " +
                "     (select id from t_md_dw_layer where classifier_stat_id in  " +
                "          (select id from t_md_busi_classify where owner_id='" + classifyId + "')))";
        this.baseDao.executeSqlUpdate(deleteDWDBMappingSQL);

        //删除T_MD_DW_DB_INSTANCE表
        String deleteDWDBInstanceSQL = "delete from t_md_dw_db_instance where owner_id in  " +
                "     (select id from t_md_dw_layer where classifier_stat_id in  " +
                "          (select id from t_md_busi_classify where owner_id='" + classifyId + "'))";
        this.baseDao.executeSqlUpdate(deleteDWDBInstanceSQL);

        //删除classify类别的数据，先删除t_md_classify_element
        String deleteClassifyEleSQL = "delete from t_md_classify_element where busi_classify_id in  " +
                "(select id from t_md_busi_classify where owner_id='" + classifyId + "')";
        this.baseDao.executeSqlUpdate(deleteClassifyEleSQL);
        //删除子数据仓库
        String deleteClassifySQL = "delete from t_md_busi_classify where id='" + classifyId + "'";
        this.baseDao.executeSqlUpdate(deleteClassifySQL);

    }

    @Override
    public String renameDWInstance(String clasifyId, String classifyName, String type, String id, String dirId) {
        Assert.notNull(clasifyId);
        Assert.hasText(classifyName, "输入不能为空");
        BaseBusiClassify bc = (BaseBusiClassify) this.baseDao.get(BaseBusiClassify.class, clasifyId);

        String sql = "select id from t_md_busi_classify where busi_dir_id =:parentId and name=:nodeName ";
        List<String> list = this.baseDao.sqlQueryForList(sql, addParam("parentId", dirId).addParam("nodeName", classifyName).param());
        if (!CollectionUtils.isEmpty(list)) {
//            Assert.fail("目录【" + classifyName + "】已存在");
            Assert.fail("该节点的子节点已存在相同节点中文名称或英文名称!");
        }
        /*if (!isExistDirName("", classifyName, dirId, type)) {
            return "目录【" + classifyName + "】已存在";
        }*/

        bc.setName(classifyName);
        this.baseDao.update(bc);

        return "success";
    }

    /**
     * 判断数据仓库名称是否重复
     *
     * @param clasifyId
     * @param classifyName
     * @return
     */
    public boolean isExistDirName(String clasifyId, String classifyName, String dirId, String type) {

        StringBuffer sql = new StringBuffer();
        sql.append("select id from t_md_busi_classify where name =:classifyName and ");
        if ("dwLayer".equals(type)) {
            sql.append(" owner_id = :dirId");
        } else {
            sql.append(" busi_dir_id= :dirId and owner_id is null");
        }

        return this.baseDao.sqlQueryForValue(sql.toString(), addParam("classifyName", classifyName).addParam("dirId", dirId).param()) == null;
    }


    @Override
    public boolean isDbInstanceExist(String clasifyId) {
        boolean flag = false;
        //此层次下是否有数据源
        String querySQL = "select id from t_md_dw_db_instance where id in(select element_id from t_md_classify_element where busi_classify_id in( select id from t_md_busi_classify where parent_classify_id =:classifyId or id =:classifyId))";
        List<String> list = this.baseDao.sqlQueryForList(querySQL, addParam("classifyId", clasifyId).param());
        if (list.size() > 0) {
            flag = true;
        }

        //此层次下是否有子层次
        String sql = "select id from t_md_busi_classify where parent_classify_id =:classifyId";
        List<String> sqlList = this.baseDao.sqlQueryForList(sql, addParam("classifyId", clasifyId).param());
        if (sqlList.size() > 0) {
            flag = true;
        }

        return flag;
    }

    @Override
    public Result saveDWLevel(String parentId, String nodeName, String nodeCode) {
        if (StringUtils.isBlank(parentId)) {
            return Result.error("404", "父节点ID不能为空!");
        }
        if (StringUtils.isBlank(nodeName)) {
            return Result.error("404", "新增节点的中文名称不能为空!");
        }
        BaseBusiClassify parentBc = null;
        if (!VIRTUAL_SQL_TREE_ROOT_ID.equals(parentId)) {
            parentBc = busiClassifyService.findBaseBusiClassifyBy(parentId);
            Assert.notNull(parentBc, "父节点不存在！");
        }
        List<BaseBusiClassify> busiClassifies;

        busiClassifies = new ArrayList<>(parentBc.getBusiClassifies());
        Iterator<BaseBusiClassify> it = busiClassifies.iterator();
        BaseBusiClassify sonClassify;
        boolean flag = false;
        while (it.hasNext()) {
            sonClassify = it.next();
            if (sonClassify.getName().equals(nodeName)) {
                flag = true;
                break;
            }
        }
        if (flag) {
            return Result.error("404", "该节点的子节点已存在相同节点中文名称或英文名称!");
        }

        BaseBusiDir baseBusiDir = parentBc.getBusiDir();
        if (baseBusiDir == null) {
            baseBusiDir = busiDirService.findBaseBusiDirBy(EnumBusiType.DATA_WAREHOUSE_DIR);
        }
        BaseBusiClassify odsBusiClassify = new BaseBusiClassify();
        odsBusiClassify.setBusiDir(baseBusiDir);
        odsBusiClassify.setParentBc(parentBc);
        odsBusiClassify.setName(nodeName);
        odsBusiClassify.setOwner(parentBc);
        odsBusiClassify.setCode(nodeCode);
        busiClassifyService.saveBaseBusiClassify(odsBusiClassify);

        return Result.success(odsBusiClassify.getId());
    }

    @Override
    public DatasetTreeModel saveBusi(String busiId, String dbType, String dbId, String catalogName, String selectId, String selectName, int level, String customName) {

        String checkSql = "select * from t_md_dw_busi_instance where db_id=:id and dir_id =:busiId";
        Map<String, String> checkResultMap = this.baseDao.sqlQueryForMap(checkSql, addParam("id", selectId).addParam("busiId", busiId).param());
        if (!checkResultMap.isEmpty()) {
            return null;
        }

        DatasetTreeModel resNode = new DatasetTreeModel();
        String id = UUID.randomUUID().toString().replace("-", "");
        String operateTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        String getSoftwareIdSql = "select owner_id from t_md_element where id=:dbId";
        String softwareId = this.baseDao.sqlQueryForValue(getSoftwareIdSql, addParam("dbId", dbId).param());

        String dbTypeId = getIdBySoftwareId(softwareId);

        List<Object[]> paramList = new ArrayList<>();
        //业务库还没有添加这种类型的库
        if (dbTypeId == null) {
            dbTypeId = UUID.randomUUID().toString().replace("-", "");
            // id, name, type, code, parent_id, db_id, software_id, operate_time
            // 保存根节点（例如 Elasticsearch，Oracle等这种）
            paramList.add(new Object[]{dbTypeId, dbType, "DW_BUSI_BASE_DB", dbType, busiId, null, softwareId, operateTime, busiId});
            saveDwBusi(paramList);
            //封装目录树的二级节点
            resNode.setId(dbTypeId);
            resNode.setIsParent(true);
            resNode.setCode(dbType);
            resNode.setExtendedType("DW_BUSI_BASE_DB");
            resNode.setLevel(1);
            resNode.setLabel(dbType);
            resNode.setName(dbType);
            resNode.setpId(busiId);
        }

        // 不是rdb的库,库类型下个节点直接就是库实例
        if (level == 1) {

            paramList = new ArrayList<>();
            String sId = UUID.randomUUID().toString().replace("-", "");
            // id, name, type, code, parent_id, db_id, software_id, operate_time
            paramList.add(new Object[]{sId, selectName, "DW_BUSI_LEAF_NODE", selectName, dbTypeId, selectId, softwareId, operateTime, busiId});
            saveDwBusi(paramList);
//            封装目录树三级节点
            DatasetTreeModel d3 = new DatasetTreeModel();
            d3.setId(selectId);
            d3.setIsParent(false);
            d3.setCode(selectName);
            d3.setExtendedType("DW_BUSI_LEAF_NODE");
            d3.setLevel(2);
            d3.setLabel(selectName);
            d3.setName(selectName);
            d3.setpId(dbTypeId);
            List<DatasetTreeModel> l3 = Lists.newArrayList();
            l3.add(d3);
            resNode.setChildren(l3);
        }

        // rdb的库, 库类型下个节点是数据库，再下级是数据库schema
        if (level == 2) {
            // 保存子节点
            paramList = new ArrayList<>();
            String uuid = UUID.randomUUID().toString().replace("-", "");
            // id, name, type, code, parent_id, db_id, software_id, operate_time
            paramList.add(new Object[]{uuid, catalogName, "DW_BUSI_RDB", catalogName, dbTypeId, null, softwareId, operateTime, busiId});
            saveDwBusi(paramList);

            //            封装目录树三级节点
            DatasetTreeModel d3 = new DatasetTreeModel();
            d3.setId(uuid);
            d3.setIsParent(true);
            d3.setCode(catalogName);
            d3.setExtendedType("DW_BUSI_RDB");
            d3.setLevel(2);
            d3.setLabel(catalogName);
            d3.setName(catalogName);
            d3.setpId(dbTypeId);


            // 保存叶子节点
            paramList = new ArrayList<>();
            String nodeName = customName + "(" + selectName + ")";
            String sId = UUID.randomUUID().toString().replace("-", "");
            paramList.add(new Object[]{sId, nodeName, "DW_BUSI_LEAF_NODE", selectName, uuid, selectId, softwareId, operateTime, busiId});
            saveDwBusi(paramList);

            //            封装目录树四级节点
            DatasetTreeModel d4 = new DatasetTreeModel();
            d4.setId(selectId);
            d4.setIsParent(false);
            d4.setCode(nodeName);
            d4.setExtendedType("DW_BUSI_LEAF_NODE");
            d4.setLevel(3);
            d4.setLabel(nodeName);
            d4.setName(nodeName);
            d4.setpId(uuid);
            List<DatasetTreeModel> l4 = Lists.newArrayList();
            l4.add(d4);
            d3.setChildren(l4);
            List<DatasetTreeModel> l3 = Lists.newArrayList();
            l3.add(d3);
            resNode.setChildren(l3);

        }
        return resNode;

    }

    /**
     * 获取业务库某种类型的id
     *
     * @param softwareId softwareId
     * @return id
     */
    private String getIdBySoftwareId(String softwareId) {
        if (softwareId == null) {
            return null;
        }
        String sql = "select id from t_md_dw_busi_instance where software_id=:softwareId and parent_id is null";
        return this.baseDao.sqlQueryForValue(sql, addParam("softwareId", softwareId).param());
    }

    @Override
    public void deleteBusi(String dwBusiId, String[] parentIds, String dirId) {

        String sql = "select id from t_md_dw_busi_instance where db_id = :dwBusiId and dir_id =:dirId";
        String busiId = this.baseDao.sqlQueryForValue(sql, addParam("dwBusiId", dwBusiId).addParam("dirId", dirId).param());

        String parentIdCountSql = "select parent_id  from t_md_dw_busi_instance where parent_id is not null";
        List<Map<String, String>> parentIdList = this.baseDao.sqlQueryForList(parentIdCountSql);
        Map<String, Long> parentIdCountMap = parentIdList.stream().collect(Collectors.groupingBy(p -> p.get("parent_id"), Collectors.counting()));

        List<String> deleteIdList = new ArrayList<>();
        deleteIdList.add(busiId);

        for (String parentId : parentIds) {
            long count = parentIdCountMap.get(parentId);
            if (count == 1) {
                deleteIdList.add(parentId);
            }
        }

        String deleteDwBusiSQL = "DELETE FROM t_md_dw_busi_instance WHERE id in (:parentIds)";
        this.baseDao.executeSqlUpdate(deleteDwBusiSQL, addParam("parentIds", deleteIdList).param());
    }

    /**
     * 保存t_md_dw_busi_instance表
     *
     * @param paramList 插入数据
     */
    private void saveDwBusi(List<Object[]> paramList) {
        String sql = "INSERT INTO t_md_dw_busi_instance (id, name, type, code, parent_id, db_id, software_id, operate_time,dir_id) VALUES(?, ?, ? , ?, ? , ? , ?, ?,?)";

        this.baseDao.batchInsert(sql, paramList);
    }

    @Override
    public List<Map<String, Object>> queryAddTable(String dataBaseId, String content) {

        String dbTypeSQL = "SELECT db_instance_type from t_md_dw_db_instance where id = '" + dataBaseId + "'";
        String dbType = this.baseDao.sqlQueryForValue(dbTypeSQL);
        String sql = "";
        Map<String, Object> params = new HashMap<>();
        if (dbType.toUpperCase().contains("ELASTICSEARCH")) {
            sql = "select " +
                    "     c.type as \"tType\", " +
                    "     c.id as \"tId\", " +
                    "     c.code as \"tCode\", " +
                    "     c.name as \"tName\", " +
                    "     a.code as \"iCode\", " +
                    "     a.name as \"iName\" " +
                    "from " +
                    "     t_md_classifier_stat c " +
                    "right join( " +
                    "          select " +
                    "               id, " +
                    "               code, " +
                    "               name " +
                    "          from " +
                    "               t_md_elasticsearch_index " +
                    "          where " +
                    "               owner_id in( " +
                    "                    select " +
                    "                         deployed_software " +
                    "                    from " +
                    "                         t_dw_db_mapping " +
                    "                    where " +
                    "                         dw_db_instance_id = :dataBaseId " +
                    "               ) " +
                    "     ) a on " +
                    "     c.type = 'ElasticSearchIndex' " +
                    "     and c.id = a.id " +
                    "where " +
                    "     c.code is not null ";
            params.put("dataBaseId", dataBaseId);
            if (!"".equals(content)) {
                sql += "and (upper(c.code) like :content or upper(c.name) like :content or upper(a.code) like :content or upper(a.name) like :content)";
                params.put("content", "%" + content.toUpperCase() + "%");
            }
        } else {
            sql = "SELECT type as \"tType\", id as \"tId\",name as \"tName\",code as \"tCode\" from t_md_classifier_stat where owner_id  in (SELECT deployed_software from t_dw_db_mapping where dw_db_instance_id = :dataBaseId)";
            params.put("dataBaseId", dataBaseId);
            if (!"".equals(content)) {
                sql += " and (upper(name) like :content or upper(code) like :content)";
                params.put("content", "%" + content.toUpperCase() + "%");
            }
        }

        List<Map<String, Object>> dbtList = this.baseDao.sqlQueryForList(sql, params);

        String newSql = "SELECT classifier_stat_id as id from t_dw_table_mapping where dw_db_id = '" + dataBaseId + "'";

        List<Map<String, Object>> dbtIdList = this.baseDao.sqlQueryForList(newSql);
        List<String> ids = dbtIdList.stream().collect(Collectors.mapping(d -> String.valueOf(d.get("id")), Collectors.toList()));
        Iterator<Map<String, Object>> iterator = dbtList.iterator();

        while (iterator.hasNext()) {
            Map<String, Object> vo = iterator.next();
            if (ids.contains(vo.get("tId"))) {
                iterator.remove();
            }
        }

        return dbtList;
    }

    private String getDataSetName(String id) {
        String sql = "select name from t_md_dw_db_instance where id = :id";
        return this.baseDao.sqlQueryForValue(sql, addParam("id", id).param());
    }

    /**
     * 添加表
     *
     * @param dataBaseId
     * @param dataObject
     */
    @Override
    public void addTable(String dataBaseId, List<String> dataObject, String userId) {

        String dataSetName = this.getDataSetName(dataBaseId);


        StringBuffer sb = new StringBuffer();
        sb.append("INSERT INTO t_dw_table_mapping ");
        sb.append("(id,\"name\", \"type\", code, operate_time,dw_db_id, classifier_stat_id, if_stream,user_name,data_set_name,operate_user_id) ");
        sb.append(" VALUES (?,?,?,?,?,?,?,?,?,?,?)");
        List<Object[]> paramList = new ArrayList<>();
        String operateTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));


        String selectSql = "select id,name,type,code,db_type,global_code from t_md_classifier_stat where id in (:dataObject)";
        List<Map<String, String>> classifiers = this.baseDao.sqlQueryForList(selectSql, addParam("dataObject", dataObject).param());

        if ("ElasticSearchIndex".equals(classifiers.get(0).get("type"))) {
            String esSql = "select d.*,c.code as user_name from t_md_elasticsearch_instance c join (select " +
                    " a.id, " +
                    " a.owner_id, " +
                    " b.type_code as name, " +
                    " a.code, " +
                    " a.db_type, " +
                    " a.type " +
                    "from " +
                    " t_md_elasticsearch_index a " +
                    "join t_md_elasticsearch_index_type__relation b on " +
                    " a.id = b.index_id ) d on c.id = d.owner_id " +
                    "where " +
                    " d.id in( " +
                    "  :dataObject " +
                    " )";
            List<Map<String, String>> esList = this.baseDao.sqlQueryForList(esSql, addParam("dataObject", dataObject).param());
            for (Map<String, String> map : esList) {

                String name = map.get("name");
                String code = StringUtils.isBlank(map.get("code")) ? map.get("name") : map.get("code") + "/" + map.get("name");
//                String type = map.get("type");
                String type = "DwTableMapping";
                String classifier_stat_id = map.get("id");
                String stream = "0";
                String userName = map.get("user_name");

                String id = UUID.randomUUID().toString().replace("-", "");
                paramList.add(new Object[]{id, name, type, code, operateTime, dataBaseId, classifier_stat_id, stream, userName, dataSetName, userId});
            }
        } else {
            for (Map<String, String> c : classifiers) {

                String name = c.get("name") == null ? c.get("code") : c.get("name");
                String code = c.get("code");
                String userName = "";
                if (!"HBASE".equals(c.get("db_type").toUpperCase())) {
                    userName = this.getUserName(c.get("id"));
                }
//                String type = c.get("type");
                String type = "DwTableMapping";
                String classifier_stat_id = c.get("id");
                String stream = "0";
                String id = UUID.randomUUID().toString().replace("-", "");
                paramList.add(new Object[]{id, name, type, code, operateTime, dataBaseId, classifier_stat_id, stream, userName, dataSetName, userId});

            }
        }
        this.baseDao.batchInsert(sb.toString(), paramList);
//        Map<String, String> globalCodeMap = this.globalCodeService.updateClassifierStatGlobalCode(dataObject);
        //spark注册

        /*for (String dataObjId : dataObject) {
            String globalCode = globalCodeMap.get(dataObjId); //注册spark
            dataSetEditService.registerTable(dataObjId, globalCode, false);

        }*/

    }

    private String getUserName(String id) {
        String userName = "";
        String sql = "select owner_id from t_md_element where id = (select owner_id from t_md_element where id =:id)";
        String catalogId = this.baseDao.sqlQueryForValue(sql, addParam("id", id).param());

        DeployedSoftware catalog = deployedSoftwareService.get(DeployedSoftware.class, catalogId);
        if ("RdbCatalog".equals(catalog.getType())) {
            RdbCatalog c = deployedSoftwareService.get(RdbCatalog.class, catalogId);
            DragonEncryptor dragonEncryptor = new DragonEncryptor();
            userName = dragonEncryptor.decrypt(c.getUserName());
        }
        return userName;

    }

    private String getUserNameNew(String id) {
        String userName = "";
        ModelElement e1 = (ModelElement) baseDao.get(ModelElement.class, id);
        ModelElement e2 = (ModelElement) baseDao.queryForObject("from ModelElement where id=:id", addParam("id", e1.getOwnerId()).param());
        String catalogId = e2.getOwnerId();
        DeployedSoftware catalog = deployedSoftwareService.get(DeployedSoftware.class, catalogId);
        if ("RdbCatalog".equals(catalog.getType())) {
            RdbCatalog c = deployedSoftwareService.get(RdbCatalog.class, catalogId);
            DragonEncryptor dragonEncryptor = new DragonEncryptor();
            userName = dragonEncryptor.decrypt(c.getUserName());
        }
        return userName;

    }

    /**
     * 添加数据仓库目录
     */
    @Override
    public String addBusiDir(String dirName, String dirType, String usrId) {
        //业务库添加
        String busiId = UUID.randomUUID().toString().replace("-", "");
        if ("busi".equals(dirType)) {
            String sql = "INSERT INTO t_md_dw_busi_instance (id,name,type,code,dir_id) VALUES (?,?,?,?,?)";
            List<Object[]> paramList = new ArrayList<>();
            paramList.add(new Object[]{busiId, dirName, "DW_BUSI_DIR", "DW_BUSI_DIR", "-1"});
            this.baseDao.batchInsert(sql, paramList);
            return busiId;

        } else {//数据仓库添加
            if (checkDirisExist(dirName, "DATAWAREHOUSE_DIR", usrId)) {
                Assert.fail("同级目录不允许有相同名称！");
            }
            BusiDir busiDir = new BusiDir();
            busiDir.setName(dirName);
            busiDir.setType("BusiDir");
            busiDir.setCode("DATAWAREHOUSE_DIR");
            busiDir.setBusiDirType("DATAWAREHOUSE_DIR");
            busiDir.setOperateUserId(usrId);
            this.baseDao.saveOrUpdate(busiDir);
            return busiDir.getId();
        }
    }

    /**
     * @return
     */
    private boolean checkDirisExist(String dirName, String dirType, String userId) {
        String sql = "select * from t_md_busi_dir where busi_dir_type='" + dirType + "' and name='" + dirName + "' and operate_user_id ='" + userId + "'";
        List list = this.baseDao.sqlQueryForList(sql);
        return list.size() > 0 ? true : false;
    }

    @Override
    public Map<String, List<DBInstancesVo>> getAllDBInstances() {

        Map<String, List<DBInstancesVo>> dbInstancesMap = new HashMap<>();

        String sql = "SELECT DISTINCT \"upper\"(DB_TYPE) as \"dbType\" FROM public.t_md_classifier_stat where db_type is not null; ";
        List<Map<String, String>> dbTypeList = this.baseDao.sqlQueryForList(sql);

        StringBuffer sb = new StringBuffer();
        sb.append("select ")
                .append("d. ID, " +
                        "     d. NAME , " +
                        "\"upper\"(s.code) as \"code\" ")
                .append("FROM ")
                .append("t_md_deployed_software d ")
                .append("LEFT JOIN t_md_software s ON d.software_id = s. ID ")
                .append("WHERE \"upper\"(s.code) in (SELECT DISTINCT \"upper\"(DB_TYPE) FROM public.t_md_classifier_stat where db_type is not null ) ");
        List<Map<String, String>> dbInfoList = this.baseDao.sqlQueryForList(sb.toString());
        //循环所有的数据库类型
        for (Map<String, String> db : dbTypeList) {
            //数据库实例集合
            List<DBInstancesVo> voList = new ArrayList<>();
            for (Map<String, String> dbInfo : dbInfoList) {
                //获取数据库类型相应的实例
                if (db.get("dbType").equals(dbInfo.get("code"))) {
                    DBInstancesVo dbiv = new DBInstancesVo();
                    dbiv.setId(dbInfo.get("id"));
                    dbiv.setCode(dbInfo.get("code"));
                    dbiv.setName(dbInfo.get("name"));
                    voList.add(dbiv);
                }
            }
            dbInstancesMap.put(db.get("dbType"), voList);
        }

        return dbInstancesMap;
    }

    /**
     * 重命名一级目录
     *
     * @param name
     * @param id
     */
    @Override
    public void updateCatalog(String name, String id, String catalogType, String userId) {

        if (checkDirisExist(name, "DATAWAREHOUSE_DIR", userId)) {
            Assert.fail("同级目录不允许有相同名称！");
        }

        if ("busi".equals(catalogType)) {
            String sql = "UPDATE t_md_dw_busi_instance set name =:name where id =:id";
            this.baseDao.executeSqlUpdate(sql, addParam("name", name).addParam("id", id).param());
        } else {
            String sql = "";
            BusiClassify busiDir = (BusiClassify) this.baseDao.get(BusiClassify.class, id);
            busiDir.setName(name);
        }
    }

    /**
     * 重命名层次
     *
     * @param name
     * @param id
     */
    @Override
    public void updateLevel(String name, String id) {
        String code = "";
        if ("知识提取层".equals(name)) {
            code = "KNOWLEDGE_EXTRACT";
        } else if ("知识挖掘层".equals(name)) {
            code = "KNOWLEDGE_EXCAVATE";
        } else if ("知识领域层".equals(name)) {
            code = "KNOWLEDGE_DOMAIN";
        } else if ("数据应用层".equals(name)) {
            code = "HIGH_LATITUDE_FEATURE";
        } else {
            code = name;
        }

        String sql = "UPDATE t_md_dw_layer set name = :name,code=:code where id = :id";
        this.baseDao.executeSqlUpdate(sql, addParam("name", name).addParam("code", code).addParam("id", id).param());
    }

    /**
     * 重命名数据源
     *
     * @param name
     * @param id
     */
    @Override
    public void updateDataOriginName(String name, String id) {
        String code = name;
        String sql = "UPDATE t_md_dw_db_instance set name = :name,code=:code where id = :id";
        this.baseDao.executeSqlUpdate(sql, addParam("name", name).addParam("code", code).addParam("id", id).param());
    }

    /**
     * 拖动数据源
     *
     * @param id
     * @param ownerId
     */
    @Override
    public void moveDataOrigin(String id, String ownerId) {
        String sql = "update t_md_classify_element set busi_classify_id = :ownerId where element_id = :id";
        this.baseDao.executeSqlUpdate(sql, addParam("ownerId", ownerId).addParam("id", id).param());
    }

    @Override
    public String deleteTreeNode(String dirId, String dirType) {
        if ("busi".equals(dirType)) {
            String sql = "SELECT id from t_md_dw_busi_instance where parent_id = :dirId ORDER BY operate_time DESC";
            List<String> instanceList = this.baseDao.sqlQueryForList(sql, addParam("dirId", dirId).param());
            if (instanceList.size() > 0) {
                return "请先删除该目录下的所有子目录";
            } else {
                String deleteSQL = "DELETE FROM t_md_dw_busi_instance where id = :dirId";
                this.baseDao.executeSqlUpdate(deleteSQL, addParam("dirId", dirId).param());
            }
        } else {
            BusiDir busiDir = (BusiDir) this.baseDao.get(BusiDir.class, dirId);
            if (busiDir.getBusiClassifys().size() > 0) {
                return "请先删除该目录下的所有子目录";
            } else {
                String deleteSQL = "DELETE FROM t_md_busi_dir where id = :dirId";
                this.baseDao.executeSqlUpdate(deleteSQL, addParam("dirId", dirId).param());
            }
        }
        return "success";
    }

    @Override
    public void renameDataSet(String dataId, String newName) {
        String sql = "UPDATE t_md_dw_db_instance set name = :newName where id = :dataId";
        this.baseDao.executeSqlUpdate(sql, addParam("newName", newName).addParam("dataId", dataId).param());
    }

    @Override
    public void renameDataObjecDataName(String dataId, String newName) {
        String sql = "update t_dw_table_mapping set data_set_name = :newName where dw_db_id = :dataId";
        this.baseDao.executeSqlUpdate(sql, addParam("newName", newName).addParam("dataId", dataId).param());

    }

    @Override
    public String getSchemaId(String tableId, String dbType) {
        String schemaSQL = "select owner_id from t_md_rdb_dataobj where id = :tableId";
        if ("es".equals(dbType)) {
            schemaSQL = "select owner_id from t_md_elasticsearch_index where id= :tableId";
        }

        Map<String, String> param = new HashMap<>();
        param.put("tableId", tableId);
        return this.baseDao.sqlQueryForValue(schemaSQL, param);
    }

    @Override
    public List<String> getTableMappingIds(List<String> elementIds) {
        List<String> tableMappingIDList = Lists.newArrayList();
        String sql = "select classifier_stat_id from t_dw_table_mapping where dw_db_id in (:elementIds)";
        List<Map<String, String>> list = this.baseDao.sqlQueryForList(sql, addParam("elementIds", elementIds).param());
        for (Map map : list) {
            tableMappingIDList.add(map.get("classifier_stat_id").toString());
        }
        return tableMappingIDList;
    }

    @Override
    public Map<String, String> libraryRepetition(String dirId, String dbType) {
        String sql = "select" +
                "    a.name whname," +
                "    b.name graname" +
                "from" +
                "    t_md_busi_classify a" +
                "left join t_md_busi_classify b on" +
                "    a.parent_classify_id = b.id" +
                "where" +
                "    a.id =(" +
                "        select" +
                "            busi_classify_id" +
                "        from" +
                "            t_md_classify_element" +
                "        where" +
                "            element_id =(" +
                "                select" +
                "                    id" +
                "                from" +
                "                    t_md_dw_db_instance" +
                "                where" +
                "                    dir_id = :busiDirId" +
                "                    and dw_db_type = :dbType" +
                "            )" +
                "    )";
        return this.baseDao.sqlQueryForMap(sql, addParam("busiDirId", dirId).addParam("dbType", dbType).param());

    }

    @Override
    public List<Map<String, String>> getElastics(String dataSetId) {
        String sql = "select s.id schemaId,s.code indexName,c.type_id typeId from t_md_elasticsearch_index s left join t_md_elasticsearch_index_type__relation c on s.id=c.index_id where s.id in (select classifier_stat_id from t_dw_table_mapping t where t.dw_db_id = :dataSetId)";
        return this.baseDao.sqlQueryForList(sql, addParam("dataSetId", dataSetId).param());
    }

    @Override
    public List<Map<String, String>> getAllTableMapping() {
        String sql = " SELECT" +
                "    b.dw_db_id," +
                "    b. ID," +
                "    b. DWID," +
                "    b. NAME," +
                "    b.code," +
                "    b.db_type," +
                "    b.owner_id," +
                "    a.NAME as dataname " +
                "FROM" +
                "    t_md_dw_db_instance a " +
                "RIGHT JOIN (" +
                "    SELECT" +
                "        M .dw_db_id," +
                "        C . ID," +
                "        M . ID AS DWID," +
                "        M . NAME," +
                "        M .code," +
                "        C .db_type," +
                "        C .owner_id" +
                "    FROM" +
                "        t_dw_table_mapping M" +
                "    LEFT JOIN t_md_classifier_stat C ON C . ID = M .classifier_stat_id" +
                "    WHERE" +
                "        C . ID IS NOT NULL" +
                ") b ON a.id = b.dw_db_id";

        return this.baseDao.sqlQueryForList(sql);
    }

    @Override
    public List<ElasticColumnVo> getAllColumn(List<String> tableIds) {
        List<ElasticColumnVo> resList = Lists.newArrayList();
        String sql = "select" +
                "    f.id,f.name,f.code,f.memo,el.code as type,f.owner_id " +
                "from" +
                "    t_md_structural_feature f " +
                "left join t_md_element el on" +
                "    f.data_type_id = el.id where f.owner_id in (:tableIds)";
        List<Map<String, String>> columns = this.baseDao.sqlQueryForList(sql, addParam("tableIds", tableIds).param());
        for (Map<String, String> column : columns) {
            ElasticColumnVo vo = new ElasticColumnVo();
            vo.setId(column.get("id"));
            vo.setMemo(column.get("memo"));
            vo.setCode(column.get("code"));
            vo.setDataType(column.get("type"));
            vo.setName(column.get("name"));
            vo.setOwnerId(column.get("owner_id"));
            resList.add(vo);
        }
        return resList;
    }

    @Override
    public DataSetVo getDataSetInfo(String id) {

        String sql = "";
        String getTypeSql = "select type from t_md_deployed_software where id = :id";
        String dbType = this.baseDao.sqlQueryForValue(getTypeSql, addParam("id", id).param());
        if (HBASE_INSTANCE.equals(dbType) || KAFKA_INSTANCE.equalsIgnoreCase(dbType)) {
            sql = "select a.id,a.code,a.name,a.id schemaId, b.code \"type\" from t_md_deployed_software a left join t_md_software b on a.software_id = b.id where a.id = :id";
        } else {
            sql = "select a.id,a.name,a.type,a.code,s.id schemaId  " +
                    "from t_md_element s right join (" +
                    "   select c.id,c.name,c.code,p.code as type " +
                    "   from t_md_deployed_software c " +
                    "   left join t_md_software p on c.software_id = p.id  where c.id= :id) a " +
                    "   on s.owner_id =  a.id " +
                    "where s.type = 'RdbSchema' " +
                    "or s.type = 'ElasticSearchClusterNode' " +
                    "or s.type ='ZkCluster' " +
                    "or s.type ='DeployedComp' " +
                    "or s.type = 'ElasticsearchSchema' " +
                    "or s.type = 'KafkaInstance' " +
                    "limit 1";
        }
        //hql才能查到结果
        //一个个获取再组装起来,海得先查rdbCatalog这个表,再去查deployedSoftware
        Map<String, String> dataSet = this.baseDao.sqlQueryForMap(sql, addParam("id", id).param());
        if (StringUtils.isBlank(dataSet.get("id"))) {
            return null;
        }
        DataSetVo dataSetVo = new DataSetVo();
        dataSetVo.setId(dataSet.get("id"));
        dataSetVo.setName(dataSet.get("name"));
        dataSetVo.setCode(dataSet.get("code"));
        dataSetVo.setDbType(dataSet.get("type"));
        dataSetVo.setSchemaId(dataSet.get("schemaid"));
        return dataSetVo;
    }

    @Override
    public DataSetInfoVo getDeployedSoftware(String id) {

        Map<String, String> map = this.getDeployedSoftwareId(id);
        String type = map.get("db_instance_type");
        String deployedSoftwareId = map.get("deployed_software_id");

        DeployedSoftware catalog = deployedSoftwareService.get(DeployedSoftware.class, deployedSoftwareId);

        DataSetInfoVo vo = new DataSetInfoVo();
        vo.setType("hwmpp".equals(type) ? "Libra" : type);
        vo.setDbSchemaName(catalog.getCode());
        vo.setDbName(catalog.getName());
        vo.setDataSourceName(map.get("name"));
        String hostIP = "";
        int port = 0;
        if ("HbaseInstance".equals(catalog.getType())) {
            String hbaseSQL = "select" +
                    " zc.port as port, " +
                    " m.ip_address as ipAddress " +
                    "from " +
                    " t_md_hbase_instance hi " +
                    "left join t_md_zk_cluster zc on " +
                    " hi.zk_id = zc.owner_id " +
                    "left join t_md_zk_instance zi on " +
                    " zi.id = zc.owner_id " +
                    "left join t_md_deployed_comp dc on " +
                    " dc.owner_id = zi.id " +
                    "left join t_md_machine m on " +
                    " m.id = dc.machine_id " +
                    "where " +
                    " hi.id = :id";
            Map<String, Object> hbaseMap = this.baseDao.sqlQueryForMap(hbaseSQL, addParam("id", catalog.getId()).param());
            hostIP = hbaseMap.get("ipaddress").toString();
            port = (int) hbaseMap.get("port");
        } else {
            hostIP = catalog.getMaster().getIPAddress();
            port = catalog.getMaster().getPort();
        }
        vo.setHostIP(hostIP);
        vo.setPort(port);
        if ("RdbCatalog".equals(catalog.getType())) {
            RdbCatalog c = deployedSoftwareService.get(RdbCatalog.class, deployedSoftwareId);
            DragonEncryptor dragonEncryptor = new DragonEncryptor();
            String userName = dragonEncryptor.decrypt(c.getUserName());
            vo.setUserName(userName);
            vo.setPassword(dragonEncryptor.decrypt(c.getPassword()));
        }
        return vo;
    }

    @Override
    public Map<String, String> getDeployedSoftwareId(String id) {
        String sql = "select db_instance_type,deployed_software_id,name from t_md_dw_db_instance where id = :id";
        return this.baseDao.sqlQueryForMap(sql, addParam("id", id).param());
    }

    @Override
    public void renameDataDeployedSoftware(String id, String newName) {
        String sql = "UPDATE t_md_deployed_software set name = :newName where id = :dataId";
        this.baseDao.executeSqlUpdate(sql, addParam("newName", newName).addParam("dataId", id).param());
    }

    @Override
    public String getLabelName(String id) {
        String sql = "select name from t_md_label_element where code='datasource_source_first_classify' and  subject_id =:id";
        return this.baseDao.sqlQueryForValue(sql, addParam("id", id).param());
    }

    @Override
    public DataObjectVo getTableInfo(String id) {
        String sql = "select name,code,data_type,if_stream from t_dw_table_mapping where id = :id";
        Map<String, String> m = this.baseDao.sqlQueryForMap(sql, addParam("id", id).param());
        DataObjectVo v = new DataObjectVo();
        v.setChineseName(m.get("name"));
        v.setEnglishName(m.get("code"));
        v.setStream(Integer.valueOf(m.get("if_stream")) == 0 ? false : true);
        v.setType(m.get("data_type"));

        return v;
    }

    @Override
    public String updateStream(String id, boolean stream, String name) {

        String querySql1 = "select classifier_stat_id from t_dw_table_mapping  where id = :id";
        List<Map<String, String>> m1 = this.baseDao.sqlQueryForList(querySql1, addParam("id", id).param());

//        String querySql = "select id from t_md_classifier_stat where name = :name";
//        List<Map<String,String>> m = this.baseDao.sqlQueryForList(querySql, addParam("name", name).param());
//        m = m.stream().filter(s->!s.get("id").equals(m1.get(0).get("classifier_stat_id"))).collect(Collectors.toList());
//
//        if(m.size()!=0){
//            return "表名称重复";
//        }else {
        String isStream = stream ? "1" : "0";
        String sql = "UPDATE t_dw_table_mapping SET if_stream = :isStream, name = :name WHERE id = :id";
        this.baseDao.executeSqlUpdate(sql, addParam("isStream", isStream).addParam("name", name).addParam("id", id).param());


        String sql1 = "UPDATE t_md_classifier_stat SET  name = :name WHERE id = :id";
        this.baseDao.executeSqlUpdate(sql1, addParam("name", name).addParam("id", m1.get(0).get("classifier_stat_id")).param());
        return "修改成功";
//        }
    }

    @Override
    public boolean isDataWareHouse(List<String> classifierIds) {
        String sql = "select id from t_dw_table_mapping where classifier_stat_id in (:classifierIds)";
        List<Map<String, String>> list = this.baseDao.sqlQueryForList(sql, addParam("classifierIds", classifierIds).param());
        //判断是否被别的数仓使用
        return classifierIds.size() < list.size() ? true : false;
    }

    @Override
    public boolean checkTable(String id) {
        String sql = "select count(1) from t_dw_table_mapping where dw_db_id = :id";
        return Integer.valueOf(this.baseDao.sqlQueryForValue(sql, addParam("id", id).param())) > 0 ? true : false;
    }

    @Override
    public void setFastTable(String tableId, String isFast) {
        String deleteSql = "delete  from t_classifier_stat_extend where classifier_stat_id = '" + tableId + "'";
        this.baseDao.executeSqlUpdate(deleteSql);
        String addSql = "insert into t_classifier_stat_extend(classifier_stat_id,is_fast) values ('" + tableId + "','" + isFast + "')";
        this.baseDao.executeSqlUpdate(addSql);
        String sql = "select global_code from t_md_classifier_stat where id = :tableId";
        Map<String, String> dataInfo = this.baseDao.sqlQueryForMap(sql, addParam("tableId", tableId).param());
        ISparkClientService sparkClientService = SparkConsumer.getSparkClient(dubboUrl);
        if (Boolean.valueOf(isFast)) {
            sparkClientService.cacheTable(dataInfo.get("global_code"));
        } else {
            sparkClientService.unCacheTable(dataInfo.get("global_code"));
        }
    }

    @Override
    public String getIsFast(String tableId) {
        String sql = "select * from t_classifier_stat_extend where classifier_stat_id = :tableId";
        Map<String, String> dataInfo = this.baseDao.sqlQueryForMap(sql, addParam("tableId", tableId).param());
        String index = "false";
        if (0 != dataInfo.size()) {
            index = dataInfo.get("is_fast");
        }
        return index;
    }

    @Override
    public DataWarehouseVo queryDBInstanceById(String id) {
        String sql = "select id,name,code,db_instance_type as type,deployed_software_id as softwareId from t_md_dw_db_instance where id = :id";
        Map<String, String> dataInfo = this.baseDao.sqlQueryForMap(sql, addParam("id", id).param());
        DataWarehouseVo dataWarehouseVo = new DataWarehouseVo();
        dataWarehouseVo.setName(dataInfo.get("name"));
        dataWarehouseVo.setDomainDbType(dataInfo.get("type"));
        dataWarehouseVo.setSoftwareId(dataInfo.get("softwareId"));
        return dataWarehouseVo;
    }

    @Override
    public String moveDataObj(String dwDbId, String dataObjId, String dataSetName, String userId) {
        Assert.notNull(dwDbId, "请选择数据源！");
        Assert.notNull(dataObjId, "数据源id不能为空！");

        //获取数据集的id
        String logicDataObjId = findLogicDataObjId(dataObjId, userId);
        //获取schemaId
        String schemaId = getSchemaIdByDwInstance(dwDbId);
        String schemaIdByDataObj = getSchemaIdByDataObj(dataObjId);
        //判断两个数据源的schema是否相同
        if (checkSchema(schemaId, schemaIdByDataObj)) return "移动失败，数据库实例不同！";
        move(dwDbId, dataObjId, dataSetName, logicDataObjId);

        return "success";
    }

    protected void move(String dwDbId, String dataObjId, String dataSetName, String logicDataObjId) {
        //移动数据对象
        moveDataObj(dwDbId, dataObjId, dataSetName);
        //移动数据集
        moveLogicDataObj(logicDataObjId, dwDbId);
    }

    private void moveLogicDataObj(String logicDataObjId, String dwDbId) {
        String updateSql = "update t_md_logic_data_relation set element_id = :dwid where logic_data_obj_id = :logicid and relation_type = '0'  ";
        this.baseDao.executeSqlUpdate(updateSql, addParam("dwid", dwDbId).addParam("logicid", logicDataObjId).param());
    }

    private String findLogicDataObjId(String dataObjId, String userId) {
        //获取旧的数据源id
        Map<String, String> oldDbInstanceIdAndClassifyId = findOldDbInstanceId(dataObjId);
        return findLogicDataObjId(oldDbInstanceIdAndClassifyId, userId);
    }

    private String findLogicDataObjId(Map<String, String> oldDbInstanceIdAndClassifyId, String userId) {
        String dwid = oldDbInstanceIdAndClassifyId.get("dwid");
        String statid = oldDbInstanceIdAndClassifyId.get("statid");
        String querySql = "select id from t_md_logic_dataobj where id in (select logic_data_obj_id from t_md_logic_data_relation where element_id = :dwid and relation_type = '0') and owner_id =:statid and operate_user_id =:user";
        return this.baseDao.sqlQueryForValue(querySql, (addParam("statid", statid).addParam("dwid", dwid)).addParam("user", userId).param());
    }

    private Map<String, String> findOldDbInstanceId(String dataObjId) {
        String querySql = "select dw_db_id dwid,classifier_stat_id statid from t_dw_table_mapping where id = :id";
        return this.baseDao.sqlQueryForMap(querySql, addParam("id", dataObjId).param());
    }

    protected void moveDataObj(String dwDbId, String dataObjId, String dataSetName) {
        String updateSQL = "update t_dw_table_mapping set dw_db_id = :id,data_set_name=:name " +
                " where id = :tableid";
        this.baseDao.executeSqlUpdate(updateSQL, addParam("id", dwDbId).addParam("tableid", dataObjId).addParam("name", dataSetName).param());
    }

    private boolean checkSchema(String schemaId, String schemaIdByDataObj) {
        boolean resFlag = false;
        if (!schemaId.equals(schemaIdByDataObj)) resFlag = true;
        return resFlag;
    }

    private String getSchemaIdByDataObj(String dataObjId) {
        String querySQL = "select classifier_stat_id from t_dw_table_mapping where id = :id";
        String classifierId = this.baseDao.sqlQueryForValue(querySQL, addParam("id", dataObjId).param());
        return getSchemaIdByClassifier(classifierId);
    }

    private String getSchemaIdByClassifier(String classifierId) {
        ClassifierStat classifierStat = (ClassifierStat) this.baseDao.get(ClassifierStat.class, classifierId);
        return classifierStat.getOwnerId();
    }

    private String getSchemaIdByDwInstance(String dwDbId) {
        String querySQL = "select deployed_software from t_dw_db_mapping where dw_db_instance_id = :id";
        return this.baseDao.sqlQueryForValue(querySQL, addParam("id", dwDbId).param());
    }

    @Override
    public int checkDataSourceName(String classifyId, String dataSourceName) {
        String checkName = "select count(1) from t_md_dw_db_instance where name = :name and id in" +
                " (select element_id from t_md_classify_element where busi_classify_id = :id)";
        return Integer.parseInt(this.baseDao.sqlQueryForValue(checkName, addParam("name", dataSourceName).addParam("id", classifyId).param()));

    }

    @Override
    public List<DatasetTreeModel> filterSameDatasourceTree(List<DatasetTreeModel> datasetTreeModels, String filterDataSetId) {
        String catalogId = getCatalogId(filterDataSetId);
        //获取同一个catalogId的数据集
        List<String> sameCatalogDatasets = getSameCatalogDatasets(catalogId);
        removeDataset(datasetTreeModels, sameCatalogDatasets);
        return datasetTreeModels;
    }

    @Override
    public PageInfo queryTableByDadaSourceId(String dbId, String userId, PageInfo pageInfo, String name) {
        return null;
    }

    private void removeDataset(List<DatasetTreeModel> datasetTreeModels, List<String> sameCatalogDatasets) {

        Iterator<DatasetTreeModel> iterator = datasetTreeModels.iterator();
        while (iterator.hasNext()) {
            DatasetTreeModel datasetTreeModel = iterator.next();
            if (datasetTreeModel.getChildren() != null) {
                removeDataset(datasetTreeModel.getChildren(), sameCatalogDatasets);
            } else {
                if (!sameCatalogDatasets.contains(datasetTreeModel.getId())) {
                    iterator.remove();
                }
            }
        }
    }


    protected String getCatalogId(String currentDataSetId) {
        String schemaId = "";
        if (StringUtils.isNotBlank(currentDataSetId)) {
            LogicDataObj obj = logicDataObjService.findLogicDataObjById(currentDataSetId);
            ClassifierStat classifierStat = logicDataObjService.getClassifierStatByLogic(obj);

            if (classifierStat == null) return "";
            schemaId = classifierStat.getOwner().getOwnerId();
        }
        return schemaId;
    }

    private List<String> getSameCatalogDatasets(String catalogId) {
        String sql = "select id from t_md_classifier_stat where owner_id in(select id from t_md_classifier_stat where owner_id in (select id from t_md_rdb_schema where owner_id=:catalogId))";
        List<HashMap> logicDataset = this.baseDao.sqlQueryForList(sql, addParam("catalogId", catalogId).param());
        List<String> ids = logicDataset.stream().map(s -> s.get("id").toString()).collect(Collectors.toList());
        return ids;
    }

    @Autowired
    private IDataWareTreeService dataWareTreeService;
    @Autowired
    private SysAuthObjRelService sysAuthObjRelService;
    @Autowired
    private IDataWarehousePlanService dataWarehouseService;


    private List<DatasetTableModel> getDatasetTableModels(String userId, List<DatasetTableModel> datasetTableModels) {
        List<String> tableId = new ArrayList<>();
        //用户-数据
        Map<String, String> userParams = Maps.newHashMap();
        userParams.put("OBJ_ID", userId.toString());
        List<TSysAuthObjFunc> tSysAuthObjFuncs = sysAuthObjFuncService.queryList(userParams);
        List<String> tSysAuthObjFuncsId = tSysAuthObjFuncs.stream().map(s -> s.gettSysFuncBase().getFuncCode()).collect(Collectors.toList());
        tableId.addAll(tSysAuthObjFuncsId);

        //用户-角色-数据
        Map<String, String> roleParams = Maps.newHashMap();
        roleParams.put("from_obj_id", userId);
        roleParams.put("RELATION_TYPE", "1");
        List<TSysAuthObjRel> sysAuthObjRels = sysAuthObjRelService.queryList(roleParams);
        for (TSysAuthObjRel tSysAuthObjRel : sysAuthObjRels) {
            Map<String, String> roleDataParams = Maps.newHashMap();
            roleDataParams.put("OBJ_ID", tSysAuthObjRel.getToAuthObj().getId());
            List<TSysAuthObjFunc> roleDataTSysAuthObjFuncs = sysAuthObjFuncService.queryList(roleDataParams);
            List<TSysFuncBase> tSysFuncBases = roleDataTSysAuthObjFuncs.stream().filter(s -> s.gettSysFuncBase().getFuncType().equals("1")).map(t -> t.gettSysFuncBase()).collect(Collectors.toList());
            List<String> roleDataTSysAuthObjFuncsId = tSysFuncBases.stream().map(s -> s.getFuncCode()).collect(Collectors.toList());
            tableId.addAll(roleDataTSysAuthObjFuncsId);
        }
        return datasetTableModels.stream().filter(s -> tableId.contains(s.getId())).collect(Collectors.toList());
    }

    @Override
    public void addTableNew(String dataBaseId, List<String> dataObject, String userId) {
        String dataSetName = this.getDataSetName(dataBaseId);
        StringBuffer sb = new StringBuffer();
        sb.append("INSERT INTO t_dw_table_mapping ");
        sb.append("(id,\"name\", \"type\", code, operate_time,dw_db_id, classifier_stat_id, if_stream,user_name,data_set_name,operate_user_id) ");
        sb.append(" VALUES (?,?,?,?,?,?,?,?,?,?,?)");
        List<Object[]> paramList = new ArrayList<>();
        String operateTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));


        String selectHql = " from ClassifierStat where id in (:dataObject)";
        List<ClassifierStat> classifiers = this.baseDao.queryForList(selectHql, addParam("dataObject", dataObject).param());
        if ("ElasticSearchIndex".equals(classifiers.get(0).getType())) {
            String esSql = "select d.*,c.code as user_name from t_md_elasticsearch_instance c join (select " +
                    " a.id, " +
                    " a.owner_id, " +
                    " b.type_code as name, " +
                    " a.code, " +
                    " a.db_type, " +
                    " a.type " +
                    "from " +
                    " t_md_elasticsearch_index a " +
                    "join t_md_elasticsearch_index_type__relation b on " +
                    " a.id = b.index_id ) d on c.id = d.owner_id " +
                    "where " +
                    " d.id in( " +
                    "  :dataObject " +
                    " )";
            List<Map<String, String>> esList = this.baseDao.sqlQueryForList(esSql, addParam("dataObject", dataObject).param());
            for (Map<String, String> map : esList) {

                String name = map.get("name");
                String code = StringUtils.isBlank(map.get("code")) ? map.get("name") : map.get("code") + "/" + map.get("name");
//                String type = map.get("type");
                String type = "DwTableMapping";
                String classifier_stat_id = map.get("id");
                String stream = "0";
                String userName = map.get("user_name");

                String id = UUID.randomUUID().toString().replace("-", "");
                paramList.add(new Object[]{id, name, type, code, operateTime, dataBaseId, classifier_stat_id, stream, userName, dataSetName, userId});
            }
        } else {
            for (ClassifierStat c : classifiers) {

                String name = c.getName() == null ? c.getCode() : c.getName();
                String code = c.getCode();
                String userName = "";
                if (!"HBASE".equals(c.getDbType().toUpperCase())) {
                    userName = this.getUserNameNew(c.getId());
                }
//                String type = c.get("type");
                String type = "DwTableMapping";
                String classifier_stat_id = c.getId();
                String stream = "0";
                String id = UUID.randomUUID().toString().replace("-", "");
                paramList.add(new Object[]{id, name, type, code, operateTime, dataBaseId, classifier_stat_id, stream, userName, dataSetName, userId});

            }
        }
        this.baseDao.batchInsert(sb.toString(), paramList);
    }

    @Override
    public DataSetVo getDataSetInfoNew(String id) {
        //改成hql的查询
        //查catalog只是为了初始化DeployedSoftware
        RdbCatalog catalog = (RdbCatalog) this.baseDao.queryForObject("from RdbCatalog where id=:id", addParam("id", id).param());
        String getTypeHql = " from DeployedSoftware where id = :id";
        DeployedSoftware deployedSoftware = (DeployedSoftware) this.baseDao.queryForObject(getTypeHql, addParam("id", id).param());
        if (null == deployedSoftware) {
            return null;
        }
        DataSetVo dataSetVo = new DataSetVo();
        if (HBASE_INSTANCE.equals(deployedSoftware.getType())) {
            //这个可以直接查
            dataSetVo.setSchemaId(deployedSoftware.getId());
        } else {
            String hql2 = "from ModelElement s where ownerId=:id and type = 'RdbSchema' or s.type = 'ElasticSearchClusterNode' or s.type ='ZkCluster' or s.type ='DeployedComp' or s.type = 'ElasticsearchSchema' ";
            ModelElement element = (ModelElement) this.baseDao.queryForObject(hql2, addParam("id", id).param());
            dataSetVo.setSchemaId(element.getId());
        }
        dataSetVo.setId(deployedSoftware.getId());
        dataSetVo.setName(deployedSoftware.getName());
        dataSetVo.setCode(deployedSoftware.getCode());
        dataSetVo.setDbType(deployedSoftware.getSoftware().getType());
        return dataSetVo;
    }

    @Override
    public List<String> getFactNameByCode(List<String> codes) {
        List<String> names=new ArrayList<>();
        String sql="select name from t_md_kg_busi_fact where code=:codes ";
        List<Map<String,String>> list=baseDao.sqlQueryForList(sql,addParam("codes",codes).param());
        if (!CollectionUtils.isEmpty(list)){
            names=list.stream().map(m->m.get("name")).collect(Collectors.toList());
        }
        return names;
    }

    /**
     * 新增或更新全局数据源配置
     * @param qo
     */
    @Override
    public void saveAnalysis(AnalysisResultLibraryQO qo) {
        Assert.isTrue(StrUtil.isNotBlank(qo.getId()),"数据源id不能为空");
        Map<String, Object> queryMap = Maps.newHashMap();
        queryMap.put("values", Arrays.asList("ANALYSIS_RESULT_LIBRARY","ANALYSIS_RESULT_LIBRARY_SCHEMA"));
        String querySql = "select * from t_sys_parameter where param_name in (:values)";
        List<Map<String,String>> list = baseDao.sqlQueryForList(querySql, queryMap);
        updateOrInsertParameter("ANALYSIS_RESULT_LIBRARY",qo.getId(),list);
        updateOrInsertParameter("ANALYSIS_RESULT_LIBRARY_SCHEMA",qo.getSchema(),list);
    }


    /**
     * 新增或更新全局数据
     */
    private void updateOrInsertParameter(String paramName,String paramValue,List<Map<String,String>> list){
        String sql;
        Map<String, String> map = Maps.newHashMap();
        map.put("paramName",paramName);
        map.put("paramValue",paramValue);
        if (list.stream().anyMatch(key -> paramName.equals(key.get("param_name")))){
            sql = "update t_sys_parameter set param_value = :paramValue where param_name = :paramName";
        }else {
            sql = "INSERT INTO t_sys_parameter (param_name, param_value, memo, can_refresh, type) VALUES (:paramName, :paramValue, null, null, 'dc')";;
        }
        baseDao.executeSqlUpdate(sql,map);
    }




    @Override
    public AnalysisResultLibraryVO getAnalysisResult() {
        AnalysisResultLibraryVO vo = new AnalysisResultLibraryVO();
        Map<String, Object> queryMap = Maps.newHashMap();
        queryMap.put("values", Arrays.asList("ANALYSIS_RESULT_LIBRARY","ANALYSIS_RESULT_LIBRARY_SCHEMA"));
        String querySql = "select * from t_sys_parameter where param_name in (:values)";
        List<Map<String,String>> list = baseDao.sqlQueryForList(querySql, queryMap);
        list.forEach(map -> {
            String paramName = Optional.ofNullable(map.get("param_name")).orElse(StrUtil.EMPTY);
            String paramValue = Optional.ofNullable(map.get("param_value")).orElse(StrUtil.EMPTY);
            if ("ANALYSIS_RESULT_LIBRARY".equals(paramName)){
                vo.setId(paramValue);
            }else if ("ANALYSIS_RESULT_LIBRARY_SCHEMA".equals(paramName)){
                vo.setSchema(paramValue);
            }
        });
        return vo;
    }

    @Override
    public List<DatasetTableModel> querySuperDataBaseTableAll(List<DatasetTableModel> datasetTableModels, String userId) {
        // 用户-角色-数据
        List<String> objIds = new ArrayList<>(Arrays.asList(userId, GlobalConstant.UserProperties.DC_SUPER_ID));
        List<TSysAuthObjRel> sysAuthObjRelList = baseDao.queryForList(
                "from TSysAuthObjRel where from_obj_id in (:from_obj_id) and RELATION_TYPE = '1'",
                Collections.singletonMap("from_obj_id", objIds)
        );
        if (ObjectUtil.isNotEmpty(sysAuthObjRelList)) {
            List<String> authIds = sysAuthObjRelList.stream()
                    .map(rel -> rel.getToAuthObj().getId())
                    .collect(Collectors.toList());
            objIds.addAll(authIds);
        }
        List<TSysAuthObjFunc> tSysAuthObjFuncList = baseDao.queryForList(
                "from TSysAuthObjFunc where OBJ_ID in (:OBJ_ID)",
                Collections.singletonMap("OBJ_ID", objIds)
        );
        List<String> tableIds = tSysAuthObjFuncList.stream()
                .filter(func -> ObjectUtil.isNotNull(func.gettSysFuncBase()) && "1".equals(func.gettSysFuncBase().getFuncType()))
                .map(func -> func.gettSysFuncBase().getFuncCode())
                .collect(Collectors.toList());
        return datasetTableModels.stream()
                .filter(model -> tableIds.contains(model.getId()))
                .collect(Collectors.toList());
    }

}
