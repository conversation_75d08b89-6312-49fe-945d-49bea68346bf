INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state", "is_doc_show") VALUES ('inputOutputDirCicadaKafkaInputMeta', '0', 'kafka输入', 'inputOutputDir', NULL, NULL, NULL, '2022-08-01 14:21:03', NULL, '1', '1');
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state", "is_doc_show") VALUES ('inputOutputDirCicadaKafkaOutputMeta', '0', 'kafka输出', 'inputOutputDir', NULL, NULL, NULL, '2022-08-01 14:21:03', NULL, '1', '1');

INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('cc722590d6135a999cf4fd2ca82bc86a', 'd6121bc4248e45019942e2cb78362500', 'inputOutputDirCicadaKafkaInputMeta', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_sys_auth_obj_func"("id", "obj_id", "func_code", "start_date", "end_date", "create_id", "update_id", "create_time", "update_time", "enable_state") VALUES ('d666a77aa50d5da2bc577f09e5b2851e', 'd6121bc4248e45019942e2cb78362500', 'inputOutputDirCicadaKafkaOutputMeta', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
