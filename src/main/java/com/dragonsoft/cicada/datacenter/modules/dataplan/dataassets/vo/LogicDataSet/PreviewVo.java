package com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.vo.LogicDataSet;

import com.code.dataset.operator.join.JoinTableStep;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020.7.27
 */
@Data
public class PreviewVo implements Serializable {
    private String dataSetId;
    private String addDataSetId;
//    private List<String> fields;
    private int page;
    private int pageSize;

    private String sql;
    private String schemaId;

    private String titleType; //默认使用字段中文名称为预览数据表头; "column":使用字段code为表头
    private List<String> columnList;

    private List<ColumnRefVO> columnRefs;

    private String tableName;

    private String classifyStatId;

    private String condition;

    /**
     * 关联查询相关步骤(可能要调整)
     */
    private List<JoinTableStep> dataSetJoinVo;

    private String userId;
}
