package com.dragonsoft.cicada.datacenter.modules.modeling.controller;

import com.code.common.paging.PageInfo;
import com.code.metadata.etl.trans.TransMeta;
import com.code.metaservice.etl.trans.TransMetaService;
import com.dragoninfo.dfw.bean.Result;
import com.dragonsoft.cicada.datacenter.modules.modeling.service.MlsqlLogManageService;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.sql.Timestamp;
import java.util.Iterator;
import java.util.Map;

@CrossOrigin
@RestController
@RequestMapping(value = "/taskLog")
public class TaskLogController {

    @Autowired
    private TransMetaService transMetaService;

    @Autowired
    private MlsqlLogManageService mlsqlLogManageService;

    /**
     * 任务日志
     *
     * @param map
     * @return
     */
    @PostMapping(value = "/transTaskLogListPage")
    public Result transTaskLogListPage(@RequestBody Map map) {
        int pageSize = Integer.parseInt(map.get("pageSize").toString());
        int pageIndex = Integer.parseInt(map.get("pageIndex").toString());
        String sortField = map.get("sortField").toString();
        String sortOrder = map.get("sortOrder").toString();
        String transMetaId = map.get("transMetaId").toString();
        String state = (String) map.get("state");
        String startTime = (String) map.get("startTime");
        String endTime = (String) map.get("endTime");

        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageIndex(pageIndex);
        pageInfo.setPageSize(pageSize);
        pageInfo.setSortField(sortField);
        pageInfo.setSortDirect(sortOrder);
        pageInfo = this.mlsqlLogManageService.getTaskLogListPage(transMetaId, pageInfo,state,startTime,endTime);
        if (CollectionUtils.isNotEmpty(pageInfo.getDataList())) {
            Iterator iterator = pageInfo.getDataList().iterator();
            while (iterator.hasNext()){
                Map next = (Map) iterator.next();
                Timestamp end_time = (Timestamp) next.get("end_time");
                Timestamp start_time = (Timestamp) next.get("start_time");
                double time = 0.0;
                if (null != end_time && null != start_time)
                    time = ((double) end_time.getTime() - (double) start_time.getTime()) / 1000;
                next.put("runTime", time + "秒");
            }
        }


        String distributedValue = transMetaService.getTransAttributeValue(transMetaId,"distributed");

        Map<String, Object> hashMap = Maps.newHashMap();
        hashMap.put("isSparkTask", "3".equals(distributedValue));
        pageInfo.setStaInfo(hashMap);

        return Result.success(pageInfo);
    }

}
