package com.dragonsoft.cicada.datacenter.modules.metadata.service;

import com.code.metaservice.datawarehouse.model.DataSourceVO;
import com.code.metaservice.res.app.zk.IZkInstanceService;
import com.code.metaservice.res.response.datasource.InsertDataSourceResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> Avizii
 * @Create : 2021.03.29
 */
@Service("zkConnectService")
public class ZookeeperConnectService extends DataBaseConnectService {

    @Autowired
    private IZkInstanceService zkInstanceService;

    @Override
    public boolean testConnection(DataSourceVO dataSourceVO) {
        return zkInstanceService.testConnection(toDataSourceRequest(dataSourceVO));
    }

    @Override
    public InsertDataSourceResponse insertDataSource(DataSourceVO dataSourceVO) {
        return zkInstanceService.insertResource(toDataSourceRequest(dataSourceVO));
    }

    @Override
    public void updateResource(DataSourceVO dataSourceVO) {
        zkInstanceService._updateResourceInfo(toDataSourceRequest(dataSourceVO));
    }

    @Override
    protected DataSourceVO getDataSourceInfo(String schemaId) {
        return null;
    }
}
