package com.dragonsoft.cicada.datacenter.modules.datavisual.dashboard.impl;

import cn.hutool.core.collection.CollUtil;
import com.code.common.utils.DateUtils;
import com.code.common.utils.StringUtils;
import com.code.metadata.datavisual.Dashboard;
import com.code.metadata.datavisual.DashboardGroup;
import com.code.metaservice.datavisual.IDashBoardService;
import com.code.metaservice.datavisual.IDashboardGroupService;
import com.dragonsoft.cicada.datacenter.common.token.GlobalConstant;
import com.dragonsoft.cicada.datacenter.modules.datavisual.dashboard.IDashboardGroupBuilder;
import com.dragonsoft.cicada.datacenter.modules.supermarkmodel.service.IMyModelService;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.service.IUserService;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
public class DashboardGroupBuilder implements IDashboardGroupBuilder {
    @Autowired
    IDashboardGroupService dashboardGroupService;
    @Autowired
    IDashBoardService dashBoardService;

    @Autowired
    private IMyModelService myModelServiceImpl;

    @Autowired
    private IUserService userService;

    @Override
    public List<DashboardGroup> builderGroupNoMy(String userId) {
        List<DashboardGroup> list = dashboardGroupService.getGroupListNoMy(userId);
        if (CollUtil.isEmpty(list)) {
            list = new ArrayList<>();
        }
        return list;
    }

    @Override
    public List<DashboardGroup> builderGroup(String userId) {
        List<DashboardGroup> list = dashboardGroupService.getGroupList(userId);
        if (CollUtil.isEmpty(list)) {
            list = new ArrayList<>();
        }
        return list;
    }


    @Override
    public List<DashboardGroup> builderGroupByShare(String userId) {
        List<DashboardGroup> list = dashboardGroupService.queryGroupStandard();
        if (CollUtil.isEmpty(list)) {
            list = new ArrayList<>();
        }
        return list;
    }

    @Override
    public List<DashboardGroup> builderGroupStandard() {
        List<DashboardGroup> list = dashboardGroupService.queryGroupStandard();
        if (CollUtil.isEmpty(list)) {
            list = new ArrayList<>();
        }
        return list;
    }

    @Override
    public DashboardGroup addOrUpdateGroup(String parentId, String name, String id, String userId) {
        String uuid = UUID.randomUUID().toString().replace("-", "");
        DashboardGroup ds = new DashboardGroup();
        ds.setParentId(parentId);
        ds.setName(name);
        ds.setOperateTime(DateUtils.getTimeStr(new Date(), 2));
        ds.setOperateUserId(userId);
        if (StringUtils.isBlank(id)) {
            ds.setId(uuid);
            dashboardGroupService.saveGroup(ds);
            return ds;
        }
        ds.setId(id);
        dashboardGroupService.updateGroup(ds);
        return ds;
    }

    @Override
    public boolean checkName(String name, String id, String userId) {
        long count = dashboardGroupService.getGroupByNameFromCount(id, name, userId);
        if (count > 0) {
            return true;
        }
        return false;
    }

    @Override
    public void deleteGroup(String id) {
        List<Dashboard> dashboardsEntities = dashBoardService.getListByGroupId(id, null);
        dashboardsEntities.forEach(d -> {
            dashBoardService.deleteDashBoard(d.getId());
            //删除模型市场数据
            myModelServiceImpl.deleteMarkModelByTransId(d.getId());
        });
        dashboardGroupService.deleteGroup(id);
    }

    @Override
    public List<DashboardGroup> builderGroupShare(String userId) {
        return null;
    }

    @Override
    public List<DashboardGroup> buliderGroupCase(String userId) {
        return dashboardGroupService.getGroupByCase(userId);
    }

    @Override
    public List<DashboardGroup> builderPersonalGroup(String userId) {
        List<DashboardGroup> result = new ArrayList<>();
        DashboardGroup dashboardGroup = new DashboardGroup();
        dashboardGroup.setName("他人空间");
        dashboardGroup.setParentId("-1");
        dashboardGroup.setId(GlobalConstant.CommonProperties.FROM_PERSONAL_TOP_NODE_ID);
        result.add(dashboardGroup);
        List<String> userIdList = dashboardGroupService.getDashboardGroupOtherUser(userId);
        if (CollUtil.isEmpty(userIdList)) {
            return result;
        }
        Map<String, String> userMap = userService.getUserMap(userIdList);
        userMap.forEach((user, name) -> {
            DashboardGroup otherGroup = new DashboardGroup();
            otherGroup.setName(name);
            otherGroup.setId(user);
            otherGroup.setParentId(GlobalConstant.CommonProperties.FROM_PERSONAL_TOP_NODE_ID);
            otherGroup.setOperateUserId(user);
            List<DashboardGroup> userGroup = builderGroup(user);
            if (CollUtil.isNotEmpty(userGroup)) {
                userGroup.forEach(d -> {
                    if ("1".equals(d.getParentId())) {
                        d.setParentId(user);
                    }
                });
                result.add(otherGroup);
                result.addAll(userGroup);
            }
        });

        return result;
    }

    //    private List<DashboardGroup> builderGroupTree(List<DashboardGroup> allList) {
//        List<DashboardGroup> parents = new ArrayList<>();
//        for (DashboardGroup all : allList) {
//            if (StringUtils.isBlank(all.getParentId())) {
//                parents.add(all);
//                this.setGroupChild(all, allList);
//            }
//        }
//        return parents;
//    }
//
//    private void setGroupChild(DashboardGroup parent, List<DashboardGroup> all) {
//        for (DashboardGroup c : all) {
//            if (StringUtils.isNotBlank(c.getParentId()) && c.getParentId().equals(parent.getId())) {
//                List<DashboardGroup> child = parent.getChild();
//                if (null == c.getChild()) {
//                    child = new ArrayList<>();
//                }
//                child.add(c);
//                parent.setChild(child);
//                this.setGroupChild(c, all);
//            }
//        }
//    }
}
