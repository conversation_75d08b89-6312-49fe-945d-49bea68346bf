package com.dragonsoft.cicada.datacenter.modules.dataplan.externaldatasources.controller;

import com.code.common.utils.R;
import com.code.metadata.res.common.ClassifierStat;
import com.code.metadata.sm.EnumServiceType;
import com.code.metadata.sm.ServiceMeta;
import com.code.metaservice.core.ClassifierService;
import com.code.metaservice.ddl.ILogicDataObjService;
import com.code.metaservice.sm.IServiceMetaService;
import com.dragoninfo.dfw.annotation.FuncScanAnnotation;
import com.dragoninfo.dfw.bean.Result;
import com.dragoninfo.dfw.entity.TSysAuthObjFunc;
import com.dragoninfo.dfw.service.SysAuthObjFuncService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.externaldatasources.service.IDataSetAuthService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.externaldatasources.vo.ClassfierStatVo;
import com.dragonsoft.cicada.datacenter.modules.dataplan.externaldatasources.vo.DataObjectVo;
import com.dragonsoft.cicada.datacenter.modules.dataplan.externaldatasources.vo.DataSetAuthVo;
import com.dragonsoft.cicada.datacenter.modules.firstpage.share.service.ShareManagementService;
import com.dragonsoft.cicada.datacenter.modules.firstpage.share.vo.ResourceTypeEnum;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.service.IRoleService;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.service.IUserService;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.vo.RoleVo;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.vo.UserVo;
import com.google.common.collect.Maps;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/6/8
 */
@Controller
@CrossOrigin
@RequestMapping("/dataSetAuth")
@Api(value = "DataSetAuthController|数据对象权限控制器")
@FuncScanAnnotation(code = "dataObjectManage", name = "数据对象管理", parentCode = "dataWarehousePlan")
public class DataSetAuthController {

    @Autowired
    private IDataSetAuthService dataSetAuthService;
    @Autowired
    private IUserService userService;
    @Autowired
    private IRoleService roleService;
    @Autowired
    private SysAuthObjFuncService sysAuthObjFuncService;

    @Autowired
    private ILogicDataObjService logicDataObjService;

    @Autowired
    private ClassifierService classifierService;

    @Autowired
    private ShareManagementService shareManagementService;

    @Autowired
    private IServiceMetaService serviceMetaService;

    @ResponseBody
    @RequestMapping("/dataSetAuthRegister")
    @ApiOperation(value = "添加数据源到功能表")
    public Result dataSetAuthRegister(@RequestBody DataSetAuthVo dataSetAuthVo, HttpServletRequest request) {
        String userId = (String) request.getSession().getAttribute("userId");
        dataSetAuthService.saveDataSetAuth(dataSetAuthVo.getDataObjectVos(),userId);
        return Result.success();
    }

    @ResponseBody
    @RequestMapping("/addDataSetAuth")
    @ApiOperation(value = "添加数据源授权")
    public Result addDataSetAuth(@RequestBody DataSetAuthVo dataSetAuthVo) {
        dataSetAuthService.saveFirstAuth(dataSetAuthVo);
        return Result.success();
    }

    @ResponseBody
    @RequestMapping("/dataSetsAuth")
    @ApiOperation(value = "批量授权")
    @FuncScanAnnotation(code = "dataObjectManageDataSetsAuth", name = "批量授权", parentCode = "dataObjectManage")
//    @ValidateAndLogAnnotation
    public Result dataSetsAuth(@RequestBody DataSetAuthVo dataSetAuthVo) {
        dataSetAuthService.saveBatchDataSetAuth(dataSetAuthVo,false);
        return Result.success();
    }

    @ResponseBody
    @RequestMapping("/saveOneDataSetUserAuth")
    @ApiOperation(value = "单独授权-用户")
    @FuncScanAnnotation(code = "dataObjectManageDataSetsAuthOne", name = "权限设置", parentCode = "dataObjectManage")
//    @ValidateAndLogAnnotation
    public Result saveOneDataSetUserAuth(@RequestBody DataSetAuthVo dataSetAuthVo) {
        dataSetAuthService.saveOneDataSetUserAuth(dataSetAuthVo);
        return Result.success();
    }

    @ResponseBody
    @RequestMapping("/saveOneDataSetRoleAuth")
    @ApiOperation(value = "单独授权-角色")
    public Result saveOneDataSetRoleAuth(@RequestBody DataSetAuthVo dataSetAuthVo) {
        dataSetAuthService.saveOneDataSetRoleAuth(dataSetAuthVo);
        return Result.success();
    }

    @ResponseBody
    @RequestMapping("/cancelDataSetsAuth")
    @ApiOperation(value = "取消授权")
    public Result cancelDataSetsAuth(@RequestBody DataSetAuthVo dataSetAuthVo) {
        dataSetAuthService.deletDataSetAuthRelation(dataSetAuthVo);
        return Result.success();
    }


    /**
     * 按用户组获取所有用户
     *
     * @return
     */
    @ResponseBody
    @RequestMapping("/getAllUserAuth")
    @ApiOperation(value = "按用户组获取所有用户，数据源使用")
    public Result getAllUserAuth(@RequestBody String dataSetId) {
        List<String> ids = userService.getAllUserAuth(dataSetId);
        return Result.success(ids);
    }


    /**
     * 查询所有角色
     *
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/queryAllRoleAuth", method = RequestMethod.POST)
    @ApiOperation(value = "查询所有角色,数据源使用")
    public Result queryAllRoleAuth(@RequestBody String dataSetId) {
        List<String> ids = roleService.queryAllRoleAuth(dataSetId);
        return Result.success(ids);
    }

    @ResponseBody
    @RequestMapping(value = "/setLibraryUserRelation", method = RequestMethod.POST)
    @ApiOperation(value = "设置库与用户关系")
    public Result setLibraryUserRelation(@RequestBody Map dataMap) {
        String userId = (String) dataMap.get("userId");
        String libraryId = (String) dataMap.get("libraryId");
        dataSetAuthService.saveLibraryUserRelation(userId, libraryId);
        return Result.success();
    }


    @ResponseBody
    @RequestMapping("/addDCThreeDataSetAuth")
    @ApiOperation(value = "3.0添加逻辑数据集授权")
    public Result addDCThreeDataSetAuth(@RequestBody DataSetAuthVo dataSetAuthVo) {
        dataSetAuthService.addDCThreeDataSetAuth(dataSetAuthVo);
        return Result.success();
    }

    @ResponseBody
    @RequestMapping("/dataDCThreeSetsAuth")
    @ApiOperation(value = "3.0逻辑数据集批量授权")
    public Result dataDCThreeSetsAuth(@RequestBody DataSetAuthVo dataSetAuthVo) {
        dataSetAuthService.saveDCThreeBatchDataSetAuth(dataSetAuthVo,false);
        return Result.success();
    }

    @ResponseBody
    @RequestMapping("/cancelDCThreeDataSetsAuth")
    @ApiOperation(value = "3.0逻辑数据集取消授权")
    public Result cancelDCThreeDataSetsAuth(@RequestBody DataSetAuthVo dataSetAuthVo,HttpServletRequest request) {
        String userId = (String) request.getSession().getAttribute("userId");
        dataSetAuthService.deleteDCThreeDataSetAuthRelation(dataSetAuthVo,userId);
        return Result.success();
    }

    @ResponseBody
    @RequestMapping("/dataSetDCThreeAuthRegister")
    @ApiOperation(value = "3.0 添加数据源到功能表")
    public Result dataSetDCThreeAuthRegister(@RequestBody DataSetAuthVo dataSetAuthVo, HttpServletRequest request) {
        String userId = (String) request.getSession().getAttribute("userId");
        if ("serviceManage".equals(dataSetAuthVo.getType())){
            String userType = "";
            List<String> userIds = new ArrayList<>();
            if (!dataSetAuthVo.getRoleVos().isEmpty()){
                userType = "1";
                List<RoleVo> roleVos = dataSetAuthVo.getRoleVos();
                for (RoleVo roleVo : roleVos) {
                    userIds.add(roleVo.getRoleId());
                }
            }else if (!dataSetAuthVo.getUserVos().isEmpty()){
                userType = "0";
                List<UserVo> userVos = dataSetAuthVo.getUserVos();
                for (UserVo userVo : userVos) {
                    userIds.add(userVo.getId());
                }
            }
            if ((dataSetAuthVo.getRoleVos().size() <= 0 && dataSetAuthVo.getUserVos().size() <= 0) || dataSetAuthVo.getDataObjectVos().size() <= 0){
                throw new RuntimeException("分享对象和资源不能为空！");
            }
            List<DataObjectVo> dataObjectVos = dataSetAuthVo.getDataObjectVos();
            List<String> resourceIds = new ArrayList<>();
            for (DataObjectVo dataObjectVo : dataObjectVos) {
                resourceIds.add(dataObjectVo.getCode());
            }
            String id = resourceIds.get(0);
            ServiceMeta serviceMeta = serviceMetaService.queryByMetaId(id);
            String serviceType = serviceMeta.getServicePublication().getServiceType();
            EnumServiceType instanceByCode = EnumServiceType.getInstanceByCode(serviceType);
            String resourceType = ResourceTypeEnum.DATA_SERVICE.getName();
            if (instanceByCode == EnumServiceType.ALGORITHM_SERVICE){
                resourceType =  ResourceTypeEnum.AI_SERVICE.getName();
            }else if (instanceByCode == EnumServiceType.CALCULATION_SERVICE){
                resourceType =  ResourceTypeEnum.MODEL_SERVICE.getName();
            }else if (instanceByCode == EnumServiceType.COMPARE_SERVICE){
                resourceType =  ResourceTypeEnum.COMPARE_SERVICE.getName();
            }else if (instanceByCode == EnumServiceType.INFORMATION_VERFICATION){
                resourceType =  ResourceTypeEnum.INFORMATION_VERFICATION.getName();
            }else if (instanceByCode == EnumServiceType.DATA_COLLISION){
                resourceType =  ResourceTypeEnum.DATA_COLLISION.getName();
            }

            shareManagementService.addShareByCondition(userType,userIds,resourceType,resourceIds,userId);

        }else {
            dataSetAuthService.saveDataSetAuthDCThree(dataSetAuthVo.getDataObjectVos(),userId);
        }
        return Result.success();
    }
    @ResponseBody
    @RequestMapping("/queryDataSetTableAllByUserOrRole")
    @ApiOperation(value = "3.0 取消授权的时候获取用户或角色的逻辑表")
    public Result queryDataSetTableAllByUserOrRole(@RequestBody Map dataMap, HttpServletRequest request) {
        List<String> ids = (List<String>) dataMap.get("ids");
        List<String> datasetIds = new ArrayList<>();
        for (String id : ids) {
            Map<String, String> params = Maps.newHashMap();
            params.put("obj_id", id);
            List<TSysAuthObjFunc> tSysAuthObjFuncs = sysAuthObjFuncService.queryList(params);
            List<String> tSysAuthObjFuncsId = new ArrayList<>();
            if (null != tSysAuthObjFuncs && 0 != tSysAuthObjFuncs.size()) {
                tSysAuthObjFuncsId = tSysAuthObjFuncs.stream().filter(s->s.gettSysFuncBase()!=null&&s.gettSysFuncBase().getFuncType().equals("1")).map(s -> s.gettSysFuncBase().getFuncCode()).collect(Collectors.toList());
            }
            datasetIds.addAll(tSysAuthObjFuncsId);
        }
        List<ClassfierStatVo> result = new ArrayList<>();
        for (String datasetId : datasetIds) {
            if(dataSetAuthService.checkLogicDataObj(datasetId)){
                ClassifierStat classifierStatObj = classifierService.getClassifierStatObj(datasetId);
                ClassfierStatVo classfierStatVo=new ClassfierStatVo();
                classfierStatVo.setId(classifierStatObj.getId());
                classfierStatVo.setName(classifierStatObj.getName());
                classfierStatVo.setCode(classifierStatObj.getCode());
                classfierStatVo.setDb_type(classifierStatObj.getDbType());
                result.add(classfierStatVo);
            }
        }
        return Result.toResult(R.ok(result));
    }


}
