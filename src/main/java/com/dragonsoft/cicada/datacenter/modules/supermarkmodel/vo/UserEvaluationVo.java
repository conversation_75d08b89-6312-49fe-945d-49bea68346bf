package com.dragonsoft.cicada.datacenter.modules.supermarkmodel.vo;

import lombok.Data;

@Data
public class UserEvaluationVo {

    /**
     * 被评价者名
     */
    private String appraisedUserName;
    /**
     * 被评价者电话
     */
    private String appraisedUserTelphone;
    /**
     * 被评价者邮箱
     */
    private String appraisedUserEmail;

    /**
     * 建模数量
     */
    private int modelCount;
    /**
     * 下载次数
     */
    private int modelDowloadCount;
    /**
     * 使用次数
     */
    private int modelUseCount;
    /**
     * 点赞量
     */
    private int modelUpvoteCount;
    /**
     * 模型评价好坏
     */
    private Float modelEvaluationScore;
    /**
     * 建模速度
     */
    private int modelCreateSpeed;



    /**
     * 登录次数
     */
    private int loginCount;
    /**
     * 使用模型数量
     */
    private int userModelSpeed;
    /**
     * 使用模型频次
     */
    private int modelCreateFrequency;
    /**
     * 需求提交量
     */
    private int needSubmitCount;
    /**
     * 需求拒绝量
     */
    private int needRejectCount;
    /**
     * 需求通过量
     */
    private int needPassCount;
    /**
     * 评论量
     */
    private int commentCount;

    /**
     * 申报战果量
     */
    private int declaredBattleCount;


}
