package com.dragonsoft.cicada.datacenter.modules.modeling.charts;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.code.common.utils.assertion.Assert;

import java.util.*;

/**
 * 需求只有两个维度
 *
 * <AUTHOR>
 * @Description
 * @Date 2021/3/4 10:10
 */
public abstract class BaseMoreDimension implements IPreviewChartsModelCreator {

    /**
     * 要做数据集的转换
     * 原数据集
     * GROUP    DIMENSION       COUNT
     * 1-1        访问用户          1
     * 1-1        下单用户          2
     * 1-2        访问用户          2
     * 1-3        下单用户          3
     * 转换后
     * GROUP    访问用户       下单用户
     * 1-1      1               2
     * 1-2      2               0
     * 1-3      0               3
     *
     * @param json    执行sql返回的json对象
     * @param columns columns第一个字段为分组字段，最后一个字段为count字段，
     *                中间所有字段为维度字段，多个维度情况还未考虑，需要和前端页面共同考虑
     *                所以目前columns最多只有三个，最少有两个，除了这情况为异常情况
     * @return
     */
    @Override
    public PreviewChartsModel create(JSONArray json, List<String> columns) {
        Assert.isTrue(columns.size() == 2 || columns.size() == 3, String.format("列数异常%d，检查sql是否正确", columns.size()));
        List rows;
        //只有一个维度
        if (columns.size() == 2) {
            //对分类字段的列进行排序
            List<String> finalColumns = columns;
            json.sort((row1, row2) -> compareMap((Map<String, Object>)row1, (Map<String, Object>)row2, finalColumns.get(0)));
            return new PreviewChartsModel(columns, json);
        } else {
            //两个维度，多个维度情况还未考虑，需要和前端页面共同考虑
            String groupColumn = columns.get(1);
            String dimensionColumn = columns.get(0);
            String countColumn = columns.get(2);
            Set<String> dimensionValues = new HashSet<>();
            Set<String> groupsValues = new HashSet<>();
            for (int i = 0; i < json.size(); i++) {
                JSONObject jsonObject = json.getJSONObject(i);
                dimensionValues.add(jsonObject.getString(dimensionColumn));
                Object o = jsonObject.get(groupColumn);
                groupsValues.add(o == null ? "" : o.toString());
            }
            rows = rowColumn(json, groupColumn, dimensionColumn, countColumn, dimensionValues);
            sortRows(rows, groupColumn);
            columns = new ArrayList<>();
            columns.add(groupColumn);
            //对分类字段的列进行排序
            List<String> classColumnValues = new ArrayList<>(dimensionValues);
            classColumnValues.sort(String::compareToIgnoreCase);
            columns.addAll(classColumnValues);

        }
        return new PreviewChartsModel(columns, rows);

    }

    private void sortRows(List<Map<String, Object>> rows, String groupColumn) {
        rows.sort((row1, row2) -> compareMap(row1, row2, groupColumn));
    }

    private int compareMap(Map<String, Object> row1, Map<String, Object> row2, String groupColumn) {
        Object o1 = row1.get(groupColumn);
        Object o2 = row2.get(groupColumn);
        if (o1 == null && o2 == null) {
            return 0;
        }
        if (o1 == null && o2 != null) {
            return -1;
        }
        if (o1 != null && o2 == null) {
            return 1;
        }
        return o1.toString().compareToIgnoreCase(o2.toString());
    }

    /**
     * @param array             原数据
     * @param groupColumn       分组字段
     * @param classColumn       分类字段
     * @param countColumn       count字段
     * @param classColumnValues 分类字段的值
     * @return
     */
    private List<Map<String, Object>> rowColumn(JSONArray array, String groupColumn, String classColumn, String countColumn, Set<String> classColumnValues) {
        if (array.size() == 0) {
            return Collections.EMPTY_LIST;
        }
        //存放分组字段值和行的关系，一个分组字段只能在一个行里
        Map<String, Map<String, Object>> groupColumnRowMap = new HashMap<>();
        List<Map<String, Object>> rows = new ArrayList<>();
        String columnValue = array.getJSONObject(0).getString(groupColumn);
        Map<String, Object> row = null;
        //遍历所有数据
        for (int i = 0; i < array.size(); i++) {
            JSONObject thisObject = array.getJSONObject(i);
            String currColumnValues = thisObject.getString(groupColumn);
            //该分组字段值没出现过，创建新行
            if (groupColumnRowMap.containsKey(currColumnValues)) {
                row = groupColumnRowMap.get(currColumnValues);
            } else {
                row = new HashMap<>();
                groupColumnRowMap.put(currColumnValues, row);
                rows.add(row);
                row.put(groupColumn, array.getJSONObject(i).getString(groupColumn));
            }
            //找到当前字段的count值
            for (String field : classColumnValues) {
                if (thisObject.getString(classColumn).equals(field)) {
                    row.put(field, thisObject.getString(countColumn));
                }
            }
        }
        return rows;
    }
}
