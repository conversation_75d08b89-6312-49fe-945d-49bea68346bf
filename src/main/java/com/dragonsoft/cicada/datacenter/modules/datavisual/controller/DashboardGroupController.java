package com.dragonsoft.cicada.datacenter.modules.datavisual.controller;


import com.code.common.utils.R;
import com.code.common.utils.StringUtils;
import com.code.metadata.datavisual.DashboardGroup;
import com.code.metaservice.business.directory.BusiClassifyService;
import com.code.metaservice.datavisual.IDashboardGroupService;
import com.dragoninfo.dfw.bean.Result;
import com.dragonsoft.cicada.datacenter.common.token.GlobalConstant;
import com.dragonsoft.cicada.datacenter.common.utils.UserContextUtil;
import com.dragonsoft.cicada.datacenter.modules.datavisual.dashboard.IDashboardGroupBuilder;
import com.dragonsoft.cicada.datacenter.modules.datavisual.model.enums.ErrorCodeEnum;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.service.IFunctionService;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.service.IUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@CrossOrigin
@RestController
@PropertySource("classpath:case-config.properties")
@RequestMapping("/dashboardGroup")
public class DashboardGroupController {

    @Autowired
    IDashboardGroupBuilder dashboardGroupBuilder;
    @Autowired
    IDashboardGroupService dashboardGroupService;

    @Value("${sceModel}")
    private boolean sceModel;

    @Value("${standModel}")
    private boolean standModel;

    @Autowired
    private IUserService userService;


    @Autowired
    private BusiClassifyService busiClassifyService;

    @Autowired
    private IFunctionService functionService;

    @GetMapping("/list")
    public Result getGroups(HttpServletRequest request) {
        Map<String, List<DashboardGroup>> map = new HashMap<>();
        String userId = UserContextUtil.getUserIdByHttpRequest(request);
        List<DashboardGroup> myList = dashboardGroupBuilder.builderGroup(userId);
        List<DashboardGroup> allList = dashboardGroupBuilder.builderGroupNoMy(userId);
        List<DashboardGroup> caseList = dashboardGroupBuilder.buliderGroupCase(userId);
        map.put("myList", myList);

        map.put("allList", allList);

        String s = dashboardGroupService.queryGroupStandardUser();
        if (userId.equals(s)) {
            List<DashboardGroup> shareList = dashboardGroupBuilder.builderGroupStandard();
            map.put("shareList", standModel ? shareList : null);
        }
        if (userService.isAdmin(userId)&&functionService.isAuthToAdmin()) {
            map.put("otherList", dashboardGroupBuilder.builderPersonalGroup(userId));
        }

        map.put("caseList", sceModel && GlobalConstant.UserProperties.DC_SUPER_ID.equals(userId) ? caseList : null);
        return Result.toResult(R.ok(map));
    }


    @PostMapping("/addOrUpdate")
    public Result addOrUpdateGroup(@RequestBody Map map, HttpServletRequest request) {
        String userId = (String) request.getSession().getAttribute("userId");
        String parentId = (String) map.get("parentId");
        String name = (String) map.get("name");
        String id = (String) map.get("id");
        if (StringUtils.isBlank(name)) {
            return Result.toResult(R.error("名称不能为空"));
        }
        if (null != id && id.equals(parentId)) {
            return Result.toResult(R.error("上级id不能等于parentId"));
        }

        boolean checkName = dashboardGroupBuilder.checkName(name, id, userId);
        if (checkName) {
            return Result.toResult(R.error(ErrorCodeEnum.DASHBOARD_GROUP_CHECK_ERROR.getCode(), ErrorCodeEnum.DASHBOARD_GROUP_CHECK_ERROR.getMsg()));
        }
        DashboardGroup ds = dashboardGroupBuilder.addOrUpdateGroup(parentId, name, id, userId);
        return Result.toResult(R.ok(ds));
    }

    @PostMapping("/deleteById")
    public Result deleteGroup(String id) {
        dashboardGroupBuilder.deleteGroup(id);
        return Result.toResult(R.ok());
    }
}
