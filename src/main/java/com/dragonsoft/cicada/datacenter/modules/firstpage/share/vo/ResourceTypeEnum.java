package com.dragonsoft.cicada.datacenter.modules.firstpage.share.vo;


import com.code.common.utils.StringUtils;

/**
 * 分享的资源
 */
public enum ResourceTypeEnum {
    DATA_SET("dataSet", "1","数据集"),
    DATA_SERVICE("dataService", "4","数据查询"),
    DASHBOARD("dashboard", "5","仪表盘"),
    AI_SERVICE("aiService", "6","AI建模"),
    MODEL_SERVICE("modelService", "7","模型分析"),
    COMPARE_SERVICE("compareService","8","比对订阅"),
    INFORMATION_VERFICATION("informationVerification","9","信息核查"),
    DATA_COLLISION("dataCollision","10","数据碰撞");

    private String name;
    private String code;
    private String chName;

    public String getChName() {
        return chName;
    }

    public void setChName(String chName) {
        this.chName = chName;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    private ResourceTypeEnum(String name, String code) {
        this.name = name;
        this.code = code;
    }

    ResourceTypeEnum(String name, String code, String chName) {
        this.name = name;
        this.code = code;
        this.chName = chName;
    }

    public static ResourceTypeEnum getInstanceByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        } else {
            ResourceTypeEnum[] var1 = values();
            int var2 = var1.length;

            for(int var3 = 0; var3 < var2; ++var3) {
                ResourceTypeEnum value = var1[var3];
                if (value.getCode().equals(code)) {
                    return value;
                }
            }

            return null;
        }
    }

    public static String getCodeByIgnoreName(String name){
        if (StringUtils.isBlank(name)) {
            return null;
        } else {
            ResourceTypeEnum[] var1 = values();
            int var2 = var1.length;

            for(int var3 = 0; var3 < var2; ++var3) {
                ResourceTypeEnum value = var1[var3];
                if (value.getName().equalsIgnoreCase(name)) {
                    return value.getCode();
                }
            }

            return null;
        }
    }
}
