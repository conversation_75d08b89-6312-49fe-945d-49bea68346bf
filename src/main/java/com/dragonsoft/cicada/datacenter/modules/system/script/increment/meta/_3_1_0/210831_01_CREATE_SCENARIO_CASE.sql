/*==============================================================*/
/* Table: T_MD_SCENARIO_CASE                                    */
/*==============================================================*/
CREATE TABLE T_MD_SCENARIO_CASE (
    ID                   VARCHAR(32)     UNIQUE NOT NULL,
   CASE_ID              VARCHAR(32)       UNIQUE  NOT NULL,
   CASE_URL             TEXT                 NULL,
   CONSTRAINT PK_T_MD_SCENARIO_CASE PRIMARY KEY (ID)
)
INHERITS (T_MD_ELEMENT);

COMMENT ON COLUMN T_MD_SCENARIO_CASE.CASE_ID IS
'案例id';


/*==============================================================*/
/* Table: T_MD_SCENARIO_CASE_TYPE                               */
/*==============================================================*/
CREATE TABLE T_MD_SCENARIO_CASE_TYPE (
   ID                   VARCHAR(32)     UNIQUE NOT NULL,
   PARENT_ID            VARCHAR(32)         NULL,
   CONSTRAINT PK_T_MD_SCENARIO_CASE_TYPE PRIMARY KEY (ID)
)
INHERITS (T_MD_ELEMENT);

ALTER TABLE T_MD_SCENARIO_CASE_TYPE
   ADD CONSTRAINT FK_T_MD_SCE_REFERENCE_T_MD_SCE_TY FOREIGN KEY (PARENT_ID)
      REFERENCES T_MD_SCENARIO_CASE_TYPE (ID)
      ON DELETE RESTRICT ON UPDATE RESTRICT;

/*==============================================================*/
/* Table: T_MD_SCENARIO_CASE_RELATION                           */
/*==============================================================*/
CREATE TABLE T_MD_SCENARIO_CASE_RELATION (
    ID                  VARCHAR(32)          NOT NULL,
   SCENARIO_CASE_ID     VARCHAR(32)          NULL,
   CASE_TYPE_ID         VARCHAR(32)          NULL,
   CONSTRAINT PK_T_MD_SCENARIO_CASE_REL PRIMARY KEY (ID)
)
INHERITS (T_MD_ELEMENT);

COMMENT ON COLUMN T_MD_SCENARIO_CASE_RELATION.SCENARIO_CASE_ID IS
'案例id';

COMMENT ON COLUMN T_MD_SCENARIO_CASE_RELATION.CASE_TYPE_ID IS
'场景（模型，可视化，门户）';

ALTER TABLE T_MD_SCENARIO_CASE_RELATION
   ADD CONSTRAINT FK_T_MD_SCE_REFERENCE_T_MD_SCE FOREIGN KEY (scenario_case_id)
      REFERENCES T_MD_SCENARIO_CASE (ID)
      ON DELETE RESTRICT ON UPDATE RESTRICT;

ALTER TABLE T_MD_SCENARIO_CASE_RELATION
   ADD CONSTRAINT FK_T_MD_SCE_REFERENCE_T_MD_SCE_TY FOREIGN KEY (CASE_TYPE_ID)
      REFERENCES T_MD_SCENARIO_CASE_TYPE (ID)
      ON DELETE RESTRICT ON UPDATE RESTRICT;

