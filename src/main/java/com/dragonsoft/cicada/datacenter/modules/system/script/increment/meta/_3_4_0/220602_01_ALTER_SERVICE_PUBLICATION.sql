ALTER TABLE t_md_service_publication add COLUMN is_multilist varchar(2);


CREATE TABLE IF NOT EXISTS T_MD_RESOURCE_RELATION (
   id                            VARCHAR(32)          NOT NULL,
   resource_id                   VARCHAR(32)          NOT NULL,
   service_id                    VARCHAR(32)          NOT NULL
);



CREATE TABLE IF NOT EXISTS T_MD_RESOURCE_CONFIG (
   id                            VARCHAR(32)          NOT NULL,
   join_exp                   TEXT                   NULL,
   conditon_exp               TEXT                  NULL,
   service_id                 VARCHAR(32)          NULL
);

CREATE TABLE IF NOT EXISTS T_MD_MANY_RESOURCE_CONFIG (
   left_resource_id           VARCHAR(32)          NULL,
   right_resource_id          VARCHAR(32)          NULL,
   join_sort                  VARCHAR(32)          NULL
)
INHERITS (T_MD_RESOURCE_CONFIG);