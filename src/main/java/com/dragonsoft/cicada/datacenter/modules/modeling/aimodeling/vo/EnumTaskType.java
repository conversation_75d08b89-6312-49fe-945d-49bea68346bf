package com.dragonsoft.cicada.datacenter.modules.modeling.aimodeling.vo;

/**
 * <AUTHOR>
 * @description：任务类型枚举
 * @date ：2021/10/9 11:04
 */
public enum EnumTaskType{
    Regress("回归", "Regress"),
    Cluster("聚类", "Cluster"),
    Rank("排序", "Rank"),
    Classify("分类", "Classify");


    EnumTaskType(String name, String code){
        this.name = name;
        this.code = code;
    }

    public static EnumTaskType getInstanceByCode(String code) {
        for (EnumTaskType value : EnumTaskType.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }

    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
