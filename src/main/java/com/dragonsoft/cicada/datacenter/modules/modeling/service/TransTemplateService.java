package com.dragonsoft.cicada.datacenter.modules.modeling.service;

import com.code.metadata.business.directory.BaseBusiClassify;
import com.code.metadata.datavisual.DashboardGroup;
import com.code.metadata.etl.trans.TransMeta;
import com.code.metadata.res.ddl.LogicDataObj;
import com.code.metadata.udf.management.UdfGraph;
import com.code.metadata.variable.TransVariable;
import com.code.metadata.variable.TransVariableRelation;
import com.code.metaservice.ddl.vo.LogicDataObjInfo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Wu.D.J
 * @Create : 2020.05.12
 */
public interface TransTemplateService {

    void saveSQL(String sql);

    void sqlRun(String sql);

    BaseBusiClassify queryModelClassify(String code, String pId);

    BaseBusiClassify queryClassify(String code);

    DashboardGroup queryGroup(String code);

    String queryExistDashboardGroup(String pId, String name);

    String saveTransModelBySQL(TransMeta oldTransMeta, Map<String, UdfGraph> graphs, BaseBusiClassify classify);

    void createTemplateView(String objIds) throws Exception;

    List<String> getLogicIdByTransMeta(TransMeta transMeta);

    String buildTransMetaSQL(TransMeta transMeta);

    void createDataObjView();

    void createViewByLogicInfo(LogicDataObjInfo logic, String dbSchema);
    void createViewByLogicInfo(LogicDataObjInfo logic, Map<String, LogicDataObj> classifyMap, String dbSchema);

    /**
     * 模型导入时，sql添加schemaName
     * @param dbSchema
     * @param sql
     * @return
     */
    public String addSchemaNameToTableName(String dbSchema, String sql);

    List<TransVariable> queryAllVariables();

    void mergeObj(Object obj);

    List<TransVariableRelation> queryAllVariablesRelations();

    Map<String,String> queryTransByVariableId(List<String> variableIds);

    List<Map<String,String>> queryTransByCodes(List<String> transMetaCodes);

    String saveTransModelBySQL(TransMeta oldTransMeta, Map<String, UdfGraph> graphs, BaseBusiClassify classify,boolean idRefresh);

    Map findDatasourceByRdbId(String rdbId);

    Map<String,Object> getSchemaByRdbId(String rdbId);
}
