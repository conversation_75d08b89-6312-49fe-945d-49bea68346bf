package com.dragonsoft.cicada.datacenter.modules.dataplan.apitest.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.code.common.paging.PageInfo;
import com.code.common.utils.StringUtils;
import com.code.common.utils.assertion.Assert;
import com.code.metadata.business.directory.BaseBusiClassify;
import com.code.metadata.sm.EnumServiceType;
import com.code.metadata.sm.ServiceMeta;
import com.code.metadata.sm.ServicePublication;
import com.code.metadata.usecase.UseCase;
import com.code.metadata.usecase.UseCaseApi;
import com.code.metadata.usecase.UseCaseApiInfo;
import com.code.metaservice.business.directory.BusiClassifyService;
import com.code.metaservice.sm.IServicePublicationService;
import com.code.metaservice.usecase.UseCaseService;
import com.dragonsoft.cicada.datacenter.modules.common.service.CommonBusiClassifyService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.apitest.service.UseCaseManageService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.apitest.vo.UseCaseVo;
import com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.service.IServiceManagementService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.vo.ParamConfigVo;
import com.dragonsoft.cicada.datacenter.modules.modeling.aimodeling.config.AiModelIpAndPort;
import com.fw.service.BaseService;
import com.fw.service.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class UseCaseManageServiceImpl extends BaseService implements UseCaseManageService {


    @Autowired
    private CommonBusiClassifyService commonBusiClassifyService;

    @Autowired
    private UseCaseService useCaseService;

    @Autowired
    private IServiceManagementService serviceManagementService;

    @Autowired
    private BusiClassifyService busiClassifyService;

    @Autowired
    private IServicePublicationService servicePublicationService;

    @Value("${dc.publish.path}")
    private String testPublishPath;

    @Resource
    private AiModelIpAndPort aiModelIpAndPort;


    @Override
    public PageInfo getUseCasePage(String classifyId, String name, Integer pageSize, Integer pageIndex) {
        Assert.hasLength(classifyId,"目录id不能为空！");
        PageInfo page = new PageInfo();
        page.setPageIndex(pageIndex);
        page.setPageSize(pageSize);
        List<String> classifyIds = commonBusiClassifyService.queryBusiClassifyIdList(classifyId);
        List<String> elementIdsByClassifyIds = commonBusiClassifyService.getElementIdsByClassifyIds(classifyIds);
        PageInfo pageInfo = useCaseService.getUseCaseByIdsAndConditionPage(elementIdsByClassifyIds, name, page);
        List<Map<String,Object>> dataList = pageInfo.getDataList();
        if (CollectionUtil.isNotEmpty(dataList)) {
            List<Map<String,Object>> result = new ArrayList<>();
            for (Map<String, Object> map : dataList) {
                String id = (String) map.get("id");
                String classifyIdByElementId = commonBusiClassifyService.getClassifyIdByElementId(id);
                BaseBusiClassify busiClassifyBy = busiClassifyService.findBusiClassifyBy(classifyIdByElementId);
                String allPathString = getAllPathString(busiClassifyBy);
                map.put("path",allPathString);
                result.add(map);
            }
            pageInfo.setDataList(result);
        }
        return pageInfo;
    }

    private String getAllPathString(BaseBusiClassify baseBusiClassify){
        StringBuilder sb = new StringBuilder();
        sb.append(baseBusiClassify.getName());
        BaseBusiClassify parentBc = baseBusiClassify.getParentBc();
        if (parentBc != null){
            sb.insert(0,getAllPathString(parentBc)+"/");
        }
        return sb.toString();
    }


    @Override
    public void saveUseCase(UseCaseVo useCaseVo,String userId) {
        String classifyId = useCaseVo.getClassifyId();
        String memo = useCaseVo.getMemo();
        String useCaseName = useCaseVo.getUseCaseName();

        Assert.hasLength(useCaseName,"用例名称不能为空！");

        String useCaseId = useCaseVo.getUseCaseId();

        //useCaseService.checkTheSameName(useCaseName,useCaseId,userId);
        checkTheSameName(useCaseId,useCaseName,classifyId);
        List<UseCaseVo.UseCaseApiVo> useCaseApis = useCaseVo.getUseCaseApis();
        UseCase useCase = new UseCase();
        if (StrUtil.isNotEmpty(useCaseId)) {
            useCase = useCaseService.getUseCaseById(useCaseId);
            useCaseService.delete(useCase.getUseCaseApis());
        }
        useCase.setName(useCaseName);
        useCase.setCode(useCaseName);
        useCase.setCaseType("service_case");
        useCase.setMemo(memo);
        useCase.setOperateUserId(userId);

        Set<UseCaseApi> useCaseApiSet = new HashSet<>();
        for (UseCaseVo.UseCaseApiVo useCaseApiVo : useCaseApis) {
            UseCaseApi api = new UseCaseApi();
            api.setCode(StringUtils.uuid());
            api.setApiId(useCaseApiVo.getServicePublicationId());
            api.setApiType(useCaseApiVo.getApiType());
            api.setUseCase(useCase);
            List<UseCaseVo.UseCaseApiParam> useCaseApiParams = useCaseApiVo.getUseCaseApiParams();
            Set<UseCaseApiInfo> useCaseApiInfos = new HashSet<>();
            for (UseCaseVo.UseCaseApiParam useCaseApiParam : useCaseApiParams) {
                String parentParamId = useCaseApiParam.getParentParamCode();
                List<Map<String, Object>> params = useCaseApiParam.getParams();
                UseCaseApiInfo parent = new UseCaseApiInfo();
                parent.setUseCaseApi(api);
                parent.setCode(StringUtils.uuid());
                parent.setParamKey(parentParamId);
                Set<UseCaseApiInfo> children = buildChildrenUseCaseApiParams(params,api);
                parent.setChildUseCaseParams(children);
                useCaseApiInfos.add(parent);

            }
            api.setUseCaseApiInfos(useCaseApiInfos);
            useCaseApiSet.add(api);
        }
        useCase.setUseCaseApis(useCaseApiSet);
        useCaseService.saveOrUpdateUseCase(useCase);
        if (StrUtil.isEmpty(useCaseVo.getUseCaseId())) {
            Assert.hasLength(classifyId,"目录id不能为空！");
            commonBusiClassifyService.addElementToClassify(useCase.getId(),classifyId);
        } else {
            if (StrUtil.isNotEmpty(classifyId)) {
                commonBusiClassifyService.moveElementToClassify(useCaseId,classifyId);
            }
        }
    }


    private Set<UseCaseApiInfo> buildChildrenUseCaseApiParams(List<Map<String, Object>> params,UseCaseApi api) {
        Set<UseCaseApiInfo> useCaseApiInfos = new HashSet<>();
        int index = 0;
        for (Map<String, Object> param : params) {
            for (Map.Entry<String, Object> entry : param.entrySet()) {
                UseCaseApiInfo info = new UseCaseApiInfo();
                info.setCode(StringUtils.uuid());
                info.setParamKey(entry.getKey());
                info.setParamValue((String) entry.getValue());
                info.setGroupBy(index + "");
                info.setUseCaseApi(api);
                useCaseApiInfos.add(info);
            }
            index++;
        }
        return useCaseApiInfos;
    }

    @Override
    public List<UseCaseVo> getImportApiInfo(List<String> caseIds) {
        /*List<String> serviceMetaIds = new ArrayList<>();
        Map<String,Object> serviceIdParamMap = new HashMap<>();
        for (String caseId : caseIds) {
            UseCase useCase = useCaseService.getUseCaseById(caseId);
            Set<UseCaseApi> useCaseApis = useCase.getUseCaseApis();
            if (CollectionUtil.isNotEmpty(useCaseApis)) {
                for (UseCaseApi useCaseApi : useCaseApis) {
                    String servicePublicationId = useCaseApi.getApiId();
                    String serviceMetaId = getServiceMetaIdByServiceId(servicePublicationId);
                    serviceMetaIds.add(serviceMetaId);
                    Set<UseCaseApiInfo> useCaseApiInfos = useCaseApi.getUseCaseApiInfos();
                    Map<String,String> useCaseApiInfoMap = new HashMap<>();
                    for (UseCaseApiInfo useCaseApiInfo : useCaseApiInfos) {
                        useCaseApiInfoMap.put(useCaseApiInfo.getParamKey(),useCaseApiInfo.getParamValue());
                    }
                    serviceIdParamMap.put(servicePublicationId,useCaseApiInfoMap);
                }
            }
        }
        List<ParamConfigVo> paramConfigVos = new ArrayList<>();
        for (String serviceMetaId : serviceMetaIds) {
            try {
                ParamConfigVo configVo = serviceManagementService.queryServiceDetails(serviceMetaId);
                List<ParamConfigVo.Param> paramList = configVo.getParamList();
                replaceDefaultValueByCase(configVo.getServiceId(),serviceIdParamMap,paramList);
                paramConfigVos.add(configVo);
            } catch (Exception e) {
             log.error(e.getMessage(),e);
            }

        }
        return paramConfigVos;*/
        List<UseCaseVo>  useCaseVos = new ArrayList<>();
        for (String caseId : caseIds) {
            UseCaseVo caseVo = getUseCaseDetailById(caseId);
            useCaseVos.add(caseVo);
        }
        return useCaseVos;
    }

    @Override
    public UseCaseVo getUseCaseDetailById(String useCaseId) {
        UseCase useCase = useCaseService.getUseCaseById(useCaseId);
        UseCaseVo useCaseVo = new UseCaseVo();
        useCaseVo.setUseCaseId(useCase.getId());
        useCaseVo.setClassifyId(commonBusiClassifyService.getClassifyIdByElementId(useCaseId));
        useCaseVo.setMemo(useCase.getMemo());
        useCaseVo.setUseCaseName(useCase.getName());
        useCaseVo.setCaseType(useCase.getCaseType());

        List<UseCaseVo.UseCaseApiVo> useCaseApis = new ArrayList<>();
        for (UseCaseApi useCaseApi : useCase.getUseCaseApis()) {
            UseCaseVo.UseCaseApiVo apiVo = new UseCaseVo.UseCaseApiVo();
            apiVo.setApiType(useCaseApi.getApiType());
            String apiId = useCaseApi.getApiId();
            apiVo.setServicePublicationId(apiId);

            ServicePublication servicePublication = servicePublicationService.getServicePublication(apiId);
            ServiceMeta serviceMeta = (ServiceMeta) servicePublication.getServiceMetas().toArray()[0];
            String requestPath;
            if (EnumServiceType.ALGORITHM_SERVICE.getCode().equals(servicePublication.getServiceType())) {
                requestPath = "http://" + aiModelIpAndPort.getRunOpServiceIp() + serviceMeta.getRequestPath();
            } else {
                requestPath = testPublishPath + serviceMeta.getRequestPath();
            }
            apiVo.setServiceType(EnumServiceType.getInstanceByCode(servicePublication.getServiceType()).getName());
            apiVo.setRequestUrl(requestPath);
            apiVo.setApiName(servicePublication.getName());
            apiVo.setApiStatus(servicePublication.getStatus());
            apiVo.setApiStatus(serviceMeta.getStatus());
            List<UseCaseVo.UseCaseApiParam> useCaseApiParams = new ArrayList<>();
            Set<UseCaseApiInfo> useCaseApiInfos = useCaseApi.getUseCaseApiInfos();
            List<UseCaseApiInfo> parent = new ArrayList<>();
            for (UseCaseApiInfo useCaseApiInfo : useCaseApiInfos) {
                if (useCaseApiInfo.getParentParam() == null) {
                    parent.add(useCaseApiInfo);
                }
            }

            for (UseCaseApiInfo info : parent) {
                UseCaseVo.UseCaseApiParam useCaseApiParam = new UseCaseVo.UseCaseApiParam();
                List<Map<String,Object>> params = new ArrayList<>();
                useCaseApiParam.setParentParamCode(info.getParamKey());
                Set<UseCaseApiInfo> childUseCaseParams = info.getChildUseCaseParams();
                if (CollectionUtil.isNotEmpty(childUseCaseParams)) {
                    Map<String, List<UseCaseApiInfo>> groupMap = childUseCaseParams.stream().collect(Collectors.groupingBy(UseCaseApiInfo::getGroupBy));
                    for (String groupKey : groupMap.keySet()) {
                        Map<String,Object> param = new HashMap<>();
                        List<UseCaseApiInfo> apiInfos = groupMap.get(groupKey);
                        for (UseCaseApiInfo apiInfo : apiInfos) {
                            param.put(apiInfo.getParamKey(),apiInfo.getParamValue());
                        }
                        params.add(param);
                    }
                }
                useCaseApiParam.setParams(params);
                useCaseApiParams.add(useCaseApiParam);
            }

            apiVo.setUseCaseApiParams(useCaseApiParams);
            useCaseApis.add(apiVo);
        }
        useCaseVo.setUseCaseApis(useCaseApis);
        return useCaseVo;
    }

    private void replaceDefaultValueByCase(String serviceId, Map<String,Object> serviceIdParamMap, List<ParamConfigVo.Param> paramList) {
        Map<String,String> useCaseApiInfoMap = (Map<String, String>) serviceIdParamMap.get(serviceId);
        for (ParamConfigVo.Param param : paramList) {
            String paramCode = param.getParamCode();
            String paramValue = useCaseApiInfoMap.get(paramCode);
            if (StrUtil.isNotEmpty(paramValue)) {
                param.setDefaultValue(paramValue);
            }
            List<ParamConfigVo.Param> children = param.getChildren();
            if (CollectionUtil.isNotEmpty(children)) {
                replaceDefaultValueByCase(serviceId,serviceIdParamMap,children);
            }
        }
    }


    private String getServiceMetaIdByServiceId(String serviceId) {
        String sql = "select m.id from t_md_service_meta m inner join t_md_service_publication p on p.id = m.service_id and p.id = :serviceId";
        return this.baseDao.sqlQueryForValue(sql,addParam("serviceId",serviceId).param());
    }

    private void checkTheSameName(String id,String name,String classifyId) {
        List<String> elementIds = commonBusiClassifyService.getElementIdsByClassifyIds(Arrays.asList(classifyId));
        if (CollectionUtil.isEmpty(elementIds)) {
            return;
        }
        String sql = "select count(id) from t_md_use_case where name = :name and id in (:elementIds) ";
        Map<String,Object> map = new HashMap<>();
        map.put("name",name);
        map.put("elementIds",elementIds);
        if (StrUtil.isNotEmpty(id)) {
            sql += " and id != :id";
            map.put("id",id);
        }
        String value = this.baseDao.sqlQueryForValue(sql, map);
        Integer count = Integer.valueOf(value);
        if (count >  0) {
            Assert.fail("该目录下存在同名用例，请重新命名！");
        }
    }
}
