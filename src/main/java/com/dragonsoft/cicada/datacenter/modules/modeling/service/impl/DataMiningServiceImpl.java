package com.dragonsoft.cicada.datacenter.modules.modeling.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.db.Db;
import cn.hutool.db.Entity;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.code.common.utils.StringUtils;
import com.code.common.utils.assertion.Assert;
import com.code.metadata.business.directory.BaseBusiClassify;
import com.code.metadata.etl.trans.EnumTransType;
import com.code.metadata.etl.trans.TransAttributeMeta;
import com.code.metadata.etl.trans.TransMeta;
import com.code.metadata.model.core.BaseModelElement;
import com.code.metadata.model.core.ModelElement;
import com.code.metaservice.business.directory.BusiClassifyService;
import com.code.metaservice.etl.trans.TransMetaService;
import com.code.metaservice.variable.ITransVariableService;
import com.code.mlsql.parse.Script;
import com.code.mlsql.utils.GraphParseHelper;
import com.dragonsoft.cicada.datacenter.modules.modeling.aimodeling.config.AiModelIpAndPort;
import com.dragonsoft.cicada.datacenter.modules.modeling.client.MLSQLExecutorClient;
import com.dragonsoft.cicada.datacenter.modules.modeling.client.MLSQLScheduleClient;
import com.dragonsoft.cicada.datacenter.modules.modeling.client.RetrofitManager;
import com.dragonsoft.cicada.datacenter.modules.modeling.qo.UpdateTransQo;
import com.dragonsoft.cicada.datacenter.modules.modeling.service.DataMiningService;
import com.dragonsoft.cicada.datacenter.modules.modeling.service.MLSQLService;
import com.dragonsoft.cicada.datacenter.modules.modeling.util.CronExpression;
import com.dragonsoft.cicada.datacenter.modules.modeling.util.MLSQLConstant;
import com.dragonsoft.cicada.datacenter.modules.modeling.util.MlSqlSparkVersionFilterUtil;
import com.fw.service.BaseService;
import com.fw.service.annotation.Service;
import lombok.extern.slf4j.Slf4j;
import okhttp3.ResponseBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import retrofit2.Response;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.sql.DataSource;
import java.io.IOException;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.ParseException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Wu.D.J
 * @Create : 2020.11.04
 */
@Slf4j
@Service
public class DataMiningServiceImpl extends BaseService implements DataMiningService {

    @Value("${mlsql.schedule.address}")
    private String scheduleServer;

    @Value("${mlsql.executor.address}")
    private String executorServer;

    @Resource(name = "graphParseHelper")
    private GraphParseHelper helper;

    @Autowired
    private BusiClassifyService busiClassifyService;

    @Autowired
    private TransMetaService transMetaService;

    @Autowired
    ITransVariableService transVariableService;

    @Resource
    private DataSource dataSource;

    @Autowired
    private MLSQLService mlsqlService;

    @Autowired
    private AiModelIpAndPort aiModelIpAndPort;

    @Value("${spark.version.use}")
    private String use_spark_version;

    //本地服务调用的地址
    @Value("${dc.publish.path}")
    private String publishPath;

    @Override
    public String createTrans(String transName) {
        TransMeta transMeta = new TransMeta();
        transMeta.setCode(transName);
        transMeta.setName(transName);
        transMeta.setDistributed("0");
        transMeta.setExceptionMode(TransMeta.EnumHandleMode.DEF.getCode());
        transMeta.setEtlInstanceCode("");
        // 获取根目录id
        String dirSql = "SELECT id FROM t_md_busi_dir WHERE code='TRANS_DIR_MF' AND type = 'BusiDir'";
        String dirId = this.baseDao.sqlQueryForValue(dirSql);
        ModelElement element = new ModelElement();
        element.setId(dirId);
        transMeta.setOwner(element);
        transMeta.setTransType(EnumTransType.TRANSFORM.getName());
        this.baseDao.saveOrUpdate(transMeta);

        try {
            String taskId = createTransTask(transMeta.getId(), transMeta.getName());
            createSchedulePlan(transMeta, taskId);
        } catch (SQLException e) {
         log.error(e.getMessage(),e);
        }
        return transMeta.getId();
    }

    @Override
    public void quickTransToProcessTrans(String transId, String busiClassifyId) {

        TransMeta transMeta = (TransMeta) this.baseDao.get(TransMeta.class, transId);
        check("TRANS_DIR_MF", busiClassifyId, transMeta.getName());
        transMeta.setTaskGroup("DEF");
        Map<String, String> map = new HashMap<>();
        map.put("busiClassifyId", busiClassifyId);
        map.put("transId", transMeta.getId());
        this.baseDao.executeSqlUpdate("update t_md_classify_element set busi_classify_id = :busiClassifyId where element_id = :transId", map);
        LocalDateTime minusTwoHours = LocalDateTime.now();
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String timeStr = minusTwoHours.format(dateTimeFormatter);
        transMeta.setOperateTime(timeStr);
        this.baseDao.saveOrUpdate(transMeta);
    }

    @Override
    public void updateTrans(UpdateTransQo updateTransQo) {
        String transId = updateTransQo.getTransId();
        String dirType = updateTransQo.getDirType();
        String classifyId = updateTransQo.getClassifyId();
        String transName = updateTransQo.getTransName();
        String userId = updateTransQo.getUserId();
        String memo = updateTransQo.getMemo();
        Integer version = updateTransQo.getVersion();
        String productionFirm = StrUtil.isNotEmpty(updateTransQo.getProductionFirm()) ? updateTransQo.getProductionFirm() : StrUtil.EMPTY;
        TransMeta transMeta = (TransMeta) this.baseDao.get(TransMeta.class, transId);
        check(dirType, classifyId, transName);
        transMeta.setName(transName);
        transMeta.setCode(transName);
        transMeta.setOperateUserId(userId);
        transMeta.setMemo(memo);
        transMeta.setVersion(version);
        transMeta.setProductionFirm(productionFirm);
        LocalDateTime minusTwoHours = LocalDateTime.now();
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String timeStr = minusTwoHours.format(dateTimeFormatter);
        transMeta.setOperateTime(timeStr);
        this.baseDao.update(transMeta);
        BaseBusiClassify busiClassify = busiClassifyService.findBusiClassifyBy(classifyId);
        if (!busiClassify.getElements().contains(new BaseModelElement(transId))) {
            busiClassify.addElement(new BaseModelElement(transId));
        }
        busiClassifyService.saveBaseBusiClassify(busiClassify);

        try {
            Db.use(dataSource).update(
                    Entity.create()
                            .set("trans_name", transName)
                    ,
                    Entity.create("t_trans_task")
                            .set("trans_id", transId)
            );
        } catch (SQLException e) {
         log.error(e.getMessage(),e);
        }
    }


    private void check(String dirType, String classifyId, String transName) {
        String checkSQL = "SELECT\n" +
                "\td.id\n" +
                "FROM\n" +
                "\tt_md_busi_dir A,\n" +
                "\tt_md_busi_classify b,\n" +
                "\tt_md_classify_element C,\n" +
                "\tt_etl_trans d\n" +
                "WHERE\n" +
                "\tA .code = '" + dirType + "'\n" +
                "AND b.busi_dir_id = A . ID\n" +
                "AND C .busi_classify_id = b. ID\n" +
                "AND C.element_id = d.id\n" +
                "AND b.id='" + classifyId + "'\n" +
                "and d.name= :transName";
        List transList = this.baseDao.sqlQueryForList(checkSQL, addParam("transName", transName).param());
        Assert.isZero(transList.size(), "该名称已存在");
    }

    @Override
    public void updateTransXAndY(String transId, int x, int y) {
        TransMeta transMeta = (TransMeta) this.baseDao.get(TransMeta.class, transId);
        transMeta.setX(x);
        transMeta.setY(y);
        LocalDateTime minusTwoHours = LocalDateTime.now();
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String timeStr = minusTwoHours.format(dateTimeFormatter);
        transMeta.setOperateTime(timeStr);
        this.baseDao.saveOrUpdate(transMeta);
    }

    @Override
    public void updateSchedulePlan(String transId, String scheduleType, String cron, String declare) {
        if (scheduleType.equals("hand")) {
            cron = "";
        } else {
            Assert.hasText(cron, "cron表达式不能为空!");
        }
        int triggerStatus;
        long triggerLastTime;
        long triggerNextTime;
        if (scheduleType.equals("hand")) {
            triggerStatus = 0;
            triggerLastTime = 0L;
            triggerNextTime = 0L;
        } else {
            CronExpression cronExpression = null;
            try {
                cronExpression = new CronExpression(cron);
            } catch (ParseException e) {
             log.error(e.getMessage(),e);
                Assert.fail("Cron表达式解析异常: [" + e.getMessage() + "]");
            }
            triggerStatus = 1;
            triggerLastTime = 0L;
            triggerNextTime = cronExpression.getNextValidTimeAfter(new Date(System.currentTimeMillis() + 5000)).getTime();
        }

        try {
            Db.use(dataSource).update(
                    Entity.create()
                            .set("schedule_type", scheduleType)
                            .set("cron_expression", cron)
                            .set("schedule_declare", declare)
                            .set("trigger_status", triggerStatus)
                            .set("trigger_last_time", triggerLastTime)
                            .set("trigger_next_time", triggerNextTime)
                    ,
                    Entity.create("t_trans_schedule")
                            .set("trans_id", transId)
            );
        } catch (SQLException e) {
         log.error(e.getMessage(),e);
        }
    }

    @Override
    public void updateSchedulePlanWithSubTrans(String transId, String scheduleType, String cron, String declare, List startPrograms) {
        if (scheduleType.equals("hand")) {
            cron = "";
        } else {
            Assert.hasText(cron, "cron表达式不能为空!");
        }
        int triggerStatus;
        long triggerLastTime;
        long triggerNextTime;
        if (scheduleType.equals("hand")) {
            triggerStatus = 0;
            triggerLastTime = 0L;
            triggerNextTime = 0L;
        } else {
            CronExpression cronExpression = null;
            try {
                cronExpression = new CronExpression(cron);
            } catch (ParseException e) {
             log.error(e.getMessage(),e);
                //Assert.fail("Cron表达式解析异常: [" + e.getMessage() + "]");
                Assert.fail("Cron表达式解析异常");
            }
            triggerStatus = 1;
            triggerLastTime = 0L;
            triggerNextTime = cronExpression.getNextValidTimeAfter(new Date(System.currentTimeMillis() + 5000)).getTime();
        }
        updateTransScheduleWithSubtrans(transId, scheduleType, cron, declare, triggerStatus, triggerLastTime, triggerNextTime);
        if (startPrograms.size() != 0) {
            for (Object obj : startPrograms) {
                String subtransId = ((HashMap) obj).get("subtransId").toString();
                int subtransDelayTime = ((HashMap) obj).get("subtransDelayTime") == null ? 10 : Integer.parseInt(((HashMap) obj).get("subtransDelayTime").toString());
                boolean subtransIsenforce = (Boolean) ((HashMap) obj).get("subtransIsenforce");
                updateTransSubTransRelation(transId, subtransId, subtransDelayTime, subtransIsenforce);
            }
        }

    }

    @Override
    public void updateSchedulePlanWithSubTrans(String transId, String scheduleType, String cron, String declare, List startPrograms, String executorServiceName) {
        if (scheduleType.equals("hand")) {
            cron = "";
        } else {
            Assert.hasText(cron, "cron表达式不能为空!");
        }
        int triggerStatus;
        long triggerLastTime;
        long triggerNextTime;
        if (scheduleType.equals("hand")) {
            triggerStatus = 0;
            triggerLastTime = 0L;
            triggerNextTime = 0L;
        } else {
            CronExpression cronExpression = null;
            try {
                cronExpression = new CronExpression(cron);
            } catch (ParseException e) {
             log.error(e.getMessage(),e);
                //Assert.fail("Cron表达式解析异常: [" + e.getMessage() + "]");
                Assert.fail("Cron表达式解析异常");
            }
            triggerStatus = 1;
            triggerLastTime = 0L;
            triggerNextTime = cronExpression.getNextValidTimeAfter(new Date(System.currentTimeMillis() + 5000)).getTime();
        }
        updateTransScheduleWithSubtrans(transId, scheduleType, cron, declare, triggerStatus, triggerLastTime, triggerNextTime, executorServiceName);
        if (startPrograms.size() != 0) {
            for (Object obj : startPrograms) {
                String subtransId = ((HashMap) obj).get("subtransId").toString();
                int subtransDelayTime = ((HashMap) obj).get("subtransDelayTime") == null ? 10 : Integer.parseInt(((HashMap) obj).get("subtransDelayTime").toString());
                boolean subtransIsenforce = (Boolean) ((HashMap) obj).get("subtransIsenforce");
                updateTransSubTransRelation(transId, subtransId, subtransDelayTime, subtransIsenforce);
            }
        }
    }

    public void updateTransScheduleWithSubtrans(String transId, String scheduleType, String cron, String declare, int triggerStatus, long triggerLastTime, long triggerNextTime, String executorServiceName) {
        try {
            Db.use(dataSource).update(
                    Entity.create()
                            .set("schedule_type", scheduleType)
                            .set("cron_expression", cron)
                            .set("schedule_declare", declare)
                            .set("trigger_status", triggerStatus)
                            .set("trigger_last_time", triggerLastTime)
                            .set("trigger_next_time", triggerNextTime)
                            .set("executor_service", executorServiceName)
                    ,
                    Entity.create("t_trans_schedule")
                            .set("trans_id", transId)
            );
        } catch (SQLException e) {
         log.error(e.getMessage(),e);
        }
    }

    public void updateTransScheduleWithSubtrans(String transId, String scheduleType, String cron, String declare, int triggerStatus, long triggerLastTime, long triggerNextTime) {
        try {
            Db.use(dataSource).update(
                    Entity.create()
                            .set("schedule_type", scheduleType)
                            .set("cron_expression", cron)
                            .set("schedule_declare", declare)
                            .set("trigger_status", triggerStatus)
                            .set("trigger_last_time", triggerLastTime)
                            .set("trigger_next_time", triggerNextTime)
                    ,
                    Entity.create("t_trans_schedule")
                            .set("trans_id", transId)
            );
        } catch (SQLException e) {
         log.error(e.getMessage(),e);
        }
    }

    public void updateTransSubTransRelation(String transId, String subtransId, int subtransDelayTime, boolean subtransIsenforce) {
        boolean existSubRelation = isExistSubRelation(transId, subtransId);
        int subtransIsenforced = subtransIsenforce ? 1 : 0;
        String sql = "";
        if (existSubRelation) {
            sql = "update t_trans_subtrans_relation set subtrans_delay_time='" + subtransDelayTime + "',subtrans_isenforce='" + subtransIsenforced + "' where trans_id='" + transId + "' and subtrans_id='" + subtransId + "'";
        } else {
            String id = UUID.randomUUID().toString().replaceAll("-", "");
            sql = "insert into t_trans_subtrans_relation values('" + id + "','" + subtransDelayTime + "',0,'" + subtransId + "','" + subtransIsenforced + "','" + transId + "')";
        }

        this.baseDao.executeSqlUpdate(sql);
    }

    public boolean isExistSubRelation(String transId, String subtransId) {
        String sql = "select count(1) as num from t_trans_subtrans_relation where trans_id='" + transId + "' and subtrans_id='" + subtransId + "'";
        List list = this.baseDao.sqlQueryForList(sql);
        return ((HashMap) list.get(0)).get("num").toString().equals("0") ? false : true;
    }


    @Override
    public Map<String, String> getSchedulePlan(String transId) {
        //这句查询平均消耗0.7s,数据建模列表查询，循环会调getSchedulePlan，性能会爆炸，所以提供了下面的一个方法
        TransMeta transMeta = transMetaService.getTransMetaById(transId);
        String sql = "select * from t_trans_schedule where trans_id = :transId";
        Map<String, String> map = this.baseDao.sqlQueryForMap(sql, addParam("transId", transId).param());
        Map<String, String> result = new HashMap<>();
        if (map == null || map.isEmpty()) {
            try {
                String taskSql = "select id from t_trans_task where trans_id = :transId";
                String taskId = this.baseDao.sqlQueryForValue(taskSql, addParam("transId", transId).param());

                if (StringUtils.isBlank(taskId)) {
                    taskId = createTransTask(transMeta.getId(), transMeta.getName());
                }
                String scheduleId = createSchedulePlan(transMeta, taskId);

                result.put("id", scheduleId);
                result.put("transMetaId", transId);
                result.put("scheduleType", "hand");
                result.put("cronExpression", "");
                result.put("scheduleDeclare", "");
            } catch (SQLException e) {
             log.error(e.getMessage(),e);
            }
        } else {
            result.put("id", map.get("id"));
            result.put("transMetaId", transId);
            result.put("scheduleType", map.get("schedule_type"));
            result.put("cronExpression", map.get("cron_expression"));
            result.put("scheduleDeclare", map.get("schedule_declare"));
            result.put("executorServerName", map.get("executor_service"));
        }
        return result;
    }

    @Override
    public Map<String, String> getSchedulePlan(String transId, String name) {
        String sql = "select * from t_trans_schedule where trans_id = :transId";
        Map<String, String> map = this.baseDao.sqlQueryForMap(sql, addParam("transId", transId).param());
        Map<String, String> result = new HashMap<>();
        if (map == null || map.isEmpty()) {
            try {
                String taskSql = "select id from t_trans_task where trans_id = :transId";
                String taskId = this.baseDao.sqlQueryForValue(taskSql, addParam("transId", transId).param());

                if (StringUtils.isBlank(taskId)) {
                    taskId = createTransTask(transId, name);
                }
                String scheduleId = createSchedulePlan(transId, taskId);

                result.put("id", scheduleId);
                result.put("transMetaId", transId);
                result.put("scheduleType", "hand");
                result.put("cronExpression", "");
                result.put("scheduleDeclare", "");
            } catch (SQLException e) {
             log.error(e.getMessage(),e);
            }
        } else {
            result.put("id", map.get("id"));
            result.put("transMetaId", transId);
            result.put("scheduleType", map.get("schedule_type"));
            result.put("cronExpression", map.get("cron_expression"));
            result.put("scheduleDeclare", map.get("schedule_declare"));
        }
        return result;
    }

    private String createTransTask(String transId, String transName) throws SQLException {
        String taskId = StringUtils.uuid();
        Db.use(dataSource).insert(
                Entity.create("t_trans_task")
                        .set("id", taskId)
                        .set("trans_id", transId)
                        .set("trans_name", transName)
                        .set("run_param", null)
                        .set("sub_task_id", null)
        );
        return taskId;
    }

    private String createSchedulePlan(TransMeta transMeta, String taskId) throws SQLException {
        String scheduleId = StringUtils.uuid();
        Db.use(dataSource).insert(
                Entity.create("t_trans_schedule")
                        .set("id", scheduleId)
                        .set("trans_id", transMeta.getId())
                        .set("task_id", taskId)
                        .set("schedule_type", "hand")
                        .set("cron_expression", null)
                        .set("schedule_declare", null)
                        .set("trigger_status", 0)
                        .set("trigger_last_time", 0)
                        .set("trigger_next_time", 0)
        );
        return scheduleId;
    }

    private String createSchedulePlan(String transId, String taskId) throws SQLException {
        String scheduleId = StringUtils.uuid();
        Db.use(dataSource).insert(
                Entity.create("t_trans_schedule")
                        .set("id", scheduleId)
                        .set("trans_id", transId)
                        .set("task_id", taskId)
                        .set("schedule_type", "hand")
                        .set("cron_expression", null)
                        .set("schedule_declare", null)
                        .set("trigger_status", 0)
                        .set("trigger_last_time", 0)
                        .set("trigger_next_time", 0)
        );
        return scheduleId;
    }

    @Override
    public void transRun(String transId, String userId) {
        MLSQLScheduleClient service = RetrofitManager.scheduleClient(scheduleServer);
        try {
            Response<ResponseBody> response = service.startTask(buildRunParam(transId, userId)).execute();
            if (String.valueOf(response.code()).startsWith("4")) {
                Assert.fail("未找到调度中心, 请检查配置文件！");
            }
            if (String.valueOf(response.code()).startsWith("5")) {
                Assert.fail("调度中心异常，具体错误插件调度中心日志文件!");
            }
        } catch (IOException e) {
         log.error(e.getMessage(),e);
        }
    }

    @Override
    public void transRun(String transId, String userId, String executorServiceName) {
        MLSQLScheduleClient service = RetrofitManager.scheduleClient(scheduleServer);
        try {
            Map<String, Object> map = buildRunParam(transId, userId);
            map.put("executorServiceName", executorServiceName);
            Response<ResponseBody> response = service.startTask(map).execute();
            if (String.valueOf(response.code()).startsWith("4")) {
                Assert.fail("未找到调度中心, 请检查配置文件！");
            }
            if (String.valueOf(response.code()).startsWith("5")) {
                Assert.fail("调度中心异常，具体错误插件调度中心日志文件!");
            }
        } catch (IOException e) {
         log.error(e.getMessage(),e);
        }
    }

    @Override
    public void transRun(String transId, String flinkUrl, HttpServletRequest request) {
        //走四系的
        Assert.hasLength(transId, "方案不能为空");
        //判断是否存在执行中的任务
        List<String> transIds=new ArrayList<>();
        transIds.add(transId);
        List<String> runningName=getRunningJobTransName(transIds);
        if (CollectionUtil.isNotEmpty(runningName)){
            Assert.fail(String.format("存在执行中的任务:%s",JSONObject.toJSONString(runningName)));
        }
        List<Object[]> batchSaveParams = new ArrayList<>();
        List<Map<String, String>> transAndTaskId = getTransIdAndTaskId(transId);
        Map<String, String> transIdAndTaskId = new HashMap<>();
        if (CollectionUtil.isNotEmpty(transAndTaskId)) {
            transIdAndTaskId = transAndTaskId.stream().collect(Collectors.toMap(k -> k.get("trans_id"), v -> v.get("id"), (o1, o2) -> o1));
        } else {
            Assert.fail("找不到对应的任务");
        }
        List<Map<String, String>> trans = getTransIdAndName(transId);
        Map<String, String> transMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(transAndTaskId)) {
            transMap = trans.stream().collect(Collectors.toMap(k -> k.get("id"), v -> v.get("name"), (o1, o2) -> o1));
        }
        String ip = request.getLocalAddr();
        int port = request.getLocalPort();
        String data = null;
        String jobId = null;
        String applicationId = null;
        Map<String, Object> dataMap = null;
        Map<String, Object> param = new HashMap<>();
        Map<String,String> jobInfo= null;
        param.put("transId", transId);
        //我这边无法获取对应的jobId,传参的时候还没获取到对应的jobId
        param.put("url", "http://".concat(ip).concat(":").concat(String.valueOf(port)).concat("/dataCenter/dataMining/updateJobStatus"));
        data = HttpUtil.post(flinkUrl.concat("/engine/submit/trans/"), JSONObject.toJSONString(param));
        try {
            dataMap = JSONObject.parseObject(data, HashMap.class);
        } catch (Exception e) {
         log.error(e.getMessage(),e);
            Assert.fail(String.format("执行异常:[%s]",e.getMessage()));
        }
        if (!Objects.equals(dataMap.get("code"), 1) && null != dataMap.get("data")) {
            jobInfo = (Map<String, String>) dataMap.get("data");
            jobId = jobInfo.get("jobId");
            applicationId = jobInfo.get("appId");
            if (StringUtils.isBlank(jobId)){
                Assert.fail("jobId is null ");
            }
            batchSaveParams.add(new Object[]{jobId, false, null, "ongoing", null, null, Timestamp.valueOf(LocalDateTime.now()), transIdAndTaskId.get(transId), transId, transMap.get(transId), applicationId});
        } else if (dataMap.containsKey("code") && Objects.equals(dataMap.get("code"), 1)) {
            Assert.fail(dataMap.get("msg").toString());
        } else {
            log.info(data);
            Assert.fail("执行异常");
        }
        if (null != batchSaveParams) {
            String batchSql = "INSERT INTO t_trans_job (id,disabled,end_time,execute_status,job_detail,schedule_id,start_time,task_id,trans_id,trans_name,task_string) VALUES " +
                    "  (?,?,?,?,?,?,?,?,?,?,?)";
            baseDao.batchInsert(batchSql, batchSaveParams);
        }
    }

    public List<String> getRunningJobTransName(List<String> transIds){
        List<String> runningJobNames=new ArrayList<>();
        List<Map<String,String>> list=getRunningJob(transIds);
        if (CollectionUtil.isNotEmpty(list)){
            runningJobNames=list.stream().map(m->m.get("name")).collect(Collectors.toList());
        }
        return runningJobNames;
    }

    public List<Map<String, String>> getRunningJob(List<String> transIds) {
        String sql = "select trans_id from t_trans_job where execute_status ='ongoing' and trans_id in(:transIds)";
        List<Map<String,String>> runningTrans=baseDao.sqlQueryForList(sql,addParam("transIds",transIds).param());
        if (CollectionUtil.isNotEmpty(runningTrans)) {
            List<String> runningTransIds = runningTrans.stream().map(m -> m.get("trans_id")).collect(Collectors.toList());
            //查询对应的方案详情
            String transSql=" select name,id from t_etl_trans where id in(:transIds)";
            return baseDao.sqlQueryForList(transSql,addParam("transIds",runningTransIds).param());
        }
        return new ArrayList<>();
    }

    @Override
    public void updateJobStatus(String jobId, String status) {
        Assert.hasLength(jobId," jobId is null");
        updateJob(jobId,status);
    }

    private List<Map<String, String>> getTransIdAndTaskId(String transId) {
        String sql = "select id,trans_id from t_trans_task where trans_id = :transId ";
        return baseDao.sqlQueryForList(sql, addParam("transId", transId).param());
    }

    private List<Map<String, String>> getTransIdAndName(String transId) {
        String sql = "select id,name from t_etl_trans where id in( :transId) ";
        return baseDao.sqlQueryForList(sql, addParam("transId", transId).param());
    }

    @Override
    public Map<String, String> transPreview(String transId) {
        TransMeta transMeta = transMetaService.getTransMetaById(transId);
        // TODO
        Map<String, Object> param = new HashMap<>();
        param.put("executeMode", "query");
        param.put("sql", trans2MLSQL(transMeta, true, null));
        param.put("jobId", StringUtils.uuid());
        param.put("transId", transId);

        MLSQLExecutorClient executorClient = RetrofitManager.executorClient(executorServer);
        try {
            Response<ResponseBody> response = executorClient.runScript(param).execute();
            if (response.code() == MLSQLConstant.SUCCESS) {
                // TODO 将表格previewPlugin结果替换掉
            } else {
                // TODO 在页面表格处显示错误日志
            }
        } catch (IOException e) {
         log.error(e.getMessage(),e);
        }
        return null;
    }

    private Map<String, Object> buildRunParam(String transId, String userId) {
        if (StringUtils.isBlank(transId)) {
            Assert.fail("transId 为空");
        }
        TransMeta transMeta = transMetaService.getTransMetaById(transId);
        //String subTransId = checkStartTaskPluginExist(transMeta);
        //String subTransId = getSubtransId(transId);
        Map<String, Object> result = new HashMap<>();
        result.put("transId", transMeta.getId());
        result.put("transName", transMeta.getName());
        String transSql = trans2MLSQL(transMeta, true, null);
        String replaceSql = transVariableService.conversionVariable(transMeta.getId(), transSql, userId);
        replaceSql = replaceSql.replace("@{ipAndPort}", aiModelIpAndPort.getRunOpServiceIp());
        replaceSql = replaceSql.replace("http://@{modelServicePath}", publishPath);
        Assert.notNull(replaceSql, "请配置至少一个插件！");
        result.put("taskParam", replaceSql);
        //result.put("subTask", subTransId);
        return result;
    }


    private List getSubtransId(String transId) {
        String sql = "select sub_trans_id from t_trans_subtrans_relation where trans_id='" + transId + "'";
        List list = this.baseDao.sqlQueryForList(sql);

        return list;
    }

    @Override
    public void transStop(String transId) {
        MLSQLScheduleClient service = RetrofitManager.scheduleClient(scheduleServer);
        try {
            service.stopTask(transId).execute();
        } catch (IOException e) {
         log.error(e.getMessage(),e);
        }
    }

    @Override
    public void transStop(String transId, String flinkUrl) {
        List<Map<String, String>> list = getAllJobIds(transId);
        if (CollectionUtil.isNotEmpty(list)) {
            //先执行调用外部接口停止任务的操作
            Map<String, String> transIdAndJobIds = list.stream().collect(Collectors.toMap(k -> k.get("trans_id"), v -> v.get("id"), (o1, o2) -> o1));
            List<String> resultIds = new ArrayList<>();
            transIdAndJobIds.forEach((k, v) -> {
                String data = null;

                data = HttpUtil.get(flinkUrl.concat("/engine/kill/job/").concat(v));
                Map<String, Object> dataMap = null;
                try {
                    dataMap = JSONObject.parseObject(data, HashMap.class);
                } catch (Exception e) {
                    Assert.fail(String.format("停止任务异常：[]",e.getMessage()));
                 log.error(e.getMessage(),e);
                }
                if (dataMap.containsKey("code") && Objects.equals(dataMap.get("code"), 0)) {
                    updateJob(v, "cancel");
                } else if (dataMap.containsKey("code") && Objects.equals(dataMap.get("code"), 1)) {
                    Assert.fail(dataMap.get("msg").toString());
                } else {
                    log.info(data);
                    Assert.fail("停止任务异常");
                }
            });

        }
    }

    private List<Map<String, String>> getAllJobIds(String transId) {
        StringBuffer sb = new StringBuffer();
        sb.append(" select * from( ")
                .append(" select id,trans_id ,")
                .append(" Row_Number() OVER (partition by trans_id ORDER by start_time DESC) as rank ")
                .append(" from t_trans_job ")
                .append(" where trans_id =:transId ) j ")
                .append(" where j.rank=1 ");
        return baseDao.sqlQueryForList(sb.toString(), addParam("transId", transId).param());
    }

    private void updateJob(String jobId, String status) {
        String sql = "update t_trans_job set execute_status=:status ";
        Map map=new HashMap();
        map.put("jobId",jobId);
        map.put("status",status);
        if (Objects.equals(status,"success")||Objects.equals(status,"error")||Objects.equals(status,"cancel")){
            sql = sql.concat(",end_time = :endTime ");
            map.put("endTime",Timestamp.valueOf(LocalDateTime.now()));
        }
        sql = sql.concat("where id =:jobId");
        baseDao.executeSqlUpdate(sql, map);
    }

    @Override
    public String showSQL(String transId) {
        TransMeta transMeta = transMetaService.getTransMetaById(transId);
        return trans2MLSQL(transMeta, true, null);
    }

    @Override
    public String showSQL(String transId, String flinkUrl) {
        Assert.hasLength(transId, "方案ID不能为空");
        String data = HttpUtil.get(flinkUrl.concat("/get/script/").concat(transId));
        Map<String, Object> dataMap = JSONObject.parseObject(data, HashMap.class);
        if (dataMap.containsKey("code") && Objects.equals(dataMap.get("code"), 0)) {
            return dataMap.get("data").toString();
        } else if (dataMap.containsKey("code") && Objects.equals(dataMap.get("code"), 0)) {
            Assert.fail(dataMap.get("msg").toString());
        } else {
            Assert.fail("获取sql异常");
        }
        return "";
    }

    @Override
    public String analysisSQL(String transId) {
        TransMeta transMeta = transMetaService.getTransMetaById(transId);
        String syntax = trans2MLSQL(transMeta, true, null);
        Map<String, Object> param = new HashMap<>();
        param.put("executeMode", "analyze");
        param.put("sql", syntax);
        MLSQLExecutorClient executorClient = RetrofitManager.executorClient(executorServer);
        try {
            Response<ResponseBody> response = executorClient.runScript(param).execute();
            if (response.code() == MLSQLConstant.SUCCESS) {
                if (response.body() != null) {
                    return response.body().string();
                }
            } else if (response.code() == MLSQLConstant.ERROR) {
                if (response.errorBody() != null) {
                    return response.errorBody().string();
                }
            }
        } catch (IOException e) {
         log.error(e.getMessage(),e);
        }
        return null;
    }

    @Override
    public String subTask(String subTranId) {
        Map<String, String> param = new HashMap<>();
        param.put("taskParam", "");
        TransMeta subMeta = transMetaService.getTransMetaById(subTranId);
        if (subMeta != null) {
            String sql = trans2MLSQL(subMeta, true, null);
            param.put("hasSubTask", "true");
            param.put("transId", subMeta.getId());
            param.put("taskParam", sql);

        }

        return JSON.toJSONString(param);
    }


    @Override
    public List getSubTrans(String transId) {
        String sql = "select subtrans_id,subtrans_delay_time,subtrans_isenforce from t_trans_subtrans_relation where trans_id= :transId";
        List subTrans = this.baseDao.sqlQueryForList(sql, addParam("transId", transId).param());
        return subTrans;
    }

    @Override
    public void deletaSubTrans(String transId, String subTransId) {
        String sql = "delete from t_trans_subtrans_relation where trans_id=:transId and subtrans_id=:subTransId";
        this.baseDao.executeSqlUpdate(sql, addParam("transId", transId).addParam("subTransId", subTransId).param());
    }


    private String trans2MLSQL(TransMeta transMeta, boolean isRun, String selectSize) {
        Script script = helper.parse(transMeta, isRun, selectSize);
        String mlSql = MlSqlSparkVersionFilterUtil.functionFilter(script.toScript(), use_spark_version);
        //服务输入插件sql
        mlSql = mlsqlService.getCicadaMetaServiceSql(transMeta) + mlSql;
        return mlSql;
    }

    private String checkStartTaskPluginExist(TransMeta transMeta) {
        Set<TransMeta> metas = transMeta.getChildren();
        List<String> list = metas.stream()
                .filter(m -> m.getUsedPlugin().getCode().equals("startTaskPlugin"))
                .map(m -> {
                    String subTransId = "";
                    Map<TransAttributeMeta, String> values = m.getAttributeValues();
                    for (Map.Entry<TransAttributeMeta, String> entry : values.entrySet()) {
                        if (entry.getKey().getCode().equals("transMetaId")) {
                            subTransId = entry.getValue();
                        }
                    }
                    return subTransId;
                }).collect(Collectors.toList());
        if (list.size() > 1) {
            Assert.fail("每个方案只允许配置一个【启动方案插件】, 请重新配置!");
        }
        if (list.isEmpty()) {
            return "";
        }
        return list.get(0);
    }

    private String checkStartTaskPluginExistAndGetOne(TransMeta transMeta) {
        Set<TransMeta> metas = transMeta.getChildren();
        List<String> list = metas.stream()
                .filter(m -> m.getUsedPlugin().getCode().equals("startTaskPlugin"))
                .map(m -> {
                    String subTransId = "";
                    Map<TransAttributeMeta, String> values = m.getAttributeValues();
                    for (Map.Entry<TransAttributeMeta, String> entry : values.entrySet()) {
                        if (entry.getKey().getCode().equals("transMetaId")) {
                            subTransId = entry.getValue();
                        }
                    }
                    return subTransId;
                }).collect(Collectors.toList());
        if (list.size() > 1) {
            log.warn("所选方案配置多个【启动方案插件】, 随机获取一个!");
        }
        if (list.isEmpty()) {
            return "";
        }
        return list.get(0);
    }

}
