package com.dragonsoft.cicada.datacenter.modules.system.permissions.service.impl;

import com.code.metadata.standard.StandCodeVal;
import com.code.metaservice.standmb.IStandCodeValService;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.service.IStanderMbCodeService;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.vo.MbEnumTreeVo;
import com.fw.service.BaseService;
import com.fw.service.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by yecc on 2020/11/18 16:24
 */
@Service
public class StanderMbCodeServiceImpl extends BaseService implements IStanderMbCodeService {

    @Autowired
    IStandCodeValService standCodeValService;

    @Override
    public List getTreeById(String id) {
        List<StandCodeVal> vals = standCodeValService.getValListByMbId(id);
        return vals.stream().map(n -> new MbEnumTreeVo(n.getName(), n.getCode())).collect(Collectors.toList());
    }

    @Override
    public List linkagePoliceStation(String id,String level) {

        List<StandCodeVal> vals = standCodeValService.getPoliceOffices(id,level);
        return vals.stream().map(n -> new MbEnumTreeVo(n.getName(), n.getCode(),n.getId())).collect(Collectors.toList());
    }

    @Override
    public  void dealPoliceStationOwnerId(){
        List<StandCodeVal> vals = standCodeValService.getPoliceOffice();
        for (StandCodeVal val : vals) {//拿到所有的分局
            String code = val.getCode();
            String ownerId = val.getId();
            List<StandCodeVal> policeStation = standCodeValService.getPoliceStation(code);//根据分局的code前六位 匹配出所属的派出所
            for (StandCodeVal standCodeVal : policeStation) {
                updatePoliceStationOwenid(standCodeVal,ownerId);//将所属分局的id存进派出所的ownerid
            }
        }
    }
    private void updatePoliceStationOwenid(StandCodeVal standCodeVal,String ownerId){
        String id = standCodeVal.getId();
        String sql="update t_md_stand_code_val set owner_id=:ownerId where id=:id";
        this.baseDao.executeSqlUpdate(sql,addParam("ownerId",ownerId).addParam("id",id).param());
    }


}
