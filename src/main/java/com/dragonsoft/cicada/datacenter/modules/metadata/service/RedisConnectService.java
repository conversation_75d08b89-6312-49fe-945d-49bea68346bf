package com.dragonsoft.cicada.datacenter.modules.metadata.service;

import com.code.metadata.res.semistructured.redis.RedisInstance;
import com.code.metaservice.datawarehouse.model.DataSourceVO;
import com.code.metaservice.res.request.datasource.DataSourceRequest;
import com.code.metaservice.res.response.datasource.InsertDataSourceResponse;
import com.code.metaservice.res.semistructured.redis.IRedisInstanceService;
import com.fw.service.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR> Avizii
 * @Create : 2021.03.29
 */
@Service(mappingName = "redisConnectService")
public class RedisConnectService extends DataBaseConnectService {

    @Autowired
    private IRedisInstanceService redisInstanceService;

    @Override
    public DataSourceVO getDataSourceInfo(String id) {
        RedisInstance redisInstance = (RedisInstance) this.baseDao.get(RedisInstance.class, id);
        DataSourceVO dataSourceVO = new DataSourceVO();
        String ipAddress = redisInstance.getDeployedComp().getMachine().getIpAddress();
        dataSourceVO.setIp(ipAddress);
        dataSourceVO.setPort(String.valueOf(redisInstance.getPort()));
        dataSourceVO.setPassword(redisInstance.getPassword());
        return dataSourceVO;
    }

    @Override
    public boolean testConnection(DataSourceVO dataSourceVO) {
        DataSourceRequest dataSourceRequest = new DataSourceRequest();
        dataSourceRequest.setIp(dataSourceVO.getIp());
        dataSourceRequest.setPort(dataSourceVO.getPort());
        dataSourceRequest.setPassword(dataSourceVO.getPassword());
        return redisInstanceService.testConnection(dataSourceRequest);
    }

    @Override
    public InsertDataSourceResponse insertDataSource(DataSourceVO dataSourceVO) {
        DataSourceRequest dataSourceRequest = toDataSourceRequest(dataSourceVO);
        return redisInstanceService.insertResource(dataSourceRequest);
    }

    @Override
    public void updateResource(DataSourceVO dataSourceVO) {
        redisInstanceService.updateResourceInfo(toDataSourceRequest(dataSourceVO));
    }
}
