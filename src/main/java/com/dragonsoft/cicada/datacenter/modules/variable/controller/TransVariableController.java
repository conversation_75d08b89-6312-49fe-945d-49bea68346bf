package com.dragonsoft.cicada.datacenter.modules.variable.controller;

import com.code.common.mist.plugin.config.service.impl.PluginConfigService;
import com.code.common.utils.R;
import com.code.common.utils.StringUtils;
import com.code.common.utils.assertion.Assert;
import com.code.metadata.etl.trans.TransMeta;
import com.code.metadata.res.ddl.LogicDataObj;
import com.code.metadata.udf.management.UdfOperator;
import com.code.metadata.variable.TransVariable;
import com.code.metadata.variable.TransVariableRelation;
import com.code.metaservice.datawarehouse.IDataWareTreeService;
import com.code.metaservice.datawarehouse.model.DataUdfTreeModel;
import com.code.metaservice.ddl.ILogicDataObjService;
import com.code.metaservice.etl.trans.TransMetaService;
import com.code.metaservice.udf.management.IUdfOperatorService;
import com.code.metaservice.variable.ITransVariableRelationService;
import com.code.metaservice.variable.ITransVariableService;
import com.dragoninfo.dfw.bean.Result;
import com.dragonsoft.cicada.datacenter.modules.plugin.service.PluginCheckService;
import com.dragonsoft.cicada.datacenter.modules.variable.vo.TransVariableVo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpSession;
import java.util.*;

/**
 * 版权所有：厦门市巨龙信息科技有限公司
 * Copyright 2018 Xiamen Dragon Information Technology Co. Ltd.
 * All right reserved.
 * ====================================================
 * 修订记录：
 * No    日期				作者(操作:具体内容)
 * 1.    2021/7/19 15:33			baijx（<EMAIL>）(创建:创建文件)
 * ====================================================
 * 类描述：
 */
@RestController
@CrossOrigin
@RequestMapping("/variableManage")
@Slf4j
public class TransVariableController {
    @Autowired
    ITransVariableService transVariableService;
    @Autowired
    ITransVariableRelationService transVariableRelationService;

    @Autowired
    private IUdfOperatorService udfOperatorService;

    @Autowired
    private IDataWareTreeService dataWareTreeService;

    @Autowired
    private TransMetaService transMetaService;

    @Autowired
    private PluginCheckService pluginCheckService;

    @Autowired
    private PluginConfigService pluginConfigService;

    @Autowired
    private ILogicDataObjService logicDataObjService;

    static List<String> variableType = Lists.newArrayList("double", "integer", "float", "byte", "long", "biginteger","date","timestamp");

    @ResponseBody
    @PostMapping("/saveTransVariable")
    public Result saveTransVariable(@RequestBody TransVariableVo transVariableVo, HttpSession session) {

        String userId = (String) session.getAttribute("userId");

        List<TransVariable> transVariables = transVariableVo.getTransVariables();
        for (TransVariable transVariable : transVariables) {
            if (transVariable.isGlobal()){
                  List<Map<String,Object>> repeats=transVariableService.queryRepeat(transVariable.getId(),transVariableVo.getTransId(),userId,transVariable.getParamCode());
                  if (CollectionUtils.isNotEmpty(repeats)){
                      //存在重复的单元
                      Map<String,Object> repeat=repeats.get(0);
                      //判断是什么类型
                      if (Objects.equals(repeat.get("relation_type"),0)){
                          TransMeta transMetaById = transMetaService.getTransMetaById(repeat.get("trans_id").toString());
                            if (transMetaById != null) {
                                return Result.toResult(R.error(transVariable.getParamCode() + "已存在方案:" + transMetaById.getName() + ",请重新命名！"));
                            }
                      }else if(Objects.equals(repeat.get("relation_type"),1)){
                          LogicDataObj dataObj = logicDataObjService.findLogicDataObjById(repeat.get("trans_id").toString());
                            if (dataObj != null) {
                                return Result.toResult(R.error(transVariable.getParamCode() + "已存在逻辑表:" + dataObj.getName() + ",请重新命名！"));
                            }
                      }else {
                          return Result.toResult(R.error(transVariable.getParamCode() + "变量已存在,请重新命名！"));
                      }

                  }
//                List<TransVariable> transVariables1 = transVariableService.queryVariableByGlobalName(transVariable.getParamCode());
//                for (TransVariable variable : transVariables1) {
//                    if (variable!=null && !variable.getId().equals(transVariable.getId())){
//                        if (StringUtils.isNotBlank(transVariableVo.getTransId())) {
//                            //这个是判断这个变量在别的模型底下是否存在重名的,也是与用户无关,也与是否是全局变量无关的一个判断
//                            TransVariableRelation transVariableInOtherTrans = transVariableRelationService.getTransVariableInOtherTrans(variable.getId(), transVariableVo.getTransId());
//                            if (transVariableInOtherTrans != null) {
//                                String transId = transVariableInOtherTrans.getTransId();
//                                int relationType = transVariableInOtherTrans.getRelationType();
//                                if (Objects.equals(relationType, 0)) {
//                                    TransMeta transMetaById = transMetaService.getTransMetaById(transId);
//                                    if (transMetaById != null) {
//                                        return Result.toResult(R.error(variable.getParamCode() + "已存在方案:" + transMetaById.getName() + ",请重新命名！"));
//                                    }
//                                } else {
//                                    LogicDataObj dataObj = logicDataObjService.findLogicDataObjById(transId);
//                                    if (dataObj != null) {
//                                        return Result.toResult(R.error(variable.getParamCode() + "已存在逻辑表:" + dataObj.getName() + ",请重新命名！"));
//                                    }
//                                }
//                            }
//                        }else{
//                            //无关联的全局变量,只考虑用户相关,与是否全局变量无关,判断是否有重名变量存在
//                            int count=transVariableService.countByParamCodeAndUser(variable.getParamCode(),userId,null);
//                            if (count>0){
//                                return Result.toResult(R.error(variable.getParamCode() + "变量已存在,请重新命名！"));
//                            }
//
//                        }
//
//                        //throw new RuntimeException(variable.getParamCode()+"已存在于其它方案,请重新命名！");
//                    }
//                }
            }else {
                List<TransVariable> transVariables1 = transVariableService.queryGlobalVariable(userId);
                for (TransVariable variable : transVariables1) {
                    if (variable!=null && !transVariable.getId().equals(variable.getId())){
                        if (variable.getParamCode().equals(transVariable.getParamCode())){
                            return Result.toResult(R.error(variable.getParamCode()+"已存在相同名称的全局参数,请重新命名！"));
                        }
                    }
                }
            }
        }
        if(CollectionUtils.isNotEmpty( transVariableVo.getDeleteTransVariable())){
            transVariableVo.getDeleteTransVariable().forEach(variableId -> {
                TransVariable transVariable = transVariableService.get(TransVariable.class, variableId);
                if (null != transVariable) {
                    transVariableRelationService.deleteRelationByVariableId(variableId);
                    transVariableService.delete(transVariable);
                }
            });
        }
        for (TransVariable transVariable : transVariableVo.getTransVariables()) {
            TransVariable transVariable1 = new TransVariable();
            if (StringUtils.isNotBlank(transVariable.getId()))
                transVariable1  = transVariableService.get(TransVariable.class, transVariable.getId());
            //这个查询实际也可以替换成用户的
            boolean b = transVariableService.checkParam(transVariable.getParamCode(), transVariableVo.getTransId(), transVariable.getId(),userId);
            if (b) {
                transVariable1.setType("0");

                transVariable1.setOperateUserId(userId);
                transVariable1.setParamValue(transVariable.getParamValue());
                transVariable1.setParamCode(transVariable.getParamCode());
                transVariable1.setParamType(transVariable.getParamType());
                transVariable1.setGlobal(transVariable.isGlobal());
                transVariable1.setName(transVariable.getName());
                transVariable1.setMemo(transVariable.getMemo());
                String paramValue = transVariable.getParamValue();
                if (transVariable.isVariable()){
                    //截取封装的算子  转成spark的函数
                   /* String[] params = new String[0];
                    Pattern p = Pattern.compile("(?<=\\()[^\\)]+");
                    Matcher matcher = p.matcher(paramValue);
                    while (matcher.find()){
                        params = Arrays.copyOf(params,params.length + 1);
                        params[params.length - 1] = matcher.group();
                    }*/
                    String substring = paramValue.substring(0, paramValue.indexOf("("));
                    UdfOperator udfOperator = udfOperatorService.queryUdfOperatorByReplaceExp(substring);
                    if (udfOperator != null){
                        //获取用户输入的参数变量 n
                        String paramN = paramValue.substring(paramValue.indexOf("(") + 1, paramValue.lastIndexOf(")"));
                        String expression = udfOperator.getExpression();
                        String replace = expression.replace(":n", paramN);
                        //存入参数变量
                        transVariable1.setCode(paramValue);
                        transVariable1.setParamValue(replace);
                    }else {
                        transVariable1.setParamValue(transVariable.getParamValue());
                    }
                }else {
                    if(!variableType.contains(transVariable.getParamType().toLowerCase())) transVariable1.setParamValue("'"+transVariable.getParamValue()+"'");
                }
                transVariable1.setCode(paramValue);
                transVariable1.setVariable(transVariable.isVariable());
                transVariableService.saveOrUpdate(transVariable1);
                if (StringUtils.isNotBlank(transVariableVo.getTransId())) {
                    //如果是有实体的关联的话才保存关联关系
                    saveTransVariableRelation(transVariable1.getId(), transVariableVo.getTransId(), transVariableVo.getRelationType());
                }
            } else Assert.fail("全局变量参数名重名");
        }
        return Result.success();
    }

    private void saveTransVariableRelation(String variableId, String transId,int relationType) {
        TransVariableRelation transVariableRelation;
        transVariableRelation = transVariableRelationService.getTransVariableRelation(transId, variableId);
        transVariableRelation.setCode("0");
        transVariableRelation.setType("0");
        transVariableRelation.setTransId(transId);
        transVariableRelation.setVariableId(variableId);
        transVariableRelation.setRelationType(relationType);
        transVariableRelationService.saveOrUpdate(transVariableRelation);
    }

    @ResponseBody
    @GetMapping("/queryTransByTransId")
    public Result queryTransByTransId(String transId,HttpSession session) {
        String userId = (String) session.getAttribute("userId");
        List<TransVariable> transVariableList = new ArrayList<>();
        if (StringUtils.isNotBlank(transId)) {
            transVariableList = transVariableService.queryTransByTransId(transId);
        }
        //查出全局
        List<TransVariable> transVariableGlobal = transVariableService.queryGlobalVariable(userId);
        transVariableList.addAll(transVariableGlobal);
        List<TransVariable> transVariables = Lists.newArrayList();
        transVariableList.forEach(transVariable -> {
            TransVariable transVariable1 = transVariable;
            if (!transVariable1.isVariable()){
                transVariable1.setParamValue(transVariable.getParamValue().replace("'",""));
            }else {
                if (StringUtils.isNotBlank(transVariable.getCode())){
                    transVariable1.setParamValue(transVariable.getCode());
                }
            }
            transVariables.add(transVariable1);
        });
        return Result.success(transVariables);
    }

    @ResponseBody
    @GetMapping("/queryTransVariable")
    public Result queryTransGlobalVariable(String logicObjId,HttpSession session){
        String userId = (String) session.getAttribute("userId");
        List<String> list= Lists.newArrayList();
        List<TransVariable> transVariableGlobal = transVariableService.queryGlobalVariable(userId);
        if (StringUtils.isNotBlank(logicObjId)) {
            List<TransVariable> transVariableList = transVariableService.queryTransByTransId(logicObjId);
            transVariableGlobal.addAll(transVariableList);
        }
        if (CollectionUtils.isNotEmpty(transVariableGlobal)){
            transVariableGlobal.forEach(t->{
                list.add(t.getParamCode());
            });
        }
        return Result.success(list);
    }

    @ResponseBody
    @GetMapping("/queryTransOwnerByTransId")
    public Result queryTransOwnerByTransId(String transId,HttpSession session) {
        TransMeta meta = transMetaService.getTransMetaById(transId);
        Set<TransMeta> children = meta.getChildren();

        List<Map<String, String>> allAttribute = Lists.newArrayList();
        for (TransMeta child : children) {
            Map<String, String> transMetaAttribute = pluginConfigService.getTransMetaAttribute(child.getId());
            allAttribute.add(transMetaAttribute);
            List<Map<String, String>> transMetaExpList = pluginConfigService.getTransMetaExpList(child.getId());
            allAttribute.addAll(transMetaExpList);
        }

        String userId = (String) session.getAttribute("userId");
        List<TransVariable> transVariableList = transVariableService.queryTransByTransId(transId);
        List<TransVariable> transVariables1 = transVariableService.queryGlobalVariable(userId);
        transVariableList.addAll(transVariables1);
        List<TransVariable> transVariables = Lists.newArrayList();
        List<String> cacheVariables = Lists.newArrayList();
        transVariableList.forEach(transVariable -> {
            TransVariable transVariable1 = transVariable;
            if (!transVariable1.isVariable()){
                transVariable1.setParamValue(transVariable.getParamValue().replace("'",""));
            }else {
                if (StringUtils.isNotBlank(transVariable.getMemo())){
                    transVariable1.setParamValue(transVariable.getMemo());
                }
            }
            for (Map<String, String> allExp : allAttribute) {
                for (String val : allExp.values()) {
                    if (val.contains(transVariable1.getParamCode())){
                        if(!cacheVariables.contains(transVariable1.getId())){
                            transVariables.add(transVariable1);
                            cacheVariables.add(transVariable1.getId());
                        }
                    }
                }
            }
        });
        return Result.success(transVariables);
    }

    @GetMapping("/queryUdfVariableList")
    public Result queryUdfVariableList(){
        List<UdfOperator> udfOperators = udfOperatorService.queryUdfOperatorWithoutParam();
        return Result.success(udfOperators);
    }


    @GetMapping("/getVariableOperatorTree")
    public Result getVariableOperatorTree(){
        try {
           /* List<DataUdfTreeModel> dataUdfTreeModelList = Lists.newArrayList();
            List<Map> enumTypeList = EnumOperator.getEnumTypeList();
            for (Map map : enumTypeList) {
                if (EnumOperator.UDF_OPERATOR.code.equals(map.get("code"))) {
                    DataUdfTreeModel dataUdfTreeModel = dataWareTreeService.queryUdfTreeByType(map.get("code").toString(), "standard", "expressionOperator");
                    if (CollectionUtils.isNotEmpty(dataUdfTreeModel.getChildren())){
                        List<DataUdfTreeModel> children = dataUdfTreeModel.getChildren();
                        for (DataUdfTreeModel child : children) {
                            if ("udf_time_operate".equalsIgnoreCase(child.getCode())){
                                child.setChildren(transVariableService.queryVariableUdfOperatorByParentType(child.getCode()));
                                dataUdfTreeModelList.add(child);
                            }
                        }
                    }
                }
            }*/
            List<DataUdfTreeModel> models = transVariableService.queryVariableUdfOperatorByParentType("udf_time_operate");
            return Result.toResult(R.ok(models));
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return Result.toResult(R.error(e.getMessage()));
        }
    }

    @GetMapping(value = "/getVariableExpression")
    public Result getExpression() {
        List<UdfOperator> udfOperators = transVariableService.queryVariableExpression();
        Map<String, String> udfExpressionMap = new HashMap<>();
        udfOperators.forEach(udfOperator -> {
            if (StringUtils.isNotBlank(udfOperator.getExpression())) {
                udfExpressionMap.put(udfOperator.getName(), udfOperator.getReplaceExp());
            }
        });
        return Result.success(udfExpressionMap);
    }
}
