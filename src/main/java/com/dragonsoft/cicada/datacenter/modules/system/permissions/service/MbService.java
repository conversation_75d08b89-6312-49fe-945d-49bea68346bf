package com.dragonsoft.cicada.datacenter.modules.system.permissions.service;

import com.code.common.paging.PageInfo;
import com.code.metaservice.standmb.vo.VisualMbTreeVo;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.vo.*;

import java.util.List;

public interface MbService {

    PageInfo getPageInfo(SearchListRequest request);

    List<SelectLabelVo> getDataSource();

    List<DataSetVo> getDataSet(String sourceId);

    List<FieldVo> getFiled(String dataSetId);

    List<CodeValListVo> selectCodeValList(SelectCodeListRequest request);

    String saveAndUpdateMb(SaveAndUpdateMbVo vo);

    void deleteById(String id);

    MbDetailsVo getMbDetails(String id);

    List<MbEnumTreeVo> getAllList();

    List<VisualMbTreeVo> getVisualMbAllList();

    List<MbEnumTreeVo> getCodesById(String id);

    List<String> getPeerTypesMb(String mbCode);
}
