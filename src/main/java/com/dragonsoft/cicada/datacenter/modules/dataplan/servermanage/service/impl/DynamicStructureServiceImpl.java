package com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.code.common.mist.service.structure.model.*;
import com.code.common.utils.StringUtils;
import com.code.common.utils.assertion.Assert;
import com.code.metadata.res.ddl.LogicDataObj;
import com.code.metadata.sm.EnumServiceType;
import com.code.metadata.sm.ParamPrefixSuffix;
import com.code.metaservice.ddl.ILogicDataObjService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.service.IDataSetEditService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.externaldatasources.service.IDataFilterService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.externaldatasources.service.IDataWarehousePlanService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.externaldatasources.vo.datawarehouse.ReleaseOutParamVo;
import com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.enums.JoinOpEnum;
import com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.service.DynamicStructureService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.service.ICicadaMetaServicePublishService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.utils.ConditionFilterBuildUtils;
import com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.utils.ServicePusblishUtil;
import com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.vo.ParamConfigVo;
import com.fw.service.annotation.Service;
import com.fw.tenon.bean.TenonBeanUtil;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;


@Service
public class DynamicStructureServiceImpl implements DynamicStructureService {

    private static final String[] STRINGTYPE = {"VARCHAR", "CHAR", "TEXT", "VARCHAR2", "NVARCHAR2", "NCHAR", "CHARACTER", "CHARACTER VARYING", "NCHAR", "NVARCHAR", "TEXT", "STRING", "TEXT", "KEYWORD", "StringType"};
    private static final String[] INTTYPE = {"INT2", "INT4", "INT8", "INTEGER", "INT"};
    private static final String[] TIMETYPE = {"TIMESTAMP", "DATE", "DATETIME", "TIME", "INTERVAL", "DateType"};
    private static final String[] FLOAT = {"FLOAT4"};
    private static final String BASICPATH = "com.code.sm.";

    private static final String IMPL = "Impl";

    private static final String ABSTRCATREFERENCE = "com.code.cicadas.datacenter.service.impl.AbstractDataWarehouseModel";
    private static final String OUTPUTTYPE = "com.code.common.operator.model.SimpleExecutorResult";
    private static final String SIMPLEDATASET = "com.code.common.operator.model.SimpleDataSet";

    @Autowired
    private IDataWarehousePlanService dataWarehouseService;
    @Autowired
    private IDataFilterService dataFilterService;

    @Autowired
    private ILogicDataObjService logicDataObjService;

    @Autowired
    private ICicadaMetaServicePublishService iCicadaMetaServicePublishService;

    @Autowired
    private IDataSetEditService dataSetEditService;

    @Override
    public ServiceClassMeta dynamicCreateClassMetaByVo(ParamConfigVo paramConfigVo) {
        EnumServiceType serviceType = EnumServiceType.getInstanceByCode(paramConfigVo.getServiceType());
        ServiceClassMeta serviceClassMeta = new ServiceClassMeta();
        serviceClassMeta.setInterfaceMeta(this.builderInterfaces(null, OUTPUTTYPE, paramConfigVo.getInterfaceEnglishName(), paramConfigVo.getInterfaceChineseName(), paramConfigVo));
        serviceClassMeta.setImplMeta(this.builderImpl(paramConfigVo.getImplVersion(), paramConfigVo.getModelId(), paramConfigVo.getInterfaceEnglishName(), paramConfigVo.getImplEnglishName(), paramConfigVo.getImplChineseName(), paramConfigVo));
        serviceClassMeta.setServiceType(serviceType);
        serviceClassMeta.setSourceId(paramConfigVo.getModelId());
        serviceClassMeta.setMemo(paramConfigVo.getMemo());
        return serviceClassMeta;
    }

    @Override
    public ServiceClassMeta dynamicQueryCreateClassMetaByVo(ParamConfigVo paramConfigVo,String userId) throws Exception {
        EnumServiceType serviceType = EnumServiceType.getInstanceByCode(paramConfigVo.getServiceType());
        ServiceClassMeta serviceClassMeta = new ServiceClassMeta();
        serviceClassMeta.setInterfaceMeta(this.builderInterfaces(null, OUTPUTTYPE, paramConfigVo.getInterfaceEnglishName(), paramConfigVo.getInterfaceChineseName(), paramConfigVo));
        serviceClassMeta.setImplMeta(this.builderSingleTableImpl(paramConfigVo.getImplVersion(), serviceClassMeta.getInterfaceMeta().getId(), paramConfigVo.getInterfaceEnglishName(), paramConfigVo.getImplEnglishName(), paramConfigVo.getImplChineseName(), userId,paramConfigVo));
        serviceClassMeta.setServiceType(serviceType);
        serviceClassMeta.setSourceId(paramConfigVo.getModelId());
        serviceClassMeta.setMemo(paramConfigVo.getMemo());
        serviceClassMeta.setSingleOrManyTable(paramConfigVo.getSingleOrManyTable());
        serviceClassMeta.setClassifyId(paramConfigVo.getClassifyId());
        serviceClassMeta.setResourceIds(paramConfigVo.getResourceIds());
        serviceClassMeta.setSaveType(paramConfigVo.getSaveType());
        serviceClassMeta.setBatchQuery(paramConfigVo.getBatchQuery());
        serviceClassMeta.setMaxQueryNum(paramConfigVo.getMaxQueryNum());
        //条件过滤
        List<ServiceClassMeta.ConditionFilterMeta> conditionFilterMetas = TenonBeanUtil.copyToList(paramConfigVo.getEncasulationJudgContion(), ServiceClassMeta.ConditionFilterMeta.class);
        serviceClassMeta.setEncasulationJudgContion(conditionFilterMetas);
        //请求参数映射
        serviceClassMeta.setTableColumnMappings(buildTableColumnMappings(paramConfigVo));
        //返回参数映射
        serviceClassMeta.setReturnParamMappings(buildReturnParamMappings(paramConfigVo));
        //排序字段
        serviceClassMeta.setOrderFieldTables(buildOrderFieldTables(paramConfigVo));
        return serviceClassMeta;
    }

    @Override
    public ServiceClassMeta dynamicVerifcationCreateClassMetaByVo(ParamConfigVo paramConfigVo,String userId) throws Exception {
        EnumServiceType serviceType = EnumServiceType.getInstanceByCode(paramConfigVo.getServiceType());
        ServiceClassMeta serviceClassMeta = new ServiceClassMeta();
        serviceClassMeta.setInterfaceMeta(this.builderVersitionInterfaces(null, OUTPUTTYPE, paramConfigVo.getInterfaceEnglishName(), paramConfigVo.getInterfaceChineseName(), paramConfigVo));
        serviceClassMeta.setImplMeta(this.builderVerifcationSingleTableImpl(paramConfigVo.getImplVersion(), paramConfigVo.getModelId(), paramConfigVo.getInterfaceEnglishName(), paramConfigVo.getImplEnglishName(), paramConfigVo.getImplChineseName(),userId, paramConfigVo));
        serviceClassMeta.setServiceType(serviceType);
        serviceClassMeta.setSourceId(paramConfigVo.getModelId());
        serviceClassMeta.setMemo(paramConfigVo.getMemo());
        serviceClassMeta.setSingleOrManyTable(paramConfigVo.getSingleOrManyTable());
        serviceClassMeta.setClassifyId(paramConfigVo.getClassifyId());
        serviceClassMeta.setResourceIds(paramConfigVo.getResourceIds());
        serviceClassMeta.setSaveType(paramConfigVo.getSaveType());
        serviceClassMeta.setBatchQuery(paramConfigVo.getBatchQuery());
        serviceClassMeta.setMaxQueryNum(paramConfigVo.getMaxQueryNum());
        //请求参数映射
        serviceClassMeta.setTableColumnMappings(buildTableColumnMappings(paramConfigVo));
        return serviceClassMeta;
    }

    private List<ServiceClassMeta.TableColumnMapping> buildReturnParamMappings(ParamConfigVo paramConfigVo) throws Exception {
        List<ServiceClassMeta.TableColumnMapping> list = new ArrayList<>();
        for (ParamConfigVo.TableColumnMapping tableColumnMapping : paramConfigVo.getReturnParamMappings()) {
            ServiceClassMeta.TableColumnMapping ColumnMapping = new ServiceClassMeta.TableColumnMapping();
            ColumnMapping.setDatasetId(tableColumnMapping.getDatasetId());
            ColumnMapping.setDatasetName(tableColumnMapping.getDatasetName());
            ColumnMapping.setDatasetZhName(tableColumnMapping.getDatasetZhName());
            List<ServiceClassMeta.ColumnMapping> columns = new ArrayList<>();
            for (ParamConfigVo.ColumnMapping column : tableColumnMapping.getColumns()) {
                ServiceClassMeta.ColumnMapping columnMapping = new ServiceClassMeta.ColumnMapping();
                columnMapping.setFiledName(column.getFiledName());
                columnMapping.setFieldCode(column.getFieldCode());
                columnMapping.setFieldAsCode(column.getFieldAsCode());
                columnMapping.setFieldAsName(column.getFieldAsName());
                columnMapping.setFieldType(column.getFieldType());
                columns.add(columnMapping);
            }
            ColumnMapping.setColumns(columns);
            list.add(ColumnMapping);
        }
        return list;
    }

    private List<ServiceClassMeta.OrderFieldTable> buildOrderFieldTables(ParamConfigVo paramConfigVo) {
        List<ServiceClassMeta.OrderFieldTable> list = new ArrayList<>();
        for (ParamConfigVo.OrderFieldTable pOrderFieldTable : paramConfigVo.getOrderFieldTables()) {
            ServiceClassMeta.OrderFieldTable sOrderFieldTable = new ServiceClassMeta.OrderFieldTable();
            sOrderFieldTable.setDatasetId(pOrderFieldTable.getDatasetId());
            sOrderFieldTable.setDatasetName(pOrderFieldTable.getDatasetName());
            sOrderFieldTable.setDatasetZhName(pOrderFieldTable.getDatasetZhName());
            List<ServiceClassMeta.OrderField> orderFields = new ArrayList<>();
            for (ParamConfigVo.OrderField orderField : pOrderFieldTable.getOrderFields()) {
                ServiceClassMeta.OrderField orderField1 = new ServiceClassMeta.OrderField();
                orderField1.setFieldCode(orderField.getFieldCode());
                orderField1.setFiledName(orderField.getFiledName());
                orderField1.setOrderNum(orderField.getOrderNum());
                orderField1.setOrderType(orderField.getOrderType());
                orderField1.setFieldType(orderField.getFieldType());
                orderFields.add(orderField1);
            }
            sOrderFieldTable.setOrderFields(orderFields);
            list.add(sOrderFieldTable);
        }
        return list;
    }


    private List<ServiceClassMeta.TableColumnMapping> buildTableColumnMappings(ParamConfigVo paramConfigVo) throws Exception {
        List<ServiceClassMeta.TableColumnMapping> list = new ArrayList<>();
        for (ParamConfigVo.TableColumnMapping tableColumnMapping : paramConfigVo.getTableColumnMappings()) {
            ServiceClassMeta.TableColumnMapping ColumnMapping = new ServiceClassMeta.TableColumnMapping();
            ColumnMapping.setDatasetId(tableColumnMapping.getDatasetId());
            ColumnMapping.setDatasetName(tableColumnMapping.getDatasetName());
            ColumnMapping.setDatasetZhName(tableColumnMapping.getDatasetZhName());
            List<ServiceClassMeta.ColumnMapping> columns = new ArrayList<>();
            for (ParamConfigVo.ColumnMapping column : tableColumnMapping.getColumns()) {
                ServiceClassMeta.ColumnMapping columnMapping = new ServiceClassMeta.ColumnMapping();
                columnMapping.setFiledName(column.getFiledName());
                columnMapping.setFieldCode(column.getFieldCode());
                columnMapping.setFieldAsCode(column.getFieldAsCode());
                columnMapping.setFieldAsName(column.getFieldAsName());
                columnMapping.setFieldType(column.getFieldType());
                columns.add(columnMapping);
            }
            ColumnMapping.setColumns(columns);
            list.add(ColumnMapping);
        }
        return list;
    }

    private ClassMeta builderInterfaces(Integer version, String outPutType, String interfaceName, String chineseName, ParamConfigVo paramConfigVo) {
        ClassMeta classMeta = ClassMeta.builder()
                .packageName(BASICPATH + StrUtil.lowerFirst(interfaceName))
                .name(StrUtil.upperFirst(interfaceName))
                .isInterface(true)
                .access(AccessFlag.PUBLIC)
                .memo(chineseName)
                .build();
        MethodMeta methodMeta = new MethodMeta();
        methodMeta.setName("query");
        int i = 0;
        for (ParamConfigVo.Param param : paramConfigVo.getParamList()) {
            param.setType(this.changeParamType(param.getType()));
            methodMeta.addParam(i, new Parameter(ServicePusblishUtil.changeTypePath(param.getType()), param.getParamName(), param.getMemo(), param.getParamCode(), param.getExample(), param.getIsMust().trim(), param.getLikeQuery(), param.getParamValue(), 0));
            i++;
        }

        //请求参数只有一个List<Map<String,Param>>
        methodMeta.addParam(i, new Parameter(ServicePusblishUtil.changeTypePath("BatchParamVo"), "batchParamVo", "batchParamVo", "batchParamVo", "batchParamVo", "t", "0", "batchParamVo", 1));
        methodMeta.setParameterConsumerTypeEnum(MethodMeta.ParameterRequestTypeEnum.JSON);
        List<ParamConfigVo.Param> outPuts = paramConfigVo.getGinsengList();
        List<MethodMeta.ReturnParam> returnParams = Lists.newArrayList();
        for (ParamConfigVo.Param outPut : outPuts) {
            MethodMeta.ReturnParam outParam = new MethodMeta.ReturnParam(outPut.getParamName(), outPut.getParamCode(), outPutType, outPut.getMemo(), outPut.getExample(), outPut.getType(), "t", outPut.getDesensitization(), outPut.getParamValue(), outPut.getDatasetId());
            returnParams.add(outParam);
        }

//        //返回参数只有一个List<Map<String,Param>>
//        List<MethodMeta.ReturnParam> returnParams = Lists.newArrayList();
//        MethodMeta.ReturnParam outParam = new MethodMeta.ReturnParam("resultList", "resultList", outPutType, "resultList", "resultList", "List","t","","resultList");
//        returnParams.add(outParam);

        methodMeta.setReturnParams(returnParams);
        methodMeta.setReturnType(outPutType);
        methodMeta.setPublish(Boolean.TRUE);
        methodMeta.setRuleEngineAcceptTypes(new String[]{});
        classMeta.addMethod(methodMeta);
        classMeta.setPromulgator(EnumPromulgator.DATA_CENTER);
        return classMeta;
    }


    private ClassMeta builderVersitionInterfaces(Integer version, String outPutType, String interfaceName, String chineseName, ParamConfigVo paramConfigVo) {
        ClassMeta classMeta = ClassMeta.builder()
                .packageName(BASICPATH + StrUtil.lowerFirst(interfaceName))
                .name(StrUtil.upperFirst(interfaceName))
                .isInterface(true)
                .access(AccessFlag.PUBLIC)
                .memo(chineseName)
                .build();
        MethodMeta methodMeta = new MethodMeta();
        methodMeta.setName("query");
        int i = 0;
        for (ParamConfigVo.Param param : paramConfigVo.getParamList()) {
            param.setType(this.changeParamType(param.getType()));
            methodMeta.addParam(i, new Parameter(ServicePusblishUtil.changeTypePath(param.getType()), param.getParamName(), param.getMemo(), param.getParamCode(), param.getExample(), param.getIsMust().trim(), param.getLikeQuery(), param.getParamValue()));
            i++;
        }
        methodMeta.addParam(i, new Parameter(ServicePusblishUtil.changeTypePath("BatchParamVo"), "batchParamVo", "batchParamVo", "batchParamVo", "batchParamVo", "t", "0", "batchParamVo", 1));
        methodMeta.setParameterConsumerTypeEnum(MethodMeta.ParameterRequestTypeEnum.JSON);

        List<ParamConfigVo.Param> outPuts = paramConfigVo.getGinsengList();
        List<MethodMeta.ReturnParam> returnParams = Lists.newArrayList();
        for (ParamConfigVo.Param outPut : outPuts) {
            MethodMeta.ReturnParam outParam = new MethodMeta.ReturnParam(outPut.getParamName(), outPut.getParamCode(), outPutType, outPut.getMemo(), outPut.getExample(), outPut.getType(), "t", outPut.getDesensitization(), outPut.getParamValue(), outPut.getDatasetId());
            returnParams.add(outParam);
        }
        methodMeta.setReturnParams(returnParams);
        methodMeta.setReturnType(outPutType);
        methodMeta.setPublish(Boolean.TRUE);
        methodMeta.setRuleEngineAcceptTypes(new String[]{});
        classMeta.addMethod(methodMeta);
        classMeta.setPromulgator(EnumPromulgator.DATA_CENTER);
        return classMeta;
    }


    private String changeParamType(String paramType) {
        if (Arrays.asList(STRINGTYPE).contains(paramType.toUpperCase())) {
            return "String";
        } else if (Arrays.asList(INTTYPE).contains(paramType.toUpperCase())) {
            return "Integer";
        } else if (Arrays.asList(FLOAT).contains(paramType.toUpperCase())) {
            return "Float";
        } else if (Arrays.asList(TIMETYPE).contains(paramType.toUpperCase())) {
            return "Date";
        } else {
            return "Double";
        }
    }

    private ClassMeta builderSingleTableImpl(Integer version, String servicePubcationId, String interfaceName, String implName, String chineseName,String userId, ParamConfigVo paramConfigVo) {
        ClassMeta implMeta = ClassMeta.builder()
                .packageName(BASICPATH + StrUtil.lowerFirst(interfaceName))
                .name(StrUtil.upperFirst(implName))
                .interfaces(new String[]{BASICPATH + StrUtil.lowerFirst(interfaceName) + "." + StrUtil.upperFirst(interfaceName)})
                .superClass(ABSTRCATREFERENCE)
                .addAnnotation(new AnnotationMeta("org.springframework.stereotype.Service"))
                .memo(chineseName)
                .version(version)
                .build();

        MethodMeta.Builder builder = MethodMeta.builder();
        List<AnnotationMeta> annotationMetas = iCicadaMetaServicePublishService.buildSensitiveAnnotations(paramConfigVo);
        if (CollectionUtil.isNotEmpty(annotationMetas)) {
            builder.annotations(annotationMetas);
        }
        builder.requestType(RequestType.POST);
        StringBuilder dataCenterBody = new StringBuilder();
        int i = 0;
        List<String> mandatoryParams = Lists.newArrayList();
        for (ParamConfigVo.Param param : paramConfigVo.getParamList()) {
            builder.addParameter(i, new Parameter(ServicePusblishUtil.changeTypePath(param.getType()), param.getParamName(), param.getMemo(), param.getParamCode(), param.getExample(), param.getIsMust().trim(), 0));
            if (("t").equals(param.getIsMust().trim())) mandatoryParams.add(param.getParamCode());
            i++;
        }
        builder.addParameter(i, new Parameter(ServicePusblishUtil.changeTypePath("BatchParamVo"), "batchParamVo", "batchParamVo", "batchParamVo", "batchParamVo", "t", 1));
        mandatoryParams.add("batchParamVo");

        dataCenterBody.append("{ java.util.List list=new java.util.ArrayList();");
        dataCenterBody.append("java.util.Map map = new java.util.HashMap(); ");
        //拿到参数与字段的映射
        List<ParamConfigVo.TableColumnMapping> tableColumnMappings = paramConfigVo.getTableColumnMappings();
        for (int j = 0; j < tableColumnMappings.size(); j++) {
            String datasetId = tableColumnMappings.get(j).getDatasetId();
            dataCenterBody.append("com.code.cicadas.datacenter.model.QueryParamMapping paramMapping" + j + "=new com.code.cicadas.datacenter.model.QueryParamMapping();");
            dataCenterBody.append("paramMapping" + j + ".setDatasetCode(\"" + tableColumnMappings.get(j).getDatasetName() + "\");");
            dataCenterBody.append("paramMapping" + j + ".setDatasetId(\"" + datasetId + "\");");
            String limitNum = 0==paramConfigVo.getMaxQueryNum()?"":paramConfigVo.getMaxQueryNum().toString();
            dataCenterBody.append("paramMapping" + j + ".setLimitNum(\"" + limitNum + "\");");
            //拿到该logicObj的sql
            String logicSql = dataSetEditService.getLogicSearchSQL(tableColumnMappings.get(j).getDatasetId()).replace("\"", "\\\"").replace("\n"," ");
            logicSql=dataSetEditService.transTransVar(logicSql,userId);
            dataCenterBody.append("paramMapping" + j + ".setSql(\"" + logicSql + "\");");
            dataCenterBody.append("java.util.Map column" + j + " = new java.util.HashMap(); ");
            //请求参数映射
            for (ParamConfigVo.ColumnMapping column : tableColumnMappings.get(j).getColumns()) {
                dataCenterBody.append("column" + j + ".put(\"" + column.getFieldAsCode() + "\"," + "\"" + column.getFieldCode() + "\");" + "");
            }
            //拿到该张表返回参数映射
            List<ParamConfigVo.TableColumnMapping> collect = paramConfigVo.getReturnParamMappings().stream().filter(s -> s.getDatasetId().equals(datasetId)).collect(Collectors.toList());
            dataCenterBody.append("java.util.Map returnColumn" + j + " = new java.util.HashMap(); ");
            dataCenterBody.append("java.util.Map orderColumn" + j + " = new java.util.LinkedHashMap(); ");
            for (ParamConfigVo.ColumnMapping column2 : collect.get(0).getColumns()) {
                dataCenterBody.append("returnColumn" + j + ".put(\"" + column2.getFieldAsCode() + "\"," + "\"" + column2.getFieldCode() + "\");" + "");
            }
            //找到这张表的排序字段
            List<List<ParamConfigVo.OrderField>> colist = paramConfigVo.getOrderFieldTables().stream().filter(s -> s.getDatasetId().equals(datasetId))
                    .map(ParamConfigVo.OrderFieldTable::getOrderFields).collect(Collectors.toList());
            if (!colist.isEmpty()) {
                for (ParamConfigVo.OrderField columnMapping : colist.get(0)) {
                    dataCenterBody.append("orderColumn" + j + ".put(\"" + columnMapping.getFieldCode() + "\"," + "\"" + columnMapping.getOrderType() + "\");");
                }
            }
            dataCenterBody.append("paramMapping" + j + ".setRequsetParamMapping(column" + j + ");");
            dataCenterBody.append("paramMapping" + j + ".setReturnParamMapping(returnColumn" + j + ");");
            dataCenterBody.append("paramMapping" + j + ".setOrderList(orderColumn" + j + ");");
            dataCenterBody.append("list.add(paramMapping" + j + ");");
        }

        //拿到 where 后面的 的语句
        String conditionfilterSql = ConditionFilterBuildUtils.toConditionSql(paramConfigVo);

        dataCenterBody.append(" String serviceId = this.checkBatchParams(\"$servicePublicationId\",$1);");
        dataCenterBody.append(" return  (" + OUTPUTTYPE + ")this.queryServiceData(\"" + paramConfigVo.getDbType() + "\",\"" + conditionfilterSql + "\",\"" + paramConfigVo.getModelId() + "\",$1,map,list,serviceId);}");

        builder.body(dataCenterBody.toString());
        builder.access(AccessFlag.PUBLIC)
                .addAnnotation(new AnnotationMeta("java.lang.Override"))
                .name("query")
                .parameterConsumerTypeEnum(MethodMeta.ParameterRequestTypeEnum.JSON)
                .returnProduceTypeEnum(MethodMeta.ParameterRequestTypeEnum.JSON)
                .returnType(OUTPUTTYPE).isPublish(true);
        implMeta.addMethod(builder.build());
        implMeta.setPromulgator(EnumPromulgator.DATA_CENTER);
        return implMeta;
    }


    private ClassMeta builderVerifcationSingleTableImpl(Integer version, String modelId, String interfaceName, String implName, String chineseName, String userId,ParamConfigVo paramConfigVo) {
        ClassMeta implMeta = ClassMeta.builder()
                .packageName(BASICPATH + StrUtil.lowerFirst(interfaceName))
                .name(StrUtil.upperFirst(implName))
                .interfaces(new String[]{BASICPATH + StrUtil.lowerFirst(interfaceName) + "." + StrUtil.upperFirst(interfaceName)})
                .superClass(ABSTRCATREFERENCE)
                .addAnnotation(new AnnotationMeta("org.springframework.stereotype.Service"))
                .memo(chineseName)
                .version(version)
                .build();

        MethodMeta.Builder builder = MethodMeta.builder();

        builder.requestType(RequestType.POST);
        StringBuilder dataCenterBody = new StringBuilder();
        //dataCenterBody.append("{ java.util.Map column = new java.util.HashMap(); ");
        int i = 0;
        List<String> mandatoryParams = Lists.newArrayList();
        for (ParamConfigVo.Param param : paramConfigVo.getParamList()) {
            builder.addParameter(i, new Parameter(ServicePusblishUtil.changeTypePath(param.getType()), param.getParamName(), param.getMemo(), param.getParamCode(), param.getExample(), param.getIsMust().trim(), 0));
            if (("t").equals(param.getIsMust().trim())) mandatoryParams.add(param.getParamCode());
            i++;
        }
        builder.addParameter(i, new Parameter(ServicePusblishUtil.changeTypePath("BatchParamVo"), "batchParamVo", "batchParamVo", "batchParamVo", "batchParamVo", "t", 1));
        mandatoryParams.add("batchParamVo");

        dataCenterBody.append("{ java.util.List list=new java.util.ArrayList();");
        dataCenterBody.append("java.util.Map map = new java.util.HashMap(); ");
        List<ParamConfigVo.TableColumnMapping> tableColumnMappings = paramConfigVo.getTableColumnMappings();
        for (int j = 0; j < tableColumnMappings.size(); j++) {

            dataCenterBody.append("com.code.cicadas.datacenter.model.ParamMappings paramMapping" + j + "=new com.code.cicadas.datacenter.model.ParamMappings();");
            dataCenterBody.append("paramMapping" + j + ".setTableName(\"" + tableColumnMappings.get(j).getDatasetName() + "\");");
            dataCenterBody.append("java.util.Map column" + j + " = new java.util.HashMap(); ");
            for (ParamConfigVo.ColumnMapping column : tableColumnMappings.get(j).getColumns()) {
                dataCenterBody.append("column" + j + ".put(\"" + column.getFieldAsCode() + "\"," + "\"" + column.getFieldCode() + "\");" + "");
            }
            dataCenterBody.append("paramMapping" + j + ".setColumnMaping(column" + j + ");");
            ParamConfigVo.Param param = paramConfigVo.getGinsengList().get(j);
            dataCenterBody.append("paramMapping" + j + ".setParamTrue(\"" + param.getParamName() + "\");");
            dataCenterBody.append("paramMapping" + j + ".setParamFalse(\"" + param.getParamValue() + "\");");
            dataCenterBody.append("paramMapping" + j + ".setParamField(\"" + param.getParamCode() + "\");");
            String logicSql = dataSetEditService.getLogicSearchSQL(tableColumnMappings.get(j).getDatasetId()).replace("\"", "\\\"").replace("\n"," ");
            //转义sql,动态参数调整
            logicSql=dataSetEditService.transTransVar(logicSql,userId);
            dataCenterBody.append("paramMapping" + j + ".setSql(\"" + logicSql + "\");");
            dataCenterBody.append("list.add(paramMapping" + j + ");");
        }
        dataCenterBody.append(" this.checkBatchParams(\"$servicePublicationId\",$1);");
        dataCenterBody.append(" return  (" + OUTPUTTYPE + ")this.queryData(\"" + paramConfigVo.getDbType() + "\",\"" + "" + "\",\"" + paramConfigVo.getModelId() + "\",$1,map,list);}");
        builder.body(dataCenterBody.toString());
        builder.access(AccessFlag.PUBLIC)
                .addAnnotation(new AnnotationMeta("java.lang.Override"))
                .name("query")
                .parameterConsumerTypeEnum(MethodMeta.ParameterRequestTypeEnum.JSON)
                .returnProduceTypeEnum(MethodMeta.ParameterRequestTypeEnum.JSON)
                .returnType(OUTPUTTYPE).isPublish(true);
        implMeta.addMethod(builder.build());
        implMeta.setPromulgator(EnumPromulgator.DATA_CENTER);
        return implMeta;
    }


    private ClassMeta builderManyTableImpl(Integer version, String modelId, String interfaceName, String implName, String chineseName, ParamConfigVo paramConfigVo) {
        ClassMeta implMeta = ClassMeta.builder()
                .packageName(BASICPATH + StrUtil.lowerFirst(interfaceName))
                .name(StrUtil.upperFirst(implName))
                .interfaces(new String[]{BASICPATH + StrUtil.lowerFirst(interfaceName) + "." + StrUtil.upperFirst(interfaceName)})
                .superClass(ABSTRCATREFERENCE)
                .addAnnotation(new AnnotationMeta("org.springframework.stereotype.Service"))
                .memo(chineseName)
                .version(version)
                .build();

        MethodMeta.Builder builder = MethodMeta.builder();
        List<AnnotationMeta> annotationMetas = iCicadaMetaServicePublishService.buildSensitiveAnnotations(paramConfigVo);
        if (CollectionUtil.isNotEmpty(annotationMetas)) {
            builder.annotations(annotationMetas);
        }
        builder.requestType(RequestType.POST);
        StringBuilder dataCenterBody = new StringBuilder();
        dataCenterBody.append("{ java.util.Map column = new java.util.HashMap(); ");
//        int i = 0;
//        List<String> mandatoryParams = Lists.newArrayList();
//        for (ParamConfigVo.Param param : paramConfigVo.getParamList()) {
//            dataCenterBody.append("  column.put(\"");
//            dataCenterBody.append("1".equals(param.getLikeQuery())?"$"+param.getParamValue():param.getParamValue());
//            dataCenterBody.append("\", ");
//            dataCenterBody.append("$");
//            dataCenterBody.append(i + 1);
//            dataCenterBody.append(");");
//            param.setType(this.changeParamType(param.getType()));
//            builder.addParameter(i, new Parameter(ServicePusblishUtil.changeTypePath(param.getType()), param.getParamName(), param.getMemo(), param.getParamCode(), param.getExample(), param.getIsMust().trim()));
//            if (("t").equals(param.getIsMust().trim())) mandatoryParams.add(param.getParamCode());
//            i++;
//        }
        int i = 0;
        List<String> mandatoryParams = Lists.newArrayList();
        for (ParamConfigVo.Param param : paramConfigVo.getParamList()) {
            dataCenterBody.append("  column.put(\"");
            dataCenterBody.append(param.getParamCode());
            dataCenterBody.append("\", ");
            dataCenterBody.append("1".equals(param.getLikeQuery()) ? "\"$" + param.getParamValue() + "\"" : "\"" + param.getParamValue() + "\"");
            dataCenterBody.append(");");
            param.setType(this.changeParamType(param.getType()));
            builder.addParameter(i, new Parameter(ServicePusblishUtil.changeTypePath(param.getType()), param.getParamName(), param.getMemo(), param.getParamCode(), param.getExample(), param.getIsMust().trim(), 0));
            if (("t").equals(param.getIsMust().trim())) mandatoryParams.add(param.getParamCode());
            i++;
        }
        builder.addParameter(i, new Parameter(ServicePusblishUtil.changeTypePath("BatchParamVo"), "batchParamVo", "batchParamVo", "batchParamVo", "batchParamVo", "t", 1));
        mandatoryParams.add("batchParamVo");

        String sql = builderManyTableSQL(paramConfigVo);

        StringBuffer stringBuffer = new StringBuffer(sql.replaceAll("\n", ""));
//        stringBuffer.append(ParamPrefixSuffix.prefix);
//        stringBuffer.append(String.join(ParamPrefixSuffix.spaceMark, mandatoryParams)).append(ParamPrefixSuffix.suffix);
        dataCenterBody.append(" this.checkBatchParams(\"$servicePublicationId\",$1);");
        dataCenterBody.append(" return  (" + OUTPUTTYPE + ")this.queryData(\"" + paramConfigVo.getDbType() + "\",\"" + stringBuffer.toString() + "\",\"" + paramConfigVo.getModelId() + "\",$1,column );}");

        builder.body(dataCenterBody.toString());
        builder.access(AccessFlag.PUBLIC)
                .addAnnotation(new AnnotationMeta("java.lang.Override"))
                .name("query")
                .parameterConsumerTypeEnum(MethodMeta.ParameterRequestTypeEnum.JSON)
                .returnProduceTypeEnum(MethodMeta.ParameterRequestTypeEnum.JSON)
                .returnType(OUTPUTTYPE).isPublish(true);
        implMeta.addMethod(builder.build());
        implMeta.setPromulgator(EnumPromulgator.DATA_CENTER);
        return implMeta;
    }

    private ClassMeta builderVerifcationManyTableImpl(Integer version, String modelId, String interfaceName, String implName, String chineseName, ParamConfigVo paramConfigVo) {
        ClassMeta implMeta = ClassMeta.builder()
                .packageName(BASICPATH + StrUtil.lowerFirst(interfaceName))
                .name(StrUtil.upperFirst(implName))
                .interfaces(new String[]{BASICPATH + StrUtil.lowerFirst(interfaceName) + "." + StrUtil.upperFirst(interfaceName)})
                .superClass(ABSTRCATREFERENCE)
                .addAnnotation(new AnnotationMeta("org.springframework.stereotype.Service"))
                .memo(chineseName)
                .version(version)
                .build();

        MethodMeta.Builder builder = MethodMeta.builder();
        builder.requestType(RequestType.POST);
        StringBuilder dataCenterBody = new StringBuilder();
        dataCenterBody.append("{ java.util.Map column = new java.util.HashMap(); ");
        int i = 0;
        List<String> mandatoryParams = Lists.newArrayList();
        for (ParamConfigVo.Param param : paramConfigVo.getParamList()) {
            dataCenterBody.append("  column.put(\"");
            dataCenterBody.append("1".equals(param.getLikeQuery()) ? "$" + param.getParamValue() : param.getParamValue());
            dataCenterBody.append("\", ");
            dataCenterBody.append("$");
            dataCenterBody.append(i + 1);
            dataCenterBody.append(");");
            param.setType(this.changeParamType(param.getType()));
            builder.addParameter(i, new Parameter(ServicePusblishUtil.changeTypePath(param.getType()), param.getParamName(), param.getMemo(), param.getParamCode(), param.getExample(), param.getIsMust().trim()));
            if (("t").equals(param.getIsMust().trim())) mandatoryParams.add(param.getParamCode());
            i++;
        }

//        LogicDataObj logicDataObj = logicDataObjService.findLogicDataObjById(paramConfigVo.getModelId());
//        Assert.notNull(logicDataObj, "找不到该数据集的实体！");
        String sql = builderVerifcationManyTableSQL(paramConfigVo);
//        if (logicDataObj.getBelongType().equals("QUICK_SQL")) {
//            sql = sql.replace(logicDataObj.getCode(), "(" + logicDataObj.getSql() + ")");
//        }
        StringBuffer stringBuffer = new StringBuffer(sql.replaceAll("\n", ""));
        stringBuffer.append(ParamPrefixSuffix.prefix);
        stringBuffer.append(String.join(ParamPrefixSuffix.spaceMark, mandatoryParams)).append(ParamPrefixSuffix.suffix);
        dataCenterBody.append(OUTPUTTYPE + " result = (" + OUTPUTTYPE + ")this.queryData(\"" + paramConfigVo.getDbType() + "\",\"" + stringBuffer.toString() + "\",\"" + paramConfigVo.getModelId() + "\",column );");
        dataCenterBody.append("String value=((java.util.Map)(result.getOperatorDataSet().get(0))).get(\"" + paramConfigVo.getGinsengList().get(0).getParamCode() + "\").toString().equals(\"0\")?\"" + paramConfigVo.getGinsengList().get(0).getParamValue() + "\":\"" + paramConfigVo.getGinsengList().get(0).getParamName() + "\";");
        dataCenterBody.append(SIMPLEDATASET + " simpleDataSet=new " + SIMPLEDATASET + "();\n" +
                "java.util.Map map = new java.util.HashMap();\n" +
                "map.put(\"" + paramConfigVo.getGinsengList().get(0).getParamCode() + "\",value);\n" +
                "simpleDataSet.add(map);\n" +
                "result.setOperatorDataSet(simpleDataSet);\n" +
                "return result;}");
        builder.body(dataCenterBody.toString());
        builder.access(AccessFlag.PUBLIC)
                .addAnnotation(new AnnotationMeta("java.lang.Override"))
                .name("query")
                .parameterConsumerTypeEnum(MethodMeta.ParameterRequestTypeEnum.FORM)
                .returnProduceTypeEnum(MethodMeta.ParameterRequestTypeEnum.JSON)
                .returnType(OUTPUTTYPE).isPublish(true);
        implMeta.addMethod(builder.build());
        implMeta.setPromulgator(EnumPromulgator.DATA_CENTER);
        return implMeta;
    }


    private ClassMeta builderImpl(Integer version, String modelId, String interfaceName, String implName, String chineseName, ParamConfigVo paramConfigVo) {
        ClassMeta implMeta = ClassMeta.builder()
                .packageName(BASICPATH + StrUtil.lowerFirst(interfaceName))
                .name(StrUtil.upperFirst(implName))
                .interfaces(new String[]{BASICPATH + StrUtil.lowerFirst(interfaceName) + "." + StrUtil.upperFirst(interfaceName)})
                .superClass(ABSTRCATREFERENCE)
                .addAnnotation(new AnnotationMeta("org.springframework.stereotype.Service"))
                .memo(chineseName)
                .version(version)
                .build();

        MethodMeta.Builder builder = MethodMeta.builder();
        builder.requestType(RequestType.POST);
        StringBuilder dataCenterBody = new StringBuilder();
        dataCenterBody.append("{ java.util.Map column = new java.util.HashMap(); ");
        int i = 0;
        List<String> mandatoryParams = Lists.newArrayList();
        for (ParamConfigVo.Param param : paramConfigVo.getParamList()) {
            dataCenterBody.append("  column.put(\"");
            dataCenterBody.append(param.getParamCode());
            dataCenterBody.append("\", ");
            dataCenterBody.append("$");
            dataCenterBody.append(i + 1);
            dataCenterBody.append(");");
            param.setType(this.changeParamType(param.getType()));
            builder.addParameter(i, new Parameter(ServicePusblishUtil.changeTypePath(param.getType()), param.getParamName(), param.getMemo(), param.getParamCode(), param.getExample(), param.getIsMust().trim()));
            if (("t").equals(param.getIsMust().trim())) mandatoryParams.add(param.getParamCode());
            i++;
        }

        LogicDataObj logicDataObj = logicDataObjService.findLogicDataObjById(paramConfigVo.getModelId());
        Assert.notNull(logicDataObj, "找不到该数据集的实体！");
        String sql = builderSQL(paramConfigVo.getDbType(), logicDataObj.getCode(), paramConfigVo.getFilterJson(), paramConfigVo.getGinsengList());
        if (logicDataObj.getBelongType().equals("QUICK_SQL")) {
            sql = sql.replace(logicDataObj.getCode(), "(" + logicDataObj.getSql() + ")");
        }
        StringBuffer stringBuffer = new StringBuffer(sql.replaceAll("\n", ""));
        stringBuffer.append(ParamPrefixSuffix.prefix);
        stringBuffer.append(String.join(ParamPrefixSuffix.spaceMark, mandatoryParams)).append(ParamPrefixSuffix.suffix);
        dataCenterBody.append(" return  (" + OUTPUTTYPE + ")this.queryData(\"" + paramConfigVo.getDbType() + "\",\"" + stringBuffer.toString() + "\",\"" + paramConfigVo.getModelId() + "\",column );}");

        builder.body(dataCenterBody.toString());
        builder.access(AccessFlag.PUBLIC)
                .addAnnotation(new AnnotationMeta("java.lang.Override"))
                .name("query")
                .parameterConsumerTypeEnum(MethodMeta.ParameterRequestTypeEnum.FORM)
                .returnProduceTypeEnum(MethodMeta.ParameterRequestTypeEnum.JSON)
                .returnType(OUTPUTTYPE).isPublish(true);
        implMeta.addMethod(builder.build());
        implMeta.setPromulgator(EnumPromulgator.DATA_CENTER);
        return implMeta;
    }

    private String builderSQL(String dbType, String tableName, String json, List<ParamConfigVo.Param> outParam) {

        List<ReleaseOutParamVo> outs = Lists.newArrayList();
        for (ParamConfigVo.Param param : outParam) {
            ReleaseOutParamVo vo = new ReleaseOutParamVo();
            vo.setFiledCode(param.getParamCode());
            outs.add(vo);
        }
        return dataFilterService.getQueryExp(dbType, tableName, json, outs);
    }

    private String builderSingleTableSQL(String dbType, String tableName, String json, List<ParamConfigVo.Param> outParam) {

        List<ReleaseOutParamVo> outs = Lists.newArrayList();
        for (ParamConfigVo.Param param : outParam) {
            ReleaseOutParamVo vo = new ReleaseOutParamVo();
            vo.setFiledCode(param.getParamCode());
            vo.setFiledName(param.getParamValue());
            outs.add(vo);
        }
        return dataFilterService.getQueryExp(dbType, tableName, json, outs);
    }

    private String builderVerifcationSingleTableSQL(String dbType, String tableName, String json, List<ParamConfigVo.Param> outParam) {

        List<ReleaseOutParamVo> outs = Lists.newArrayList();
        for (ParamConfigVo.Param param : outParam) {
            ReleaseOutParamVo vo = new ReleaseOutParamVo();
            vo.setFiledCode(param.getParamCode());
            vo.setFiledName(param.getParamValue());
            outs.add(vo);
        }
        return dataFilterService.getVercationQueryExp(dbType, tableName, json, outs);
    }


    private String builderManyTableSQL(ParamConfigVo dataQueryParamConfigVo) {

        //select的字段  `dm`,`mc`
        dataQueryParamConfigVo.getParamList().addAll(dataQueryParamConfigVo.getGinsengList());
        List<ParamConfigVo.Param> collect = dataQueryParamConfigVo.getParamList().stream().distinct().collect(Collectors.toList());
        String fieldSqlStr = collect.stream().map(s -> s.getParamValue() + " AS " + s.getParamCode()).collect(Collectors.joining(","));
        //from 表名
        String fromTableSql = buildManyTableJoinOnSql(dataQueryParamConfigVo);
        //拿到 where 后面的 的语句
        String conditionfilterSql = ConditionFilterBuildUtils.toConditionSql(dataQueryParamConfigVo);

        String resultSql = "select " + fieldSqlStr + " from " + fromTableSql;
        if (StringUtils.isBlank(conditionfilterSql)) {
            return resultSql;
        }
        return resultSql + " where " + conditionfilterSql;

    }

    private String builderVerifcationManyTableSQL(ParamConfigVo dataQueryParamConfigVo) {

        //select的字段  `dm`,`mc`
        //String fieldSqlStr=dataQueryParamConfigVo.getGinsengList().stream().map(s->s.getParamValue()+" AS "+"`"+s.getParamCode()+'`').collect(Collectors.joining(","));
        //from 表名
        String fromTableSql = buildManyTableJoinOnSql(dataQueryParamConfigVo);
        //拿到 where 后面的 的语句
        String conditionfilterSql = ConditionFilterBuildUtils.toConditionSql(dataQueryParamConfigVo);

        String resultSql = "select count(1) as " + dataQueryParamConfigVo.getGinsengList().get(0).getParamCode() + " from " + fromTableSql;
        if (StringUtils.isBlank(conditionfilterSql)) {
            return resultSql;
        }
        return resultSql + " where " + conditionfilterSql;


    }

    private String buildManyTableJoinOnSql(ParamConfigVo dataQueryParamConfigVo) {
        String tableJoinSql = "";
        List<ParamConfigVo.ManyJoinMeta> manyJoinMetas = dataQueryParamConfigVo.getManyJoinMetas();
        for (int i = 0; i < manyJoinMetas.size(); i++) {
            if (i == 0) {
                String tablesql = manyJoinMetas.get(i).getLeftStepCode() + " " + JoinOpEnum.getCodeByName(manyJoinMetas.get(i).getOp()) + " " + manyJoinMetas.get(i).getRightStepCode();
                String joinOnSql = manyJoinMetas.get(i).getJoinOnColumns().stream().map(s -> " " + s.getLeftColumnCode() + " = " + s.getRightColumnCode() + " ").collect(Collectors.joining(manyJoinMetas.get(i).getJoinOnOperate()));
                String relationsql = tablesql + " on " + joinOnSql + " ";
                tableJoinSql += relationsql;
            } else {
                String tablesql = " " + JoinOpEnum.getCodeByName(manyJoinMetas.get(i).getOp()) + " " + manyJoinMetas.get(i).getRightStepCode();
                String joinOnSql = manyJoinMetas.get(i).getJoinOnColumns().stream().map(s -> " " + s.getLeftColumnCode() + " = " + s.getRightColumnCode() + " ").collect(Collectors.joining(manyJoinMetas.get(i).getJoinOnOperate()));
                String relationsql = tablesql + " on " + joinOnSql + " ";
                tableJoinSql += relationsql;
            }
        }
        return tableJoinSql;
    }


}
