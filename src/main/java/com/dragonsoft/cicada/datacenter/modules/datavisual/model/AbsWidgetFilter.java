package com.dragonsoft.cicada.datacenter.modules.datavisual.model;


import com.code.meta.dml.standard.IMultCdin;
import com.code.meta.dml.standard.cdins.QueryCdins;
import com.code.metadata.datavisual.Field;


public abstract class AbsWidgetFilter {

    String id;
    Field field;
    String parentType;
    String type;

    public abstract IMultCdin builderCondition(QueryCdins queryCdins);

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Field getField() {
        return field;
    }

    public void setField(Field field) {
        this.field = field;
    }

    public String getParentType() {
        return parentType;
    }

    public void setParentType(String parentType) {
        this.parentType = parentType;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}
