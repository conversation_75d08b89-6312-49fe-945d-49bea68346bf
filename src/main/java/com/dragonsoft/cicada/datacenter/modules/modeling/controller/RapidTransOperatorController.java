package com.dragonsoft.cicada.datacenter.modules.modeling.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.code.common.utils.R;
import com.code.mist.builder.model.DubboResult;
import com.code.mist.builder.service.ITransformService;
import com.code.mist.builder.service.dubbo.ITransformApiService;
import com.dragoninfo.dfw.annotation.FuncScanAnnotation;
import com.dragoninfo.dfw.annotation.ValidateAndLogAnnotation;
import com.dragoninfo.dfw.bean.Result;
import com.dragonsoft.cicada.datacenter.modules.modeling.qo.UpdateTransQo;
import com.dragonsoft.cicada.datacenter.modules.modeling.service.DataMiningService;
import com.dragonsoft.cicada.datacenter.modules.modeling.service.DataModelingService;
import com.dragonsoft.cicada.datacenter.modules.modeling.service.TransCloneService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

@CrossOrigin
@RestController
@RequestMapping(value = "/transOperator/rapid")
@FuncScanAnnotation(code = "fastAnalysis", name = "快速分析", parentCode = "dataModeling")
public class RapidTransOperatorController {


    @Autowired
    private ITransformApiService transformApiService;

    @Autowired
    private DataModelingService dataModelingService;

    @Autowired
    private ITransformService transformService;

    @Autowired
    private TransCloneService transCloneService;

    @Autowired
    private DataMiningService dataMiningService;


    /**
     * 更新方案名称
     *
     * @param reqMap
     * @return
     */
    @PostMapping(value = "/updateTransName")
    @FuncScanAnnotation(code = "fastAnalysisUpdateTransName", name = "重命名", parentCode = "fastAnalysis")
    @ValidateAndLogAnnotation
    public Result updateTransName(@RequestBody Map reqMap) {
        String name = reqMap.get("name").toString();
        String transId = reqMap.get("transId").toString();
        DubboResult result = transformApiService.updateTransStepName(transId, name);
        return Result.toResult(R.ok(result.getMsg()));
    }

    /**
     * 方案复制
     *
     * @param transId
     * @param dirParentId
     * @return
     */
    @GetMapping(value = "/copyTrans")
    public Result copyTrans(String transId, String dirParentId) {
//        Map<String, String> map = transformService.copyTransId(transId, dirParentId);
        Map<String, String> map = transCloneService.saveCopyTrans(transId, dirParentId,null,null);
        return Result.toResult(R.ok(map));
    }

    /**
     * 方案另存为
     *
     * @param
     * @param map
     * @return
     */
    @PostMapping(value = "/asSaveTrans")
    @FuncScanAnnotation(code = "fastAnalysisCopyTrans", name = "另存为", parentCode = "fastAnalysis")
    @ValidateAndLogAnnotation
    public Result asSaveTrans(@RequestBody Map<String,String> map) {

        String transId = map.get("transId");
        String classifyId = map.get("classifyId");
        String transName = map.get("transName");
        String memo = map.get("memo");
        Map<String, String> mapData = transCloneService.saveCopyTrans(transId, classifyId,transName,memo);
        return Result.toResult(R.ok(mapData));
    }
    /**
     * 方案删除
     *
     * @param transId
     * @return
     */
    @GetMapping(value = "/deleteTrans")
    @FuncScanAnnotation(code = "fastAnalysisDeleteTrans", name = "删除", parentCode = "fastAnalysis")
    @ValidateAndLogAnnotation
    public Result deleteTrans(String transId) {
        DubboResult result = transformApiService.deleteTrans(transId);
        return Result.toResult(R.ok(result.getData()));
    }

    @GetMapping(value = "/moveModel")
    @FuncScanAnnotation(code = "fastAnalysisQueryTransTree", name = "移动", parentCode = "fastAnalysis")
    @ValidateAndLogAnnotation
    public Result moveModel(String elementId, String classifyId) {
        String msg = dataModelingService.moveModel(elementId, classifyId);
        if ("success".equals(msg)) {
            return Result.toResult(R.ok());
        } else {
            return Result.toResult(R.error(msg));
        }
    }

    @PostMapping(value = "/updateTrans")
    @FuncScanAnnotation(code = "fastAnalysisEdit", name = "编辑", parentCode = "fastAnalysis")
    @ValidateAndLogAnnotation
    public Result updateTrans(@RequestBody Map saveMap, HttpServletRequest request) {
        UpdateTransQo updateTransQo = new UpdateTransQo();
        updateTransQo.setUserId((String) request.getSession().getAttribute("userId"));
        updateTransQo.setTransName((String) saveMap.get("transName"));
        updateTransQo.setTransId((String) saveMap.get("transId"));
        updateTransQo.setClassifyId((String) saveMap.get("classifyId"));
        updateTransQo.setDirType((String) saveMap.get("dirType"));
        updateTransQo.setMemo((String) saveMap.get("memo"));
        updateTransQo.setVersion(ObjectUtil.isNotEmpty(saveMap.get("version"))?(Integer) saveMap.get("version") : null);
        updateTransQo.setProductionFirm(ObjectUtil.isNotEmpty(saveMap.get("productionFirm"))? StrUtil.toString(saveMap.get("productionFirm")):StrUtil.EMPTY);
        if (StrUtil.isEmpty(updateTransQo.getUserId())){
            throw new RuntimeException("请先登录后再操作！");
        }
        dataMiningService.updateTrans(updateTransQo);
        return Result.success();
    }

    @GetMapping(value = "/saveTempTrans")
    @FuncScanAnnotation(code = "fastAnalysisSaveTempTrans", name = "快速分析", parentCode = "fastAnalysis")
    @ValidateAndLogAnnotation
    public Result saveTempTrans(String transName,String transType,HttpServletRequest request) {
        String tempTransId = this.dataModelingService.saveTempTrans(transName,transType,request);
        return Result.toResult(R.ok(tempTransId));
    }
}
