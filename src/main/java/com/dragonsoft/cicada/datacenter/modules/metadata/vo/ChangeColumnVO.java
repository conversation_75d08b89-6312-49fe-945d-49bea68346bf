package com.dragonsoft.cicada.datacenter.modules.metadata.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ChangeColumnVO implements Serializable{

    private String rdbObjId;
    private String rdbType;
    private String key;
    private int addSize;
    private int deleteSize;
    private int updateSize;
    private List<ColumnProperty> addColumnList;
    private List<ColumnProperty> deleteColumnList;
    private List<ColumnProperty> updateColumnList;

    @Data
    public static class ColumnProperty implements Serializable {
        private String type;
        private String name;
        private String code;
        private String id;
        private String checkStatus;
        private String dataType;
    }


}
