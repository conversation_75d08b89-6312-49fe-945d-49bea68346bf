package com.dragonsoft.cicada.datacenter.modules.dataplan.externaldatasources.controller;

import com.code.common.utils.R;
import com.code.metaservice.base.vo.ClassifierPartitionVo;
import com.code.metaservice.base.vo.PartitionInfoVo;
import com.dragoninfo.dfw.bean.Result;
import com.dragonsoft.cicada.datacenter.modules.dataplan.externaldatasources.service.DatasetPartitionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@CrossOrigin
@RestController
@RequestMapping("/dataset/partition")
public class DatasetPartitionController {

    @Autowired
    private DatasetPartitionService datasetPartitionService;

    @GetMapping(value = "/getPartitionInfo")
    public Result getPartitionPage(String id, String partitionName) {
        List<ClassifierPartitionVo> list = datasetPartitionService.queryPartitionList(id, partitionName);
        return Result.success(R.ok(list));
    }

    @GetMapping(value = "/delete")
    public Result deletePartitionById(String id) {
        Map<String, String> data = datasetPartitionService.deleteDatasetPartitionById(id);
        return Result.toResult(R.ok(data));
    }

    @GetMapping(value = "/load")
    public Result loadOnePartitionById(String id) {
        PartitionInfoVo data = datasetPartitionService.queryPartitionRange(id);
        return Result.toResult(R.ok(data));
    }

    @GetMapping(value = "/changeStat")
    public Result toggleIsExecute(String id, String classifierId, String str) {
        Map<String, String> data = datasetPartitionService.updateExecutePartition(id, classifierId, str);
        return Result.toResult(R.ok(data));
    }

    @GetMapping(value = "/getField")
    public Result getFieldByPartitionId(String partitionId) {
        List<String> data = datasetPartitionService.findColumnInfomationByPartitionId(partitionId);
        return Result.toResult(R.ok(data));
    }

    @GetMapping(value = "/getFieldByClassifier")
    public Result getFieldByClassifierId(String classifierId) {
        List<String> data = datasetPartitionService.findColumnInfomationByClassifierId(classifierId);
        return Result.toResult(R.ok(data));
    }

    @PostMapping(value = "/save")
    public Result savePartitionAndRange(@RequestBody PartitionInfoVo partition) {
        Map<String, String> data = datasetPartitionService.saveOrUpdatePartition(partition);
        return Result.toResult(R.ok(data));
    }

}
