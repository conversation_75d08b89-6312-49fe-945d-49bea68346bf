package com.dragonsoft.cicada.datacenter.modules.system.permissions.service;

import com.code.common.paging.PageInfo;
import com.dragoninfo.dfw.entity.TSysAuthObj;
import com.dragoninfo.dfw.entity.TSysAuthRole;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.vo.PageVo;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.vo.RoleVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/5/21
 */
public interface IRoleService {
    /**
     * 通过角色Id或者名字模糊匹配角色
     *
     * @param pageVo
     * @return
     */
    PageInfo queryRolesPageByCodeOrName(PageVo pageVo);

    /**
     * 查询所有角色
     *
     * @return
     */
    List<TSysAuthRole> queryAllRole();

    /**
     * 查询所有角色
     *
     * @return
     */
    List<TSysAuthRole> queryDidsRoles();

    /**
     * 查询所有角色
     *
     * @return
     */
    List<String> queryAllRoleAuth(String dataSetId);

    /**
     * 通过ID查询角色
     *
     * @param roleId
     * @return
     */
    RoleVo queryRoleById(String roleId);

    /**
     * 更新角色信息
     *
     * @param roleVo
     */
    void upataRole(RoleVo roleVo);

    /**
     * 删除角色
     *
     * @param roleId
     */
    String deleteRole(String roleId);

    /**
     * 添加角色
     *
     * @param roleVo
     */
    void addRole(RoleVo roleVo);

    /**
     * 获取角色随机数
     *
     * @return
     */
    String getRoleRandom();

    /**
     *通过角色Id获取用户ID
     * @param roleId
     * @return
     */
    List<String> getUserIdsByRoleId(String roleId);

    List<TSysAuthObj> getTSysUserByRoleId(String roleId);
}
