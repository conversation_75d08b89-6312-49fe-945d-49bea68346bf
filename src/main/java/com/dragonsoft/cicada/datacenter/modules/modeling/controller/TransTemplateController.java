package com.dragonsoft.cicada.datacenter.modules.modeling.controller;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.code.common.bean.BeanFactory;
import com.code.common.encrypt.DragonEncryptor;
import com.code.common.utils.DateUtils;
import com.code.common.utils.StringUtils;
import com.code.common.utils.assertion.Assert;
import com.code.common.utils.io.SerializableMsgCodec;
import com.code.metadata.base.expression.ElementNode;
import com.code.metadata.base.expression.FeatureNode;
import com.code.metadata.business.directory.BaseBusiClassify;
import com.code.metadata.datavisual.Dashboard;
import com.code.metadata.datavisual.DashboardGroup;
import com.code.metadata.datavisual.Widget;
import com.code.metadata.datavisual.WidgetDataset;
import com.code.metadata.etl.trans.TransExpMeta;
import com.code.metadata.etl.trans.TransMeta;
import com.code.metadata.etl.trans.TransPluginMeta;
import com.code.metadata.model.core.DataType;
import com.code.metadata.portal.MenuConfig;
import com.code.metadata.portal.Portal;
import com.code.metadata.portal.PortalConfig;
import com.code.metadata.portal.PublishUrl;
import com.code.metadata.res.ddl.LogicDataColumn;
import com.code.metadata.res.ddl.LogicDataObj;
import com.code.metadata.udf.management.UdfGraph;
import com.code.metaservice.business.directory.BusiClassifyService;
import com.code.metaservice.datavisual.IDashBoardService;
import com.code.metaservice.datavisual.IDashboardGroupService;
import com.code.metaservice.ddl.ILogicDataObjService;
import com.code.metaservice.ddl.vo.LogicDataObjInfo;
import com.code.metaservice.etl.trans.TransMetaService;
import com.code.metaservice.portal.IMenuConfigService;
import com.code.metaservice.portal.IPortalConfigService;
import com.code.metaservice.portal.IPortalService;
import com.code.metaservice.portal.IPublishUrlService;
import com.code.metaservice.udf.management.IUdfGraphService;
import com.code.plugin.db.TypeMapping;
import com.code.std.types.NonStandardType;
import com.code.std.types.StandardType;
import com.dragoninfo.dfw.bean.Result;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.service.IDataSetEditService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.service.IDataSetOperationService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.util.DataSetUtils;
import com.dragonsoft.cicada.datacenter.modules.datavisual.dashboard.IDashboardBuilder;
import com.dragonsoft.cicada.datacenter.modules.modeling.service.TransTemplateService;
import com.fw.dao.hbmimpl.BaseDaoImpl;
import com.fw.service.BaseService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.JSQLParserException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StreamUtils;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.Serializable;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Wu.D.J
 * @Create : 2020.05.12
 */
@CrossOrigin
@RestController
@RequestMapping("/template")
@Slf4j
public class TransTemplateController {


    @Autowired
    private TransMetaService transMetaService;

    @Autowired
    private ILogicDataObjService logicDataObjService;

    @Autowired
    private IDashBoardService dashBoardService;

    @Autowired
    private IUdfGraphService udfGraphService;

    @Autowired
    private BaseDaoImpl baseDao;

    @Autowired
    private BaseService baseService;

    @Autowired
    private TransTemplateService transTemplateService;

    @Autowired
    private IDashboardBuilder dashboardBuilder;

    @Autowired
    private IDashboardGroupService dashboardGroupService;

    @Autowired
    private BusiClassifyService busiClassifyService;

    @Autowired
    IDataSetEditService editService;

    @Autowired
    private IPortalService portalService;

    @Autowired
    private IPortalConfigService portalConfigService;

    @Autowired
    private IMenuConfigService menuConfigService;

    @Autowired
    private IPublishUrlService publishUrlService;

    @Autowired
    BeanFactory beanFactory;


    @Autowired
    IDataSetOperationService dataSetOperationService;

    public TransTemplateController() {

    }

    public TransTemplateController(BaseDaoImpl baseDao, BaseService baseService, TransTemplateService transTemplateService) {
        this.baseDao = baseDao;
        this.baseService = baseService;
        this.transTemplateService = transTemplateService;
    }


    private final String ip = "{ip}";
    private final String orginal_user = "{orginal_user}";
    private final String encrypt_user = "{encrypt_user}";
    private final String password = "{password}";
    private final String port = "{port}";
    private final String db = "{db}";
    private final String type = "{type}";
    private final String schema = "{schema}";

    private final List<String> collisionPlugins = Lists.newArrayList("'fullJoinPlugin'", "'innerJoinPlugin'", "'leftOrRightJoinPlugin'", "'subtractByKeyPlugin'", "'unionJoinPlugin'");

    @RequestMapping("/correctionDataset")
    public Result correctionDataset(String id, String logicId, String oldLogicId) {
        Dashboard dashboard = dashBoardService.getDashboard(id);

        for (Widget widget : dashboard.getWidgets()) {
            if(widget.getBeforeData() != null) {
                widget.setBeforeData(widget.getBeforeData().replaceAll(oldLogicId, logicId));
            }
            if(widget.getQuery() != null) {
                widget.setQuery(widget.getQuery().replaceAll(oldLogicId, logicId));
            }
            WidgetDataset widgetDataset = widget.getWidgetDataset();
            if(widgetDataset.getClassifierStatId() != null && widgetDataset.getClassifierStatId().equals(oldLogicId)) {
                widgetDataset.setClassifierStatId(logicId);
            }
            if(widgetDataset.getDatasetId() != null && widgetDataset.getDatasetId().equals(oldLogicId)) {
                widgetDataset.setDatasetId(logicId);
            }
        }
        dashBoardService.saveOrUpdate(dashboard);
        return Result.success();
    }


    /**
     * 通过数仓id导出整个数仓的数据对象SQL脚本，其中涉及到一些需要替换的参数，
     * 比如用户名/密码等信息，则用特殊符号代替(↑↑↑↑↑↑↑↑上面的几个final String定义的成员变量)，
     * 部署的时候由部署人员通过match上面这些特殊符号，进行手动修改成具体有效值。
     * 还有一个需要注意的点：当前只支持 hwmpp 和 greenplum 两种类型的导出
     *
     * @param response 将sql文件流写入response
     * @param dirId    数据空间 - 数据源管理 - 目录树根节点 - 数据仓库id
     * @param version  二个类型可选：标准版 -> standard 、 基础版 -> basic （两个版本的区别问项目经理）
     * @return 整个数据仓库的SQL脚本文件
     * @throws IOException
     */
    @RequestMapping("/dataObjExport")
    public Result downloadDataObj(HttpServletResponse response, String dirId, String version, String dbType, String dbPort) throws IOException {
        boolean isSTD = "standard".equalsIgnoreCase(version);

        String software_id = getSoftwareId(dbType);
        StringBuilder builder = new StringBuilder();
        builder.append(buildMachineSQL(dirId)); // 导出机器信息
        builder.append(buildCatalogSQL(dirId, software_id, dbPort)); // 导出数据库实例信息
        builder.append(buildLabelElementSQL(dirId));  // 导出标签信息, 这个是管家系统需要的，缺失会导致导出的数据在管家无法正常显示和使用
        builder.append(buildClusterSQL(dirId, dbPort)); // 导出数据库集群信息
        builder.append(buildSchemaSQL(dirId));  // 导出数据库schema信息

        // 导出数仓目录信息
        builder.append(buildDirSQL(dirId));

        builder.append(buildClassifyTreeSql(dirId, isSTD));
        // 导出数仓的实例和库信息
        builder.append(buildDbInstanceSQL(dirId, isSTD, dbType));
        builder.append(buildClassifyElementSQL(dirId, isSTD));
        builder.append(buildDbMappingSQL(dirId, isSTD));

        // 导出元数据的表和字段信息
        builder.append(buildDataObjSQL(dirId, isSTD, dbType));
        builder.append(buildDataColumnSQL(dirId, isSTD));

        // 导出数据对象对应的权限控制信息
        builder.append(buildSysFuncSQL(dirId, isSTD));
        builder.append(buildSysAuthSQL(dirId, isSTD));

        // 导出数仓的表信息
        builder.append(buildDwTableSQL(dirId, isSTD));

//        TODO RDB主键信息与字段映射表存在bug，而且主键信息目前没有使用到，故不导出t_md_rdb_unique_key、t_md_rdb_uk_datacolumn
//        builder.append(buildUniqueKeySQL(dirId));
//        builder.append(buildUniqueKeyColumnSQL(dirId));
        try {
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("stand-model-data-schema.sql", "UTF-8"));
            StreamUtils.copy(builder.toString().getBytes(), response.getOutputStream());
            return Result.success();
        } finally {
            response.getOutputStream().close();
        }
    }

    /**
     * 通过数仓id导出整个数仓的表所对应的源数据集表
     *
     * @param response 将logic二进制文件流写入response
     * @param dirId    数据空间 - 数据源管理 - 目录树根节点 - 数据仓库id
     * @param version  二个类型可选：标准版 -> standard 、 基础版 -> basic （两个版本的区别问项目经理）
     * @return 整个数仓的表所对应的全部源数据集表(LogicDataObj)的二进制文件 (源数据集概念不懂可以问佳培)
     * @throws IOException
     */

    @RequestMapping("/exportDataset")
    public Result exportDataset(HttpServletResponse response, String dirId, String version, String dbType) throws IOException {
        boolean isSTD = "standard".equalsIgnoreCase(version);
        // 1. 通过数仓目录的id，找到数仓的表所对应的全部源数据集的id
        List<Map<String, String>> logicIds = getMetaLogicObjInfo(dirId, isSTD);

        // 2. 通过源数据集id，查询出全部源数据集对象(这个接口是佳培在维护)
        List<LogicDataObjInfo> logicDataObjInfos = logicIds.stream()
                .map(map -> logicDataObjService.getLogicDataObjInfo(map.get("id")))
                .collect(Collectors.toList());

        String[] ids = new String[logicIds.size()];
        for (int i = 0; i < logicIds.size(); i++) {
            Map<String, String> map = logicIds.get(i);
            String id = map.get("id");
            if (StringUtils.isNotBlank(id)) {
                ids[i] = "'" + id + "'";
            }
        }
        //修改logic_data数据库类型
        if (StringUtils.isNotBlank(dbType)) {
            for (LogicDataObjInfo logicDataObjInfo : logicDataObjInfos) {
                logicDataObjInfo.getLogicDataObj().setDbType(dbType);
                logicDataObjInfo.setDataColumns(transLogicColumnDataType(logicDataObjInfo.getLogicDataObj(),logicDataObjInfo.getDataColumns()));
            }
        }

        // 3. 通过源数据集id，找到所有源数据集与数仓目录的挂接关系
        String condition = String.join(",", ids);
        String sql = String.format("select * from t_md_logic_data_relation where logic_data_obj_id in (%s) and relation_type = '0'", condition);
        List<Map<String, Object>> list = baseDao.sqlQueryForList(sql);
        String relationSQL = list.stream().map(map -> insertSQL("t_md_logic_data_relation", map)).collect(Collectors.joining("\n"));

        // 4. 将全部源数据集对象和数仓目录挂接关系SQL，组装成LogicObjBean，序列化导出成logic文件
        LogicObjBean bean = new LogicObjBean(logicDataObjInfos, relationSQL);
        try {
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("stand_logic_dataset.logic", "UTF-8"));
            StreamUtils.copy(SerializableMsgCodec.encode(bean), response.getOutputStream());
            return Result.success();
        } finally {
            response.getOutputStream().close();
        }
    }


    /**
     * logic数据集字段类型更新
     * @param dataColumns
     * @param logicDataObj
     * @return
     */
    private List<LogicDataColumn> transLogicColumnDataType(LogicDataObj logicDataObj,List<LogicDataColumn> dataColumns ) {
        List<LogicDataColumn> logicDataColumnList = Lists.newArrayList();
        TypeMapping typeMapping = (TypeMapping) beanFactory.getBean("typeMapping");
        if (CollectionUtils.isNotEmpty(dataColumns)) {
            for (LogicDataColumn logicDataColumn : dataColumns) {
                LogicDataColumn dataColumn = new LogicDataColumn();
                BeanUtil.copyProperties(logicDataColumn, dataColumn);
                NonStandardType nonStandardType = DataSetUtils.changType(logicDataObj.getDbType(), logicDataColumn.getDataType().getCode());
                if(!(dataColumn.getDataType().getName().split("\\.")[0].startsWith("std"))){
                    StandardType trans = typeMapping.trans(nonStandardType);
                    dataColumn.setDataType(getStandDataTypeByCode(trans.getCode()));
                }
                logicDataColumnList.add(dataColumn);
            }
        }
        return logicDataColumnList;
    }

    private DataType getStandDataTypeByCode(String code) {
        String sql = "select t2.* from t_md_type_system t1 left join t_md_element t2 on t1.id = t2.owner_id where t1.code = 'STANDARD' and  t2.code  = '" + code + "'";
        List<Map> list = this.baseDao.sqlQueryForList(sql);
        if (CollectionUtils.isNotEmpty(list)) {
            DataType dataType = new DataType();
            dataType.setCode(list.get(0).get("code").toString());
            dataType.setId(list.get(0).get("id").toString());
            return dataType;
        }
        return null;
    }

    /**
     * 导入源数据集文件(该文件是上步↑↑↑↑↑↑↑↑导出的stand_logic_dataset.logic文件)
     *
     * @param path     源数据集文件(stand_logic_dataset.logic)的存放路径
     * @param dbSchema greenplum库的currentSchema参数，用来替换源数据集对象的schema信息
     * @return
     * @throws IOException
     */
    @RequestMapping("/importDataset")
    public Result importDataset(String path, String dbSchema, String dbType) throws IOException {
        File file = new File(path.trim());
        // 1. 反序列化stand_logic_dataset.logic文件
        byte[] bytes = FileUtils.readFileToByteArray(file);
        LogicObjBean bean = (LogicObjBean) SerializableMsgCodec.decode(bytes);
        List<LogicDataObjInfo> logicDataObjInfos = bean.getLogicDataObjInfos();
        String relationSQL = bean.getRelationSQL();
        if (logicDataObjInfos != null && !logicDataObjInfos.isEmpty()) {
            // 2. 修改源数据集对象的currentSchema信息
            changeLogicDataObjSQL(logicDataObjInfos, dbSchema);

            // 3. 保存源数据集对象信息(该接口佳培在维护)
            logicDataObjService.saveCloneLogicDataObj(null, logicDataObjInfos, true);

            // 4. 保存源数据集的目录挂接信息
            if (StringUtils.isNotBlank(relationSQL)) transTemplateService.sqlRun(relationSQL);

            // 5. 为源数据集创建数据库视图(该接口佳培在维护)
            //createViewsByModelImport(logicDataObjInfos, dbSchema);

            //将视图修改成数据集sql
            replaceLogicDataObjSearchsql();
        }
        return Result.success();
    }

    public void replaceLogicDataObjSearchsql() {
        try {
            editService.replaceLogicDataObjSearchsql();
        } catch (Exception e) {
            log.error(e.getMessage(),e);
        }
    }

    /**
     * 通过自定义树id导出整个自定义数据集表
     *
     * @param response   将logic二进制文件流写入response
     * @param classifyId 数据空间 - 数据连接 - 目录树节点
     * @param version    二个类型可选：标准版 -> standard 、 基础版 -> basic （两个版本的区别问项目经理）
     * @return 整个数仓的表所对应的全部源数据集表(LogicDataObj)的二进制文件 (源数据集概念不懂可以问佳培)
     * 自定义数据集导出也必须是一级目录，即我的，或者标准模型，不支持分享给我的导出
     * @throws IOException
     */
    @RequestMapping("/exportCustomDataset")
    public Result exportCustomDataset(HttpServletResponse response, String classifyId, String version) throws IOException {
        boolean isSTD = "standard".equalsIgnoreCase(version);
        StringBuilder dataSetSql = new StringBuilder();
        String dirId = getDirIdByClassifyId(classifyId);

        // 1. 通过自定义节点id（目录树的classifyId），找到根节点dir
        dataSetSql.append(buildDirSQLByClassifyId(classifyId));

        // 2. 构建自身节点sql
        // dataSetSql.append(buildClassifySqlByClassifyId(classifyId));

        // 3. 构建子节点目录sql 查询出多级  原有的查的数据层级不够全面
        dataSetSql.append(buildSonClassifySqlByClassifyId(classifyId));
        // 4. 构建目录节点与数据集关系 查询多级
        dataSetSql.append(buildCustomClassifyElementSQL(classifyId));

        // 4. 通过目录节点id，找到数仓的表所对应的全部自定义数据集的id
        List<Map<String, String>> logicIds = getMetaLogicObjInfo(dirId, isSTD);

        // 5. 通过源数据集id，查询出全部源数据集对象(这个接口是佳培在维护)
        List<LogicDataObjInfo> logicDataObjInfos = logicIds.stream()
                .map(map -> logicDataObjService.getLogicDataObjInfo(map.get("id")))
                .collect(Collectors.toList());

        String[] ids = new String[logicIds.size()];

        for (int i = 0; i < logicIds.size(); i++) {
            Map<String, String> map = logicIds.get(i);
            String id = map.get("id");
            if (StringUtils.isNotBlank(id)) {
                ids[i] = "'" + id + "'";
            }
        }

        // 6. 通过源数据集id，找到所有源数据集与数仓目录的挂接关系
        String condition = String.join(",", ids);
        //  0 逻辑数据集与数据源关系  1 逻辑数据集与数据集关系';
        String sql = String.format("select * from t_md_logic_data_relation where logic_data_obj_id in (%s) and relation_type = '0'", condition);
        List<Map<String, Object>> list = baseDao.sqlQueryForList(sql);
        String relationSQL = list.stream().map(map -> insertSQL("t_md_logic_data_relation", map)).collect(Collectors.joining("\n"));

        // 7. 将全部源数据集对象和数仓目录挂接关系SQL，组装成LogicObjBean，序列化导出成logic文件
        //LogicObjBean bean = new LogicObjBean(logicDataObjInfos, relationSQL, dataSetSql.toString());
        LogicObjBean bean = new LogicObjBean(logicDataObjInfos, relationSQL);
        try {
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("custom_logic_dataset.logic", "UTF-8"));
            StreamUtils.copy(SerializableMsgCodec.encode(bean), response.getOutputStream());
            return Result.success();
        } finally {
            response.getOutputStream().close();
        }
    }

    /**
     * 导入源数据集文件(该文件是上步↑↑↑↑↑↑↑↑custom_logic_dataset.logic文件)
     *
     * @param path     源数据集文件(custom_logic_dataset.logic)的存放路径
     * @param dbSchema greenplum库的currentSchema参数，用来替换源数据集对象的schema信息
     * @return
     * @throws IOException
     */
    @RequestMapping("/importCustomDataset")
    public Result importCustomDataset(String path, String dbSchema) throws IOException {
        File file = new File(path.trim());
        // 1. 反序列化stand_logic_dataset.logic文件
        byte[] bytes = FileUtils.readFileToByteArray(file);
        LogicObjBean bean = (LogicObjBean) SerializableMsgCodec.decode(bytes);
        List<LogicDataObjInfo> logicDataObjInfos = bean.getLogicDataObjInfos();
        String relationSQL = bean.getRelationSQL();
        // String classifySQL = bean.getClassifySQL();
        if (logicDataObjInfos != null && !logicDataObjInfos.isEmpty()) {
            // 2. 修改源数据集对象的currentSchema信息
            changeLogicDataObjSQL(logicDataObjInfos, dbSchema);

            // 3. 保存源数据集对象信息(该接口佳培在维护)
            logicDataObjService.saveCloneLogicDataObj(null, logicDataObjInfos, true);

            // 4. 保存源数据集的目录挂接信息
            if (StringUtils.isNotBlank(relationSQL)) transTemplateService.sqlRun(relationSQL);

            // 5. 为源数据集创建数据库视图(该接口佳培在维护)
            //  createViewsByModelImport(logicDataObjInfos, dbSchema);
            // 5.将视图转化为数据集
            replaceLogicDataObjSearchsql();
        }
        return Result.success();
    }

    private void changeLogicDataObjSQL(List<LogicDataObjInfo> logicDataObjInfos, String dbSchema) {
        for (LogicDataObjInfo logicDataObjInfo : logicDataObjInfos) {
            LogicDataObj logicDataObj = logicDataObjInfo.getLogicDataObj();
            if (logicDataObj.getCode().contains(".")) {
                String oldSchema = logicDataObj.getCode().split("\\.")[0];
                String tableCode = logicDataObj.getCode().replace(oldSchema, dbSchema);
                logicDataObj.setCode(tableCode);
            }
            String newSQL = transTemplateService.addSchemaNameToTableName(dbSchema, logicDataObj.getSql());
            logicDataObj.setSql(newSQL);
        }
    }

    public static void main(String[] args) throws IOException, JSQLParserException {
        String path = "/Users/<USER>/Desktop/stand_logic_dataset.logic";
        File file = new File(path.trim());
        byte[] bytes = FileUtils.readFileToByteArray(file);
        LogicObjBean bean = (LogicObjBean) SerializableMsgCodec.decode(bytes);
        List<LogicDataObjInfo> logicDataObjInfos = bean.getLogicDataObjInfos();
        String relationSQL = bean.getRelationSQL();
        System.out.println(relationSQL);
    }

    /**
     * 通过id找到对应的方案(流程、可视化、数据集)，合在一起导出一份二进制模型文件
     *
     * @param portalIds
     * @param response
     * @param modelName 导出的二进制文件的文件名称
     * @param transIds  要导出的数据挖掘方案，多个用,隔开，也可不传
     * @param visualIds 要导出的可视化方案，多个用,隔开，也可不传
     * @param logicIds  要导出的数据集方案，多个用,隔开，也可不传
     * @return
     * @throws IOException
     */
    @RequestMapping("/modelExport")
    public Result downloadModel(HttpServletResponse response, String modelName, String transIds, String visualIds, String logicIds, String portalIds, String dbType) throws IOException {
        ExportBean bean = new ExportBean();
        // 1. 导出数据挖掘方案
        if (StringUtils.isNotBlank(transIds)) {
            List<TransMeta> transMetas = new ArrayList<>();
            List<Map<String, Object>> transPlugins = Lists.newArrayList();
            List<UdfGraph> graphs = new ArrayList<>();
            List<String> transTasks = Lists.newArrayList();
            List<String> transSchedules = Lists.newArrayList();
            List<String> transSubTransRelations = Lists.newArrayList();
            for (String transId : transIds.split(",")) {
                // 导出TransMeta对象
                TransMeta transMeta = transMetaService.getTransMetaById(transId);
                // transPluginSQL.append(buildTransPluginSQL(transId));
                //导出插件元信息
                transPlugins.addAll(buildTransPlugins(transId));
                //导出碰撞插件不同的元信息
                if (CollectionUtils.isNotEmpty(transPlugins.stream().filter(s -> ("collisionPlugin").equalsIgnoreCase((String) s.get("code"))).collect(Collectors.toList())))
                    transPlugins.addAll(buildTransCollisionPlugins(collisionPlugins));
                for (TransMeta child : transMeta.getChildren()) {
                    TransPluginMeta usedPlugin = child.getUsedPlugin();

                    // 由于算子编排有自己的数据库实体，导入时需要做特殊处理，因此导出时也要查询出UdfGraph对象，单独存放
                    if ("serviceOrganization".equals(usedPlugin.getCode())) {  // 处理算子编排
                        FeatureNode featureNode = usedPlugin.getPluginExp("serviceOrgOutputs");
                        ElementNode elementNode = (ElementNode) featureNode.getArgument("serviceOrgId");
                        Set<TransExpMeta> transExps = child.getTransExps();
                        for (TransExpMeta exp : transExps) {
                            String expValue = exp.getExpValue(elementNode);
                            UdfGraph graph = udfGraphService.get(UdfGraph.class, expValue);
                            if (null != graph)
                                graphs.add(graph);
                        }
                    }
                }

                // 导出方案对应的调度配置信息
                buildScheduleInfos(transId, transTasks, transSchedules, transSubTransRelations);
                transMetas.add(transMeta);
            }
            TransScheduleInfo scheduleInfo = new TransScheduleInfo();
            scheduleInfo.setTransSchedules(transSchedules);
            scheduleInfo.setTransSubTransRelations(transSubTransRelations);
            scheduleInfo.setTransTasks(transTasks);
            transPlugins = filterMap(transPlugins, "id");
            String params = collectParams(transPlugins, "id");

            bean.setTransPluginPartitions(buildTransPluginPartitions(transPlugins, "partition"));
            bean.setTransPluginExpRelations(buildTransPluginPartitions(transPlugins, "relation"));
            bean.setTransPluginExpNodes(buildTransPluginPartitions(transPlugins, "expNode"));
            bean.setTransPluginExpNodeRelation(buildTransPluginPartitions(transPlugins, "expNodeRelation"));
            //bean.setTransPluginTransExp(buildTransPluginPartitions(transPlugins, "transExp"));
            bean.setTransPluginTransAttributes(buildTransPluginPartitions(transPlugins, "attribute"));
            bean.setTransPlugins(transPlugins);
            bean.setTransMetas(transMetas);
            bean.setUdfGraphs(graphs);
            bean.setScheduleInfo(scheduleInfo);
        }

        // 2.导出数据集方案
        if (StringUtils.isNotBlank(logicIds)) {
            List<LogicDataObjInfo> logics = new ArrayList<>();
            for (String id : logicIds.split(",")) {
                LogicDataObjInfo logicDataObjInfo = logicDataObjService.getLogicDataObjInfo(id);
                logicDataObjInfo.getLogicDataObj().setDbType(dbType);
                logicDataObjInfo.setDataColumns(transLogicColumnDataType(logicDataObjInfo.getLogicDataObj(),logicDataObjInfo.getDataColumns()));
                logics.add(logicDataObjInfo); // 该接口佳培在维护
            }

            bean.setLogics(logics);
        }

        // 3. 导出可视化方案
        if (StringUtils.isNotBlank(visualIds)) {
            List<Dashboard> dashboards = new ArrayList<>();
            for (String id : visualIds.split(",")) {
                Dashboard dashboard = dashBoardService.getDashboard(id);
                if (dashboard != null) {
                    dashboards.add(dashboard);
                }
            }
            bean.setDashboards(dashboards);
        }
        //导出门户
        if (StringUtils.isNotBlank(portalIds)) {
            List<PortalInfo> portalInfos = Lists.newArrayList();
            for (String portalId : portalIds.split(",")) {
                PortalInfo portalInfo = new PortalInfo();
                Portal portal = portalService.get(Portal.class, portalId);
                PortalConfig portalConfig = portalConfigService.queryPortalConfigByPortalId(portalId);
                List<MenuConfig> menuConfigs = menuConfigService.queryMenuConfigByConfigId(portalConfig.getId());
                PublishUrl publishUrl = publishUrlService.queryPublishUrlByPortalId(portalId);

                if (null != portal) {
                    portalInfo.setPortal(portal);
                    portalInfo.setPortalConfig(portalConfig);
                    portalInfo.setMenuConfigs(menuConfigs);
                    portalInfo.setPublishUrl(publishUrl);
                    portalInfos.add(portalInfo);
                }
            }
            bean.setPortalInfos(portalInfos);
        }

        try {
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(modelName + ".model", "UTF-8"));
            StreamUtils.copy(SerializableMsgCodec.encode(bean), response.getOutputStream());
            return Result.success();
        } finally {
            response.getOutputStream().close();
        }
    }

    public void buildScheduleInfos(String transId, List<String> transTasks, List<String> transSchedules, List<String> transSubTransRelations) {
        // 导出方案对应的调度配置信息
        Map<String, String> param = Maps.newHashMap();
        param.put("trans_id", transId);
        Map taskMap = baseDao.sqlQueryForMap("select * from t_trans_task where trans_id = :trans_id", param);
        if (taskMap.size() > 0) {
            transTasks.add(JSON.toJSONString(taskMap));
        }
        Map scheduleMap = baseDao.sqlQueryForMap("select * from t_trans_schedule where trans_id = :trans_id", param);
        if (scheduleMap.size() > 0) {
            transSchedules.add(JSON.toJSONString(scheduleMap));
        }
        List<Map> relationList = baseDao.sqlQueryForList("select * from t_trans_subtrans_relation where trans_id = :trans_id", param);
        for (Map relationMap : relationList) {
            if (relationMap.size() > 0) {
                transSubTransRelations.add(JSON.toJSONString(relationMap));
            }
        }
    }

    /**
     * 导入二进制模型文件(该文件是上步↑↑↑↑↑↑↑↑导出的.model文件)
     *
     * @param path       二进制模型文件(xxxxxxx.model)的存放路径
     * @param dbSchema   greenplum库的currentSchema参数，用来替换数据集对象的schema信息
     * @param classifyId 导入的数据集的位置
     * @return
     * @throws IOException
     */
    @RequestMapping("/modelImport")
    public Result modelImport(String path, String dbSchema, String classifyId) throws IOException {
        // 1. 反序列化 .model 模型文件
        File file = new File(path.trim());
        String modelName = file.getName().split("\\.")[0];
        System.out.println(modelName);
        String transSuffix = "模型方案";
        String logicSuffix = "数据集方案";
        String visualSuffix = "可视化方案";
        String portalSuffix = "门户方案";
        byte[] bytes = FileUtils.readFileToByteArray(file);
        ExportBean exportBean = (ExportBean) SerializableMsgCodec.decode(bytes);
        List<TransMeta> transMetas = exportBean.getTransMetas();
        List<LogicDataObjInfo> logics = exportBean.getLogics();
        List<Dashboard> dashboards = exportBean.getDashboards();
        List<UdfGraph> udfGraphs = exportBean.getUdfGraphs();
        List<Map<String, Object>> transPlugins = exportBean.getTransPlugins();
        TransScheduleInfo scheduleInfo = exportBean.getScheduleInfo();
        List<PortalInfo> portalInfos = exportBean.getPortalInfos();

        // 2. 导入数据挖掘方案
        if (transMetas != null && !transMetas.isEmpty()) {
            //先导入插件的元信息
            String transPluginSQL = buildTransPluginSQL(transPlugins);
            String pluginMetaSQL = buildTransPluginExpSQL(exportBean);
            if (StringUtils.isNotBlank(transPluginSQL)) transTemplateService.sqlRun(transPluginSQL);
            if (StringUtils.isNotBlank(pluginMetaSQL)) transTemplateService.sqlRun(pluginMetaSQL);
            // 通过 modelName 查找或创建标准模型目录
            BaseBusiClassify transClassify;
            if (StringUtils.isNotBlank(classifyId))
                transClassify = (BaseBusiClassify) this.baseService.get(BaseBusiClassify.class, classifyId);
            else
                transClassify = transTemplateService.queryClassify("TRANS_DIR_STANDARD");

            Assert.notNull(transClassify, "数据挖掘目录不存在!");
            BaseBusiClassify transChildClassify = transTemplateService.queryModelClassify(modelName + transSuffix, transClassify.getId());
            if (transChildClassify == null) {
                transChildClassify = new BaseBusiClassify();
                transChildClassify.setParentBc(transClassify);
                transChildClassify.setBusiDir(transClassify.getBusiDir());
                transChildClassify.setName(modelName + transSuffix);
                transChildClassify.setCode(modelName + transSuffix);
                transChildClassify.setOperateTime(DateUtils.getTimeStr(new Date(), 2));
                transChildClassify.setOperateUserId(transClassify.getOperateUserId());
                baseService.saveOrUpdate(transChildClassify);
            }
            Map<String, String> transMapping = new HashMap<>();
            Map<String, UdfGraph> graphMap = new HashMap<>();

            if (udfGraphs != null && !udfGraphs.isEmpty()) {
                graphMap = udfGraphs.stream().collect(Collectors.toMap(UdfGraph::getId, o -> o));
            }
            for (TransMeta t : transMetas) {
//                String n_id = transTemplateService.saveCopyTransModel(t, graphMap, transChildClassify);
                // 将TransMeta对象解析成SQL语句，并保存
                String n_id = transTemplateService.saveTransModelBySQL(t, graphMap, transChildClassify);
                transMapping.put(t.getId(), n_id);
            }

            // 保存方案相关的调度、任务依赖信息
            if (scheduleInfo != null) {
                List<String> executeList = new ArrayList<>();
                Map<String, String> taskMapping = new HashMap<>();
                for (String json : scheduleInfo.getTransTasks()) {
                    Map map = JSON.parseObject(json, Map.class);
                    String uuid = StringUtils.uuid();
                    taskMapping.put((String) map.get("id"), uuid);
                    map.put("id", uuid);
                    map.put("trans_id", transMapping.get(map.get("trans_id")));
                    map.put("run_param", null);
                    executeList.add(insertSQL("t_trans_task", map));
                }
                for (String json : scheduleInfo.getTransSchedules()) {
                    Map map = JSON.parseObject(json, Map.class);
                    map.put("id", StringUtils.uuid());
                    map.put("trans_id", transMapping.get(map.get("trans_id")));
                    map.put("task_id", taskMapping.get(map.get("task_id")));
                    executeList.add(insertSQL("t_trans_schedule", map));
                }
                for (String json : scheduleInfo.getTransSubTransRelations()) {
                    Map map = JSON.parseObject(json, Map.class);
                    map.put("id", StringUtils.uuid());
                    map.put("trans_id", transMapping.get(map.get("trans_id")));
                    map.put("subtrans_id", transMapping.get(map.get("trans_id")));
                    executeList.add(insertSQL("t_trans_subtrans_relation", map));
                }
                String taskSQL = String.join(" ", executeList);
                if (StringUtils.isNotBlank(taskSQL)) transTemplateService.saveSQL(taskSQL);
            }
        }

        // 导入数据集管理方案
        if (logics != null && !logics.isEmpty()) {
            // 通过 modelName 查找或创建标准模型目录
            BaseBusiClassify baseBusiClassify = addBusiClassify(classifyId, modelName, logicSuffix, "DATA_SET_DIR_STANDARD");

            // 保存数据集管理方案(佳培维护)
            logicDataObjService.saveCloneLogicDataObj(baseBusiClassify.getId(), logics, true);
            //创建视图(佳培维护)
            // createViewsByModelImport(logics, dbSchema);
            replaceLogicDataObjSearchsql();
        }

        // 导入可视化方案
        if (dashboards != null && !dashboards.isEmpty()) {
            // 通过 modelName 查找或创建标准模型目录
            DashboardGroup dashboardGroup;
            if (StringUtils.isNotBlank(classifyId))
                dashboardGroup = (DashboardGroup) this.baseService.get(DashboardGroup.class, classifyId);
            else
                dashboardGroup = transTemplateService.queryGroup("BOARD_DIR_STANDARD");
            Assert.notNull(dashboardGroup, "可视化标准模型目录不存在!");
            String boardId = transTemplateService.queryExistDashboardGroup(dashboardGroup.getId(), modelName + visualSuffix);
            if (StringUtils.isBlank(boardId)) {
                DashboardGroup ds = new DashboardGroup();
                ds.setParentId(dashboardGroup.getId());
                ds.setName(modelName + visualSuffix);
                ds.setCode(modelName + visualSuffix);
                ds.setOperateTime(DateUtils.getTimeStr(new Date(), 2));
                ds.setOperateUserId(dashboardGroup.getOperateUserId());
                ds.setId(StringUtils.uuid());
                dashboardGroupService.saveGroup(ds);
                boardId = ds.getId();
            }
            // 保存可视化方案(调用可视化模块克隆方案)
            for (Dashboard d : dashboards) if (d != null) dashboardBuilder.copyDashboard(d, boardId);
        }
        if (null != portalInfos && !portalInfos.isEmpty()) {
            // 通过 modelName 查找或创建标准模型目录
            BaseBusiClassify baseBusiClassify = addBusiClassify(classifyId, modelName, portalSuffix, "POR_DIR_STANDARD");
            //保存主题门户方案
            for (PortalInfo portalInfo : portalInfos) {
                Portal portal = portalInfo.getPortal();
                Assert.notNull(portal, "主题门户不存在!");
                portal.setId(null);
                portal.setOwner(baseBusiClassify);
                portalService.saveOrUpdate(portal);
                PortalConfig portalConfig = portalInfo.getPortalConfig();
                if (null != portalConfig) {
                    portalConfig.setOwner(portal);
                    portalConfig.setId(null);
                    portalConfigService.saveOrUpdate(portalConfig);

                    List<MenuConfig> menuConfigs = portalInfo.getMenuConfigs();
                    if (CollectionUtils.isNotEmpty(menuConfigs)) {
                        tSaveMenuConfig(menuConfigs, portalConfig, null);
                    }

                }
                PublishUrl publishUrl = portalInfo.getPublishUrl();
                if (null != publishUrl && publishUrl.getId() != null) {
                    publishUrl.setOwner(portal);
                    publishUrl.setId(null);
                    publishUrlService.saveOrUpdate(publishUrl);
                }

            }
        }
        return Result.success();
    }


    public BaseBusiClassify addBusiClassify(String classifyId, String modelName, String suffix, String busiClassifyCode) {
        BaseBusiClassify classify;
        if (StringUtils.isNotBlank(classifyId))
            classify = this.baseService.get(BaseBusiClassify.class, classifyId);
        else
            classify = transTemplateService.queryClassify(busiClassifyCode);
        Assert.notNull(classify, "目录不存在!");
        BaseBusiClassify childClassify = transTemplateService.queryModelClassify(modelName + suffix, classify.getId());
        if (childClassify == null) {
            childClassify = new BaseBusiClassify();
            childClassify.setParentBc(classify);
            childClassify.setName(modelName + suffix);
            childClassify.setCode(modelName + suffix);
            childClassify.setOperateTime(DateUtils.getTimeStr(new Date(), 2));
            childClassify.setOperateUserId(classify.getOperateUserId());
            baseService.saveOrUpdate(childClassify);
        }
        return childClassify;
    }


    private void tSaveMenuConfig(List<MenuConfig> menuConfigs, PortalConfig portalConfig, String parentId) {
        for (MenuConfig menuConfig : menuConfigs) {
            menuConfig.setOwner(portalConfig);
            menuConfig.setId(null);
            if ("dashboard".equals(menuConfig.getContentType())) {
                menuConfig.setPostURLParam(queryDashGroup(menuConfig.getContentGetURl()));
            }
            menuConfig.setParentMenuConfig(parentId == null ? null : menuConfigService.get(MenuConfig.class, parentId));
            menuConfigService.saveOrUpdate(menuConfig);
            if (CollectionUtils.isNotEmpty(menuConfig.getChildrenMenuConfigs())) {
                List<MenuConfig> childMenuConfigs = new ArrayList<>(menuConfig.getChildrenMenuConfigs());
                this.tSaveMenuConfig(childMenuConfigs, portalConfig, menuConfig.getId());
            }
        }
    }

    private String queryDashGroup(String id) {
        String sql = "select group_id from t_v_dashboards where id =:id";
        Map<String, String> params = new HashMap<>();
        params.put("id", id);
        return this.baseDao.sqlQueryForValue(sql, params);
    }

    private void createViewsByModelImport(List<LogicDataObjInfo> logics, String dbSchema) {

        Map<String, List<LogicDataObjInfo>> ownerClassify = getLogicByOwnerClassify(logics);
        //存储owner是classify的数据集
        Map<String, LogicDataObj> classifyMap = Maps.newHashMap();
        for (LogicDataObjInfo logic : ownerClassify.get("classify")) {
            try {
                transTemplateService.createViewByLogicInfo(logic, dbSchema);
                classifyMap.put(logic.getLogicDataObj().getId(), logic.getLogicDataObj());
            } catch (Exception e) {
                log.error(e.getMessage(),e);
            }
        }
        for (LogicDataObjInfo logic : ownerClassify.get("logic")) {
            try {
                transTemplateService.createViewByLogicInfo(logic, classifyMap, dbSchema);
            } catch (Exception e) {
                log.error(e.getMessage(),e);
            }
        }
    }

    private Map<String, List<LogicDataObjInfo>> getLogicByOwnerClassify(List<LogicDataObjInfo> logics) {
        Map<String, List<LogicDataObjInfo>> resMap = Maps.newHashMap();
        List<LogicDataObjInfo> classifyList = Lists.newArrayList();
        List<LogicDataObjInfo> logicList = Lists.newArrayList();
        for (LogicDataObjInfo logic : logics) {
            String ownerId = logic.getLogicDataObj().getOwnerId();
            //判断owner是逻辑数据集还是物理表
            boolean check = judgeDataSet(ownerId);
            if (check) {
                logicList.add(logic);
            } else {
                classifyList.add(logic);
            }
        }
        resMap.put("classify", classifyList);
        resMap.put("logic", logicList);
        return resMap;
    }

    private boolean judgeDataSet(String ownerId) {
        String type = getDataSetType(ownerId);
        if (type != null && !"LogicDataObj".equalsIgnoreCase(type)) {
            //owner是物理表return false
            return false;
        }
        return true;
    }

    private String getDataSetType(String ownerId) {
        String queryType = "select type from t_md_classifier_stat where id = :id";
        Map<String, String> param = Maps.newHashMap();
        param.put("id", ownerId);
        return this.baseDao.sqlQueryForValue(queryType, param);
    }

    private String getLogicIds(List<LogicDataObjInfo> logics) {
        if (logics.isEmpty()) return "";
        String res = "";
        for (int i = 0; i < logics.size(); i++) {
            if (i == 0) {
                res += logics.get(i).getLogicDataObj().getId();
            } else {
                res += "," + logics.get(i).getLogicDataObj().getId();
            }
        }
        return res;
    }

    private String buildMachineSQL(String dirId) {
        String sql = "SELECT A  " +
                " .*   " +
                "FROM  " +
                " t_md_machine A,  " +
                " t_md_rdbcatalog_cluster b,  " +
                " t_md_rdb_catalog C,  " +
                " ( SELECT DISTINCT deployed_software_id FROM t_md_dw_db_instance WHERE dir_id = :dir_id ) d   " +
                "WHERE  " +
                " C.ID = d.deployed_software_id   " +
                " AND b.owner_id = C.ID   " +
                " AND b.machine_id = A.ID";
        Map<String, String> replace = new HashMap<>();
        replace.put("name", ip);
        replace.put("code", ip);
        replace.put("ip_address", ip);
        return standardSimpleBuildSql(dirId, sql, "t_md_machine", replace);
    }

    private String buildCatalogSQL(String dirId, String software_id, String dbPort) {
        String sql = "SELECT A  " +
                " .*   " +
                "FROM  " +
                " t_md_rdb_catalog A,  " +
                " ( SELECT DISTINCT deployed_software_id FROM t_md_dw_db_instance WHERE dir_id = :dir_id ) b   " +
                "WHERE  " +
                " A.ID = b.deployed_software_id";
        Map<String, String> replace = new HashMap<>();
        replace.put("user_name", encrypt_user);
        replace.put("password", password);
        replace.put("port", port);
        if (StringUtils.isNotBlank(dbPort)) replace.put("port", dbPort);

        replace.put("code", db);
        if (StringUtils.isNotBlank(software_id)) {
            replace.put("owner_id", software_id);
            replace.put("software_id", software_id);
        }
        return simpleBuildSql(dirId, sql, "t_md_rdb_catalog", replace);
    }

    private String buildLabelElementSQL(String dirId) {
        String sql = "SELECT A  " +
                " .*   " +
                "FROM  " +
                " t_md_label_element A,  " +
                " ( SELECT DISTINCT deployed_software_id FROM t_md_dw_db_instance WHERE dir_id = :dir_id ) b   " +
                "WHERE  " +
                " A.subject_id = b.deployed_software_id   " +
                " AND A.code = 'datasource_source_first_classify'";
        return simpleBuildSql(dirId, sql, "t_md_label_element", null);
    }

    private String buildClusterSQL(String dirId, String dbPort) {
        String sql = "SELECT A  " +
                " .*   " +
                "FROM  " +
                " t_md_rdbcatalog_cluster A,  " +
                " t_md_rdb_catalog b,  " +
                " ( SELECT DISTINCT deployed_software_id FROM t_md_dw_db_instance WHERE dir_id = :dir_id ) C   " +
                "WHERE  " +
                " b.ID = C.deployed_software_id   " +
                " AND A.owner_id = b.ID";
        Map<String, String> replace = new HashMap<>();
        replace.put("port", port);
        if (StringUtils.isNotBlank(dbPort)) replace.put("port", dbPort);
        replace.put("code", db);
        return simpleBuildSql(dirId, sql, "t_md_rdbcatalog_cluster", replace);
    }


    private String buildTransPluginSQL(String transId) {
        String sql = "select * from t_md_etl_trans_plugin\n" +
                " where id in (select transplugin_id from t_etl_trans where id in (select child_trans_id from T_ETL_TRANS_STEPDETAIL where trans_id =:transId))";
        return simpleBuildSql("transId", transId, sql, "t_md_etl_trans_plugin", null);
    }

    private List<Map<String, Object>> buildTransPlugins(String transId) {
        String sql = "select * from t_md_etl_trans_plugin\n" +
                " where id in (select transplugin_id from t_etl_trans where id in (select child_trans_id from T_ETL_TRANS_STEPDETAIL where trans_id =:transId))";
        Map<String, String> param = Maps.newHashMap();
        param.put("transId", transId);
        return baseDao.sqlQueryForList(sql, param);
    }

    private List<Map<String, Object>> buildTransCollisionPlugins(List<String> collisionPlugins) {
        String sql = "select * from t_md_etl_trans_plugin where code in (%s)";
        return baseDao.sqlQueryForList(String.format(sql, String.join(",", collisionPlugins)));
    }

    /*
    * public List<Map<String,Object>>  transPlugins;//插件元信息 t_md_etl_trans_plugin
        public List<Map<String,Object>>  transPluginPartitions;//插件分区 T_MD_ETL_TRANS_PLUGIN_PARTITION
        public List<Map<String,Object>>  transPluginExpRelations;//T_MD_ETL_PLUGIN_EXP
        public List<Map<String,Object>>  transPluginExpNodes;//T_MD_ETL_PLUGIN_EXP
        * */
    private List<Map<String, Object>> buildTransPluginPartitions(List<Map<String, Object>> transPlugins, String queryCode) {
        List<Map<String, Object>> transPluginPartitions = Lists.newArrayList();
        for (Map map : transPlugins) {
            Map<String, String> param = Maps.newHashMap();
            param.put("transplugin_id", (String) map.get("id"));
            String sql = "";
            if ("partition".equalsIgnoreCase(queryCode))
                sql = queryPluginPartitions();
            else if ("relation".equalsIgnoreCase(queryCode))
                sql = queryPluginRelations();
            else if ("expNode".equalsIgnoreCase(queryCode))
                sql = queryPluginExpNodes();
            else if ("transExp".equalsIgnoreCase(queryCode))
                sql = queryPluginTransExp();
            else if ("attribute".equalsIgnoreCase(queryCode))
                sql = queryPluginTransAttributes();
            else if ("expNodeRelation".equalsIgnoreCase(queryCode)) {
                sql = queryPluginExpNodeRelations();
            }
            List list = baseDao.sqlQueryForList(sql, param);
            if (CollectionUtils.isNotEmpty(list)) {
                Map<String, Object> transPartition = Maps.newHashMap();
                transPartition.put((String) map.get("id"), list);
                transPluginPartitions.add(transPartition);
            }
        }
        return transPluginPartitions;
    }

    private String queryPluginPartitions() {
        String sql = "select * from T_MD_ETL_TRANS_PLUGIN_PARTITION where transplugin_id =:transplugin_id";
        return sql;
    }

    private String queryPluginRelations() {
        return "select * from T_MD_ETL_PLUGIN_EXP where transplugin_id =:transplugin_id";
    }

    //查询插件属性之间的关系  比如  set下面的属性
    private String queryPluginExpNodeRelations() {
        return "select * from \n" +
                "\tT_MD_EXP_RELATION where parent_exp_node_id in ( (select exp_node_id from T_MD_ETL_PLUGIN_EXP \n" +
                "where  transplugin_id=:transplugin_id))";
    }

    //查询 插件的属性 比如 set  attribute
    private String queryPluginExpNodes() {
        return "select * from T_MD_EXP_NODE where exp_node_id in\n" +
                " (select exp_node_id from T_MD_ETL_PLUGIN_EXP where  transplugin_id=:transplugin_id)\n" +
                "UNION \n" +
                "select t1.* from T_MD_EXP_NODE t1 where t1.exp_node_id in (select child_exp_node_id from \n" +
                "\tT_MD_EXP_RELATION where parent_exp_node_id in ( (select exp_node_id from T_MD_ETL_PLUGIN_EXP \n" +
                "where  transplugin_id=:transplugin_id)))";
    }

    private String queryPluginTransExp() {
        return "select * from T_ETL_TRANS_EXP where exp_node_id in (select exp_node_id from T_MD_ETL_PLUGIN_EXP where  transplugin_id=:transplugin_id)";
    }

    private String queryPluginTransAttributes() {
        return "select * from t_md_etl_trans_attribute where owner_id=:transplugin_id";
    }

    private String buildSchemaSQL(String dirId) {
        String sql = "SELECT A  " +
                " .*   " +
                "FROM  " +
                " t_md_rdb_schema A,  " +
                " ( SELECT DISTINCT A.deployed_software FROM t_dw_db_mapping A, t_md_dw_db_instance b WHERE A.dw_db_instance_id = b.ID AND b.dir_id = :dir_id ) b   " +
                "WHERE  " +
                " A.ID = b.deployed_software";
        Map<String, String> replace = new HashMap<>();
        replace.put("user_name", encrypt_user);
        replace.put("password", password);
        replace.put("code", encrypt_user);
        return simpleBuildSql(dirId, sql, "t_md_rdb_schema", replace);
    }

    private String buildDirSQL(String dirId) {
        String sql = "SELECT  " +
                " *   " +
                "FROM  " +
                " t_md_busi_dir   " +
                "WHERE  " +
                " ID = :dir_id";
       String  sql1 =  simpleBuildSql(dirId, sql, "t_md_busi_dir", null);
        return  sql1.replaceAll("VALUES \\(","select ").replaceAll("\\);"," where not exists (select * from t_md_element where id = 'cf681b2849a44c3da632133ef85b2c5f');");
    }

    private String buildDirSQLByClassifyId(String classifyId) {
        String sql = "select * from t_md_busi_dir where id in (select busi_dir_id from t_md_busi_classify where id =:classifyId)";
        return simpleBuildSql("classifyId", classifyId, sql, "t_md_busi_dir", null);
    }

    private String buildClassifySqlByClassifyId(String classifyId) {
        String sql = "select \n" +
                "\t* \n" +
                "from \n" +
                "t_md_busi_classify \n" +
                "where id =:classifyId";
        return simpleBuildSql("classifyId", classifyId, sql, "t_md_busi_classify", null);
    }

    private String buildSonClassifySqlByClassifyId(String classifyId) {
        String sql = "WITH RECURSIVE r AS ( \n" +
                "\tSELECT * FROM t_md_busi_classify WHERE id =:classifyId\n" +
                "\t\t\tUNION ALL SELECT l.* FROM t_md_busi_classify l, r WHERE l.parent_classify_id = r.id ) \n" +
                "SELECT * FROM r ORDER BY id ";
        return simpleBuildSql("classifyId", classifyId, sql, "t_md_busi_classify", null);
    }

    private String getDirIdByClassifyId(String classifyId) {
        String sql = "select busi_dir_id from t_md_busi_classify where id =:classifyId";
        Map<String, String> param = Maps.newHashMap();
        param.put("classifyId", classifyId);
        String dirId = baseDao.sqlQueryForValue(sql, param);
        return dirId;
    }

    private List<Map<String, Object>> buildClassifySQL(String dirId, boolean isSTD) {
        String sql = "SELECT  " +
                " *   " +
                "FROM  " +
                " t_md_busi_classify   " +
                "WHERE  " +
                " busi_dir_id = :dir_id   " +
                " AND parent_classify_id IS NULL   ";
        if (!isSTD) sql += " AND NAME != '集市层'";
        Map<String, String> param = Maps.newHashMap();
        param.put("dir_id", dirId);
        List<Map<String, Object>> list = baseDao.sqlQueryForList(sql, param);
        //return simpleBuildSql(dirId, sql, "t_md_busi_classify", null);
        return list;
    }

    private List<Map<String, Object>> buildClassifySQLAll(List<String> classifyIds, boolean isSTD) {
        String sql = "select * from t_md_busi_classify where parent_classify_id in (%s) and parent_classify_id is not null";
        if (!isSTD) sql += " AND NAME != '集市层'";
        sql += " ORDER BY operate_time ";
        List<String> params = Lists.newArrayList();
        classifyIds.stream().forEach(s -> {
            String p = "'" + s + "'";
            params.add(p);
        });
        List<Map<String, Object>> list = baseDao.sqlQueryForList(String.format(sql, String.join(",", params)));
        //return simpleBuildSql(dirId, sql, "t_md_busi_classify", null);
        return list;
    }

    private String buildClassifyTreeSql(String dirId, boolean isSTD) {
        List<Map<String, Object>> classifyParents = buildClassifySQL(dirId, isSTD);
        List<String> parentIds = classifyParents.stream().map(s -> (String) s.get("id")).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(parentIds)) buildClassifyTree(parentIds, classifyParents);
        return standardSimpleBuildSqlByData(classifyParents, "t_md_busi_classify", null);

    }

    private List<Map<String, Object>> buildClassifyTree(List<String> parentIds, List<Map<String, Object>> classifyParents) {
        List<Map<String, Object>> classifySons = buildClassifySQLAll(parentIds, true);
        if (CollectionUtils.isNotEmpty(classifySons)) {
            classifyParents.addAll(classifySons);
            parentIds = classifySons.stream().map(s -> (String) s.get("id")).collect(Collectors.toList());
            buildClassifyTree(parentIds, classifyParents);
        }
        return classifySons;
    }


    private String buildSonClassifySQL(String dirId, boolean isSTD) {
        String sql = "SELECT A  " +
                " .*   " +
                "FROM  " +
                " t_md_busi_classify A,  " +
                " t_md_busi_classify b   " +
                "WHERE  " +
                " A.parent_classify_id = b.ID   " +
                " AND b.busi_dir_id = :dir_id   " +
                " AND b.parent_classify_id IS NULL   ";
        if (!isSTD) sql += " AND b.NAME != '集市层'";
        return simpleBuildSql(dirId, sql, "t_md_busi_classify", null);
    }

    private String buildDbInstanceSQL(String dirId, boolean isSTD, String db_instance_type) {
        String sql = "select * from t_md_dw_db_instance\n" +
                " where id in (select element_id from t_md_classify_element\n" +
                " where busi_classify_id in (select id  from t_md_busi_classify\n" +
                " where busi_dir_id =:dir_id) )";
        /* if (!isSTD) sql += " AND b.NAME != '集市层'"; */
        Map<String, String> replace = new HashMap<>();
        if (StringUtils.isNotBlank(db_instance_type)) {
            replace.put("db_instance_type", db_instance_type);
        }
        return simpleBuildSql(dirId, sql, "t_md_dw_db_instance", replace);
    }

    private String buildClassifyElementSQL(String dirId, boolean isSTD) {
        String sql = "select * from t_md_classify_element where busi_classify_id in" +
                " (select id  from t_md_busi_classify where busi_dir_id = :dir_id ) ";
        if (!isSTD) sql += " AND b.NAME != '集市层'";
        return simpleBuildSql(dirId, sql, "t_md_classify_element", null);
    }

    private String buildCustomClassifyElementSQL(String classifyId) {
        String sql = "WITH RECURSIVE r AS ( \n" +
                "\tSELECT * FROM t_md_busi_classify WHERE id =:classifyId\n" +
                "\t\t\tUNION ALL SELECT l.* FROM t_md_busi_classify l, r WHERE l.parent_classify_id = r.id ) \n" +
                "SELECT e.* FROM r s LEFT JOIN t_md_classify_element e on s.id = e.busi_classify_id";
        return simpleBuildSql(classifyId, sql, "t_md_classify_element", null);
    }

    private String buildDbMappingSQL(String dirId, boolean isSTD) {
        String sql = "select * from t_dw_db_mapping\n" +
                " where dw_db_instance_id in (select element_id from t_md_classify_element\n" +
                " where busi_classify_id in (select id  from t_md_busi_classify\n" +
                " where busi_dir_id =:dir_id) )  ";
/*
        if (!isSTD) sql += " AND b.NAME != '集市层'";
*/
        return simpleBuildSql(dirId, sql, "t_dw_db_mapping", null);
    }

    private String buildDataObjSQL(String dirId, boolean isSTD, String dbType) {
        String sql = "select * from t_md_rdb_dataobj \n" +
                "where id in (\n" +
                "select classifier_stat_id from t_dw_table_mapping where dw_db_id in (\n" +
                "select id from t_md_dw_db_instance\n" +
                " where id in (select element_id from t_md_classify_element\n" +
                " where busi_classify_id in (select id  from t_md_busi_classify\n" +
                " where busi_dir_id =:dir_id) )))";
/*
        if (!isSTD) sql += " AND b.NAME != '集市层'";
*/
        return dataObjSQLReplace(dirId, sql, "t_md_rdb_dataobj", dbType);
    }

    private String buildDataColumnSQL(String dirId, boolean isSTD) {
        String sql = "select cl.* from t_md_rdb_datacolumn cl left join  (\n" +
                "select id from t_md_rdb_dataobj \n" +
                "\twhere id in (\n" +
                "\tselect classifier_stat_id from t_dw_table_mapping where dw_db_id in (\n" +
                "\t\tselect id from t_md_dw_db_instance\n" +
                "\t\twhere id in (select element_id from t_md_classify_element\n" +
                "\twhere busi_classify_id in (select id  from t_md_busi_classify\n" +
                " where busi_dir_id =:dir_id) )))) tb  ON cl.db_obj_id = tb.id";
/*
        if (!isSTD) sql += " AND b.NAME != '集市层'";
*/
        Map<String, String> replace = new HashMap<>();
        replace.put("default_value", null);
        return simpleBuildSql(dirId, sql, "t_md_rdb_datacolumn", replace);
    }

    private String buildSysFuncSQL(String dirId, boolean isSTD) {
        String sql = "select h.* from \n" +
                "\t(select * from t_md_rdb_dataobj \n" +
                "\t\twhere id in (select classifier_stat_id from t_dw_table_mapping where dw_db_id in (\n" +
                "\t\tselect id from t_md_dw_db_instance\twhere id in (select element_id from t_md_classify_element\n" +
                "\t\t\twhere busi_classify_id in (select id  from t_md_busi_classify\n" +
                "\t\t\t\twhere busi_dir_id =:dir_id) )))) f,  \n" +
                "                 t_sys_auth_obj_func g, \n" +
                "                 t_sys_func h  \n" +
                "where   h.func_code = g.func_code  AND g.func_code = f.id and g.obj_id = '40289754739d4e7e11729d4e682b2020'";
/*
        if (!isSTD) sql += " AND b.NAME != '集市层'";
*/
        return simpleBuildSql(dirId, sql, "t_sys_func", null);
    }

    private String buildSysAuthSQL(String dirId, boolean isSTD) {
        String sql = "select g.* from \n" +
                "\t(select * from t_md_rdb_dataobj \n" +
                "\t\twhere id in (select classifier_stat_id from t_dw_table_mapping where dw_db_id in (\n" +
                "\t\tselect id from t_md_dw_db_instance\twhere id in (select element_id from t_md_classify_element\n" +
                "\t\t\twhere busi_classify_id in (select id  from t_md_busi_classify\n" +
                "\t\t\t\twhere busi_dir_id =:dir_id) )))) f,  \n" +
                "                 t_sys_auth_obj_func g\n" +
                "where    g.func_code = f.id and g.obj_id = '40289754739d4e7e11729d4e682b2020' ";
/*
        if (!isSTD) sql += " AND b.NAME != '集市层'";
*/
        return simpleBuildSql(dirId, sql, "t_sys_auth_obj_func", null);
    }

    private String buildDwTableSQL(String dirId, boolean isSTD) {
        String sql = "\n" +
                "select * from t_dw_table_mapping where dw_db_id in (\n" +
                "select id from t_md_dw_db_instance\n" +
                " where id in (select element_id from t_md_classify_element\n" +
                " where busi_classify_id in (select id  from t_md_busi_classify\n" +
                " where busi_dir_id =:dir_id) ))\n  ";
/*
        if (!isSTD) sql += " AND b.NAME != '集市层'";
*/
        return dwTableSQLReplace(dirId, sql, "t_dw_table_mapping");
    }

    private String getSoftwareId(String dbType) {
        String sql = "select id from t_md_software where code =:dbType\n";
        Map<String, String> param = Maps.newHashMap();
        param.put("dbType", dbType);
        return baseDao.sqlQueryForValue(sql, param);
    }


    @Deprecated
    private String buildUniqueKeySQL(String dirId) {
        String sql = "select * from t_md_rdb_unique_key where owner_id in (select id from t_md_rdb_dataobj " +
                "where id in (select classifier_stat_id from t_dw_table_mapping where  dw_db_id in " +
                "(select id from t_md_dw_db_instance where dir_id = :dir_id)))";
        return simpleBuildSql(dirId, sql, "t_md_rdb_unique_key", null);
    }

    @Deprecated
    private String buildUniqueKeyColumnSQL(String dirId) {
        String sql = "select * from t_md_rdb_uk_datacolumn where uk_id in" +
                " (select id from t_md_rdb_unique_key where owner_id in " +
                "(select id from t_md_rdb_dataobj where id in " +
                "(select classifier_stat_id from t_dw_table_mapping " +
                "where  dw_db_id in (select id from t_md_dw_db_instance where dir_id = :dir_id))))";
        return simpleBuildSql(dirId, sql, "t_md_rdb_uk_datacolumn", null);
    }

    private String buildMetaLogicObjRelationSQL(String dirId, boolean isSTD) {
        List<Map<String, Object>> list = getMetaLogicObjRelationInfo(dirId, isSTD);
        StringBuilder resBuilder = new StringBuilder();
        for (Map<String, Object> map : list) {
            resBuilder.append(insertSQL("t_md_logic_data_relation", map));
        }
        return resBuilder.toString();
    }

    private List<Map<String, Object>> getMetaLogicObjRelationInfo(String dirId, boolean isSTD) {
        StringBuffer sb = new StringBuffer();
        sb.append("select " +
                " a.* " +
                "from " +
                " t_md_logic_data_relation a " +
                "left join  " +
                " t_md_logic_dataobj b " +
                "on a.logic_data_obj_id = b.id " +
                "where " +
                " logic_data_obj_id in( " +
                "  select " +
                "   logic_data_obj_id " +
                "  from " +
                "   t_md_logic_data_relation  " +
                "  where " +
                "   element_id in( ");
        sb.append(getInstanceIdSQL(isSTD));
        sb.append("   ) " +
                " ) " +
                "and " +
                " b.operate_user_id = '40289754739d4e7e11729d4e682b2020'");
        Map<String, String> param = Maps.newHashMap();
        param.put("dir_id", dirId);
        return baseDao.sqlQueryForList(sb.toString(), param);
    }

    private List<Map<String, String>> getMetaLogicObjInfo(String dirId, boolean isSTD) {
        String sb = "select " +
                " b.id " +
                "from " +
                " t_md_logic_data_relation a  " +
                "left join  " +
                " t_md_logic_dataobj b " +
                "on a.logic_data_obj_id = b.id  " +
                "where " +
                " a.element_id in( " + getInstanceIdSQL(isSTD) + " ) " +
                " and relation_type = '0' " +
                " and b.operate_user_id = '40289754739d4e7e11729d4e682b2020'";
        Map<String, String> param = Maps.newHashMap();
        param.put("dir_id", dirId);
        return baseDao.sqlQueryForList(sb, param);
    }

    private String getInstanceIdSQL(boolean isSTD) {
        if (isSTD) {
            return "select " +
                    "   id " +
                    "  from " +
                    "   t_md_dw_db_instance " +
                    "  where " +
                    "   dir_id =:dir_id";
        }
        return "select element_id from t_md_classify_element where busi_classify_id in " +
                "(select id from t_md_busi_classify where owner_id in " +
                "(select id from t_md_busi_classify where busi_dir_id = :dir_id and owner_id is null and name != '集市层'))";
    }

    private String dataObjSQLReplace(String dirId, String sql, String tableName, String dbType) {
        Map<String, String> param = Maps.newHashMap();
        param.put("dir_id", dirId);
        List<Map<String, Object>> list = baseDao.sqlQueryForList(sql, param);
        StringBuilder builder = new StringBuilder();
        for (Map<String, Object> map : list) {
            String columnCode = map.get("code").toString();
            String[] arr = columnCode.split("\\.");
            columnCode = schema + "." + (arr.length > 1 ? arr[1] : columnCode);
            map.put("code", columnCode);
            map.put("db_type", type);
            if (StringUtils.isNotBlank(dbType)) map.put("db_type", dbType);

            builder.append(insertSQL(tableName, map))
                    .append(" ");
        }
        return builder.toString();
    }

    private String dwTableSQLReplace(String dirId, String sql, String tableName) {
        Map<String, String> param = Maps.newHashMap();
        param.put("dir_id", dirId);
        List<Map<String, Object>> list = baseDao.sqlQueryForList(sql, param);
        StringBuilder builder = new StringBuilder();
        for (Map<String, Object> map : list) {
            String columnCode = map.get("code").toString();
            String[] arr = columnCode.split("\\.");
            columnCode = schema + "." + (arr.length > 1 ? arr[1] : columnCode);
            map.put("code", columnCode);
            map.put("user_name", orginal_user);
            builder.append(insertSQL(tableName, map))
                    .append(" ");
        }
        return builder.toString();
    }

    private String simpleBuildSql(String dirId, String sql, String tableName, Map<String, String> replaceMap) {
        Map<String, String> param = Maps.newHashMap();
        param.put("dir_id", dirId);
        return standardSimpleBuildSql(param, sql, tableName, replaceMap);
    }

    private String simpleBuildSql(String paramCode, String paramValue, String sql, String tableName, Map<String, String> replaceMap) {
        Map<String, String> param = Maps.newHashMap();
        param.put(paramCode, paramValue);
        return standardSimpleBuildSql(param, sql, tableName, replaceMap);
    }

    private String standardSimpleBuildSql(String dirId, String sql, String tableName, Map<String, String> replaceMap) {
        Map<String, String> param = Maps.newHashMap();
        param.put("dir_id", dirId);
        List<Map<String, Object>> list = baseDao.sqlQueryForList(sql, param);
        List<Map<String, Object>> list1 = filterMap(list, "id");
        return standardSimpleBuildSqlByData(list1, tableName, replaceMap);
    }

    private String standardSimpleBuildSql(Map<String, String> param, String sql, String tableName, Map<String, String> replaceMap) {
        List<Map<String, Object>> list = baseDao.sqlQueryForList(sql, param);
        return standardSimpleBuildSqlByData(list, tableName, replaceMap);
    }

    private String standardSimpleBuildSqlByData(List<Map<String, Object>> list, String tableName, Map<String, String> replaceMap) {
        StringBuilder builder = new StringBuilder();
        for (Map<String, Object> map : list) {
            if (map.size() > 0) {
                if (replaceMap != null) {
                    replaceMap.forEach((k, v) -> {
                        if (map.containsKey(k)) map.put(k, v);
                    });
                }
                builder.append(insertSQL(tableName, map))
                        .append(" ");
            }
        }
        return builder.toString();
    }


    public String insertSQL(String tableName, Map<String, Object> param) {
        List<String> list = new ArrayList<>();
        for (String s : param.keySet()) {
            Object val = param.get(s);
            String value = val != null ? "'" + val.toString() + "'" : "NULL";
            if (s.equals("relation_type")) value = value.trim();
            list.add(value);
        }
        String sql = "INSERT INTO %s (%s) VALUES (%s);";
        return String.format(sql, tableName, String.join(",", param.keySet()), String.join(",", list));
    }

    private List<LogicDataObjInfo> getLogicDataObjInfoBelongTrans(TransMeta transMeta) {
        List<String> logicIdByTransMeta = transTemplateService.getLogicIdByTransMeta(transMeta);

        return null;
    }


    @Data
    public static class ExportBean implements Serializable {
        public List<TransMeta> transMetas;
        public List<LogicDataObjInfo> logics;
        public List<Dashboard> dashboards;
        public List<UdfGraph> udfGraphs;
        public TransScheduleInfo scheduleInfo;
        public List<Map<String, Object>> transPlugins;//插件元信息 t_md_etl_trans_plugin
        public List<Map<String, Object>> transPluginPartitions;//插件分区 T_MD_ETL_TRANS_PLUGIN_PARTITION
        public List<Map<String, Object>> transPluginExpRelations;//T_MD_ETL_PLUGIN_EXP 插件与exp关系表
        public List<Map<String, Object>> transPluginExpNodes;//T_MD_EXP_NODE  exp表
        public List<Map<String, Object>> transPluginExpNodeRelation;//T_MD_EXP_RELATION    exp_node之间的关系表
        public List<Map<String, Object>> transPluginTransAttributes;//t_md_etl_trans_attribute
        public List<PortalInfo> portalInfos;


    }

    @Data
    public static class PortalInfo implements Serializable {
        public Portal portal;
        public PortalConfig portalConfig;
        public PublishUrl publishUrl;
        public List<MenuConfig> menuConfigs;
    }

    @Data
    public static class TransScheduleInfo implements Serializable {
        public List<String> transTasks;
        public List<String> transSchedules;
        public List<String> transSubTransRelations;
    }

    @Data
    @AllArgsConstructor
    static class LogicObjBean implements Serializable {
        List<LogicDataObjInfo> logicDataObjInfos;
        String relationSQL;
        // String classifySQL;
    }

    @RequestMapping("/dataset")
    public Result dataset(HttpServletResponse response, String logicIds) throws IOException {
        LogicDataObjInfo logic = logicDataObjService.getLogicDataObjInfo(logicIds);
        try {
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(logicIds + ".model", "UTF-8"));
            StreamUtils.copy(SerializableMsgCodec.encode(logic), response.getOutputStream());
            return Result.success();
        } finally {
            response.getOutputStream().close();
        }
    }

    @RequestMapping("/resolve")
    public String resolveUserAndPassword(String user, String password) {
        DragonEncryptor encryptor = new DragonEncryptor();
        String enUser = encryptor.encrypt(user);
        String enPassword = encryptor.encrypt(password);
        return String.format("user = [%s], password = [%s]", enUser, enPassword);
    }

    @RequestMapping("/createDataObjView")
    public Result createDataObjView() {
        transTemplateService.createDataObjView();
        return Result.success();
    }

    @RequestMapping("/createModelView")
    public Result createModelView(String transId) throws Exception {
        transTemplateService.createTemplateView(transId);
        return Result.success();
    }


    @RequestMapping("/queryTransMeta")
    public Result queryTransMeta(String id) {
        TransMeta transMeta = transMetaService.getTransMetaById(id);
        String sql = transTemplateService.buildTransMetaSQL(transMeta);
        return Result.success(sql);
    }

    @RequestMapping("/test")
    public Result testSQLCopy(String tid) {
        TransMeta transMeta = transMetaService.getTransMetaById(tid);
        BaseBusiClassify busiClassifyBy = busiClassifyService.findBusiClassifyBy("17f2a37ee75144069d8c7385566080cc");
        transTemplateService.saveTransModelBySQL(transMeta, null, busiClassifyBy);
        return Result.success();
    }

    private String buildMetaLogicColumnObjSQL(String dirId, boolean isSTD) {
        List<Map<String, Object>> list = getMetaLogicColumnInfo(dirId, isSTD);
        StringBuilder resBuilder = new StringBuilder();
        for (Map<String, Object> map : list) {
            resBuilder.append(insertSQL("t_md_logic_data_column", map));
        }
        return resBuilder.toString();
    }

    private List<Map<String, Object>> getMetaLogicColumnInfo(String dirId, boolean isSTD) {
        StringBuffer sb = new StringBuffer();
        sb.append("select * from t_md_logic_data_column where owner_id in (select  " +
                " id " +
                "from  " +
                " t_md_logic_data_relation a   " +
                "left join   " +
                " t_md_logic_dataobj b  " +
                "on a.logic_data_obj_id = b.id   " +
                "where  " +
                " a.element_id in(  ");
        sb.append(getInstanceIdSQL(isSTD));
        sb.append(" )  " +
                " and relation_type = '0'  " +
                " and b.operate_user_id = '40289754739d4e7e11729d4e682b2020') ");

        Map<String, String> param = Maps.newHashMap();
        param.put("dir_id", dirId);
        return baseDao.sqlQueryForList(sb.toString(), param);
    }

    @RequestMapping("/createViewTest")
    public Result test(String path) throws IOException {
        File file = new File(path.trim());

        byte[] bytes = FileUtils.readFileToByteArray(file);

        LogicDataObjInfo logicData = (LogicDataObjInfo) SerializableMsgCodec.decode(bytes);
        //createViewsByModelImport(ImmutableList.of(logicData), "public");
        replaceLogicDataObjSearchsql();
        return Result.success();
    }

    private String buildTransPluginSQL(List<Map<String, Object>> transPlugin) {
        //过滤掉已经存在的插件
        for (int i = 0; i < transPlugin.size(); i++) {
            Map plugin = transPlugin.get(i);
            if (checkPluginExist((String) plugin.get("name"), (String) plugin.get("code"))) {
                transPlugin.remove(i);
                i--;
            }
        }
        return standardSimpleBuildSqlByData(transPlugin, "t_md_etl_trans_plugin", null);
    }


    private boolean checkPluginExist(String name, String code) {
        String sql = "select * from t_md_etl_trans_plugin where  code =:code";
        Map<String, String> param = Maps.newHashMap();
        param.put("code", code);
        List list = baseDao.sqlQueryForList(sql, param);
        if (CollectionUtils.isNotEmpty(list)) return true;
        return false;
    }

    private List<Map<String, Object>> filterMap(List<Map<String, Object>> repeatList, String id) {
        Map<String, Map> msp = new HashMap<>();
        List<Map<String, Object>> listMap = Lists.newArrayList();
        for (Map map : repeatList) {
            String key = (String) map.get(id);
            msp.put(key, map);
        }
        Set<String> mspKey = msp.keySet();
        for (String key : mspKey) {
            if (key == null)
                continue;
            Map newMap = msp.get(key);
            listMap.add(newMap);
        }
        return listMap;
    }

    private String collectParams(List<Map<String, Object>> arrayList, String code) {
        String[] params = new String[arrayList.size()];
        for (int i = 0; i < arrayList.size(); i++) {
            Map<String, Object> map = arrayList.get(i);
            String param = (String) map.get(code);
            if (StringUtils.isNotBlank(param)) {
                params[i] = "'" + param + "'";
            }
        }

        // 3. 通过源数据集id，找到所有源数据集与数仓目录的挂接关系
        String condition = String.join(",", params);
        return condition;
    }

    private String buildTransPluginExpSQL(ExportBean bean) {
        StringBuilder transPluginPartitionSQL = new StringBuilder();
        StringBuilder transPluginRelationSQL = new StringBuilder();
        StringBuilder transPluginExpNodeSQL = new StringBuilder();
        //StringBuilder transPluginExpSQL = new StringBuilder();
        StringBuilder transPluginAttributeSQL = new StringBuilder();
        StringBuilder transPluginExpNodeRelationSQL = new StringBuilder();
        List<Object> pluginIds = bean.getTransPlugins().stream().map(s -> s.get("id")).collect(Collectors.toList());
        for (Object pluginId : pluginIds) {
            List<Map<String, Object>> partitions = Lists.newArrayList();
            addPluginMetaData(bean.getTransPluginPartitions(), pluginId, partitions);
            transPluginPartitionSQL.append(standardSimpleBuildSqlByData(partitions, "T_MD_ETL_TRANS_PLUGIN_PARTITION", null));

            List<Map<String, Object>> expRelations = Lists.newArrayList();
            addPluginMetaData(bean.getTransPluginExpRelations(), pluginId, expRelations);
            transPluginRelationSQL.append(standardSimpleBuildSqlByData(expRelations, "T_MD_ETL_PLUGIN_EXP", null));

            List<Map<String, Object>> expNodes = Lists.newArrayList();
            addPluginMetaData(bean.getTransPluginExpNodes(), pluginId, expNodes);
            transPluginExpNodeSQL.append(standardSimpleBuildSqlByData(expNodes, "T_MD_EXP_NODE", null));

            List<Map<String, Object>> expNodeRelations = Lists.newArrayList();
            addPluginMetaData(bean.getTransPluginExpNodeRelation(), pluginId, expNodeRelations);
            transPluginExpNodeRelationSQL.append(standardSimpleBuildSqlByData(expNodeRelations, "T_MD_EXP_RELATION", null));


          /*  List<Map<String, Object>> transExps = Lists.newArrayList();
            addPluginMetaData(bean.getTransPluginTransExp(), pluginId, transExps);
            transPluginExpSQL.append(standardSimpleBuildSqlByData(transExps, "T_ETL_TRANS_EXP", null));*/

            List<Map<String, Object>> transAttributes = Lists.newArrayList();
            addPluginMetaData(bean.getTransPluginTransAttributes(), pluginId, transAttributes);
            transPluginAttributeSQL.append(standardSimpleBuildSqlByData(transAttributes, "t_md_etl_trans_attribute", null));
        }
        return transPluginPartitionSQL.append(transPluginExpNodeSQL.toString()).append(transPluginRelationSQL.toString()).append(transPluginExpNodeRelationSQL.toString()).append(transPluginAttributeSQL.toString()).toString();
    }

    private void addPluginMetaData(List<Map<String, Object>> dataObj, Object pluginId, List<Map<String, Object>> resultData) {
        if (CollectionUtils.isNotEmpty(dataObj)) {
            dataObj.forEach(map -> {
                List<Map<String, Object>> o = (List<Map<String, Object>>) map.get(pluginId);
                if (null != o) resultData.addAll(o);
            });
        }
    }

}
