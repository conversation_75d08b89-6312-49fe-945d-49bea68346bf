package com.dragonsoft.cicada.datacenter.modules.modeling.service.impl;

import com.code.common.mist.plugin.config.service.impl.PluginConfigService;
import com.code.common.utils.StringUtils;
import com.code.common.utils.assertion.Assert;
import com.code.dataset.AbstractAtomDataSetOperator;
import com.code.dataset.IStepRelationService;
import com.code.dragonsoft.dataquery.service.DDLOperationService;
import com.code.metadata.base.expression.ElementNode;
import com.code.metadata.base.expression.FeatureNode;
import com.code.metadata.business.directory.BaseBusiClassify;
import com.code.metadata.datavisual.DashboardGroup;
import com.code.metadata.etl.trans.*;
import com.code.metadata.res.common.ClassifierStat;
import com.code.metadata.res.ddl.LogicDataObj;
import com.code.metadata.udf.management.UdfEdge;
import com.code.metadata.udf.management.UdfGraph;
import com.code.metadata.udf.management.UdfNode;
import com.code.metadata.variable.TransVariable;
import com.code.metadata.variable.TransVariableRelation;
import com.code.metaservice.ddl.ILogicDataColumnService;
import com.code.metaservice.ddl.ILogicDataObjService;
import com.code.metaservice.ddl.vo.LogicDataObjInfo;
import com.code.metaservice.etl.trans.TransMetaService;
import com.code.metaservice.res.common.ClassifierStatService;
import com.code.thirdplugin.sql.meta.input.StandardSqlInput;
import com.code.thirdplugin.sql.meta.output.StandardSqlOutput;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.service.IDataSetEditService;
import com.dragonsoft.cicada.datacenter.modules.modeling.importandexport.util.DataExportUtil;
import com.dragonsoft.cicada.datacenter.modules.modeling.service.TransTemplateService;
import com.fw.service.BaseService;
import com.fw.service.annotation.Service;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.schema.Table;
import net.sf.jsqlparser.statement.select.FromItemVisitorAdapter;
import net.sf.jsqlparser.statement.select.Join;
import net.sf.jsqlparser.statement.select.PlainSelect;
import net.sf.jsqlparser.statement.select.Select;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Statement;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.dragonsoft.cicada.datacenter.modules.modeling.importandexport.util.DataExportUtil.checkPluginRelation;

/**
 * <AUTHOR> Wu.D.J
 * @Create : 2020.05.12
 */
@Service
@Slf4j
public class TransTemplateServiceImpl extends BaseService implements TransTemplateService {

    private static final List<String> JOIN = Lists.newArrayList("innerJoinPlugin", "collisionPlugin",
            "leftOrRightJoinPlugin", "unionJoinPlugin", "subtractByKeyPlugin", "fullJoinPlugin", "cicadaInnerJoinPlugin",
            "cicadaCollisionPlugin", "cicadaLeftOrRightJoinPlugin", "cicadaUnionJoinPlugin", "cicadaSubtractByKeyPlugin", "cicadaFullJoinPlugin");
    private static final List<String> SERVICE = Lists.newArrayList("cicadaAnalysisModelService","cicadaCompareServiceModelService","cicadaDataCollisionModelService","cicadaInfoCheckModelService");

    private static final List<String> CHECK_SERVICE = Lists.newArrayList("cicadaMetaServiceCheckOutPut");
    @Autowired
    private TransMetaService transMetaService;

    @Autowired
    private PluginConfigService pluginConfigService;

    @Autowired
    private ClassifierStatService classifierStatService;

    @Autowired
    private ILogicDataObjService logicDataObjService;

    @Autowired
    private IDataSetEditService dataSetEditService;

    @Resource
    private DataSource dataSource;

    @Autowired
    IStepRelationService stepRelationService;

    @Autowired
    ILogicDataColumnService logicDataColumnService;

    @Autowired
    DDLOperationService ddlOperationService;

    @Override
    public void saveSQL(String sql) {
        this.baseDao.executeSqlUpdate(sql);
    }

    @Override
    public void sqlRun(String sql) {
        Connection connection = null;
        Statement statement = null;
        try {
            connection = dataSource.getConnection();
            connection.setAutoCommit(false);
            statement = connection.createStatement();
            statement.execute(sql);
            connection.commit();
        } catch (Throwable e) {
         log.error(e.getMessage(),e);
            if (connection != null) {
                try {
                    connection.rollback();
                } catch (SQLException throwables) {
                    throwables.printStackTrace();
                }
            }
        } finally {
            try {
                statement.close();
                connection.close();
            } catch (SQLException throwables) {
                throwables.printStackTrace();
            }
        }
    }

    @Override
    public BaseBusiClassify queryClassify(String code) {
        String hql = "From BaseBusiClassify where code = '" + code + "' ";
        return (BaseBusiClassify) baseDao.queryForObject(hql);
    }

    @Override
    public BaseBusiClassify queryModelClassify(String code, String pId) {
        String hql = "From BaseBusiClassify where code = '" + code + "' and parent_classify_id = '" + pId + "'";
        return (BaseBusiClassify) baseDao.queryForObject(hql);
    }

    @Override
    public DashboardGroup queryGroup(String code) {
        String hql = "From DashboardGroup where code = '" + code + "' and name = '标准模型'";
        return (DashboardGroup) baseDao.queryForObject(hql);
    }

    @Override
    public String queryExistDashboardGroup(String pId, String name) {
        String sql = "select id from t_v_dashboard_groups where parent_id = :pid and name = :name";
        return this.baseDao.sqlQueryForValue(sql, addParam("pid", pId).addParam("name", name).param());
    }

    @Override
    public String saveTransModelBySQL(TransMeta oldTransMeta, Map<String, UdfGraph> graphs, BaseBusiClassify classify) {
        Map<String, TransMeta> transMapping = new HashMap<>();
        // 将TransMeta进行克隆，并重置所有对象的id，保证模型多次写入时，每次id都不一样
        // 上面的transMapping，用来存储transMeta和所克隆出来的transMeta的映射关系，key为transMeta的id， value为transMeta锁克隆出来的对象
        TransMeta newTransMeta = refreshMeta(oldTransMeta, true, transMapping);
        saveTransModeCommon(oldTransMeta,graphs,classify,newTransMeta);
//        this.baseDao.executeSqlUpdate(transMetaSQL);
        return newTransMeta.getId();
    }

    @Override
    public String saveTransModelBySQL(TransMeta oldTransMeta, Map<String, UdfGraph> graphs, BaseBusiClassify classify,boolean idRefresh) {
        Map<String, TransMeta> transMapping = new HashMap<>();
        // 将TransMeta进行克隆，并重置所有对象的id，保证模型多次写入时，每次id都不一样
        // 上面的transMapping，用来存储transMeta和所克隆出来的transMeta的映射关系，key为transMeta的id， value为transMeta锁克隆出来的对象
        TransMeta newTransMeta = refreshMeta(oldTransMeta, idRefresh, transMapping);
        saveTransModeCommon(oldTransMeta,graphs,classify,newTransMeta);
//        this.baseDao.executeSqlUpdate(transMetaSQL);
        return newTransMeta.getId();
    }


    private void saveTransModeCommon(TransMeta oldTransMeta, Map<String, UdfGraph> graphs, BaseBusiClassify classify,  TransMeta newTransMeta ){
        String operateTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        newTransMeta.setOperateTime(operateTime);
        newTransMeta.setName(newTransMeta.getName());
        newTransMeta.setCode(newTransMeta.getCode() + "@_@" + System.currentTimeMillis());
        Map<String, String> idMapping = new HashMap<>();
        for (TransMeta old : oldTransMeta.getChildren()) {
            String n_id = newTransMeta.getChildren().stream()
                    .filter(t -> t.getCode().equals(old.getCode()))
                    .map(TransMeta::getId)
                    .collect(Collectors.toList())
                    .get(0);
            idMapping.put(old.getId(), n_id);
        }

        // 替换/修改新克隆出来的transMeta对象的一些插件的attribute value
        for (TransMeta meta : newTransMeta.getChildren()) {
            TransPluginMeta usedPlugin = meta.getUsedPlugin();
            if (JOIN.contains(usedPlugin.getCode())) {   // 处理碰撞插件
                TransAttributeMeta leftAttribute = usedPlugin.getFeature("leftStepId");
                String leftStepId = meta.getAttributeValue(leftAttribute);
                String new_left_id = idMapping.get(leftStepId);
                meta.addTransAttribute(leftAttribute, new_left_id);

                TransAttributeMeta rightAttribute = usedPlugin.getFeature("rightStepId");
                String rightStepId = meta.getAttributeValue(rightAttribute);
                String new_right_id = idMapping.get(rightStepId);
                meta.addTransAttribute(rightAttribute, new_right_id);

                Set<TransExpMeta> transExps = meta.getTransExps();
                for (TransExpMeta exp : transExps) {
                    ElementNode elementNode = (ElementNode) exp.getExpression().getArgument("objId");
                    if (elementNode == null) continue;
                    String expValue = exp.getExpValue(elementNode);
                    if (expValue == null) continue;
                    exp.addExpValue(elementNode, idMapping.get(expValue));
                }
            }
            //给后来者留下重构的机会
            if (CHECK_SERVICE.contains(usedPlugin.getCode())) {   // 处理信息核查插件
                TransAttributeMeta leftAttribute = usedPlugin.getFeature("serviceInputStepId");
                String leftStepId = meta.getAttributeValue(leftAttribute);
                String new_left_id = idMapping.get(leftStepId);
                meta.addTransAttribute(leftAttribute, new_left_id);

                //右侧数据源,需要解析json的属性，然后重新保存
                Iterator<TransExpMeta> iterator = meta.getTransExps().iterator();
                while (iterator.hasNext()){
                    TransExpMeta next = iterator.next();
                    Map<ElementNode, String> expValues = next.getExpValues();
                    for(Map.Entry entry : expValues.entrySet()){
                        if(idMapping.get(entry.getValue()) != null){
                            entry.setValue(idMapping.get(entry.getValue()));
                        }
                        String value = (String) entry.getValue();
                        if(value.contains("dataSourceStepName") && value.contains("dataSourcesStepId")){
                            for(Map.Entry idEntry : idMapping.entrySet()){
                                value =  value.replaceAll((String) idEntry.getKey(), (String) idEntry.getValue());
                            }
                            entry.setValue(value);
                        }
                    }
                }
                this.baseDao.saveOrUpdate(meta);
            }
            if (SERVICE.contains(usedPlugin.getCode())) {
                Set<TransExpMeta> transExps = meta.getTransExps();
                for (TransExpMeta exp : transExps) {
                    ElementNode targetStep = (ElementNode) exp.getExpression().getArgument("inputStepId");
                    String expValue = exp.getExpValue(targetStep);
                    exp.addExpValue(targetStep, idMapping.get(expValue));
                }
                this.baseDao.saveOrUpdate(meta);
            }
            if ("serviceOrganization".equals(usedPlugin.getCode())) {  // 处理算子编排
                Set<TransExpMeta> transExps = meta.getTransExps();
                for (TransExpMeta exp : transExps) {
                    ElementNode elementNode = (ElementNode) exp.getExpression().getArgument("serviceOrgId");
                    String expValue = exp.getExpValue(elementNode);
                    if (graphs != null) {
                        UdfGraph oldGraph = graphs.get(expValue);
                        if (oldGraph != null) {
                            // 算子图保存
                            UdfGraph graph = _clone(oldGraph);
                            this.baseDao.save(graph);
                            exp.addExpValue(elementNode, graph.getId());
                        }
                    }
                }
            }
            if ("samplingAndShuntingPlugin".equals(usedPlugin.getCode())) {  // 处理采样分流
                TransAttributeMeta attribute = usedPlugin.getFeature("defaultTargetStep");
                String attributeValue = meta.getAttributeValue(attribute);
                meta.addTransAttribute(attribute, idMapping.get(attributeValue));
                Set<TransExpMeta> transExps = meta.getTransExps();
                for (TransExpMeta exp : transExps) {
                    ElementNode targetStep = (ElementNode) exp.getExpression().getArgument("targetStep");
                    String expValue = exp.getExpValue(targetStep);
                    exp.addExpValue(targetStep, idMapping.get(expValue));
                }
            }
            if ("standardSqlInput".equals(usedPlugin.getCode()) || "cicadaStandardSqlInput".equals(usedPlugin.getCode())) {
                TransAttributeMeta tableNameAttr = usedPlugin.getFeature("tableName");
                TransAttributeMeta dbTypeAttr = usedPlugin.getFeature("dbType");
                TransAttributeMeta csIdAttr = usedPlugin.getFeature("classifierStatId");
                String csId = meta.getAttributeValue(csIdAttr);
                ClassifierStat classifierStat = classifierStatService.getClassifierStat(ClassifierStat.class, csId);
                if (classifierStat != null) {
//                    meta.addTransAttribute(tableNameAttr, classifierStat.getCode());
                    meta.addTransAttribute(dbTypeAttr, classifierStat.getDbType());
                } else {
                    System.out.println("ERROR MSG - [数据对象为空，检查数据对象是否已导入，id =  " + csId + "]");
                }
            }

            if ("standardSqlOutput".equals(usedPlugin.getCode()) || "cicadaStandardSqlOutput".equals(usedPlugin.getCode())) {
                TransAttributeMeta tableNameAttr = usedPlugin.getFeature("tableName");
                TransAttributeMeta dbTypeAttr = usedPlugin.getFeature("dbType");
                TransAttributeMeta csIdAttr = usedPlugin.getFeature("tableId");
                String csId = meta.getAttributeValue(csIdAttr);
                if(StringUtils.isNotBlank(csId)){
                    ClassifierStat classifierStat = classifierStatService.getClassifierStat(ClassifierStat.class, csId);
                    if (classifierStat != null) {
                        meta.addTransAttribute(tableNameAttr, classifierStat.getCode());
                        meta.addTransAttribute(dbTypeAttr, classifierStat.getDbType());
                    } else {
                        System.out.println("ERROR MSG - [数据对象为空，检查数据对象是否已导入，id =  " + csId + "]");
                    }
                }
            }
        }
        String elementSql = "";
        if(classify != null){
            elementSql  = "INSERT INTO t_md_classify_element (element_id, busi_classify_id) VALUES ('" + newTransMeta.getId() + "','" + classify.getId() + "');\n";
            //DataExportUtil.insertClassifyElementSQL("t_md_classify_element",classify.getId(),newTransMeta.getId());
        }

        // 将 TransMeta对象 转换成 SQL语句，避免保存对象时出现的各种疑难杂症
        String transMetaSQL = buildTransMetaSQL(newTransMeta);
        transMetaSQL += elementSql;

        // TODO 手动维护事务提交
        Connection connection = null;
        Statement statement = null;
        try {
            connection = dataSource.getConnection();
            connection.setAutoCommit(false);
            statement = connection.createStatement();
            statement.execute(transMetaSQL);
            connection.commit();
        } catch (Throwable e) {
         log.error(e.getMessage(),e);
            if (connection != null) {
                try {
                    connection.rollback();
                } catch (SQLException throwables) {
                    throwables.printStackTrace();
                }
            }
        } finally {
            try {
                statement.close();
                connection.close();
            } catch (SQLException throwables) {
                throwables.printStackTrace();
            }
        }
    }

    public TransMeta refreshMeta(TransMeta source, boolean idRefresh, Map<String, TransMeta> transMapping) {
        TransMeta target = new TransMeta();
        String[] ignoreProperties = {"id", "parent", "parentId", "transExps", "outDataSetMeta",
                "inDataSetMeta", "transPartition", "children", "hops", "startTrans", "parents", "attributeValues"};
        BeanUtils.copyProperties(source, target, ignoreProperties);
        if (idRefresh) target.setId(StringUtils.uuid());
        else target.setId(source.getId());
        DataExportUtil.addElementUserId(target.getId());
        transMapping.put(source.getId(), target);
        TransPluginMeta plugin = source.getUsedPlugin();
        if (plugin != null) {
            plugin = checkTransPluginMeta(plugin);
            target.setUsedPlugin(plugin);
            for (Map.Entry<TransAttributeMeta, String> entry : source.getAttributeValues().entrySet()) {
                TransAttributeMeta n_attr = plugin.getFeature(entry.getKey().getCode());
                if (n_attr != null) target.addTransAttribute(n_attr, entry.getValue());
            }
        }
        if (source.getTransExps() != null) {
            Set<TransExpMeta> newTransExps = new HashSet<>();
            for (TransExpMeta transExp : source.getTransExps()) {
                TransExpMeta newTransExp = new TransExpMeta();
                if (idRefresh) newTransExp.setTransExpId(StringUtils.uuid());
                newTransExp.setTransMeta(target);
                FeatureNode feature = plugin.getPluginExp(transExp.getExpression().getCode());
                newTransExp.setExpression(feature);
                Map<ElementNode, String> expValues = new HashMap<>();
                transExp.getExpValues().forEach((k, v) -> {
                    if (null != feature) {
                        ElementNode element = (ElementNode) feature.getArgument(k.getCode());
                        if (element != null) {
                            expValues.put(element, v);
                        }
                    }
                });
                newTransExp.setExpValues(expValues);
                newTransExps.add(newTransExp);
            }
            target.setTransExps(newTransExps);
        }

        String[] ignorePropertiesForPartition = {"id", "transMeta", "ranges", "partitionType"};
        String[] ignorePropertiesForPartitionRange = {"id"};

        if (source.getTransPartition() != null) {
            TransPartitionMeta sourcePartition = source.getTransPartition();
            TransPluginPartitionMeta partitionType = plugin.getPartitionType(sourcePartition.getPartitionType().getCode());
            TransPartitionMeta targetPartition = new TransPartitionMeta();
            BeanUtils.copyProperties(sourcePartition, targetPartition, ignorePropertiesForPartition);
            if (idRefresh) targetPartition.setId(StringUtils.uuid());
            targetPartition.setTransMeta(target);
            targetPartition.setPartitionType(partitionType);
            targetPartition.setMemo(null);
            for (TransPartitionRangeMeta transPartitionRangeMeta : source.getTransPartition().getRanges()) {
                TransPartitionRangeMeta range = new TransPartitionRangeMeta();
                BeanUtils.copyProperties(transPartitionRangeMeta, range, ignorePropertiesForPartitionRange);
                if (idRefresh) range.setId(StringUtils.uuid());
                targetPartition.addRange(range);
            }
            target.setTransPartition(targetPartition);
        }
        for (TransMeta child : source.getChildren()) {
            TransMeta newChild = refreshMeta(child, true, transMapping);
            target.addChild(newChild);
        }
        String[] ignorePropertiesForDataSetMeta = {"dataSetId", "dataColumnMetas"};
        String[] ignorePropertiesForDataColumnMeta = {"dataColumnId"};

        if (source.getOutDataSetMeta() != null) {
            DataSetMeta newOutDataSetMeta = new DataSetMeta();
            BeanUtils.copyProperties(source.getOutDataSetMeta(), newOutDataSetMeta, ignorePropertiesForDataSetMeta);
            if (idRefresh) newOutDataSetMeta.setDataSetId(StringUtils.uuid());
            Map<String, DataColumnMeta> oldDataColumnMetas = source.getOutDataSetMeta().getDataColumnMetas();
            for (String columnName : oldDataColumnMetas.keySet()) {
                DataColumnMeta newDataColumnMeta = new DataColumnMeta();
                DataColumnMeta oldDataColumnMeta = oldDataColumnMetas.get(columnName);
                BeanUtils.copyProperties(oldDataColumnMeta, newDataColumnMeta, ignorePropertiesForDataColumnMeta);
                if (idRefresh) newDataColumnMeta.setDataColumnId(StringUtils.uuid());
                newDataColumnMeta.setColumnName(columnName);
                newOutDataSetMeta.addColumnMeta(newDataColumnMeta);
            }
            target.setOutDataSetMeta(newOutDataSetMeta);
        }
        if (source.getInDataSetMeta() != null) {
            DataSetMeta newInDataSetMeta = new DataSetMeta();
            BeanUtils.copyProperties(source.getInDataSetMeta(), newInDataSetMeta, ignorePropertiesForDataSetMeta);
            if (idRefresh) newInDataSetMeta.setDataSetId(StringUtils.uuid());
            Map<String, DataColumnMeta> oldDataColumnMetas = source.getInDataSetMeta().getDataColumnMetas();
            for (String columnName : oldDataColumnMetas.keySet()) {
                DataColumnMeta newDataColumnMeta = new DataColumnMeta();
                DataColumnMeta oldDataColumnMeta = oldDataColumnMetas.get(columnName);
                BeanUtils.copyProperties(oldDataColumnMeta, newDataColumnMeta, ignorePropertiesForDataColumnMeta);
                if (idRefresh) newDataColumnMeta.setDataColumnId(StringUtils.uuid());
                newDataColumnMeta.setColumnName(columnName);
                newInDataSetMeta.addColumnMeta(newDataColumnMeta);
            }
            target.setInDataSetMeta(newInDataSetMeta);
        }
        if (source.getHops() != null && !source.getHops().isEmpty()) {
            String[] ignorePropertiesForHop = {"id", "fromTrans", "toTrans", "belongToTrans"};
            Set<TransHopMeta> newHops = new HashSet<>();
            for (TransHopMeta hop : source.getHops()) {
                TransHopMeta newHop = new TransHopMeta();
                BeanUtils.copyProperties(hop, newHop, ignorePropertiesForHop);
                if (idRefresh) newHop.setId(StringUtils.uuid());
                else  newHop.setId(hop.getId());
                newHop.setFromTrans(transMapping.get(hop.getFromTrans().getId()));
                newHop.setToTrans(transMapping.get(hop.getToTrans().getId()));
                newHop.setBelongToTrans(target);
                newHops.add(newHop);
            }
            target.setHops(newHops);
        }
        return target;
    }

    public UdfGraph _clone(UdfGraph source) {
        UdfGraph graph = new UdfGraph();
        BeanUtils.copyProperties(source, graph, "id", "udfNodeSet", "udfEdgeSet");
        this.baseDao.save(graph);

        Set<UdfNode> nodes = new HashSet<>();
        Set<UdfEdge> edges = new HashSet<>();
        Map<String, String> idMapping = new HashMap<>();

        for (UdfNode node : source.getUdfNodeSet()) {
            UdfNode new_node = new UdfNode();
            BeanUtils.copyProperties(node, new_node, "id", "graphId");
            new_node.setGraphId(graph.getId());
            this.baseDao.save(new_node);
            nodes.add(new_node);

            idMapping.put(node.getId(), new_node.getId());
        }
        graph.setUdfNodeSet(nodes);

        for (UdfEdge edge : source.getUdfEdgeSet()) {
            UdfEdge new_edge = new UdfEdge();
            BeanUtils.copyProperties(edge, new_edge, "id", "graphId", "inNodeId", "outNodeId");
            new_edge.setGraphId(graph.getId());
            new_edge.setInNodeId(idMapping.get(edge.getInNodeId()));
            new_edge.setOutNodeId(idMapping.get(edge.getOutNodeId()));
            this.baseDao.save(new_edge);

            edges.add(new_edge);
        }
        graph.setUdfEdgeSet(edges);
        return graph;
    }

    private TransPluginMeta checkTransPluginMeta(TransPluginMeta source) {
        Map<String, String> param = new HashMap<>();
        param.put("code", source.getCode());
        TransPluginMeta targetPlugin = (TransPluginMeta) this.baseDao.queryForObject("FROM TransPluginMeta WHERE code=:code", param);
        Assert.notNull(targetPlugin, "找不到插件[" + source.getCode() + "-" + source.getName() + "]请先在plugin加入对应的插件包");
        return targetPlugin;
    }

    @Override
    public void createDataObjView() {
        //获取所有的数据集
        List<LogicDataObj> allLogicDataObj = logicDataObjService.findAllLogicDataObj();
        //创建视图
        try {
            createDataObjView(allLogicDataObj);
        } catch (Exception e) {
         log.error(e.getMessage(),e);
        }

    }

    @Override
    public void createViewByLogicInfo(LogicDataObjInfo logic, String dbSchema) {

//        LogicDataObj logicDataObjById = logicDataObjService.findLogicDataObjById("349c6b56ea024444b6105195a2584e5c");
//        DataSetStepRelation o = (DataSetStepRelation) this.baseDao.get(DataSetStepRelation.class, logicDataObjById.getStepRelationId());
//        List<LogicDataColumn> columns = logicDataColumnService.findLogicDataColumnsByLogicDataObjId(logicDataObjById.getId());

        boolean isLogic = logic.getLogicDataObj().getCode().startsWith("v_t") ||
                logic.getLogicDataObj().getCode().startsWith("v_v_t") ||
                logic.getLogicDataObj().getCode().startsWith("v_v_v_t");
        String sql = getCreateViewSQL(logic, dbSchema, isLogic);
        try {
            dataSetEditService.registerTable(logic.getLogicDataObj(), sql, false);
        } catch (Exception e) {
         log.error(e.getMessage(),e);
        }


    }

    @Override
    public void createViewByLogicInfo(LogicDataObjInfo logic, Map<String, LogicDataObj> classifyMap, String dbSchema) {
        String sql = getCreateViewSQL(logic, dbSchema, true);
        LogicDataObj obj = classifyMap.get(logic.getLogicDataObj().getOwnerId());
        try {
            dataSetEditService.registerTable(logic.getLogicDataObj().getGlobalCode(), obj, sql, false);
        } catch (Exception e) {
         log.error(e.getMessage(),e);
        }
    }

    protected String getCreateViewSQL(LogicDataObjInfo logic, String dbSchema, boolean isLogic) {
        String sql = "";
        if (logic.getDataSetStepRelation() == null) {
            sql = logic.getLogicDataObj().getSql();
        } else {
            List<AbstractAtomDataSetOperator> setOperators = stepRelationService.flushStep("relation", logic.getDataSetStepRelation());
            sql = stepRelationService.getSQL(setOperators, logic.getDataColumns());
        }

        if (!isLogic) {
            sql = addSchemaNameToTableName(dbSchema, sql);
        }
        System.out.printf("创建视图sql : [%s]\n", sql);

        return sql;
    }

    public String addSchemaNameToTableName(String dbSchema, String sql) {
        Select stmt = null;
        try {
            stmt = (Select) CCJSqlParserUtil.parse(sql);
            PlainSelect plain = (PlainSelect) stmt.getSelectBody();
            plain.getFromItem().accept(new FromItemVisitorAdapter() {
                @Override
                public void visit(Table table) {
                    table.setSchemaName(dbSchema);
                }
            });
            if (plain.getJoins() != null) {
                for (Join join : plain.getJoins()) {
                    join.getRightItem().accept(new FromItemVisitorAdapter() {
                        @Override
                        public void visit(Table table) {
                            table.setSchemaName(dbSchema);
                        }
                    });
                }
            }
            sql = stmt.toString();
        } catch (JSQLParserException e) {
         log.error(e.getMessage(),e);
        }
        return sql;
    }


    private void createDataObjView(List<LogicDataObj> allLogicDataObj) throws Exception {
        Map<String, List<LogicDataObj>> logicOwnerMap = getLogicByOwner(allLogicDataObj);
        List<LogicDataObj> classifyLogicDataObjList = logicOwnerMap.get("classify");
        //先创建owner为物理表的数据集
        for (LogicDataObj obj : classifyLogicDataObjList) {
            dataSetEditService.registerTable(obj, obj.getSql(), false);
        }
        List<LogicDataObj> logicDataObjLists = logicOwnerMap.get("logic");
        //再创建owner是逻辑数据集的数据集
        for (LogicDataObj obj : logicDataObjLists) {
            dataSetEditService.registerTable(obj, obj.getSql(), false);

        }
    }

    @Override
    public void createTemplateView(String transIds) throws Exception {
        Assert.notNull(transIds, "逻辑数据集id为空！");
        //流程方案创建视图
        String[] split = transIds.split(",");
        if (split.length > 0) {
            for (int i = 0; i < split.length; i++) {

                createTransView(split[i]);
            }
        }

    }

    private void createTransView(String transId) {
        //获取模型
        TransMeta transMeta = transMetaService.getTransMetaById(transId);
        List<String> logiciIds = getLogicIdByTransMeta(transMeta);
        //创建数据集的视图
        createModelView(logiciIds);
    }

    public List<String> getLogicIdByTransMeta(TransMeta transMeta) {
        //获取模型中所有的输入和输出插件
        List<TransMeta> ioPlugin = getIOPlugin(transMeta);
        //获取输入和输出插件中的逻辑数据集
        return getLogicIdsByPlugins(ioPlugin);
    }

    /**
     * 模型创建视图
     *
     * @param logiciIds
     */
    private void createModelView(List<String> logiciIds) {
        List<LogicDataObj> allLogicDataObj = logicDataObjService.findLogicDataObjByIds(logiciIds);
        Map<String, List<LogicDataObj>> logicOwnerMap = getLogicByOwner(allLogicDataObj);
        List<LogicDataObj> classifyLogicDataObjList = logicOwnerMap.get("classify");
        //先创建owner为物理表的数据集
        registerView(classifyLogicDataObjList);
        List<LogicDataObj> logicDataObjLists = logicOwnerMap.get("logic");
        //再创建owner是逻辑数据集的数据集
        registerView(logicDataObjLists);
    }

    private void registerView(List<LogicDataObj> classifyLogicDataObjList) {
        for (LogicDataObj obj : classifyLogicDataObjList) {
            List setOperators = dataSetEditService.getSetOperators(obj.getId());
            try {
                dataSetEditService.registerModelView(setOperators, obj.getId());
            } catch (Exception e) {
             log.error(e.getMessage(),e);
            }
        }
    }

    /**
     * 获取输入插件的逻辑数据集id和输出插件物理表id对应的数据集的id
     *
     * @param ioPlugins
     * @return
     */
    private List<String> getLogicIdsByPlugins(List<TransMeta> ioPlugins) {
        List<String> resList = Lists.newArrayList();
        for (TransMeta ioPlugin : ioPlugins) {
            if ("INPUT".equalsIgnoreCase(ioPlugin.getPluginType())) {
                //输入插件数据集id
                StandardSqlInput pluginInstance = pluginConfigService.getPluginInstance(StandardSqlInput.class, ioPlugin.getId());
                if (StringUtils.isNotBlank(pluginInstance.getLogicObjId())) resList.add(pluginInstance.getLogicObjId());
            } else {
                //输出插件只能拿到表id
                StandardSqlOutput pluginInstance = pluginConfigService.getPluginInstance(StandardSqlOutput.class, ioPlugin.getId());
                String tableId = pluginInstance.getTableId();
                //通过表id获取它的所有数据集
                List<LogicDataObj> logicObjs = logicDataObjService.getLogicDataObjsByOwnerId(tableId);
                for (LogicDataObj logicObj : logicObjs) {
                    resList.add(logicObj.getId());
                }
            }
        }
        return resList;
    }

    /**
     * 通过方案获取输入和输出插件
     *
     * @param transMeta
     * @return
     */
    private List<TransMeta> getIOPlugin(TransMeta transMeta) {
        List<TransMeta> resList = Lists.newArrayList();
        Set<TransMeta> children = transMeta.getChildren();
        for (TransMeta child : children) {
            if ("INPUT".equalsIgnoreCase(child.getPluginType())
                    || "OUTPUT".equalsIgnoreCase(child.getPluginType())) {
                resList.add(child);
            }
        }
        return resList;
    }


    @Override
    public String buildTransMetaSQL(TransMeta transMeta) {
        StringBuilder builder = new StringBuilder();
        Map<String, Object> baseParam = buildTransMeta(transMeta);
        builder.append(insertSQL("t_etl_trans", baseParam));
        Set<TransMeta> children = transMeta.getChildren();
        for (TransMeta meta : children) {
            DataSetMeta inDataSetMeta = meta.getInDataSetMeta();
            if (inDataSetMeta != null) {
                Map<String, Object> inDatasetParam = buildDatasetParam(inDataSetMeta);
                builder.append(insertSQL("t_etl_dataset_meta", inDatasetParam));

                Map<String, DataColumnMeta> columnMetas = inDataSetMeta.getDataColumnMetas();
                columnMetas.forEach((k, v) -> {
                    Map<String, Object> inDataColumnParam = buildDataColumnParam(inDataSetMeta.getDataSetId(), k, v);
                    builder.append(insertSQL("t_etl_data_column_meta", inDataColumnParam));
                });
            }
            DataSetMeta outDataSetMeta = meta.getOutDataSetMeta();
            if (outDataSetMeta != null) {
                Map<String, Object> inDatasetParam = buildDatasetParam(outDataSetMeta);
                builder.append(insertSQL("t_etl_dataset_meta", inDatasetParam));

                Map<String, DataColumnMeta> columnMetas = outDataSetMeta.getDataColumnMetas();
                columnMetas.forEach((k, v) -> {
                    Map<String, Object> inDataColumnParam = buildDataColumnParam(outDataSetMeta.getDataSetId(), k, v);
                    builder.append(insertSQL("t_etl_data_column_meta", inDataColumnParam));
                });
            }

            Map<String, Object> childParam = buildTransMeta(meta);
            builder.append(insertSQL("t_etl_trans", childParam));

            TransPartitionMeta transPartition = meta.getTransPartition();
            if (transPartition != null) {
                Map<String, Object> partitionParam = buildPartitionParam(meta.getId(), transPartition);
                builder.append(insertSQL("t_etl_partition", partitionParam));
                Set<TransPartitionRangeMeta> ranges = transPartition.getRanges();
                for (TransPartitionRangeMeta range : ranges) {
                    Map<String, Object> rangeParam = buildRangeParam(transPartition.getId(), range);
                    builder.append(insertSQL("t_etl_partition_range", rangeParam));
                }
            }

            Map<TransAttributeMeta, String> attributeValues = meta.getAttributeValues();
            attributeValues.forEach((k, v) -> {
                if(k == null) return;
                Map<String, Object> attributeParam = buildAttributeParam(meta.getId(), k.getId(), v);
                builder.append(insertSQL("t_etl_trans_attribute", attributeParam));
            });

            Set<TransExpMeta> transExps = meta.getTransExps();
            if (transExps != null && !transExps.isEmpty()) {
                for (TransExpMeta transExp : transExps) {
                    String expId = transExp.getTransExpId();
                    FeatureNode featureNode = transExp.getExpression();
                    String expNodeId = featureNode.getExpNodeId();
                    Map<String, Object> expParam = buildExpParam(expId, meta.getId(), expNodeId);
                    builder.append(insertSQL("t_etl_trans_exp", expParam));
                    Map<ElementNode, String> expValues = transExp.getExpValues();
                    expValues.forEach((k, v) -> {
                        Map<String, Object> expNodeParam = buildExpNodeParam(expId, k.getExpNodeId(), v);
                        builder.append(insertSQL("t_etl_trans_exp_node", expNodeParam));
                    });
                }
            }
            Map<String, Object> detailParam = buildStepDetailParam(transMeta.getId(), meta.getId());
            builder.append(insertSQL("t_etl_trans_stepdetail", detailParam));
            if(CHECK_SERVICE.contains(meta.getUsedPlugin().getCode())){
                checkPluginRelation.add(detailParam);
            }
        }

        Set<TransHopMeta> hops = transMeta.getHops();
        for (TransHopMeta hop : hops) {
            Map<String, Object> hopParam = buildHopParam(hop);
            builder.append(insertSQL("t_etl_trans_hops", hopParam));
        }

        return builder.toString();
    }

    private Map<String, Object> buildTransMeta(TransMeta t) {
        Map<String, Object> param = new HashMap<>();
        param.put("id", t.getId());
        param.put("name", t.getName());
        param.put("type", t.getType());
        param.put("code", t.getCode());
        param.put("owner_id", t.getOwnerId());
        param.put("operate_time", t.getOperateTime());
        param.put("operate_user_id", t.getOperateUserId());
        param.put("transplugin_id", t.getUsedPlugin() == null ? null : t.getUsedPlugin().getId());
        param.put("out_dataset_id", t.getOutDataSetMeta() == null ? null : t.getOutDataSetMeta().getDataSetId());
        param.put("in_dataset_id", t.getInDataSetMeta() == null ? null : t.getInDataSetMeta().getDataSetId());
        param.put("x", t.getX());
        param.put("y", t.getY());
        param.put("version", t.getVersion());
        param.put("memo", t.getMemo());
        param.put("map_key", null);
        param.put("extended_type", null);
        param.put("target_id", null);
        param.put("source_id", null);
        param.put("trans_type", t.getTransType());
        param.put("work_thread_count", t.getWorkThreadCount());
        param.put("weight", t.getWeight());
        param.put("distributed", t.getDistributed());
        param.put("dataset_copy", "1");
        param.put("topology_node_id", null);
        param.put("etl_instance_code", null);
        param.put("create_by", null);
        param.put("exception_mode", "0");
        param.put("log_level", "ERROR");
        param.put("execute_engine", null);
        param.put("task_group", "DEF");
        param.put("is_custody", "0");
        param.put("is_template", null);
        return param;
    }

    private Map<String, Object> buildStepDetailParam(String pId, String cId) {
        Map<String, Object> param = new HashMap<>();
        param.put("trans_id", pId);
        param.put("child_trans_id", cId);
        param.put("step_type", null);
        return param;
    }

    private Map<String, Object> buildDatasetParam(DataSetMeta dataSet) {
        Map<String, Object> param = new HashMap<>();
        param.put("dataset_id", dataSet.getDataSetId());
        param.put("io_type", "0");
        param.put("filter_express", null);
        param.put("distinct_express", null);
        return param;
    }

    private Map<String, Object> buildDataColumnParam(String dataSetId, String columnName, DataColumnMeta dataColumn) {
        Map<String, Object> param = new HashMap<>();
        if(dataColumn.getColumnType() == null) return param;
        param.put("data_column_id", dataColumn.getDataColumnId());
        param.put("dataset_id", dataSetId);
        param.put("data_type_id", dataColumn.getColumnType().getId());
        param.put("column_name", columnName);
        param.put("data_type_length", dataColumn.getDataLength());
        param.put("precsn", dataColumn.getPrecsn());
        param.put("meta_column_id", dataColumn.getMetaColumnId());
        param.put("filter_express", dataColumn.getFilterExpress());
        param.put("unique_value", dataColumn.getUniqueValue());
        param.put("column_zh_name", dataColumn.getColumnZhName());
        return param;
    }


    private Map<String, Object> buildPartitionParam(String tId, TransPartitionMeta transPartition) {
        Map<String, Object> param = new HashMap<>();
        param.put("id", transPartition.getId());
        param.put("name", transPartition.getName());
        param.put("version", transPartition.getVersion());
        param.put("type", transPartition.getType());
        param.put("memo", null);
        param.put("code", transPartition.getCode());
        param.put("operate_time", transPartition.getOperateTime());
        param.put("operate_user_id", transPartition.getOperateUserId());
        param.put("trans_id", tId);
        param.put("trans_plugin_partition_id", transPartition.getPartitionType().getId());
        param.put("partition_param", transPartition.getPartitionParam());
        param.put("date_format", transPartition.getDateFormat());
        param.put("date_partition_type", transPartition.getDatePartitionType());
        return param;
    }

    private Map<String, Object> buildRangeParam(String pId, TransPartitionRangeMeta range) {
        Map<String, Object> param = new HashMap<>();
        param.put("id", range.getId());
        param.put("name", range.getName());
        param.put("version", range.getVersion());
        param.put("type", range.getType());
        param.put("memo", null);
        param.put("code", range.getCode());
        param.put("owner_id", pId);
        param.put("map_key", null);
        param.put("extended_type", null);
        param.put("operate_time", range.getOperateTime());
        param.put("operate_user_id", range.getOperateUserId());
        param.put("partition_val", range.getRangeValue());
        param.put("start_val", range.getStartVal());
        param.put("end_val", range.getEndVal());
        param.put("start_comparison", range.getStartComparison());
        param.put("end_comparison", range.getEndComparison());
        param.put("increment_stamp", range.getIncrementStamp());
        param.put("cycle_extra", range.getCycleExtra());
        param.put("exec_status", range.getExecStatus());
        param.put("range_type", range.getRangeType());
        param.put("exec_flag", range.getExecFlag());
        param.put("range_param", range.getRangeParam());
        return param;
    }

    private Map<String, Object> buildAttributeParam(String transId, String attributeId, String value) {
        Map<String, Object> param = new HashMap<>();
        param.put("trans_id", transId);
        param.put("trans_attribute_id", attributeId);
        param.put("param_value", value);
        return param;
    }

    private Map<String, Object> buildExpParam(String expId, String transId, String expNodeId) {
        Map<String, Object> param = new HashMap<>();
        param.put("trans_exp_id", expId);
        param.put("trans_id", transId);
        param.put("exp_node_id", expNodeId);
        return param;
    }

    private Map<String, Object> buildExpNodeParam(String expId, String expNodeId, String value) {
        Map<String, Object> param = new HashMap<>();
        param.put("trans_exp_id", expId);
        param.put("exp_node_id", expNodeId);
        param.put("exp_val", value);
        return param;
    }

    private Map<String, Object> buildHopParam(TransHopMeta hop) {
        Map<String, Object> param = new HashMap<>();
        param.put("id", hop.getId());
        param.put("name", hop.getName());
        param.put("version", hop.getVersion());
        param.put("type", hop.getType());
        param.put("memo", null);
        param.put("code", hop.getCode());
        param.put("owner_id", null);
        param.put("map_key", null);
        param.put("extended_type", null);
        param.put("operate_time", hop.getOperateTime());
        param.put("operate_user_id", hop.getOperateUserId());
        param.put("from_trans_id", hop.getFromTrans().getId());
        param.put("to_trans_id", hop.getToTrans().getId());
        param.put("belong_trans_id", hop.getBelongToTrans().getId());
        param.put("handle_mode", hop.getHandleMode());
        return param;
    }

    private String insertSQL(String tableName, Map<String, Object> param) {
        List<String> list = new ArrayList<>();
        for (String s : param.keySet()){
            list.add(param.get(s) != null ? "'" + param.get(s).toString().replaceAll("'","''") + "'" : "NULL");
        }        String sql = "INSERT INTO %s (%s) VALUES (%s);\n";
        return String.format(sql, tableName, String.join(",", param.keySet()), String.join(",", list));
    }

    private Map<String, List<LogicDataObj>> getLogicByOwner(List<LogicDataObj> allLogicDataObj) {
        Map<String, List<LogicDataObj>> resMap = Maps.newHashMap();
        List<LogicDataObj> logicsClassify = Lists.newArrayList();
        List<LogicDataObj> logics = Lists.newArrayList();
        for (LogicDataObj obj : allLogicDataObj) {
//            String owner_id = (String) obj.get("owner_id");
            if (obj.getOwner() == null) continue;
            if (StringUtils.isBlank(obj.getOwner().getType())) continue;
            //数据集的owner是逻辑表
            if ("LogicDataObj".equalsIgnoreCase(obj.getOwner().getType())) {
                LogicDataObj logicDataObjById = logicDataObjService.findLogicDataObjById(obj.getOwnerId());
                putLogicDataObj(logicDataObjById, resMap);
                logics.add(obj);
            }
            //数据集owner是物理表
            if (!"LogicDataObj".equalsIgnoreCase(obj.getOwner().getType())) {
                logicsClassify.add(obj);
            }
        }
        resMap.put("classify", logicsClassify);
        resMap.put("logic", logics);
        return resMap;
    }

    private void putLogicDataObj(LogicDataObj obj, Map<String, List<LogicDataObj>> resMap) {
        if (!"LogicDataObj".equalsIgnoreCase(obj.getOwner().getType())) {
            List<LogicDataObj> dataObjs = resMap.get("classify");
            if (dataObjs == null) {
                dataObjs = Lists.newArrayList();
            }
            dataObjs.add(obj);
            resMap.put("classify", dataObjs);
        } else {
            LogicDataObj logicDataObjById = logicDataObjService.findLogicDataObjById(obj.getOwnerId());
            putLogicDataObj(logicDataObjById, resMap);
            List<LogicDataObj> logicDataObjList = resMap.get("logic");
            if (logicDataObjList == null) {
                logicDataObjList = Lists.newArrayList();
            }
            logicDataObjList.add(obj);
            resMap.put("logic", logicDataObjList);
        }

    }


    @Override
    public List<TransVariable> queryAllVariables() {
        List<TransVariable> transVariableList = baseDao.queryForList("From TransVariable");
        return transVariableList;
    }

    @Override
    public void mergeObj(Object obj) {
        this.baseDao.merge(obj);
    }

    @Override
    public List<TransVariableRelation> queryAllVariablesRelations() {
        List<TransVariableRelation> transVariableRelations = baseDao.queryForList("From TransVariableRelation");
        return transVariableRelations;
    }


    @Override
    public Map<String, String> queryTransByVariableId(List<String> variableIds) {
        Map<String, String> map = new HashMap<>();
        List<String> ids = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(variableIds)) {
            String sql = "select code ,id from t_etl_trans where id in (select trans_id from t_md_trans_variable_relation where variable_id in (%s) )";
            List<Map> list = this.baseDao.sqlQueryForList(String.format(sql, String.join(",", variableIds)));
            if (CollectionUtils.isNotEmpty(list)) {
                for (Map maps : list) {
                    map.put((String) maps.get("id"), (String) maps.get("code"));
                }
            }
        }
        return map;
    }


    @Override
    public List<Map<String, String>> queryTransByCodes(List<String> transMetaCodes) {
        StringBuffer sql = new StringBuffer();

         sql.append("select t.id,t.code from t_etl_trans t LEFT JOIN t_md_classify_element e ON t.id = e.element_id  ");
        sql.append(" where trans_type='TRANSFORM' and t.id = e.element_id  ");
        if(CollectionUtils.isNotEmpty(transMetaCodes)){
            sql.append(" and (");
            Iterator<String> iterator = transMetaCodes.iterator();
            while (iterator .hasNext()){
                String next = iterator.next();
                sql.append("code like '").append(next).append("%'");
                if(iterator.hasNext())sql.append(" or ");
            }
            sql.append(" )");
        }

        return this.baseDao.sqlQueryForList(sql.toString());
    }

    @Override
    public Map findDatasourceByRdbId(String rdbId) {
        StringBuffer sql = new StringBuffer();
        sql.append(" SELECT db.code AS dbcode,db.NAME AS dbname,rdb.NAME tableName,rdb.code tableCode FROM t_md_dw_db_instance db, ")
                .append(" t_md_rdb_dataobj rdb WHERE db.ID = ( SELECT dw_db_id FROM t_dw_table_mapping WHERE classifier_stat_id =:rdbId)")
                .append(" and rdb.id=:rdbId");
        return this.baseDao.sqlQueryForMap(sql.toString(), addParam("rdbId", rdbId).param());
    }

    @Override
    public Map<String, Object> getSchemaByRdbId(String rdbId) {
        String sql = "select owner_id from t_md_rdb_dataobj where id = :rdbId";
        String schemaId = this.baseDao.sqlQueryForValue(sql, addParam("rdbId", rdbId).param());
        sql = "select * from t_md_rdb_schema where id = :schemaId";
        if(schemaId == null) return new HashMap<>();
        return this.baseDao.sqlQueryForMap(sql,addParam("schemaId",schemaId).param());
    }
}
