package com.dragonsoft.cicada.datacenter.modules.system.permissions.task;


import com.code.metaservice.datavisual.IDashBoardService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Configurable;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;

@Slf4j
@Component
@Configurable
@EnableScheduling
public class ScheduledTasks {
    private int count = 0;

    @Autowired
    IDashBoardService dashBoardService;
    //每5秒执行一次
//    */5 * * * * ?
//    每天凌晨1点执行一次
//    0 0 1 * * ?
    @Scheduled(cron = "0 0 1 * * ?")
    public void reportCurrentByCron(){
//        log.info("删除可视化多余组件！");
        dashBoardService.deleteWidget();
        /*count ++;
        System.out.println ("执行次数："+ count +"次     Scheduling Tasks Examples By Cron: The time is now " + dateFormat ().format (new Date()));*/
    }

    private SimpleDateFormat dateFormat(){
        return new SimpleDateFormat ("HH:mm:ss");
    }
}
