package com.dragonsoft.cicada.datacenter.modules;

import com.dragonsoft.authorization.servlet.LicenseManagerServlet;
import com.dragonsoft.cicada.datacenter.modules.filter.CORSFilter;
import com.dragonsoft.cicada.datacenter.modules.filter.LicenceFilter;
import com.dragonsoft.cicada.datacenter.modules.filter.UserPermissionFilter;
import com.dragonsoft.cicada.datacenter.modules.filter.XssAndSqlFilter;
import com.google.common.collect.Lists;
import edu.yale.its.tp.cas.client.filter.CASFilter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.boot.web.servlet.ServletRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

import javax.servlet.Filter;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> Jiebin
 * @date 2021-03-26 14:12
 */
@Configuration
@PropertySource("classpath:case-config.properties")
public class LicenceConfig {

    @Value("${licenceOpen}")
    private String licenceOpen;

    @Value("${accessThirdSystem}")
    private String accessThirdSystem;

    @Value("${system.filterXss}")
    private String filterXss;

    private Map<String, String> initLicenceParametersMap =  new HashMap<>();
    private Map<String, String> initAccessParametersMap =  new HashMap<>();




    @Bean
    public ServletRegistrationBean licenseManagerServlet() {
        ServletRegistrationBean servletRegistrationBean = new ServletRegistrationBean();
        servletRegistrationBean.setServlet(new LicenseManagerServlet());
        servletRegistrationBean.setUrlMappings(Lists.newArrayList("/license/LicenseManagerServlet"));
        return servletRegistrationBean;
    }

    @Bean
    public FilterRegistrationBean corsFilter() {
        FilterRegistrationBean filterRegistrationBean = new FilterRegistrationBean();
        filterRegistrationBean.setFilter(new CORSFilter());
        filterRegistrationBean.setUrlPatterns(Lists.newArrayList("/*"));
        filterRegistrationBean.setOrder(1);
        return filterRegistrationBean;
    }

    @Bean
    public FilterRegistrationBean licenceFilter() {
        initLicenceParametersMap.put("licenceOpen",licenceOpen);
        FilterRegistrationBean filterRegistrationBean = new FilterRegistrationBean();
        filterRegistrationBean.setFilter(new LicenceFilter());
        filterRegistrationBean.setInitParameters(initLicenceParametersMap);
        filterRegistrationBean.setUrlPatterns(Lists.newArrayList("/*"));
        filterRegistrationBean.setOrder(2);
        return filterRegistrationBean;
    }

    @Bean
    public FilterRegistrationBean userPermissionFilter() {
        initAccessParametersMap.put("accessThirdSystem",accessThirdSystem);
        FilterRegistrationBean filterRegistrationBean = new FilterRegistrationBean();
        filterRegistrationBean.setFilter(this.UserPermissionFilter());
        filterRegistrationBean.setInitParameters(initAccessParametersMap);
        filterRegistrationBean.setUrlPatterns(Lists.newArrayList("/*"));
        filterRegistrationBean.setOrder(5);
        return filterRegistrationBean;
    }
    @Bean
    public Filter UserPermissionFilter() {
        return new UserPermissionFilter();
    }

    @Bean
    public FilterRegistrationBean xssAndSqlFilter() {
        initLicenceParametersMap.put("filterXss",filterXss);
        FilterRegistrationBean filterRegistrationBean = new FilterRegistrationBean();
        filterRegistrationBean.setFilter(new XssAndSqlFilter());
        filterRegistrationBean.setUrlPatterns(Lists.newArrayList("/*"));
        filterRegistrationBean.setOrder(4);
        return filterRegistrationBean;
    }

    @Bean
    public FilterRegistrationBean casFilterRegistration() {
        FilterRegistrationBean filterRegistrationBean = new FilterRegistrationBean();
        filterRegistrationBean.setFilter(new CASFilter());
        filterRegistrationBean.setName("CAS Filter");
        filterRegistrationBean.addUrlPatterns("/*");
        filterRegistrationBean.setOrder(3);
        return filterRegistrationBean;
    }
}
