package com.dragonsoft.cicada.datacenter.modules.system.permissions.controller;

import com.dragoninfo.dfw.bean.Result;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/5/18
 */
@Controller
@CrossOrigin
@RequestMapping("/licence")
public class LicenceController {

    /**
     * licence过滤器需要的请求
     * @return
     */
    @ResponseBody
    @RequestMapping("/licence")
    public Result licence() {
        Map map = new HashMap();
        map.put("licence", "licence");
        return Result.success(map);
    }

}
