package com.dragonsoft.cicada.datacenter.modules.datavisual.widgetmeta.impl;

import com.code.common.utils.StringUtils;
import com.code.metadata.datavisual.WidgetCategories;
import com.code.metadata.datavisual.WidgetMeta;
import com.code.metaservice.datavisual.IWidgetCategoriesService;
import com.dragonsoft.cicada.datacenter.modules.datavisual.model.enums.WidgetType;
import com.dragonsoft.cicada.datacenter.modules.datavisual.widgetmeta.IWidgetCategoriesBuilder;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Set;

import static com.dragonsoft.cicada.datacenter.modules.datavisual.model.enums.WidgetType.CHART;


@Component
public class WidgetCategoriesBuilder implements IWidgetCategoriesBuilder {
    @Autowired
    IWidgetCategoriesService widgetCategoriesService;


    @Override
    public List<WidgetCategories> getChartGroup() {
//        List<WidgetCategories> widgetCategories = widgetCategoriesService.getChildren(CHART.getId());
        List<WidgetCategories> categoriesServiceList = widgetCategoriesService.getList();
        List<WidgetCategories> parentCategories = getParentCategories(categoriesServiceList);
        for (WidgetCategories parentCategory : parentCategories) {
            List<WidgetCategories> childCategories = Lists.newArrayList();
            Set<WidgetMeta> widgetMetas = Sets.newLinkedHashSet();
            for (WidgetCategories widgetCategories : categoriesServiceList) {
                if(StringUtils.isNotBlank(widgetCategories.getParentId()) && widgetCategories.getParentId().equals(parentCategory.getId())){
                    widgetCategories.setWidgetMetas(parentCategory.getWidgetMetas());
                    childCategories.add(widgetCategories);
//                    WidgetMeta widgetMeta = new WidgetMeta();
//                    widgetMeta.set
                }
            }
            parentCategory.setChildren(childCategories);
//            parentCategory.setWidgetMetas(childCategories);
        }
        return parentCategories;
    }

    public List<WidgetCategories> getParentCategories(List<WidgetCategories> categoriesServiceList) {
        List<WidgetCategories> parentCategories = Lists.newArrayList();

        for (WidgetCategories widgetCategories : categoriesServiceList) {
            if(CHART.getId().equals(widgetCategories.getParentId())){
                parentCategories.add(widgetCategories);
            }
        }

        return parentCategories;
    }


    @Override
    public void saveWidgetCategories() {
        List<WidgetType> widgetTypes = Arrays.asList(WidgetType.values());
        for (WidgetType w : widgetTypes) {
            if (null == w.getParent()) {
                WidgetCategories wc = new WidgetCategories();
                wc.setId(w.getId());
                wc.setName(w.getName());
                widgetCategoriesService.saveOrUpdateCategories(wc);
                this.saveChildren(wc, widgetTypes);
            }
        }
    }


    public void saveChildren(WidgetCategories p, List<WidgetType> all) {
        for (WidgetType w : all) {
            if (null != w.getParent() && w.getParent().getId().equals(p.getId())) {
                WidgetCategories ww = new WidgetCategories();
                ww.setId(w.getId());
                ww.setName(w.getName());
                ww.setParentId(p.getId());
                widgetCategoriesService.saveOrUpdateCategories(ww);
                this.saveChildren(ww, all);
            }
        }
    }
}
