package com.dragonsoft.cicada.datacenter.modules.firstpage.share.vo;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 分享vo
 */
@Data
public class ShareVo {

    private int pageSize = 10;
    private int pageNum = 1;

    private String userId;
    private String username; //根据用户名查找
    private String userType;
    private String resourceName;//资源名称
    private String resourceType;//4为服务，1为数据集

    //来自哪些用户
    private List<String> fromUserIds;

    private List<String> sharedUserIds;//被分享对象id

    private List<String> resourceIds;

    private String dataSetTreeId;//所属目录id

    private List<Map<String,String>> resources;
}
