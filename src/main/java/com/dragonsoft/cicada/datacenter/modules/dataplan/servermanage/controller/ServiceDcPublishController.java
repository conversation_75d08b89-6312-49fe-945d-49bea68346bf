package com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.code.common.utils.R;
import com.code.common.utils.StringUtils;
import com.code.common.utils.assertion.Assert;
import com.code.metadata.ga.log.security.audit.platform.IAsyncServiceRunLogService;
import com.code.metadata.sm.EnumServiceType;
import com.code.metadata.sm.ServiceMeta;
import com.code.metadata.sm.ServiceParams;
import com.code.metaservice.sm.IServiceMetaService;
import com.code.metaservice.sm.IServiceParamsService;
import com.code.metaservice.sm.IServicePublicationService;
import com.dragoninfo.dfw.annotation.FuncScanAnnotation;
import com.dragoninfo.dfw.annotation.ValidateAndLogAnnotation;
import com.dragoninfo.dfw.bean.Result;
import com.dragoninfo.dfw.entity.TSysAuthUser;
import com.dragoninfo.dfw.service.SysAuthUserService;
import com.dragonsoft.cicada.datacenter.ServicePublishCenterClient;
import com.dragonsoft.cicada.datacenter.ServicePublishClient;
import com.dragonsoft.cicada.datacenter.common.token.GlobalConstant;
import com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.service.ICicadaMetaServicePublishService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.service.IServiceManagementService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.service.IServicePublicService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.utils.ClientAccesstIpUtil;
import com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.vo.*;
import com.dragonsoft.cicada.datacenter.modules.modeling.client.MLSQLScheduleClient;
import com.dragonsoft.cicada.datacenter.modules.modeling.client.RetrofitManager;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import retrofit2.Response;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@CrossOrigin
@RestController
@RequestMapping("/publish")
@FuncScanAnnotation(code = "serviceManagement", name = "服务管理", parentCode = "dataModeling")
@Api(value = "服务管理")
@Slf4j
public class ServiceDcPublishController {


    @Autowired
    private IServicePublicService servicePublicService;

    @Autowired
    private ICicadaMetaServicePublishService cicadaMetaServicePublishServiceImpl;

    @Autowired
    private IServiceMetaService serviceMetaService;

    @Value("${mlsql.schedule.address}")
    private String scheduleServer;

    @Autowired
    private SysAuthUserService sysAuthUserService;

    @Autowired
    private ServicePublishClient servicePublishClient;

    @Autowired
    IServicePublicationService servicePublicationService;

    @Autowired
    IServiceParamsService serviceParamsService;

    @Autowired
    IAsyncServiceRunLogService iAsyncServiceRunLogService;

    @Autowired
    private IServiceManagementService serviceManagementService;

    @Resource
    private ServicePublishCenterClient servicePublishCenterClient;

    @GetMapping("/isIssue")
    @FuncScanAnnotation(code = "", name = "服务发布", parentCode = "")
    @ValidateAndLogAnnotation
    public Result isIssue(String sourceId) {
        String issue = servicePublicService.isIssue(sourceId);
        return Result.toResult(R.ok(StringUtils.isNotBlank(issue)));
    }

    @PostMapping("/createDataQueryService")
    @FuncScanAnnotation(code = "serviceManagementGenerateAPI", name = "数据查询发布服务", parentCode = "serviceManagement")
    @ValidateAndLogAnnotation
    @ApiOperation(value = "数据查询发布服务")
    public Result createDataQueryService(@RequestBody ParamConfigVo paramConfigVo, HttpServletRequest request) {
        String userId = (String) request.getSession().getAttribute("userId");
        servicePublicService.checkPublishName(paramConfigVo.getServiceMetaId(), paramConfigVo.getInterfaceChineseName(), false, EnumServiceType.INQUIRY_SERVICE.getCode(), userId);
        Result result = null;
        try {
            result = servicePublicService.createDataQueryService(paramConfigVo, userId);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
        }
        return result;
    }

    @PostMapping("/createInformationVerificationService")
//    @FuncScanAnnotation(code = "serviceManagementInformationVerification", name = "信息核查发布服务", parentCode = "serviceManagement")
//    @ValidateAndLogAnnotation
    @ApiOperation(value = "信息核查发布服务")
    public Result createInformationVerificationService(@RequestBody ParamConfigVo paramConfigVo, HttpServletRequest request) {
        String userId = (String) request.getSession().getAttribute("userId");
        servicePublicService.checkPublishName(paramConfigVo.getServiceMetaId(), paramConfigVo.getInterfaceChineseName(), false, EnumServiceType.INFORMATION_VERFICATION.getCode(), userId);
        Result result = null;
        try {
            result = servicePublicService.createInformationVerificationService(paramConfigVo, userId);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
        }
        return result;
    }

    @RequestMapping("/publishServiceNoSaveMeta")
    @ApiOperation(value = "已有元信息，创建jar并发布")
    public Result publishServiceNoSaveMeta(String id, HttpServletRequest request) throws Exception {
        String userId = (String) request.getSession().getAttribute("userId");
        Result result = null;
        ParamConfigVo paramConfigVo = serviceManagementService.queryServiceDetails(id);
        result = servicePublicService.publishServiceNoSaveMeta(paramConfigVo, userId);
        return result;
    }

    @PostMapping("/createService")
    @FuncScanAnnotation(code = "serviceManagementGenerateAPI", name = "数据查询生成API", parentCode = "serviceManagement")
    @ValidateAndLogAnnotation
    @ApiOperation(value = "数据集服务发布")
    public Result createService(@RequestBody ParamConfigVo paramConfigVo, HttpServletRequest request) {
        String userId = (String) request.getSession().getAttribute("userId");
        servicePublicService.checkPublishName(paramConfigVo.getServiceMetaId(), paramConfigVo.getInterfaceChineseName(), false, EnumServiceType.INQUIRY_SERVICE.getCode(), userId);
        return Result.toResult(servicePublicService.createService(paramConfigVo, userId));
    }

    @PostMapping("/editModelService")
    //@FuncScanAnnotation(code = "modelServiceGenerateAPI", name = "模型服务生成API", parentCode = "processModeling")
    @ApiOperation(value = "编辑模型服务发布")
    public Result editModelService(@RequestBody SubscribeParamConfigVo paramConfigVo, HttpServletRequest request) {
        String userId = (String) request.getSession().getAttribute("userId");
        ServiceMeta serviceMeta = serviceMetaService.queryByMetaId(paramConfigVo.getServiceMetaId());
        String servicePublicationId = serviceMeta.getServicePublication().getId();
        paramConfigVo.setServicePublicationId(servicePublicationId);

        Result result = cicadaMetaServicePublishServiceImpl.updateService(paramConfigVo, userId);

        servicePublicService.updateElementAndClassifyByMetaId(paramConfigVo.getServiceMetaId(), paramConfigVo.getClassifyId());

        //TODO 目前先这样干
        if ("1".equals(paramConfigVo.getSaveType())) {
            //如果是 保存并退出 即未发布  上面的操作实际上已经启用了  所以先将启用的服务下线 不然再点发布的话 重复启用导致同一个类加载器重复加载同一个类 导致错误
            servicePublicService.disableService(serviceMeta.getId());
        }

        return result;
    }

    @PostMapping("/testQueryAndVercationService")
    public Result testQueryAndVercationService(HttpServletRequest servletRequest, @RequestBody BatchTestVo paramConfigVo) {
        paramConfigVo.setToken(GlobalConstant.UserProperties.DC_SUPER_ID);
        R r = null;
        try {
            r = servicePublicService.testBatchService(paramConfigVo, servletRequest);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
        }
        return Result.toResult(r);
    }

    @PostMapping("/createModelService")
    //@FuncScanAnnotation(code = "modelServiceGenerateAPI", name = "模型服务生成API", parentCode = "processModeling")
    //@FuncScanAnnotation(code = "modelServiceGenerateAPI", name = "生成API", parentCode = "processModeling")
    //@ValidateAndLogAnnotation
    @ApiOperation(value = "模型服务发布")
    public Result createModelService(@RequestBody SubscribeParamConfigVo paramConfigVo, HttpServletRequest request) {
        String userId = (String) request.getSession().getAttribute("userId");
        Result result = cicadaMetaServicePublishServiceImpl.createService(paramConfigVo, userId);
        return result;
    }

    @RequestMapping("/publishModelServiceCreateJar")
    //@FuncScanAnnotation(code = "modelServiceGenerateAPI", name = "模型服务生成API", parentCode = "processModeling")
    @FuncScanAnnotation(code = "modelServiceGenerateAPI", name = "发布API", parentCode = "processModeling")
    @ValidateAndLogAnnotation
    @ApiOperation(value = "模型服务生成jar包发布")
    public Result publishModelServiceCreateJar(String id, HttpServletRequest request) {
        try {
            String userId = (String) request.getSession().getAttribute("userId");
            SubscribeParamConfigVo paramConfigVo = (SubscribeParamConfigVo) serviceManagementService.queryServiceDetails(id);
            Result result = cicadaMetaServicePublishServiceImpl.publishModelServiceCreateJar(paramConfigVo, userId);
            return result;
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return Result.error("500", e.getMessage());
        }
    }


    @PostMapping("/testService")
    @FuncScanAnnotation(code = "serviceManagementTest", name = "数据查询测试", parentCode = "serviceManagement")
    @ValidateAndLogAnnotation
    public Result testService(HttpServletRequest servletRequest, @RequestBody ParamConfigVo paramConfigVo) {
        paramConfigVo.setToken(GlobalConstant.UserProperties.DC_SUPER_ID);
        R r = null;
        try {
            r = servicePublicService.testService(paramConfigVo, servletRequest);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
        }
        return Result.toResult(r);
    }

    @PostMapping("/testModelService")
    //@FuncScanAnnotation(code = "modelServiceTest", name = "模型服务测试", parentCode = "processModeling")
    @FuncScanAnnotation(code = "modelServiceTest", name = "测试API", parentCode = "processModeling")
    @ValidateAndLogAnnotation
    @ApiOperation(value = "模型服务测试")
    public Result testModelService(HttpServletRequest servletRequest, @RequestBody ModelServiceRequestVo requestVo) throws UnsupportedEncodingException {
        requestVo.setToken(GlobalConstant.UserProperties.DC_SUPER_ID);
        return cicadaMetaServicePublishServiceImpl.testService(requestVo, servletRequest);
    }


    @PostMapping("/testModelServiceGetResult")
    //@FuncScanAnnotation(code = "modelServiceGetResultTest", name = "模型服务测试异步获取结果", parentCode = "processModeling")
    @FuncScanAnnotation(code = "modelServiceGetResultTest", name = "异步测试", parentCode = "processModeling")
    @ValidateAndLogAnnotation
    @ApiOperation(value = "模型服务测试异步获取结果")
    public Result testModelServiceGetResult(HttpServletRequest servletRequest, @RequestBody ModelServiceRequestVo requestVo) throws UnsupportedEncodingException {
        requestVo.setToken(GlobalConstant.UserProperties.DC_SUPER_ID);
        return cicadaMetaServicePublishServiceImpl.testServiceGetResult(requestVo, servletRequest);
    }

    @RequestMapping("/offlineService")
    public Result offlineService(String sourceId) {
        servicePublicService.offlineService(sourceId);
        return Result.success();
    }

    /**
     * 服务卸载
     *
     * @param serviceMetaId
     * @return
     */
    @RequestMapping("/offlineServiceByServiceMetaId")
    @FuncScanAnnotation(code = "serviceManagementUninstallg", name = "服务卸载", parentCode = "serviceManagement")
    @ValidateAndLogAnnotation
    @ApiOperation(value = "服务卸载")
    public Result offlineServiceByServiceMetaId(String serviceMetaId) {
        //todo 服务卸载校验
        //SELECT * FROM  t_md_etl_trans_attribute WHERE CODE ='serviceId' where map_key ='服务id'
        //加个接口判断，如果存在相对应的数据，则不让卸载
        servicePublicService.deleteElementAndClassifyByMetaId(serviceMetaId);
        servicePublicService.offlineServiceByServiceMetaId(serviceMetaId);
        return Result.success();
    }

    /**
     * 服务重新发布
     *
     * @param serviceMetaId
     * @return
     */
    @RequestMapping("/redistributionService")
    @FuncScanAnnotation(code = "serviceManagementStart", name = "服务启用", parentCode = "serviceManagement")
    @ValidateAndLogAnnotation
    public Result redistributionService(String serviceMetaId) {
        servicePublicService.redistributionService(serviceMetaId);
        return Result.success();
    }

    /**
     * 服务停用
     *
     * @param serviceMetaId
     * @return
     */
    @RequestMapping("/disableService")
    @FuncScanAnnotation(code = "serviceManagementDeactivate", name = "服务停用", parentCode = "serviceManagement")
    @ValidateAndLogAnnotation
    public Result disableService(String serviceMetaId) {
        servicePublicService.disableService(serviceMetaId);
        return Result.success();
    }

    @RequestMapping("/editService")
//    @FuncScanAnnotation(code = "serviceManagementUninstallg", name = "服务编辑", parentCode = "serviceManagement")
//    @ValidateAndLogAnnotation
    public Result editService(@RequestBody ParamConfigVo paramConfigVo, HttpServletRequest request) {
        String userId = (String) request.getSession().getAttribute("userId");
        servicePublicService.checkPublishName(paramConfigVo.getServiceMetaId(), paramConfigVo.getInterfaceChineseName(), true, EnumServiceType.INQUIRY_SERVICE.getCode(), userId);
        ServiceMeta serviceMeta = serviceMetaService.queryByMetaId(paramConfigVo.getServiceMetaId());
        String servicePublicationId = serviceMeta.getServicePublication().getId();
        paramConfigVo.setServicePublicationId(servicePublicationId);
        servicePublicService.deleteElementAndClassifyByMetaId(paramConfigVo.getServiceMetaId());
        servicePublicService.offlineServiceByServiceMetaIdComitSession(paramConfigVo.getServiceMetaId());
        servicePublicService.createService(paramConfigVo, userId);
        return Result.success();
    }


    @RequestMapping("/editDataQueryService")
    public Result editDataQueryService(@RequestBody ParamConfigVo paramConfigVo, HttpServletRequest request) {
        String userId = (String) request.getSession().getAttribute("userId");
        servicePublicService.checkPublishName(paramConfigVo.getServiceMetaId(), paramConfigVo.getInterfaceChineseName(), true, EnumServiceType.INQUIRY_SERVICE.getCode(), userId);
        ServiceMeta serviceMeta = serviceMetaService.queryByMetaId(paramConfigVo.getServiceMetaId());
        String servicePublicationId = serviceMeta.getServicePublication().getId();
        paramConfigVo.setServicePublicationId(servicePublicationId);
        Result result = null;
        try {
            if ("0".equals(paramConfigVo.getSaveType())) {
                result = cicadaMetaServicePublishServiceImpl.updateQueryService(paramConfigVo, userId);
            } else {
                result = cicadaMetaServicePublishServiceImpl.updateNoPublishService(paramConfigVo, userId);
            }
        } catch (Exception e) {
            log.error(e.getMessage(),e);
        }
        servicePublicService.updateElementAndClassifyByMetaId(paramConfigVo.getServiceMetaId(), paramConfigVo.getClassifyId());
        return result;
    }

    @RequestMapping("/editInformationVerificationService")
    public Result editInformationVerificationService(@RequestBody ParamConfigVo paramConfigVo, HttpServletRequest request) {
        String userId = (String) request.getSession().getAttribute("userId");
        servicePublicService.checkPublishName(paramConfigVo.getServiceMetaId(), paramConfigVo.getInterfaceChineseName(), true, EnumServiceType.INFORMATION_VERFICATION.getCode(), userId);
        Result result = null;
        ServiceMeta serviceMeta = serviceMetaService.queryByMetaId(paramConfigVo.getServiceMetaId());
        String servicePublicationId = serviceMeta.getServicePublication().getId();
        paramConfigVo.setServicePublicationId(servicePublicationId);
        try {
            if ("0".equals(paramConfigVo.getSaveType())) {
                result = cicadaMetaServicePublishServiceImpl.updateVerifactionService(paramConfigVo, userId);
            } else {
                result = cicadaMetaServicePublishServiceImpl.updateNoPublishService(paramConfigVo, userId);
            }
        } catch (Exception e) {
            log.error(e.getMessage(),e);
        }
        servicePublicService.updateElementAndClassifyByMetaId(paramConfigVo.getServiceMetaId(), paramConfigVo.getClassifyId());
        return result;
    }


    @RequestMapping("/editNoPublishService")
    public Result editNoPublishService(@RequestBody ParamConfigVo paramConfigVo, HttpServletRequest request) {
        String userId = (String) request.getSession().getAttribute("userId");
        servicePublicService.checkPublishName(paramConfigVo.getServiceMetaId(), paramConfigVo.getInterfaceChineseName(), true, EnumServiceType.INQUIRY_SERVICE.getCode(), userId);
        ServiceMeta serviceMeta = serviceMetaService.queryByMetaId(paramConfigVo.getServiceMetaId());
        String servicePublicationId = serviceMeta.getServicePublication().getId();
        paramConfigVo.setServicePublicationId(servicePublicationId);
        Result result = null;
        try {
            result = cicadaMetaServicePublishServiceImpl.updateNoPublishService(paramConfigVo, userId);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
        }
        servicePublicService.updateElementAndClassifyByMetaId(paramConfigVo.getServiceMetaId(), paramConfigVo.getClassifyId());
        return result;
    }


    @GetMapping(value = "/createNewServiceTrans")
    public Result createNewServiceTrans(HttpServletRequest request, String serviceType) {
        String userId = (String) request.getSession().getAttribute("userId");
        Assert.hasLength(userId, "请先登录！");
        String transName = "新建模型_1";
        String tempTransId = this.cicadaMetaServicePublishServiceImpl.createServiceTransTemp(transName, request, serviceType);
        return Result.toResult(R.ok(tempTransId));
    }

    @GetMapping(value = "/queryTransSteps")
    public Result queryTransSteps(HttpServletRequest request, String transId) {
        String userId = (String) request.getSession().getAttribute("userId");
        Assert.hasLength(userId, "请先登录！");
        List<TransStepInputVo> transStepInputVos = this.cicadaMetaServicePublishServiceImpl.queryTransSteps(transId);
        return Result.toResult(R.ok(transStepInputVos));
    }

    @PostMapping(value = "/subscribe")
    public Result subscribe(@RequestBody Map serviceScheduleJobBean, HttpServletRequest servletRequest) throws UnsupportedEncodingException {
        checkHeard(servletRequest, serviceScheduleJobBean);
        checkParam(serviceScheduleJobBean, servletRequest);
        return serviceSubscribe(serviceScheduleJobBean, servletRequest);
    }

    @PostMapping(value = "/serviceSubscribe")
    public Result serviceSubscribe(@RequestBody Map serviceScheduleJobBean, HttpServletRequest servletRequest) throws UnsupportedEncodingException {

        serviceScheduleJobBean.put("regId", "000001000001");
        serviceScheduleJobBean.put("requester", "数据建模分析");
        serviceScheduleJobBean.put("terminalId", ClientAccesstIpUtil.getRemoteIpAddress(servletRequest));
        String serviceId = checkParam(serviceScheduleJobBean, servletRequest);
        return buildServiceBody(serviceId, serviceScheduleJobBean);
    }

    private String checkParam(Map serviceScheduleJobBean, HttpServletRequest servletRequest) {
        Map<String, String> checkParams = new HashMap<>();

        Assert.notNull(serviceScheduleJobBean.get("operatorDataSet"), "必填项operatorDataSet没有填写！");
        Assert.hasLength((String) serviceScheduleJobBean.get("url"), "必填项url没有填写！");
        checkParams.put("startTime", (String) serviceScheduleJobBean.get("startTime"));
        checkParams.put("endTime", (String) serviceScheduleJobBean.get("endTime"));


        String serviceId = serviceMetaService.queryServiceIdByPath((String) serviceScheduleJobBean.get("url"));
        Assert.hasLength(serviceId, "找不到这个url对应的服务！");
        List<ServiceParams> serviceParamsList = serviceParamsService.queryParamByOwnerId(serviceId);
        Map<String, String> params = new HashMap<>();
        for (ServiceParams serviceParam : serviceParamsList) {
            if ((Objects.equals("t", serviceParam.getIsMust())) && serviceParam.getIsOutput().equals("0")) {
                params.put(serviceParam.getCode(), serviceParam.getCode());
            }
        }
        JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(serviceScheduleJobBean.get("operatorDataSet")));
        if (jsonObject != null) {
            String tableName = jsonObject.keySet().iterator().next();
            checkParams.put(tableName, jsonObject.keySet().iterator().next());
            checkParams.put("operatorDataSet", "operatorDataSet");
            JSONArray tableDatas = (JSONArray) jsonObject.get(tableName);
            JSONObject tableData = (JSONObject) tableDatas.get(0);
            for (String key : tableData.keySet()) {
                checkParams.put(key, (String) tableData.get(key));
            }
        }
        for (Map.Entry<String, String> param : params.entrySet()) {
            Assert.hasLength(checkParams.get(param.getKey()), "必填参数：" + param.getKey() + "未填写！");
        }
        return serviceId;
    }

    private String getRemoteIpAddress() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();
        return request.getRemoteAddr();
    }

    private void checkHeard(HttpServletRequest servletRequest, Map serviceScheduleJobBean) throws UnsupportedEncodingException {
        String reg_id = servletRequest.getHeader("Reg_ID");
        String requester = servletRequest.getHeader("Requester");
        String terminal_id = servletRequest.getHeader("Terminal_ID");
        Assert.hasLength(reg_id, "请求头中必须要有Reg_ID");
        Assert.hasLength(requester, "请求头中必须要有Requester");
        Assert.hasLength(terminal_id, "请求头中必须要有Terminal_ID");
        serviceScheduleJobBean.put("regId", reg_id);
        serviceScheduleJobBean.put("requester", URLEncoder.encode(requester, "UTF-8"));
        serviceScheduleJobBean.put("terminalId", terminal_id);
    }


    @PostMapping(value = "/updateSubscribe")
    public Result updateSubscribe(@RequestBody Map serviceScheduleJobBean) {

        Assert.hasLength((String) serviceScheduleJobBean.get("requestId"), "必填项requestId没有填写！");
        // Assert.hasLength((String) serviceScheduleJobBean.get("operatorDataSet"),"必填项operatorDataSet没有填写！");
        Assert.hasLength((String) serviceScheduleJobBean.get("endTime"), "必填项endTime没有填写！");


        MLSQLScheduleClient service = RetrofitManager.scheduleClient(scheduleServer);
        Response<Result> response = Response.success(null);
        try {
            response = service.updateSubscribe(serviceScheduleJobBean).execute();
            if (String.valueOf(response.code()).startsWith("4")) {
                Assert.fail("未找到调度中心, 请检查配置文件！");
            }
            if (String.valueOf(response.code()).startsWith("5")) {
                Assert.fail("调度中心异常，具体错误插件调度中心日志文件!");
            }
            return response.body();
        } catch (IOException e) {
            log.error(e.getMessage(),e);
        }
        return response.body();

    }

    @PostMapping(value = "/removeSubscribe")
    public Result removeSubscribe(@RequestBody Map serviceScheduleJobBean) {
        Assert.hasLength((String) serviceScheduleJobBean.get("requestId"), "必填项requestId没有填写！");
        MLSQLScheduleClient service = RetrofitManager.scheduleClient(scheduleServer);
        Response<Result> response = Response.success(null);
        try {
            response = service.removeSubscribe(serviceScheduleJobBean).execute();
            if (String.valueOf(response.code()).startsWith("4")) {
                Assert.fail("未找到调度中心, 请检查配置文件！");
            }
            if (String.valueOf(response.code()).startsWith("5")) {
                Assert.fail("调度中心异常，具体错误插件调度中心日志文件!");
            }
            return response.body();
        } catch (IOException e) {
            log.error(e.getMessage(),e);
        }
        return response.body();
    }

    private Result buildServiceBody(String serviceId, Map serviceScheduleJobBean) {
        MLSQLScheduleClient service = RetrofitManager.scheduleClient(scheduleServer);
        Response<Result> response = Response.success(null);
        try {
            response = service.subscribe(serviceScheduleJobBean).execute();
            if (String.valueOf(response.code()).startsWith("4")) {
                Assert.fail("未找到调度中心, 请检查配置文件！");
            }
            if (String.valueOf(response.code()).startsWith("5")) {
                Assert.fail("调度中心异常，具体错误插件调度中心日志文件!");
            }
            Map map = (Map) response.body().getData();
            iAsyncServiceRunLogService.saveLog(serviceId, (String) map.get("requestId"));
            return response.body();
        } catch (IOException e) {
            log.error(e.getMessage(),e);
            return response.body();

        }
    }

    @PostMapping("/getServiceToken")
//    @FuncScanAnnotation(code = "getServiceToken", name = "获取服务token", parentCode = "serviceManagement")
//    @ValidateAndLogAnnotation
    @ApiOperation(value = "获取服务token")
    public Result getServiceToken(HttpServletRequest request) {
        String userId = (String) request.getSession().getAttribute("userId");
        Assert.hasLength(userId, "请先登录！");
        Map<String, String> params = new HashMap<>();
        params.put("id", userId);
        TSysAuthUser tSysAuthUser = (TSysAuthUser) sysAuthUserService.query(params);
        Assert.notNull(tSysAuthUser, "用户不存在");

        ServicePublishClient.ServiceTokenVo serviceTokenVo = new ServicePublishClient.ServiceTokenVo();
        serviceTokenVo.setUserId(userId);
        serviceTokenVo.setUserName(tSysAuthUser.getObjName());
        serviceTokenVo.setRememberMe(true);
        Result result = servicePublishClient.getServiceToken(serviceTokenVo);

        return result;
    }
}
