package com.dragonsoft.cicada.datacenter.modules.datavisual.controller;

import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.code.common.utils.R;
import com.code.common.utils.StringUtils;
import com.dragoninfo.dfw.bean.Result;
import com.dragonsoft.cicada.datacenter.modules.datavisual.businessrelation.BusinessRelationEnum;
import com.dragonsoft.cicada.datacenter.modules.datavisual.datapolishing.DateGranularityEnum;
import com.dragonsoft.cicada.datacenter.modules.datavisual.function.FunctionManager;
import com.dragonsoft.cicada.datacenter.modules.datavisual.model.TableChartWidget;
import com.dragonsoft.cicada.datacenter.modules.datavisual.widget.IChartWidgetBuilder;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.service.MbService;
import com.fw.tenon.excel.TenonExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@CrossOrigin
@RestController
@RequestMapping("/widget")
@Slf4j
public class WidgetController {
    @Autowired
    IChartWidgetBuilder chartWidgetBuilder;
    @Autowired
    MbService mbService;

    @Autowired
    TableChartWidget tableChartWidget;

    @Value("${fileOutput.limit.size:10}")
    private String limitSize;

    @GetMapping("/getDownloadSize")
    public Result getDownloadSize() {
        Map map = new HashMap();
        map.put("limitSize", limitSize);
        return Result.toResult(R.ok(map));
    }

    @PostMapping("/loading")
    public Result loadingData(@RequestBody Map map) {
        String code = (String) map.get("code");
        String data = JSON.toJSONString(map.get("data"));
        boolean type = (boolean) map.get("type");
        Map<String, Object> ob = chartWidgetBuilder.loadingData(code, data, type);
        int c = (int) ob.get("code");
        if (c == -1) {
//            return Result.toResult(R.error("暂无数据"));
            return Result.success();
        } else if (c == -2) {
            return Result.toResult(R.error((String) ob.get("msg")));
        }
        return Result.toResult(R.ok(ob.get("data")));
    }


    @PostMapping("/getSQL")
    public Result getSQL(@RequestBody Map map) {
        String code = (String) map.get("code");
        String data = (String) map.get("data");
        boolean type = (boolean) map.get("type");
        String ob = chartWidgetBuilder.getSQL(code, data, type);
        ob = ob.replaceAll("`", "\"");
        return Result.toResult(R.ok(ob));
    }

    /**
     * 获取所有函数
     *
     * @return
     */
    @RequestMapping("/getFunctionList")
    public Result getFunctionList() {
        FunctionManager functionManager = new FunctionManager();
        return Result.toResult(R.ok(functionManager.getFunctionList()));
    }

    /**
     * 下载excel数据
     *
     * @return
     */
    @RequestMapping("/downloadExcel")
    public Result downloadExcel(HttpServletResponse response, @RequestBody Map map) {

        String code = (String) map.get("code");
        String downLoadSize = (String) map.get("downLoadSize");
        String downLoadName = (String) map.get("downLoadName");
        String data = JSON.toJSONString(map.get("data"));
        int limit = Integer.parseInt(limitSize);
        JSONObject jsonObject = JSONObject.parseObject(data);
        if(Integer.parseInt(limitSize) > 0)jsonObject.put("pageSize",downLoadSize);
        jsonObject.put("index",1);

        if(Integer.parseInt(downLoadSize) < Integer.parseInt(limitSize) ){
            jsonObject.put("pageSize",downLoadSize);
        }else jsonObject.put("pageSize",limitSize);

        String requestData = JSONObject.toJSONString(jsonObject);
        boolean type = (boolean) map.get("type");

        Map<String, Object> objectMap = chartWidgetBuilder.loadingData(code, requestData, type);
        Map resultMap = (Map) objectMap.get("data");
        List<Map> resultData = (List<Map>) resultMap.get("data");
        ExcelWriter excelWriter = TenonExcelUtil.getWriter(true);
        List<TableChartWidget.HeadData> headDataMap = (List<TableChartWidget.HeadData>) resultMap.get("head");
        for(TableChartWidget.HeadData headData : headDataMap){
            excelWriter.addHeaderAlias(headData.getProp(),headData.getLabel());
        }
        try {
            TenonExcelUtil.writeDataToExcelMaxContent(response, StringUtils.isNotBlank(downLoadName) ? downLoadName : (String) jsonObject.get("title"),resultData,excelWriter);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
        }
        return Result.toResult(R.ok());
    }


    /**
     * 获取同行类型和同行关系
     *
     * @return
     */
    @RequestMapping("/getRelationTypes")
    public Result getRelationType() {
        Map<String, List> res = new HashMap<>();
        //获取关系类型
        res.put("relationType", getRelationTypes());
        //获取同行类型
        res.put("peerType", getPeerType("RYTGXLX"));
        return Result.success(res);
    }

    private List<String> getPeerType(String mbCode) {
        return mbService.getPeerTypesMb(mbCode);
    }

    private List<Map> getRelationTypes() {
        BusinessRelationEnum[] values = BusinessRelationEnum.values();
        List<Map> resPeerType = new ArrayList<>();
        for (BusinessRelationEnum value : values) {
            Map<String, String> resMap = new HashMap<>();
            resMap.put("name", value.getBusinessRelationType());
            resMap.put("code", value.getCode());
            resPeerType.add(resMap);
        }
        return resPeerType;
    }

    /**
     * 获取时间颗粒度
     *
     * @return
     */
    @RequestMapping("/getDateGranularity")
    public Result getDateGranularity() {
        return Result.success(DateGranularityEnum.getAllDateGranularity());
    }
}
