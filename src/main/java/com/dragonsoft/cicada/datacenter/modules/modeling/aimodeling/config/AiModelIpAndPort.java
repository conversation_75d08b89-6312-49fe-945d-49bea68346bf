package com.dragonsoft.cicada.datacenter.modules.modeling.aimodeling.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "ai.model")
public class AiModelIpAndPort {

    private String initBuildServiceIp;
    private String startAndSaveServiceIp;
    private String runScriptServiceIp;
    private String evaluateRstIp;
    private String runOpServiceIp;
    private String serviceReleaseIp;
}
