package com.dragonsoft.cicada.datacenter.modules.dataplan.externaldatasources.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.code.common.encrypt.DragonEncryptor;
import com.code.common.mist.plugin.config.service.impl.PluginConfigService;
import com.code.common.paging.PageInfo;
import com.code.common.utils.R;
import com.code.common.utils.StringUtils;
import com.code.common.utils.assertion.Assert;
import com.code.dragonsoft.dataquery.service.QueryDataService;
import com.code.dragonsoft.dataquery.service.querymodel.ColumnDataModel;
import com.code.dragonsoft.dataquery.util.ChangeColumnNameUtil;
import com.code.metadata.base.softwaredeployment.DeployedSoftware;
import com.code.metadata.res.common.ClassifierStat;
import com.code.metadata.res.ddl.LogicDataObj;
import com.code.metadata.res.structured.rdb.RdbCatalog;
import com.code.metadata.res.structured.rdb.RdbSchema;
import com.code.metaservice.base.softwaredeployment.DeployedSoftwareService;
import com.code.metaservice.core.ClassifierService;
import com.code.metaservice.datawarehouse.IDataWareTreeService;
import com.code.metaservice.datawarehouse.IDataWarehouseService;
import com.code.metaservice.datawarehouse.model.DataSourceDTO;
import com.code.metaservice.datawarehouse.model.DataSourceVO;
import com.code.metaservice.datawarehouse.model.DatasetTableModel;
import com.code.metaservice.datawarehouse.model.DatasetTreeModel;
import com.code.metaservice.ddl.ILogicDataObjService;
import com.code.metaservice.variable.ITransVariableService;
import com.code.thirdplugin.cicada.sql.page.page.service.CicadaStandardSqlOutputService;
import com.dragoninfo.dfw.bean.Result;
import com.dragoninfo.dfw.entity.*;
import com.dragoninfo.dfw.service.*;
import com.dragonsoft.cicada.datacenter.common.config.PreviewErrorException;
import com.dragonsoft.cicada.datacenter.common.token.GlobalConstant;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.common.UserConstant;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.service.IDataSetOperationService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.externaldatasources.dto.SearchFactNameDTO;
import com.dragonsoft.cicada.datacenter.modules.dataplan.externaldatasources.service.BusiDirTreeEditorService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.externaldatasources.service.IDataSetAuthService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.externaldatasources.service.IDataWarehousePlanService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.externaldatasources.vo.datawarehouse.*;
import com.dragonsoft.cicada.datacenter.modules.metadata.service.DataBaseConnectService;
import com.dragonsoft.cicada.datacenter.modules.metadata.service.DataBaseFactory;
import com.dragonsoft.cicada.datacenter.modules.metadata.service.MetadataService;
import com.dragonsoft.cicada.datacenter.modules.metadata.vo.ChangeColumnVO;
import com.dragonsoft.cicada.datacenter.modules.metadata.vo.SoftwareVo;
import com.dragonsoft.cicada.datacenter.modules.modeling.qo.AnalysisResultLibraryQO;
import com.dragonsoft.cicada.datacenter.modules.modeling.vo.AnalysisResultLibraryVO;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.service.IRoleService;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.service.IUserService;
import com.fw.service.BaseService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.text.Collator;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by leo on 2019/3/14.
 * 数据仓库管理
 */
@CrossOrigin
@RestController
@Slf4j
@RequestMapping(value = "/manage/dataWarehouse")
@PropertySource("classpath:case-config.properties")
//@FuncScanAnnotation(code = "dataWarehouse", name = "数据源管理", parentCode = "dataWarehousePlan")
public class DataWarehouseController {

    @Autowired
    private IDataWarehousePlanService dataWarehouseService;

    @Autowired
    private IDataWarehouseService dataWarehouseMetaService;

    @Autowired
    private BusiDirTreeEditorService busiDirTreeEditorService;

    @Autowired
    private IDataWareTreeService dataWareTreeService;

    @Autowired
    private DeployedSoftwareService deployedSoftwareService;

    @Autowired
    private QueryDataService queryDataService;

    @Autowired
    private IDataSetAuthService dataSetAuthService;

    @Autowired
    private SysAuthObjFuncService sysAuthObjFuncService;

    @Autowired
    private SysAuthObjRelService sysAuthObjRelService;

    @Autowired
    private SysAuthObjService sysAuthObjService;

    @Autowired
    private SysFuncService sysFuncService;

    @Autowired
    private IRoleService roleService;

    @Autowired
    private IUserService userService;

    @Autowired
    private BaseService baseService;
    @Autowired
    private IDataSetOperationService operationService;
    @Autowired
    private ClassifierService classifierService;

    @Autowired
    private MetadataService metadataService;

    @Autowired
    private ILogicDataObjService logicDataObjService;

    @Autowired
    private CicadaStandardSqlOutputService standardSqlOutputService;

    @Autowired
    ITransVariableService transVariableService;

    @Autowired
    PluginConfigService pluginConfigService;

    @Autowired
    private IDataSetOperationService dataSetOperationService;

    private DragonEncryptor dragonEncryptor=new DragonEncryptor();
    @Autowired
    private DataBaseFactory dataBaseFactory;

    @Autowired
    private ValidateAndLogService validateAndLogService;

    @Autowired
    private SysAuthUserService sysAuthUserService;

    private static final List<String> JDBC_TYPE = Arrays.asList("POSTGRESQL", "GREENPLUM", "ORACLE", "hbase", "hwmpp", "Hive");



    @Value("${sceModel}")
    private boolean sceModel;
    /**
     * 读取树结构信息
     */
    @GetMapping(value = "/queryDirTree")
    public Result queryDirTree(String id, String pId, String keyword, HttpServletRequest request) {
        String userId = (String) request.getSession().getAttribute("userId");
        List<TSysAuthRole> roleList = (List<TSysAuthRole>) request.getSession().getAttribute("roles");
        if (StringUtils.isNotBlank(id)) {
            return Result.toResult(R.ok());
        }
        List<DatasetTreeModel> tree = dataWareTreeService.getDataWarehouseTreeCopy(keyword, userId);
        tree = tree.stream().sorted(Comparator.comparing(DatasetTreeModel::getName, Collator.getInstance(Locale.CHINA))).collect(Collectors.toList());
        if(roleList==null){
            return Result.toResult(R.ok(tree));
        }
        if (!sceModel){
            for (int i = 0; i < tree.size(); i++) {
                if ("场景案例".equals(tree.get(i).getName())){
                    tree.remove(i);
                    break;
                }
            }
        }
        return Result.toResult(R.ok(this.isHaveDataObjAuth(roleList) ? tree : this.isHaveAuth(tree, userId, roleList)));
    }

    /**
     * 3.0读取数据源列表
     * */
    @RequestMapping("/queryDirTreeChidren")
    public Result queryDirTreeChidren(@RequestBody Map dataMap) {
        int pageSize = Integer.parseInt(dataMap.get("pageSize").toString());
        int pageIndex = Integer.parseInt(dataMap.get("pageIndex").toString());
        String id = dataMap.get("id").toString();
        String name = dataMap.get("name").toString();
        String instanceType = dataMap.get("instanceType").toString();
        String sortField = dataMap.get("sortField").toString();
        String sortOrder = dataMap.get("sortOrder").toString();
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageIndex(pageIndex);
        pageInfo.setPageSize(pageSize);
        pageInfo.setSortField(sortField);
        pageInfo.setSortDirect(sortOrder);
        PageInfo dataSet = dataWareTreeService.queryDataTable(pageInfo, id, name, instanceType);
        //赋值连接状态
        List<Map<String,Object>> dataList= dataSet.getDataList();
        boolean connectStatus = false;
        if(CollectionUtil.isNotEmpty(dataList)){
            for (Map<String, Object> l : dataList) {
                if (Objects.nonNull(l.get("catalogId"))) {
                    connectStatus = connectStatus(l.get("catalogId").toString());
                }
                l.put("connectStatus",connectStatus);
            }
        }
        return Result.success(dataSet);
    }

    public boolean connectStatus(String instanceId){
        DataSourceDTO dsVo = metadataService.findDataSourceById(instanceId);
        List<SoftwareVo> softwareVersions = metadataService.findSoftwareVersions(dsVo.getDbType());
        DataSourceVO dataSourceVO = new DataSourceVO();
        dataSourceVO.setDbCode(dsVo.getDbCode());
        dataSourceVO.setDbName(dsVo.getDbName());
        dataSourceVO.setDbType(dsVo.getDbType());
        dataSourceVO.setIp(dsVo.getIp());
        dataSourceVO.setPort(dsVo.getPort());
        SoftwareVo softwareVo = softwareVersions.stream().filter(item -> dsVo.getVersion().equals(item.getVersion())).collect(Collectors.toList()).get(0);
        dataSourceVO.setSoftwareId(softwareVo.getId());
        dataSourceVO.setZookeeperId(dsVo.getZookeeperId());
        dataSourceVO.setZookeeperVersion(dsVo.getZookeeperVersion());
        if (isToEncrypt(dsVo.getDbType())) {
            dataSourceVO.setPassword(dragonEncryptor.encrypt(dsVo.getPassword()));
        } else {
            dataSourceVO.setPassword(dsVo.getPassword());
        }
        dataSourceVO.setUsername(dsVo.getUsername());
        dataSourceVO.setConnType(JDBC_TYPE.contains(dsVo.getDbType()) ? "jdbc" : "");
        DataBaseConnectService connectService = dataBaseFactory.matchDb(dataSourceVO.getDbType()); //根据type创造不同工厂
        boolean connectionStatus = false;
        try {
            connectionStatus = connectService.testConnection(dataSourceVO);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
        }
        return connectionStatus;
    }

    private boolean isToEncrypt(String dbType) {
        if (dbType.equalsIgnoreCase("REDIS")
                || dbType.equalsIgnoreCase("KAFKA")
        ) {
            return true;
        }
        return false;
    }

    @RequestMapping("/queryAllTypes")
    public Result queryAllTypes() {
        return Result.success(dataWareTreeService.queryAllTypes());
    }

    private List<DatasetTreeModel> isHaveAuth(List<DatasetTreeModel> tree, String userId, List<TSysAuthRole> roleList) {
        List<DatasetTreeModel> copyTree = new ArrayList<>();
        for (DatasetTreeModel datasetTreeModel : tree) {
            if (null != datasetTreeModel.getChildren() && 3 != datasetTreeModel.getLevel()) {
                datasetTreeModel.setChildren(this.isHaveAuth(datasetTreeModel.getChildren(), userId, roleList));
            }

            if (3 == datasetTreeModel.getLevel()) {
                Boolean flag = false;
                Map<String, String> params = Maps.newHashMap();
                params.put("obj_id", userId);
                params.put("func_code", datasetTreeModel.getId());
                TSysAuthObjFunc tSysAuthObjFunc = (TSysAuthObjFunc) sysAuthObjFuncService.query(params);
                if (null != tSysAuthObjFunc) {
                    flag = true;
                }
                List<DatasetTableModel> datasetTableModels = dataWareTreeService.queryDataBaseTableAll(datasetTreeModel.getId());
                for (DatasetTableModel datasetTableModel : datasetTableModels) {
                    //当前用户有此数据源关系 或者此数据源下有授权对象时
                    if (dataSetAuthService.IsAuthDataset(datasetTableModel.getId(), userId, roleList)) {
                        flag = true;
                    }
                }
                if (!flag) {
                    copyTree.add(datasetTreeModel);
                }
            }
        }
        List<String> copyTreeId = copyTree.stream().map(DatasetTreeModel::getId).collect(Collectors.toList());
        return tree.stream().filter(s -> !copyTreeId.contains(s.getId())).collect(Collectors.toList());
    }

    /**
     * 跳转到新增子数据仓库数据库实例页面
     *
     * @param
     * @return
     */
    @GetMapping(value = "/addDataObject")
    public Result addDataObject(String softwares) {
        return Result.toResult(R.ok(this.getResTree(softwares, "")));
    }

    /**
     * 保存“数据仓库的数据库对象”
     *
     * @param vo
     * @return
     */
    @PostMapping(value = "/saveDWDBInstance")
//    @ValidateAndLogAnnotation
    public Result saveDWDBInstance(String classifyId,
                                   String busiDirId,
                                   String source,
                                   @RequestBody DataWarehouseVo vo,
                                   @RequestParam(required = false, defaultValue = "false") boolean isAddExistDataSource, HttpServletRequest request) {

        String userId = (String) request.getSession().getAttribute("userId");
        int count = dataWarehouseService.checkDataSourceName(classifyId, vo.getDataSourceName());
        DatasetTreeModel children = new DatasetTreeModel();

        if (isAddExistDataSource) {
            Assert.isZero(count, "数据源名称重复，请重新输入！");
        } else if(count > 0) {
            vo.setDataSourceName(vo.getDataSourceName() + "_new");
            children.setMsg("数据源名称存在同名，重命名为[" + vo.getDataSourceName() + "]");
        }

        String instanceId = dataWarehouseService.addDataWarehouseInstance(vo, classifyId, busiDirId, userId, source);
        //对象id存在，执行修改
        if (StringUtils.isNotBlank(vo.getId())) {
            dataWarehouseService.editDWRelationDb(vo);
        } else {
            dataWarehouseService.addDWRelationDb(vo, instanceId);
            dataWarehouseService.addTreeNode(instanceId, classifyId);
        }
        children.setId(instanceId);
        children.setName(vo.getDataSourceName());
        children.setCode(vo.getDataSourceName());
        children.setExtendedType(vo.getDbType());
        children.setOpen(false);
        children.setIsParent(false);
        children.setInstanceType(vo.getDomainDbType());
        return Result.toResult(R.ok(children));
    }

    @PostMapping(value = "/copyDWDBInstance")
    public Result copyDWDBInstance(String classifyId, String busiDirId, String dbId, HttpServletRequest request) {
        String userId = (String) request.getSession().getAttribute("userId");
        DataWarehouseVo dataWarehouseVo = dataWarehouseService.queryDBInstanceById(dbId);
        String instanceId = dataWarehouseService.addDataWarehouseInstance(dataWarehouseVo, classifyId, busiDirId, userId);
        return Result.toResult(R.ok());
    }


    /**
     * 删除数据仓库数据库实例
     *
     * @param elementId
     * @param classifyId
     * @return
     */
    @GetMapping(value = "/deleteDWDBInstance")
//    @FuncScanAnnotation(code = "dataWarehouseDeleteDWDBInstance", name = "删除", parentCode = "dataWarehouse")
//    @ValidateAndLogAnnotation
    public Result deleteDWDBInstance(String elementId, String classifyId) {

        if (dataWarehouseService.checkTable(elementId)) {
            return Result.toResult(R.error("该数据源存在数据对象，无法删除"));
        }
        boolean deletePhysics = dataWarehouseService.deleteDataWarehouseDBInstance(elementId, classifyId);




        return Result.success(deletePhysics);
    }


    /**
     * 删除子数据仓库
     *
     * @param classifyId
     * @return
     */
    @GetMapping(value = "/deleteDwInstance")
    public Result deleteDwInstance(String classifyId, String type) {
        if (dataWarehouseService.isDbInstanceExist(classifyId)) {
            if ("deleteFile".equalsIgnoreCase(type)) {
                return Result.toResult(R.error("层次下还有数据源，不允许删除"));
            } else {
                return Result.toResult(R.error("仓库下还有层次，不允许删除"));
            }
        } else {
            dataWarehouseService.deleteDwInstance(classifyId);
            return Result.toResult(R.ok("success"));
        }
    }

    /**
     * 查看数据仓库数据库实例
     *
     * @param elementId
     * @param model
     * @return
     */
    @GetMapping(value = "/lookDWDBInstance")
    public Result editDataWarehouse(String elementId, Model model) {
        JSONObject jsonObject = dataWarehouseService.getDWDBInstanceForLook(elementId);
        Map<String, Object> map = new HashMap<>();
        map.put("data", jsonObject.get("data"));
        map.put("instance", jsonObject.get("instance"));
        return Result.toResult(R.ok(map));
    }

    /**
     * 添加子数据仓库节点
     *
     * @return
     */
    @PostMapping(value = "/addTreeNode")
    public Result addTreeNode(@RequestBody Map requestMap, HttpServletRequest request) {
        String pId = requestMap.get("pId").toString();
        String dirName = requestMap.get("dirName").toString();
        String dirCode = requestMap.get("dirCode").toString();
        String userId = (String) request.getSession().getAttribute("userId");
        return dataWarehouseService.addClassifyTreeNode(pId, dirName, dirCode, userId);
    }

    /**
     * 数据仓库重命名
     *
     * @return
     */
    @PostMapping(value = "/renameDWInstance")
    public Result renameDWInstance(@RequestBody Map resMap) {
        String classifyId = resMap.get("classifyId").toString();
        String classifyName = resMap.get("classifyName").toString();
        String type = resMap.get("type").toString();
        String id = resMap.get("id").toString();
        String dirId = resMap.get("dirId").toString();
        String res = dataWarehouseService.renameDWInstance(classifyId, classifyName, type, id, dirId);
        return Result.toResult(R.ok(res));
    }

    /**
     * 查询出库实例中的表
     *
     * @param dataMap
     * @return
     */
    @RequestMapping("/queryDataBaseTable")
    public Result queryDataBaseTable(@RequestBody Map dataMap, HttpServletRequest request) {
        String userId = (String) request.getSession().getAttribute("userId");
        List<TSysAuthRole> roleList = (List<TSysAuthRole>) request.getSession().getAttribute("roles");
        List<String> authIds = roleList.stream().map(TSysAuthObj::getId).collect(Collectors.toList());
        authIds.add(userId);

        int pageSize = Integer.parseInt(dataMap.get("pageSize").toString());
        int pageIndex = Integer.parseInt(dataMap.get("pageIndex").toString());
        String name = dataMap.get("name").toString();
        String sortField = dataMap.get("sortField").toString();
        String sortOrder = dataMap.get("sortOrder").toString();
        String dwDbId = dataMap.get("dwDbId").toString();
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageIndex(pageIndex);
        pageInfo.setPageSize(pageSize);
        pageInfo.setSortField(sortField);
        pageInfo.setSortDirect(sortOrder);
        PageInfo dataSet = dataWareTreeService.queryDataBaseTable(pageInfo, name, sortField, sortOrder, dwDbId, false, authIds);
        List<DatasetTableModel> datasetTableModels = dataSet.getDataList();
        for (DatasetTableModel datasetTableModel : datasetTableModels) {
            datasetTableModel.setIsFast(dataWarehouseService.getIsFast(datasetTableModel.getId()));
        }
        dataSet.setDataList(this.getDatasetTableModels(userId, datasetTableModels));
        return Result.toResult(R.ok(dataSet));
    }

    /**
     * 查询出库实例中的表
     *
     * @param dataMap
     * @return
     */
    @RequestMapping("/queryDatasetBaseTable")
    public Result queryDatasetBaseTable(@RequestBody Map dataMap, HttpServletRequest request) {
        String userId = (String) request.getSession().getAttribute("userId");
        /*List<TSysAuthRole> roleList = (List<TSysAuthRole>) request.getSession().getAttribute("roles");
        List<String> authIds = roleList.stream().map(TSysAuthObj::getId).collect(Collectors.toList());
        authIds.add(userId);*/

        int pageSize = Integer.parseInt(dataMap.get("pageSize").toString());
        int pageIndex = Integer.parseInt(dataMap.get("pageIndex").toString());
        String name = dataMap.get("name").toString();
        String sortField = dataMap.get("sortField").toString();
        String sortOrder = dataMap.get("sortOrder").toString();
        String dwDbId = dataMap.get("dwDbId").toString();
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageIndex(pageIndex);
        pageInfo.setPageSize(pageSize);
        pageInfo.setSortField(sortField);
        pageInfo.setSortDirect(sortOrder);
        PageInfo dataSet = dataWareTreeService.queryDataBaseTable(pageInfo, name, dwDbId, userId);
        return Result.toResult(R.ok(dataSet));
    }

    @RequestMapping("/queryDatasetBaseTableC")
    public Result queryDatasetBaseTableC(@RequestBody Map dataMap, HttpServletRequest request) {
        String userId = (String) request.getSession().getAttribute("userId");
        int pageSize = Integer.parseInt(dataMap.get("pageSize").toString());
        int pageIndex = Integer.parseInt(dataMap.get("pageIndex").toString());
        String name = dataMap.get("name").toString();
        String sortField = dataMap.get("sortField").toString();
        String sortOrder = dataMap.get("sortOrder").toString();
        String dwDbId = dataMap.get("dwDbId").toString();
        String dbType = dataMap.get("dbType").toString();
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageIndex(pageIndex);
        pageInfo.setPageSize(pageSize);
        pageInfo.setSortField(sortField);
        pageInfo.setSortDirect(sortOrder);
        PageInfo dataSet = dataWareTreeService.queryDataBaseTableC(pageInfo, name, dwDbId, userId, dbType);
        return Result.toResult(R.ok(dataSet));
    }


    @RequestMapping("/queryDataBaseTableAll")
    public Result queryDataBaseTableAll(String dwId, HttpServletRequest request) {
        List<DatasetTableModel> datasetTableModels = dataWareTreeService.queryDataBaseTableAll(dwId);
        String userId = (String) request.getSession().getAttribute("userId");
        for (DatasetTableModel datasetTableModel : datasetTableModels) {
            datasetTableModel.setIsFast(dataWarehouseService.getIsFast(datasetTableModel.getId()));
        }
        return Result.toResult(R.ok(this.getDatasetTableModels(userId, datasetTableModels)));
    }


    private List<DatasetTableModel> getDatasetTableModels(String userId, List<DatasetTableModel> datasetTableModels) {
        List<String> tableId = new ArrayList<>();
        //用户-数据
        Map<String, String> userParams = Maps.newHashMap();
        userParams.put("OBJ_ID", userId.toString());
        List<TSysAuthObjFunc> tSysAuthObjFuncs = sysAuthObjFuncService.queryList(userParams);
        List<String> tSysAuthObjFuncsId = tSysAuthObjFuncs.stream().map(s -> s.gettSysFuncBase().getFuncCode()).collect(Collectors.toList());
        tableId.addAll(tSysAuthObjFuncsId);

        //用户-角色-数据
        Map<String, String> roleParams = Maps.newHashMap();
        roleParams.put("from_obj_id", userId);
        roleParams.put("RELATION_TYPE", "1");
        List<TSysAuthObjRel> sysAuthObjRels = sysAuthObjRelService.queryList(roleParams);
        for (TSysAuthObjRel tSysAuthObjRel : sysAuthObjRels) {
            Map<String, String> roleDataParams = Maps.newHashMap();
            roleDataParams.put("OBJ_ID", tSysAuthObjRel.getToAuthObj().getId());
            List<TSysAuthObjFunc> roleDataTSysAuthObjFuncs = sysAuthObjFuncService.queryList(roleDataParams);
            List<TSysFuncBase> tSysFuncBases = roleDataTSysAuthObjFuncs.stream().filter(s -> s.gettSysFuncBase().getFuncType().equals("1")).map(t -> t.gettSysFuncBase()).collect(Collectors.toList());
            List<String> roleDataTSysAuthObjFuncsId = tSysFuncBases.stream().map(s -> s.getFuncCode()).collect(Collectors.toList());
            tableId.addAll(roleDataTSysAuthObjFuncsId);
        }
        return datasetTableModels.stream().filter(s -> tableId.contains(s.getId())).collect(Collectors.toList());
    }

    @RequestMapping("/queryDataBaseTableAllByUserOrRole")
    public Result queryDataBaseTableAllByUserOrRole(@RequestBody Map dataMap, HttpServletRequest request) {
        String dwId = (String) dataMap.get("dwId");
        List<String> ids = (List<String>) dataMap.get("ids");
        List<TSysAuthRole> roleList = (List<TSysAuthRole>) request.getSession().getAttribute("roles");
        String userId = (String) request.getSession().getAttribute("userId");
        List<DatasetTableModel> datasetTableModels = dataWareTreeService.queryDataBaseTableAll(dwId);
        for (DatasetTableModel datasetTableModel : datasetTableModels) {
//            datasetTableModel.setAuth(dataSetAuthService.IsAuthDataset(datasetTableModel.getId(), userId, roleList));
            datasetTableModel.setIsFast(dataWarehouseService.getIsFast(datasetTableModel.getId()));
        }
        List<String> tableId = new ArrayList<>();
        for (String id : ids) {
            Map<String, String> params = Maps.newHashMap();
            params.put("obj_id", id);
            List<TSysAuthObjFunc> tSysAuthObjFuncs = sysAuthObjFuncService.queryList(params);
            List<String> tSysAuthObjFuncsId = new ArrayList<>();
            if (null != tSysAuthObjFuncs && 0 != tSysAuthObjFuncs.size()) {
                tSysAuthObjFuncsId = tSysAuthObjFuncs.stream().map(s -> s.gettSysFuncBase().getFuncCode()).collect(Collectors.toList());
            }
            tableId.addAll(tSysAuthObjFuncsId);
        }
        List<DatasetTableModel> datasetTableModelList = datasetTableModels.stream().filter(s -> tableId.contains(s.getId())).collect(Collectors.toList());
        return Result.toResult(R.ok(datasetTableModelList));
    }


    @PostMapping(value = "/saveDWLevel")
    public Result saveDWLevel(@RequestBody Map requestMap) {
        String levelName = requestMap.get("levelName").toString();
        String levelCode = requestMap.get("levelCode").toString();
        String pId = requestMap.get("pId").toString();
        return dataWarehouseService.saveDWLevel(pId, levelName, levelCode);
    }

    /**
     * 数仓管理左边树,业务库右键添加业务库后保存业务库
     *
     * @param busiId
     * @param dbType
     * @param catalogId
     * @param catalogName
     * @param selectId
     * @param selectName
     * @param levelNum
     * @param customName
     * @return
     */
    @GetMapping(value = "/saveBusi")
    public Result saveBusi(String busiId, String dbType, String catalogId, String catalogName, String selectId, String selectName, int levelNum, String customName) {

        DatasetTreeModel datasetTreeModel = dataWarehouseService.saveBusi(busiId, dbType, catalogId, catalogName, selectId, selectName, levelNum, customName);
        if (datasetTreeModel == null) {
            return Result.toResult(R.error("已存在该业务库实例"));
        } else {
            return Result.toResult(R.ok(datasetTreeModel));
        }
    }

    /**
     * 数仓管理左边树,业务库右键删除业务库
     *
     * @param dwBusiId
     * @param parentIds
     * @param dirId
     * @return
     */
    @GetMapping(value = "/deleteDwBusi")
    public Result deleteDwBusi(String dwBusiId, String[] parentIds, String dirId) {
        dataWarehouseService.deleteBusi(dwBusiId, parentIds, dirId);
        return Result.toResult(R.ok());
    }

    /**
     * 获取添加业务库时的数据库
     *
     * @param softwares
     * @param keyWord
     * @return
     */
    @GetMapping(value = "/getResTree")
    public Set<BusiDirTreeVO> getResTree(String softwares, String keyWord) {
        List<Map> list = busiDirTreeEditorService.getResTree(softwares == null || org.apache.commons.lang.StringUtils.isEmpty(softwares) ? null : Arrays.asList(softwares.split(",")), keyWord);
        Set<BusiDirTreeVO> vos = new TreeSet<BusiDirTreeVO>();
        for (Map m : list) {
            String schemaId = (String) m.get("schemaId");
            BusiDirTreeVO v1 = new BusiDirTreeVO();
            v1.setId(String.valueOf(m.get("softwareId")));
            v1.setName(String.valueOf(m.get("softwareCode")));
            vos.add(v1);

            String catalogId = (String) m.get("catalogId");
            if (org.apache.commons.lang.StringUtils.isNotBlank(catalogId)) {
                BusiDirTreeVO v2 = new BusiDirTreeVO();
                v2.setId(catalogId);
                v2.setName(String.valueOf(m.get("catalogCode")));
                v2.setpId(String.valueOf(m.get("catalogOwner")));
                v2.setCnName(String.valueOf(m.get("catalogName")));
                vos.add(v2);
            }
            if (StringUtils.isNotBlank(schemaId)) {
                BusiDirTreeVO v3 = new BusiDirTreeVO();
                v3.setId(schemaId);
                Object schemaCode = m.get("schemaCode");
                String code = "";
                if (null == schemaCode) {
                    v3.setName(String.valueOf(m.get("schemaName")));
                } else {
                    if (org.apache.commons.lang.StringUtils.isBlank(schemaCode.toString().trim())) {
                        v3.setName(String.valueOf(m.get("schemaName")));
                    } else {
                        v3.setName(schemaCode.toString());
                    }
                }
                v3.setpId(String.valueOf(m.get("schemeOwner")));
                vos.add(v3);
            }
            String hbaseId = (String) m.get("hbaseId");
            if (org.apache.commons.lang.StringUtils.isNotBlank(hbaseId)) {
                BusiDirTreeVO v4 = new BusiDirTreeVO();
                v4.setId(hbaseId);
                v4.setName(String.valueOf(m.get("hbaseCode")));
                v4.setCnName(String.valueOf(m.get("hbaseName")));
                v4.setpId(String.valueOf(m.get("hbaseOwner")));
                vos.add(v4);
            }

        }
        for (BusiDirTreeVO vo : vos) {
            if (vo.getpId() == null && "hwmpp".equals(vo.getName())) {
                vo.setName("LibrA");
            }
        }
        return vos;
    }

    /**
     * 删除数据实例的表
     *
     * @param tableId
     * @param dbType
     * @return
     */
    @GetMapping(value = "/deleteTable")
//    @FuncScanAnnotation(code = "dataWarehouseDeleteTable", name = "删除", parentCode = "dataObjectManage")
//    @ValidateAndLogAnnotation
    public Result deleteTable(String classifyId, String dbType, String tableId) {
        List<String> list = Lists.newArrayList();
        list.add(tableId);
        if (dataWarehouseService.isDataWareHouse(list)) {
            return Result.toResult(R.ok("其他仓库有使用到该表，无法删除！"));
        }
        if (dataWareTreeService.isBuildModel(list)) {
            return Result.toResult(R.ok("该表已被用于流程建模，无法删除！"));
        }
        if (dataWareTreeService.isBuildDMCModel(list)) {
            return Result.toResult(R.ok("该表已被用于自助建模，无法删除！"));
        }
        if (dataWareTreeService.isBuildVisualModel(list)) {
            return Result.toResult(R.ok("该表已被用于可视化建模，无法删除！"));
        }
        //删除此表跟角色和用户关系
        Map<String, String> functionRelationParams = Maps.newHashMap();
        functionRelationParams.put("func_code", tableId);
        List<TSysAuthObjFunc> tSysAuthObjFuncs = sysAuthObjFuncService.queryList(functionRelationParams);
        for (TSysAuthObjFunc tSysAuthObjFunc : tSysAuthObjFuncs) {
            sysAuthObjService.deleteAuthObj(tSysAuthObjFunc);
        }

        //删除注册到功能表的数据对象
        Map<String, String> functionParams = Maps.newHashMap();
        functionParams.put("func_code", tableId);
        TSysFuncBase tSysFuncBase = (TSysFuncBase) sysFuncService.query(functionParams);
        if (null != tSysFuncBase) {
            sysAuthObjService.deleteAuthObj(tSysFuncBase);
        }

        dataWarehouseService.deleteTable(tableId, dbType, null);
        return Result.toResult(R.ok());
    }

    /**
     * 查询相应数据库的表
     *
     * @param dataBaseId
     * @return
     */
    @GetMapping(value = "/queryAddTable")
    public Result queryAddTable(String dataBaseId, String queryContent) {
        List<Map<String, Object>> dbtList = dataWarehouseService.queryAddTable(dataBaseId, queryContent);
        return Result.toResult(R.ok(dbtList));
    }

    @PostMapping(value = "/saveTable")
    public Result saveTable(String dataBaseId, @RequestBody List<String> dataObject, HttpServletRequest request) {
        String userId = (String) request.getSession().getAttribute("userId");
        dataWarehouseService.addTable(dataBaseId, dataObject, userId);
        return Result.toResult(R.ok());
    }

    /**
     * 添加数据仓库目录
     *
     * @return
     */
    @PostMapping(value = "/addBusiDir")
    public Result addBusiDir(@RequestBody Map resMap, HttpServletRequest request) {
        String dirName = resMap.get("dirName").toString();
        String dirType = resMap.get("dirType").toString();
        String userId = (String)request.getSession().getAttribute("userId");
        return Result.success(dataWarehouseService.addBusiDir(dirName, dirType, userId));
    }

    /**
     * 重名名一级目录（例：数据仓库）
     *
     * @return
     */
    @PostMapping(value = "/updateCatalogName")
    public Result updateCatalogName(@RequestBody Map resMap, HttpServletRequest httpServletRequest) {
        String newName = resMap.get("newName").toString();
        String id = resMap.get("id").toString();
        String catalogType = resMap.get("catalogType").toString();
        String userId = (String)httpServletRequest.getSession().getAttribute("userId");
        dataWarehouseService.updateCatalog(newName, id, catalogType, userId);
        return Result.toResult(R.ok());
    }

    /**
     * 重命名 层次
     *
     * @param newName
     * @param id
     * @return
     */
    @GetMapping(value = "/updateLevelName")
    public Result updateLevelName(String newName, String id) {
        dataWarehouseService.updateLevel(newName, id);
        return Result.toResult(R.ok());
    }

    @GetMapping(value = "/updateDataOriginName")
    public Result updateDataOriginName(String newName, String id) {
        dataWarehouseService.updateDataOriginName(newName, id);
        return Result.toResult(R.ok());
    }

    /**
     * 拖动数据源
     *
     * @param id      数据源id
     * @param ownerId 数据源更新的父id
     * @return
     */
    @GetMapping(value = "/moveDataOrigin")
//    @FuncScanAnnotation(code = "dataWarehouseMoveDataOrigin", name = "移动", parentCode = "dataWarehouse")
//    @ValidateAndLogAnnotation
    public Result moveDataOrigin(String id, String ownerId) {
        dataWarehouseService.moveDataOrigin(id, ownerId);
        return Result.toResult(R.ok());
    }

    /**
     * 删除目录树
     *
     * @param dirId
     * @param dirType
     * @return
     */
    @GetMapping(value = "/deleteTreeNode")
    public Result deleteTreeNode(String dirId, String dirType) {
        String res = dataWarehouseService.deleteTreeNode(dirId, dirType);
        if ("success".equals(res)) {
            return Result.toResult(R.ok());
        } else {
            return Result.toResult(R.error(res));
        }
    }

    @PostMapping(value = "/renameDataSet")
//    @FuncScanAnnotation(code = "dataWarehouseRenameDataSet", name = "重命名", parentCode = "dataWarehouse")
//    @ValidateAndLogAnnotation
    public Result renameDataSet(@RequestBody Map resMap) {
        String dataId = resMap.get("dataId").toString();
        String newName = resMap.get("newName").toString();
        String dirId = resMap.get("dirId").toString();
        if (dataWarehouseService.checkDWIsExist(newName, dirId)) {
            return Result.toResult(R.error("该名称已存在"));
        }
//        Map<String, String> map = dataWarehouseService.getDeployedSoftwareId(dataId);
//        String deployedSoftwareId = map.get("deployed_software_id");
//        dataWarehouseService.renameDataDeployedSoftware(deployedSoftwareId, newName);
        dataWarehouseService.renameDataSet(dataId, newName);
        dataWarehouseService.renameDataObjecDataName(dataId, newName);
        return Result.toResult(R.ok());
    }

    @PostMapping("/preview")
//    @FuncScanAnnotation(code = "dataWarehousePreview", name = "数据预览", parentCode = "dataObjectManage")
//    @ValidateAndLogAnnotation
    public Result preview(@RequestBody Map resMap, HttpSession session) {
        String userId = (String) session.getAttribute("userId");
        String tableId = resMap.get("tableId").toString();
        String limit = resMap.get("limit").toString();
        String isFile = resMap.getOrDefault("isFile", "0").toString();
       /* String stepId = resMap.get("transStepId").toString();
        String transId = resMap.get("transId").toString();
        List<Map<String, String>> transMetaExpList = this.standardSqlOutputService.getTransMetaExpList(stepId);*/
        int size = 10;
        if (StringUtils.isNotBlank(limit)) {
            size = Integer.parseInt(limit);
        }
        ColumnDataModel rows = null;
      /*  List<TransVariable> transVariableList = transVariableService.queryTransByTransId(transId);
        List<TransVariable> transVariables = transVariableService.queryGlobalVariable(userId);
        List<TransVariable> collect = Stream.of(transVariableList, transVariables)
                .flatMap(Collection::stream)
                .distinct()
                .collect(Collectors.toList());*/

        if ("1".equals(isFile)) {
            /*try {
                rows = fileDataSetService.preview(tableId, String.valueOf(size));
            } catch (ExecutionException e) {
                    log.error(e.getMessage(),e);
                return Result.toResult(R.error("调用Spark失败!"));
            }*/
        } else {
            ClassifierStat obj = classifierService.getClassifierObj(ClassifierStat.class, tableId);
            if ("LogicDataObj".equals(obj.getType())) {
                tableId = obj.getOwnerId();
            }
            try {
                rows = queryDataService.queryData(tableId, size, false);
            } catch (Exception e) {
                log.error(e.getMessage(),e);
            }
        }
        ColumnDataModel columnDataModel = ChangeColumnNameUtil.changeName(tableId, rows);
        return Result.toResult(R.ok(columnDataModel));
    }


    @PostMapping("/previewRdb")
    public Result previewRdb(@RequestBody Map resMap) {
        String transStepId = resMap.get("transStepId").toString();
        String limit = resMap.get("limit").toString();
        Map<String, String> transMetaAttribute = pluginConfigService.getTransMetaAttribute(transStepId);
        String tableId = transMetaAttribute.get("tableId");
        int size = 10;
        if (StringUtils.isNotBlank(limit)) {
            size = Integer.parseInt(limit);
        }
        ColumnDataModel rows = null;

        ClassifierStat obj = classifierService.getClassifierObj(ClassifierStat.class, tableId);
        if ("LogicDataObj".equals(obj.getType())) {
            tableId = obj.getOwnerId();
        }
        try {
            rows = queryDataService.queryData(tableId, size, false);
        } catch (Exception e) {
           log.error(e.getMessage(),e);
            throw new PreviewErrorException(e.getMessage());
        }

        ColumnDataModel columnDataModel = ChangeColumnNameUtil.changeName(tableId, rows);
        return Result.toResult(R.ok(columnDataModel));
    }

    @GetMapping("/getElastics")
    public Result getElastics(String dataSetId) {
        return Result.toResult(R.ok(this.dataWarehouseService.getElastics(dataSetId)));
    }

    @GetMapping("/getDataSetInfo")
    public Result getDataSetInfo(String id) {
        DataSetVo dataSetInfo = dataWarehouseService.getDataSetInfo(id);
        if (dataSetInfo == null) {
            return Result.toResult(R.error("获取不到新注册数据源！"));
        } else {
            return Result.toResult(R.ok(dataSetInfo));
        }
    }

    @GetMapping("/viewDataSet")
//    @FuncScanAnnotation(code = "dataWarehouseViewDataSet", name = "查看", parentCode = "dataWarehouse")
//    @ValidateAndLogAnnotation
    public Result viewDataSet(String id) {
        return Result.toResult(R.ok(dataWarehouseService.getDeployedSoftware(id)));
    }

    @GetMapping("/getDataObject")
//    @FuncScanAnnotation(code = "dataWarehouseViewGetDataObject", name = "添加数据对象", parentCode = "dataWarehouse")
//    @ValidateAndLogAnnotation
    public Result getDataObject(String id) {
        Map<String, String> deployedSoftware = dataWarehouseService.getDeployedSoftwareId(id);
        String deployedSoftwareId = deployedSoftware.get("deployed_software_id");
        DeployedSoftware catalog = deployedSoftwareService.get(DeployedSoftware.class, deployedSoftwareId);
        String schemaId = "";
        if ("RdbCatalog".equals(catalog.getType())) {
            RdbCatalog c = deployedSoftwareService.get(RdbCatalog.class, deployedSoftwareId);
            String userName = c.getUserName();
            for (RdbSchema rs : c.getSchemas()) {
                if (rs.getUserName().equals(userName)) {
                    schemaId = rs.getId();
                }
            }
        }
        String code = dataWarehouseService.getLabelName(deployedSoftwareId);
        NewDataObjectParamVo vo = new NewDataObjectParamVo();
        vo.setDeployedSoftwareId(deployedSoftwareId);
        vo.setLabelName(code);
        vo.setAccountId(schemaId);

        return Result.toResult(R.ok(vo));
    }

    /**
     * 查询出字段
     *
     * @param dataMap
     * @return
     */
    @PostMapping("/queryElasticsColumns")
//    @FuncScanAnnotation(code = "dataWarehouseQueryElasticsColumnst", name = "详情", parentCode = "dataObjectManage")
//    @ValidateAndLogAnnotation
    public Result queryElasticsColumns(@RequestBody Map dataMap) {
        String objId = dataMap.get("objId").toString();
        String dbType = dataMap.get("dbType").toString();
        List<ElasticColumnVo> dataSet = dataWarehouseService.queryElasticsColumns(objId, dbType);
        return Result.toResult(R.ok(dataSet));
    }

    @GetMapping("/getTableInfo")
//    @FuncScanAnnotation(code = "dataWarehouseGetTableInfo", name = "编辑", parentCode = "dataObjectManage")
//    @ValidateAndLogAnnotation
    public Result getTableInfo(String id) {
        return Result.toResult(R.ok(dataWarehouseService.getTableInfo(id)));
    }

    @GetMapping("/updateStream")
    public Result updateStream(String id, boolean stream, String name) {
        String msg = this.dataWarehouseService.updateStream(id, stream, name);
        return Result.toResult(R.ok(msg));
    }

    @GetMapping("/checkServiceName")
    public Result checkServiceName(String name, String code) {
        return Result.toResult(R.ok(dataWarehouseMetaService.checkServiceName(name, code)));
    }

    @RequestMapping("/setFastTable")
//    @FuncScanAnnotation(code = "dataWarehouseSetFastTable", name = "极速表", parentCode = "dataObjectManage")
//    @ValidateAndLogAnnotation
    public Result setFastTable(String tableId, String isFast) {
        dataWarehouseService.setFastTable(tableId, isFast);
        return Result.toResult(R.ok());
    }

    @ResponseBody
    @RequestMapping("/getDataSetByDataSetType")
    public Result getDataSetByDataSetType(String type, HttpServletRequest request) {
        List listSchema = dataWareTreeService.getDataSetByDataSetType(type);
        return Result.toResult(R.ok(listSchema));
    }

    private Boolean isHaveDataObjAuth(List<TSysAuthRole> roleList) {
        //是否有查看权限 有则不需要过滤
        boolean flag = false;
        for (TSysAuthRole role : roleList) {
            List<String> code = roleService.queryRoleById(role.getId()).getFunctions()
                    .stream().map(TSysFuncBase::getFuncCode).collect(Collectors.toList());
            if (code.contains("dataWarehouseViewDataSet")) {
                flag = true;
                break;
            }
        }
        return flag;
    }

    private List<Map<String, String>> filterDataObj(List<Map<String, String>> list, String userId, List<TSysAuthRole> roleList) {
        List<String> ids = new ArrayList<>();
        for (Map<String, String> dataObj : list) {
            boolean flag = false;
            Map<String, String> params = Maps.newHashMap();
            params.put("obj_id", userId);
            params.put("func_code", dataObj.get("id"));
            TSysAuthObjFunc tSysAuthObjFunc = (TSysAuthObjFunc) sysAuthObjFuncService.query(params);
            if (null != tSysAuthObjFunc) {
                flag = true;
            }
            List<DatasetTableModel> datasetTableModels = dataWareTreeService.queryDataBaseTableAll(dataObj.get("id"));
            for (DatasetTableModel datasetTableModel : datasetTableModels) {
                //当前用户有此数据源关系 或者此数据源下有授权对象时
                if (dataSetAuthService.IsAuthDataset(datasetTableModel.getId(), userId, roleList)) {
                    flag = true;
                }
            }
            if (!flag) {
                ids.add(dataObj.get("id"));
            }
        }
        return list.stream().filter(s -> !ids.contains(s.get("id"))).collect(Collectors.toList());
    }


    /**
     * 获取数据源，层级到数据对象
     *
     * @return
     */
    @RequestMapping("/getDataObjectTree")
    public Result getDataObjectTree(HttpServletRequest request) {
        String userId = (String) request.getSession().getAttribute("userId");
        List<String> authIds = new ArrayList<>();

        if (null != userId) {
            authIds = userService.getAllAuthId(userId);
        }
        List<DatasetTreeModel> treeModelList = dataWareTreeService.getDataWarehouseTreeCopy("", true, true, userId, authIds, "");
        return Result.success(this.filterTree(treeModelList));
    }

    /**
     * 获取数据源，层级到数据对象,z只包含数据仓库
     *
     * @return
     */
    @RequestMapping("/getDatabaseDataObjectTree")
    public Result getDatabaseDataObjectTree(HttpServletRequest request) {
        String userId = (String) request.getSession().getAttribute("userId");
        List<String> authIds = new ArrayList<>();

        if (null != userId) {
            authIds = userService.getAllAuthId(userId);
        }
        List<DatasetTreeModel> treeModelList = dataWareTreeService.getDataWarehouseTreeCopy("", true, true, userId, authIds, "dataSpace");
        return Result.success(this.filterTree(treeModelList));
    }

    /**
     * 获取数据准备的树
     *
     * @return
     */
    @RequestMapping("/getDataPreparationTree")
    public Result getDataPreparationTree(HttpServletRequest request) {
        String userId = (String) request.getSession().getAttribute("userId");
        List<String> authIds = new ArrayList<>();
        if (null != userId) {
            authIds = userService.getAllAuthId(userId);
        }
        List<DatasetTreeModel> treeModelList = dataWareTreeService.getDataWarehouseTree("", true, true, userId, authIds, "dataSpace","all");
        if (!sceModel){
            for (int i = 0; i < treeModelList.size(); i++) {
                if ("场景案例".equals(treeModelList.get(i).getName())){
                    treeModelList.remove(i);
                    break;
                }
            }
        }
        return Result.success(this.filterTree(treeModelList));
    }

    @RequestMapping("/getModelingPluginTree")
    public Result getModelingPluginTree(HttpServletRequest request) {
        String userId = (String) request.getSession().getAttribute("userId");
        List<String> authIds = new ArrayList<>();
        if (null != userId) {
            authIds = userService.getAllAuthId(userId);
        }
        List<DatasetTreeModel> treeModelList = dataWareTreeService.getDataWarehouseTree("", false, false, userId, authIds, "dataSpace","all");
        if (!sceModel){
            for (int i = 0; i < treeModelList.size(); i++) {
                if ("场景案例".equals(treeModelList.get(i).getName())){
                    treeModelList.remove(i);
                    break;
                }
            }
        }
        return Result.success(this.filterTree(treeModelList));
    }

    @RequestMapping("/getKafkaInstanceTree")
    public Result getKafkaInstanceTree(HttpServletRequest request) {
        String userId = (String) request.getSession().getAttribute("userId");
        List<String> authIds = new ArrayList<>();
        if (null != userId) {
            authIds = userService.getAllAuthId(userId);
        }
        List<DatasetTreeModel> treeModelList = dataWareTreeService.getDataWarehouseTree("", false, false, userId, authIds, "dataSpace","all");
        if (!sceModel){
            for (int i = 0; i < treeModelList.size(); i++) {
                if ("场景案例".equals(treeModelList.get(i).getName())){
                    treeModelList.remove(i);
                    break;
                }
            }
        }
        filterNotKafka(treeModelList);
        return Result.success(this.filterTree(treeModelList));
    }

    private void filterNotKafka(List<DatasetTreeModel> treeModelList){
        Iterator<DatasetTreeModel> iterator = treeModelList.iterator();
        while (iterator.hasNext()) {
            DatasetTreeModel datasetTreeModel = iterator.next();
            if (datasetTreeModel.getChildren().size()>0) {
                filterNotKafka(datasetTreeModel.getChildren());
            } else {
                if (!"kafka".equalsIgnoreCase(datasetTreeModel.getInstanceType())) {
                    iterator.remove();
                }
            }
        }
    }

    /**
     * 获取数据源，层级到数据源，不到数据对象
     *
     * @return
     */
    @RequestMapping("/getDataSourceTree")
    public Result getDataSourceTree(@RequestParam(required = false, defaultValue = "false") boolean isLogic, @RequestParam(required = false, defaultValue = "false") boolean dataSpace, HttpServletRequest request) {
        String userId = (String) request.getSession().getAttribute("userId");
        List<String> authIds = new ArrayList<>();

        String dataSpaceStr = dataSpace ? "dataSpace" : "";
        if (null != userId) {
            authIds = userService.getAllAuthId(userId);
        }
        List<DatasetTreeModel> treeModelList = null;
        if (isLogic) {
            treeModelList = operationService.querySourceDatasetTree(userId, "",null);
        } else {
            treeModelList = dataWareTreeService.getDataWarehouseTreeCopy("", false, true, userId, authIds, dataSpaceStr);
        }
        if (!sceModel){
            for (int i = 0; i < treeModelList.size(); i++) {
                if ("场景案例".equals(treeModelList.get(i).getName())){
                    treeModelList.remove(i);
                    break;
                }
            }
        }
        return Result.success(treeModelList);
    }


    /**
     *  该树是组合两颗树，并且可以过滤相同库的数据集，可以重构一下！！！
     * @param hasDataObj
     * @param currentDataSetId
     * @param isLogic
     * @param dataSpace
     * @param filterDataSetId
     * @param hasDataWarehouse
     * @param request
     * @return
     */
    @ResponseBody
    @GetMapping("/getPublishDataSetTree")
    @ApiOperation(value = "获取生成API数据集目录树")
    @ApiImplicitParam(name = "hasDataObj", value = "是否带数据集", required = true, dataType = "boolean")
    public Result getPublishDataSetTree(boolean hasDataObj,
                                        @RequestParam(value = "currentDataSetId", required = false, defaultValue = "") String currentDataSetId,
                                        @RequestParam(required = false, defaultValue = "false") boolean isLogic,
                                        @RequestParam(required = false, defaultValue = "false") boolean dataSpace,
                                        String filterDataSetId,boolean hasDataWarehouse,HttpServletRequest request) {
        String userId = (String) request.getSession().getAttribute("userId");
        //数据集的树不包含数仓
        List<DatasetTreeModel> datasetTreeModels = dataSetOperationService.queryPublishDataSetTree(userId, hasDataObj, currentDataSetId);

        //是否有数仓的权限，两棵树合成一颗
        if(hasDataWarehouse){
            //数仓
            List<String> authIds = new ArrayList<>();
            String dataSpaceStr = dataSpace ? "dataSpace" : "";
            if (null != userId) {
                authIds = userService.getAllAuthId(userId);
            }
            List<DatasetTreeModel> treeModelList = null;
            if (isLogic) {
                treeModelList = operationService.querySourceDatasetTree(userId, "",null);
            } else {
                treeModelList = dataWareTreeService.getDataWarehouseTreeCopy("", false, true, userId, authIds, dataSpaceStr);
            }
            if (!sceModel){
                for (int i = 0; i < treeModelList.size(); i++) {
                    if ("场景案例".equals(treeModelList.get(i).getName())){
                        treeModelList.remove(i);
                        break;
                    }
                }
            }

            datasetTreeModels.addAll(treeModelList);
        }

        //通过当前数据集id过滤同库的数据集
        List<DatasetTreeModel> result=datasetTreeModels;
        if(StringUtils.isNotBlank(filterDataSetId)){
            result= dataWarehouseService.filterSameDatasourceTree(datasetTreeModels, filterDataSetId);

        }
        return Result.success(result);
    }


    private List<DatasetTreeModel> filterTree(List<DatasetTreeModel> tree) {
        List<DatasetTreeModel> copyTree = new ArrayList<>();
        for (DatasetTreeModel datasetTreeModel : tree) {
            if (null != datasetTreeModel.getChildren() && 3 != datasetTreeModel.getLevel()) {
                datasetTreeModel.setChildren(this.filterTree(datasetTreeModel.getChildren()));
            }

            if (null == datasetTreeModel.getChildren()) {
                copyTree.add(datasetTreeModel);
            }
        }
        List<String> copyTreeId = copyTree.stream().map(DatasetTreeModel::getId).collect(Collectors.toList());
        return tree.stream().filter(s -> !copyTreeId.contains(s.getId())).collect(Collectors.toList());
    }


    /**
     * 移动数据集
     *
     * @param dwDbId
     * @param dataObjId
     * @param dataSetName
     * @return
     */
    @GetMapping("/removeDataObj")
    public Result moveDataObj(String dwDbId, String dataObjId, String dataSetName,HttpServletRequest request) {
        String userId = (String) request.getSession().getAttribute("userId");
        String msg = dataWarehouseService.moveDataObj(dwDbId, dataObjId, dataSetName,userId);
        if ("success".equalsIgnoreCase(msg)) Result.success();
        return Result.success(msg);
    }


    /**
     * 数据源同步
     *
     * */
    @GetMapping("/column")
    @ApiOperation(value = "获取已注册数据对象的列变更信息")
    public Result findChangeColumn(String dataObjId, String dbType) {
        ChangeColumnVO columnVO = metadataService.findChangeColumns(dataObjId, dbType);
        return Result.success(columnVO);
    }


    @GetMapping("/sync")
//    @FuncScanAnnotation(code = "dataWarehouseDatasourceSynchronization", name = "同步", parentCode = "dataWarehouse")
    @ApiOperation(value = "同步已注册数据对象")
    public Result syncColumn(String key, String dataObjId, String dbType) {
        metadataService.syncColumn(key, dataObjId, dbType);
        LogicDataObj logicobjBelongtoRdb = metadataService.getLogicobjBelongtoRdb(dataObjId);
        return Result.success(logicobjBelongtoRdb.getId());
    }

    /**
     * 3.0同步已注册数据对象且同步业务主题下对应的logicdataset  全新出发，奥利给！
     * @param key
     * @param dataObjId
     * @param dbType
     * @return
     */
    @GetMapping("/syncColumnsDcThree")
    @ApiOperation(value = "同步已注册数据对象且同步业务主题下对应的logicdataset")
    public Result syncColumnsDcThree(String key, String dataObjId, String dbType) {
        metadataService.syncColumn(key, dataObjId, dbType);
        //同步业务主题下的那个logic
//        metadataService.syncColumnsLogicDataSet(dataObjId);
        return Result.success();
    }

    @PostMapping("/getFactNameByCode")
    @ApiOperation(value = "根据事实code获取事实名称")
    public Result getFactNameByCode(@RequestBody SearchFactNameDTO dto){
        Assert.notNull(dto.getCodes(),"code不能为空");
        List<String> list=dataWarehouseService.getFactNameByCode(dto.getCodes());
        return Result.success(list);
    }


    @ApiOperation(value = "新增或更新全局数据源配置")
    @PostMapping("/saveAnalysis")
    private Result saveAnalysis(@RequestBody AnalysisResultLibraryQO qo){
        dataWarehouseService.saveAnalysis(qo);
        return Result.success();
    }


    @ApiOperation(value = "获取全局数据源配置")
    @GetMapping("/getResult")
    private Result<AnalysisResultLibraryVO> getAnalysisResult(){
        AnalysisResultLibraryVO vo = dataWarehouseService.getAnalysisResult();
        return Result.success(vo);
    }


    @GetMapping(value = "/querySuperAllDirTree")
    public Result queryAllDirTree() {
        String userId = GlobalConstant.UserProperties.DC_SUPER_ID;
        List<String> authIds = userService.getAllAuthId(userId);
        List<DatasetTreeModel> treeModelList = dataWareTreeService.getDataWarehouseTree("", false, false, userId, authIds, "dataSpace","all");
        if (!sceModel){
            for (int i = 0; i < treeModelList.size(); i++) {
                if ("场景案例".equals(treeModelList.get(i).getName())){
                    treeModelList.remove(i);
                    break;
                }
            }
        }
        return Result.success(this.filterTree(treeModelList));
    }


    @RequestMapping("/querySuperDataBaseTableAll")
    public Result querySuperDataBaseTableAll(String dwId,HttpServletRequest request) {
        List<DatasetTableModel> datasetTableModels = dataWareTreeService.queryDataBaseTableAll(dwId);
        for (DatasetTableModel datasetTableModel : datasetTableModels) {
            datasetTableModel.setIsFast(dataWarehouseService.getIsFast(datasetTableModel.getId()));
        }
        String userId = (String) request.getSession().getAttribute("userId");
        datasetTableModels = dataWarehouseService.querySuperDataBaseTableAll(datasetTableModels,userId);
        return Result.toResult(R.ok(datasetTableModels));
    }

}
