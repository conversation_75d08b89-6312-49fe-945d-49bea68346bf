package com.dragonsoft.cicada.datacenter.modules.system.dataportal.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.code.common.paging.PageInfo;
import com.code.common.utils.StringUtils;
import com.code.metadata.business.directory.BaseBusiClassify;
import com.code.metadata.business.directory.BaseBusiDir;
import com.code.metadata.business.directory.BusiClassify;
import com.code.metadata.portal.MenuConfig;
import com.code.metadata.portal.Portal;
import com.code.metadata.portal.PortalConfig;
import com.code.metadata.portal.PublishUrl;
import com.code.metaservice.business.directory.BusiDirService;
import com.code.metaservice.datawarehouse.model.DatasetTreeModel;
import com.code.metaservice.portal.*;
import com.dragoninfo.dfw.entity.TSysAuthObjFunc;
import com.dragoninfo.dfw.entity.TSysAuthUser;
import com.dragoninfo.dfw.entity.TSysFunc;
import com.dragoninfo.dfw.service.SysAuthObjFuncService;
import com.dragoninfo.dfw.service.SysAuthObjService;
import com.dragoninfo.dfw.service.SysAuthUserService;
import com.dragoninfo.dfw.service.SysFuncService;
import com.dragonsoft.cicada.datacenter.common.utils.DatasetTreeModelUtils;
import com.dragonsoft.cicada.datacenter.modules.supermarkmodel.service.IMyModelService;
import com.dragonsoft.cicada.datacenter.modules.system.common.util.ListUtil;
import com.dragonsoft.cicada.datacenter.modules.system.common.vo.TreeVo;
import com.dragonsoft.cicada.datacenter.modules.system.dataportal.service.IDataPortalService;
import com.dragonsoft.cicada.datacenter.modules.system.dataportal.vo.PortalConfigVo;
import com.dragonsoft.cicada.datacenter.modules.system.dataportal.vo.PortalMenuTreeVo;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.service.IUserService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.lang.reflect.InvocationTargetException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2020/8/31
 */
@Service
@Slf4j
@PropertySource("classpath:case-config.properties")
public class DataPortalServiceImpl implements IDataPortalService {

    @Autowired
    private IPortalService portalService;

    @Autowired
    private IPortalConfigService portalConfigService;

    @Autowired
    private IMenuConfigService menuConfigService;

    @Autowired
    private IPublishUrlService publishUrlService;

    @Autowired
    private IPortalTreeService portalTreeService;

    @Autowired
    private SysAuthUserService sysAuthUserService;

    @Autowired
    private SysFuncService sysFuncService;

    @Autowired
    private SysAuthObjService sysAuthObjService;

    @Autowired
    private SysAuthObjFuncService sysAuthObjFuncService;
    @Autowired
    private IUserService userService;

    private final String FUNC_CODE = "funcCode";

    private final String ShareId = "2";

    @Value("${sceModel}")
    private boolean sceModel;

    @Value("${standModel}")
    private boolean standModel;

    @Autowired
    private BusiDirService busiDirService;

    @Autowired
    private IMyModelService myModelServiceImpl;

    @Override
    public List<DatasetTreeModel> queryTree(String userId) {
        List<DatasetTreeModel> allDatasetTreeModels = portalTreeService.queryPortalTree(userId);


        for (DatasetTreeModel allDatasetTreeModel : allDatasetTreeModels) {
            List<DatasetTreeModel> childrenList = allDatasetTreeModel.getChildren();
            if (CollUtil.isNotEmpty(childrenList)) {
                Iterator<DatasetTreeModel> iterator = childrenList.iterator();
                while (iterator.hasNext()) {
                    DatasetTreeModel model = iterator.next();
                    if (!sceModel && "场景案例".equals(model.getName())) {
                        iterator.remove();
                    }
                    if (!standModel && "标准模型".equals(model.getName())) {
                        iterator.remove();
                    }
                    if ( "分享给我的".equals(model.getName())) {
                        iterator.remove();
                    }
                }
            }
        }
//        List<DatasetTreeModel> shareDatasetTreeModels = portalTreeService.getDatasetTreeModelListByShare(userId);
//        DatasetTreeModel datasetTreeModel = new DatasetTreeModel();
//        datasetTreeModel.setId("share");
//        datasetTreeModel.setCode("share");
//        datasetTreeModel.setName("分享给我的");
//        datasetTreeModel.setLabel("分享给我的");
//        datasetTreeModel.setOpen(true);
//        datasetTreeModel.setLevel(2);
//        datasetTreeModel.setpId(allDatasetTreeModels.get(0).getId());
//        datasetTreeModel.setChildren(shareDatasetTreeModels);

//        allDatasetTreeModels.get(0).getChildren().get(1).setChildren(shareDatasetTreeModels);

        return allDatasetTreeModels;
    }

    /**
     * 他人空间
     *
     * @param userId 管理员用户Id
     * @return
     */
    @Override
    public  DatasetTreeModel getOtherSpaceTree(String userId) {
        List<String> userIdList = portalService.queryOtherUserSpaceByUserId(userId);
        if (CollUtil.isEmpty(userIdList)) {
            return new DatasetTreeModel();
        }
        DatasetTreeModel resultModel = DatasetTreeModelUtils.buildPersonalDataset("他人空间");
        List<DatasetTreeModel> userModelList = new ArrayList<>();
        Map<String, String> userMap = userService.getUserMap(userIdList);
        userMap.forEach((k, v) -> {
            List<DatasetTreeModel> allDatasetTreeModels = portalTreeService.queryPortalTree(k);
            for (DatasetTreeModel allDatasetTreeModel : allDatasetTreeModels) {
                List<DatasetTreeModel> childrenList = allDatasetTreeModel.getChildren();
                for (DatasetTreeModel children : childrenList) {
                    if ("我的空间".equals(children.getName())) {
                        children.setpId(resultModel.getId());
                        children.setName(v);
                        children.setCurrentUserId(k);
                        children.setLabel(v);
                        userModelList.add(children);
                    }
                }
            }
        });
        resultModel.setChildren(userModelList);
        return resultModel;
    }

    @Override
    public String addTreeNode(String pId, String name, String userId) {
        if (Boolean.FALSE.equals(portalTreeService.checkName(name, userId, pId))) {
            throw new IllegalArgumentException("名称已存在！");
        }

        BusiClassify busiClassify = new BusiClassify();
        BusiClassify parentBusiClassify = portalTreeService.get(BusiClassify.class, pId);
        busiClassify.setParentBc(parentBusiClassify);
        busiClassify.setName(name);
        busiClassify.setOperateUserId(userId);
        busiClassify.setCode("PORTAL_DIR");
        portalTreeService.save(busiClassify);
        return busiClassify.getId();
    }

    @Override
    public void deleteTreeNode(String nodeId, String userId) {
        BusiClassify busiClassify = portalTreeService.get(BusiClassify.class, nodeId);
        if (!userId.equals(busiClassify.getOperateUserId())) throw new IllegalArgumentException("不是目录创建人,无法删除！");
        List<Portal> portals = portalService.queryPortalByNodeId(nodeId);
        if (0 != portals.size()) throw new IllegalArgumentException("此目录下有门户方案,不允许删除！");
        portalTreeService.delete(busiClassify);
    }

    @Override
    public void editTreeNode(String id, String pId, String name) {
        BusiClassify busiClassify = portalTreeService.get(BusiClassify.class, id);
        BusiClassify parentBusiClassify = portalTreeService.get(BusiClassify.class, pId);
        busiClassify.setName(name);
        busiClassify.setParentBc(parentBusiClassify);
        portalTreeService.saveOrUpdate(busiClassify);
    }

    @Override
    public void upDataSecurityModeAndTime(String portalId, String securityMode, String beginTime, String endTime) {
        Portal portal = portalService.get(Portal.class, portalId);
        if (null == portal) throw new IllegalArgumentException("该门户不存在！");
        portal.setSecurityMode(securityMode);

        if (this.checkDateRange(beginTime) || null == beginTime) {
            portal.setBeginTime(beginTime);
        } else {
            throw new IllegalArgumentException("开始时间范围错误！");
        }

        if (this.checkDateRange(endTime) || null == endTime) {
            portal.setEndTime(endTime);
        } else {
            throw new IllegalArgumentException("结束时间范围错误！");
        }
        portalService.update(portal);
    }

    private Boolean checkDateRange(String day) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date date;
        try {
            date = simpleDateFormat.parse(day);
        } catch (Exception e) {
            return false;
        }

        String newDay = simpleDateFormat.format(date);
        if (!day.equals(newDay)) {
            return false;
        }

        return true;
    }


    @Override
    public Map<String, String> createOrUpdatePortal(PortalConfigVo portalConfigVo, String userId, String dirId) {
        Portal portal = this.setPortal(portalConfigVo, userId, dirId);
        portalService.saveOrUpdate(portal);
        this.tSaveSysFunc(portal.getId(), userId, "1");
        PortalConfig portalConfig = this.setPortalConfig(portalConfigVo);
        if (null == portalConfig)
            throw new IllegalArgumentException("该目录下不能新建门户");
        portalConfig.setOwner(portal);
        portalConfigService.saveOrUpdate(portalConfig);
        if (CollectionUtils.isNotEmpty(portalConfigVo.getPortalMenuList())) {
            if (null != portalConfig && CollectionUtils.isNotEmpty(portalConfigVo.getDeleteMenuList())) {
                updateDefaultHomePage(portalConfig, portalConfigVo.getDeleteMenuList());
                deleteMenuConfigList(portalConfigVo.getDeleteMenuList());
            }
            this.tSaveMenuConfig(portalConfigVo.getPortalMenuList(), portalConfig, null, userId);
        }
        Map<String, String> map = new HashMap<>();
        map.put("portalConfigId", portalConfig.getId());
        map.put("portalId", portal.getId());
        return map;
    }

    public void copy(String id, String dirId, String portalName, String userId) {
        Portal originalPortal = portalService.queryPortalByPortalId(id);
        PortalConfigVo portalConfigVo = new PortalConfigVo();
        portalConfigVo.setPortalName(portalName);
        portalConfigVo.setSecurityMode(originalPortal.getSecurityMode());
        Portal portal = this.setPortal(portalConfigVo, originalPortal.getOperateUserId(), dirId);
        portal.setOperateUserId(userId);
        portalService.saveOrUpdate(portal);
        this.tSaveSysFunc(portal.getId(), portal.getOperateUserId(), "1");
        PortalConfig config = portalConfigService.queryPortalConfigByPortalId(id);
        PortalConfig newConfig = new PortalConfig();
        org.springframework.beans.BeanUtils.copyProperties(config, newConfig);
        newConfig.setId(null);
//        newConfig.setOwnerId(portal.getId());
        newConfig.setOwner(portal);
        portalConfigService.saveOrUpdate(newConfig);

        List<MenuConfig> menuConfigs = menuConfigService.queryMenuConfigByConfigId(config.getId());
        List<PortalMenuTreeVo> treeVoList = this.setMenuCfgTree(menuConfigs, portalConfigVo.getDefaultHomePage(), new ArrayList<>(), true);
        //设置id为null
        for (PortalMenuTreeVo vo : treeVoList) {
            vo.setId(null);
        }
        this.tSaveMenuConfig(treeVoList, newConfig, null, portal.getOperateUserId());

    }

    private void updateDefaultHomePage(PortalConfig portalConfig, List<TreeVo> deleteMenuList) {
        for (TreeVo treeVo : deleteMenuList) {
            if (treeVo.getId().equals(portalConfig.getDefaultHomePage())) {
                portalConfig.setDefaultHomePage(null);
            } else if (CollectionUtils.isNotEmpty(treeVo.getChildren())) {
                this.updateDefaultHomePage(portalConfig, treeVo.getChildren());
            }
        }
    }

    private void deleteMenuConfigList(List<TreeVo> deleteMenuList) {
        for (TreeVo treeVo : deleteMenuList) {
            MenuConfig menuConfig = menuConfigService.get(MenuConfig.class, treeVo.getId());
            if (null != menuConfig) {
                menuConfigService.delete(menuConfig);
            }
            this.tDeleteSysAuthObjFunc(treeVo.getId());
            this.tDeleteSysFunc(treeVo.getId());
        }
    }

    private void tSaveSysFunc(String businessId, String userId, String funcType) {
        Map<String, String> functionParams = Maps.newHashMap();
        functionParams.put(FUNC_CODE, businessId);
        TSysFunc tSysFunc = (TSysFunc) sysFuncService.query(functionParams);
        if (tSysFunc != null) return;
        TSysFunc tSysFuncBase = new TSysFunc();
        tSysFuncBase.setFuncCode(businessId);
        tSysFuncBase.setFuncName(businessId);
        tSysFuncBase.setFuncType(funcType);
        tSysFuncBase.setEnableState("0");
        tSysFuncBase.setDescription(userId);
        sysFuncService.store(tSysFuncBase);
    }

    private void tDeleteSysFunc(String businessId) {
        Map<String, String> functionParams = Maps.newHashMap();
        functionParams.put(FUNC_CODE, businessId);
        List<TSysFunc> tSysFuncList = sysFuncService.queryList(functionParams);
        if (CollectionUtils.isNotEmpty(tSysFuncList)) {
            for (TSysFunc tSysFunc : tSysFuncList) {
                sysAuthObjService.deleteAuthObj(tSysFunc);
            }
        }
    }


    private void tDeleteSysAuthObjFunc(String businessId) {
        Map<String, String> functionParams = Maps.newHashMap();
        functionParams.put("FUNC_CODE", businessId);
        List<TSysAuthObjFunc> tSysAuthObjFuncList = sysAuthObjFuncService.queryList(functionParams);
        if (CollectionUtils.isNotEmpty(tSysAuthObjFuncList)) {
            for (TSysAuthObjFunc TSysAuthObjFunc : tSysAuthObjFuncList) {
                sysAuthObjService.deleteAuthObj(TSysAuthObjFunc);
            }
        }
    }


    private Portal setPortal(PortalConfigVo portalConfigVo, String userId, String dirId) {
        Assert.hasText(portalConfigVo.getPortalName(), "门户名称不能为空！");
        if (portalService.checkPortalName(portalConfigVo.getPortalId(), portalConfigVo.getPortalName(), userId))
            throw new IllegalArgumentException("已存在相同的门户名称！");
       /* if (portalConfigService.checkPortalPublishUrl(portalConfigVo.getPublishUrl(), portalConfigVo.getPortalConfigId()))
            throw new IllegalArgumentException("数据门户别名已被使用，请重新换一个试试！");*/
        Portal portal = new Portal();
        if (StringUtils.isNotBlank(portalConfigVo.getPortalId()))
            portal = portalService.get(Portal.class, portalConfigVo.getPortalId());
        portal.setName(portalConfigVo.getPortalName());
        portal.setCode(portalConfigVo.getPortalName());
        portal.setEditUserId(userId);
        if (StringUtils.isBlank(portalConfigVo.getPortalId())) {
            portal.setOperateUserId(userId);
        }
        portal.setType(portal.getClass().getName());
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        portal.setOperateTime(df.format(new Date()));
        BaseBusiClassify parentBc = portalService.get(BaseBusiClassify.class, dirId);
        portal.setOwner(parentBc);
        portal.setSecurityMode(portalConfigVo.getSecurityMode());
        return portal;
    }

    private PortalConfig setPortalConfig(PortalConfigVo portalConfigVo) {
        PortalConfig portalConfig = new PortalConfig();
        if (StringUtils.isNotBlank(portalConfigVo.getPortalConfigId()))
            portalConfig = portalConfigService.get(PortalConfig.class, portalConfigVo.getPortalConfigId());
        portalConfig.setBackgroundType(portalConfigVo.getThemeSettings());
        portalConfig.setLayout(portalConfigVo.getLayoutScheme());
        portalConfig.setMenuCache(portalConfigVo.getIsMenuCache());
        portalConfig.setPortalAliasURL(portalConfigVo.getPortalAlias());
        portalConfig.setPortalPublishURL(portalConfigVo.getPublishUrl());
        portalConfig.setType(portalConfig.getClass().getName());
        portalConfig.setLogo(portalConfigVo.getLogo());
        portalConfig.setName(portalConfigVo.getConfigName());
        portalConfig.setCode(portalConfig.getClass().getName());
        return portalConfig;
    }

    private void tSaveMenuConfig(List<PortalMenuTreeVo> portalMenuTreeVoList, PortalConfig portalConfig, String parentId, String userId) {
        for (PortalMenuTreeVo portalMenuTreeVo : portalMenuTreeVoList) {
            MenuConfig menuConfig = this.setMenuConfig(portalMenuTreeVo);
            menuConfig.setOwner(portalConfig);
            menuConfig.setParentMenuConfig(parentId == null ? null : menuConfigService.get(MenuConfig.class, parentId));
            MenuConfig menuConfig1 = null;
            if (StringUtils.isNotBlank(menuConfig.getId())) {
                menuConfig1 = menuConfigService.get(MenuConfig.class, menuConfig.getId());
            }
            if (menuConfig1 == null) menuConfigService.save(menuConfig);
            else menuConfigService.saveOrUpdate(menuConfig);
            this.tSaveSysFunc(menuConfig.getId(), userId, "3");
            if (portalMenuTreeVo.getIsDefaultHomePage()) {
                portalConfig.setDefaultHomePage(menuConfig.getId());
                portalConfigService.saveOrUpdate(portalConfig);
            }
            if (CollectionUtils.isNotEmpty(portalMenuTreeVo.getPortalMenuChildrenList())) {
                this.tSaveMenuConfig(portalMenuTreeVo.getPortalMenuChildrenList(), portalConfig, menuConfig.getId(), userId);
            }
        }
    }

    private MenuConfig setMenuConfig(PortalMenuTreeVo portalMenuTreeVo) {
        MenuConfig menuConfig = new MenuConfig();
        if (StringUtils.isNotBlank(portalMenuTreeVo.getId()))
            menuConfig = menuConfigService.get(MenuConfig.class, portalMenuTreeVo.getId());
        if (menuConfig == null) {
            menuConfig = new MenuConfig();
            menuConfig.setId(portalMenuTreeVo.getId());
        }

        menuConfig.setType(menuConfig.getClass().getName());
        menuConfig.setCode(portalMenuTreeVo.getName());
        menuConfig.setName(portalMenuTreeVo.getName());
        if (portalMenuTreeVo.getDefaultMenuFold()) {
            menuConfig.setFoldType(PortalMenuTreeVo.DEFAULT_FOLD);
        } else menuConfig.setFoldType(portalMenuTreeVo.getIsMenuFold() ? PortalMenuTreeVo.MENU_FOLD : null);
        menuConfig.setContentType(portalMenuTreeVo.getConnectType());
        menuConfig.setIsNull(portalMenuTreeVo.getIsNullNode());
        menuConfig.setViewType(portalMenuTreeVo.getViewType());
        menuConfig.setContentGetURl(portalMenuTreeVo.getGetUrl());
        menuConfig.setPostURLParam(portalMenuTreeVo.getPostParam());
        menuConfig.setChildrenMenuConfigs(null);
        return menuConfig;
    }

    @Override
    public void deletePortal(String portalId) {
        Portal portal = portalService.get(Portal.class, portalId);
        if (null == portal)
            throw new IllegalArgumentException("该门户不存在！");
        this.tDeleteSysAuthObjFunc(portalId);
        this.tDeleteSysFunc(portalId);
        PortalConfig portalConfig = portalConfigService.queryPortalConfigByPortalId(portalId);
        if (null != portalConfig) {
            this.tDeleteMenuConfig(portalConfig.getId());
            portalConfigService.delete(portalConfig);
        }
        PublishUrl publishUrl = publishUrlService.queryPublishUrlByPortalId(portalId);
        if (null != publishUrl) {
            publishUrlService.delete(publishUrl);
        }
        portalService.delete(portal);
        //删除模型市场数据
        myModelServiceImpl.deleteMarkModelByTransId(portalId);
    }

    private void tDeleteMenuConfig(String portalCfgId) {
        List<MenuConfig> menuConfigs = menuConfigService.queryMenuConfigByConfigId(portalCfgId);
        List<Map> menuIds = menuConfigService.queryMenuConfigIdByConfigId(portalCfgId);
        if (CollectionUtils.isNotEmpty(menuConfigs)) {
            for (MenuConfig menuConfig : menuConfigs) {
                menuConfigService.delete(menuConfig);
            }
        }
        if (CollectionUtils.isNotEmpty(menuIds)) {
            for (Map menuId : menuIds) {
                this.tDeleteSysAuthObjFunc(menuId.get("id").toString());
                this.tDeleteSysFunc(menuId.get("id").toString());
            }
        }
    }

    @Override
    public PageInfo getPortalPage(String userId, String keyWord, String dirId, PageInfo pageInfo) throws InvocationTargetException, IllegalAccessException {

        BusiClassify busiClassify = portalService.get(BusiClassify.class, dirId);
        BaseBusiDir singleBaseBusiDirById = busiDirService.getSingleBaseBusiDirById(dirId);

        PageInfo pageInfo1 = new PageInfo();
        if (null != busiClassify || singleBaseBusiDirById != null) {
            if (singleBaseBusiDirById != null) {
                pageInfo1 = portalService.queryPortalPage(keyWord, userId, getPorClassify(dirId, userId, singleBaseBusiDirById), pageInfo, true);
            } else {
                pageInfo1 = portalService.queryPortalPage(keyWord, userId, getPorClassify(dirId, userId, null), pageInfo, false);
            }
        } else if (StringUtils.isBlank(dirId)) {
            pageInfo1 = portalService.queryPortalPage(keyWord, userId, Lists.newArrayList(), pageInfo, false);
        }
        if (StringUtils.isNotBlank(dirId) && null == busiClassify && singleBaseBusiDirById == null) {
            if (ShareId.equals(dirId)) {
                pageInfo1 = portalService.querySharePortalPage(keyWord, userId, pageInfo);
            }
        }
        List<Portal> dataList = pageInfo1.getDataList();
        List<Map> list = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(dataList)) {
            for (Portal portal : dataList) {
                try {
                    Map<String, String> params = Maps.newHashMap();
                    params.put("id", portal.getOperateUserId());
                    TSysAuthUser tSysAuthUser = (TSysAuthUser) sysAuthUserService.query(params);
                    Map<String, Object> map = BeanUtils.describe(portal);
                    map.put("createName", tSysAuthUser == null ? null : tSysAuthUser.getObjName());
                    if (Objects.equals(userId, portal.getOperateUserId())) map.put("isCreator", true);
                    else map.put("isCreator", false);
                    list.add(map);
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
            }
        }
        pageInfo1.setDataList(list);
        return pageInfo1;
    }


    private List<String> getPorClassify(String dirId, String userId, BaseBusiDir singleBaseBusiDirById) throws InvocationTargetException, IllegalAccessException {
        List<String> dirIdList = Lists.newArrayList();
        if (singleBaseBusiDirById != null && CollectionUtils.isNotEmpty(singleBaseBusiDirById.getBusiClassifys())) {
            for (BaseBusiClassify busiClassify : singleBaseBusiDirById.getBusiClassifys()) {
                if (Objects.equals(userId, busiClassify.getOperateUserId())) {//userId.equals(busiClassify.getOperateUserId())
                    List<String> dirs = Lists.newArrayList();
                    getDirIdList(busiClassify.getId(), userId, dirs);
                    dirIdList.addAll(dirs);
                }
            }
        } else {
            getDirIdList(dirId, userId, dirIdList);
        }
        return dirIdList;
    }

    private void getDirIdList(String dirId, String userId, List<String> dirIdList) throws InvocationTargetException, IllegalAccessException {
        DatasetTreeModel datasetTreeModel = new DatasetTreeModel();
        queryDatasetTreeModel(dirId, this.queryTree(userId), datasetTreeModel);
        List<DatasetTreeModel> datasetTreeModelList = Lists.newArrayList();
        datasetTreeModelList.add(datasetTreeModel);
        ListUtil.datasetTreeToList(dirIdList, datasetTreeModelList);

    }

    private void queryDatasetTreeModel(String dirId, List<DatasetTreeModel> datasetTreeModels, DatasetTreeModel datasetTreeModel0) throws InvocationTargetException, IllegalAccessException {
        for (DatasetTreeModel datasetTreeModel : datasetTreeModels) {
            if (dirId.equals(datasetTreeModel.getId())) {
                BeanUtils.copyProperties(datasetTreeModel0, datasetTreeModel);
            } else if (CollectionUtils.isNotEmpty(datasetTreeModel.getChildren()) && !dirId.equals(datasetTreeModel.getId()))
                this.queryDatasetTreeModel(dirId, datasetTreeModel.getChildren(), datasetTreeModel0);
        }
    }

    @Override
    public PortalConfigVo getPortalConfig(String portalId, String userId) {
        PortalConfigVo portalConfigVo = new PortalConfigVo();
        boolean isCreatorId = false;
        Portal portal = portalService.get(Portal.class, portalId);
        portalConfigVo.setPortalName(portal.getName());
        this.setPortalCfgVo(portalId, portalConfigVo);
        List<MenuConfig> menuConfigs = menuConfigService.queryMenuConfigByConfigId(portalConfigVo.getPortalConfigId());
        List<String> authIds = Lists.newArrayList();
        if (portal.getOperateUserId().equals(userId))
            isCreatorId = true;
        else authIds = userService.getAllAuthFunctionId(userId);
        portalConfigVo.setPortalMenuList(this.setMenuCfgTree(menuConfigs, portalConfigVo.getDefaultHomePage(), authIds, isCreatorId));
        return portalConfigVo;
    }

    private void setPortalCfgVo(String portalId, PortalConfigVo portalConfigVo) {
        PortalConfig portalConfig = portalConfigService.queryPortalConfigByPortalId(portalId);
        portalConfigVo.setPortalId(portalId);
        portalConfigVo.setPortalConfigId(portalConfig.getId());
        portalConfigVo.setDefaultHomePage(portalConfig.getDefaultHomePage());
        portalConfigVo.setIsMenuCache(portalConfig.getMenuCache());
        portalConfigVo.setConfigName(portalConfig.getName());
        portalConfigVo.setLayoutScheme(portalConfig.getLayout());
        portalConfigVo.setLogo(portalConfig.getLogo());
        portalConfigVo.setPortalAlias(portalConfig.getPortalAliasURL());
        String[] urls = portalConfig.getPortalPublishURL().split(portalConfig.getPortalAliasURL() + "/");
        if (urls.length > 0) portalConfigVo.setPortalUrlParams(urls[1]);
        portalConfigVo.setThemeSettings(portalConfig.getBackgroundType());
    }

    private List<PortalMenuTreeVo> setMenuCfgTree(List<MenuConfig> menuConfigs, String defaultHomePage, List<String> authIds, boolean isCreatorId) {
        List<PortalMenuTreeVo> portalMenuTreeVoList = Lists.newArrayList();
        for (MenuConfig menuConfig : menuConfigs) {
            PortalMenuTreeVo portalMenuTreeVo = new PortalMenuTreeVo();
            this.setPortalMenuTreeVo(portalMenuTreeVo, menuConfig);
            if (isCreatorId)
                portalMenuTreeVo.setIsAuth(true);
            else
                portalMenuTreeVo.setIsAuth(authIds.contains(menuConfig.getId()));
            if (portalMenuTreeVo.getIsAuth() && menuConfig.getId().equals(defaultHomePage))
                portalMenuTreeVo.setIsDefaultHomePage(true);
            if (CollectionUtils.isNotEmpty(menuConfig.getChildrenMenuConfigs())) {
                portalMenuTreeVo.setPortalMenuChildrenList(this.setMenuCfgTree(new ArrayList<>(menuConfig.getChildrenMenuConfigs()), defaultHomePage, authIds, isCreatorId));
            }
            portalMenuTreeVoList.add(portalMenuTreeVo);
        }
        return portalMenuTreeVoList;
    }

    private void setPortalMenuTreeVo(PortalMenuTreeVo portalMenuTreeVo, MenuConfig menuConfig) {
        portalMenuTreeVo.setConnectType(menuConfig.getContentType());
        portalMenuTreeVo.setFoldType(menuConfig.getFoldType());
        portalMenuTreeVo.setGetUrl(menuConfig.getContentGetURl());
        portalMenuTreeVo.setIsNullNode(menuConfig.getIsNull());
        portalMenuTreeVo.setPostParam(menuConfig.getPostURLParam());
        portalMenuTreeVo.setPostRul(menuConfig.getContentPostURl());
        portalMenuTreeVo.setViewType(menuConfig.getViewType());
        portalMenuTreeVo.setId(menuConfig.getId());
        portalMenuTreeVo.setName(menuConfig.getName());
    }

    @Override
    public PortalConfigVo getPortalPageByUrl(String userId, String portalPublishUrl) {
        List<String> authIds = userService.getAllAuthFunctionId(userId);
        PortalConfig portalConfig = portalConfigService.queryPortalPageByUrl(portalPublishUrl);
        if (authIds.contains(portalConfig.getOwnerId())) {
            return this.getPortalConfig(portalConfig.getOwnerId(), userId);
        }
        return null;
    }
}
