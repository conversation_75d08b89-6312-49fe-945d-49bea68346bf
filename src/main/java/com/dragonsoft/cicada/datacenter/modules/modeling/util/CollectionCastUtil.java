package com.dragonsoft.cicada.datacenter.modules.modeling.util;

import java.util.ArrayList;
import java.util.List;

/**
 * @Auther Yecc
 * @Date: 2021/07/22/上午10:45
 */
public class CollectionCastUtil {

    /**
     * 用于将 Object 转换成List<T>
     * @param obj
     * @param clazz
     * @param <T>
     * @return
     */
    public static <T> List<T> castList(Object obj, Class<T> clazz)
    {
        List<T> result = new ArrayList<T>();
        if(obj instanceof List<?>)
        {
            for (Object o : (List<?>) obj)
            {
                result.add(clazz.cast(o));
            }
            return result;
        }
        return null;
    }
}
