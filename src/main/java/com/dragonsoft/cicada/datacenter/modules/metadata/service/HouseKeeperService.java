package com.dragonsoft.cicada.datacenter.modules.metadata.service;

import com.dragonsoft.cicada.datacenter.modules.metadata.vo.DataSourceKeepVo;
import com.fw.service.BaseService;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public abstract class HouseKeeperService extends BaseService {

    public List<DataSourceKeepVo> templateAssembly(String dbType) {
        List<Map<String, String>> data = getData(dbType);
        List<DataSourceKeepVo> dataSourceKeepVos = new ArrayList<>(data.size());
        for (Map<String, String> datum : data) {
            dataSourceKeepVos.add(new DataSourceKeepVo().builder()
                    .classifyId(datum.get("classifyId"))
                    .catalogId(datum.get("catalogid"))
                    .name(datum.get("name"))
                    .id(datum.get("id"))
                    .builder()
            );
        }
        return dataSourceKeepVos;
    }


    abstract protected List<Map<String, String>> getData(String dbType);
}
