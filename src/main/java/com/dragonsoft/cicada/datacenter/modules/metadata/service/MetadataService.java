package com.dragonsoft.cicada.datacenter.modules.metadata.service;

import com.code.metadata.res.common.ClassifierStat;
import com.code.metadata.res.ddl.LogicDataObj;
import com.code.metaservice.datawarehouse.model.DataSourceDTO;
import com.code.metaservice.datawarehouse.model.DataSourceMachineVo;
import com.code.metaservice.datawarehouse.model.DataSourceVO;
import com.code.metaservice.datawarehouse.model.SchemaVo;
import com.code.metaservice.ddl.vo.LogicHttpColumns;
import com.code.metaservice.res.response.datasource.InsertDataSourceResponse;
import com.code.metaservice.res.response.vo.dataobject.DataObjectColumnView;
import com.code.metaservice.res.response.vo.dataobject.DataObjectView;
import com.dragonsoft.cicada.datacenter.modules.metadata.vo.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Avizii
 * @Create : 2021.03.29
 */
public interface MetadataService {

    InsertDataSourceResponse insertDataSource(DataSourceVO dataSourceVO);

    void updateDataSource(DataSourceVO dataSourceVO);

    MachineVo checkMachineIpExist(String ip);

    List<StandardCodeVo> findZookeepers();

    List<SoftwareVo> findSoftwareVersions(String dbType);

    DataSourceDTO findDataSourceById(String instanceId);

    List<SchemaVo> findUserByDataSourceId(String instanceId, String dbType);

    DbTableVO findDatabaseTableList(String schemaId, String dbType);

    List<DataObjectView>  saveDataObject(DataObjectVO dataObjectVO);

    ChangeColumnVO findChangeColumns(String dbObjId, String dbType);

    void syncColumn(String key, String dataObjId, String dbType);

    List<DataSourceMachineVo> findMachineByInstanceId(String instanceId, String dataSourceType);

    void updateDwbName(String id, String dbType, String name);

    LogicDataObj getLogicobjBelongtoRdb(String dataObjId);

    /**
     * dataObjId为rdb的id,这边得需要根据rdb查出业务主题下的logic进行同步
     * @param dataObjId
     */
    void syncColumnsLogicDataSet(String dataObjId);

    /**
     * 同步逻辑数据集
     * @param logicDataObj 需要同步的logicDataObj
     * @param classifierStat 被同步的物理表对象
     */
    void syncLogicDataSetColumns(LogicDataObj logicDataObj, ClassifierStat classifierStat);

    List<DataObjectColumnView> getColumn(String dbType, String dataObjId);

    /**
     *  移动数据源
     * */
    void moveDwbInstance(String dirId, String dwbId);

    /**
     *  另存为数据源  需要先把数据源信息捞出来
     * */
    DataSourceVO saveAs(String schemaId, String dbType, String name);

    Map<String, String> getAllHouseKeeperType();

    List<DataSourceKeepVo> findDataObjByType(String dbType);

    List<DbTableDTO> getDbTableByCode(String id, String type);

    void overlaySynchronization(String id);

    void deleteDwFromMetdata(String id);

    void syncFromChildrenToParent(String logicId);

    Map<String, List<LogicHttpColumns>> findChangeColumnsForParents(String dbObjId, String dbType);

    Map<String, List<LogicHttpColumns>> getSyncColumns(String logicId);

    void syncChidren(String logicId);

    void saveDataObjNew(NewDataObjectVO newDataObjectVO);

    String getSchemaIdByCatalogId(String catalogId);


}
