package com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.enums;

import java.util.List;

import com.code.metadata.sm.EnumServiceType;
import com.google.common.collect.Lists;

public enum ServiceClassifyEnum {

    ALL("全部","all"),
    LOCAL_SERVICE("本地服务","localService"),
    THIRD_SERVICE("第三方服务","thirdService"),
    PROXY_SERVICE("代理服务","proxyService");

    public String name;
    public String code;

    private ServiceClassifyEnum(String name,String code) {
        this.name = name;
        this.code = code;
    }

    public static List<ServiceClassifyEnum> getServiceClassifyEnumToLoadModel(){
        return Lists.newArrayList(ALL,LOCAL_SERVICE);
    }

    public String getName() {
        return name;
    }

    public String getCode() {
        return code;
    }
    

    public static List<EnumServiceType> getEnumServiceTypeListToLoadModel(){
        return Lists.newArrayList(EnumServiceType.CALCULATION_SERVICE,
        EnumServiceType.INFORMATION_VERFICATION,
        EnumServiceType.COMPARE_SERVICE,
        EnumServiceType.DATA_COLLISION);
    }








}
