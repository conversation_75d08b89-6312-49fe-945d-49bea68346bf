package com.dragonsoft.cicada.datacenter.modules.system.permissions.controller;

import com.code.common.paging.PageInfo;
import com.code.common.utils.StringUtils;
import com.code.common.utils.assertion.Assert;
import com.dragoninfo.dfw.annotation.FuncScanAnnotation;
import com.dragoninfo.dfw.annotation.ValidateAndLogAnnotation;
import com.dragoninfo.dfw.bean.Result;
import com.dragoninfo.dfw.entity.TSysAuthUser;
import com.dragoninfo.dfw.entity.TSysFuncBase;
import com.dragoninfo.dfw.enums.BusiErrorCode;
import com.dragoninfo.dfw.util.LoginUtil;
import com.dragoninfo.dfw.utils.ExceptionUtil;
import com.dragonsoft.cicada.datacenter.common.utils.EncryptionUtils;
import com.dragonsoft.cicada.datacenter.common.utils.FrontEncryptUtils;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.service.IUserService;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.vo.GroupUserTreeVo;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.vo.PageVo;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.vo.UserVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2020/5/21
 */
@Controller
@CrossOrigin
@RequestMapping("/user")
@Api(value = "UserController|用户操作控制器，页面上的ID为数据库中的code")
@FuncScanAnnotation(code = "userManagement", name = "用户管理", parentCode = "systemManagement")
@PropertySource("classpath:case-config.properties")
@Slf4j
public class UserController {

    @Autowired
    private IUserService userService;

    @Value("${accessThirdSystem}")
    private String accessThirdSystem;


    @Value("${batchUserFilePath}")
    private String batchUserFilePath;

//    /**
//     * 登录
//     *
//     * @return
//     */
//    @ResponseBody
//    @RequestMapping(value = "/login", method = RequestMethod.POST)
//    @ApiOperation(value = "登录")
//    public Result login(UserVo userVo) {
//        TSysAuthUser tSysAuthUser = userService.login(userVo);
//        return Result.success(tSysAuthUser);
//    }

//    /**
//     * 注销
//     *
//     * @param request
//     * @param response
//     * @return
//     */
//    @ResponseBody
//    @RequestMapping(value = "/logout", method = RequestMethod.POST)
//    @ApiOperation(value = "注销")
//    public Result logout(HttpServletRequest request, HttpServletResponse response) {
//        Enumeration<String> p = request.getSession().getAttributeNames();
//        while (p.hasMoreElements()) {
//            request.getSession().removeAttribute(p.nextElement());
//        }
//        return Result.success();
//    }

    /**
     * 获取用户的随机标识ID
     *
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/getUserRandom", method = RequestMethod.POST)
    @ApiOperation(value = "获取用户页面的随机ID")
    public Result getUserRandom() {
        String code = userService.getUserRandom();
        return Result.success(code);
    }

    /**
     * 通过ID获取用户
     *
     * @param userId
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/queryUserById", method = RequestMethod.POST)
    @ApiOperation(value = "通过ID查询用户，此ID为数据库实际ID")
    @FuncScanAnnotation(code = "userManagementQueryUser", name = "详情", parentCode = "userManagement")
    @ValidateAndLogAnnotation
    public Result queryUserById(@RequestBody String userId) {
        UserVo userVo = userService.queryUserById(userId);
        return Result.success(userVo);
    }


    @ResponseBody
    @RequestMapping(value = "/queryUserFunction", method = RequestMethod.POST)
    @ApiOperation(value = "查询当前登录用户的权限")
    @FuncScanAnnotation(code = "login", name = "登录", parentCode = "login")
    @ValidateAndLogAnnotation
    public Result queryUserFunction(HttpServletRequest request) {
        String userId = (String) request.getSession().getAttribute("userId");
        if (StringUtils.isBlank(userId)) {
            ExceptionUtil.throwBusiException(BusiErrorCode.NOT_LOGON);
        }
        List<TSysFuncBase> tSysFuncBases = userService.queryUserFunction(userId);
        return Result.success(tSysFuncBases);
    }


    @ResponseBody
    @RequestMapping("/isOverTime")
    @ApiOperation(value = "查询当前登录是否过期")
    public Result isOverTime(HttpServletRequest request) {
        String userId = (String) request.getSession().getAttribute("userId");
        if (userId.isEmpty()) {
            ExceptionUtil.throwBusiException(BusiErrorCode.NOT_LOGON);
        }
//        List<TSysFuncBase> tSysFuncBases = userService.queryUserFunction(userId);
        return Result.success();
    }

    /**
     * 获取用户列表 支持ID与name的模糊查询
     *
     * @param pageVo
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/queryUserList", method = RequestMethod.POST)
    @ApiOperation(value = "获取用户列表 支持ID与name的模糊查询")
    public Result queryUserList(@RequestBody PageVo pageVo) {
        PageInfo pageInfo = userService.queryUsersPageByCodeOrName(pageVo);
        return Result.success(pageInfo);
    }

    /**
     * 添加用户
     *
     * @param userVo
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/addUser", method = RequestMethod.POST)
    @ApiOperation(value = "添加")
    @FuncScanAnnotation(code = "userManagementAddUser", name = "添加用户", parentCode = "userManagement")
    @ValidateAndLogAnnotation
    public Result addUser(@RequestBody UserVo userVo) {
        userService.addUser(userVo);
        return Result.success();
    }


    /**
     * 添加用户
     *
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/batchAddUser", method = RequestMethod.POST)
    @FuncScanAnnotation(code = "batchAddUser", name = "批量添加用户", parentCode = "userManagement")
    public Result batchAddUser() {
        userService.batchAddUser(batchUserFilePath);
        return Result.success();
    }

    /**
     * 重置密码
     *
     * @return
     */
    @ResponseBody
    @RequestMapping("/resetPassword")
    @ApiOperation(value = "重置密码")
    @FuncScanAnnotation(code = "userManagementResetPassword", name = "重置密码", parentCode = "userManagement")
    @ValidateAndLogAnnotation
    public Result resetPassword(String userId, String passWord) {
        userService.updataPassword(userId, passWord);
        return Result.success();
    }

    /**
     * 编辑用户
     *
     * @param userVo
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/editUser", method = RequestMethod.POST)
    @ApiOperation(value = "编辑")
    @FuncScanAnnotation(code = "userManagementEditUser", name = "编辑", parentCode = "userManagement")
    @ValidateAndLogAnnotation
    public Result editUser(@RequestBody UserVo userVo) {
        userService.updateUser(userVo);
        return Result.success();
    }

    /**
     * 删除用户
     *
     * @param userId
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/deleteUser", method = RequestMethod.POST)
    @ApiOperation(value = "删除")
    @FuncScanAnnotation(code = "userManagementDeleteUser", name = "删除", parentCode = "userManagement")
    @ValidateAndLogAnnotation
    public Result deleteUser(@RequestBody String userId) {
        String msg = userService.deleteUser(userId);
        return Result.success(msg);
    }

    /**
     * 修改密码
     *
     * @param userCode
     * @param oldPassword
     * @param newPassword
     * @return
     */
    @ResponseBody
    @RequestMapping("/updataPassword")
    @ApiOperation(value = "修改密码")
    public Result updataPassword(String userCode, String oldPassword, String newPassword) {
        String msg = userService.updataPassword(userCode, oldPassword, newPassword);
        return Result.success(msg);
    }

    @ResponseBody
    @RequestMapping("/updataLoginNumber")
    @ApiOperation(value = "更新登录次数")

    public Result updataLoginNumber(String userId) {
        userService.updataLoginNumber(userId);
        return Result.success();
    }

    /**
     * 按用户组获取所有用户
     *
     * @return
     */
    @ResponseBody
    @RequestMapping("/getAllUser")
    @ApiOperation(value = "按用户组获取所有用户")
    public Result getAllUser() {
        List<GroupUserTreeVo> groupUserTreeVos = userService.queryAllUser();
        return Result.success(groupUserTreeVos);
    }

    @ResponseBody
    @RequestMapping("/getAllUserNotIncludeItself")
    @ApiOperation(value = "获取所有用户不包含自身")
    public Result getAllUserNotIncludeItself(String userId) {
        List<TSysAuthUser> allUserNotIncludeItself = userService.getAllUserNotIncludeItself(userId);
        return Result.success(allUserNotIncludeItself);
    }

    @ResponseBody
    @RequestMapping("/getUserRole")
    @ApiOperation(value = "获取用户的所有角色")
    public Result getUserRole(String userId, HttpServletRequest request) {
        String Id = (String) request.getSession().getAttribute("userId");
        if (null == Id) {
            return Result.success("未登录");
        } else {
            TSysAuthUser tSysAuthObjs = userService.getUserRole(Id);
            return Result.success(tSysAuthObjs);
        }
    }

    @ResponseBody
    @RequestMapping("/isExistUser")
    @ApiOperation(value = "是否存在此code的用户")
    public Result isExistUser(String userCode){
        userService.isExistUser(userCode);
        return Result.success();
    }


    @ResponseBody
    @RequestMapping("/getUserId")
    @ApiOperation(value = "根据用户code查找用户id")
    public Result getUserIdByUserCode(String userCode){
        return Result.success(userService.getUserIdbyUserCode(userCode));
    }

    @ResponseBody
    @RequestMapping("/getEncryptPass")
    @ApiOperation(value = "密码进行加密")
    public Result getEncryptPass(String passWord){
        String frontEncryptPassword="";
        try {
            if("1".equalsIgnoreCase(accessThirdSystem)){
                frontEncryptPassword = EncryptionUtils.encrypt(passWord);
            }else{
                frontEncryptPassword = LoginUtil.encoderByMd5(FrontEncryptUtils.getFrontEncryptPassword(passWord));
            }
        }catch (Exception e){
            log.error(e.getMessage(),e);
            Assert.fail("获取加密密码失败！");
        }
        return Result.success(frontEncryptPassword);
    }
}
