package com.dragonsoft.cicada.datacenter.modules.system.permissions.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.code.common.paging.PageInfo;
import com.code.metadata.business.directory.BaseBusiClassify;
import com.dragoninfo.dfw.entity.*;
import com.dragoninfo.dfw.exception.BusiException;
import com.dragoninfo.dfw.service.*;
import com.dragonsoft.cicada.datacenter.common.utils.CastUtils;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.enums.PermissionErrorCode;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.service.IRoleService;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.service.IUserService;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.util.UserUtil;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.vo.PageVo;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.vo.RoleVo;
import com.fw.service.BaseService;
import com.fw.service.DfwBaseService;
import com.fw.service.annotation.Service;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2020/5/21
 */
@Service
@Transactional
public class RoleServiceImpl extends BaseService implements IRoleService {

    public static final String JS = "JS";
    public static final String ID = "id";
    public static final String ONE = "1";
    public static final String ADMIN = "dc_super";
    public static final String FUNCTIONCODE = "funcCode";
    public static final String DCADMINROLES = "数据中心超级管理员职责";

    @Autowired
    private SysAuthRoleService sysAuthRoleService;
    @Autowired
    private SysAuthObjFuncService sysAuthObjFuncService;
    @Autowired
    private SysAuthObjService sysAuthObjService;
    @Autowired
    private SysAuthUserService sysAuthUserService;
    @Autowired
    private SysAuthObjRelService sysAuthObjRelService;
    @Autowired
    private SysFuncService sysFuncService;

    @Autowired
    private IUserService userService;

    @Autowired
    private DfwBaseService dfwBaseService;

    @Override
    public PageInfo queryRolesPageByCodeOrName(PageVo pageVo) {
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageIndex(pageVo.getPageNum());
        pageInfo.setPageSize(pageVo.getPageSize());
        return sysAuthRoleService.queryBlurryPage(UserUtil.setPageParams(pageVo), pageInfo);
    }

    @Override
    public List<TSysAuthRole> queryAllRole() {
        Map<String, String> params = Maps.newHashMap();
        return CastUtils.cast(sysAuthRoleService.queryList(params));
    }

    @Override
    public List<TSysAuthRole> queryDidsRoles() {
        Map<String, String> params = Maps.newHashMap();
        List<TSysAuthRole> tSysAuthRoles = sysAuthRoleService.queryList(params);
        return tSysAuthRoles.stream().filter(s -> ONE.equals(s.getSourceType()) && !DCADMINROLES.equals(s.getObjCode()))
                .collect(Collectors.toList());
    }

    @Override
    public List<String> queryAllRoleAuth(String dataSetId) {

        String sql = "select b.obj_id  as id from t_sys_auth_obj a inner join t_sys_auth_obj_func b on a.id =b.obj_id " +
                " where  b.func_code =:dateSetId and a.obj_type ='2'";
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("dateSetId", dataSetId);
        List<Map<String, String>> mapList = CastUtils.cast(dfwBaseService.getDfwBaseDao().sqlQueryForList(sql, paramMap));
        List<String> ids = new ArrayList<>();
        if (CollUtil.isNotEmpty(mapList)) {
            for (Map<String, String> map : mapList) {
                ids.add(map.get("id"));
            }
        }
        return ids;
    }

    @Override
    public RoleVo queryRoleById(String roleId) {
        Map<String, String> params = Maps.newHashMap();
        params.put(ID, roleId);
        TSysAuthRole role = CastUtils.cast(sysAuthRoleService.query(params));
        RoleVo roleVo = new RoleVo();
        roleVo.setRoleId(role.getId());
        roleVo.setRoleCode(role.getObjCode());
        roleVo.setRoleName(role.getObjName());
        roleVo.setLastEditime(role.getUpdateTime());
        roleVo.setEditor(role.getUpdateObj() == null ? "" : role.getUpdateObj().getObjName());
        List<TSysFuncBase> functions = role.gettSysAuthObjFuncSet().stream()
                .map(TSysAuthObjFunc::gettSysFuncBase)
                .filter(tSysFuncBase -> tSysFuncBase.getFuncType().equals("0")).collect(Collectors.toList());
        roleVo.setFunctions(functions);
        return roleVo;
    }

    @Override
    public void upataRole(RoleVo roleVo) {
        Map<String, String> roleParams = Maps.newHashMap();
        roleParams.put(ID, roleVo.getRoleId());
        TSysAuthRole tSysAuthRole = (TSysAuthRole) sysAuthRoleService.query(roleParams);

        tSysAuthRole.setObjName(roleVo.getRoleName());
        Timestamp d = new Timestamp(System.currentTimeMillis());
        tSysAuthRole.setUpdateTime(d);

        Map<String, String> params = Maps.newHashMap();
        params.put(ID, roleVo.getEditor());
        TSysAuthUser user = (TSysAuthUser) sysAuthUserService.query(params);
        tSysAuthRole.setUpdateObj(user);

        Map<String, String> functionParams = Maps.newHashMap();
        functionParams.put("obj_id", roleVo.getRoleId());

        List<TSysAuthObjFunc> tSysAuthObjFuncs = sysAuthObjFuncService.queryList(functionParams);

        List<String> deleteFunctionIds = roleVo.getDeleteFunctions().stream().map(s -> s.getFuncCode()).collect(Collectors.toList());
        for (TSysAuthObjFunc tSysAuthObjFunc : tSysAuthObjFuncs) {
            //删除角色-功能关系 getFuncType为0（功能权限）才删除
            if ("0".equals(tSysAuthObjFunc.gettSysFuncBase().getFuncType()) && deleteFunctionIds.contains(tSysAuthObjFunc.gettSysFuncBase().getFuncCode())) {
                sysAuthObjService.deleteAuthObj(tSysAuthObjFunc);
            }
        }


        sysAuthObjService.updateAuthObj(tSysAuthRole);


        List<String> functionIds = roleVo.getAddFunctions().stream().map(s -> s.getFuncCode()).collect(Collectors.toList());

        tSysAuthRole.settSysAuthObjFuncSet(this.saveAuthObjFuncs(functionIds, roleVo.getRoleId(), tSysAuthRole));

        addServiceDirByRoleId(roleVo.getRoleId());

    }

    private void addServiceDirByRoleId(String roleId) {
        List<String> userIdsByRoleId = this.getUserIdsByRoleId(roleId);
        String hql = "from BaseBusiClassify where code = 'TRANS_SERVICE_CLASSIFY' and busiDir.code = 'TRANS_SERVICE_DIR' and operateUserId = :userId";
        if (CollectionUtil.isNotEmpty(userIdsByRoleId)) {
            for (String userId : userIdsByRoleId) {
                BaseBusiClassify baseBusiClassify = (BaseBusiClassify) this.baseDao.queryForObject(hql, this.addParam("userId", userId).param());
                if (baseBusiClassify != null) {
                    break;
                } else {
                    userService.saveClassifyByCondition("我的服务", "TRANS_SERVICE_CLASSIFY", "BaseBusiClassify", "TRANS_SERVICE_DIR", userId);
                }
            }
        }
    }

    @Override
    public String deleteRole(String roleId) {
        Map<String, String> params = Maps.newHashMap();
        params.put("RELATION_TYPE", ONE);
        List<TSysAuthObjRel> tSysAuthObjRels = CastUtils.cast(sysAuthObjRelService.queryList(params));
        List<TSysAuthObjRel> belongTSysAuthObjRels = tSysAuthObjRels.stream().filter(s -> s.getToAuthObj().getId().equals(roleId)).collect(Collectors.toList());
        for (TSysAuthObjRel t : belongTSysAuthObjRels) {
            //删除人-角色关系
            sysAuthObjService.deleteAuthObj(t);
        }

        Map<String, String> functionParams = Maps.newHashMap();
        functionParams.put("obj_id", roleId);
        List<TSysAuthObjFunc> tSysAuthObjFuncs = sysAuthObjFuncService.queryList(functionParams);
        for (TSysAuthObjFunc tSysAuthObjFunc : tSysAuthObjFuncs) {
            //删除角色-功能关系和角色-数据集关系
            sysAuthObjService.deleteAuthObj(tSysAuthObjFunc);
        }


        Map<String, String> param = Maps.newHashMap();
        param.put(ID, roleId);
        TSysAuthRole tSysAuthRole = (TSysAuthRole) sysAuthRoleService.query(param);
        sysAuthObjService.deleteAuthObj(tSysAuthRole);
        return "删除成功";

    }

    @Override
    public void addRole(RoleVo roleVo) {
        checkRoleCode(roleVo);
        TSysAuthRole tSysAuthRole = new TSysAuthRole();
        Timestamp d = new Timestamp(System.currentTimeMillis());
        tSysAuthRole.setCreateTime(d);
        tSysAuthRole.setUpdateTime(d);
        tSysAuthRole.setObjCode(roleVo.getRoleCode());
        tSysAuthRole.setObjName(roleVo.getRoleName());
        Map<String, String> params = Maps.newHashMap();
        params.put(ID, roleVo.getEditor());
        TSysAuthUser user = CastUtils.cast(sysAuthUserService.query(params));
        tSysAuthRole.setCreateObj(user);
        tSysAuthRole.setUpdateObj(user);
        tSysAuthRole.setObjType("2");
        sysAuthRoleService.store(tSysAuthRole);

        List<String> addFunctionIds = roleVo.getFunctions().stream().map(s -> s.getFuncCode()).collect(Collectors.toList());

        tSysAuthRole.settSysAuthObjFuncSet(this.saveAuthObjFuncs(addFunctionIds, null, tSysAuthRole));
    }

    private void checkRoleCode(RoleVo roleVo) {
        Map<String, String> params = Maps.newHashMap();
        params.put("objCode", roleVo.getRoleCode());
        List<TSysAuthRole> sysAuthRoleList = CastUtils.cast(sysAuthRoleService.queryList(params));
        if (CollUtil.isNotEmpty(sysAuthRoleList)) {
            throw new BusiException(PermissionErrorCode.ROLE_EXISTED);
        }
    }

    @Override
    public String getRoleRandom() {
        return UserUtil.getRandom(Lists.newArrayList(), JS);
    }

    @Override
    public List<String> getUserIdsByRoleId(String roleId) {
        Map<String, String> params = Maps.newHashMap();
        params.put("RELATION_TYPE", ONE);
        params.put("TO_OBJ_ID", roleId);
        List<TSysAuthObjRel> tSysAuthObjRels = CastUtils.cast(sysAuthObjRelService.queryList(params));
        List<TSysAuthObjRel> belongTSysAuthObjRels = tSysAuthObjRels.stream()
                .filter(s -> s.getToAuthObj().getId().equals(roleId)).collect(Collectors.toList());
        return belongTSysAuthObjRels.stream()
                .map(s -> s.getFromAuthObj().getId())
                .collect(Collectors.toList());
    }

    @Override
    public List<TSysAuthObj> getTSysUserByRoleId(String roleId) {
        Map<String, String> params = Maps.newHashMap();
        params.put("RELATION_TYPE", ONE);
        params.put("TO_OBJ_ID", roleId);
        List<TSysAuthObjRel> tSysAuthObjRels = CastUtils.cast(sysAuthObjRelService.queryList(params));
        return tSysAuthObjRels.stream().filter(s -> s.getToAuthObj().getId().equals(roleId))
                .map(TSysAuthObjRel::getFromAuthObj)
                .collect(Collectors.toList());
    }

    /**
     * 设置角色-功能关系
     *
     * @param
     * @return
     */
    private Set<TSysAuthObjFunc> saveAuthObjFuncs(List<String> functionIds, String roleId, TSysAuthRole tSysAuthRole) {
        Set<TSysAuthObjFunc> tSysAuthObjFuncs = new HashSet<>();
//        List<String> functionIds = roleVo.getFunctions().stream().map(s -> s.getFuncCode()).collect(Collectors.toList());
        //id实际为功能的code 作为唯一标识
        for (String id : functionIds) {
            TSysAuthObjFunc tSysAuthObjFunc = new TSysAuthObjFunc();
            Map<String, String> functionParams = Maps.newHashMap();
            functionParams.put(FUNCTIONCODE, id);
            TSysFuncBase tSysFuncBase = (TSysFuncBase) sysFuncService.query(functionParams);
            if (null != roleId) {
                Map<String, String> roleParams = Maps.newHashMap();
                roleParams.put(ID, roleId);
                TSysAuthRole role = CastUtils.cast(sysAuthRoleService.query(roleParams));
                tSysAuthObjFunc.settSysAuthObj(role);
            } else {
                tSysAuthObjFunc.settSysAuthObj(tSysAuthRole);
            }
            tSysAuthObjFunc.settSysFuncBase(tSysFuncBase);
            sysAuthObjFuncService.store(tSysAuthObjFunc);
            tSysAuthObjFuncs.add(tSysAuthObjFunc);
        }
        return tSysAuthObjFuncs;
    }

    /**
     * 包装角色前端返回对象
     *
     * @param roles
     * @return
     */
    private List<RoleVo> setRoles(List<TSysAuthRole> roles) {
        List<RoleVo> roleVoList = new ArrayList<>();
        for (TSysAuthRole role : roles) {
            RoleVo roleVo = new RoleVo();
            roleVo.setRoleId(role.getId());
            roleVo.setRoleCode(role.getObjCode());
            roleVo.setRoleName(role.getObjName());
            roleVo.setLastEditime(role.getUpdateTime());
            roleVoList.add(roleVo);
        }
        return roleVoList;
    }
}
