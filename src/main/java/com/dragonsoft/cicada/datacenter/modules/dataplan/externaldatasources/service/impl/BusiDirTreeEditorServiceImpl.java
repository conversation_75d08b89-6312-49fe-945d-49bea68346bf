package com.dragonsoft.cicada.datacenter.modules.dataplan.externaldatasources.service.impl;

import com.code.metadata.sql.utils.SQLUtils;
import com.dragonsoft.cicada.datacenter.modules.dataplan.externaldatasources.service.BusiDirTreeEditorService;
import com.fw.service.BaseService;
import com.fw.service.annotation.Service;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;

import java.util.*;

@Service
public class BusiDirTreeEditorServiceImpl extends BaseService implements BusiDirTreeEditorService {

    private static Map<String, String> rdbSoftwares = new LinkedHashMap<String, String>();
    private static Map<String, String> noRdbSoftwares = new LinkedHashMap<String, String>();

    static {
        rdbSoftwares.put("Mysql", "Mysql");
        rdbSoftwares.put("ADS", "ADS");
        rdbSoftwares.put("Oracle", "Oracle");
        rdbSoftwares.put("PostgreSql", "PostgreSql");
        rdbSoftwares.put("SqlServer", "SqlServer");
        rdbSoftwares.put("Hana", "Hana");
        rdbSoftwares.put("Hive", "Hive");
        rdbSoftwares.put("Gbase", "Gbase");
        rdbSoftwares.put("Greenplum", "Greenplum");
        rdbSoftwares.put("Dm", "Dm");
        rdbSoftwares.put("ODPS", "ODPS");
        rdbSoftwares.put("GBASE8T", "GBASE8T");
        rdbSoftwares.put("GBASE8M", "GBASE8M");
        rdbSoftwares.put("ADS", "ADS");
        rdbSoftwares.put("Mongodb", "Mongodb");
        rdbSoftwares.put("Hwmpp", "hwmpp");
        rdbSoftwares.put("EsgynDB", "EsgynDB");
        rdbSoftwares.put("Vertica", "Vertica");

        noRdbSoftwares.put("Elasticsearch", "ElasticsearchCatalog");
        noRdbSoftwares.put("Hbase", "HbaseInstance");
        noRdbSoftwares.put("Solr", "SolrInstance");
        noRdbSoftwares.put("Ots", "OtsInstance");
        noRdbSoftwares.put("Redis", "Redis");
    }

    @Override
    public List<Map> getResTree(List<String> softWares, String keyWord) {
        return this.getResTree(softWares, keyWord,null);
    }

    @Override
    public List<Map> getResTree(List<String> softwares, String keyWord, List<String> rightList) {
        Collection rdbs = rdbSoftwares.keySet();
        Collection noRdbs = noRdbSoftwares.keySet();
        String _keyWord = com.code.common.utils.StringUtils.isBlank(keyWord) ? null : "%" + keyWord + "%";
        if (softwares != null && softwares.size() > 0) {
            rdbs = CollectionUtils.retainAll(rdbs, softwares);
            noRdbs = CollectionUtils.retainAll(noRdbs, softwares);
        }
        List<String> idList = getElementIds(rightList);

        String sql =
                "SELECT a.ID       " + SQLUtils.getSQLField("catalogId") + "," +
                        "       a.CODE     " + SQLUtils.getSQLField("catalogCode") + "," +
                        "       a.OWNER_ID    " + SQLUtils.getSQLField("catalogOwner") + "," +
                        "       a.NAME     " + SQLUtils.getSQLField("catalogName") + "," +
                        "       b.ID       " + SQLUtils.getSQLField("schemaId") + "," +
                        "       b.CODE     " + SQLUtils.getSQLField("schemaCode") + "," +
                        "       b.NAME     " + SQLUtils.getSQLField("schemaName") + "," +
                        "       b.OWNER_ID  " + SQLUtils.getSQLField("schemeOwner") + "," +
                        "       d.ID       " + SQLUtils.getSQLField("softwareId") + "," +
                        "       d.CODE     " + SQLUtils.getSQLField("softwareCode") + "," +
                        "       d.OWNER_ID " + SQLUtils.getSQLField("softwareOwner") + " " +
                        "  FROM t_md_element a, t_md_element b, t_md_element d" +
                        " WHERE a.id = b.OWNER_ID" +
                        "   AND a.type in ('RdbCatalog','MongoCatalog')" +
                        "   AND b.TYPE in ('RdbSchema','MongoSchema')" +
                        "   AND d.TYPE = 'Software'" +
//			"   AND d.CODE IN ('Oracle', 'MySql', 'Hive')" +
                        "  AND LOWER(d.CODE) IN (:rdbs)  " +
//                        "/~ AND a.CODE like :keyWord  ~/" +
                        "/~ AND (LOWER(a.CODE) like :keyWord or LOWER(d.CODE) like :keyWord or LOWER(b.CODE) like :keyWord or LOWER(a.NAME) like :keyWord) ~/" +
                        "   AND a.OWNER_ID = d.ID";
        List<String> rdbsParam = getLowerList(rdbs);
        List<Map> rdbList = baseDao.sqlQueryForList(sql, addParam("keyWord", _keyWord).addParam("rdbs", rdbsParam.size() == 0 ? "" : rdbsParam).param());

        sql =
                "SELECT a.ID        " + SQLUtils.getSQLField("hbaseId") + "," +
                        "       a.OWNER_ID  " + SQLUtils.getSQLField("hbaseOwner") + "," +
                        "       a.CODE      " + SQLUtils.getSQLField("hbaseCode") + "," +
                        "       a.CODE      " + SQLUtils.getSQLField("hbaseName") + "," +
                        "       b.ID        " + SQLUtils.getSQLField("softwareId") + "," +
                        "       b.CODE      " + SQLUtils.getSQLField("softwareCode") + "," +
                        "       b.OWNER_ID  " + SQLUtils.getSQLField("softwareOwner") + " " +
                        "  FROM t_md_element a, t_md_element b" +
                        " WHERE a.OWNER_ID = b.ID" +
                        "   AND a.TYPE IN ('HbaseInstance', 'SolrInstance','OtsInstance','RedisInstance','ElasticsearchCatalog','ElasticSearchInstance')" +
                        "   AND b.TYPE = 'Software'" +
//			"   AND b. CODE IN ('Hbase', 'Solr')";
                        "/~ AND (LOWER(a.CODE) like :keyWord OR LOWER(b.CODE) like :keyWord) ~/" +
                        " AND LOWER(b.CODE) IN (:noRdbs) ";
        List<String> noRdbsParam=getLowerList(noRdbs);
        Param param1 = addParam("keyWord", _keyWord).addParam("noRdbs", noRdbsParam.size()==0?"":noRdbsParam);
        if (idList != null && idList.size() != 0) {
            sql += "   AND a.ID IN(:ids)";
            param1.addParam("ids", idList);
        }
        List<Map> hbaseList = baseDao.sqlQueryForList(sql, param1.param());

        rdbList.addAll(hbaseList);
        return rdbList;
    }

    /***
     * 根据DIDS返回的权限码(大数据管家资源目录权限)获取目录下所有注册的资源编号（ELEMENT_ID）
     * @param rightList
     * @return
     */
    private List<String> getElementIds(List<String> rightList) {
        if (rightList != null && rightList.size() > 0) {
            String sql = "SELECT a.ELEMENT_ID " +
                    "FROM t_md_classify_element a " +
                    "WHERE a.BUSI_CLASSIFY_ID IN (" +
                    "	SELECT ID FROM t_md_element" +
                    "	WHERE ID IN (" +
                    "		SELECT BUSI_CLASSIFY_ID FROM t_md_busi_classify" +
                    "		WHERE BUSI_DIR_ID IN (:rightIds)" +
                    "	)" +
                    ")";
            Param param = addParam("rightIds", rightList.toArray());
            List<Map<String, String>> idMap = baseDao.sqlQueryForList(sql, param.param());

            if (idMap != null) {
                List<String> idList = Lists.newArrayList();
                for (Map<String, String> id : idMap) {
                    idList.addAll(id.values());
                }
                return idList;
            }
        }
        return null;
    }

    private List<String> getLowerList(Collection collection) {
        List<String> list = new ArrayList<>();
        Iterator iterator = collection.iterator();
        while (iterator.hasNext()) {
            String obj = iterator.next().toString();
            list.add(obj.toLowerCase());
        }
        return list;
    }

}
