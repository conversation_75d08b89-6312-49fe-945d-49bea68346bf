-- 流程建模目录
INSERT INTO t_md_busi_dir
(id, "name", "version", "type", memo, code, owner_id, map_key, extended_type, operate_time, operate_user_id, is_show, busi_dir_type, sort_no)
VALUES('2c90e5a45deef37d015deef471810019', '我的空间', NULL, 'BusiDir', NULL, 'TRANS_DIR_MF', NULL, NULL, NULL, NULL, NULL, NULL, 'TRANS_DIR_MF', NULL);
-- SQL建模
INSERT INTO t_md_busi_dir (id, name, version, type, memo, code, owner_id, map_key, extended_type, operate_time, operate_user_id, is_show, busi_dir_type, sort_no) VALUES ('4028c8815ce83a80015ce83ad9a0022d', '我的空间', null, 'BusiDir', null, 'SQL_DATA_DIR', null, null, null, null, null, null, 'SQL_DATA_DIR', null);

-- ----------------------------
-- 通用算子以及流式算子目录树
-- ----------------------------
INSERT INTO "public"."t_md_busi_dir" (
	"id",
	"name",
	"version",
	"type",
	"memo",
	"code",
	"owner_id",
	"map_key",
	"extended_type",
	"operate_time",
	"operate_user_id",
	"is_show",
	"busi_dir_type",
	"sort_no"
)
VALUES
	(
		'b17b76efd6e24769887acfcf86f8b7d8',
		'服务编排算子目录',
		NULL,
		'BusiDir',
		NULL,
		'SERVICE_OPERATOR_DIR',
		NULL,
		NULL,
		NULL,
		NULL,
		NULL,
		NULL,
		'FEATURE_OPERATOR_DIR',
		'0'
	);


INSERT INTO "public"."t_md_busi_classify" (
	"id",
	"name",
	"version",
	"type",
	"memo",
	"code",
	"owner_id",
	"map_key",
	"extended_type",
	"operate_time",
	"operate_user_id",
	"busi_dir_id",
	"parent_classify_id",
	"sort_no",
	"res_count",
	"x",
	"y"
)
VALUES
	(
		'5d36116d1bf64e4f993ed3e5acd7528d',
		'通用算子',
		NULL,
		'BusiClassify',
		NULL,
		'AVIATOR_OPERATOR',
		NULL,
		NULL,
		NULL,
		NULL,
		NULL,
		'b17b76efd6e24769887acfcf86f8b7d8',
		NULL,
		NULL,
		NULL,
		NULL,
		NULL
	);


	INSERT INTO "public"."t_md_busi_dir" (
	"id",
	"name",
	"version",
	"type",
	"memo",
	"code",
	"owner_id",
	"map_key",
	"extended_type",
	"operate_time",
	"operate_user_id",
	"is_show",
	"busi_dir_type",
	"sort_no"
)
VALUES
	(
		'c18c87ffd6e24769887acfcf86f8c8e9',
		'流式算子目录',
		NULL,
		'BusiDir',
		NULL,
		'STREAM_ALL_DIR',
		NULL,
		NULL,
		NULL,
		NULL,
		NULL,
		NULL,
		'STREAM_ALL_DIR',
		'0'
	);


INSERT INTO "public"."t_md_busi_classify" (
	"id",
	"name",
	"version",
	"type",
	"memo",
	"code",
	"owner_id",
	"map_key",
	"extended_type",
	"operate_time",
	"operate_user_id",
	"busi_dir_id",
	"parent_classify_id",
	"sort_no",
	"res_count",
	"x",
	"y"
)
VALUES
	(
		'9g58666d1bf64e4f993ed3e5acd7539h',
		'流式算子',
		NULL,
		'BusiClassify',
		NULL,
		'STREAM_ALL',
		NULL,
		NULL,
		NULL,
		NULL,
		NULL,
		'c18c87ffd6e24769887acfcf86f8c8e9',
		NULL,
		NULL,
		NULL,
		NULL,
		NULL
	);


INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('********************************', '显示控件类型', NULL, 'DisPlayPluginType', NULL, 'DISPLAY_PLUGIN', NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('2e9e8dea5c1111e7907ba6006ad3db02', '文本框', NULL, 'DisPlayPluginType', NULL, 'INPUT', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('2e9e8dea5c1111e7907ba6006ad3db03', '下拉框', NULL, 'DisPlayPluginType', NULL, 'ComboBox', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('2e9e8dea5c1111e7907ba6006ad3db04', '时间选择框', NULL, 'DisPlayPluginType', NULL, 'TimeSelect', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('2e9e8dea5c1111e7907ba6006ad3db05', '时间范围框', NULL, 'DisPlayPluginType', NULL, 'TimeRang', '********************************', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('3ece6d4a5c0a11e707ba6006ad3dba01', 'SQL_OBJ数据类型', NULL, 'DataType', NULL, 'SQL_OBJ', NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('3ece6d4a5c0a11e707ba6006ad3dba02', 'SQL_OBJ.ArrayType', NULL, 'DataType', NULL, 'ArrayType', '3ece6d4a5c0a11e707ba6006ad3dba01', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('3ece6d4a5c0a11e707ba6006ad3dba03', 'SQL_OBJ.BinaryType', NULL, 'DataType', NULL, 'BinaryType', '3ece6d4a5c0a11e707ba6006ad3dba01', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('3ece6d4a5c0a11e707ba6006ad3dba04', 'SQL_OBJ.ByteType', NULL, 'DataType', NULL, 'ByteType', '3ece6d4a5c0a11e707ba6006ad3dba01', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('3ece6d4a5c0a11e707ba6006ad3dba05', 'SQL_OBJ.CalendarIntervalType', NULL, 'DataType', NULL, 'CalendarIntervalType', '3ece6d4a5c0a11e707ba6006ad3dba01', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('3ece6d4a5c0a11e707ba6006ad3dba06', 'SQL_OBJ.DecimalType', NULL, 'DataType', NULL, 'DecimalType', '3ece6d4a5c0a11e707ba6006ad3dba01', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('3ece6d4a5c0a11e707ba6006ad3dba07', 'SQL_OBJ.FloatType', NULL, 'DataType', NULL, 'FloatType', '3ece6d4a5c0a11e707ba6006ad3dba01', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('3ece6d4a5c0a11e707ba6006ad3dba08', 'SQL_OBJ.IntegerType', NULL, 'DataType', NULL, 'IntegerType', '3ece6d4a5c0a11e707ba6006ad3dba01', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('3ece6d4a5c0a11e707ba6006ad3dba09', 'SQL_OBJ.LongType', NULL, 'DataType', NULL, 'LongType', '3ece6d4a5c0a11e707ba6006ad3dba01', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('3ece6d4a5c0a11e707ba6006ad3dba10', 'SQL_OBJ.MapType', NULL, 'DataType', NULL, 'MapType', '3ece6d4a5c0a11e707ba6006ad3dba01', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('3ece6d4a5c0a11e707ba6006ad3dba11', 'SQL_OBJ.NullType', NULL, 'DataType', NULL, 'NullType', '3ece6d4a5c0a11e707ba6006ad3dba01', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('3ece6d4a5c0a11e707ba6006ad3dba12', 'SQL_OBJ.ShortType', NULL, 'DataType', NULL, 'ShortType', '3ece6d4a5c0a11e707ba6006ad3dba01', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('3ece6d4a5c0a11e707ba6006ad3dba13', 'SQL_OBJ.StringType', NULL, 'DataType', NULL, 'StringType', '3ece6d4a5c0a11e707ba6006ad3dba01', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('3ece6d4a5c0a11e707ba6006ad3dba14', 'SQL_OBJ.StructType', NULL, 'DataType', NULL, 'StructType', '3ece6d4a5c0a11e707ba6006ad3dba01', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('3ece6d4a5c0a11e707ba6006ad3dba15', 'SQL_OBJ.TimestampType', NULL, 'DataType', NULL, 'TimestampType', '3ece6d4a5c0a11e707ba6006ad3dba01', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('3ece6d4a5c0a11e707ba6006ad3dba16', 'SQL_OBJ.DateType', NULL, 'DataType', NULL, 'DateType', '3ece6d4a5c0a11e707ba6006ad3dba01', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('2e9e8dea5c1111e7907ba6006ad3dd03', '内存', NULL, 'SPARK_CACHE_LEVEL', NULL, 'MEMORY_ONLY', '2e9e8dea5c1111e7907ba6006ad3dd02', NULL, NULL, NULL, NULL);
INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('2e9e8dea5c1111e7907ba6006ad3dd04', '内存与磁盘', NULL, 'SPARK_CACHE_LEVEL', NULL, 'MEMORY_AND_DISK', '2e9e8dea5c1111e7907ba6006ad3dd02', NULL, NULL, NULL, NULL);


INSERT INTO "public"."t_md_element" ("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id") VALUES ('4028b8855c3e5aef015c3e5b95b42222', 'POSTGRESQL.BOOLEAN', NULL, 'DataType', NULL, 'BOOLEAN', '4028b8855c3e5aef015c3e5b95b401cc', NULL, NULL, NULL, NULL);


INSERT INTO t_md_busi_classify
(id, "name", "version", "type", memo, code, owner_id, map_key, extended_type, operate_time, operate_user_id, busi_dir_id, parent_classify_id, sort_no, res_count, x, y)
VALUES('92bebf124ws541d489623f98561fd41b', '金融信息', NULL, 'BusiClassify', NULL, 'finance', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 4, NULL, NULL, NULL);


-- 数据集初始化
INSERT INTO "public"."t_md_busi_dir"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id", "is_show", "busi_dir_type", "sort_no") VALUES ('a06a65efd6e2476987acfcf76fdwipq', '全部', NULL, 'BusiDir', '', 'DATA_SET_DIR', '', '', '', '', '', ' ', 'DATA_SET_DIR', NULL);






-- 可视化组件初始脚本
--INSERT INTO "t_v_widget_metas" VALUES ('767ec9c15a3d4bf8be7091d6561f9be8', '文本框', NULL, 'com.dragonsoft.cicada.datacenter.modules.datavisual.model.TextConditionWidget', NULL, 'TextConditionWidget', NULL, NULL, NULL, '2020-07-29 12:07:42', NULL, '2_1', NULL, 0);
INSERT INTO "t_v_widget_metas" VALUES ('4c704d5733d5442b8bdfcd42abd1541c', '文本', NULL, 'com.dragonsoft.cicada.datacenter.modules.datavisual.model.TextWidget', NULL, 'TextWidget', NULL, NULL, NULL, '2020-07-29 12:07:42', NULL, '3', NULL, 1);
INSERT INTO "t_v_widget_metas" VALUES ('06df1ef0e53d40fd90f4cb4204f87182', '查询', NULL, 'com.dragonsoft.cicada.datacenter.modules.datavisual.model.SelectWidget', NULL, 'SelectWidget', NULL, NULL, NULL, '2020-03-29 13:10:28', NULL, '2', NULL, 0);
INSERT INTO "t_v_widget_metas" VALUES ('454f52866211459ca85c7599ddc0073d', '表格', NULL, 'com.dragonsoft.cicada.datacenter.modules.datavisual.model.TableChartWidget', NULL, 'TableChartWidget', NULL, NULL, NULL, '2020-03-27 16:54:46', NULL, '1_4', NULL, 0);
INSERT INTO "t_v_widget_metas" VALUES ('e932b751471b4f49a4fba466c82d811b', '柱状图', NULL, 'com.dragonsoft.cicada.datacenter.modules.datavisual.model.BarChartWidget', NULL, 'BarChartWidget', NULL, NULL, NULL, '2020-03-26 17:23:39', NULL, '1_1', NULL, 0);
INSERT INTO "t_v_widget_metas" VALUES ('6547c66bbceb4f80b1cf470f7e99daec', '折线图', NULL, 'com.dragonsoft.cicada.datacenter.modules.datavisual.model.LineChartWidget', NULL, 'LineChartWidget', NULL, NULL, NULL, '2020-03-31 10:02:15', NULL, '1_2', NULL, 0);
--INSERT INTO "t_v_widget_metas" VALUES ('782ab83da4474567bc159d5217dd5cf6', '地图', NULL, 'com.dragonsoft.cicada.datacenter.modules.datavisual.model.MapChartWidget', NULL, 'MapChartWidget', NULL, NULL, NULL, '2020-04-03 08:50:50', NULL, '1_5', NULL, 0);
INSERT INTO "t_v_widget_metas" VALUES ('3fb056d0c1d34bd18aba6b2471c9bb61', '饼图', NULL, 'com.dragonsoft.cicada.datacenter.modules.datavisual.model.PieChartWidget', NULL, 'PieChartWidget', NULL, NULL, NULL, '2020-03-29 13:10:28', NULL, '1_3', NULL, 0);

INSERT INTO "public"."t_v_widget_categories"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id", "parent_id") VALUES ('1', '图表', NULL, 'com.code.metadata.datavisual.WidgetCategories', NULL, 'com.code.metadata.datavisual.WidgetCategories', NULL, NULL, NULL, '2020-03-25 09:23:55', NULL, NULL);
INSERT INTO "public"."t_v_widget_categories"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id", "parent_id") VALUES ('1_1', '柱状图', NULL, 'com.code.metadata.datavisual.WidgetCategories', NULL, 'com.code.metadata.datavisual.WidgetCategories', NULL, NULL, NULL, '2020-03-25 09:23:56', NULL, '1');
INSERT INTO "public"."t_v_widget_categories"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id", "parent_id") VALUES ('1_2', '折线图', NULL, 'com.code.metadata.datavisual.WidgetCategories', NULL, 'com.code.metadata.datavisual.WidgetCategories', NULL, NULL, NULL, '2020-03-25 09:23:56', NULL, '1');
INSERT INTO "public"."t_v_widget_categories"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id", "parent_id") VALUES ('1_3', '饼图', NULL, 'com.code.metadata.datavisual.WidgetCategories', NULL, 'com.code.metadata.datavisual.WidgetCategories', NULL, NULL, NULL, '2020-03-25 09:23:56', NULL, '1');
INSERT INTO "public"."t_v_widget_categories"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id", "parent_id") VALUES ('1_4', '表格', NULL, 'com.code.metadata.datavisual.WidgetCategories', NULL, 'com.code.metadata.datavisual.WidgetCategories', NULL, NULL, NULL, '2020-03-25 09:23:56', NULL, '1');
-- INSERT INTO "public"."t_v_widget_categories"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id", "parent_id") VALUES ('1_5', '地图', NULL, 'com.code.metadata.datavisual.WidgetCategories', NULL, 'com.code.metadata.datavisual.WidgetCategories', NULL, NULL, NULL, '2020-03-25 09:23:56', NULL, '1');
INSERT INTO "public"."t_v_widget_categories"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id", "parent_id") VALUES ('2', '查询', NULL, 'com.code.metadata.datavisual.WidgetCategories', NULL, 'com.code.metadata.datavisual.WidgetCategories', NULL, NULL, NULL, '2020-03-25 09:23:57', NULL, NULL);
INSERT INTO "public"."t_v_widget_categories"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id", "parent_id") VALUES ('2_1', '条件', NULL, 'com.code.metadata.datavisual.WidgetCategories', NULL, 'com.code.metadata.datavisual.WidgetCategories', NULL, NULL, NULL, '2020-03-25 09:23:57', NULL, '2');
INSERT INTO "public"."t_v_widget_categories"("id", "name", "version", "type", "memo", "code", "owner_id", "map_key", "extended_type", "operate_time", "operate_user_id", "parent_id") VALUES ('3', '小控件', NULL, 'com.code.metadata.datavisual.WidgetCategories', NULL, 'com.code.metadata.datavisual.WidgetCategories', NULL, NULL, NULL, '2020-03-25 09:23:57', NULL, NULL);



