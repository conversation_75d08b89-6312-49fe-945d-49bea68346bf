package com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.qo;

import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.vo.LogicDataSet.PreviewVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@Data
@ApiModel(value = "预览Excel导出请求类")
public class LogicDataExportQo extends PreviewVo {


    @ApiModelProperty(value = "文件名称")
    private String excelName;


    @ApiModelProperty(value = "条数")
    private Integer excelLength;


}
