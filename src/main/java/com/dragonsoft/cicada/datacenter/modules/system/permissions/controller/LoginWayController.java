package com.dragonsoft.cicada.datacenter.modules.system.permissions.controller;

import com.dragoninfo.dfw.bean.Result;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * <AUTHOR>
 * @Date: 2021/09/22/下午2:27
 */

@Controller
@CrossOrigin
@RequestMapping("/loginWay")
@PropertySource("classpath:case-config.properties")
public class LoginWayController {

    @Value("${accessThirdSystem}")
    private String accessThirdSystem;


    /**
     * 获取登录系统的登录方式
     * @return
     */
    @ResponseBody
    @RequestMapping("/getSystemLoginWay")
    public Result getSystemLoginWay() {
        return Result.success(accessThirdSystem);
    }

}
