package com.dragonsoft.cicada.datacenter.modules.modeling.aimodeling.vo;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description：评估指标结果项
 * @date ：2021/10/9 10:56
 */
public class EvaluateRstItem implements Serializable {

    public EvaluateRstItem() {
    }

    public EvaluateRstItem(String name, String code, EnumTaskType taskType, EnumDataType dataType, Object data) {
        this.name = name;
        this.code = code;
        this.taskType = taskType;
        this.dataType = dataType;
        this.data = data;
    }

    /**
     * 中文名
     */
    private String name;
    /**
     * 英文名
     */
    private String code;
    /**
     * 任务类型
     */
    private EnumTaskType taskType;
    /**
     * 数据类型
     */
    private EnumDataType dataType;

    /**
     * 数据值
     */
    private Object data;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public EnumTaskType getTaskType() {
        return taskType;
    }

    public void setTaskType(EnumTaskType taskType) {
        this.taskType = taskType;
    }

    public EnumDataType getDataType() {
        return dataType;
    }

    public void setDataType(EnumDataType dataType) {
        this.dataType = dataType;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }
}
