package com.dragonsoft.cicada.datacenter.modules.metadata.service;

import com.code.metadata.res.semistructured.kafka.KafkaInstance;
import com.code.metaservice.datawarehouse.model.DataSourceVO;
import com.code.metaservice.res.request.datasource.DataSourceRequest;
import com.code.metaservice.res.response.datasource.InsertDataSourceResponse;
import com.code.metaservice.res.semistructured.kafka.IKafkaInstanceService;
import com.fw.service.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR> Avizii
 * @Create : 2021.03.29
 */
@Service(mappingName = "kafkaConnectService")
public class KafkaConnectService extends DataBaseConnectService {

    @Autowired
    private IKafkaInstanceService kafkaInstanceService;

    @Override
    public DataSourceVO getDataSourceInfo(String id) {
        KafkaInstance kafkaInstance = (KafkaInstance) this.baseDao.get(KafkaInstance.class, id);
        DataSourceVO dataSourceVO = new DataSourceVO();
        dataSourceVO.setZookeeperId(kafkaInstance.getZk_id());
        dataSourceVO.setIp(kafkaInstance.getBroker_lists());
        return dataSourceVO;
    }

    @Override
    public boolean testConnection(DataSourceVO dataSourceVO) {
        DataSourceRequest dataSourceRequest = new DataSourceRequest();
        dataSourceRequest.setIp(dataSourceVO.getIp());
        dataSourceRequest.setPort(dataSourceVO.getPort());
        dataSourceRequest.setPassword(dataSourceVO.getPassword());
//        return kafkaInstanceService.testConnection(dataSourceRequest);
        return true;
    }

    @Override
    public InsertDataSourceResponse insertDataSource(DataSourceVO dataSourceVO) {
        DataSourceRequest dataSourceRequest = toDataSourceRequest(dataSourceVO);
        return kafkaInstanceService.insertResource(dataSourceRequest);
    }

    @Override
    public void updateResource(DataSourceVO dataSourceVO) {
        kafkaInstanceService.updateResourceInfo(toDataSourceRequest(dataSourceVO));
    }

}
