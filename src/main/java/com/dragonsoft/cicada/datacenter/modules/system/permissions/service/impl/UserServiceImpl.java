package com.dragonsoft.cicada.datacenter.modules.system.permissions.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.util.StrUtil;
import com.code.common.paging.PageInfo;
import com.code.common.utils.BeanUtils;
import com.code.metadata.business.directory.BaseBusiClassify;
import com.code.metadata.business.directory.BaseBusiDir;
import com.code.metadata.business.directory.BusiDir;
import com.code.metaservice.business.directory.BusiClassifyService;
import com.code.metaservice.business.directory.BusiDirService;
import com.code.metaservice.standmb.IStandCodeValService;
import com.dragoninfo.dfw.entity.*;
import com.dragoninfo.dfw.enums.BusiErrorCode;
import com.dragoninfo.dfw.exception.BusiException;
import com.dragoninfo.dfw.service.*;
import com.dragoninfo.dfw.utils.ExceptionUtil;
import com.dragonsoft.cicada.datacenter.common.token.GlobalConstant;
import com.dragonsoft.cicada.datacenter.common.utils.CastUtils;
import com.dragonsoft.cicada.datacenter.common.utils.EncryptionUtils;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.service.IDataSetOperationService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.externaldatasources.service.IDataSetAuthService;
import com.dragonsoft.cicada.datacenter.modules.firstpage.share.service.DataShareAuthRelService;
import com.dragonsoft.cicada.datacenter.modules.modeling.service.DataModelingService;
import com.dragonsoft.cicada.datacenter.modules.system.common.vo.TreeVo;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.service.IUserGroupsService;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.service.IUserService;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.util.UserUtil;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.vo.GroupUserTreeVo;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.vo.PageVo;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.vo.UserVo;
import com.dragonsoft.dids.client.sys.DidsServiceLocator;
import com.fw.dao.IDao;
import com.fw.service.DfwBaseService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.io.FileInputStream;
import java.io.IOException;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2020/5/21
 */
@Slf4j
@Service
@Transactional
public class UserServiceImpl implements IUserService {

    public static final String ZERO = "0";
    public static final String ONE = "1";
    public static final String TWO = "2";
    public static final String YH = "YH";
    public static final String ID = "id";
    public static final String OBJCODE = "objCode";
    public static final String PASSWORD = "password";
    public static final String OBJTYPE = "objType";
    public static final String ADMIN_CODE = "dc_super";
    public static final String FROM_OBJ_ID = "from_obj_id";
    public static final String OBJ_ID = "obj_id";
    public static final String BASE_BUSI_CLASSIFY = "BaseBusiClassify";


    @Autowired
    private SysAuthUserService sysAuthUserService;
    @Autowired
    private SysAuthObjRelService sysAuthObjRelService;
    @Autowired
    private SysAuthObjService sysAuthObjService;
    @Autowired
    private SysOperateLogService sysOperateLogService;
    @Autowired
    private SysAuthGroupService sysAuthGroupService;
    @Autowired
    private SysAuthRoleService sysAuthRoleService;
    @Autowired
    private IUserGroupsService userGroupsService;
    @Autowired
    private SysAuthObjFuncService sysAuthObjFuncService;
    @Autowired
    private DataModelingService dataModelingService;
    @Autowired
    private BusiClassifyService busiClassifyService;
    @Autowired
    private IStandCodeValService standCodeValService;
    @Autowired
    private IDataSetOperationService dataSetOperationService;
    @Autowired
    private IDataSetAuthService dataSetAuthService;
    @Autowired
    private BusiDirService busiDirService;
    @Autowired
    private DataShareAuthRelService dataShareAuthRelService;

    @Autowired
    private DfwBaseService dfwBaseService;


    public static final String DATA_CONNECTION_DATA_WARE_HOUSE = "dataConnectionDataWarehouse";

    @Override
    public TSysAuthUser login(UserVo userVo) {
        Map<String, String> params = Maps.newHashMap();
        params.put(OBJCODE, userVo.getObjCode());
        TSysAuthUser tSysAuthUser = (TSysAuthUser) sysAuthUserService.query(params);
        if (null == tSysAuthUser || StringUtils.isEmpty(tSysAuthUser.getObjCode())) {
            throw new BusiException(BusiErrorCode.NOT_USER);
        }
        if (!UserUtil.encoderByMd5(userVo.getPassword()).equals(tSysAuthUser.getPassword())) {
            throw new BusiException(BusiErrorCode.WRONG_PASSWORD);
        }
        if (ONE.equals(tSysAuthUser.getEnableState())) {
            throw new BusiException(BusiErrorCode.USER_LOGIN_ERROR);
        }
        return tSysAuthUser;
    }

    @Override
    public void updataPassword(String userId, String passWord) {
        Map<String, String> params = Maps.newHashMap();
        params.put(ID, userId);
        TSysAuthUser tSysAuthUser = (TSysAuthUser) sysAuthUserService.query(params);
        tSysAuthUser.setPassword(UserUtil.encoderByMd5(passWord));
        tSysAuthUser.setLoginNumber(0);
        sysAuthObjService.updateAuthObj(tSysAuthUser);
    }

    @Override
    public UserVo queryUserById(String userId) {
        Map<String, String> params = Maps.newHashMap();
        params.put(ID, userId);
        TSysAuthUser tSysAuthUser = (TSysAuthUser) sysAuthUserService.query(params);
        UserVo userVo = new UserVo();
        userVo.setId(tSysAuthUser.getId());
        userVo.setObjCode(tSysAuthUser.getObjCode());
        userVo.setObjName(tSysAuthUser.getObjName());
        Set<TSysAuthObjRel> tSysAuthObjRels = tSysAuthUser.gettSysAuthObjRelSet();
        //获取组对象
        List<TSysAuthObj> groupTSysAuthObjs = tSysAuthObjRels.stream()
                .map(TSysAuthObjRel::getToAuthObj)
                .filter(toAuthObj -> toAuthObj.getObjCode().contains("YHZ"))
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(groupTSysAuthObjs)) {
            userVo.setBelongGroup(groupTSysAuthObjs.get(0));
        }
        userVo.setEmail(tSysAuthUser.getEmail());
        userVo.setPhone(tSysAuthUser.getPhone());
        //获取角色对象
        List<TSysAuthObj> roleTSysAuthObjs = tSysAuthObjRels.stream().map(TSysAuthObjRel::getToAuthObj)
                .filter(toAuthObj -> toAuthObj.getObjCode().contains("JS")).collect(Collectors.toList());
        userVo.setRoles(roleTSysAuthObjs);
        userVo.setEnableState(tSysAuthUser.getEnableState());
        userVo.setLoginNumber(tSysAuthUser.getLoginNumber());
        return userVo;
    }

    @Override
    public List<TSysFuncBase> queryUserFunction(String userId) {
        Map<String, String> params = Maps.newHashMap();
        params.put(ID, userId);
        TSysAuthUser tSysAuthUser = (TSysAuthUser) this.sysAuthUserService.query(params);
        List<TSysAuthObjRel> tSysAuthObjRels = new ArrayList<>(tSysAuthUser.gettSysAuthObjRelSet());
        //获取人-角色关系
        List<TSysAuthObjRel> roleTSysAuthObjRels = tSysAuthObjRels.stream().filter(s -> ONE.equals(s.getRelationType())).collect(Collectors.toList());
        //获取所有角色
        List<String> roleIds = roleTSysAuthObjRels.stream().map(s -> s.getToAuthObj().getId()).collect(Collectors.toList());
        List<TSysFuncBase> functions = new ArrayList<>();
        for (String roleId : roleIds) {
            Map<String, String> roleParams = Maps.newHashMap();
            roleParams.put(ID, roleId);
            TSysAuthRole tSysAuthRole = (TSysAuthRole) sysAuthRoleService.query(roleParams);
            List<TSysFuncBase> funcs = tSysAuthRole.gettSysAuthObjFuncSet().stream().map(TSysAuthObjFunc::gettSysFuncBase)
                    .filter(s -> ZERO.equals(s.getFuncType().trim()) && ONE.equals(s.getEnableState().trim()))
                    .collect(Collectors.toList());
            functions.addAll(funcs);
        }
        List<TSysFuncBase> collect = functions.stream().distinct().collect(Collectors.toList());
        return filterDataWareHouse(collect, userId);
    }


    @Override
    public String getUserRandom() {
        return UserUtil.getRandom(Lists.newArrayList(), YH);
    }

    @Override
    public PageInfo queryUsersPageByCodeOrName(PageVo pageVo) {
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageIndex(pageVo.getPageNum());
        pageInfo.setPageSize(pageVo.getPageSize());
        return sysAuthUserService.queryBlurryPage(UserUtil.setPageParams(pageVo), pageInfo);
    }

    @Override
    public void batchAddUser(String batchUserFilePath) {
        try (FileInputStream inputStream = new FileInputStream(batchUserFilePath)) {
            Workbook workbook = null;
            workbook = new XSSFWorkbook(inputStream);

            Sheet sheet = workbook.getSheetAt(0);

            int rowStart = 1; // 从第二行开始
            for (int i = rowStart; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                UserVo userVo = new UserVo();
                Assert.hasText(row.getCell(0).getStringCellValue(), "账号不能为空");
                userVo.setObjCode(row.getCell(0).getStringCellValue().trim().replace("'", ""));
                Assert.hasText(row.getCell(1).getStringCellValue(), "用户名不能为空");
                userVo.setObjName(row.getCell(1).getStringCellValue().trim().replace("'", ""));
                Assert.hasText(row.getCell(2).getStringCellValue(), "身份证号码不能为空");
                userVo.setCertificateNumber(row.getCell(2).getStringCellValue().trim().replace("'", ""));
                userVo.setEnableState("0");
                userVo.setPassword("0FA00516C7FA7350DED84B110C438F04");
                TSysAuthObj tSysAuthObj = new TSysAuthObj();
                tSysAuthObj.setId("26321b75c339489e9396d81bc558b26f");
                userVo.setBelongGroup(tSysAuthObj);
                TSysAuthObj role = new TSysAuthObj();
                role.setId("9a5502cef25f4b31ba150bbda8d6c34a");
                List<TSysAuthObj> list = new ArrayList<>();
                list.add(role);
                userVo.setRoles(list);
                addUser(userVo);
            }

            workbook.close();
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }

    }


    @Override
    public void addUser(UserVo userVo) {
        //检查对象代码
        checkUserCode(userVo);
        TSysAuthUser tSysAuthUser = new TSysAuthUser();
        BeanUtils.simpleCopyProperties(userVo, tSysAuthUser);
        tSysAuthUser.setPassword(UserUtil.encoderByMd5(userVo.getPassword()));
        tSysAuthUser.setLoginNumber(0);
        tSysAuthUser.setObjType(ZERO);
        tSysAuthUser.setEnableState(userVo.getEnableState());
        Timestamp d = new Timestamp(System.currentTimeMillis());
        tSysAuthUser.setCreateTime(d);
        tSysAuthUser.setUpdateTime(d);
        tSysAuthUser.setPhone(userVo.getPhone());
        tSysAuthUser.setEmail(userVo.getEmail());
        tSysAuthUser.setCertificateNumber(userVo.getCertificateNumber());
        sysAuthUserService.store(tSysAuthUser);
        List<String> groupIds = new ArrayList<>();
        groupIds.add(userVo.getBelongGroup().getId());
        List<String> roleIs = userVo.getRoles().stream().map(TSysAuthObj::getId).collect(Collectors.toList());
        this.saveRelation(roleIs, tSysAuthUser, ONE);
        this.saveRelation(groupIds, tSysAuthUser, ZERO);
        //分享逻辑已修改 查询来自分享的数据集时先查询该用户所属角色授权的数据集 不需要再新增数据
        //this.saveAccreditDatasetToUser(roleIs, tSysAuthUser);
        this.saveRapidAnaDirToUser(tSysAuthUser);
        //保存 我的服务目录
        saveClassifyByCondition("我的服务", "TRANS_SERVICE_CLASSIFY", BASE_BUSI_CLASSIFY, "TRANS_SERVICE_DIR", tSysAuthUser.getId());
        //新增用例目录
        saveClassifyByCondition("全部用例", "USE_CASE_API_CLASSIFY", BASE_BUSI_CLASSIFY, "USE_CASE_API_DIR", tSysAuthUser.getId());
    }

    private void checkUserCode(UserVo userVo) {
        Map<String, String> params = Maps.newHashMap();
        params.put("objCode", userVo.getObjCode());
        List<TSysAuthUser> tSysAuthUsers = sysAuthUserService.queryList(params);
        if (CollUtil.isNotEmpty(tSysAuthUsers)) {
            throw new BusiException(BusiErrorCode.USER_ACCOUNT_EXISTED);
        }
    }

    //新增用户时新增该用户的快速分析目录
    private void saveRapidAnaDirToUser(TSysAuthUser tSysAuthUser) {
        BaseBusiClassify baseBusiClassify = new BaseBusiClassify();
        baseBusiClassify.setName("我的空间");
        baseBusiClassify.setType(BASE_BUSI_CLASSIFY);
        baseBusiClassify.setCode("TRANS_RAPID_DIR_MF_MY");
        Date date = new Date();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        baseBusiClassify.setOperateTime(format.format(date));
        baseBusiClassify.setOperateUserId(tSysAuthUser.getId());
        Map<String, Object> map = new HashMap<>();
        map.put("code", "TRANS_RAPID_DIR_MF");
        BaseBusiDir baseBusiDir = (BaseBusiDir) busiDirService.queryObj("from BaseBusiDir where code = :code", map);
        baseBusiClassify.setBusiDir(baseBusiDir);
        busiClassifyService.saveBaseBusiClassify(baseBusiClassify);
    }

    @Override
    public void saveClassifyByCondition(String classifyName, String classifyCode, String type, String parentDirCode, String userId) {
        BaseBusiClassify baseBusiClassify = new BaseBusiClassify();
        baseBusiClassify.setName(classifyName);
        baseBusiClassify.setType(type);
        baseBusiClassify.setCode(classifyCode);
        Date date = new Date();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        baseBusiClassify.setOperateTime(format.format(date));
        baseBusiClassify.setOperateUserId(userId);
        Map<String, Object> map = new HashMap<>();
        map.put("code", parentDirCode);
        BaseBusiDir baseBusiDir = (BaseBusiDir) busiDirService.queryObj("from BaseBusiDir where code = :code", map);
        if (baseBusiDir == null) {
            log.warn("不存在该父目录！");
            return;
        }
        baseBusiClassify.setBusiDir(baseBusiDir);
        busiClassifyService.saveBaseBusiClassify(baseBusiClassify);
    }

    @Override
    public boolean isAdmin(String userId) {
        Map<String, String> map = new HashMap<>();
        map.put("relation_type", "1");
        map.put(FROM_OBJ_ID, userId);
        map.put("to_obj_id", GlobalConstant.UserProperties.ADMIN_ROLE_ID);
        List<TSysAuthObjRel> list = sysAuthObjRelService.queryList(map);
        return CollUtil.isNotEmpty(list);
    }

    @Override
    public Map<String, String> getUserMap(List<String> userIds) {
        Map<String, String> userMap = new HashMap<>();
        if (CollUtil.isNotEmpty(userIds)) {
            for (String id : userIds) {
                Map<String, String> param = new HashMap<>();
                param.put("id", id);
                TSysAuthObj users = (TSysAuthObj) sysAuthObjService.query(param);
                if (Objects.nonNull(users)) {
                    userMap.put(users.getId(), users.getObjName());
                }
            }
        }
        return userMap;
    }

    private void saveDataConnectToUser(TSysAuthUser tSysAuthUser) {
        if (!GlobalConstant.UserProperties.DC_SUPER_ID.equals(tSysAuthUser.getId())) {
            BusiDir busiDir = new BusiDir();
            busiDir.setName("我的空间");
            busiDir.setType("BusiDir");
            busiDir.setCode("DATAWAREHOUSE_DIR");
            busiDir.setBusiDirType("DATAWAREHOUSE_DIR");
            busiDir.setOperateUserId(tSysAuthUser.getId());
            busiDirService.saveDir(busiDir);
        }

    }

    private void saveAccreditDatasetToUser(List<String> roleIs, TSysAuthUser tSysAuthUser) {
        Set<String> allAuthFunctionList = getAuthFunctionIdByRoleIds(roleIs);
        //拿到每个角色授权的数据集
        for (String classifierStatId : allAuthFunctionList) {
            Integer num = this.userAuthDatasetNumber(tSysAuthUser.getId(), classifierStatId);
            if (num == 0) {
                String dataSetDbType = dataSetOperationService.getDataSetDbType(classifierStatId);
                if (!"".equals(dataSetDbType) && !"ElasticSearch".equalsIgnoreCase(dataSetDbType)
                        && !"Hbase".equalsIgnoreCase(dataSetDbType) && !"FILE".equalsIgnoreCase(dataSetDbType)) {
                    List<String> codeList = new ArrayList<>();
                    codeList.add(classifierStatId);
                    try {
                        dataSetAuthService.saveDataSetRelation(tSysAuthUser, codeList);
                    } catch (Exception e) {
                        log.warn("新增用户授权数据集失败:" + e.getMessage(), e);
                    }
                }
            }
        }
    }


    /**
     * 授权数据集
     *
     * @param roleIs
     * @param userId
     */
    private void saveAccreditDataSet(List<String> roleIs, String userId) {
        Set<String> allAuthFunctionList = getAuthFunctionIdByRoleIds(roleIs);
        if (CollUtil.isEmpty(allAuthFunctionList)) {
            return;
        }
        for (String classifierStatId : allAuthFunctionList) {
            Integer num = this.userAuthDatasetNumber(userId, classifierStatId);
            if (num == 0) {
                saveAccreditDataSet(userId, classifierStatId);
            }
        }
    }

    private Set<String> getAuthFunctionIdByRoleIds(List<String> roleIds) {
        Set<String> allAuthFunctionList = new HashSet<>();
        for (String roleId : roleIds) {
            allAuthFunctionList.addAll(this.getAllAuthDatasetId(roleId));
        }
        return allAuthFunctionList;
    }

    private void saveAccreditDataSet(String userId, String classifierStatId) {
        String dataSetDbType = dataSetOperationService.getDataSetDbType(classifierStatId);
        if (!"".equals(dataSetDbType) && !"ElasticSearch".equalsIgnoreCase(dataSetDbType)
                && !"Hbase".equalsIgnoreCase(dataSetDbType) && !"FILE".equalsIgnoreCase(dataSetDbType)) {
            try {
                String dbId = dataSetOperationService.getDwDbIdByClassifierStatId(classifierStatId);
                if (StrUtil.isBlank(dbId)) {
                    log.warn("====> classifierStatId={} dbId is blank", classifierStatId);
                    return;
                }
                //物理表转换成logic表
                dataSetOperationService.accreditDataSet(Lists.newArrayList(classifierStatId), dbId, userId);
            } catch (Exception e) {
                log.warn("新增用户授权数据集失败:" + e.getMessage(), e);
            }
        }
    }

    /**
     * 删除授权数据集
     *
     * @param roleIds
     * @param userId
     */
    private void deleteAlreadyAccreditDataSet(List<String> roleIds, String userId) {
        if (CollUtil.isEmpty(roleIds)) {
            return;
        }
        Set<String> allAuthFunctionList = getAuthFunctionIdByRoleIds(roleIds);
        //拿到每个角色授权的数据集
        for (String code : allAuthFunctionList) {
            dataSetAuthService.deleteAccreditDataSet(code, userId);
        }
    }

    @Override
    public void updateUser(UserVo userVo) {
        Map<String, TSysAuthObjRel> historyAuthObjMap = getTSysAuthObjRelMapByUserId(userVo.getId());
        Map<String, List<String>> resultMap = roleIdSetComparator(historyAuthObjMap, userVo.getRoles().stream()
                .map(TSysAuthObj::getId)
                .collect(Collectors.toList()));
        List<String> deleteRoleList = resultMap.get("delete");
        deleteAuthObjRel(deleteRoleList, historyAuthObjMap);

        //删除原本的转换的逻辑表
        this.deleteAlreadyAccreditDataSet(deleteRoleList, userVo.getId());
        TSysAuthUser tSysAuthUser = updateSysAuthUser(userVo);
        List<String> roleIs = resultMap.get("add");
        List<String> groupIds = new ArrayList<>();
        groupIds.add(userVo.getBelongGroup().getId());

        //保存用户 组关系
        this.saveRelation(groupIds, tSysAuthUser, ZERO);
        // 保存用户 角色关系 0-用户组，1-用户角色，2-组角色
        this.saveRelation(roleIs, tSysAuthUser, ONE);
        this.saveAccreditDataSet(roleIs, tSysAuthUser.getId());
    }

    @NotNull
    private Map<String, TSysAuthObjRel> getTSysAuthObjRelMapByUserId(String id) {
        Map<String, String> relationParams = Maps.newHashMap();
        relationParams.put(FROM_OBJ_ID, id);
        //查询历史角色
        List<TSysAuthObjRel> sysAuthObjRels = sysAuthObjRelService.queryList(relationParams);
        Map<String, TSysAuthObjRel> historyAuthObjMap = Maps.newHashMap();
        for (TSysAuthObjRel tSysAuthObjRel : sysAuthObjRels) {
            if (tSysAuthObjRel.getRelationType().equals("1")) {
                historyAuthObjMap.put(tSysAuthObjRel.getToAuthObj().getId(), tSysAuthObjRel);
            } else {
                sysAuthObjService.deleteAuthObj(tSysAuthObjRel);
            }
        }
        return historyAuthObjMap;
    }

    private void deleteAuthObjRel(List<String> deleteRoleList, Map<String, TSysAuthObjRel> historyAuthObjMap) {
        if (CollUtil.isEmpty(deleteRoleList)) {
            return;
        }
        for (String roleId : deleteRoleList) {
            TSysAuthObjRel tSysAuthObjRel = historyAuthObjMap.get(roleId);
            if (tSysAuthObjRel != null) {
                sysAuthObjService.deleteAuthObj(tSysAuthObjRel);
            }
        }
    }

    private Map<String, List<String>> roleIdSetComparator(Map<String, TSysAuthObjRel> historyAuthObjMap, List<String> fromUpdateRoleIds) {
        Map<String, List<String>> result = Maps.newHashMap();
        List<String> addList = Lists.newArrayList();
        List<String> deleteRoleList = Lists.newArrayList();
        result.put("delete", deleteRoleList);
        result.put("add", addList);
        for (Map.Entry<String, TSysAuthObjRel> entry : historyAuthObjMap.entrySet()) {
            if (!fromUpdateRoleIds.contains(entry.getKey())) {
                deleteRoleList.add(entry.getKey());
            }
        }
        for (String roleId : fromUpdateRoleIds) {
            if (!historyAuthObjMap.containsKey(roleId)) {
                addList.add(roleId);
            }
        }

        return result;
    }

    private TSysAuthUser updateSysAuthUser(UserVo userVo) {
        Map<String, String> params = Maps.newHashMap();
        params.put(ID, userVo.getId());
        TSysAuthUser tSysAuthUser = (TSysAuthUser) sysAuthUserService.query(params);
        tSysAuthUser.setObjName(userVo.getObjName());
        tSysAuthUser.setEmail(userVo.getEmail());
        tSysAuthUser.setPhone(userVo.getPhone());
        tSysAuthUser.setEnableState(userVo.getEnableState());
        tSysAuthUser.setCertificateNumber(userVo.getCertificateNumber());
        Timestamp d = new Timestamp(System.currentTimeMillis());
        tSysAuthUser.setUpdateTime(d);
        sysAuthObjService.updateAuthObj(tSysAuthUser);
        return tSysAuthUser;
    }

    @Override
    public String deleteUser(String userId) {
        Map<String, String> logParams = Maps.newHashMap();
        logParams.put("VISIT_ID", userId);
        List<TSysOperateLog> tSysOperateLogs = sysOperateLogService.queryList(logParams);
        List<TSysOperateLog> notQuerySysOperateLogs = tSysOperateLogs.stream().filter(s -> s.getVisitUrl().indexOf("query") == -1).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(notQuerySysOperateLogs)) {
            return "此用户有操作纪录不能删除";
        } else {
            for (TSysOperateLog tSysOperateLog : tSysOperateLogs) {
                sysAuthObjService.deleteAuthObj(tSysOperateLog);
            }
            Map<String, String> params = Maps.newHashMap();
            params.put(ID, userId);
            TSysAuthUser tSysAuthUser = (TSysAuthUser) sysAuthUserService.query(params);
            for (TSysAuthObjRel t : tSysAuthUser.gettSysAuthObjRelSet()) {
                sysAuthObjService.deleteAuthObj(t);
            }
            // 删除用户-数据源关系
            Map<String, String> functionParams = Maps.newHashMap();
            functionParams.put(OBJ_ID, userId);
            List<TSysAuthObjFunc> tSysAuthObjFuncs = sysAuthObjFuncService.queryList(functionParams);
            for (TSysAuthObjFunc tSysAuthObjFunc : tSysAuthObjFuncs) {
                sysAuthObjService.deleteAuthObj(tSysAuthObjFunc);
            }
            //删除用户
            sysAuthObjService.deleteAuthObj(tSysAuthUser);
            return "删除成功";
        }
    }

    @Override
    public String updataPassword(String userCode, String oldPassword, String newPassword) {
        Map<String, String> params = Maps.newHashMap();
        params.put(OBJCODE, userCode);
        TSysAuthUser tSysAuthUser = (TSysAuthUser) sysAuthUserService.query(params);
        if (null == tSysAuthUser) {
            ExceptionUtil.throwBusiException(BusiErrorCode.NOT_OR_WRONG);
        } else if (!tSysAuthUser.getPassword().equals(UserUtil.encoderByMd5(oldPassword))) {
            throw new BusiException(BusiErrorCode.WRONG_PASSWORD);
        }
        tSysAuthUser.setPassword(UserUtil.encoderByMd5(newPassword));
        sysAuthObjService.updateAuthObj(tSysAuthUser);
        return tSysAuthUser.getId();
    }

    @Override
    public void updataLoginNumber(String userId) {
        Map<String, String> params = Maps.newHashMap();
        params.put(ID, userId);
        TSysAuthUser tSysAuthUser = (TSysAuthUser) sysAuthUserService.query(params);

        //List<String> classifyIds = busiClassifyService.getClassifyIdsByUserId(userId);
        //if (0 == tSysAuthUser.getLoginNumber() && classifyIds.size() == 0) {
        if (0 == tSysAuthUser.getLoginNumber()) {
            //构建属于当前用户的 流程建模-我的空间 目录
            dataModelingService.createTransClassifyForMy(null, "我的空间", "TRANS_DIR_MF_MY", "TRANS_DIR_MF", userId);
            //构建属于当前用户的 数据集-我的空间 目录
            dataModelingService.createTransClassifyForMy(null, "我的空间", "DATASET_DIR_MY", "DATA_SET_DIR", userId);
            //构建属于当前用户的 数据挖掘-我的空间 目录
            dataModelingService.createTransClassifyForMy(null, "我的空间", "DIG_DIR_MF_MY", "DIG_DIR_MF", userId);
            //构建门户当前用户的 门户-我的空间 目录
            dataModelingService.createTransClassifyForMy(null, "我的空间", "PORTAL_DIR_MY", "PORTAL_DIR", userId);
            //构建属于当前用户的 AI建模-我的空间 目录
            dataModelingService.createTransClassifyForMy(null, "我的空间", "TRANS_AI_DIR_MF_MY", "TRANS_AI_DIR_MF", userId);
            saveDataConnectToUser(tSysAuthUser);
        }

        tSysAuthUser.setLoginNumber(tSysAuthUser.getLoginNumber() + 1);
        sysAuthObjService.updateAuthObj(tSysAuthUser);
    }


    @Override
    public List<GroupUserTreeVo> queryAllUser() {
        TimeInterval timeInterval = new TimeInterval();
        List<TreeVo> groupTree = userGroupsService.queryAllUserGroups("");
        log.info("--->>> 获取所有用户处理用户组共耗时{}ms", timeInterval.intervalRestart());
        Map<String, List<UserVo>> userGroupByPidMap = getUserGroupMap();
        List<GroupUserTreeVo> list = this.groupUserTreeVOS(userGroupByPidMap, groupTree);
        log.info("--->>> 获取所有用户处理结果共耗时{}ms", timeInterval.intervalRestart());
        return list;
    }

    private Map<String, List<UserVo>> getUserGroupMap() {
        String sql = "select tsao.id,tsao.obj_code ,tsao.obj_name,tsaor.to_obj_id pid from t_sys_auth_obj tsao \n" +
                "inner join t_sys_auth_obj_rel tsaor on tsao.id =tsaor.from_obj_id  \n" +
                "where tsaor.relation_type='0' and tsao.enable_state ='0'";
        List<Map<String, String>> mapList = CastUtils.cast(dfwBaseService.getDfwBaseDao().sqlQueryForList(sql));
        List<UserVo> list = new ArrayList<>();
        if (CollUtil.isNotEmpty(mapList)) {
            UserVo treeVo;
            for (Map<String, String> map : mapList) {
                treeVo = new UserVo();
                treeVo.setId(map.get("id"));
                treeVo.setObjCode(map.get("obj_code"));
                treeVo.setObjName(map.get("obj_name"));
                treeVo.setPid(map.get("pid"));
                list.add(treeVo);
            }
        }
        return list.stream().collect(Collectors.groupingBy(UserVo::getPid));

    }

    @Override
    public TSysAuthUser getUserRole(String userId) {
        Map<String, String> params = Maps.newHashMap();
        params.put(ID, userId);
        TSysAuthUser tSysAuthUser = (TSysAuthUser) sysAuthUserService.query(params);
//        Set<TSysAuthObjRel> tSysAuthObjRels = tSysAuthUser.gettSysAuthObjRelSet();
//        List<TSysAuthObj> tSysAuthObjs = tSysAuthObjRels.stream().filter(s -> s.getRelationType().equals(ONE)).map(t -> t.getToAuthObj()).collect(Collectors.toList());
//        userAuthDataNumber(userId,"2fa1eeb9358f454ca9afdbe1a816007f");
        return tSysAuthUser;
    }

    @Override
    public List<String> getAllUserAuth(String dataSetId) {
        String sql = "select b.obj_id  as id from t_sys_auth_obj a inner join t_sys_auth_obj_func b on a.id =b.obj_id " +
                " where  b.func_code =:dateSetId and a.obj_type ='1'";
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("dateSetId", dataSetId);
        List<Map<String, String>> mapList = CastUtils.cast(dfwBaseService.getDfwBaseDao().sqlQueryForList(sql, paramMap));
        List<String> ids = new ArrayList<>();
        if (CollUtil.isNotEmpty(mapList)) {
            for (Map<String, String> map : mapList) {
                ids.add(map.get("id"));
            }
        }
        return ids;

    }

    @Override
    public List<String> getAllAuthId(String userId) {

        Map<String, String> params = Maps.newHashMap();
        params.put(OBJ_ID, userId);
        List<TSysAuthObjFunc> tSysAuthObjFuncs = sysAuthObjFuncService.queryList(params);
        List<String> authIds = tSysAuthObjFuncs.stream().map(s -> s.gettSysAuthObj().getId()).collect(Collectors.toList());


        Map<String, String> relationParams = Maps.newHashMap();
        relationParams.put(FROM_OBJ_ID, userId);
        relationParams.put("relation_type", "1");
        List<TSysAuthObjRel> sysAuthObjRels = sysAuthObjRelService.queryList(relationParams);
        authIds.addAll(sysAuthObjRels.stream().map(t -> t.getToAuthObj().getId()).collect(Collectors.toList()));

        authIds = authIds.stream().distinct().collect(Collectors.toList());
        return authIds;
    }


    @Override
    public List<String> getAllAuthFunctionId(String userId) {
        Map<String, String> params = Maps.newHashMap();
        params.put(OBJ_ID, userId);
        List<TSysAuthObjFunc> tSysAuthObjFuncs = sysAuthObjFuncService.queryList(params);
        List<String> authFunctionIds = tSysAuthObjFuncs.stream().map(s -> s.gettSysFuncBase().getFuncCode()).collect(Collectors.toList());
        return filterFuncDataWareHouse(authFunctionIds, userId);
    }

    private List<String> filterFuncDataWareHouse(List<String> authFunctionIds, String userId) {
        if (GlobalConstant.UserProperties.DC_SUPER_ID.equals(userId)) return authFunctionIds;
        return authFunctionIds.stream().filter(s -> !s.equals(DATA_CONNECTION_DATA_WARE_HOUSE)).collect(Collectors.toList());
    }

    private List<TSysFuncBase> filterDataWareHouse(List<TSysFuncBase> authFunctionIds, String userId) {
        if (GlobalConstant.UserProperties.DC_SUPER_ID.equals(userId)) return authFunctionIds;
        return authFunctionIds.stream().filter(s -> !s.getFuncCode().equals(DATA_CONNECTION_DATA_WARE_HOUSE)).collect(Collectors.toList());
    }

    @Override
    public void isExistUser(String userCode) {
        Map<String, String> params = Maps.newHashMap();
        params.put(OBJCODE, userCode);
        TSysAuthUser tSysAuthUser = (TSysAuthUser) sysAuthUserService.query(params);
        if (null != tSysAuthUser) {
            ExceptionUtil.throwBusiException(BusiErrorCode.USER_ACCOUNT_EXISTED);
        }
    }


    @Override
    public Integer userAuthDataNumber(String userId, String classifyId) {
        Integer number = 0;
        if (null != this.getTSysAuthObjFunc(userId, classifyId)) {
            number++;
        }

        Map<String, String> params = Maps.newHashMap();
        params.put(ID, userId);
        TSysAuthUser tSysAuthUser = (TSysAuthUser) sysAuthUserService.query(params);
        List<TSysAuthObj> tSysAuthObjs = tSysAuthUser.gettSysAuthObjRelSet().stream()
                .filter(s -> s.getRelationType().equals(ONE))
                .map(t -> t.getToAuthObj())
                .collect(Collectors.toList());
        for (TSysAuthObj authObj : tSysAuthObjs) {
            if (null != this.getTSysAuthObjFunc(authObj.getId(), classifyId)) {
                number++;
            }
        }
        return number;
    }

    @Override
    public Map isTianJinFenJuUser(String userId) {
        Map result = Maps.newHashMap();
        Map<String, String> params = Maps.newHashMap();
        params.put(ID, userId);
        try {
            TSysAuthUser tSysAuthUser = (TSysAuthUser) sysAuthUserService.query(params);
            //获取所属组对象
            Set<TSysAuthObjRel> tSysAuthObjRels = tSysAuthUser.gettSysAuthObjRelSet();
            List<String> toAuthObjId = tSysAuthObjRels.stream().filter(s -> ZERO.equals(s.getRelationType())).map(m -> m.getToAuthObj().getId()).collect(Collectors.toList());
            for (String id : toAuthObjId) {
                Map<String, String> param = Maps.newHashMap();
                param.put(ID, id);
                TSysAuthObj tSysAuthObj = (TSysAuthObj) sysAuthGroupService.query(param);
                List<String> objNameList = tSysAuthObj.gettSysAuthObjRelSet().stream().map(s -> s.getToAuthObj().getObjName()).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(objNameList) && "天津分局用户组".equals(objNameList.get(0))) {
                    HashMap userMap = standCodeValService.getPoliceCodeByName(tSysAuthObj.getObjName());
                    result.put("code", userMap.get("code"));
                    result.put("id", userMap.get("id"));
                    if (tSysAuthUser.getObjName().contains("派出所")) {
                        userMap = standCodeValService.getPoliceCodeByName(tSysAuthObj.getObjName());
                        result.put("pcsCode", userMap.get("code"));
                        result.put("pcsId", userMap.get("id"));
                    }
                }
            }
        } catch (NullPointerException e) {
            throw new RuntimeException("用户不存在！");
        }


        return result;
    }

    @Override
    public List<String> getAllAuthDatasetId(String roleId) {
        Map<String, String> params = Maps.newHashMap();
        params.put(OBJ_ID, roleId);
        String sql = "select b.func_code from t_sys_auth_obj_func a inner join " +
                " t_sys_func b on a.func_code =b.func_code and b.func_type ='1' " +
                " where a.obj_id =:obj_id";
        List<String> resultList=new ArrayList<>();
        List<Map<String, String>> mapList = CastUtils.cast(dfwBaseService.getDfwBaseDao().sqlQueryForList(sql, params));
        if (CollUtil.isNotEmpty(mapList)) {
          mapList.forEach(map -> resultList.add(map.get("func_code")));
        }
        return resultList;
    }

    @Override
    public Integer userAuthDatasetNumber(String userId, String classifyId) {
        Integer number = 0;
        if (null != this.getTSysAuthObjFunc(userId, classifyId)) {
            number++;
        }

        return number;
    }

    @Override
    public TSysAuthObj checkDidsUserInDC(String userCode, String objType) {
        Map<String, String> params = Maps.newHashMap();
        params.put(OBJCODE, userCode);

        TSysAuthObj tSysAuthObj = ZERO.equals(objType) ? (TSysAuthObj) sysAuthUserService.query(params) : (TSysAuthObj) sysAuthRoleService.query(params);

        if (null != tSysAuthObj) {
            return tSysAuthObj;
        }
        return null;
    }

    @Override
    public List<String> dataSetAuthFromOthers(String roleId) {


        //先查出该用户所属角色

        List<String> dataAuthByUserId = dataShareAuthRelService.getDataAuthByUserId(roleId);
        /*   Map<String, String> params = Maps.newHashMap();
        params.put("obj_id", roleId);
        List<TSysAuthObjFunc> tSysAuthObjFuncs = sysAuthObjFuncService.queryList(params);
        List<String> authFunctionIds = tSysAuthObjFuncs.stream().filter(f -> f.gettSysFuncBase().getFuncType().equals("1")).map(s -> s.gettSysFuncBase().getFuncCode()).collect(Collectors.toList());*/
        return dataAuthByUserId;

    }

    @Override
    public List<String> dataSetAuthFuncIdFromOthers(String roleId) {
        List<String> dataFuncIds = dataShareAuthRelService.getDataFuncIdByUserId(roleId);
        return dataFuncIds;
    }

    @Override
    public void syncUserToDc(TSysAuthUser tSysAuthUser) {
        sysAuthUserService.store(tSysAuthUser);
//        List<String> groupIds = new ArrayList<>();
//        groupIds.add(userVo.getBelongGroup().getId());
//        List<String> roleIs = userVo.getRoles().stream().map(s -> s.getId()).collect(Collectors.toList());
//        this.saveRelation(roleIs, tSysAuthUser, ONE);
//        this.saveRelation(groupIds, tSysAuthUser, ZERO);
//        this.saveAccreditDatasetToUser(roleIs, tSysAuthUser);
        this.saveRapidAnaDirToUser(tSysAuthUser);
    }

    @Override
    public TSysAuthObj getUserOrRole(String objCode, String objType) {
        Map<String, String> params = Maps.newHashMap();
        params.put(OBJCODE, objCode);
        params.put(OBJTYPE, objType);
        TSysAuthObj tSysAuthObj = ZERO.equals(objType) ? (TSysAuthObj) sysAuthUserService.query(params) : (TSysAuthObj) sysAuthRoleService.query(params);
        return tSysAuthObj;
    }

    @Override
    public void saveUserAndRoleRelation(TSysAuthObj toObj, TSysAuthObj fromObj) {
        Map<String, String> relationParams = Maps.newHashMap();
        relationParams.put(FROM_OBJ_ID, fromObj.getId());
        relationParams.put("to_obj_id", toObj.getId());
        sysAuthObjRelService.store(UserUtil.setTSysAuthObjRel(toObj, ONE, fromObj));
    }

    @Override
    public List<TSysAuthUser> getAllUserNotIncludeItself(String userId) {
        Map<String, String> params = Maps.newHashMap();
        params.put(OBJTYPE, ZERO);
        List<TSysAuthUser> tSysAuthUsers = sysAuthUserService.queryList(params);
        //过滤掉自己 过滤掉来源不是dids的
        List<TSysAuthUser> filterUsers = tSysAuthUsers.stream()
                .filter(s -> !userId.equals(s.getId()) && (ONE.equals(s.getSourceType()) || ADMIN_CODE.equals(s.getObjCode())))
                .collect(Collectors.toList());
        return filterUsers;
    }

    @Override
    public String getUserIdbyUserCode(String userCode) {
        Map<String, String> params = Maps.newHashMap();
        params.put(OBJCODE, userCode);
        TSysAuthUser tSysAuthUser = (TSysAuthUser) sysAuthUserService.query(params);
        if (tSysAuthUser != null && com.code.common.utils.StringUtils.isNotBlank(tSysAuthUser.getId())) {
            return tSysAuthUser.getId();
        }
        return "该用户不存在";
    }

    @Override
    public Boolean checkUserAndPassward(String userNo, String realPassWard, String uncheckPassWard, String loginType) {
        //如果是dids,密码是通过不同的工具加密的，这边需要解密
        if ("1".equals(loginType)) {
            if (!StringUtils.isEmpty(DidsServiceLocator.getValidateService().checkLogin(userNo, EncryptionUtils.decrypt(uncheckPassWard)))) {
                return true;
            }
        } else {
            if (uncheckPassWard.equals(realPassWard)) {
                return true;
            }
        }
        return false;
    }

    private TSysAuthObjFunc getTSysAuthObjFunc(String objId, String funcId) {
        Map<String, String> objParams = Maps.newHashMap();
        objParams.put(OBJ_ID, objId);
        objParams.put("func_code", funcId);
        return (TSysAuthObjFunc) sysAuthObjFuncService.query(objParams);
    }

    /**
     * 构建包含用户的用户组树
     *
     * @param listMap
     * @param treeVos
     * @return
     */
    private List<GroupUserTreeVo> groupUserTreeVOS(Map<String, List<UserVo>> listMap, List<TreeVo> treeVos) {
        List<GroupUserTreeVo> groupUserTreeVos = new ArrayList<>();
        for (TreeVo treeVo : treeVos) {
            GroupUserTreeVo groupUserTreeVo = new GroupUserTreeVo();
            BeanUtils.simpleCopyProperties(treeVo, groupUserTreeVo);
            groupUserTreeVo.setUserVos(listMap.get(treeVo.getId()));
            if (null != treeVo.getChildren()) {
                groupUserTreeVo.setGroupUserTreeChildren(this.groupUserTreeVOS(listMap, treeVo.getChildren()));
            }
            groupUserTreeVos.add(groupUserTreeVo);
        }
        return groupUserTreeVos;
    }

    /**
     * 设置关系
     *
     * @param tos          属于的某个分组 或者角色 一个人可有多个角色  多个分组
     * @param fromObj
     * @param relationType 0-用户组，1-用户角色，2-组组， 3-组角色
     */
    private void saveRelation(List<String> tos, TSysAuthObj fromObj, String relationType) {
        for (String toId : tos) {
            TSysAuthObj toObj;
            Map<String, String> params = Maps.newHashMap();
            params.put(ID, toId);
            if (ONE.equals(relationType)) {
                toObj = (TSysAuthObj) sysAuthRoleService.query(params);
            } else {
                toObj = (TSysAuthObj) sysAuthGroupService.query(params);
            }
            sysAuthObjRelService.store(UserUtil.setTSysAuthObjRel(toObj, relationType, fromObj));
        }
    }
}
