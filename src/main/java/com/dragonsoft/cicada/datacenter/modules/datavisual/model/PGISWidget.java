package com.dragonsoft.cicada.datacenter.modules.datavisual.model;

import com.code.common.utils.StringUtils;
import com.code.dragonsoft.dataquery.service.querymodel.ColumnDataModel;
import com.code.metadata.datavisual.WidgetDatasetDims;
import com.dragonsoft.cicada.datacenter.common.utils.AreaUtils;
import com.dragonsoft.cicada.datacenter.modules.datavisual.model.annotation.WidgetLabel;
import com.dragonsoft.cicada.datacenter.modules.datavisual.model.enums.WidgetType;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by zhuangjp on 2020/11/16 13:32
 */
@Service
@WidgetLabel(name = "PGIS地图", type = WidgetType.PGIS, describe = "PGIS地图")
public class PGISWidget extends BarChartWidget {

    String mapCode;
    private final String CHINA = "china";
    PGISResData resData = new PGISResData();

    public Map<String, Object> builderResult(ColumnDataModel columns, String code) {
        Map re = new HashMap();

        List<WidgetDatasetDims> dims = this.widgetDataset.getFromListDims();
        String dim = dims.get(0).getFiledCode();
        String latAndLon = dims.get(1).getFiledCode();

        String measure = "";
        if (this.widgetDataset.getFromListMeasures().size() > 0) {
            measure = this.widgetDataset.getFromListMeasures().get(0).getFiledCode();
        }
        for (Map map : columns.getFieldValue()) {
            Map resMap = Maps.newLinkedHashMap();
            resMap.put("dim", map.get(dim));
            if (StringUtils.isBlank(measure)) {
                resMap.put("value", "");
            } else {
                resMap.put("value", map.get(measure));
            }
            if (dims.size() > 2) {
                String lon = dims.get(2).getFiledCode();
                if (map.get(latAndLon) != null && map.get(lon) != null) {
                    String resLatAndLon = "{\"type\":\"Point\",\"coordinates\":[" + map.get(latAndLon) + "," + map.get(lon) + "]}\n";
                    resMap.put("latAndLon", resLatAndLon);
                }
            } else {
                resMap.put("latAndLon", map.get(latAndLon));
            }
            resData.addData(resMap);
        }
        //获取中心经纬度，默认北京经纬度
        double jd = 116.4;
        double wd = 39.9;
        if (!CHINA.equalsIgnoreCase(mapCode)) {
            Map coordiantes = AreaUtils.getCoordiantes(mapCode);
            jd = (double) coordiantes.get("jd");
            wd = (double) coordiantes.get("wd");
        }

        resData.regionLatitude = jd;
        resData.regionLongitude = wd;
        List<String> dimsCode = this.widgetDataset.getFromListDims().stream().map(s -> s.getFiledName()).collect(Collectors.toList());
        this.barChartData.setDimsCodes(dimsCode);
        re.put("data", resData);
        re.put("code", 1);
        return re;
    }

    class PGISResData {
        /*private String longitude;
        private String latitude;
        private Object mwasureValue;*/
        List<Map> rows = Lists.newArrayList();
        double regionLongitude;
        double regionLatitude;

        public void addData(Map data) {
            for (Map row : rows) {
                if (null != row.get("latAndLon") && null != data.get("dim") && null != row.get("dim") && row.get("dim").equals(data.get("dim")) &&
                        row.get("latAndLon").equals(data.get("latAndLon"))){
                    return;
                }
            }
            this.rows.add(data);
        }
        public List<Map> getRows() {
            return rows;
        }

        public void setRows(List<Map> rows) {
            this.rows = rows;
        }

        public double getRegionLongitude() {
            return regionLongitude;
        }

        public void setRegionLongitude(double regionLongitude) {
            this.regionLongitude = regionLongitude;
        }

        public double getRegionLatitude() {
            return regionLatitude;
        }

        public void setRegionLatitude(double regionLatitude) {
            this.regionLatitude = regionLatitude;
        }
    }

    public String getMapCode() {
        return mapCode;
    }

    public void setMapCode(String mapCode) {
        this.mapCode = mapCode;
    }
}
