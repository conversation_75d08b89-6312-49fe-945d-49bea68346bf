package com.dragonsoft.cicada.datacenter.modules.dataplan.externaldatasources.vo.datawarehouse;

import lombok.Data;

@Data
public class BusiDirTreeVO implements Comparable<BusiDirTreeVO>{
	private String id;
	private String pId;
	private String name;
	private String cnName;
	private Integer resCount;
	private boolean isOpen;
	private String code;
	private boolean isParent;
	private boolean isRoot;
	private boolean isDrag = true;
	private boolean isDrop = true;
	private boolean dropInner = true;
	private boolean dropRoot = false;
	private boolean childOuter = true;
	private boolean childOrder = true;
	private boolean noR=false;
	private String emType;
	private String extendedType;

	public String getpId() {
		return pId;
	}

	public void setpId(String pId) {
		this.pId = pId;
	}

	@Override
	public int compareTo(BusiDirTreeVO obj) {
		if(obj == null) {
			return -1;
		}
		if(this.getName().equals(obj.getName())) {
			return this.getId().compareTo(obj.getId());
		}
		return this.getName().compareTo(obj.getName());
	}

}
