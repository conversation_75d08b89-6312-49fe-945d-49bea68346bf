package com.dragonsoft.cicada.datacenter.modules.supermarkmodel.vo;

public enum  ModelResourcesEnum {

    MODEL_PULTILEXING("1","模型复用"),
    MODEL_PUBLISH("0","模型发布"),

    RESOURCE_AUDIT_UNAUDIT("0","审批中"),
    RESOURCE_AUDTI_AGREE("2","审批通过"),
    RESOURCE_AUDIT_DISAGRESS("1","审批未通过"),

    APPLY_LIST("APPLY","申请信息"),
    AUDIT_LIST("AUDIT","审批信息"),

    AUDIT_CONFIRM("1","已确认"),
    AUDIT_UNCONFIRM("0","未确认");

    private String code;
    private String name;

    ModelResourcesEnum(String code,String name){
        this.code = code;
        this.name= name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
