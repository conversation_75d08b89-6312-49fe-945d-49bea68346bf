CREATE TABLE IF NOT EXISTS t_resource_column_relation (
   id                   VARCHAR(32)          NOT NULL,
   resource_id                   VARCHAR(32)          NULL,
   resource_name                   VARCHAR(100)          NULL,
   resource_zh_name                   VARCHAR(100)          NULL,
   filed_name                          VARCHAR(100)          NULL,
   filed_code                          VARCHAR(100)          NULL,
   filed_as_name                       VARCHAR(100)          NULL,
   filed_as_code                       VARCHAR(100)          NULL,
   filed_type                          VARCHAR(32)          NULL,
   service_id                          VARCHAR(32)           NULL
);
COMMENT ON COLUMN t_resource_column_relation.resource_id IS '资源id';
COMMENT ON COLUMN t_resource_column_relation.resource_name IS '资源英文名';
COMMENT ON COLUMN t_resource_column_relation.resource_zh_name IS '资源中文名';
COMMENT ON COLUMN t_resource_column_relation.filed_name IS '字段中文名';
COMMENT ON COLUMN t_resource_column_relation.filed_code IS '字段英文名';
COMMENT ON COLUMN t_resource_column_relation.filed_as_name IS '别名中文名';
COMMENT ON COLUMN t_resource_column_relation.filed_as_code IS '别名英文名';
COMMENT ON COLUMN t_resource_column_relation.filed_type IS '字段类型';
COMMENT ON COLUMN t_resource_column_relation.service_id IS '服务id';