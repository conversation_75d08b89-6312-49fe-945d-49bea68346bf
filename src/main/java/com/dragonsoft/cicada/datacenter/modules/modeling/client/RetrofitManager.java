package com.dragonsoft.cicada.datacenter.modules.modeling.client;

import okhttp3.OkHttpClient;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> Wu.D.J
 * @Create : 2020.10.12
 */
public class RetrofitManager {

    private static volatile RetrofitManager manager;

    private final Object lock = new Object();

    private final Map<String, Retrofit> retrofitMap = new ConcurrentHashMap<>();

    private RetrofitManager() {
    }

    public static RetrofitManager getInstance() {
        if (null == manager) {
            synchronized (RetrofitManager.class) {
                if (null == manager) {
                    manager = new RetrofitManager();
                }
            }
        }
        return manager;
    }

    public Retrofit init(String httpUrl) {
        if (!httpUrl.startsWith("http")) {
            httpUrl = "http://" + httpUrl;
        }
        if (!httpUrl.endsWith("/")) {
            httpUrl = httpUrl + "/";
        }
        if (retrofitMap.get(httpUrl) == null) {
            synchronized (lock) {
                if (retrofitMap.get(httpUrl) == null) {
                    OkHttpClient okHttpClient = new OkHttpClient.Builder()
                            .connectTimeout(300000, TimeUnit.MILLISECONDS)
                            .readTimeout(300000, TimeUnit.MILLISECONDS)
                            .writeTimeout(300000, TimeUnit.MILLISECONDS)
                            .build();

                    Retrofit mRetrofit = new Retrofit.Builder()
                            .client(okHttpClient)
                            .addConverterFactory(GsonConverterFactory.create())
                            .baseUrl(httpUrl)
                            .build();
                    retrofitMap.put(httpUrl, mRetrofit);
                }
            }
        }
        return retrofitMap.get(httpUrl);
    }

    public static MLSQLScheduleClient scheduleClient(String url) {
        Retrofit retrofit = RetrofitManager.getInstance().init(url);
        return retrofit.create(MLSQLScheduleClient.class);
    }

    public static MLSQLExecutorClient executorClient(String url) {
        Retrofit retrofit = RetrofitManager.getInstance().init(url);
        return retrofit.create(MLSQLExecutorClient.class);
    }
}
