package com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.common;

import com.code.common.bean.BeanFactory;
import com.code.common.model.step.BaseDataStep;
import com.code.common.utils.assertion.Assert;
import com.code.dataset.AbstractAtomDataSetOperator;
import com.code.dataset.operator.OperatorEnum;
import com.code.metadata.res.ddl.DataSetStepAttribute;
import com.fw.service.annotation.Service;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;
import java.util.Set;


/**
 * <AUTHOR>
 * @date 2020.9.9 14:35
 */
@Service
public class DataSetStepContext {

    @Autowired
    private BeanFactory beanFactory;

    private Map<String, AbstractAtomDataSetOperator> operatorMap = Maps.newHashMap();

    private AbstractAtomDataSetOperator getOperatorMap(String stepCode) {
        AbstractAtomDataSetOperator operator = operatorMap.get(stepCode);
        if (operator == null) {
            operator = OperatorEnum.getOperator(stepCode, beanFactory);
            operatorMap.put(stepCode, operator);
        }
        return operator;
    }

    /**
     * 通过Attribute 构建 BaseDataStep
     *
     * @param stepCode
     * @param dataSetStepAttributes
     * @return
     */
    public BaseDataStep buildDataStepByAttribute(String stepCode, Set<DataSetStepAttribute> dataSetStepAttributes) {
        AbstractAtomDataSetOperator operator = getOperatorMap(stepCode);
        Assert.notNull(operator, "暂时不支持该操作！");
        return operator.buildDataSetStep(dataSetStepAttributes);
    }

    /**
     * 修改步骤的attribute
     *
     * @param stepCode
     * @param baseDataStep
     * @param attributes
     */
    public void updateAttribute(String stepCode, BaseDataStep baseDataStep, Set<DataSetStepAttribute> attributes) {
        AbstractAtomDataSetOperator operator = getOperatorMap(stepCode);
        Assert.notNull(operator, "暂时不支持该操作！");
        operator.updateAttribute(baseDataStep, attributes);
    }

    /**
     * 撤销步骤
     *
     * @param stepCode
     * @param dataStep
     */
    public void revocationDataStep(String stepCode, BaseDataStep dataStep) {
        AbstractAtomDataSetOperator operator = getOperatorMap(stepCode);
        Assert.notNull(operator, "暂时不支持该操作！");
        operator.revocationDataStep(dataStep);
    }


}
