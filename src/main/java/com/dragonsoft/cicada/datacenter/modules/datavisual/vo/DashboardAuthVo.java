package com.dragonsoft.cicada.datacenter.modules.datavisual.vo;

import com.dragonsoft.cicada.datacenter.modules.system.permissions.vo.RoleVo;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.vo.UserVo;
import lombok.Data;

import java.util.List;

@Data
public class DashboardAuthVo {

    /**
     * 被授权数据集
     */
    List<DashboardVo> dashboardVos;
    /**
     * 授权角色
     */
    List<RoleVo> roleVos;
    /**
     * 授权用户
     */
    List<UserVo> userVos;
}
