package com.dragonsoft.cicada.datacenter.modules.modeling.vo;

import com.google.common.collect.Lists;
import lombok.Data;

import java.util.List;

@Data
public class DataExportVo {
    private List<String> transClassifyIds = Lists.newArrayList();//方案目录id
    private List<String> dataSetClassifyIds = Lists.newArrayList();//数据集目录id
    private List<String> dataSourceClassifyIds = Lists.newArrayList();//数据源目录id

    public DataExportVo(List<String> transClassifyIds, List<String> dataSetClassifyIds, List<String> dataSourceClassifyIds) {
        this.transClassifyIds = transClassifyIds;
        this.dataSetClassifyIds = dataSetClassifyIds;
        this.dataSourceClassifyIds = dataSourceClassifyIds;
    }

    public DataExportVo(String transClassifyId,String dataSetClassifyId,String dataSourceClassifyId) {
        transClassifyIds.add(transClassifyId);
        dataSetClassifyIds.add(dataSetClassifyId);
        dataSourceClassifyIds.add(dataSourceClassifyId);
    }

    public DataExportVo() {
    }
}
