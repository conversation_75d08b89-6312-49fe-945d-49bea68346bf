package com.dragonsoft.cicada.datacenter.modules.modeling.aimodeling.vo;

/**
 * <AUTHOR>
 * @description：数据类型枚举
 * @date ：2021/10/9 11:11
 */
public enum  EnumDataType {
    SingleInt("单个整型值", "SingleInt"),
    SingleFloat("单个浮点值", "SingleFloat"),
    Matrix("矩阵/二维表格", "Matrix"),
    Curve2D("二维曲线", "Curve2D"),
    Histogram("直方图", "Histogram"),
    TagValSeq("标签-数值 序列", "TagValSeq");


    EnumDataType(String name, String code){
        this.name = name;
        this.code = code;
    }

    public static EnumDataType getInstanceByCode(String code) {
        for (EnumDataType value : EnumDataType.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }

    private String name;
    private String code;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
}
