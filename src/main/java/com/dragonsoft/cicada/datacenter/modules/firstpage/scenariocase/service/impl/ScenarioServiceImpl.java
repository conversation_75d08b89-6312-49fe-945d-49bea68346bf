package com.dragonsoft.cicada.datacenter.modules.firstpage.scenariocase.service.impl;

import cn.hutool.core.util.StrUtil;
import com.code.common.paging.PageInfo;
import com.code.metadata.aimodel.ScriptLog;
import com.code.metadata.business.directory.BusiClassify;
import com.code.metadata.scenario.TScenarioCase;
import com.code.metadata.scenario.TScenarioCaseRel;
import com.code.metadata.scenario.TScenarioCaseType;
import com.code.metaservice.aimodel.ScriptLogServiceImpl;
import com.code.metaservice.business.directory.BusiClassifyService;
import com.code.metaservice.datavisual.IDashboardGroupService;
import com.code.metaservice.scenario.ScenarioCaseService;
import com.code.metaservice.scenario.ScenarioCaseTypeService;
import com.code.mist.builder.service.dubbo.ITransformApiService;
import com.dragonsoft.cicada.datacenter.modules.firstpage.scenariocase.service.ScenarioService;
import com.dragonsoft.cicada.datacenter.modules.firstpage.scenariocase.vo.ScenarioResultItem;
import com.dragonsoft.cicada.datacenter.modules.firstpage.scenariocase.vo.ScenarioVo;
import com.fw.service.BaseService;
import com.fw.service.annotation.Service;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

@Service
public class ScenarioServiceImpl extends BaseService implements ScenarioService {


    @Autowired
    private ScenarioCaseService scenarioCaseService;

    @Autowired
    private ScenarioCaseTypeService caseTypeService;

    @Autowired
    private BusiClassifyService busiClassifyService;

    @Autowired
    private ITransformApiService transformApiService;

    @Autowired
    private IDashboardGroupService dashboardGroupService;

    @Autowired
    private ScriptLogServiceImpl scriptLogService;

    private List<String> typeList = Lists.newArrayList("数据模型","仪表盘","主题门户","AI建模");

    @Override
    public PageInfo getScenarioPage(ScenarioVo scenarioVo) {
        String caseTypeId = scenarioVo.getCaseTypeId();
        Integer pageNum = scenarioVo.getPageNum();
        Integer pageSize = scenarioVo.getPageSize();
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageIndex(pageNum);
        pageInfo.setPageSize(pageSize);
        String scenarioName = scenarioVo.getScenarioName();
        if (StrUtil.isEmpty(caseTypeId)){
            PageInfo pageInfo1 = scenarioCaseService.queryByPageAndCondition(pageInfo, scenarioName);
            List<TScenarioCase> dataList = pageInfo1.getDataList();
            List<ScenarioResultItem> scenarioResultItems = setResult(dataList);
            pageInfo1.setDataList(scenarioResultItems);
            return pageInfo1;
        }
        //TScenarioCaseRel o = (TScenarioCaseRel) this.baseDao.queryForObject("from TScenarioCaseRel where id = 'f9931fc7d4f44919a8e90679fafde552'");
        //Object o = this.baseDao.queryForObject("from TScenarioCase where id = 'c3b736f78b654a72b7f3fe6dbd3127e0' ");
        List<String> caseTypeIds = new ArrayList<>();
        caseTypeIds.add(caseTypeId);
        List<TScenarioCaseType> tScenarioCaseTypes = caseTypeService.queryChildrenSceTypeByParentId(caseTypeId);
        for (TScenarioCaseType tScenarioCaseType : tScenarioCaseTypes) {
            caseTypeIds.add(tScenarioCaseType.getId());
        }
        PageInfo pageInfo1 = scenarioCaseService.queryByPageAndCondition(pageInfo, scenarioName, caseTypeIds);
        List<Map<String,String>> dataList = pageInfo1.getDataList();
        List<TScenarioCase> tScenarioCases = new ArrayList<>();
        for (Map<String, String> map : dataList) {
            TScenarioCase tScenarioCase = new TScenarioCase();
            tScenarioCase.setCaseId(map.get("caseid"));
            tScenarioCase.setName(map.get("name"));
            tScenarioCase.setId(map.get("id"));
            tScenarioCase.setCaseUrl(map.get("caseurl"));
            tScenarioCases.add(tScenarioCase);
        }
        List<ScenarioResultItem> list = setResult(tScenarioCases);
        pageInfo1.setDataList(list);
        return pageInfo1;
    }

    @Override
    public PageInfo getScenarioByPage(ScenarioVo scenarioVo) {
        String caseTypeId = scenarioVo.getCaseTypeId();
        Integer pageNum = scenarioVo.getPageNum();
        Integer pageSize = scenarioVo.getPageSize();
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageIndex(pageNum);
        pageInfo.setPageSize(pageSize);
        String scenarioName = scenarioVo.getScenarioName();
        String parentType = "";
        //查询类型为子类还是父类
        TScenarioCaseType caseType = (TScenarioCaseType) this.baseDao.get(TScenarioCaseType.class, caseTypeId);
        //if (caseType.getParentId() == null){
            //父类查询所有
       /* if (caseType == null){
            throw new RuntimeException("该类型不存在！");
        }*/
        TScenarioCaseType parentCaseType = null;
        if (caseType != null && StrUtil.isNotEmpty(caseType.getParentId())){
            parentCaseType  = (TScenarioCaseType) this.baseDao.get(TScenarioCaseType.class, caseType.getParentId());
        }


        List<String> allCaseTypes = new ArrayList<>();
        allCaseTypes.add(caseType.getId());
        for (TScenarioCaseType child : caseType.getChildren()) {
            allCaseTypes.add(child.getId());
        }
        String sql = "select case_id from t_md_scenario_case where id in (select scenario_case_id from t_md_scenario_case_relation where case_type_id in (:allCaseTypes))";
        List<Map<String,String>> caseIdsMap = this.baseDao.sqlQueryForList(sql, this.addParam("allCaseTypes", allCaseTypes).param());
        List<String> caseIds = new ArrayList<>();
        for (Map<String, String> caseId : caseIdsMap) {
            caseIds.add(caseId.get("case_id"));
        }
        //查询场景案例目录下的案例
        Map<String,Object> map = new HashMap<>();
        PageInfo pageInfo1 = new PageInfo();
        if ("数据模型".equals(caseType.getName())||(parentCaseType!=null && "数据模型".equals(parentCaseType.getName()))){
            parentType = "数据模型";
            //String hql = " from BusiClassify where name = '场景案例' and type = 'BaseBusiClassify' and code = 'TRANS_DIR_MF_CASE' ";
            //BusiClassify caseDir = (BusiClassify) this.baseDao.queryForObject(hql);
            //List<String> caseDirs = getAllChildrenClassifyIds(caseDir);
           /* if ("数据模型".equals(caseType.getName())){
                List<Map<String,String>> transIds = this.baseDao.sqlQueryForList("select element_id from t_md_classify_element " +
                        " where busi_classify_id in (:caseDirs) ",this.addParam("caseDirs",caseDirs).param());
                for (Map<String, String> transId : transIds) {
                    caseIds.add(transId.get("element_id"));
                }
            }*/
            sql = "select tt.id as case_id,tt.name as name,tt.memo as description,tt.operate_time as operate_time " +
                    "   from t_etl_trans tt LEFT JOIN t_md_scenario_case tc " +
                    " on tc.case_id = tt.id  where tt.id in (:caseIds) ";

            if (StrUtil.isNotEmpty(scenarioName)){
                sql += " and tt.name like :scenarioName ";
                map.put("scenarioName","%"+scenarioName+"%");
            }
            sql += "  order by tt.operate_time desc ";

        }else if ("主题门户".equals(caseType.getName())||(parentCaseType!=null && "主题门户".equals(parentCaseType.getName()))){
            parentType = "主题门户";
           /* String hql = " from BusiClassify where name = '场景案例' and type = 'BaseBusiClassify' and code = 'PORTAL_DIR_CASE' ";
            BusiClassify caseDir = (BusiClassify) this.baseDao.queryForObject(hql);*/
            //List<String> caseDirs = getAllChildrenClassifyIds(caseDir);
          /*  List<Map<String,String>> portIds = this.baseDao.sqlQueryForList("select id from t_md_portal " +
                    " where owner_id in (:caseDirs) ",this.addParam("caseDirs",caseDirs).param());
            for (Map<String, String> portId : portIds) {
                caseIds.add(portId.get("id"));
            }*/
            sql = "select tt.id as case_id,tt.name as name,tt.memo as description,tt.operate_time as operate_time " +
                    "  from t_md_portal tt LEFT JOIN t_md_scenario_case tc " +
                    " on tc.case_id = tt.id  where tt.id in (:caseIds) ";

            if (StrUtil.isNotEmpty(scenarioName)){
                sql += " and tt.name like :scenarioName ";
                map.put("scenarioName","%"+scenarioName+"%");
            }
            sql += "  order by tt.operate_time desc ";
        }else if ("仪表盘".equals(caseType.getName())||(parentCaseType != null && "仪表盘".equals(parentCaseType.getName()))){
            parentType = "仪表盘";
           /* List<Map<String,String>> list = this.baseDao.sqlQueryForList("select id from t_v_dashboards where group_id = 'c1c92f3b20f2487cae20e2a40c4e60cd' and operate_user_id = :userId ",
                    this.addParam("userId",scenarioVo.getUserId()).param());
            for (Map<String, String> dashId : list) {
                caseIds.add(dashId.get("id"));
            }*/
            sql = "select tt.id as case_id,tt.name as name,tt.memo as description,tt.operate_time as operate_time " +
                    "  from t_v_dashboards tt LEFT JOIN t_md_scenario_case tc " +
                    " on tc.case_id = tt.id  where tt.id in (:caseIds) ";

            if (StrUtil.isNotEmpty(scenarioName)){
                sql += " and tt.name like :scenarioName ";
                map.put("scenarioName","%"+scenarioName+"%");
            }
            sql += "  order by tt.operate_time desc ";
        }else if ("AI建模".equals(caseType.getName())||(parentCaseType != null && "AI建模".equals(parentCaseType.getName()))){
            parentType = "AI建模";
           /* List<Map<String,String>> list = this.baseDao.sqlQueryForList("select id from t_v_dashboards where group_id = 'c1c92f3b20f2487cae20e2a40c4e60cd' and operate_user_id = :userId ",
                    this.addParam("userId",scenarioVo.getUserId()).param());
            for (Map<String, String> dashId : list) {
                caseIds.add(dashId.get("id"));
            }*/
            sql = "select tt.id as case_id,tt.name as name,tt.model_desc as description,tt.operate_time as operate_time " +
                    "  from t_script_info tt LEFT JOIN t_md_scenario_case tc " +
                    " on tc.case_id = tt.id  where tt.id in (:caseIds) ";

            if (StrUtil.isNotEmpty(scenarioName)){
                sql += " and tt.name like :scenarioName ";
                map.put("scenarioName","%"+scenarioName+"%");
            }
            sql += "  order by tt.operate_time desc ";
        }
        if (caseIds.size() <= 0 ){
            //throw new RuntimeException("该分类下无场景案例！");
            return null;
        }
        map.put("caseIds",caseIds);
        pageInfo1 = this.baseDao.sqlQueryForPage(sql, map , pageInfo);
        pageInfo1.setDataList(setResultItem(pageInfo1.getDataList(),parentType));
        return pageInfo1;
    }

    @Override
    public List<TScenarioCaseType> getAllParentTypes(String userId) {
        List<TScenarioCaseType> tScenarioCaseTypes = caseTypeService.queryAllParentType();
        //查询当前用户所属角色
        String sql = "SELECT id from t_sys_auth_obj where id in (select tr.to_obj_id from t_sys_auth_obj t LEFT JOIN t_sys_auth_obj_rel tr on t.id = tr.from_obj_id where t.id = :userId and tr.relation_type = '1')";
        List<Map<String,String>> list = this.baseDao.sqlQueryForList(sql, this.addParam("userId", userId).param());
        List<String> allObjId = new ArrayList<>();
        allObjId.add(userId);
        for (Map<String, String> map : list) {
            allObjId.add(map.get("id"));
        }
        sql = "select func_code from t_sys_auth_obj_func where obj_id in (:allObjId) and func_code in ('scenarioCaseGateway','scenarioCaseDashboard','scenarioCaseDataModel','scenarioCaseAiModel');";
        List<Map<String,String>> list1 = this.baseDao.sqlQueryForList(sql, this.addParam("allObjId", allObjId).param());
        List<String> roles = new ArrayList<>();
        for (Map<String,String> o : list1) {
            roles.add(o.get("func_code"));
        }
       /* for (int i = 0; i < tScenarioCaseTypes.size(); i++) {
            if (!roles.contains("scenarioCaseGateway")){
                if ()
            }
        }*/
        Iterator<TScenarioCaseType> iterator = tScenarioCaseTypes.iterator();
        while (iterator.hasNext()){
            TScenarioCaseType type = iterator.next();
            if (!roles.contains("scenarioCaseGateway")){
                if ("主题门户".equals(type.getName())){
                    iterator.remove();
                }
            }
            if (!roles.contains("scenarioCaseDashboard")){
                if ("仪表盘".equals(type.getName())){
                    iterator.remove();
                }
            }
            if (!roles.contains("scenarioCaseDataModel")){
                if ("数据模型".equals(type.getName())){
                    iterator.remove();
                }
            }
            if (!roles.contains("scenarioCaseAiModel")){
                if ("AI建模".equals(type.getName())){
                    iterator.remove();
                }
            }
        }

        return tScenarioCaseTypes;
    }

    @Override
    public void changeToScenarioCase(String modelId, String parentCaseType, String currentCaseType,String desc) {

        //判断该类别下是否有此方案
        boolean existedByType = isExistedByType(modelId, parentCaseType);
        if (!existedByType){
            throw new RuntimeException("该分类下不存在此方案！");
        }

        //判断改案例是否已存在
        String countCase = this.baseDao.sqlQueryForValue("select count(case_id) from t_md_scenario_case where case_id = :case_id", this.addParam("case_id", modelId).param());
        if (Integer.parseInt(countCase) > 0){
            //存在
            TScenarioCase tCase = (TScenarioCase) this.baseDao.queryForObject(" from TScenarioCase where caseId = :caseId ", this.addParam("caseId", modelId).param());
            tCase.setMemo(desc);
            tCase.setVersion(tCase.getVersion()+1);
            this.baseDao.save(tCase);
            TScenarioCaseType currentType;
            if (StrUtil.isNotEmpty(currentCaseType)){
                currentType = getChildCaseType(parentCaseType, currentCaseType);
            }else {
                currentType = getParentTypeByName(parentCaseType);
            }
            TScenarioCaseRel tr = (TScenarioCaseRel) this.baseDao.queryForObject(" from TScenarioCaseRel tr where tr.tScenarioCase.caseId = :modelId ", this.addParam("modelId", modelId).param());
            tr.settScenarioCaseType(currentType);
            this.baseDao.save(tr);
        }else {
            TScenarioCaseType currentType;
            if (StrUtil.isNotEmpty(currentCaseType)){
                currentType = getChildCaseType(parentCaseType, currentCaseType);
            }else {
                currentType = getParentTypeByName(parentCaseType);
            }
           /* if ("主题门户".equals(parentCaseType) || "仪表盘".equals(parentCaseType)){
                String updateStr = getUpdateStr(parentCaseType);
                this.baseDao.executeSqlUpdate(updateStr,this.addParam("modelId",modelId).param());
                deleteBusiRelation(modelId,parentCaseType);
            }*/

            TScenarioCaseRel tr = new TScenarioCaseRel();
            tr.settScenarioCaseType(currentType);
            TScenarioCase tScenarioCase = new TScenarioCase();
            String modelName = getModelName(modelId, parentCaseType);
            tScenarioCase.setCaseId(modelId);
            tScenarioCase.setCode("scenario_case");
            tScenarioCase.setName(modelName);
            tScenarioCase.setType("SCENARIO_CASE");
            tScenarioCase.setMemo(desc);
            tScenarioCase.setVersion(1);
            this.baseDao.save(tScenarioCase);
            tr.settScenarioCase(tScenarioCase);
            tr.setCode(modelId);
            tr.setType("SCENARIO_CASE_REL");
            this.baseDao.save(tr);
        }
    }

    @Override
    public void deleteScenarioCases(List<String> caseIds) {
        this.baseDao.executeSqlUpdate(" delete from t_md_scenario_case_relation where scenario_case_id in (select id from t_md_scenario_case where case_id in  (:caseIds)) ",this.addParam("caseIds",caseIds).param());
        this.baseDao.executeSqlUpdate(" delete from t_md_scenario_case where case_id in (:caseIds)",this.addParam("caseIds",caseIds).param());
        /*for (String caseId : caseIds) {
            transformApiService.deleteTrans(caseId);
        }*/
    }


    private boolean isExistedByType(String modelId, String parentCaseType){
        String countStr = "";
        Map<String,String> map = new HashMap<>();
        map.put("modelId",modelId);
        if (!typeList.contains(parentCaseType)){
            throw new RuntimeException("无此分类！");
        }
        countStr = this.baseDao.sqlQueryForValue("select count(id) from t_md_element where id = :modelId ",
                    map);

            //throw new RuntimeException("该父类不存在！");

        int i = Integer.parseInt(countStr);
        return i > 0;
    }

    private List<ScenarioResultItem> setResultItem(List<Map<String,String>> list,String parentType){
        List<ScenarioResultItem> scenarioResultItems = new ArrayList<>();
        for (Map<String, String> map : list) {
            ScenarioResultItem resultItem = new ScenarioResultItem();
            resultItem.setName(map.get("name"));
            String hql = "from TScenarioCase where caseId = :caseId";
            TScenarioCase tCase = (TScenarioCase) this.baseDao.queryForObject(hql, this.addParam("caseId", map.get("case_id")).param());
            if (tCase == null || StrUtil.isEmpty(tCase.getMemo())){
                resultItem.setDescription(map.get("description"));
            }else {
                resultItem.setDescription(tCase.getMemo());
            }

            resultItem.setCaseId(map.get("case_id"));
            hql = "from TScenarioCaseRel tr where tr.tScenarioCase.caseId = :caseId ";
            TScenarioCaseRel tr = (TScenarioCaseRel) this.baseDao.queryForObject(hql, this.addParam("caseId", map.get("case_id")).param());
            if (tr != null){
                resultItem.setCaseType(tr.gettScenarioCaseType());
            }
            Map map1 = this.baseDao.sqlQueryForMap("select id,name from t_md_scenario_case_type where name = :parentName",
                    this.addParam("parentName", parentType).param());
            resultItem.setParentType(map1);
            if ("AI建模".equals(parentType)){
                ScriptLog log = scriptLogService.getNewestLogByScriptId(resultItem.getCaseId());
                if (log != null){
                    resultItem.setAiNewestLogId(log.getId());
                }
            }
            scenarioResultItems.add(resultItem);
            //resultItem.set(map.get("name"));
        }
        return scenarioResultItems;
    }

    private String getUpdateStr(String parentCaseType){
        String sql = "update ";
        if ("数据模型".equals(parentCaseType)){
            sql += " t_etl_trans ";
        }else if ("仪表盘".equals(parentCaseType)){
            sql += " t_v_dashboards ";
        }else if ("主题门户".equals(parentCaseType)){
            sql += " t_md_portal ";
        }else {
            throw new RuntimeException(" 该类型不存在！ ");
        }
        sql += " set operate_user_id = null where id = :modelId";
        return sql;
    }

    private TScenarioCaseType getChildCaseType(String parentCaseType,String currentCaseType){
        TScenarioCaseType parentType = getParentTypeByName(parentCaseType);
        if (parentType == null){
            throw new RuntimeException(" 该父类型不存在！");
        }
        String hql = " from TScenarioCaseType tct where tct.parentId = :parentId and tct.name = :name";
        Map<String,String> map = new HashMap<>();
        map.put("parentId",parentType.getId());
        map.put("name",currentCaseType);
        TScenarioCaseType currentType = (TScenarioCaseType) this.baseDao.queryForObject(hql, map);
        if (currentType == null){
            throw new RuntimeException(" 该子类型不存在！");
        }
        return currentType;
    }

    private TScenarioCaseType getParentTypeByName(String parentCaseType){
        String hql = " from TScenarioCaseType tct where tct.parentId is null and tct.name = :parentCaseType ";
        TScenarioCaseType parentType = (TScenarioCaseType) this.baseDao.queryForObject(hql,this.addParam("parentCaseType",parentCaseType).param());
        return parentType;
    }

    private String getModelName(String modelId,String caseType){
        StringBuilder sql = new StringBuilder("select name from t_md_element  ");

        sql.append(" where id = :modelId ") ;
        String name = this.baseDao.sqlQueryForValue(sql.toString(),this.addParam("modelId",modelId).param());
        return name;
    }

    //去掉相关目录的关联
    private void deleteBusiRelation(String modelId,String parentCaseType){
        if ("数据模型".equals(parentCaseType)){
            this.baseDao.executeSqlUpdate("delete from t_md_classify_element where element_id = :id ",this.addParam("id",modelId).param());
        }else if ("仪表盘".equals(parentCaseType)){
            this.baseDao.executeSqlUpdate("update t_v_dashboards set group_id = null where id = :id",this.addParam("id",modelId).param());
        }else if ("主题门户".equals(parentCaseType)){
            this.baseDao.executeSqlUpdate("update t_md_portal set owner_id = null where id = :id",this.addParam("id",modelId).param());
        }else {
            throw new RuntimeException(" 该类型不存在！ ");
        }
    }

    private List<ScenarioResultItem> setResult(List<TScenarioCase> tScenarioCases){
        List<ScenarioResultItem> scenarioResultItems = new ArrayList<>();
        for (TScenarioCase tScenarioCase : tScenarioCases) {
            ScenarioResultItem resultItem = new ScenarioResultItem();
            resultItem.setCaseId(tScenarioCase.getCaseId());
            String caseTypeId = this.baseDao.sqlQueryForValue("select case_type_id from t_md_scenario_case_relation where scenario_case_id = (select id from t_md_scenario_case where case_id = :caseId) ",this.addParam("caseId",tScenarioCase.getCaseId()).param());
            TScenarioCaseType tScenarioCaseType = (TScenarioCaseType) this.baseDao.queryForObject("from TScenarioCaseType where id = :caseTypeId", this.addParam("caseTypeId", caseTypeId).param());
            //TScenarioCaseType tScenarioCaseType = tScenarioCase.gettScenarioCaseRel().gettScenarioCaseType();
            String parentId = tScenarioCaseType.getParentId();
            TScenarioCaseType parentType = (TScenarioCaseType) this.baseDao.queryForObject("from TScenarioCaseType where id = :parentId", this.addParam("parentId", parentId).param());
            Map<String,String> map1 = new HashMap<>();
            map1.put("name",parentType.getName());
            map1.put("id",parentType.getId());
            resultItem.setCaseType(tScenarioCaseType);
            resultItem.setParentType(map1);
            String sql = "select name,memo from";
            if ("数据模型".equals(tScenarioCaseType.getName())){
                sql += " t_etl_trans where id = :caseId ";
            }else if ("主题门户".equals(tScenarioCaseType.getName())){
                sql += " t_md_portal where id = :caseId ";
            }else if ("仪表盘".equals(tScenarioCaseType.getName())){
                sql += " t_v_dashboards where id = :caseId ";
            }
            Map map = this.baseDao.sqlQueryForMap(sql, this.addParam("caseId", tScenarioCase.getCaseId()).param());
            //String name = (String) map.get("name");
            String description = (String) map.get("memo");
            resultItem.setName(tScenarioCase.getName());
            if (StrUtil.isEmpty(tScenarioCase.getMemo())){
                resultItem.setDescription(description);
            }else {
                resultItem.setDescription(tScenarioCase.getMemo());
            }
            scenarioResultItems.add(resultItem);
        }
        return scenarioResultItems;
    }

    private List<String> getAllChildrenClassifyIds(BusiClassify busiClassify){
        List<String> list = new ArrayList<>();
        list.add(busiClassify.getId());
        Set<BusiClassify> busiClassifies = busiClassify.getBusiClassifies();
        if (busiClassifies.size() > 0){
            for (BusiClassify classify : busiClassifies) {
                List<String> allChildrenClassifyIds = getAllChildrenClassifyIds(classify);
                list.addAll(allChildrenClassifyIds);
            }
        }
        return list;
    }
}
