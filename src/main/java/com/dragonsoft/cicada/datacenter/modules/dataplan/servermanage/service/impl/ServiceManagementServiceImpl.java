package com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.code.common.mist.plugin.config.service.impl.PluginConfigService;
import com.code.common.paging.PageInfo;
import com.code.common.utils.DateUtils;
import com.code.common.utils.R;
import com.code.common.utils.StringUtils;
import com.code.common.utils.assertion.Assert;
import com.code.common.utils.json.JsonUtil;
import com.code.metadata.business.directory.BaseBusiClassify;
import com.code.metadata.business.directory.BaseBusiDir;
import com.code.metadata.etl.trans.TransMeta;
import com.code.metadata.model.core.ModelElement;
import com.code.metadata.plugin.operate.PluginDirType;
import com.code.metadata.res.ddl.LogicDataObj;
import com.code.metadata.sm.*;
import com.code.metaservice.business.directory.BusiClassifyService;
import com.code.metaservice.business.directory.BusiDirService;
import com.code.metaservice.ddl.ILogicDataObjService;
import com.code.metaservice.sm.*;
import com.dragoninfo.dfw.bean.Result;
import com.dragonsoft.cicada.datacenter.common.token.GlobalConstant;
import com.dragonsoft.cicada.datacenter.modules.dataplan.apitest.vo.TestApiParamVo;
import com.dragonsoft.cicada.datacenter.modules.dataplan.apitest.vo.TestApiResultVo;
import com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.enums.ServiceClassifyEnum;
import com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.service.IAiServicePublishService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.service.ICicadaMetaServicePublishService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.service.IServiceManagementService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.service.IServicePublicService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.vo.*;
import com.dragonsoft.cicada.datacenter.modules.modeling.aimodeling.config.AiModelIpAndPort;
import com.dragonsoft.cicada.datacenter.modules.modeling.service.DataModelingService;
import com.dragonsoft.cicada.datacenter.modules.modeling.vo.TransClassifyVo;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.service.IUserService;
import com.fw.service.BaseService;
import com.fw.service.annotation.Service;
import com.fw.tenon.bean.TenonBeanUtil;
import com.fw.tenon.tree.Tree;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 版权所有：厦门市巨龙信息科技有限公司
 * Copyright 2018 Xiamen Dragon Information Technology Co. Ltd.
 * All right reserved.
 * ====================================================
 * 修订记录：
 * No    日期				作者(操作:具体内容)
 * 1.    2021/9/8 7:57			baijx（<EMAIL>）(创建:创建文件)
 * ====================================================
 * 类描述：
 */
@Service
@Slf4j
public class ServiceManagementServiceImpl extends BaseService implements IServiceManagementService {

    @Value("${dc.publish.path}")
    private String testPublishPath;

    @Resource
    private AiModelIpAndPort aiModelIpAndPort;

    private static final String DEFAULT_CODE = "1";


    private static final String SERVICE_DIR_CODE = "TRANS_SERVICE_DIR";
    private static final String SERVICE_CLASSIFY_CODE = "TRANS_SERVICE_CLASSIFY";

    @Autowired
    private IServicePublicationService servicePublicationService;

    @Autowired
    private IServiceMetaService serviceMetaService;

    @Autowired
    private IServiceParamsService serviceParamsService;

    @Autowired
    private ILogicDataObjService logicDataObjService;

    @Autowired
    private PluginConfigService pluginConfigService;

    @Autowired
    private IAiServicePublishService aiServicePublishService;

    @Autowired
    private DataModelingService dataModelingService;

    @Autowired
    private BusiDirService busiDirService;

    @Autowired
    private BusiClassifyService busiClassifyService;

    @Autowired
    private IServicePublicService servicePublicService;

    @Autowired
    private IServiceManyResourceConfigService serviceManyResourceConfigService;
    @Autowired
    private IServiceResourceRelation serviceResourceRelation;

    @Autowired
    private IServiceResourceConfigService serviceResourceConfigService;

    @Autowired
    private ICicadaMetaServicePublishService cicadaMetaServicePublishService;

    @Autowired
    private IUserService userService;

    @Override
    public PageInfo queryServicePageByCondition(ManagementPageVo managementPage, String userId) {

        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageSize(managementPage.getSize());
        pageInfo.setPageIndex(managementPage.getIndex());
        String classifyId = managementPage.getClassifyId();
        Assert.hasLength(classifyId, "目录id不能为空！");
        List<String> allClassifyIds = queryBusiClassifyIdList(classifyId);
        PageInfo result = servicePublicationService.queryDataByName(pageInfo, managementPage.getName(), managementPage.getQueryType(),
                userId, managementPage.getServicetype(), managementPage.getStatus(), allClassifyIds);
        List<Map<String, Object>> dataList = result.getDataList();
        if (CollectionUtils.isEmpty(dataList)) {
            result.setDataList(Collections.emptyList());
            return result;
        }
        List<ParamConfigVo> collect = dataList.stream().map(x -> {
            ParamConfigVo resultMap = new ParamConfigVo();
            resultMap.setInterfaceChineseName((String) x.get("api_name"));
            resultMap.setServiceType(EnumServiceType.getInstanceByCode((String) x.get("service_type")).getName());
            resultMap.setStatus((String) x.get("status"));
            resultMap.setSingleOrManyTable((String) x.get("is_multilist"));
            resultMap.setTime((String) x.get("operate_time"));
            resultMap.setRequestPath(testPublishPath + x.get("request_path"));
            resultMap.setModelId((String) x.get("source_id"));
            resultMap.setServiceMetaId((String) x.get("service_meta_id"));
            resultMap.setSourceName((String) x.get("source_name"));
            resultMap.setServiceId((String) x.get("service_id"));
            resultMap.setSaveType((String) x.get("save_type"));
            if (EnumServiceType.ALGORITHM_SERVICE.getCode().equals(x.get("service_type"))) {
                resultMap.setRequestPath("http://" + aiModelIpAndPort.getRunOpServiceIp() + x.get("request_path"));
                Map scriptInfo = this.baseDao.sqlQueryForMap(" select id,name from t_script_info where id = (select script_id from t_script_log where id = :sourceId) ",
                        this.addParam("sourceId", x.get("source_id")).param());
                resultMap.setSourceName((String) scriptInfo.get("name"));
                resultMap.setTransId((String) scriptInfo.get("id"));
                /*String scriptId = (String) scriptInfo.get("id");
                String practiceVersion = getPracticeVersion((String) x.get("source_id"));
                resultMap.setPracticeVersion(practiceVersion);*/
            }
            //数据查询和信息核查 可以多个资源
            if (EnumServiceType.INFORMATION_VERFICATION.getCode().equals(x.get("service_type")) ||
                    EnumServiceType.INQUIRY_SERVICE.getCode().equals(x.get("service_type"))) {
                Map<String, String> sourceIdAndNameFromServiceRelation = getSourceIdAndNameFromServiceRelation(resultMap.getServiceId());
                if (StrUtil.isNotEmpty(sourceIdAndNameFromServiceRelation.get("name"))) {
                    resultMap.setSourceName(sourceIdAndNameFromServiceRelation.get("name"));
                }
                if (StrUtil.isNotEmpty(sourceIdAndNameFromServiceRelation.get("id"))) {
                    resultMap.setModelId(sourceIdAndNameFromServiceRelation.get("id"));
                }
            }
            buildServiceParam(resultMap, (String) x.get("service_id"));
            String serviceId = resultMap.getServiceId();
            String realClassifyId = this.baseDao.sqlQueryForValue("select busi_classify_id from t_md_classify_element where element_id = :serviceId", this.addParam("serviceId", serviceId).param());
            //我只是进来这边加一个目录全路径
            resultMap.setDirAllName(getClassifyAndParents(realClassifyId));
            resultMap.setClassifyId(realClassifyId);
            return resultMap;
        }).collect(Collectors.toList());
        result.setDataList(collect);
        return result;
    }


    private String getClassifyAndParents(String classifyId) {
        String hql = "From BaseBusiClassify where id = :id";
        BaseBusiClassify baseBusiClassify = (BaseBusiClassify) this.baseDao.queryForObject(hql, addParam("id", classifyId).param());
        return getParantsClassifyName(baseBusiClassify, baseBusiClassify.getName());
    }

    private String getParantsClassifyName(BaseBusiClassify baseBusiClassify, String name) {
        if (baseBusiClassify.getParentBc() != null) {
            name = baseBusiClassify.getParentBc().getName() + "/" + name;
            String parantsClassifyName = this.getParantsClassifyName(baseBusiClassify.getParentBc(), name);
            return parantsClassifyName;
        }
        return name;
    }


    private Map<String, String> getSourceIdAndNameFromServiceRelation(String serviceId) {

        Map<String, String> map = new HashMap<>();
        String sql = "select * from t_md_resource_relation where service_id = :serviceId";
        List<Map<String, String>> relations = this.baseDao.sqlQueryForList(sql, this.addParam("serviceId", serviceId).param());
        List<String> resourceId = relations.stream().map(r -> r.get("resource_id")).collect(Collectors.toList());
        List<ModelElement> resources = this.baseDao.queryForList("from ModelElement where id in (:resourceId)", this.addParam("resourceId", resourceId).param());

        String name = resources.stream().map(r -> r.getName()).collect(Collectors.joining(","));
        String id = resources.stream().map(r -> r.getId()).collect(Collectors.joining(","));
        map.put("name", name);
        map.put("id", id);
        return map;
    }

    @Override
    public SubscribeParamConfigVo queryServiceDetails(String serviceMetaId) throws Exception {
        ServicePublication servicePublication = servicePublicationService.getServicePublications(Collections.singletonList(queryPublicIdServiceMetaId(serviceMetaId))).get(0);
        ServiceMeta serviceMeta = serviceMetaService.get(ServiceMeta.class, serviceMetaId);

        //服务与表关系
        List<String> resourceIds = serviceResourceRelation.queryResourceRelation(serviceMeta.getServicePublication().getId()).stream().distinct().collect(Collectors.toList());
        //条件过滤
        ResourceConfig resourceConfig = serviceResourceConfigService.queryConfigByServiceId(serviceMeta.getServicePublication().getId());
        SubscribeParamConfigVo paramConfigVo = buildServiceApiVo(serviceMeta, servicePublication, resourceIds, resourceConfig);
        buildSubscribeApiVo(paramConfigVo, servicePublication.getId());
        paramConfigVo.setRelativePath(serviceMeta.getRequestPath());
        return paramConfigVo;
    }

    private void buildSubscribeApiVo(SubscribeParamConfigVo paramConfigVo, String serviceId) {
        ResourceConfig resourceConfig = serviceResourceConfigService.queryConfigByServiceId(serviceId);
        paramConfigVo.setIncrementalColumn(resourceConfig.getIncrementalColumn());
        paramConfigVo.setCompareType(resourceConfig.getCompareType());
        paramConfigVo.setIncrementalZhColumn(resourceConfig.getIncrementalZhColumn());
        paramConfigVo.setIncrementalStepId(resourceConfig.getIncrementalStepId());
        paramConfigVo.setEncasulationJudgContion(JSONArray.parseArray(resourceConfig.getConditionExp(), ParamConfigVo.ConditionFilterMeta.class));
    }


    @Override
    public List<Map<String, Object>> queryServicesByModelId(String modelId, String type) {
        if ("ai".equalsIgnoreCase(type)) {
            List<Map<String, Object>> logIds = this.baseDao.sqlQueryForList("select id from t_script_log where script_id = :scriptId", this.addParam("scriptId", modelId).param());
            List<String> sourIds = Lists.newArrayList();
            for (Map<String, Object> logId : logIds) {
                sourIds.add((String) logId.get("id"));
            }
            if (sourIds.size() <= 0) {
                return null;
            }
            String sql = "select id,name from t_md_service_publication where source_id in (:sourIds) order by operate_time desc  ";
            return this.baseDao.sqlQueryForList(sql, this.addParam("sourIds", sourIds).param());
        }
        List<Map<String, Object>> dataSetByTrans = dataModelingService.getDataSetByTrans(modelId);
        List<String> sourceIds = new ArrayList<>();
        sourceIds.add(modelId);
        if (dataSetByTrans != null) {
            for (Map<String, Object> dataSetByTran : dataSetByTrans) {
                String id = (String) dataSetByTran.get("id");
                List<LogicDataObj> logicDataObjsByOwnerId = logicDataObjService.getLogicDataObjsByOwnerId(id);
                for (LogicDataObj logicDataObj : logicDataObjsByOwnerId) {
                    sourceIds.add(logicDataObj.getId());
                }
            }
        }
        String sql = "select id,name from t_md_service_publication where source_id in (:sourceIds) " +
                " and id in (select element_id  from t_md_classify_element) order by operate_time desc ";
        return this.baseDao.sqlQueryForList(sql, this.addParam("sourceIds", sourceIds).param());
    }

    @Override
    public Map<String, Object> queryServiceDetailsByPublishedId(String serviceId) {
        ServicePublication servicePublication = servicePublicationService.getServicePublications(Collections.singletonList(serviceId)).get(0);
        Set<ServiceMeta> serviceMetas = servicePublication.getServiceMetas();
        if (serviceMetas.size() <= 0) {
            Assert.fail("找不到该服务，请确认该服务是否已被卸载");
        }
        List<ServiceMeta> collect = serviceMetas.stream().sorted(Comparator.comparing(ServiceMeta::getPublishTime).reversed()).collect(Collectors.toList());
        ServiceMeta serviceMeta = collect.get(0);
        Map<String, Object> map = Maps.newHashMap();
        if (EnumServiceType.CALCULATION_SERVICE.getCode().equals(servicePublication.getServiceType()) ||
                EnumServiceType.COMPARE_SERVICE.getCode().equals(servicePublication.getServiceType()) ||
                EnumServiceType.DATA_COLLISION.getCode().equals(servicePublication.getServiceType())) {
            //流程建模服务
            String sql = "select ts.name,tp.code,ts.id as id from t_etl_trans ts left JOIN t_md_etl_trans_plugin tp on  ts.transplugin_id = tp.id where ts.id in (SELECT child_trans_id from T_ETL_TRANS_STEPDETAIL where trans_id = :tranId and tp.code = 'cicadaMetaServiceOutput') ";
            Map<String, Object> objectMap = this.baseDao.sqlQueryForMap(sql, this.addParam("tranId", servicePublication.getSourceId()).param());
            String stepId = (String) objectMap.get("id");
            if (StrUtil.isNotEmpty(stepId)) {
                Map<String, String> pluginAttribute = pluginConfigService.getTransMetaAttribute(stepId);
                String async = pluginAttribute.get("async");
                map.put("async", async);
            }
        }
        map.put("servicePublishId", servicePublication.getId());
        map.put("sourceId", servicePublication.getSourceId());
        map.put("serviceMetaId", serviceMeta.getId());
        map.put("apiName", servicePublication.getName());
        map.put("apiType", EnumServiceType.getInstanceByCode(servicePublication.getServiceType()).getName());
        if (EnumServiceType.ALGORITHM_SERVICE.getCode().equals(servicePublication.getServiceType())) {
            map.put("requestPath", "http://" + aiModelIpAndPort.getRunOpServiceIp() + serviceMeta.getRequestPath());
            map.put("practiceVersion", getPracticeVersion(servicePublication.getSourceId()));
        } else {
            map.put("requestPath", testPublishPath + serviceMeta.getRequestPath());
        }
        map.put("requestType", "post");
        map.put("version", serviceMeta.getVersion());
        map.put("memo", serviceMeta.getMemo());
        map.put("status", serviceMeta.getStatus());
        List<Map<String, Object>> inputParams = this.queryServiceParamsByCondition(serviceId, "0");
        List<Map<String, Object>> outputParams = this.queryServiceParamsByCondition(serviceId, "1");
        map.put("inputParams", inputParams);
        map.put("outputParams", outputParams);
        map.put("relativePath", serviceMeta.getRequestPath());
        String realClassifyId = this.baseDao.sqlQueryForValue("select busi_classify_id from t_md_classify_element where element_id = :serviceId", this.addParam("serviceId", serviceId).param());
        map.put("classifyId", realClassifyId);
        return map;
    }

    @Override
    public List<Map<String, Object>> queryServiceParamsByCondition(String serviceId, String isOutput) {
        Map<String, Object> map = Maps.newHashMap();
        map.put("serviceId", serviceId);
        map.put("isOutput", isOutput);
        String sql = " select * from t_md_service_params where owner_id = :serviceId and is_output = :isOutput and (parent_param_id is null or parent_param_id = '')";
        List<Map<String, Object>> params = this.baseDao.sqlQueryForList(sql, map);
        if (params.size() > 0) {
            for (Map<String, Object> param : params) {
                String desenType = (String) param.get("desen_type");
                EnumServiceParamDesensitization instanceByCode = EnumServiceParamDesensitization.getInstanceByCode(desenType);
                param.put("desen_type", instanceByCode == null ? "" : instanceByCode.getName());
                param.put("pos", "body");
                String parentId = (String) param.get("id");
                List<Map<String, Object>> children = this.getChildParamsByParentId(parentId);
                param.put("children", children);
            }
        }
        List<Map<String, Object>> resultParams = params.stream().filter(s -> !"batchParamVo".equals(s.get("code"))).collect(Collectors.toList());
        return resultParams;
    }

    @Override
    public List<Map<String, Object>> queryMyServiceByType(String serviceType, String userId) {
        /*if ("modelService".equalsIgnoreCase(serviceType)){
            serviceType = "1";
        }else if ("aiService".equalsIgnoreCase(serviceType)){
            serviceType = "2";
        }*/
        EnumServiceType serviceTypeByEnName = EnumServiceType.getServiceTypeByEnName(serviceType);
        serviceType = serviceTypeByEnName.getCode();
        String sql = " select p.name,m.id,p.service_type from t_md_service_meta m inner join t_md_service_publication p on m.service_id = p.id " +
                " and p.service_type = :serviceType and p.operate_user_id = :userId ";
        Map<String, Object> map = Maps.newHashMap();
        map.put("serviceType", serviceType);
        map.put("userId", userId);
        List list = this.baseDao.sqlQueryForList(sql, map);
        return list;
    }


    private List<Map<String, Object>> getChildParamsByParentId(String parentId) {
        String sql = " select * from t_md_service_params where parent_param_id = :parentId ";
        List<Map<String, Object>> params = this.baseDao.sqlQueryForList(sql, this.addParam("parentId", parentId).param());
        if (params.size() > 0) {
            for (Map<String, Object> param : params) {
                String desenType = (String) param.get("desen_type");
                EnumServiceParamDesensitization instanceByCode = EnumServiceParamDesensitization.getInstanceByCode(desenType);
                param.put("desen_type", instanceByCode == null ? "" : instanceByCode.getName());
                String id = (String) param.get("id");
                param.put("pos", "body");
                List<Map<String, Object>> children = this.getChildParamsByParentId(id);
                param.put("children", children);
            }
        }
        return params;
    }


    //获取ai服务训练版本
    private String getPracticeVersion(String sourceId) {
        Map scriptInfo = this.baseDao.sqlQueryForMap(" select id,name from t_script_info where id = (select script_id from t_script_log where id = :sourceId) ",
                this.addParam("sourceId", sourceId).param());
        String scriptId = (String) scriptInfo.get("id");
        try {
            JSONArray logIdAndParams = aiServicePublishService.getLogIdAndParams(scriptId);
            for (Object logIdAndParam : logIdAndParams) {
                JSONObject param = (JSONObject) logIdAndParam;
                if (param.get("log_id").equals(sourceId)) {
                    return (String) param.get("practiceVersion");
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }


        return null;
    }

    private SubscribeParamConfigVo buildServiceApiVo(ServiceMeta serviceMeta, ServicePublication servicePublication, List<String> resourceIds, ResourceConfig resourceConfig) throws Exception {
        SubscribeParamConfigVo paramConfigVo = new SubscribeParamConfigVo();

        paramConfigVo.setImplChineseName(serviceMeta.getName());
        paramConfigVo.setImplEnglishName(serviceMeta.getCode().replace("DC_" + serviceMeta.getVersion() + "_", ""));
        paramConfigVo.setInterfaceEnglishName(StrUtil.lowerFirst(serviceMeta.getServicePublication().getCode()));
        paramConfigVo.setImplVersion(serviceMeta.getVersion());
        paramConfigVo.setInterfaceVersion(serviceMeta.getVersion());
        paramConfigVo.setServiceMetaId(serviceMeta.getId());
        paramConfigVo.setRequestPath(testPublishPath + serviceMeta.getRequestPath());
        paramConfigVo.setMemo(serviceMeta.getMemo());
        paramConfigVo.setStatus(serviceMeta.getStatus());

        paramConfigVo.setServiceType(EnumServiceType.getInstanceByCode(servicePublication.getServiceType()).getName());
        paramConfigVo.setInterfaceChineseName(servicePublication.getName());
        paramConfigVo.setModelId(servicePublication.getSourceId());
        paramConfigVo.setTransId(servicePublication.getSourceId());
        paramConfigVo.setServiceId(servicePublication.getId());
        paramConfigVo.setResourceIds(resourceIds);
        paramConfigVo.setSingleOrManyTable(servicePublication.getIsMultilist());
        paramConfigVo.setSaveType(servicePublication.getSaveType());
        paramConfigVo.setBatchQuery(servicePublication.getBatchQuery());
        paramConfigVo.setMaxQueryNum(servicePublication.getMaxQueryNum());


        if (EnumServiceType.INQUIRY_SERVICE.getCode().equals(servicePublication.getServiceType())) {
            LogicDataObj logicDataObj = logicDataObjService.findLogicDataObjById(paramConfigVo.getModelId());
            paramConfigVo.setDbType(logicDataObj.getDbType());
            buildServiceParam(paramConfigVo, servicePublication.getId());
            paramConfigVo.setSourceName(logicDataObj.getName());
            //查询表字段映射信息
            buildQueryMappings(paramConfigVo, resourceIds);
            //字段排序构建
            buildOrderFieldMapping(paramConfigVo, resourceIds);

        } else if (isBuildParam(servicePublication.getServiceType()) || "2".equals(servicePublication.getIsMultilist())) {
            buildModelServiceParam(paramConfigVo, servicePublication.getId());
            TransMeta transMeta = (TransMeta) this.baseDao.get(TransMeta.class, servicePublication.getSourceId());
            paramConfigVo.setSourceName(transMeta.getName());
        } else if (EnumServiceType.ALGORITHM_SERVICE.getCode().equals(servicePublication.getServiceType())) {
            buildModelServiceParam(paramConfigVo, servicePublication.getId());
            paramConfigVo.setRequestPath("http://" + aiModelIpAndPort.getRunOpServiceIp() + serviceMeta.getRequestPath());
            String practiceVersion = getPracticeVersion(servicePublication.getSourceId());
            paramConfigVo.setPracticeVersion(practiceVersion);
            String sql = "select name from t_script_info where id = (select script_id from t_script_log where id = :source_id)";
            String name = this.baseDao.sqlQueryForValue(sql, this.addParam("source_id", servicePublication.getSourceId()).param());
            paramConfigVo.setSourceName(name);
        } else if (EnumServiceType.INFORMATION_VERFICATION.getCode().equals(servicePublication.getServiceType())) {
            LogicDataObj logicDataObj = logicDataObjService.findLogicDataObjById(paramConfigVo.getModelId());
            paramConfigVo.setDbType(logicDataObj.getDbType());
            buildServiceParam(paramConfigVo, servicePublication.getId());
            paramConfigVo.setSourceName(logicDataObj.getName());
            //查询表字段映射信息
            buildTableColumnMappings(paramConfigVo, resourceIds);
        }
        String serviceId = paramConfigVo.getServiceId();
        String realClassifyId = this.baseDao.sqlQueryForValue("select busi_classify_id from t_md_classify_element where element_id = :serviceId", this.addParam("serviceId", serviceId).param());
        paramConfigVo.setClassifyId(realClassifyId);
        return paramConfigVo;
    }


    private void buildQueryMappings(SubscribeParamConfigVo paramConfigVo, List<String> resourceIds) {
        List<ParamConfigVo.TableColumnMapping> requestmappingList = new ArrayList<>();
        List<ParamConfigVo.TableColumnMapping> returnmappingList = new ArrayList<>();
        for (String resourceId : resourceIds) {
            List<ResourceColumnRelation> resourceColumns = serviceResourceRelation.getResourceColumns(resourceId, paramConfigVo.getServiceId());
            if (resourceColumns.size() > 0) {
                ParamConfigVo.TableColumnMapping requesttableColumnMapping = new ParamConfigVo.TableColumnMapping();
                requesttableColumnMapping.setDatasetId(resourceId);
                requesttableColumnMapping.setDatasetName(resourceColumns.get(0).getResourceName());
                requesttableColumnMapping.setDatasetZhName(resourceColumns.get(0).getResourceZhName());

                ParamConfigVo.TableColumnMapping returntableColumnMapping = new ParamConfigVo.TableColumnMapping();
                returntableColumnMapping.setDatasetId(resourceId);
                returntableColumnMapping.setDatasetName(resourceColumns.get(0).getResourceName());
                returntableColumnMapping.setDatasetZhName(resourceColumns.get(0).getResourceZhName());

                List<ParamConfigVo.ColumnMapping> requestcolumns = new ArrayList<>();
                List<ParamConfigVo.ColumnMapping> returncolumns = new ArrayList<>();
                for (ResourceColumnRelation resourceColumn : resourceColumns) {
                    ParamConfigVo.ColumnMapping columnMapping = new ParamConfigVo.ColumnMapping();
                    columnMapping.setFiledName(resourceColumn.getFiledName());
                    columnMapping.setFieldCode(resourceColumn.getFieldCode());
                    columnMapping.setFieldAsName(resourceColumn.getFieldAsName());
                    columnMapping.setFieldAsCode(resourceColumn.getFieldAsCode());
                    columnMapping.setFieldType(resourceColumn.getFieldType());
                    if ("0".equals(resourceColumn.getParamType())) {
                        requestcolumns.add(columnMapping);
                    } else if ("1".equals(resourceColumn.getParamType())) {
                        returncolumns.add(columnMapping);
                    }
                }
                requesttableColumnMapping.setColumns(requestcolumns);
                returntableColumnMapping.setColumns(returncolumns);
                requestmappingList.add(requesttableColumnMapping);
                returnmappingList.add(returntableColumnMapping);
            }
        }

        paramConfigVo.setTableColumnMappings(requestmappingList);
        paramConfigVo.setReturnParamMappings(returnmappingList);
    }

    private void buildOrderFieldMapping(SubscribeParamConfigVo paramConfigVo, List<String> resourceIds) {
        List<ParamConfigVo.OrderFieldTable> orderFieldTables = new ArrayList<>();
        for (String resourceId : resourceIds) {
            List<ResourceColumnRelation> resourceColumns = serviceResourceRelation.getResourceColumns(resourceId, paramConfigVo.getServiceId()).stream().filter(s -> "2".equals(s.getParamType())).collect(Collectors.toList());
            if (resourceColumns.size() > 0) {
                ParamConfigVo.OrderFieldTable orderFieldTable = new ParamConfigVo.OrderFieldTable();
                orderFieldTable.setDatasetId(resourceColumns.get(0).getResouceId());
                orderFieldTable.setDatasetName(resourceColumns.get(0).getResourceName());
                orderFieldTable.setDatasetZhName(resourceColumns.get(0).getResourceZhName());
                List<ParamConfigVo.OrderField> orderFields = new ArrayList<>();
                for (ResourceColumnRelation resourceColumn : resourceColumns) {
                    ParamConfigVo.OrderField orderField = new ParamConfigVo.OrderField();
                    orderField.setFieldCode(resourceColumn.getFieldCode());
                    orderField.setFieldType(resourceColumn.getFieldType());
                    orderField.setFiledName(resourceColumn.getFiledName());
                    orderField.setOrderType(resourceColumn.getOrderType());
                    orderField.setOrderNum(resourceColumn.getOrderNum());
                    orderFields.add(orderField);
                }
                orderFieldTable.setOrderFields(orderFields);
                orderFieldTables.add(orderFieldTable);
            }
        }
        paramConfigVo.setOrderFieldTables(orderFieldTables);
    }


    private void buildTableColumnMappings(SubscribeParamConfigVo paramConfigVo, List<String> resourceIds) {
        List<ParamConfigVo.TableColumnMapping> mappingList = new ArrayList<>();
        for (String resourceId : resourceIds) {
            List<ResourceColumnRelation> resourceColumns = serviceResourceRelation.getResourceColumns(resourceId, paramConfigVo.getServiceId());
            ParamConfigVo.TableColumnMapping tableColumnMapping = new ParamConfigVo.TableColumnMapping();
            if (resourceColumns.size() > 0) {
                tableColumnMapping.setDatasetId(resourceId);
                tableColumnMapping.setDatasetName(resourceColumns.get(0).getResourceName());
                tableColumnMapping.setDatasetZhName(resourceColumns.get(0).getResourceZhName());
                List<ParamConfigVo.ColumnMapping> columns = new ArrayList<>();
                for (ResourceColumnRelation resourceColumn : resourceColumns) {
                    ParamConfigVo.ColumnMapping columnMapping = new ParamConfigVo.ColumnMapping();
                    columnMapping.setFiledName(resourceColumn.getFiledName());
                    columnMapping.setFieldCode(resourceColumn.getFieldCode());
                    columnMapping.setFieldAsName(resourceColumn.getFieldAsName());
                    columnMapping.setFieldAsCode(resourceColumn.getFieldAsCode());
                    columnMapping.setFieldType(resourceColumn.getFieldType());
                    columns.add(columnMapping);
                }
                tableColumnMapping.setColumns(columns);
                mappingList.add(tableColumnMapping);
            }
        }

        paramConfigVo.setTableColumnMappings(mappingList);
    }

    private boolean isBuildParam(String serviceType) {
        if (EnumServiceType.CALCULATION_SERVICE.getCode().equals(serviceType) ||
                EnumServiceType.COMPARE_SERVICE.getCode().equals(serviceType) ||
                EnumServiceType.DATA_COLLISION.getCode().equals(serviceType)) {
            return true;
        }
        return false;
    }

    private void buildManyJoinConfig(ParamConfigVo paramConfigVo, List<ManyResourceConfig> manyResourceConfigs) throws Exception {
        List<ManyResourceConfig> collect = manyResourceConfigs.stream().sorted(Comparator.comparing(ManyResourceConfig::getJoinSort)).collect(Collectors.toList());
        List<ParamConfigVo.ManyJoinMeta> manyJoinMetas = new ArrayList<>();
        List<ParamConfigVo.ConditionFilterMeta> encasulationJudgContion = new ArrayList<>();
        for (ManyResourceConfig manyResourceConfig : collect) {
            encasulationJudgContion = JSONArray.parseArray(manyResourceConfig.getConditionExp(), ParamConfigVo.ConditionFilterMeta.class);
            ParamConfigVo.ManyJoinMeta manyJoinMeta = (ParamConfigVo.ManyJoinMeta) JsonUtil.json2Obj(manyResourceConfig.getJoinExp(), ParamConfigVo.ManyJoinMeta.class);
            manyJoinMetas.add(manyJoinMeta);
        }
        paramConfigVo.setManyJoinMetas(manyJoinMetas);
        paramConfigVo.setEncasulationJudgContion(encasulationJudgContion);
    }

    private void buildServiceParam(ParamConfigVo paramConfigVo, String serviceId) {
        List<ParamConfigVo.Param> requestParams = Lists.newArrayList();
        List<ParamConfigVo.Param> responseParams = Lists.newArrayList();
        List<ServiceParams> serviceParamsList = serviceParamsService.queryParamByOwnerId(serviceId);
        serviceParamsList.forEach(p -> {
            ParamConfigVo.Param param = new ParamConfigVo.Param();
            param.setParamId(p.getId());
            param.setExample(p.getExample());
            param.setIsMust(p.getIsMust());
            param.setMemo(p.getMemo());
            param.setParamCode(p.getCode());
            param.setParamName(p.getName());
            param.setLikeQuery(p.getLikeQuery());
            param.setDesensitization(p.getDesenType());
            param.setParamValue(p.getParamField());
            param.setDatasetId(p.getResourceId());
            if ("0".equalsIgnoreCase(p.getIsOutput())) {
                param.setType(p.getDataType().split("\\.")[p.getDataType().split("\\.").length - 1]);
                //过滤掉批量参数的bean
                if (!"batchParamVo".equals(param.getParamCode())) {
                    requestParams.add(param);
                }
            } else {
                param.setType(p.getExtendedType());
                responseParams.add(param);
            }
        });
        paramConfigVo.setParamList(requestParams);
        paramConfigVo.setGinsengList(responseParams);
    }


    private void buildModelServiceParam(ParamConfigVo paramConfigVo, String serviceId) {
        List<ParamConfigVo.Param> requestParams = Lists.newArrayList();
        List<ParamConfigVo.Param> responseParams = Lists.newArrayList();
        List<ServiceParams> serviceParamsList = serviceParamsService.queryParamByOwnerIdAndParentParamId(serviceId, null, true);
        if (CollectionUtils.isNotEmpty(serviceParamsList)) {
            serviceParamsList.forEach(p -> {
                if (ServiceParams.EnumPutStatus.IN_PUT.getCode().equals(p.getIsOutput())) {
                    buildModelServiceParam(requestParams, new ArrayList<>(p.getChildrenParam()));
                } else {
                    buildModelServiceParam(responseParams, new ArrayList<>(p.getChildrenParam()));
                }
            });
        }
        paramConfigVo.setParamList(requestParams);
        paramConfigVo.setGinsengList(responseParams);
    }

    private void buildModelServiceParam(List<ParamConfigVo.Param> params, List<ServiceParams> serviceParamsList) {
        if (params == null || serviceParamsList == null) {
            return;
        }
        serviceParamsList.forEach(p -> {
            ParamConfigVo.Param param = new ParamConfigVo.Param();
            param.setParamId(p.getId());
            param.setExample(p.getExample());
            param.setIsMust(p.getIsMust());
            param.setMemo(p.getMemo());
            param.setParamCode(p.getCode());
            param.setParamName(p.getName());
            param.setDesensitization(p.getDesenType());
            param.setType(p.getDataType().split("\\.")[p.getDataType().split("\\.").length - 1]);
            params.add(param);
            if (CollectionUtils.isNotEmpty(p.getChildrenParam())) {
                if (param.getChildren() == null) {
                    param.setChildren(new ArrayList<>());
                }
                buildModelServiceParam(param.getChildren(), new ArrayList<>(p.getChildrenParam()));
            }
        });
    }


    private String queryPublicIdServiceMetaId(String serviceMetaId) {
        String sql = "select service_id from t_md_service_meta where id =:serviceMetaId";
        String id = this.baseDao.sqlQueryForValue(sql, addParam("serviceMetaId", serviceMetaId).param());
        Assert.notNull(id, "找不到该服务，请确认该服务是否已被卸载");
        return id;
    }

    @Override
    public Result queryPublishedServiceMetaSelectList(String sourceId) {
        String hql = "from ServiceMeta where servicePublication.sourceId = :sourceId order by publishTime desc";
        List<ServiceMeta> serviceMetas = baseDao.queryForList(hql, addParam("sourceId", sourceId).param());
        if (CollectionUtils.isNotEmpty(serviceMetas)) {
            List<Map<String, Object>> resultList = serviceMetas.stream().map(serviceMeta -> {
                Map<String, Object> map = new HashMap<>();
                map.put("id", serviceMeta.getId());
                map.put("name", serviceMeta.getServicePublication().getName());
                return map;
            }).collect(Collectors.toList());
            return Result.success(resultList);
        }
        return Result.success();
    }

    @Override
    public String newServiceClassify(String parentId, String name, String code, String userId) {
        Assert.hasLength(name, "分类中文名不能为空！");
        Assert.hasLength(parentId, "父目录不能为空！");
        Assert.hasLength(userId, "请先登录！");
        checkClassifyName(parentId, name, null);
        if (StringUtils.isBlank(code)) {
            code = SERVICE_CLASSIFY_CODE;
        }
        BaseBusiClassify classify = new BaseBusiClassify();
        classify.setName(name);
        classify.setCode(code);
        BaseBusiDir baseBusiDir = queryBusiDirByCode(SERVICE_DIR_CODE);
        classify.setBusiDir(baseBusiDir);
        BaseBusiClassify parentClassify = busiClassifyService.findBusiClassifyBy(parentId);
        if (parentClassify == null) Assert.fail("该父目录不存在！");
        classify.setParentBc(parentClassify);
        classify.setOperateTime(DateUtils.getDateFormat("yyyy-MM-dd HH:mm:ss"));
        classify.setOperateUserId(userId);
        classify.setOwnerId(parentId);
        classify.setOwner(parentClassify);
        this.baseDao.saveOrUpdate(classify);
        return classify.getId();
    }

    @Override
    public List<Tree> queryServiceClassifyTree(String userId) {
        Assert.hasLength(userId, "请先登录!");
        BaseBusiDir busiDir = queryBusiDirByCode(SERVICE_DIR_CODE);
        return getTreeByBusiDirId(busiDir.getId(), userId);
    }

    @Override
    public List<Tree> queryServiceTree(String userId) {
        Assert.hasLength(userId, "请先登录!");
        BaseBusiDir busiDir = queryBusiDirByCode(SERVICE_DIR_CODE);
        List<Tree> treeByBusiDirId = getTreeByBusiDirId(busiDir.getId(), userId);
        addServiceElementByTree(treeByBusiDirId);

        return treeByBusiDirId;

    }

    private void addServiceElementByTree(List<Tree> trees) {
        for (Tree tree : trees) {
            List<Tree> children = (List<Tree>) tree.getChildren();
            if (CollectionUtil.isNotEmpty(children)) {
                addServiceElementByTree(children);
            }
            if (tree.getElementOrClassify().equals("0")) {
                List<Map<String, String>> servicesByClassify = getServicesByClassify(tree.getId());
                for (Map<String, String> map : servicesByClassify) {
                    Tree element = new Tree();
                    element.setId(map.get("id"));
                    element.setName(map.get("name"));
                    element.setType(EnumServiceType.getInstanceByCode(map.get("service_type")).getEnName());
                    element.setCode(map.get("code"));
                    element.setOprateTime(map.get("operate_time"));
                    element.setElementOrClassify("1");
                    element.setMemo(map.get("memo"));
                    element.setOperateUserId(map.get("operate_user_id"));
                    Map<String, Object> others = new HashMap<>();
                    String serviceMetaId = map.get("service_meta_id");
                    others.put("serviceMetaId", serviceMetaId);
                    String sql = "select status from t_md_service_meta where id = :id";
                    String status = this.baseDao.sqlQueryForValue(sql, addParam("id", serviceMetaId).param());
                    //ServiceMeta serviceMeta = serviceMetaService.queryByMetaId(serviceMetaId);
                    others.put("apiStatus", status);

                    element.setOthers(others);
                    children.add(element);
                }
            }
            tree.setChildren(children);
        }


    }

    private List<Map<String, String>> getServicesByClassify(String classifyId) {
        String sql = "select p.*,m.id as service_meta_id from t_md_service_publication p inner join t_md_service_meta m on m.service_id = p.id " +
                " and p.id in (select element_id from t_md_classify_element where busi_classify_id = :classifyId)";
        return this.baseDao.sqlQueryForList(sql, addParam("classifyId", classifyId).param());
    }

    @Override
    public void deleteServiceClassify(String id) {
        List<String> classifyIds = queryBusiClassifyIdList(id);
        if (CollectionUtils.isNotEmpty(classifyIds)) {
            List<BaseBusiClassify> busiClassifyList = new ArrayList<>();
            for (String classifyId : classifyIds) {
                BaseBusiClassify curBusiClassify = (BaseBusiClassify) baseDao.get(BaseBusiClassify.class, classifyId);
                if (curBusiClassify != null) {
                    if (CollectionUtils.isNotEmpty(curBusiClassify.getElements())) {
                        Assert.fail("该目录存在服务，无法删除");
                    }
                    busiClassifyList.add(curBusiClassify);
                }

            }
            deleteElement(busiClassifyList);
            baseDao.delete(busiClassifyList);
        }
    }


    private void deleteElement(List<BaseBusiClassify> baseBusiClassifies) {
        for (BaseBusiClassify classify : baseBusiClassifies) {
            String sql = "delete from t_md_classify_element where busi_classify_id=:classifyId";
            this.baseDao.executeSqlUpdate(sql, addParam("classifyId", classify.getId()).param());
        }
    }


    @Override
    public void editServiceClassify(String id, String parentId, String name, String code, String userId) {
        Assert.hasLength(id, "分类id不能为空！");
        Assert.hasLength(name, "分类中文名不能为空！");
        Assert.hasLength(parentId, "父目录不能为空！");
        Assert.hasLength(userId, "请先登录！");
        if (StringUtils.isBlank(code)) {
            code = SERVICE_CLASSIFY_CODE;
        }
        checkClassifyName(parentId, name, id);
        BaseBusiClassify baseBusiClassify = busiClassifyService.findBusiClassifyBy(id);
        if (baseBusiClassify == null) Assert.fail("该目录不存在！");
        baseBusiClassify.setName(name);
        baseBusiClassify.setCode(code);
        BaseBusiClassify parentClassify = busiClassifyService.findBusiClassifyBy(parentId);
        if (parentClassify == null) Assert.fail("该父目录不存在！");
        baseBusiClassify.setParentBc(parentClassify);
        baseBusiClassify.setOperateTime(DateUtils.getDateFormat("yyyy-MM-dd HH:mm:ss"));
        baseBusiClassify.setOperateUserId(userId);
        baseBusiClassify.setOwnerId(parentId);
        baseBusiClassify.setOwner(parentClassify);
        this.baseDao.saveOrUpdate(baseBusiClassify);
    }

    @Override
    public void moveModelService(String id, String classifyId) {
        servicePublicService.checkTheSameNameInClassify(id, classifyId);
        String sql = "update t_md_classify_element set busi_classify_id = :classifyId where element_id = :id";
        this.baseDao.executeSqlUpdate(sql, this.addParam("classifyId", classifyId).addParam("id", id).param());
    }


    private BaseBusiDir queryBusiDirByCode(String code) {
        String hql = " from BaseBusiDir where code = :code";
        return (BaseBusiDir) this.baseDao.queryForObject(hql, this.addParam("code", code).param());
    }


    private void checkClassifyName(String parentId, String name, String id) {
        String sql = "select count(*) from t_md_busi_classify where name = :name and parent_classify_id = :parentId";
        Map<String, Object> params = new HashMap<>();
        params.put("name", name);
        params.put("parentId", parentId);

        if (StringUtils.isNotBlank(id)) {
            sql += " and id != :id";
            params.put("id", id);
        }
        String value = this.baseDao.sqlQueryForValue(sql, params);
        int count = Integer.parseInt(value);
        if (count > 0) {
            Assert.fail("该目录下已存在同名的分类，请重新命名后再操作！");
        }
    }

    @Override
    public List<String> queryBusiClassifyIdList(String classifyId) {
        List<String> classifyIds = new ArrayList<>();
        //查询实体目录
        String classifysql = " with recursive dict as ( " +
                " select * from t_md_busi_classify where id = :classifyId union ALL " +
                " select classify.* from t_md_busi_classify classify, dict where classify.parent_classify_id = dict.id) " +
                " select d.* from dict d ";

        List<Map<String, Object>> classifyList = baseDao.sqlQueryForList(classifysql, addParam("classifyId", classifyId).param());

        if (CollectionUtils.isNotEmpty(classifyList)) {
            classifyIds = classifyList.stream().map(dataMap -> dataMap.get("id").toString()).collect(Collectors.toList());
        }
        return classifyIds;
    }

    @Override
    public List<Tree> getOtherApiByUser(String userId) {
        String userSql = "select operate_user_id from t_md_service_publication where operate_user_id !=:userId " +
                "and operate_user_id is not null group by  operate_user_id";
        List<Map<String, String>> userMapList = baseDao.sqlQueryForList(userSql, addParam("userId", userId).param());
        if (CollUtil.isEmpty(userMapList)) {
            return new ArrayList<>();
        }
        List<String> userIdList = new ArrayList<>();
        Tree tree = new Tree();
        tree.setName("他人服务");
        tree.setId(GlobalConstant.CommonProperties.FROM_PERSONAL_TOP_NODE_ID);
        List<Tree> resultList = new ArrayList<>();
        tree.setChildren(resultList);
        userMapList.forEach(m -> userIdList.add(m.get("operate_user_id")));
        Map<String, String> userMap = userService.getUserMap(userIdList);
        userMap.forEach((otherUserId, userName) -> {
            List<Tree> treeList = queryServiceClassifyTree(otherUserId);
            for (Tree model : treeList) {
                if (model.getName().equals("我的服务")) {
                    model.setName(userName);
                    model.setParentId(tree.getId());
                    model.setOperateUserId(otherUserId);
                }
            }
            resultList.addAll(treeList);

        });
        return Lists.newArrayList(tree);
    }

    @SneakyThrows
    private List<Tree> getTreeByBusiDirId(String busiDirId, String userId) {
        String sql = "select * from t_md_busi_classify where busi_dir_id = :busiDirId and operate_user_id = :userId and (parent_classify_id is null or parent_classify_id = '')";
        //第一层
        List<Map<String, Object>> firstClassifys = this.baseDao.sqlQueryForList(sql, this.addParam("busiDirId", busiDirId).addParam("userId", userId).param());
        List<Tree> trees = new ArrayList<>();
        for (Map<String, Object> firstClassify : firstClassifys) {
            Tree tree = TenonBeanUtil.convertMapToBean(firstClassify, Tree.class);
            tree.setChildren(getChildrenTree(tree.getId()));
            tree.setElementOrClassify("0");
            tree.setOprateTime((String) firstClassify.get("operate_time"));
            tree.setOperateUserId((String) firstClassify.get("operate_user_id"));
            trees.add(tree);
        }
        return trees;
    }

    @SneakyThrows
    private List<Tree> getChildrenTree(String parentId) {
        List<Tree> trees = Lists.newArrayList();
        String sql = "select * from t_md_busi_classify where parent_classify_id = :parentId";
        List<Map<String, Object>> children = this.baseDao.sqlQueryForList(sql, this.addParam("parentId", parentId).param());
        if (CollectionUtil.isNotEmpty(children)) {
            for (Map<String, Object> child : children) {
                Tree tree = TenonBeanUtil.convertMapToBean(child, Tree.class);
                tree.setParentId(parentId);
                tree.setChildren(getChildrenTree(tree.getId()));
                tree.setElementOrClassify("0");
                tree.setOprateTime((String) child.get("operate_time"));
                tree.setOperateUserId((String) child.get("operate_user_id"));
                trees.add(tree);
            }
        }
        return trees;
    }

    @Override
    public List<GetModelServiceClassifyVo> getModelServiceClassify() {
        ServiceClassifyEnum[] values = ServiceClassifyEnum.values();
        List<GetModelServiceClassifyVo> collect =Arrays.stream(values)
        .filter(e -> ServiceClassifyEnum.getServiceClassifyEnumToLoadModel().contains(e)).map(e -> {
            GetModelServiceClassifyVo vo = new GetModelServiceClassifyVo();
            String label = e.getName();
            String value = e.getCode();
            vo.setLabel(label);
            vo.setValue(value);
            List<GetModelServiceClassifyVo> secondClassifys = new ArrayList<>();
            secondClassifys.add(new GetModelServiceClassifyVo("全部", "all", Collections.emptyList()));
            if (e.equals(ServiceClassifyEnum.ALL) || e.equals(ServiceClassifyEnum.LOCAL_SERVICE)) {
                secondClassifys.addAll(Arrays.stream(EnumServiceType.values()).filter(t -> ServiceClassifyEnum.getEnumServiceTypeListToLoadModel().contains(t)
                ).map(t -> {
                    GetModelServiceClassifyVo child = new GetModelServiceClassifyVo();
                    child.setLabel(t.getName());
                    child.setValue(t.getEnName());
                    child.setChildren(null);
                    return child;
                }).collect(Collectors.toList()));
            }
            vo.setChildren(secondClassifys);

            return vo;
        }).collect(Collectors.toList());

        return collect;
    }

    @Override
    public List<GetPluginModelServiceListOutVo> getPluginModelServiceList(String firstClassify, String secondClassify, String userId) {
        List<GetPluginModelServiceListOutVo> vos = new ArrayList<>();
        if (firstClassify.equals(ServiceClassifyEnum.ALL.code)) {
            vos = getModelServiceListByAll(secondClassify, userId);
        } else if (firstClassify.equals(ServiceClassifyEnum.LOCAL_SERVICE.code)) {
            vos = getGetPluginModelServiceListByLocal(secondClassify, userId);
        }

        return vos;
    }

    //本地
    private List<GetPluginModelServiceListOutVo> getGetPluginModelServiceListByLocal(String secondClassify, String userId) {
        List<GetPluginModelServiceListOutVo> vos;
        EnumServiceType serviceTypeByName = EnumServiceType.getServiceTypeByEnName(secondClassify);
        String sql = "select is_multilist,id,name,operate_time,service_type from t_md_service_publication where operate_user_id = :userId and service_type != '4' ";
        Map<String, Object> params = new HashMap<>();
        params.put("userId", userId);
        if (serviceTypeByName != null) {
            sql += " and service_type = :serviceType ";
            params.put("serviceType", serviceTypeByName.getCode());
        }
        sql += " order by operate_time desc ";
        List<Map<String, String>> servicePubs = this.baseDao.sqlQueryForList(sql, params);
        vos = servicePubs.stream().map(s -> {
            GetPluginModelServiceListOutVo vo = new GetPluginModelServiceListOutVo();
            vo.setName(s.get("name"));
            vo.setServiceId(s.get("id"));
            vo.setOperateTime(s.get("operate_time"));
            EnumServiceType serviceType = EnumServiceType.getInstanceByCode(s.get("service_type"));
            vo.setServiceType(serviceType.getEnName());
            String multilist = s.get("is_multilist");
            vo.setPluginCode(getPluginCodeByServiceType(serviceType, multilist));

            vo.setServiceClassify(ServiceClassifyEnum.LOCAL_SERVICE.code);
            return vo;
        }).collect(Collectors.toList());
        return vos;
    }

    private List<GetPluginModelServiceListOutVo> getModelServiceListByAll(String secondClassify, String userId) {
        List<GetPluginModelServiceListOutVo> getPluginModelServiceListOutVos = new ArrayList<>();
        if (StringUtils.isBlank(secondClassify) || secondClassify.equals("all")) {

            getPluginModelServiceListOutVos.addAll(getGetPluginModelServiceListByLocal(null, userId));
            //...

        } else {
            List<String> localNames = Arrays.stream(EnumServiceType.values()).map(e -> e.getEnName()).collect(Collectors.toList());
            if (localNames.contains(secondClassify)) {
                //本地
                getPluginModelServiceListOutVos = getGetPluginModelServiceListByLocal(secondClassify, userId);

            }
            //TODO 其它 第三方或者代理
        }

        return getPluginModelServiceListOutVos;

    }


    private String getPluginCodeByServiceType(EnumServiceType serviceType, String multilist) {
        if (serviceType.equals(EnumServiceType.ALGORITHM_SERVICE)) {
            return "cicadaAiModelService";
        } else if (serviceType.equals(EnumServiceType.CALCULATION_SERVICE)) {
            return "cicadaAnalysisModelService";
        } else if (serviceType.equals(EnumServiceType.COMPARE_SERVICE)) {
            return "cicadaCompareServiceModelService";
        } else if (serviceType.equals(EnumServiceType.INFORMATION_VERFICATION) && "2".equals(multilist)) {
            return "cicadaInfoCheckModelService";
        } else if (serviceType.equals(EnumServiceType.INFORMATION_VERFICATION)) {
            return "cicadaInfoVerificationModelService";
        } else if (serviceType.equals(EnumServiceType.DATA_COLLISION)) {
            return "cicadaDataCollisionModelService";
        }
        return "";
    }

    @Override
    public List<String> checkPlugin(TransClassifyVo transClassifyVo) {
        List<String> list = Lists.newArrayList();
        TransMeta transMeta = (TransMeta) this.baseDao.get(TransMeta.class, transClassifyVo.getTransId());
        if (transMeta == null) return list;
        Set<TransMeta> children = transMeta.getChildren();
        Map<String, Integer> map = new HashMap<>();
        Map<String, String> dirMap = new HashMap<>();
        for (TransMeta transMeta1 : children) {
            if (map.get(transMeta1.getUsedPlugin().getCode()) != null) {
                map.put(transMeta1.getUsedPlugin().getCode(), map.get(transMeta1.getUsedPlugin().getCode()) + 1);
                dirMap.put(transMeta1.getUsedPlugin().getDefPluginDir(), transMeta1.getUsedPlugin().getDefPluginDir());
            } else {
                map.put(transMeta1.getUsedPlugin().getCode(), 1);
                dirMap.put(transMeta1.getUsedPlugin().getDefPluginDir(), transMeta1.getUsedPlugin().getDefPluginDir());
            }
        }

        Integer pluginInput = map.get("cicadaMetaServiceInput");
        Integer pluginOutPut = map.get("cicadaMetaServiceOutput");
        Integer pluginCheckOutPut = map.get("cicadaMetaServiceCheckOutPut");
        list.add(EnumServiceType.CALCULATION_SERVICE.getCode());

        if ((checkCollision(map) && pluginInput != null && pluginOutPut != null && pluginInput == 1 && pluginOutPut == 1) && (checkCollisionDir(dirMap))) {
            if ((pluginInput != null && pluginCheckOutPut != null && pluginInput == 1 && pluginCheckOutPut == 1)) {
                list.add(EnumServiceType.INFORMATION_VERFICATION.getCode());
            } else {
                list.add(EnumServiceType.COMPARE_SERVICE.getCode());
                list.add(EnumServiceType.DATA_COLLISION.getCode());
            }
        } else if ((checkCollision(map) && pluginInput != null && pluginOutPut != null && pluginInput >= 1 && pluginOutPut >= 1) && (checkCollisionDir(dirMap))) {
            if ((pluginInput != null && pluginCheckOutPut != null && pluginInput == 1 && pluginCheckOutPut == 1)) {
                list.add(EnumServiceType.INFORMATION_VERFICATION.getCode());
            } else {
                list.add(EnumServiceType.DATA_COLLISION.getCode());
            }
        } else if ((pluginInput != null && pluginCheckOutPut != null && pluginInput == 1 && pluginCheckOutPut == 1)) {
            list.add(EnumServiceType.INFORMATION_VERFICATION.getCode());
        }
        if (list.contains(EnumServiceType.INFORMATION_VERFICATION) || (pluginCheckOutPut != null && pluginCheckOutPut > 0)) {
            list.remove(EnumServiceType.CALCULATION_SERVICE.getCode());
        }
        return list;

    }

    @SneakyThrows
    @Override
    public TestApiResultVo testServiceResult(TestApiParamVo paramVo, HttpServletRequest request) {
        String serviceId = paramVo.getServiceId();
        Assert.hasLength(serviceId, "服务id不能为空！");
        ServicePublication servicePublication = servicePublicationService.getServicePublication(serviceId);
        Set<ServiceMeta> serviceMetas = servicePublication.getServiceMetas();
        if (CollectionUtil.isEmpty(serviceMetas)) {
            Assert.fail("服务元信息不存在！");
        }
        String serviceMetaId = "";
        for (ServiceMeta serviceMeta : serviceMetas) {
            serviceMetaId = serviceMeta.getId();
            break;
        }
        String serviceType = servicePublication.getServiceType();
        EnumServiceType serviceTypeInstance = EnumServiceType.getInstanceByCode(serviceType);
        long start = System.currentTimeMillis();
        long end = 0l;

        String testDataJson = paramVo.getTestDataJson();
        TestApiResultVo resultVo = new TestApiResultVo();
        resultVo.setCode("200");
        resultVo.setResult("success");
        resultVo.setServicePublicationId(serviceId);
        resultVo.setApiName(servicePublication.getName());
        if (serviceTypeInstance.equals(EnumServiceType.INQUIRY_SERVICE) || (serviceTypeInstance.equals(EnumServiceType.INFORMATION_VERFICATION) && !"2".equals(servicePublication.getIsMultilist()))) {
            JSONArray jsonArray = JSONArray.parseArray(testDataJson);
            List<Map<String, Object>> paramVoList = new ArrayList<>();
            for (Object o : jsonArray) {
                Map<String, Object> param = (Map<String, Object>) o;
                paramVoList.add(param);
            }
            BatchTestVo testVo = new BatchTestVo();
            testVo.setServiceMetaId(serviceMetaId);
            testVo.setToken(GlobalConstant.UserProperties.DC_SUPER_ID);
            testVo.setParamVoList(paramVoList);
            R r = servicePublicService.testBatchService(testVo, request);
            String data = (String) r.get("data");
            resultVo.setData(data);
            if ((Integer) r.get("code") == 1) {
                String msg = (String) r.get("msg");
                int errorMsgIndex = msg.indexOf("message:");
                String code = msg.substring(5, errorMsgIndex).trim();
                resultVo.setCode(code);
                resultVo.setResult("fail");
            }
        } else if (serviceTypeInstance.equals(EnumServiceType.CALCULATION_SERVICE) || serviceTypeInstance.equals(EnumServiceType.COMPARE_SERVICE)
                || serviceTypeInstance.equals(EnumServiceType.DATA_COLLISION) || (serviceTypeInstance.equals(EnumServiceType.INFORMATION_VERFICATION) && "2".equals(servicePublication.getIsMultilist()))) {
            ModelServiceRequestVo requestVo = new ModelServiceRequestVo();
            requestVo.setServiceMetaId(serviceMetaId);
            requestVo.setTestDataJson(paramVo.getTestDataJson());
            requestVo.setToken(GlobalConstant.UserProperties.DC_SUPER_ID);
            Result testResult = null;
            if ("getValue".equals(paramVo.getApiType())) {
                //异步
                requestVo.setRequestId(paramVo.getRequestId());
                requestVo.setRequestUrl(paramVo.getRequestUrl());
                requestVo.setPageIndex(paramVo.getPageIndex());
                requestVo.setPageSize(paramVo.getPageSize());
                testResult = cicadaMetaServicePublishService.testServiceGetResult(requestVo, request);
            } else {
                testResult = cicadaMetaServicePublishService.testService(requestVo, request);
            }
            if (testResult.getCode() == 1) {
                resultVo.setResult("fail");
                resultVo.setCode(testResult.getErrorCode());
            }
            resultVo.setData(testResult.getData());

        } else if (serviceTypeInstance.equals(EnumServiceType.ALGORITHM_SERVICE)) {
            JSONObject object = JSONObject.parseObject(testDataJson);
            Map map = aiServicePublishService.testAiService(serviceId, object);
            boolean success = (boolean) map.get("success");
            if (!success) {
                resultVo.setResult("fail");
                resultVo.setCode("500");
            }
            resultVo.setData(JSONObject.toJSONString(map));
        } else {
            Assert.fail("不存在的服务类型！");
        }
        end = System.currentTimeMillis();
        resultVo.setUsedTime(getUsingSeconds(start, end));
        return resultVo;
    }

    private long getUsingSeconds(long start, long end) {
        return end - start;
    }

    private boolean checkCollision(Map<String, Integer> map) {
        Integer pluginFull = map.get("cicadaFullJoinPlugin");
        Integer pluginCollision = map.get("cicadaCollisionPlugin");
        Integer pluginInner = map.get("cicadaInnerJoinPlugin");
        Integer pluginLeft = map.get("cicadaLeftOrRightJoinPlugin");
        Integer pluginUnion = map.get("cicadaUnionJoinPlugin");
        Integer pluginByKey = map.get("cicadaSubtractByKeyPlugin");
        return ((pluginFull != null && pluginFull >= 1) || (pluginCollision != null && pluginCollision >= 1) ||
                (pluginInner != null && pluginInner >= 1) || (pluginLeft != null && pluginLeft >= 1) ||
                (pluginUnion != null && pluginUnion >= 1) || (pluginByKey != null && pluginByKey >= 1));
    }


    //订阅
    private boolean checkCollisionDir(Map<String, String> mapDir) {
        List<String> dirList = Lists.newArrayList(PluginDirType.DATA_INPUT_OUTPUT_TYPE, PluginDirType.DATA_COLLISION_TYPE, PluginDirType.DATA_SCREENING_TYPE, PluginDirType.DATA_SERVICE_TYPE);
        for (Map.Entry<String, String> dir : mapDir.entrySet()) {
            if (!dirList.contains(dir.getKey())) {
                return false;
            }
        }
        return true;
    }
}
