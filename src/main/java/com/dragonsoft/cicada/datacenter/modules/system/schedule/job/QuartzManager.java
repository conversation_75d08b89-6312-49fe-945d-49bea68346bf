package com.dragonsoft.cicada.datacenter.modules.system.schedule.job;

import org.quartz.*;
import org.quartz.impl.StdSchedulerFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020-11-30 18:22
 */
public class QuartzManager {

    private static final SchedulerFactory SF = new StdSchedulerFactory();
    private static final String JOB_GROUP_NAME = "DROP_EXT_TABLE_JOB_GROUP";
    private static final String TRIGGER_GROUP_NAME = "DROP_EXT_TABLE_TRIGGER_GROUP";
    private static final  Logger logger = LoggerFactory.getLogger(QuartzManager.class);

    public static void addJob(String jobName, Map<String, Object> params, Class<? extends Job> jobClass, String cronExp) {
        try {
            Scheduler scheduler = SF.getScheduler();
            JobDetail jobDetail = JobBuilder.newJob(jobClass)
                    .withIdentity(jobName, JOB_GROUP_NAME)
                    .setJobData(new JobDataMap(params))
                    .build();
            CronTrigger trigger = TriggerBuilder.newTrigger()
                    .withIdentity(jobName, TRIGGER_GROUP_NAME)
                    .withSchedule(CronScheduleBuilder.cronSchedule(cronExp))
                    .build();

            scheduler.scheduleJob(jobDetail, trigger);
            scheduler.start();
            if (logger.isDebugEnabled()) {
                logger.debug("add job [name = {}, group = {}]", jobName, TRIGGER_GROUP_NAME);
            }
        } catch (SchedulerException e) {
            logger.error(e.getMessage(),e);
            throw new RuntimeException(e);
        }
    }

    public static void removeJob(String jobName) {
        try {
            Scheduler scheduler = SF.getScheduler();
            scheduler.pauseTrigger(TriggerKey.triggerKey(jobName, TRIGGER_GROUP_NAME));
            scheduler.unscheduleJob(TriggerKey.triggerKey(jobName, TRIGGER_GROUP_NAME));
            scheduler.deleteJob(JobKey.jobKey(jobName, JOB_GROUP_NAME));
        } catch (Exception e) {
            logger.error(e.getMessage(),e);
            throw new RuntimeException(e);
        }
    }
}
