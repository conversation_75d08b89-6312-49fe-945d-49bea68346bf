package com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.vo.LogicDataSet;

import com.code.dataset.operator.filter.FilterConditionStep;
import com.code.dataset.operator.join.JoinTableStep;
import com.code.metaservice.ddl.vo.LogicDataSetColumnVo;
import lombok.Data;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/10/12 16:41
 */
@Data
public class DataSetJoinVo {
    private List<JoinTableStep> joinTableSteps;
    FilterConditionStep filterConditionStep;
    private String dataSetId;
    private String dataSetName;
    private String dataSetCode;
    /**
     * 数据对象ids，一个数据集可能对应多个表
     */
    private List<String> dataObjIds;
    /**
     * 字段列表
     */
    private List<LogicDataSetColumnVo> columns;

}
