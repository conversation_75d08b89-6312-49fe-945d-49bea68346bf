package com.dragonsoft.cicada.datacenter.modules.datavisual.model.enums;


public enum WidgetType {

    CHART("1", "图表", "图表控件", null),
    BAR_CHART("1_1", "柱状图", "条形图", CHART),
    LINE_CHART("1_2", "折线图", "折线图", CHART),
    PIE_CHART("1_3", "饼图", "饼图", CHART),
    TABLE_CHART("1_4", "表格", "表格", CHART),
    MAP_CHART("1_5", "地图", "地图", CHART),
    SELECT("2", "查询", "查询控件", null),
    CONDITION("2_1", "条件", "条件控件", SELECT),
    SMALL("3", "小控件", "小控件", null),
    INDICATOR_CARD("1_6", "指标卡", "指标卡", CHART),
    PGIS("1_8", "PGIS地图", "PGIS地图", CHART),
    HeatMap("1_9", "热力图", "热力图", CHART),
    Combination("1_11", "组合图", "组合图", CHART),
    Radar("1_12", "雷达图", "雷达图", CHART),
    WordCloud("1_10", "词云图", "词云图", CHART),
    RELATIONSHIP("1_13", "关系图", "关系图", CHART),
    FORMWIDGET("1_14", "表单", "表单", CHART);

    private String id;
    private String name;
    private String description;
    private WidgetType parent;

    WidgetType(String id, String name, String description, WidgetType parent) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.parent = parent;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public WidgetType getParent() {
        return parent;
    }

    public void setParent(WidgetType parent) {
        this.parent = parent;
    }
}
