package com.dragonsoft.cicada.datacenter.modules.metadata.vo;

import lombok.Data;

import java.io.Serializable;

@Data
public class MachineVo implements Serializable {

    private String cpuCoreNum;
    private String cpuCoreSpeed;
    private String cpuNum;
    private String diskSize;
    private String hostName;
    private String id;
    private String ipAddress;
    private String macAddress;
//    private MachineType machineType;
    private String memo;
    private String memorySize;
    private String machineName;
    private String password;
    private String userName;

    //label
    private String contactUser;
    private String operatingSystem;
    private String mainUsage;
    private String connected;
    private String contactPhone;

    //monitor
    private String state;
    private String execStatus;
    //唯一标识
    private String uniqueIdentification;
    private String createTime;

}
