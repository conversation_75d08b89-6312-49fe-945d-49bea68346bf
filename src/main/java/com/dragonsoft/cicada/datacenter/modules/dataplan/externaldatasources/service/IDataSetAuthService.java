package com.dragonsoft.cicada.datacenter.modules.dataplan.externaldatasources.service;

import com.dragoninfo.dfw.entity.TSysAuthObj;
import com.dragoninfo.dfw.entity.TSysAuthRole;
import com.dragonsoft.cicada.datacenter.modules.dataplan.externaldatasources.vo.DataObjectVo;
import com.dragonsoft.cicada.datacenter.modules.dataplan.externaldatasources.vo.DataSetAuthVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/6/8
 */
public interface IDataSetAuthService {
    /**
     * 注册数据源到授权表里
     *
     * @param dataObjectVos
     */
    void saveDataSetAuth(List<DataObjectVo> dataObjectVos, String userId);

    /**
     * 添加数据源时授权
     *
     * @param dataSetAuthVo
     */
    void saveFirstAuth(DataSetAuthVo dataSetAuthVo);

    /**
     * 批量授权
     *
     * @param dataSetAuthVo
     */
    void saveBatchDataSetAuth(DataSetAuthVo dataSetAuthVo, Boolean hasOldUserOrRole);



    /**
     * 单个授权 用户
     *
     * @param dataSetAuthVo
     */
    void saveOneDataSetUserAuth(DataSetAuthVo dataSetAuthVo);

    /**
     * 单个授权 用户
     *
     * @param dataSetAuthVo
     */
    void saveOneDataSetRoleAuth(DataSetAuthVo dataSetAuthVo);

    /**
     * 取消授权（删除数据集与对象关系）
     *
     * @param dataSetAuthVo
     */
    void deletDataSetAuthRelation(DataSetAuthVo dataSetAuthVo);

    /**
     * 删除数据集时删除授权关系
     *
     * @param DataSetId
     */
    void deleteDataSetRelation(String DataSetId, String id);

    /**
     * 是否为已授权数据对象
     *
     * @param tableId
     * @return
     */
    Boolean IsAuthDataset(String tableId, String userId, List<TSysAuthRole> roleList);


    void saveLibraryUserRelation(String userId, String libraryId);

    void deleteAccreditDataSet(String code, String userID);

    /**
     * 3.0 授权逻辑数据集
     */
    void addDCThreeDataSetAuth(DataSetAuthVo dataSetAuthVo);

    /**
     * 3.0批量授权逻辑数据集
     * @param dataSetAuthVo
     * @param hasOldUserOrRole
     */
    void saveDCThreeBatchDataSetAuth(DataSetAuthVo dataSetAuthVo, Boolean hasOldUserOrRole);

    /**
     * 3.0 取消授权（删除数据集与对象关系）
     *
     * @param dataSetAuthVo
     */
    void deleteDCThreeDataSetAuthRelation(DataSetAuthVo dataSetAuthVo,String userId);


    /**
     *
     */
    /**
     * 3.0注册数据源到授权表里
     * 这个动作放在每次授权之前，如果库里面没有这个functioncode，再放到库里
     *
     * @param dataObjectVos
     */
    void saveDataSetAuthDCThree(List<DataObjectVo> dataObjectVos, String userId);

    /**
     * 检验数据集是否是逻辑数据集
     * @param datasetId
     * @return
     */
    boolean checkLogicDataObj(String datasetId);


    void saveDataSetRelation(TSysAuthObj tSysAuthObj, List<String> functionCodes);

    void saveDataSetRelationNew(TSysAuthObj tSysAuthObj, List<String> functionCodes);

    void saveFirstAuthNew(DataSetAuthVo dataSetAuthVo);

}
