package com.dragonsoft.cicada.datacenter.modules.datavisual.controller;

import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.fastjson.JSONObject;
import com.code.common.paging.PageInfo;
import com.code.common.utils.R;
import com.code.common.utils.assertion.Assert;
import com.code.metadata.datavisual.Dashboard;
import com.code.metadata.datavisual.DashboardGroup;
import com.code.metaservice.datavisual.IDashBoardService;
import com.code.metaservice.datavisual.IDashboardGroupService;
import com.dragoninfo.dfw.annotation.FuncScanAnnotation;
import com.dragoninfo.dfw.annotation.ValidateAndLogAnnotation;
import com.dragoninfo.dfw.bean.Result;
import com.dragoninfo.dfw.entity.TSysAuthUser;
import com.dragoninfo.dfw.entity.TSysFunc;
import com.dragoninfo.dfw.enums.BusiErrorCode;
import com.dragoninfo.dfw.exception.BusiException;
import com.dragoninfo.dfw.service.SysAuthUserService;
import com.dragoninfo.dfw.service.SysFuncService;
import com.dragoninfo.dfw.service.ValidateAndLogService;
import com.dragonsoft.cicada.datacenter.common.token.GlobalConstant;
import com.dragonsoft.cicada.datacenter.common.utils.UserContextUtil;
import com.dragonsoft.cicada.datacenter.modules.datavisual.dashboard.IDashboardAuthBuilder;
import com.dragonsoft.cicada.datacenter.modules.datavisual.dashboard.IDashboardBuilder;
import com.dragonsoft.cicada.datacenter.modules.datavisual.dashboard.IDashboardGroupBuilder;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.service.IUserService;
import com.google.common.collect.Maps;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.scheduling.annotation.SchedulingConfigurer;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;


/**
 * 仪表盘
 */
@CrossOrigin
@RestController
@RequestMapping("/dashboard")
@FuncScanAnnotation(code = "dashboard", name = "仪表盘", parentCode = "dataVisualAnalysis")
@PropertySource("classpath:case-config.properties")
@Slf4j
public class DashboardController {

    @Autowired
    IDashboardBuilder dashboardBuilder;
    @Autowired
    IDashBoardService dashBoardService;
    @Autowired
    IDashboardGroupBuilder dashboardGroupBuilder;
    @Autowired
    private SysFuncService sysFuncService;

//    @Autowired
//    private DashboardScheduled dashboardScheduled;
    @Autowired
    private SysAuthUserService sysAuthUserService;
    @Autowired
    private IUserService userService;
    @Resource
    private ValidateAndLogService validateAndLogService;

    @Autowired
    private IDashboardGroupService dashboardGroupService;

    private SchedulingConfigurer schedulingConfigurer;

    @Autowired
    private IDashboardAuthBuilder dashboardAuthBuilder;

    @Value("${standModel}")
    private boolean standModel;

    @Value("${sceModel}")
    private boolean sceModel;

    @Value("${accessThirdSystem}")
    private String accessThirdSystem;

    @GetMapping("/getOpenOn")
    public Result getOpenOn(HttpSession session){
        String userId = (String) session.getAttribute("userId");
        Map<String,Object> map = new HashMap<>();
        map.put("standModel",standModel);
        map.put("sceModel",sceModel);
        if ( GlobalConstant.UserProperties.DC_SUPER_ID.equals(userId)){
            map.put("isNormal",false);
        }else {
            map.put("isNormal",true);
        }
        return Result.toResult(R.ok(map));
    }

    @GetMapping("/authRegister")
    public Result authRegister(String visualId, HttpServletRequest request) {
        String userId = (String) request.getSession().getAttribute("userId");
        TSysFunc tSysFuncBase = new TSysFunc();
        tSysFuncBase.setFuncCode(visualId);
        tSysFuncBase.setFuncName(visualId);
        tSysFuncBase.setFuncType("1");
        tSysFuncBase.setEnableState("1");
        tSysFuncBase.setDescription(userId);
        sysFuncService.store(tSysFuncBase);
        return Result.success();
    }

    @PostMapping("/getListByGroupId")
    //@FuncScanAnnotation(code = "dashboardGetListByGroupId", name = "查看", parentCode = "dashboard")
    //@ValidateAndLogAnnotation
    public Result getDashbgetListPageByGroupId(@RequestBody Map<String,Object> map, HttpServletRequest request) {
        String userId = UserContextUtil.getUserIdByHttpRequest(request);
        String groupId = (String) map.get("groupId");
        String keyWord = (String) map.get("keyWord");
        String  operatorId=(String) map.get("operatorId");
        int index = (int) map.get("index");
        int pageSize = (int) map.get("pageSize");
        if(userId.equals(operatorId)|| CharSequenceUtil.isBlank(operatorId)){
            operatorId=userId;
        }
        if(operatorId.equals(groupId)){
            groupId="1";
        }
        PageInfo list = dashboardBuilder.builderListForPage(groupId, keyWord, index, pageSize, operatorId);
        return Result.toResult(R.ok(list));
    }

    @PostMapping("/getDashboardAllByGroupId")
    public Result getDashboardAllByGroupId(@RequestBody Map<String,Object> map, HttpServletRequest request) {
        String userId = (String) request.getSession().getAttribute("userId");
        String groupId = (String) map.get("groupId");

        List list = dashboardBuilder.getListAll(groupId, userId);
        Map<String,Object> resultData = new HashMap<>();
        resultData.put("dashboardData",list);
        resultData.put("dashboardGroupS",dashboardGroupService.queryGroupByParentId(groupId,userId));
        return Result.toResult(R.ok(resultData));
    }

    @GetMapping("/list")
    public Result getGroups() {
        Map<String, List<DashboardGroup>> map = new HashMap<>();
        List<DashboardGroup> myList = dashboardGroupBuilder.builderGroup("1");
        List<DashboardGroup> allList = dashboardGroupBuilder.builderGroupNoMy("1");
        map.put("myList", myList);
        map.put("allList", allList);
        return Result.toResult(R.ok(map));
    }

    @GetMapping("/initWidget")
    public Result initWidget() {
        String id = dashBoardService.saveWidget();
        return Result.success(id);
    }

    @GetMapping("/getDashboard")
    @FuncScanAnnotation(code = "dashboardGetDashboard", name = "高级", parentCode = "dashboard")
    @ValidateAndLogAnnotation
    public Result getDashboard(String id) {
        return this.getPreVieWData(id);
    }

    @RequestMapping("/getPreVieWDataByUserId")
    @ApiOperation(value = " 可视化预览带userId接口 参数为id(可视化id) userId")
    public Result getPreVieWDataByUserId(HttpServletRequest request, String id, String userId,Boolean state) {

       /* if (null == request.getSession().getAttribute("userId")) {
            try{
                Map<String, String> params = Maps.newHashMap();
                params.put("id", userId);
                TSysAuthUser tSysAuthUser = (TSysAuthUser) sysAuthUserService.query(params);
                HttpSession session = request.getSession();
                session.setMaxInactiveInterval(24 * 60 * 60);
                session.setAttribute("userId", tSysAuthUser.getId());
                List<TSysAuthRole> tSysAuthRoleList = validateAndLogService.getUserAuth(tSysAuthUser);
                if (null != tSysAuthRoleList && tSysAuthRoleList.size() > 0) {
                    session.setAttribute("roles", tSysAuthRoleList);
                }
                session.setAttribute("tSysAuthUser", tSysAuthUser);
            }catch (NullPointerException e){
                throw new RuntimeException("该用户不存在！");
            }

        }*/


        return this.getPreVieWData(id,userId,state);
    }

    @RequestMapping("/getPreVieWDataByUserCodeAndPassWord")
    @ApiOperation(value = " 可视化预览带userCode与passWord 接口 参数为id(可视化id) userCode  passWord")
    public Result getPreVieWDataByUserCodeAndPassWord(HttpServletRequest request, String id, String userCode,String passWord) {
        Assert.notNull(id, "仪表盘id不能为空");
        Assert.notNull(userCode, "用户名不能为空");
        Assert.notNull(passWord, "密码不能为空");
        Map<String, String> params = Maps.newHashMap();
        params.put("objCode", userCode);
        TSysAuthUser tSysAuthUser = (TSysAuthUser) sysAuthUserService.query(params);
//        if (null == request.getSession().getAttribute("userId")) {
//            if (null == tSysAuthUser) {
//                ExceptionUtil.throwBusiException(BusiErrorCode.NOT_OR_WRONG);
//            }
        if (tSysAuthUser == null) {
            throw new BusiException(BusiErrorCode.NOT_OR_WRONG);
        }
        //加上dids的校验逻辑
        if (!userService.checkUserAndPassward(userCode,tSysAuthUser.getPassword(),passWord,accessThirdSystem)) {
            throw new BusiException(BusiErrorCode.NOT_OR_WRONG);
        }
        if ("1".equals(tSysAuthUser.getEnableState())) {
            throw new BusiException(BusiErrorCode.USER_FORBID);
        }
        try {
            Dashboard dashboard = dashboardBuilder.builder(id);
            Assert.notNull(dashboard, "仪表盘不存在");
            if (!tSysAuthUser.getId().equals(dashboard.getOperateUserId())) {
                if (!dashboardAuthBuilder.hasDashboardAuth(id, tSysAuthUser.getId())) { //非本人仪表盘 判断是否是分享数据
                    throw new BusiException(BusiErrorCode.UNAUTHORIZED);
                }
            }
            return Result.toResult(R.ok(dashboard));
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return Result.toResult(R.error(e.getMessage()));
        }

//            HttpSession session = request.getSession();
//            session.setMaxInactiveInterval(24*60*60);
//            session.setAttribute("userId", tSysAuthUser.getId());
//            List<TSysAuthRole> tSysAuthRoleList = validateAndLogService.getUserAuth(tSysAuthUser);
//            if (null != tSysAuthRoleList && tSysAuthRoleList.size() > 0) {
//                session.setAttribute("roles", tSysAuthRoleList);
//            }
//            session.setAttribute("tSysAuthUser", tSysAuthUser);
       // }

       // return this.getPreVieWData(id);
    }

    private Result getPreVieWData(String id,String userId,Boolean state) {
        try {
            Dashboard dashboard = dashboardBuilder.builder(id);
            if(Boolean.TRUE.equals(state)){
                Map map = userService.isTianJinFenJuUser(userId);
                dashboard.getFilterPolice().put("code",map.get("code"));
                dashboard.getFilterPolice().put("id",map.get("id"));
                if(map.containsKey("pcsCode")&&map.containsKey("pcsId")){
                    dashboard.getFilterPolice().put("pcsCode",map.get("pcsCode"));
                    dashboard.getFilterPolice().put("pcsId",map.get("pcsId"));
                }
            }
            return Result.toResult(R.ok(dashboard));
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return Result.toResult(R.error(e.getMessage()));
        }
    }

    private Result getPreVieWData(String id) {
        try {
            Dashboard dashboard = dashboardBuilder.builder(id);
            return Result.toResult(R.ok(dashboard));
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return Result.toResult(R.error(e.getMessage()));
        }
    }

    @GetMapping("/getLinkageDashboard")
    public Result getLinkageDashboard(String id) {

        return Result.success(dashboardBuilder.getLinkageDashboard(id));

    }


    private static volatile Lock lock = new ReentrantLock();

    @GetMapping("/getConcurrentUserId")
    public Result getConcurrentEditUserId(String id, HttpServletRequest request) {
        String userId = (String) request.getSession().getAttribute("userId");
        lock.lock();
        try {
            Dashboard dashboard = dashBoardService.getDashboard(id);
            if (dashboard.getEditUserId() == null) {
                dashboard.setEditUserId(userId);
                dashBoardService.updateDashboard(dashboard);
                return Result.success(true);
            } else if (!userId.equals(dashboard.getEditUserId())) {
                Map<String, String> params = Maps.newHashMap();
                params.put("id", dashboard.getEditUserId());
                TSysAuthUser tSysAuthUser = (TSysAuthUser) sysAuthUserService.query(params);
                return Result.success(false);
            } else {
                return Result.success(true);
            }
        } finally {
            lock.unlock();
        }
    }


    @GetMapping("/deleteConcurrentUserI·d")
    public Result deleteConcurrentUserId(String id, HttpServletRequest request) {
        String userId = (String) request.getSession().getAttribute("userId");
        Dashboard dashboard = dashBoardService.getDashboard(id);
        dashboard.setEditUserId(null);
        dashBoardService.updateDashboard(dashboard);
        return Result.success();
    }


    @PostMapping("/saveOrUpdate")
    @FuncScanAnnotation(code = "dashboardSaveOrUpdate", name = "新建仪表盘", parentCode = "dashboard")
    @ValidateAndLogAnnotation
    public Result saveOrUpdate(@RequestBody Map map, HttpServletRequest request) {
        String userId = (String) request.getSession().getAttribute("userId");
        String data = (String) map.get("data");
        Dashboard dashboard = JSONObject.parseObject(data, Dashboard.class);
        boolean checkname = dashBoardService.checkName(dashboard.getId(), dashboard.getGroupId(), dashboard.getName(), userId);
        if (checkname) {
            return Result.toResult(R.error("名称已存在"));
        }
        dashboard.setOperateUserId(userId);
        String id = dashboardBuilder.saveOrUpdate(dashboard);
        return Result.toResult(R.ok(id));
    }

    @PostMapping("/updateGroup")
    @FuncScanAnnotation(code = "dashboardUpdateGroup", name = "移动", parentCode = "dashboard")
    @ValidateAndLogAnnotation
    public Result updateGroup(@RequestBody Map map) {
        String id = (String) map.get("id");
        String groupId = (String) map.get("groupId");
        List<String> nameList = dashBoardService.updateGroup(id, groupId);
        if (nameList.size() > 0) {
            return Result.toResult(R.error("名称[" + nameList.get(1) + "]已在[" + nameList.get(0) + "]存在"));
        }
        return Result.toResult(R.ok());
    }

    @PostMapping("/delete")
    @FuncScanAnnotation(code = "dashboardDelete", name = "删除", parentCode = "dashboard")
    @ValidateAndLogAnnotation
    public Result delete(@RequestBody Map map) {
        String id = (String) map.get("id");
        dashboardBuilder.delete(id);
        return Result.toResult(R.ok());
    }

    @PostMapping("/copy")
    @FuncScanAnnotation(code = "dashboardCopy", name = "复制", parentCode = "dashboard")
    @ValidateAndLogAnnotation
    public Result copy(@RequestBody Map map, HttpServletRequest request) {
        String userId = (String) request.getSession().getAttribute("userId");
        String id = (String) map.get("id");
        String groupId = (String) map.get("groupId");
        String dashboardName = (String) map.get("dashboardName");
        dashboardBuilder.copy(id,groupId,dashboardName,userId);
        return Result.toResult(R.ok());
    }


    @PostMapping("/dashboardAdvanced")
    @ResponseBody
    public Result dashboardAdvanced(@RequestBody Map map) {
        Dashboard dashboard = new Dashboard();
        //更新仪表板高级配置
        dashboard.setId(map.get("id").toString());
        dashboard.setIsCache(Boolean.valueOf(String.valueOf(map.get("isCache"))));
        dashboard.setCron(map.get("cron").toString());
        //配置定时器
        dashBoardService.updateAdvancedCfg(dashboard);
        return Result.toResult(R.ok());
    }

    @PostMapping("/getDashboardAllByUserId")
    @ApiOperation(value = "批量分享获取仪表盘下拉列表")
    public Result getDashboardAllByUserId(@RequestBody Map<String, Object> queryMap, HttpServletRequest request) {
        String userId = (String) request.getSession().getAttribute("userId");
        queryMap.put("userId", userId);
        queryMap.put("standModel", standModel);
        queryMap.put("sceModel", sceModel);

        Result result = dashboardBuilder.getDashboardAllByUserId(queryMap);

        return result;
    }
}
