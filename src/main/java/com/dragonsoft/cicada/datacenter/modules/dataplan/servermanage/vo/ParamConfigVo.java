package com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.vo;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class ParamConfigVo extends CommonParamConfigVo{

    /**
     * 保存方式
     * 0 保存并测试 保存并发布
     * 1 保存并退出 保存不发布
     */
    private String saveType;


    /**
     * //0:单表，1：多表
     */
    private String singleOrManyTable;

    /**
     * 服务绑定的资源id，即表id
     */
    private List<String> resourceIds;
    /**
     * 多资源合并
     */
    private List<ManyJoinMeta> manyJoinMetas=new ArrayList<>();

    private String servicePublicationId;

    private String dirAllName;

    /**
     * 是否启用批量查询 0开启 1 不开启
     */
    private String batchQuery;

    /**
     * 允许最大记录数
     */
    private Integer maxQueryNum;

    /**
     * 结果过滤
     */
    private List<ConditionFilterMeta> encasulationJudgContion=new ArrayList<>();

    @Data
    public static class ConditionFilterMeta{

        private String judgCondition;
        /**
         * 内部的关联条件
         */
        private String relationCondition;
        /**
         * 外部大的与下一个之间的关联条件
         */
        private String outRelationContion;

        private int no;

    }

    @Data
    public static class ManyJoinMeta{
        /**
         * 序号
         */
        private String index;
        /**
         * 左表id
         */
        private String leftStepId;

        /**
         * 左表表英文名
         */
        private String leftStepCode;

        /**
         * 右表表id
         */
        private String rightStepId;

        /**
         * 右表表英文名
         */
        private String rightStepCode;

        /**
         * 左表表中文名
         */
        private String leftStepName;

        /**
         * 右表表中文名
         */
        private String rightStepName;


        /**
         * join操作类型：left join 等
         */
        private String op;

        /**
         * join on 是 AND 还是 OR
         */
        private String joinOnOperate;

        /**
         * join on 的字段
         */
        private List<JoinOnColumn> joinOnColumns;
    }

    @Data
    public static class JoinOnColumn{
        /**
         * 左表链接字段
         */
        private String leftColumnCode;
        /**
         * 左表的id
         */
        private String leftTableId;

        /**
         * 右表链接字段字段
         */
        private String rightColumnCode;
        /**
         * 右表id
         */
        private String rightTableId;
    }

    @Data
    public static class Param {
        private String paramId;
        private String paramCode;
        //信息核查 这个值存存在的参数值
        private String paramName;
        //信息核查 这个值存不存在的参数值
        private String paramValue;
        private String type;
        private String mappingMode;
        private String isMust;
        private String defaultValue;
        private String memo;
        private String example;
        /**
         * 0代表否 1代表是
         */
        private String likeQuery;
        private String paramLocation = "body";
        private List<Param> children; //子参数

        private String desensitization;//脱敏类型

        private String datasetId;// 数据集id

        //返回参数排序方式
        private String orderType;
    }

    /**
     *  单表多表 表字段与请求参数绑定
     */
    private List<TableColumnMapping> tableColumnMappings=new ArrayList();

    /**
     * 单表多表 表字段与返回字段映射
     */
    private List<TableColumnMapping> returnParamMappings=new ArrayList<>();

    /**
     * 每个表的排序字段
     */
    private List<OrderFieldTable> orderFieldTables=new ArrayList<>();

    @Data
    public static class TableColumnMapping {
        //表id
        private String datasetId;
        //表名
        private String datasetName;
        //表中文名
        private String datasetZhName;
        //字段 与参数 映射
        private List<ColumnMapping> columns;
    }

    @Data
    public static class ColumnMapping {
        //字段中文名
        private String filedName;
        //字段英文名
        private String fieldCode;
        //字段别名中文
        private String fieldAsName;
        //字段别名英文
        private String fieldAsCode;
        //字段类型
        private String fieldType;

    }

    @Data
    public static class OrderFieldTable{
        //表id
        private String datasetId;
        //表名
        private String datasetName;
        //表中文名
        private String datasetZhName;
        //排序字段
        private List<OrderField> orderFields;
    }

    @Data
    public static class OrderField{
        //字段中文名
        private String filedName;
        //字段英文名
        private String fieldCode;
        //字段类型
        private String fieldType;
        //排序类型 asc 升序  desc降序
        private String orderType;
        //排序序号
        private Integer orderNum;

    }

}
