package com.dragonsoft.cicada.datacenter.modules.variable.vo;

import com.code.metadata.variable.TransVariable;
import lombok.Data;

import java.util.List;

/**
 * 版权所有：厦门市巨龙信息科技有限公司
 * Copyright 2018 Xiamen Dragon Information Technology Co. Ltd.
 * All right reserved.
 * ====================================================
 * 修订记录：
 * No    日期				作者(操作:具体内容)
 * 1.    2021/7/19 15:35			baijx（<EMAIL>）(创建:创建文件)
 * ====================================================
 * 类描述：
 */
@Data
public class TransVariableVo {
    private List<TransVariable> transVariables;

    private String transId;

    private List<String> deleteTransVariable;

    private int relationType;//默认0,1表示关联的是logicObjData;

}
