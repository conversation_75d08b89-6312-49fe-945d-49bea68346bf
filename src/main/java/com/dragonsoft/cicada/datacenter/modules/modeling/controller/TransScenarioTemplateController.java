package com.dragonsoft.cicada.datacenter.modules.modeling.controller;

import com.alibaba.fastjson.JSON;
import com.code.common.utils.StringUtils;
import com.code.common.utils.io.SerializableMsgCodec;
import com.code.metadata.aimodel.ScriptInfo;
import com.code.metadata.business.directory.BaseBusiClassify;
import com.code.metadata.datavisual.Dashboard;
import com.code.metadata.etl.trans.TransMeta;
import com.code.metadata.scenario.TScenarioCaseType;
import com.code.metaservice.aimodel.IScriptInfoService;
import com.code.metaservice.scenario.ScenarioCaseTypeService;
import com.dragoninfo.dfw.bean.Result;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.service.IDataSetEditService;
import com.dragonsoft.cicada.datacenter.modules.modeling.service.ITransScenarioTemplateService;
import com.dragonsoft.cicada.datacenter.modules.modeling.service.TransTemplateService;
import com.fw.dao.hbmimpl.BaseDaoImpl;
import com.fw.service.BaseService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StreamUtils;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.Serializable;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 版权所有：厦门市巨龙信息科技有限公司
 * Copyright 2018 Xiamen Dragon Information Technology Co. Ltd.
 * All right reserved.
 * ====================================================
 * 修订记录：
 * No    日期				作者(操作:具体内容)
 * 1.    2021/9/15 11:10			baijx（<EMAIL>）(创建:创建文件)
 * ====================================================
 * 类描述：
 */
@CrossOrigin
@RestController
@RequestMapping("/transScenarioTemplate")
@Slf4j
public class TransScenarioTemplateController {


    @Autowired
    IDataSetEditService editService;


    @Autowired
    private ScenarioCaseTypeService scenarioCaseTypeService;

    @Autowired
    private ITransScenarioTemplateService scenarioTemplateService;

    @Autowired
    private IScriptInfoService scriptInfoService;

    @Autowired
    private BaseDaoImpl baseDao;

    @Autowired
    private BaseService baseService;

    @Autowired
    private TransTemplateService transTemplateService;


    private List<Map<String, String>> scenarioMaps = Lists.newArrayList();
    private Map<String, String> scenarioTypeIds = new HashMap<>();

    /**
     * 场景案例导入
     * @param path
     * @return
     * @throws IOException
     */
    @RequestMapping("/importScenario")
    public Result buildScenarioData(String path) throws IOException {
        // 1. 反序列化 .model 模型文件
        File file = new File(path.trim());
        String modelName = file.getName().split("\\.")[0];
        byte[] bytes = FileUtils.readFileToByteArray(file);
        TransTemplateController.ExportBean exportBean = null;
        ExportAiBean exportAiBean;
        List<TScenarioCaseType> tScenarioCaseTypes = scenarioCaseTypeService.queryAllType();
        scenarioTypeIds = new HashMap<>();
        tScenarioCaseTypes.stream().forEach(s -> scenarioTypeIds.put(s.getCode(), s.getId()));

        try{
            exportBean  = (TransTemplateController.ExportBean) SerializableMsgCodec.decode(bytes);
        }catch (Exception e) {
            exportAiBean = (ExportAiBean)SerializableMsgCodec.decode(bytes);
            buildScenarioData(exportAiBean);
            return  Result.success();
        }


        List<TransMeta> transMetas = exportBean.getTransMetas();
        List<Dashboard> dashboards = exportBean.getDashboards();
        List<TransTemplateController.PortalInfo> portalInfos = exportBean.getPortalInfos();
        //模型方案
        if (CollectionUtils.isNotEmpty(transMetas)) {
            List<String> transMetasNames = Lists.newArrayList();
            transMetas.stream().forEach(s -> transMetasNames.add("'" + s.getName() + "'"));
            List<Map<String, String>> maps = scenarioTemplateService.queryTransByName(transMetasNames);
            buildScenarioInfo(maps);

        }
        //可视化
        if (CollectionUtils.isNotEmpty(dashboards)) {
            List<String> dashboardsNames = Lists.newArrayList();
            dashboards.stream().forEach(s -> dashboardsNames.add("'" + s.getName() + "'"));
            List<Map<String, String>> maps = scenarioTemplateService.queryDashboardByName(dashboardsNames);
            buildScenarioInfo(maps);

        }
        //门户
        if (CollectionUtils.isNotEmpty(portalInfos)) {
            List<String> portalInfosNames = Lists.newArrayList();
            portalInfos.stream().forEach(s -> portalInfosNames.add("'" + s.getPortal().getName() + "'"));
            List<Map<String, String>> maps = scenarioTemplateService.queryPortalByName(portalInfosNames);
            buildScenarioInfo(maps);
        }
        scenarioTemplateService.saveScenarioType(scenarioMaps);
        return Result.success();
    }

    private void buildScenarioData(ExportAiBean exportAiBean){
        List<ScriptInfo> scriptInfoList = exportAiBean.getScriptInfos();
        if (CollectionUtils.isNotEmpty(scriptInfoList)) {
            List<String> dashboardsNames = Lists.newArrayList();
            scriptInfoList.stream().forEach(s -> dashboardsNames.add("'" + s.getName() + "'"));
            List<Map<String, String>> maps = scenarioTemplateService.queryScriptInfoByName(dashboardsNames);
            buildScenarioInfo(maps);
            scenarioTemplateService.saveScenarioType(scenarioMaps);
        }
    }

    private void buildScenarioInfo(List<Map<String, String>> maps) {
        scenarioMaps = Lists.newArrayList();
        for (Map.Entry<String, String> scenarioTypeId : scenarioTypeIds.entrySet()) {
            //方案
            for (Map<String, String> map : maps) {
                Map<String, String> s = new HashMap<>();
                if (map.get("name").contains(scenarioTypeId.getKey())) {
                    s.put("modelId", map.get("id"));
                    s.put("modelName", map.get("name"));
                    s.put("modelCode", map.get("code"));
                    s.put("caseId", scenarioTypeId.getValue());
                    s.put("caseTypeCode", scenarioTypeId.getKey());
                    scenarioMaps.add(s);
                }
            }
        }

    }


    @Data
    public static class ExportAiBean implements Serializable {
        public List<ScriptInfo> scriptInfos;
        public TransTemplateController.TransScheduleInfo aiTransScheduleInfo;
    }


    /**
     * ai建模导出
     * @param response
     * @param modelName
     * @param aiScriptIds
     * @return
     * @throws IOException
     */
    @RequestMapping("/aiModelExport")
    public Result aiModelExport(HttpServletResponse response, String modelName, String aiScriptIds) throws IOException {
        ExportAiBean exportAiBean = new ExportAiBean();
        TransTemplateController transTemplateController = new TransTemplateController(baseDao, null, null);
        if (StringUtils.isNotBlank(aiScriptIds)) {
            List<ScriptInfo> scriptInfoList = Lists.newArrayList();
            List<String> transTasks = com.google.common.collect.Lists.newArrayList();
            List<String> transSchedules = com.google.common.collect.Lists.newArrayList();
            List<String> transSubTransRelations = com.google.common.collect.Lists.newArrayList();
            for (String scriptId : aiScriptIds.split(",")) {
                ScriptInfo scriptInfo = scriptInfoService.get(ScriptInfo.class, scriptId);
                scriptInfoList.add(scriptInfo);
                transTemplateController.buildScheduleInfos(scriptId, transTasks, transSchedules, transSubTransRelations);

            }
            exportAiBean.setScriptInfos(scriptInfoList);
            TransTemplateController.TransScheduleInfo scheduleInfo = new TransTemplateController.TransScheduleInfo();
            scheduleInfo.setTransSchedules(transSchedules);
            scheduleInfo.setTransSubTransRelations(transSubTransRelations);
            scheduleInfo.setTransTasks(transTasks);
            exportAiBean.setAiTransScheduleInfo(scheduleInfo);
        }
        try {
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(modelName + ".model", "UTF-8"));
            StreamUtils.copy(SerializableMsgCodec.encode(exportAiBean), response.getOutputStream());
            return Result.success();
        } finally {
            response.getOutputStream().close();
        }
    }

    /**
     * AI建模导入
     * @param path
     * @param classifyId
     * @return
     * @throws IOException
     */
    @RequestMapping("/aiModelImport")
    public Result aiModelImport(String path, String classifyId) throws IOException {
        // 1. 反序列化 .model 模型文件
        File file = new File(path.trim());
        String modelName = file.getName().split("\\.")[0];
        System.out.println(modelName);
        String aiScriptSuffix = "AI建模方案";
        byte[] bytes = FileUtils.readFileToByteArray(file);
        ExportAiBean exportAiBean = (ExportAiBean) SerializableMsgCodec.decode(bytes);
        //ai建模方案
        List<ScriptInfo> scriptInfos = exportAiBean.getScriptInfos();
        //ai建模调度方案
        TransTemplateController.TransScheduleInfo aiTransScheduleInfo = exportAiBean.getAiTransScheduleInfo();
        //ai建模保存
        if (CollectionUtils.isNotEmpty(scriptInfos)) {
            TransTemplateController transTemplateController = new TransTemplateController(null, baseService, transTemplateService);
            BaseBusiClassify baseBusiClassify = transTemplateController.addBusiClassify(classifyId, modelName, aiScriptSuffix, "DATA_SET_DIR_STANDARD");
            Map<String, String> transMapping = new HashMap<>();
            for (ScriptInfo scriptInfo : scriptInfos) {
                String oldScriptId = scriptInfo.getId();
                scriptInfo.setId(null);
                scriptInfo.setScriptLogs(null);
                scriptInfoService.saveOrUpdate(scriptInfo);
                String newScriptId = scriptInfo.getId();
                String elementSql = "INSERT INTO t_md_classify_element (element_id, busi_classify_id) VALUES ('" + scriptInfo.getId() + "','" + baseBusiClassify.getId() + "');\n";
                baseDao.executeSqlUpdate(elementSql);
                transMapping.put(oldScriptId, newScriptId);
            }

            if (aiTransScheduleInfo != null) {
                List<String> executeList = new ArrayList<>();
                Map<String, String> taskMapping = new HashMap<>();
                for (String json : aiTransScheduleInfo.getTransTasks()) {
                    Map map = JSON.parseObject(json, Map.class);
                    String uuid = StringUtils.uuid();
                    taskMapping.put((String) map.get("id"), uuid);
                    map.put("id", uuid);
                    map.put("trans_id", transMapping.get(map.get("trans_id")));
                    map.put("run_param", null);
                    executeList.add(transTemplateController.insertSQL("t_trans_task", map));
                }
                for (String json : aiTransScheduleInfo.getTransSchedules()) {
                    Map map = JSON.parseObject(json, Map.class);
                    map.put("id", StringUtils.uuid());
                    map.put("trans_id", transMapping.get(map.get("trans_id")));
                    map.put("task_id", taskMapping.get(map.get("task_id")));
                    executeList.add(transTemplateController.insertSQL("t_trans_schedule", map));
                }
                for (String json : aiTransScheduleInfo.getTransSubTransRelations()) {
                    Map map = JSON.parseObject(json, Map.class);
                    map.put("id", StringUtils.uuid());
                    map.put("trans_id", transMapping.get(map.get("trans_id")));
                    map.put("subtrans_id", transMapping.get(map.get("trans_id")));
                    executeList.add(transTemplateController.insertSQL("t_trans_subtrans_relation", map));
                }
                String taskSQL = String.join(" ", executeList);
                if (StringUtils.isNotBlank(taskSQL)) transTemplateService.saveSQL(taskSQL);
            }
        }
        return Result.success();
    }


    enum ScenarioType {
        trans_type_code("trans", "模型"),
        trans_collision_type_code("collision", "模型碰撞"),
        trans_count_type_code("count", "模型统计"),
        visual_type_code("visual", "可视化"),
        portal_type_code("portal", "门户"),
        ai_type_code("ai", "ai建模");

        private String code;
        private String name;

        ScenarioType(String code, String name) {
            this.name = name;
            this.code = code;
        }

        public static ScenarioType getInstanceByCode(String code) {
            for (ScenarioType value : ScenarioType.values()) {
                if (value.code.equals(code)) {
                    return value;
                }
            }
            return null;
        }
    }

}


