package com.dragonsoft.cicada.datacenter.modules.system.busmanagement.controller;

import com.code.common.utils.R;
import com.code.metadata.bus.management.enums.EnumBusVersion;
import com.dragoninfo.dfw.bean.Result;
import com.dragonsoft.cicada.datacenter.modules.system.busmanagement.service.IBusInfoManagementService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @date 2021/03/15
 */

@RestController
@CrossOrigin
@Slf4j
@RequestMapping("/busInfoManagement")
public class BusInfoManagementController {

    @Autowired
    private IBusInfoManagementService busInfoManagementService;


    /**
     * 查询总线信息
     * @param request
     * @return
     */
    @ResponseBody
    @RequestMapping("/getBusInfo")
    public Result getBusInfo(HttpServletRequest request) {
        try{
            String userId = (String) request.getSession().getAttribute("userId");
            return Result.success(busInfoManagementService.queryBusInfo(userId));
        }catch (Exception e){
            log.error(e.getMessage(),e);
            return Result.error("400",e.getMessage());
        }
    }



    /**
     * 总线版本类型
     */
    @ResponseBody
    @GetMapping("getBusVersion")
    public Result getBusVersion() {
        try {
            return Result.toResult(R.ok(EnumBusVersion.getEnumTypeList()));
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return Result.toResult(R.error(e.getMessage()));
        }
    }


}
