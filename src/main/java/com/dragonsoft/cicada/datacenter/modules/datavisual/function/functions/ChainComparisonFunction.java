package com.dragonsoft.cicada.datacenter.modules.datavisual.function.functions;

import com.code.dragonsoft.dataquery.service.querymodel.ColumnDataModel;
import com.dragonsoft.cicada.datacenter.modules.datavisual.function.ChartConfig;
import com.dragonsoft.cicada.datacenter.modules.datavisual.function.ColumnMeta;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Assert;

import java.util.List;
import java.util.concurrent.ThreadLocalRandom;

/**
 * <AUTHOR>
 * @date 2021/1/4
 */
public class ChainComparisonFunction extends AbstractFunction<ChainComparisonFunction.ChainComparisonMeta> {

    private final Logger logger = LoggerFactory.getLogger(ChainComparisonFunction.class);

    @Override
    protected ChainComparisonMeta buildMeta(ChartConfig config,ColumnDataModel dates) {
        List<ColumnMeta> dimensions = config.getDimensions();
        Assert.notEmpty(dimensions, "chart dimension cannot be empty!");
        /*List<ColumnMeta> timeColumns = dimensions.stream()
                .filter(dimension -> timeType.contains(dates.getFieldName().get(dimension.getCode())))
                .collect(Collectors.toList());
        Assert.notEmpty(timeColumns, "time field cannot be empty!");*/
        List<ColumnMeta> timeColumns = dimensions;
        if (timeColumns.size() > 1 && logger.isWarnEnabled()) {
            logger.warn("There is more than one time field, actual: {}", timeColumns.size());
        }
        ColumnMeta timeColumn = timeColumns.get(ThreadLocalRandom.current().nextInt(timeColumns.size()));
        for (ColumnMeta dimension : dimensions) {
            if(dimension.isQueryColumnl()){
                timeColumn = dimension;
            }
        }
        if (logger.isDebugEnabled()) {
            logger.debug("get random time field: {}", timeColumn.getCode());
        }
        List<ColumnMeta> metrics = config.getMetrics();
        Assert.notEmpty(metrics, "chart metric cannot be empty!");
        ColumnMeta metricColumn = metrics.get(0);
        return new ChainComparisonFunction.ChainComparisonMeta(timeColumn.getCode(),
                metricColumn.getCode(),
                timeColumn.getTimeFormat(),
                timeColumn.getDateGranularity());
    }

    @Override
    protected ColumnDataModel doCalculate(ColumnDataModel dates, ChainComparisonMeta chainComparisonMeta) {

        String metricColumn = chainComparisonMeta.metricColumn;

        if (2 <= dates.getFieldValue().size()) {
            Double previousValue = 0.0;
            for (int i = 0; i < dates.getFieldValue().size(); i++) {

                if (0 == i) {
                    previousValue = Double.valueOf(dates.getFieldValue().get(i).get(metricColumn).toString());
                    dates.getFieldValue().get(i).put(metricColumn, 0.0);
                }

                if (0 != i) {
                    Double currentValue = Double.valueOf(dates.getFieldValue().get(i).get(metricColumn).toString());
                    dates.getFieldValue().get(i).put(metricColumn, currentValue - previousValue);
                    previousValue = currentValue;
                }
            }
            return dates;
        }

        return dates;
    }

    @Override
    public String getName() {
        return "求环比";
    }

    @Override
    public String getCode() {
        return "chain-comparison";
    }

    protected static class ChainComparisonMeta {
        private String timeColumn;
        private String metricColumn;
        private String timeFormat;
        private String dateGranularity;

        public ChainComparisonMeta(String timeColumn, String metricColumn, String timeFormat, String dateGranularity) {
            this.timeColumn = timeColumn;
            this.metricColumn = metricColumn;
            this.timeFormat = timeFormat;
            this.dateGranularity = dateGranularity;
        }
    }
}
