package com.dragonsoft.cicada.datacenter.modules.datavisual.dataset.Impl;

import com.code.meta.dml.IDataSourceBuilder;
import com.code.metadata.datavisual.Field;
import com.code.metaservice.res.common.ClassifierStatService;
import com.dragonsoft.cicada.datacenter.modules.datavisual.dataset.IFiledBuilder;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

public class FiledBuilder implements IFiledBuilder {

    @Autowired
    protected IDataSourceBuilder dataSourceBuilder;

    @Autowired
    ClassifierStatService classifierStatService;


    @Override
    public List<Field> BuilderField(String sourceId, String setId) {
        return null;
    }
}
