/*==============================================================*/
/* Table: T_PUBLISHED_SERVICE_LOG                               */
/*==============================================================*/
CREATE TABLE T_PUBLISHED_SERVICE_LOG (
                                         NUM_ID               SERIAL               NOT NULL,
                                         REG_ID               VARCHAR(12)          NULL,
                                         INTERFACE_NAME       TEXT                 NULL,
                                         REQUESTER            TEXT                 NULL,
                                         USER_ID              VARCHAR(18)          NULL,
                                         ORGANIZATION         TEXT                 NULL,
                                         ORGANIZATION_ID      VARCHAR(12)          NULL,
                                         USER_NAME            TEXT                 NULL,
                                         INTERFACE_TIME       VARCHAR(14)          NULL,
                                         TERMINAL_ID          TEXT                 NULL,
                                         INTERFACE_RESULT     VARCHAR(1)           NULL,
                                         ERROR_CODE           VARCHAR(30)          NULL,
                                         INTERFACE_CONDITION  TEXT                 NULL,
                                         RESULT_TIME          VARCHAR(14)          NULL,
                                         RESULT_COUNT         VARCHAR(12)          NULL,
                                         CONSTRAINT PK_T_PUBLISHED_SERVICE_LOG PRIMARY KEY (NUM_ID)
);

COMMENT ON COLUMN T_PUBLISHED_SERVICE_LOG.NUM_ID IS
'用于唯一标识应用系统/资源库产生的日志数据中的一条记录，在日志记录产生时生成，其格式和产生方式由应用系统/资源库自行决定 ';

COMMENT ON COLUMN T_PUBLISHED_SERVICE_LOG.REG_ID IS
'参见附录B：应用系统/资源库标识';

COMMENT ON COLUMN T_PUBLISHED_SERVICE_LOG.INTERFACE_NAME IS
'被调用的接口的具体名称';

COMMENT ON COLUMN T_PUBLISHED_SERVICE_LOG.REQUESTER IS
'请求方的应用系统或客户端名称';

COMMENT ON COLUMN T_PUBLISHED_SERVICE_LOG.USER_ID IS
'采用数字证书进行用户身份验证的应用系统，填写用户公民身份号码。未采用数字证书进行用户身份验证的应用系统/资源库，则填写警号';

COMMENT ON COLUMN T_PUBLISHED_SERVICE_LOG.ORGANIZATION IS
'用户所属单位的公安机关机构名称，采用GA/T 543 DE00065 单位名称';

COMMENT ON COLUMN T_PUBLISHED_SERVICE_LOG.ORGANIZATION_ID IS
'用户所属单位的公安机关机构代码，采用GA/T 543 DE00060 公安机关机构代码';

COMMENT ON COLUMN T_PUBLISHED_SERVICE_LOG.USER_NAME IS
'采用GA/T 543 DE00002 姓名';

COMMENT ON COLUMN T_PUBLISHED_SERVICE_LOG.INTERFACE_TIME IS
'用户操作时的系统日期时间，采用格式YYYYMMDDhhmmss，24小时制格式';

COMMENT ON COLUMN T_PUBLISHED_SERVICE_LOG.TERMINAL_ID IS
'用户操作时所使用的信息处理终端的唯一标识；对于直接接入公安网使用的信息处理终端，填写其网络IP地址，如：**********；移动警务终端通过网络安全边界接入设备接入公安网的，填写该终端的硬件序列号';

COMMENT ON COLUMN T_PUBLISHED_SERVICE_LOG.INTERFACE_RESULT IS
'用户操作的结果，包括成功/失败。1:成功；0：失败';

COMMENT ON COLUMN T_PUBLISHED_SERVICE_LOG.ERROR_CODE IS
'当操作结果为失败时，可记录操作失败的原因代码，采用附录A：用户操作失败原因代码';

COMMENT ON COLUMN T_PUBLISHED_SERVICE_LOG.INTERFACE_CONDITION IS
'操作类型为0-登录时，置空；为其它类型时，可记录用户进行操作时的数据筛选条件，填写数据操作SQL语句的where子句内容，如：name=‘张三’';


ALTER TABLE t_published_service_log alter COLUMN num_id type int8;
