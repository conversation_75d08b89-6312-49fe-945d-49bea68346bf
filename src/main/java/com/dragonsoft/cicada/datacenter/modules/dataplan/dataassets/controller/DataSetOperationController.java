package com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.text.CharSequenceUtil;
import com.code.common.paging.PageInfo;
import com.code.common.utils.R;
import com.code.common.utils.StringUtils;
import com.code.common.utils.assertion.Assert;
import com.code.dragonsoft.dataquery.service.QueryDataService;
import com.code.dragonsoft.dataquery.service.querymodel.ColumnDataModel;
import com.code.dragonsoft.dataquery.service.querymodel.ParamDataModel;
import com.code.dragonsoft.dataquery.util.ChangeColumnNameUtil;
import com.code.metadata.datavisual.DataSet;
import com.code.metadata.res.common.ClassifierStat;
import com.code.metadata.res.ddl.LogicDataObj;
import com.code.metaservice.core.ClassifierService;
import com.code.metaservice.datawarehouse.IDataWareTreeService;
import com.code.metaservice.datawarehouse.model.DatasetTreeModel;
import com.code.metaservice.ddl.ILogicDataObjService;
import com.code.metaservice.ddl.vo.LogicDataSetColumnVo;
import com.dragoninfo.dfw.annotation.FuncScanAnnotation;
import com.dragoninfo.dfw.annotation.ValidateAndLogAnnotation;
import com.dragoninfo.dfw.bean.Result;
import com.dragonsoft.cicada.datacenter.common.utils.UserContextUtil;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.service.IDataSetEditService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.service.IDataSetOperationService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.util.RemoveOuterParenthesesUtil;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.vo.LogicDataSet.ColumnDataSetVo;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.vo.LogicDataSet.SaveSelfHelpDataSetVo;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.vo.LogicDataSet.SaveTableColumnMappingVo;
import com.dragonsoft.cicada.datacenter.modules.dataplan.externaldatasources.service.IDataSetAuthService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.externaldatasources.vo.DataSetAuthVo;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.service.IRoleService;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.service.IUserService;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.UnexpectedRollbackException;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/7/8
 */
@Slf4j
@Controller
@CrossOrigin
@RequestMapping("/dataSetOperation")
@Api(value = "DataSetOperationController|数据集控制器")
@FuncScanAnnotation(code = "dataSetOperation", name = "数据准备", parentCode = "dataAssets")

public class DataSetOperationController {

    @Autowired
    private IDataSetOperationService dataSetOperationService;

    @Autowired
    private IUserService userService;

    @Autowired
    private IDataWareTreeService dataWareTreeService;

    @Autowired
    private IRoleService iRoleService;

    @Autowired
    private ClassifierService classifierService;

    @Autowired
    private QueryDataService queryDataService;

    @Autowired
    IDataSetEditService editService;

    @Autowired
    private IDataSetAuthService dataSetAuthService;

    @Autowired
    private ILogicDataObjService logicDataObjService;


    @ResponseBody
    @GetMapping("/queryDataSetTree")
    @ApiOperation(value = "获取数据集目录树")
    @ApiImplicitParam(name = "hasDataObj", value = "是否带数据集", required = true, dataType = "boolean")
    public Result queryDataSetTree(boolean hasDataObj,
                                   @RequestParam(value = "currentDataSetId", required = false, defaultValue = "") String currentDataSetId,
                                   HttpServletRequest request) {
        String userId = UserContextUtil.getUserIdByHttpRequest(request);
        List<DatasetTreeModel> datasetTreeModels = dataSetOperationService.queryDataSetTree(userId, hasDataObj, currentDataSetId);
        if (CollUtil.isEmpty(datasetTreeModels)) {
            datasetTreeModels = new ArrayList<>();
        }
        //系统管理员
        if (userService.isAdmin(userId) && !hasDataObj) {
            datasetTreeModels.get(0).getChildren().add(dataSetOperationService.queryDatasetTreeFromOtherUser(userId, false, currentDataSetId));
        }
        return Result.success(datasetTreeModels);
    }

    @ResponseBody
    @GetMapping("/queryDataSetTreeByDsType")
    @ApiOperation(value = "3.6.1按数据来源获取数据集目录树")
    @ApiImplicitParam(name = "hasDataObj", value = "是否带数据集", required = true, dataType = "boolean")
    public Result queryDataSetTreeBySourceType(boolean hasDataObj,
                                               @RequestParam(value = "currentDataSetId", required = false, defaultValue = "") String currentDataSetId,
                                               HttpServletRequest request) {
        String userId = UserContextUtil.getUserIdByHttpRequest(request);
        List<DatasetTreeModel> datasetTreeModels = dataSetOperationService.queryDataSetTreeBySourceType(userId, hasDataObj, currentDataSetId);
        if (CollUtil.isEmpty(datasetTreeModels)) {
            datasetTreeModels = new ArrayList<>();
        }
        //系统管理员
        if (userService.isAdmin(userId) && !hasDataObj) {
            datasetTreeModels.add(dataSetOperationService.queryDatasetTreeFromOtherUserBySourceType(userId, false, currentDataSetId));
        }
        return Result.success(datasetTreeModels);
    }


    @ResponseBody
    @GetMapping("/queryPublishDataSetTree")
    @ApiOperation(value = "获取生成API数据集目录树")
    @ApiImplicitParam(name = "hasDataObj", value = "是否带数据集", required = true, dataType = "boolean")
    public Result queryPublishDataSetTree(boolean hasDataObj,
                                          @RequestParam(value = "currentDataSetId", required = false, defaultValue = "") String currentDataSetId, HttpServletRequest request) {
        String userId = (String) request.getSession().getAttribute("userId");
        List<DatasetTreeModel> datasetTreeModels = dataSetOperationService.queryPublishDataSetTree(userId, hasDataObj, currentDataSetId);
        return Result.success(datasetTreeModels);
    }

    @ResponseBody
    @GetMapping("/addDataSetTreeNode")
    @ApiOperation(value = "添加数据集树节点")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "name", value = "节点名称", required = true, dataType = "String"),
            @ApiImplicitParam(name = "busiId", value = "父节点id", required = true, dataType = "String")
    })
    public Result addDataSetTreeNode(String name, String busiId, HttpServletRequest request) {
        String userId = (String) request.getSession().getAttribute("userId");

        if (dataSetOperationService.isRepeatName(name, userId)) {
//            return Result.error("400", "[" + name + "],已存在，请重新输入！");
            return Result.error("400", "该节点的子节点已存在相同节点中文名称或英文名称!");
        }

        DatasetTreeModel datasetTreeModel = dataSetOperationService.addDataSetTreeNode(name, busiId, userId);
        return Result.success(datasetTreeModel);
    }

    @ResponseBody
    @GetMapping("/deleteDataSetTreeNode")
    @ApiOperation(value = "删除数据集树节点")
    @ApiImplicitParam(name = "nodeId", value = "目录树节点id", required = true, dataType = "String")
    public Result deleteDataSetTreeNode(String nodeId, HttpServletRequest request) {
        String userId = (String) request.getSession().getAttribute("userId");
        String msg = dataSetOperationService.deleteDataSetTreeNode(nodeId, userId);
        return Result.success(msg);
    }


    @ResponseBody
    @GetMapping("/reNameDataSetTreeNode")
    @ApiOperation(value = "数据集树节点名称修改")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "oldName", value = "目录树旧的名称", required = true, dataType = "String"),
            @ApiImplicitParam(name = "name", value = "目录树新的名称", required = true, dataType = "String"),
            @ApiImplicitParam(name = "classifyId", value = "目录树节点Id", required = true, dataType = "String"),
    })
    public Result reNameDataSetTreeNode(String oldName, String name, String classifyId, HttpServletRequest request) {
        String userId = (String) request.getSession().getAttribute("userId");

        if (dataSetOperationService.isRepeatName(name, userId)) {
//            return Result.error("400", "[" + name + "]已存在，请重新输入！");
            return Result.error("400", "该节点的子节点已存在相同节点中文名称或英文名称!");
        }
        String msg = dataSetOperationService.updateNode(name, classifyId, userId, oldName);
        return Result.success(msg);
    }


    @ResponseBody
    @GetMapping("/getDataSetPage")
    @ApiOperation(value = "获取数据集分页")
//    @FuncScanAnnotation(code = "dataSetOperationGetDataSetPage", name = "创建仪表板", parentCode = "dataSetOperation")
//    @ValidateAndLogAnnotation
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "目录树节点Id", required = true, dataType = "String"),
            @ApiImplicitParam(name = "name", value = "查询名称", required = true, dataType = "String"),
            @ApiImplicitParam(name = "page", value = "当前页", required = true, dataType = "int"),
            @ApiImplicitParam(name = "pageSize", value = "页的大小", required = true, dataType = "int"),
            @ApiImplicitParam(name = "dbType", value = "数据库类型", required = true, dataType = "String"),
            @ApiImplicitParam(name = "isShare", value = "是否共享节点", required = true, dataType = "boolean"),
            @ApiImplicitParam(name = "operatorId", value = "当前节点数据创建人Id", required = true, dataType = "String")
    })
    public Result getDataSetPage(String id, String name, int page, int pageSize, String dbType, boolean isShare,
                                 String operatorId, HttpServletRequest request) {
        String userId = UserContextUtil.getUserIdByHttpRequest(request);

        PageInfo pageInfo;
        if (isShare) {
            // 数据来源类型
            pageInfo = dataSetOperationService.getDataSetSharePage(id, name, page, pageSize, userId, dbType, true);
        } else {
            if (userId.equals(operatorId) || CharSequenceUtil.isBlank(operatorId)) {
                //我的数据集或我的空间()
                pageInfo = dataSetOperationService.getDataSetPage(id, name, page, pageSize, userId, dbType, true);
            } else {
                //他人数据集或他人空间
                pageInfo = dataSetOperationService.getDataSetPageForOtherPerson(id, name, page, pageSize, operatorId, dbType, true);
            }
        }

        return Result.success(pageInfo);
    }

    @ResponseBody
    @GetMapping("/getDataSetForJurisdiction")
    @ApiOperation(value = "获取数据集分页")
    @FuncScanAnnotation(code = "dataSetOperationGetDataSetPage", name = "搜索数据集", parentCode = "dataSetOperation")
//    @ValidateAndLogAnnotation
    public Result getDataSetPage(String id, HttpServletRequest request) {
        String userId = (String) request.getSession().getAttribute("userId");
        PageInfo pageInfo = dataSetOperationService.getDataSetPage(id, "", 1, 10, userId, "", false);
        return Result.success(pageInfo);
    }


    @ResponseBody
    @GetMapping("/deleteDataSet")
    @ApiOperation(value = "删除数据集")
    @FuncScanAnnotation(code = "dataSetOperationDeleteDataSet", name = "删除", parentCode = "dataSetOperation")
    @ApiImplicitParam(name = "id", value = "数据id", required = true, dataType = "String")
    @ValidateAndLogAnnotation
    public Result deleteDataSet(String id) {
        dataSetOperationService.deleteDataSet(id);
        return Result.success();
    }

    @ResponseBody
    @GetMapping("/checkDataSetUserInVisual")
    @ApiOperation(value = "校验是否被可视化使用")
    @FuncScanAnnotation(code = "dataSetOperationDeleteDataSet", name = "删除", parentCode = "dataSetOperation")
    @ApiImplicitParam(name = "id", value = "数据集id", required = true, dataType = "String")
    public Result checkDataSetUserInVisual(String id) {

        return Result.success(dataSetOperationService.checkDataSetByVisual(id));
    }


    /*@ResponseBody
    @GetMapping("/queryDataSetById")
    @ApiOperation(value = "数据集详情")
    public Result queryDataSetById(String id) {
        DataSetOperationVo dataSetOperationVo = dataSetOperationService.queryDataSetById(id);
        return Result.success(dataSetOperationVo);
    }*/

    @ResponseBody
    @GetMapping("/getDataSetColumn")
    @ApiOperation(value = "获取数据集字段")
    @ApiImplicitParam(name = "id", value = "数据集id", required = true, dataType = "String")
    public Result getDataSetColumn(String id) {
        List logicDataColumns = dataSetOperationService.getDataSetColumn(id);
        return Result.success(logicDataColumns);
    }


    @ResponseBody
    @GetMapping("/getDataSetColumnList")
    @ApiOperation(value = "获取数据集字段2")
    @ApiImplicitParam(name = "id", value = "数据集id", required = true, dataType = "String")
    public Result getDataSetColumnList(String id) {
        List logicDataColumns = dataSetOperationService.getDataSetColumnList(id);
        return Result.success(logicDataColumns);
    }

    @ResponseBody
    @PostMapping("/accreditLogicDataObj")
    @ApiOperation(value = "授权数据集")
    @FuncScanAnnotation(code = "dataSetOperationAccreditLogicDataObj", name = "添加授权数据集", parentCode = "dataSetOperation")
    @ValidateAndLogAnnotation
    public Result accreditLogicDataObj(@ApiParam(value = "参数：List<String> dataObjIds 多个数据对象的id，String dataSetTreeNodeId 目录树id") @RequestBody Map dataMap,
                                       HttpServletRequest request
    ) {
        String userId = (String) request.getSession().getAttribute("userId");

        List<String> dataObjIds = (List<String>) dataMap.get("dataObjIds");
        String dataSetTreeNodeId = (String) dataMap.get("dataSetTreeNodeId");

        if (dataObjIds == null || dataObjIds.size() <= 0) {
            return Result.success("未选择数据对象！");
        }
        if (StringUtils.isBlank(dataSetTreeNodeId)) {
            return Result.error("400", "请选择数据集目录");
        }

        String msg = dataSetOperationService.accreditDataSet(dataObjIds, dataSetTreeNodeId, userId);
        return Result.success(msg);
    }


    @ResponseBody
    @GetMapping("/checkHasDataSet")
    @ApiOperation(value = "校验是否有数据集")
    public Result checkHasDataSet(String busiId) {
        boolean flag = dataSetOperationService.checkHasDataSet(busiId);
        return Result.success(flag);
    }


    @ResponseBody
    @GetMapping("/getAllDataSet")
    @ApiOperation(value = "校验是否有数据集")
    public Result getAllDataSet(HttpServletRequest request) {
        String userId = (String) request.getSession().getAttribute("userId");
        List<DataSet> dataSetOperationVos = dataSetOperationService.getAllDataSetByUserId("99959");
        return Result.success(dataSetOperationVos);
    }

    @ResponseBody
    @GetMapping("/moveLogicDataSet")
    @ApiOperation(value = "移动数据集")

    @FuncScanAnnotation(code = "dataSetOperationMoveLogicDataSet", name = "移动", parentCode = "dataSetOperation")
    public Result moveLogicDataSet(@ApiParam(value = "数据集id") String dataSetID,
                                   @ApiParam(value = "目录源树Id") String treeNodeId,
                                   @ApiParam(value = "目录目标树Id") String dTreeNodeId,
                                   @ApiParam(value = "数据来源树Id") String dsTreeNodeId,
                                   @ApiParam(value = "数据来源目标树Id") String dDsTreeNodeId) {

        if (CharSequenceUtil.isNotBlank(treeNodeId) && CharSequenceUtil.isNotBlank(dTreeNodeId)
                && !CharSequenceUtil.equals(treeNodeId, dTreeNodeId)) {
            dataSetOperationService.moveLogicDataSet(dataSetID, treeNodeId, dTreeNodeId);
        }
        if (CharSequenceUtil.isNotBlank(dsTreeNodeId)
                && CharSequenceUtil.isNotBlank(dDsTreeNodeId)
                && !CharSequenceUtil.equals(dDsTreeNodeId, dsTreeNodeId)) {
            dataSetOperationService.moveLogicDataSet(dataSetID, dsTreeNodeId, dDsTreeNodeId);
        }
        return Result.success();
    }

    @ResponseBody
    @PostMapping("/preview")
    @ApiOperation(value = "数据预览")
    @FuncScanAnnotation(code = "dataSetOperationPreviewData", name = "数据预览", parentCode = "dataSetOperation")
    public Result preview(@RequestBody Map resMap) {
        String tableId = resMap.get("tableId").toString();
        String limit = resMap.get("limit").toString();

        LogicDataObj obj = logicDataObjService.findLogicDataObjById(tableId);
        int size = 10;
        if (StringUtils.isNotBlank(limit)) {
            size = Integer.parseInt(limit);
        }
        ColumnDataModel rows = null;
        String logicSearchSQL = editService.getLogicSearchSQL(tableId);
        String sql = RemoveOuterParenthesesUtil.removeOuterParentheses(logicSearchSQL);

        ColumnDataModel res = null;
        ParamDataModel paramDataModel = new ParamDataModel();
        paramDataModel.setLimit(size);
        paramDataModel.setScript(sql);
        try {
            if (StringUtils.isNotBlank(tableId) && obj == null) {
                //走物理表
                res = queryDataService.queryData(tableId, paramDataModel);
            } else {
                if (!"QUICK_SQL".equalsIgnoreCase(obj.getBelongType())) {
                    ClassifierStat classifierStat = logicDataObjService.getClassifierStatByLogic(obj);
                    res = queryDataService.queryData(classifierStat.getId(), paramDataModel);
                } else {
                    LogicDataObj metaLogicDataObJ = logicDataObjService.getMetaLogicDataObJ(obj);
                    res = queryDataService.queryDataBySchema(metaLogicDataObJ.getOwnerId(), paramDataModel);
                }
            }
            rows = ChangeColumnNameUtil.changeName(tableId, res);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return Result.toResult(R.ok(rows));
    }

    @ResponseBody
    @GetMapping("/changeIsFast")
    @ApiOperation(value = "是否极速表")
    @FuncScanAnnotation(code = "dataSetOperationMoveLogicDataSet", name = "是否极速表", parentCode = "dataSetOperation")
    public Result changeIsFast(@ApiParam(value = "数据集id") String dataSetId, @ApiParam(value = "是否极速表") boolean ifFase) {
        dataSetOperationService.changeIsFast(dataSetId, ifFase);
        return Result.success();
    }

    @ResponseBody
    @GetMapping("/initOldPhysicalDatasetToLogic")
    @ApiOperation(value = "原始物理数据集迁移逻辑数据集")
    public Result initOldDatasetToLogic(HttpServletRequest request) {
        //不用经过权限拿到整颗树
        List<DatasetTreeModel> treeModelList = dataWareTreeService.getDataWarehouseTree("", true, true, "", null, "dataSpace");
        List<Map<String, List<String>>> treelist = dataSetOperationService.treelist(treeModelList);
        int count = 0;
        //根据数据集拿到userid
        for (Map<String, List<String>> map : treelist) {
            for (Map.Entry<String, List<String>> entry : map.entrySet()) {
                List<String> datasetIds = entry.getValue();
                for (String datasetId : datasetIds) {
                    //根据物理数据集id查找这个数据集有几个用户
                    List<String> UserIds = dataSetOperationService.findAuthRoleAndUserbyPhysicalDatasetId(datasetId);
                    if (UserIds.size() != 0) {
                        for (String userId : UserIds) {
                            //物理表转换之前进行校验是否已经转换
                            Boolean isAccredit = dataSetOperationService.isAccreditDataSet(userId, datasetId);
                            dataSetOperationService.updateTableMappingOperateId(datasetId);
                            if (!isAccredit) {
                                //count++;
                                System.out.println(datasetId + "----------" + entry.getKey().toString() + "==========" + userId + "-----------" + isAccredit);
                                String dataSetDbType = dataSetOperationService.getDataSetDbType(datasetId);
                                if (!"".equals(dataSetDbType) && !"ElasticSearch".equalsIgnoreCase(dataSetDbType) && !"Hbase".equalsIgnoreCase(dataSetDbType) && !"FILE".equalsIgnoreCase(dataSetDbType)) {
                                    List<String> codeList = new ArrayList<String>();
                                    codeList.add(datasetId);
                                    try {
                                        dataSetOperationService.accreditDataSet(codeList, entry.getKey().toString(), userId);//物理表转换成logic表
                                    } catch (UnexpectedRollbackException e) {
                                        System.out.println("授权数据集失败，事务回滚=====" + e.getMessage());
                                    }
                                }

                            }
                        /*    //删除之前接口错误产生的重复数据
                            Boolean repeatDataSet = dataSetOperationService.isRepeatDataSet(userId, datasetId);
                            if(repeatDataSet){
                                System.out.println("userId:"+userId+"     datasetId:"+datasetId);
                                dataSetOperationService.deleteRepeatDataSet(userId,datasetId);
                            }*/

                        }
                    }

                }
            }

        }
        System.out.println("------------------------迁移结束！-------------------------------------");
        return Result.success(treelist);
    }


    @ResponseBody
    @GetMapping("/deleteRepeatDataSet")
    @ApiOperation(value = "删除重复转换成逻辑数据集的数据")
    public Result deleteRepeatDataSet(HttpServletRequest request) {
        //不用经过权限拿到整颗树
        List<DatasetTreeModel> treeModelList = dataWareTreeService.getDataWarehouseTree("", true, true, "", null, "dataSpace");
        List<Map<String, List<String>>> treelist = dataSetOperationService.treelist(treeModelList);
        int count = 0;
        //根据数据集拿到userid
        for (Map<String, List<String>> map : treelist) {
            for (Map.Entry<String, List<String>> entry : map.entrySet()) {
                List<String> datasetIds = entry.getValue();
                for (String datasetId : datasetIds) {
                    //根据物理数据集id查找这个数据集有几个用户
                    List<String> UserIds = dataSetOperationService.findAuthRoleAndUserbyPhysicalDatasetId(datasetId);
                    if (UserIds.size() != 0) {
                        for (String userId : UserIds) {
                            //删除之前接口错误产生的重复数据
                            Boolean repeatDataSet = dataSetOperationService.isRepeatDataSet(userId, datasetId);
                            if (repeatDataSet) {
                                System.out.println("userId:" + userId + "     datasetId:" + datasetId);
                                dataSetOperationService.deleteRepeatDataSet(userId, datasetId);
                            }

                        }
                    }

                }
            }
        }
        //System.out.println("-----------------"+count);
        return Result.success(treelist);
    }

    /**
     * 数据集获取字段
     */
    @ResponseBody
    @GetMapping("/getClassifyFeatures")
    public Result getClassifyFeatures(String dataObjId) {
        ColumnDataSetVo classifyFeature = dataSetOperationService.getClassifyFeature(dataObjId);
        return Result.success(Collections.singletonList(classifyFeature));
    }


    @ResponseBody
    @PostMapping("/saveAll")
    @ApiOperation(value = "3.6.1保存")
    public Result saveAll(@RequestBody SaveSelfHelpDataSetVo vo, HttpServletRequest request) {  //name , column , guolv

        Result result = validate(vo);
        if (Objects.nonNull(result)) {
            return result;
        }
        //1.首先在数据库生成一个自定义数据集，classifyId为已选择的源数据Id
        //数据集Id  以前为多个，现在只保存一个
        String userId = (String) request.getSession().getAttribute("userId");
        if (dataSetOperationService.checkDataSetName(vo.getName(), vo.getDataSetTreeNodeId())) {
            Assert.fail("数据集中文名已存在！");
        }
        String dataSetId = dataSetOperationService.saveSingleTableDataSet(vo.getDataObjIds(), vo.getDataSetTreeNodeId(), vo.getDataSetDsTypeTreeNodeId(), userId, vo.getColumns(), vo.getName());
        Map<String, Object> map = new HashMap<>();
        map.put("dataSetId", dataSetId);
        return Result.success(map);
    }

    private Result validate(SaveSelfHelpDataSetVo vo) {
        if (CollUtil.isEmpty(vo.getDataObjIds())) {
            return Result.success("未选择数据对象！");
        }
        if (StringUtils.isBlank(vo.getDataSetTreeNodeId())) {
            return Result.error("400", "请选择数据集目录");
        }
        if (StringUtils.isBlank(vo.getDataSetDsTypeTreeNodeId())) {
            return Result.error("400", "请选择数据来源类型");
        }
        return null;
    }

    @ResponseBody
    @PostMapping("/batchSaveAll")
    @ApiOperation(value = "3.6.1批量保存")
    public Result batchSaveAll(@RequestBody List<SaveSelfHelpDataSetVo> vos, HttpServletRequest request) {  //name , column , guolv
        if (CollUtil.isEmpty(vos)) {
            return Result.success("未选择数据对象！");
        }
        //1.首先在数据库生成一个自定义数据集，classifyId为已选择的源数据Id
        //数据集Id  以前为多个，现在只保存一个
        String userId = (String) request.getSession().getAttribute("userId");
        List<String> dataSetIds = new ArrayList<>();
        List<String> dataSetNameList = new ArrayList<>();

        List<SaveTableColumnMappingVo> tableIds = new ArrayList<>();
        for (SaveSelfHelpDataSetVo vo : vos) {
            if (dataSetOperationService.checkDataSetName(vo.getName(), vo.getDataSetTreeNodeId())) {
                dataSetNameList.add(vo.getName());
            }
            tableIds.add(vo.getTableMapping());
        }

        if (CollUtil.isNotEmpty(dataSetNameList)) {
            Assert.fail(String.format("数据集中文名已存在！分别是：%s", String.join(",", dataSetNameList)));
        }
        TimeInterval interval = new TimeInterval();
        Map<String, List<LogicDataSetColumnVo>> mapColumns = dataSetOperationService.getClassifyFeatures(tableIds);
        log.info("cost ClassifyFeatureByObjIds {}ms", interval.restart());
        for (SaveSelfHelpDataSetVo vo : vos) {
            List<LogicDataSetColumnVo> columnVos = new ArrayList<>();
            for (String objId : vo.getDataObjIds()) {
                columnVos.addAll(mapColumns.get(objId));
            }
            if(CollUtil.isNotEmpty(columnVos)){
            String dataSetId = dataSetOperationService.saveSingleTableDataSet(vo.getDataObjIds(), vo.getDataSetTreeNodeId(), vo.getDataSetDsTypeTreeNodeId(), userId, columnVos, vo.getName());
            dataSetIds.add(dataSetId);
            }
        }
        log.info("cost allSave {}ms", interval.restart());
        return Result.success(dataSetIds);
    }

    @ResponseBody
    @PostMapping("/saveSelfHelpDataSet")
    @ApiOperation(value = "3.6.1保存自助数据集")
    public Result saveSelfHelpDataSet(@RequestBody SaveSelfHelpDataSetVo vo, HttpServletRequest request) {
        Result result = validate(vo);
        if (Objects.nonNull(result)) {
            return result;
        }
        if (vo.getDataSetJoinVo() == null) {
            vo.setDataSetJoinVo(Collections.emptyList());
        }
        String userId = (String) request.getSession().getAttribute("userId");
        if (dataSetOperationService.checkDataSetName(vo.getName(), vo.getDataSetTreeNodeId())) {
            Assert.fail("数据集中文名已存在！");
        }
        String dataSetId = dataSetOperationService.saveMultiTableDataSet(vo.getDataObjIds(), vo.getDataSetTreeNodeId(), vo.getDataSetDsTypeTreeNodeId(), userId, vo.getColumns(), vo.getName());
        editService.multiJoinAndFilter(dataSetId, vo.getDataSetJoinVo(), vo.getFilterConditionStep());
        Map<String, Object> map = new HashMap<>();
        map.put("dataSetId", dataSetId);
        return Result.success(map);
    }

    @ResponseBody
    @GetMapping("/getAllTypes")
    public Result getAllType() {
        return Result.success(dataSetOperationService.findAllType());
    }


    @ResponseBody
    @RequestMapping("/addDCThreeDataSetAuth")
    @ApiOperation(value = "3.0添加逻辑数据集授权")
    @FuncScanAnnotation(code = "dataSetOperationSetSingleShare", name = "分享", parentCode = "dataSetOperation")
    public Result addDCThreeDataSetAuth(@RequestBody DataSetAuthVo dataSetAuthVo) {
        dataSetAuthService.addDCThreeDataSetAuth(dataSetAuthVo);
        return Result.success();
    }

    @ResponseBody
    @RequestMapping("/dataDCThreeSetsAuth")
    @ApiOperation(value = "3.0逻辑数据集批量授权")
    @FuncScanAnnotation(code = "dataSetOperationSetBatchSharing", name = "批量分享", parentCode = "dataSetOperation")
    public Result dataDCThreeSetsAuth(@RequestBody DataSetAuthVo dataSetAuthVo) {
        dataSetAuthService.saveDCThreeBatchDataSetAuth(dataSetAuthVo, false);
        return Result.success();
    }

    @ResponseBody
    @RequestMapping("/cancelDCThreeDataSetsAuth")
    @ApiOperation(value = "3.0逻辑数据集取消授权")
    public Result cancelDCThreeDataSetsAuth(@RequestBody DataSetAuthVo dataSetAuthVo, HttpServletRequest request) {
        String userId = (String) request.getSession().getAttribute("userId");
        dataSetAuthService.deleteDCThreeDataSetAuthRelation(dataSetAuthVo, userId);
        return Result.success();
    }


    @RequestMapping("/getDataSourceTree")
    @FuncScanAnnotation(code = "dataSetOperationSetBatchSharing", name = "数据仓库", parentCode = "dataSetOperation")
    public Result getDataSourceTree(@RequestParam(required = false, defaultValue = "false") boolean isLogic, @RequestParam(required = false, defaultValue = "false") boolean dataSpace, HttpServletRequest request) {
        String userId = (String) request.getSession().getAttribute("userId");
        List<String> authIds = new ArrayList<>();

        String dataSpaceStr = dataSpace ? "dataSpace" : "";
        if (null != userId) {
            authIds = userService.getAllAuthId(userId);
        }
        List<DatasetTreeModel> treeModelList = null;
        if (isLogic) {
            treeModelList = dataSetOperationService.querySourceDatasetTree(userId, "", null);
        } else {
            treeModelList = dataWareTreeService.getDataWarehouseTreeCopy("", false, true, userId, authIds, dataSpaceStr);
        }
        return Result.success(treeModelList);
    }

    @ResponseBody
    @GetMapping("/{id}")
    @ApiOperation("获取数据集分类")
    public Result getCatalog(@PathVariable("id") String id) {
        Map<String, String> map = dataSetOperationService.getCatalogList(id);
        return Result.success(map);
    }
}
