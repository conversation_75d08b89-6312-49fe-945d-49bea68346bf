package com.dragonsoft.cicada.datacenter.modules.dataplan.externaldatasources.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * spark表缓存级别枚举
 *
 * <AUTHOR> Wu.D.J
 * @Create : 2020.04.15
 */
public enum SparkCacheEnum {

    /**
     * 表示spark表缓存到硬盘
     */
    DISK("硬盘", "DISK_ONLY"),

    /**
     * 表示spark表缓存到内存
     */
    MEMORY("内存","MEMORY_ONLY");

    private final String name;

    private final String code;

    SparkCacheEnum(String name, String code) {
        this.name = name;
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public String getCode() {
        return code;
    }

    public static Map<String, String> getCaches() {
        Map<String, String> cache = new HashMap<>();
        for (SparkCacheEnum spark : SparkCacheEnum.values()) {
            cache.put(spark.getName(), spark.getCode());
        }
        return cache;
    }
}
