package com.dragonsoft.cicada.datacenter.modules.datavisual.model;
import cn.hutool.core.date.StopWatch;
import com.code.dragonsoft.dataquery.service.querymodel.ColumnDataModel;
import com.code.metadata.datavisual.WidgetDatasetDims;
import com.code.metadata.datavisual.WidgetDatasetDimsDrill;
import com.dragonsoft.cicada.datacenter.modules.datavisual.dataset.IDataSetBuilder;
import com.dragonsoft.cicada.datacenter.modules.datavisual.model.annotation.WidgetLabel;
import com.dragonsoft.cicada.datacenter.modules.datavisual.model.enums.WidgetType;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Set;

/**
 * @author: yecc
 * @create: 2021-04-06 08:59
 */
@Slf4j
@WidgetLabel(name = "表单", describe = "表单", type = WidgetType.FORMWIDGET)
public class FormWidget extends TableChartWidget{
    @Override
    public String getSQL(IDataSetBuilder dataSetBuilder, int mode) {
        String sql = super.getSQL(dataSetBuilder, mode);
        if("ORACLE".equalsIgnoreCase(this.getWidgetDataset().getDbType())){
            if(!sql.toLowerCase().contains("where")){
                return String.format("%s where rownum < %s", sql, this.previewLine);
            }else if(sql.toLowerCase().contains("where")){
                return String.format("%s and rownum < %s", sql, this.previewLine);
            }
        }

        return String.format("%s limit %s", sql, this.previewLine);
    }

    @Override
    public Map buildResult(int timers, StopWatch stopWatch, ColumnDataModel columns) {
        stopWatch.start("表格前端对象封装");


        Set<WidgetDatasetDims> datasetDims = this.getWidgetDataset().sortWidgetDatasetDims();
        Set<WidgetDatasetDimsDrill> datasetDimsDrill = this.getWidgetDataset().sortWidgetDatasetDimsDrill();
        for (Map b : columns.getFieldValue()) {
            Map<String, Object> map = new LinkedHashMap();
            if(datasetDimsDrill.isEmpty()){
                for (WidgetDatasetDims d : datasetDims) {
                    map.put(d.getFiledName(), this.getDimsFilterVal(d, null,b));
                }
            }else{
                for (WidgetDatasetDimsDrill d : datasetDimsDrill) {
                    map.put(d.getFiledName(), this.getDimsFilterVal(new WidgetDatasetDims(), d,b));
                }
            }
            tableDatas.add(map);
        }
        Map rq = new HashMap();
        Map data = new HashMap();
        data.put("head", this.headDatas);
        data.put("data", this.tableDatas);
        data.put("count", count);
        rq.put("code", 1);
        rq.put("data", data);
        stopWatch.stop();
        if (stopWatch.getTotalTimeSeconds() >= timers) {
            log.info(stopWatch.prettyPrint());
        }
        return rq;
    }
}
