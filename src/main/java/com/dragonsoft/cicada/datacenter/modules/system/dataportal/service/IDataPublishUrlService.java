package com.dragonsoft.cicada.datacenter.modules.system.dataportal.service;

import com.code.metadata.portal.PublishUrl;

/**
 * <AUTHOR>
 * @date 2020/9/3
 */
public interface IDataPublishUrlService {

    /**
     * 保存发布发布页面链接
     * @param url
     * @param name
     * @param userId
     * @param stat
     */
    void savePublishUrl(String url,String name,String userId,String stat,String portalId);

    /**
     * 删除
     * @param id
     */
    void deletePublishUrl(String id);

    /**
     * 通过Id查询
     * @param id
     * @return
     */
    PublishUrl queryById(String id);
}
