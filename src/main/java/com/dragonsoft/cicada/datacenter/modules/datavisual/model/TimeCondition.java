package com.dragonsoft.cicada.datacenter.modules.datavisual.model;

import com.code.common.utils.StringUtils;
import com.code.meta.dml.standard.IMultCdin;
import com.code.meta.dml.standard.cdins.MultCdin;
import com.code.meta.dml.standard.cdins.QueryCdins;

import java.text.SimpleDateFormat;
import java.util.Date;


public class TimeCondition extends AbsConditionWidget {
    Long[] value;

    @Override
    IMultCdin builderCondition(QueryCdins queryCdins) {
        IMultCdin multCdin=new MultCdin();
        if(null==value||value.length!=2||null==value[0]||value[0]==0||null==value[1]||value[1]==0){
            return multCdin;
        }

        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String st = simpleDateFormat.format(new Date(value[0]));
        String et = simpleDateFormat.format(new Date(value[1]));
        String s ="TO_TIMESTAMP('"+st+"','yyyy-MM-dd hh24:mi:ss')";
        String e ="TO_TIMESTAMP('"+et+"','yyyy-MM-dd hh24:mi:ss')";
        String fieldCode = StringUtils.isBlank(this.field.getFieldAlias()) ? this.field.getCode() : this.field.getFieldAlias();

        multCdin.and(queryCdins.ge(fieldCode, s), queryCdins.le(fieldCode, e));

        return multCdin;
    }

    public Long[] getValue() {
        return value;
    }

    public void setValue(Long[] value) {
        this.value = value;
    }
}
