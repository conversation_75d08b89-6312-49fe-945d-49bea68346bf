package com.dragonsoft.cicada.datacenter.modules.datavisual.function.functions;

import com.code.dragonsoft.dataquery.service.querymodel.ColumnDataModel;
import com.dragonsoft.cicada.datacenter.modules.datavisual.function.ChartConfig;
import com.dragonsoft.cicada.datacenter.modules.datavisual.function.MemoryFunction;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;

import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR> Jiebin
 * @date 2020-12-30 10:11
 */
@Slf4j
public abstract class AbstractFunction<T> implements MemoryFunction {

    protected Set<String> timeType = Sets.newHashSet("TIME", "TIMESTAMP", "DATE");
    protected List<String> weekAndQuarter = Lists.newArrayList("周", "季度","当年周");


    @Override
    public final ColumnDataModel calculate(ColumnDataModel dates, ChartConfig config) {
        return this.doCalculate(dates, buildMeta(config,dates));
    }

    protected abstract T buildMeta(ChartConfig config,ColumnDataModel dates);

    protected abstract ColumnDataModel doCalculate(ColumnDataModel dates, T t);

    protected static class CalMeta {
        private List<String> groupColumns = new ArrayList<>();
        private List<String> metricColumns = new ArrayList<>();

        public CalMeta() {
        }

        public CalMeta addGroupColumn(String columnName) {
            this.groupColumns.add(columnName);
            return this;
        }

        public CalMeta addMetricColumn(String columnName) {
            this.metricColumns.add(columnName);
            return this;
        }

        public List<String> getGroupColumns() {
            return groupColumns;
        }

        public void setGroupColumns(List<String> groupColumns) {
            this.groupColumns = groupColumns;
        }

        public List<String> getMetricColumns() {
            return metricColumns;
        }

        public void setMetricColumns(List<String> metricColumns) {
            this.metricColumns = metricColumns;
        }
    }

    protected String getTimeStampDate(Date timeStamp) {
        Date date = timeStamp;
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        // 将时分秒,毫秒域清零
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        //直接返回date对象跟第一次对象不是同一个  拿不到value 转为string返回
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        String str = df.format(calendar.getTime());
        return str;
    }

    protected Map getValueMap(Date previousDate, Map<Object, Double> dataMap, String metricColumn, Map valueMap, String timeColumn) {
        Double previousValue = 0.0;

        if (valueMap.get(timeColumn) instanceof Timestamp) {
            previousValue = dataMap.get(this.getTimeStampDate(previousDate));
        } else {
            previousValue = dataMap.get(previousDate);
        }

        Double currentValue = Double.valueOf(valueMap.get(metricColumn).toString());
        valueMap.put(metricColumn, currentValue - previousValue);
        return valueMap;
    }

    //根据字符串获取值
    protected Map getValueMap(String previousDate, Map<Object, Double> dataMap, String metricColumn, Map valueMap, String timeColumn) {
        Double previousValue = 0.0;

        previousValue = dataMap.get(previousDate);

        if(previousValue == null){
            previousValue = 0.0;
        }
        Double currentValue = Double.valueOf(valueMap.get(metricColumn).toString());
        valueMap.put(metricColumn, currentValue - previousValue);
        return valueMap;
    }


    protected Map<Object, Double> getInitialValueMap(List<Map> InitialValue, String timeColumn, String metricColumn) {
        Map<Object, Double> dataMap = new HashMap<>();
        for (Map valueMap : InitialValue) {
            if (valueMap.get(timeColumn) instanceof Timestamp) {
                dataMap.put(this.getTimeStampDate((Date) valueMap.get(timeColumn)), Double.valueOf(valueMap.get(metricColumn).toString()));
            } else {
                dataMap.put(valueMap.get(timeColumn), Double.valueOf(valueMap.get(metricColumn).toString()));
            }
        }
        return dataMap;
    }

    protected Date getPreviousYear(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.YEAR, -1);
        return calendar.getTime();
    }

    /**
     * 取精方法
     * @param number 数
     * @param decimal 小数位数
     * @return
     */
    protected Double KeepDecimals(Double number, int decimal) {
        NumberFormat nf = NumberFormat.getInstance();
        //设置保留小数位数
        nf.setMaximumFractionDigits(decimal);
        return Double.valueOf(nf.format(number));
    }

    /**
     * 字符串转换成int类型，如果不是数值转换出错，提示信息
     * @param currentDate
     * @return
     */
    protected Integer currentDateChangeIntType(String currentDate) {
        int preInt = 0;
        try {
            preInt = Integer.valueOf(currentDate.substring(0, 4));
        }catch (Exception e){
            log.error(e.getMessage(),e);
            com.code.common.utils.assertion.Assert.fail(String.format("数据[%s]无法做快速计算",currentDate));
        }
        return preInt;
    }
}
