package com.dragonsoft.cicada.datacenter.modules.system.permissions.vo;

import com.dragonsoft.cicada.datacenter.modules.system.common.vo.TreeVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/6/8
 */
@Data
@ApiModel(value="用户组树模型")
public class GroupUserTreeVo extends TreeVo {

    @ApiModelProperty(value="某用户组层级下的用户集合" ,required=true)
    private List<UserVo> userVos;
    @ApiModelProperty(value="用户组树模型子层次" ,required=true)
    private List<GroupUserTreeVo> groupUserTreeChildren;
}
