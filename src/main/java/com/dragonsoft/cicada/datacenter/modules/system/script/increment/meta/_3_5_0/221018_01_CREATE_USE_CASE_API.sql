CREATE TABLE if not exists T_MD_USE_CASE (
   ID                   VARCHAR(32)          NOT NULL,
   CASE_TYPE            VARCHAR(32)          NULL,
   CONSTRAINT PK_T_MD_USE_CASE PRIMARY KEY (ID)
)
INHERITS (T_MD_ELEMENT);

COMMENT ON COLUMN T_MD_USE_CASE.CASE_TYPE IS
'用例类型';




CREATE TABLE if not exists T_MD_USE_CASE_API (
   ID                   VARCHAR(32)          NOT NULL,
   USE_CASE_ID          VARCHAR(32)           NULL,
   API_ID               VARCHAR(32)           NULL,
   API_TYPE             VARCHAR(32)           NULL,
   CONSTRAINT PK_T_MD_USE_CASE_API PRIMARY KEY (ID)
)
INHERITS (T_MD_ELEMENT);

COMMENT ON COLUMN T_MD_USE_CASE_API.USE_CASE_ID IS
'用例id';

COMMENT ON COLUMN T_MD_USE_CASE_API.API_ID IS
'api元素id';

COMMENT ON COLUMN T_MD_USE_CASE_API.API_TYPE IS
'api接口类型';



CREATE TABLE if not exists T_MD_USE_CASE_API_INFO (
   ID                   VARCHAR(32)          NOT NULL,
   USE_CASE_API_ID      VARCHAR(32)          NULL,
   PARAM_KEY            VARCHAR(32)          NULL,
   PARAM_VALUE          VARCHAR(100)         NULL,
   CONSTRAINT PK_T_MD_USE_CASE_API_INFO PRIMARY KEY (ID)
)
INHERITS (T_MD_ELEMENT);

COMMENT ON COLUMN T_MD_USE_CASE_API_INFO.USE_CASE_API_ID IS
'用例apiID';

COMMENT ON COLUMN T_MD_USE_CASE_API_INFO.PARAM_KEY IS
'用例参数名称';

COMMENT ON COLUMN T_MD_USE_CASE_API_INFO.PARAM_VALUE IS
'用例参数值';


