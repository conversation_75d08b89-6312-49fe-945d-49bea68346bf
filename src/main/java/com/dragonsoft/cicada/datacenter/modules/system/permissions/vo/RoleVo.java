package com.dragonsoft.cicada.datacenter.modules.system.permissions.vo;

import com.dragoninfo.dfw.entity.TSysFuncBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/5/21
 */
@Data
@ApiModel(value="用户对象模型")
public class RoleVo {
    @ApiModelProperty(value="数据库唯一标识ID" ,required=true)
    private String roleId;
    /**
     * 页面上的ID
     */
    @ApiModelProperty(value="code，页面的ID" ,required=true)
    private String roleCode;
    @ApiModelProperty(value="角色名称" ,required=true)
    private String roleName;
    @ApiModelProperty(value="最后编辑时间，可为空" ,required=true)
    private Timestamp lastEditime;
    /**
     * 功能 功能无ID code作为唯一标识
     */
    @ApiModelProperty(value="角色拥有的功能" ,required=true)
    private List<TSysFuncBase> functions;
    @ApiModelProperty(value="编辑人" ,required=true)
    private String editor;
    /**
     * 在数据源获取全部角色使用 当前角色是有授权
     */
    private Boolean auth;

    @ApiModelProperty(value="更新时删除功能" ,required=true)
    private List<TSysFuncBase> deleteFunctions;

    @ApiModelProperty(value="更新时新加功能" ,required=true)
    private List<TSysFuncBase> addFunctions;
}
