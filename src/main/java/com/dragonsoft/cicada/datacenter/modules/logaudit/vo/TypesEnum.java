package com.dragonsoft.cicada.datacenter.modules.logaudit.vo;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/10/12
 */
public enum TypesEnum {

    /**
     * 操作类型推断
     * */
    LOGIN("0", "登录"),
    ADD("1","新增"),
    DELETE("2","删除"),
    SELECT("3","查询"),
    EDIT("4","编辑"),
    SUPERMARKERT_LOGIN("5","模型市场登录"),
    EXPORT("6","导出");

    private String code;
    private String label;

    TypesEnum(String code, String label) {
        this.code = code;
        this.label = label;
    }

    public static String getCode(String value) {
        TypesEnum[] values = TypesEnum.values();
        for (TypesEnum typesEnum : values) {
            if(Objects.equals(value, typesEnum.code)) {
                return typesEnum.label;
            }
        }
        return "";
    }

    public static Map<String, String> getAllTypes() {
        TypesEnum[] values = TypesEnum.values();
        Map<String, String> rtValue = new HashMap<>(values.length);
        for (TypesEnum value : values) {
            rtValue.put(value.label, value.code);
        }
        return rtValue;
    }
}
