package com.dragonsoft.cicada.datacenter.modules.supermarkmodel.service;

import com.code.common.paging.PageInfo;
import com.dragoninfo.dfw.bean.Result;
import com.dragonsoft.cicada.datacenter.modules.supermarkmodel.vo.ModelMarketQueryVo;
import com.dragonsoft.cicada.datacenter.modules.supermarkmodel.vo.SupermarkModelVo;

import java.util.Map;

public interface IModelMarkService {

    /**
     * 模型市场模型明细
     */
    Result initViewMarkModelDetail(SupermarkModelVo modelVo);

    /**
     * 推荐同类型其它模型查询
     */
    Result querySameTypeMarkModelPage(Map<String, Object> queryModelMap);

    /**
     * 已关注用户列表查询
     */
    Result queryFocusedUserList(Map<String, Object> queryModelMap);

    /**
     * 关注或取消关注模型
     */
    Result updateFocusOrUnFocusModel(SupermarkModelVo modelVo);

    /**
     * 查询累计评价列表
     */
    Result queryModelEvaluationList(Map<String, Object> queryModelMap);

    /**
     *评价模型
     */
    Result updateEvaluationModel(SupermarkModelVo modelVo);

    /**
     *评价人员
     */
    Result updateEvaluationUser(SupermarkModelVo modelVo);

    /**
     * 查看模型评价详情
     */
    Result viewModelEvaluationDetail(SupermarkModelVo modelVo);

    /**
     * 查看人员评价基础详情  基础信息+数据分析
     */
    Result viewUserEvaluationBasicDetail(SupermarkModelVo modelVo);

    /**
     * 查看人员评价详情
     */
    Result viewUserEvaluationDetail(SupermarkModelVo modelVo);

    PageInfo queryMarketPage(ModelMarketQueryVo queryVo);
}
