package com.dragonsoft.cicada.datacenter.modules.system.permissions.vo;

public enum DatasourceTypeEnum {
    Catalog("RdbCatalog");

    private String name;

    DatasourceTypeEnum(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static DatasourceTypeEnum getInstanceByName(String name) {
        for (DatasourceTypeEnum value : DatasourceTypeEnum.values()) {
            if (value.name.equals(name)) {
                return value;
            }
        }
        return null;
    }
}
