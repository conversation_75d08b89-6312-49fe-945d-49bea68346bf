package com.dragonsoft.cicada.datacenter.modules.aopaspect;

import com.dragonsoft.cicada.datacenter.modules.filter.XssAndSqlHttpServletRequestWrapper;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import static com.dragonsoft.cicada.datacenter.modules.filter.XssAndSqlHttpServletRequestWrapper.sessionMap;

@Aspect
@Component
public class LoginAspect {

    @After(value = "execution(* com.dragoninfo.dfw.*.login..*.*login(..))")
    public void loginService() {
        HttpServletRequest servletRequest = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        HttpSession session = servletRequest.getSession(false);
        if (null != session && !XssAndSqlHttpServletRequestWrapper.sessionIdList.contains(session.getId())) {
            addSession(session);
        }
    }

    @Before(value = "execution(* com.dragoninfo.dfw.*.login..*.*logout(..))")
    public void logoutService() {
        HttpServletRequest servletRequest = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        HttpSession session = servletRequest.getSession(false);
        if (null != session && XssAndSqlHttpServletRequestWrapper.sessionIdList.contains(session.getId())) {
            removeSession(session);
        }
    }

    @After(value = "execution(* com.dragonsoft.cicada.datacenter.modules.dids.controller..*.*ssoSysInit(..))")
    public void didsLoginService() {
        HttpServletRequest servletRequest = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        HttpSession session = servletRequest.getSession(false);
        if (null != session && !XssAndSqlHttpServletRequestWrapper.sessionIdList.contains(session.getId())) {
            addSession(session);
        }
    }

    @After(value = "execution(* com.dragonsoft.cicada.datacenter.modules.dids.controller..*.*loginOut(..))")
    public void didsLogoutService() {
        HttpServletRequest servletRequest = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        HttpSession session = servletRequest.getSession(false);
        if (null != session && XssAndSqlHttpServletRequestWrapper.sessionIdList.contains(session.getId())) {
            removeSession(session);
        }
    }

    private void addSession(HttpSession session) {
        XssAndSqlHttpServletRequestWrapper.sessionIdList.add(session.getId());
        if (StringUtils.isNotBlank(session.getId())){
            session.setMaxInactiveInterval(86400);
            sessionMap.put(session.getId(), session);
        }
    }

    private void removeSession(HttpSession session) {
        XssAndSqlHttpServletRequestWrapper.sessionIdList.remove(session.getId());
        sessionMap.remove(session.getId());
    }
}
