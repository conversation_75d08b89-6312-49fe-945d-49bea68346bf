package com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.vo.LogicDataSet;

import com.alibaba.fastjson.JSON;
import com.code.dataset.operator.join.JoinTableStep;
import lombok.Data;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/10/19 17:38
 */
@Data
public class JoinTableStepVo extends JoinTableStep {
    private String targetDataSetName;
    private String sourceDataSetName;

    public static JoinTableStepVo fromJoinTableStep(JoinTableStep joinTableStep) {
        return JSON.parseObject(JSON.toJSONString(joinTableStep), JoinTableStepVo.class);
    }
}
