package com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.enums;

import java.util.HashMap;
import java.util.Map;

public enum DataSetTypesEnum {

    STRING("字符型","String"),
    DATE("日期型","Date"),
    DOUBLE("浮点型","Double"),
    INTEGER("整数型","Integer");

    private String label;
    private String value;

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    DataSetTypesEnum(String label, String value) {
        this.label = label;
        this.value = value;
    }

    public static Map<String, String> getAllTypes() {
        Map<String, String> rtValue = new HashMap<>();
        for (DataSetTypesEnum value : DataSetTypesEnum.values()) {
            rtValue.put(value.getLabel(), value.getValue());
        }
        return rtValue;
    }

}
