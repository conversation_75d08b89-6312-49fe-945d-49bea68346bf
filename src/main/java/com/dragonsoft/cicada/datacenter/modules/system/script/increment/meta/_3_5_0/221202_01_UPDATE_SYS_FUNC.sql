
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state")
 VALUES ('tempFuncCode_testSingleService', null, null, null, NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, null);
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state")
VALUES ('tempFuncCode_testBatchService', null, null, null, NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, null);
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state")
 VALUES ('tempFuncCode_importUseCase', null, null, null, NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, null);
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state")
VALUES ('tempFuncCode_addUseCase', null, null, null, NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, null);
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state")
VALUES ('tempFuncCode_runUseCase', null, null, null, NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, null);
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state")
 VALUES ('tempFuncCode_editUseCase', null, null, null, NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, null);
INSERT INTO "public"."t_sys_func"("func_code", "func_type", "func_name", "parent_func_code", "description", "start_date", "end_date", "create_time", "update_time", "enable_state")
 VALUES ('tempFuncCode_deleteUseCase', null, null, null, NULL, NULL, NULL, '2020-08-19 18:45:57.515', NULL, null);




update t_sys_auth_obj_func set func_code ='tempFuncCode_testSingleService' where id in ( select id from t_sys_auth_obj_func where func_code ='testSingleService' );
update t_sys_auth_obj_func set func_code ='tempFuncCode_testBatchService' where id in ( select id from t_sys_auth_obj_func where func_code ='testBatchService' );
update t_sys_auth_obj_func set func_code ='tempFuncCode_importUseCase' where id in ( select id from t_sys_auth_obj_func where func_code ='importUseCase' );
update t_sys_auth_obj_func set func_code ='tempFuncCode_addUseCase' where id in ( select id from t_sys_auth_obj_func where func_code ='addUseCase' );
update t_sys_auth_obj_func set func_code ='tempFuncCode_runUseCase' where id in ( select id from t_sys_auth_obj_func where func_code ='runUseCase' );
update t_sys_auth_obj_func set func_code ='tempFuncCode_editUseCase' where id in ( select id from t_sys_auth_obj_func where func_code ='editUseCase' );
update t_sys_auth_obj_func set func_code ='tempFuncCode_deleteUseCase' where id in ( select id from t_sys_auth_obj_func where func_code ='deleteUseCase' );

update t_sys_func set func_code ='testOnlineTestSingleService' where func_code ='testSingleService';
update t_sys_func set func_code ='testOnlineTestBatchService' where func_code ='testBatchService';
update t_sys_func set func_code ='testOnlineImportUseCase' where func_code ='importUseCase';
update t_sys_func set func_code ='useCaseManagementAddUseCase' where func_code ='addUseCase';
update t_sys_func set func_code ='useCaseManagementRunUseCase' where func_code ='runUseCase';
update t_sys_func set func_code ='useCaseManagementEditUseCase' where func_code ='editUseCase';
update t_sys_func set func_code ='useCaseManagementDeleteUseCase' where func_code ='deleteUseCase';


update t_sys_auth_obj_func set func_code ='testOnlineTestSingleService' where func_code ='tempFuncCode_testSingleService';
update t_sys_auth_obj_func set func_code ='testOnlineTestBatchService' where func_code ='tempFuncCode_testBatchService';
update t_sys_auth_obj_func set func_code ='testOnlineImportUseCase' where func_code ='tempFuncCode_importUseCase';
update t_sys_auth_obj_func set func_code ='useCaseManagementAddUseCase'  where func_code ='tempFuncCode_addUseCase';
update t_sys_auth_obj_func set func_code ='useCaseManagementRunUseCase' where func_code ='tempFuncCode_runUseCase';
update t_sys_auth_obj_func set func_code ='useCaseManagementEditUseCase' where func_code ='tempFuncCode_editUseCase';
update t_sys_auth_obj_func set func_code ='useCaseManagementDeleteUseCase' where func_code ='tempFuncCode_deleteUseCase';


delete from t_sys_func where func_code = 'tempFuncCode_testSingleService' ;
delete from t_sys_func where func_code = 'tempFuncCode_testBatchService' ;
delete from t_sys_func where func_code = 'tempFuncCode_importUseCase' ;
delete from t_sys_func where func_code = 'tempFuncCode_addUseCase' ;
delete from t_sys_func where func_code = 'tempFuncCode_runUseCase' ;
delete from t_sys_func where func_code = 'tempFuncCode_editUseCase' ;
delete from t_sys_func where func_code = 'tempFuncCode_deleteUseCase' ;




