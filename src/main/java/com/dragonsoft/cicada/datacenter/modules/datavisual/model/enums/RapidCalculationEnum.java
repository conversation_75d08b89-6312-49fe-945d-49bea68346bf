package com.dragonsoft.cicada.datacenter.modules.datavisual.model.enums;


/**
 * <AUTHOR>
 * @Date 2021/3/9 10:45
 */
public enum RapidCalculationEnum {
    YEAR_ON_YEAR("[\"year-on-year\"]", "year_on_year", "同比"),
    YEAR_ON_YEAR_PROPORTION("[\"year-on-year-proportion\"]", "[year_on_year_proportion]", "同比比例"),
    CHAIN_COMPARISON("[\"chain-comparison\"]", "chain_comparison", "环比"),
    CHAIN_COMPARISON_PROPORTION("[\"chain-comparison-proportion\"]", "chain_comparison_proportion", "环比比例");
    private String code;
    private String alias;
    private String name;

    RapidCalculationEnum(String code, String alias, String name) {
        this.code = code;
        this.alias = alias;
        this.name = name;
    }

    public static String getAliasByCode(String code) {
        for (RapidCalculationEnum value : RapidCalculationEnum.values()) {
            if (value.getCode().equalsIgnoreCase(code)) {
                return value.getAlias();
            }
        }
        return "";
    }

    public static String getNameByCode(String code) {
        for (RapidCalculationEnum value : RapidCalculationEnum.values()) {
            if (value.getCode().equalsIgnoreCase(code)) {
                return value.getName();
            }
        }
        return "";
    }

    public String getCode() {
        return code;
    }

    public String getAlias() {
        return alias;
    }

    public String getName() {
        return name;
    }
}
