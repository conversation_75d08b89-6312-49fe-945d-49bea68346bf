package com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.common;

import com.code.common.model.step.BaseDataStep;
import com.code.common.utils.assertion.Assert;
import com.code.metadata.res.ddl.LogicDataObj;
import com.code.metaservice.ddl.ILogicDataObjService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.service.IDataSetEditService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.vo.StepVo;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * Created by zhuangjp on 2021/1/29 16:07
 */
@Aspect
@Component
public class LogicDataSetStepInterceptor {

    @Autowired
    IDataSetEditService editService;
    @Autowired
    ILogicDataObjService logicDataObjService;

    @Pointcut("execution(* com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.service.impl.DataSetEditServiceImpl.*(..))")
    private void anyMethod() {
    }
    @Pointcut("execution(* com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.service.impl.DataSetEditServiceImpl.deleteDataStep(..))")
    private void deleteStep() {
    }

    /**
     * 数据集步骤操作前置通知
     *
     * @param baseDataStep
     */
    @Before("anyMethod() && args(baseDataStep) ")
    public void doAccessStepOperator(BaseDataStep baseDataStep) {
        checkLogicDataSet(baseDataStep.getDataSetId());
    }
    /**
     * 数据集步骤编辑前置通知
     *
     * @param stepVo
     */
    @Before("anyMethod() && args(stepVo) ")
    public void doAccessEditStep(StepVo stepVo) {
        checkLogicDataSet(stepVo.getDataSetId());
    }
    /**
     * 数据集步骤删除前置通知
     * @param id
     * @param dataSetId
     */
    @Before("deleteStep() && args(id, dataSetId) ")
    public void doAccessDeleteStep(String id, String dataSetId) {
        checkLogicDataSet(dataSetId);
    }

    private void checkLogicDataSet(String dataSetId) {
        List<Map> list = editService.checkLogicDataSetUsed(dataSetId);
        if (list.size() > 0){
            LogicDataObj obj = logicDataObjService.findLogicDataObjById(dataSetId);
            StringBuilder failInfo = new StringBuilder();
            failInfo.append("数据集[");
            failInfo.append(obj.getName());
            failInfo.append("]被");
            if(list.size() == 1){
                failInfo.append("[");
                failInfo.append(list.get(0).get("name"));
                failInfo.append("]");
            }else{
                failInfo.append("多个数据");
            }
            failInfo.append("引用，无法进行操作，如需操作，请先另存为别的数据集");

            Assert.fail(failInfo.toString());

        }

    }

}
