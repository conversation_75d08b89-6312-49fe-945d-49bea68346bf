package com.dragonsoft.cicada.datacenter.modules.supermarkmodel;

public enum MarkModelEnum {


    DXLX("DXLX", "对象类型", "objTypeCodeList"),
    YYLX("YYLX", "应用类型", "appTypeCodeList"),
    JZFL("JZ<PERSON>", "警种分类", "polTypeCodeList"),
    AJLX("AJLX", "案件类型", "caseTypeCodeList"),
    QYLX("QYLX", "区域类型", "areaTypeCodeList"),
    DFGKSL("DFGKSL", "打防管控类", "controlTypeCodeList"),
    HYFL_SJ("HYFL_SJ", "行业分类", "hyflSjTypeCodeList"),
    YYLX_SJ("YYLX_SJ", "应用类型", "yylxSjTypeCodeList");

    private final String code;
    private final String name;
    private final String liscode;

    MarkModelEnum(String code, String name, String liscode) {
        this.code = code;
        this.name = name;
        this.liscode = liscode;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getLiscode() {
        return liscode;
    }
}