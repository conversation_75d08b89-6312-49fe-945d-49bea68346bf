package com.dragonsoft.cicada.datacenter.modules.dataplan.externaldatasources.vo.datawarehouse;

import lombok.Data;

@Data
public class DataWarehouseVo {

    private String id;
    private String name;    //领域库别名
    private String dbType;  //ods/领域库/特征库/专题
    private String domainDbType;    //数据库选型
    private String esElementId;     //es实例id
    private String hbaseElementId;     //hbase实例id
    private String otherElementId;    //其他数据库实例id
    private String elementId;        //用于查看数据源返回的数据
    private String softwareId;
    private String dataSourceName;

}
