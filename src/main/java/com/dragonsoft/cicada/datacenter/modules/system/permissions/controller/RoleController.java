package com.dragonsoft.cicada.datacenter.modules.system.permissions.controller;

import com.code.common.paging.PageInfo;
import com.dragoninfo.dfw.annotation.FuncScanAnnotation;
import com.dragoninfo.dfw.annotation.ValidateAndLogAnnotation;
import com.dragoninfo.dfw.bean.Result;
import com.dragoninfo.dfw.entity.TSysAuthRole;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.service.IRoleService;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.vo.PageVo;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.vo.RoleVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2020/5/21
 */
@RestController
@CrossOrigin
@RequestMapping("/role")
@Api(value = "RoleController|角色操作控制器，页面上的ID为数据库中的code")
@FuncScanAnnotation(code = "roleManagement", name = "角色管理", parentCode = "systemManagement")
public class RoleController {

    @Autowired
    private IRoleService roleService;


    /**
     * 获取角色随机code
     *
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/getRoleRandom", method = RequestMethod.POST)
    @ApiOperation(value = "获取角色页面的随机ID")
    public Result getRoleRandom() {
        String code = roleService.getRoleRandom();
        return Result.success(code);
    }

    /**
     * 获取角色列表 支持ID与name的模糊查询
     *
     * @param pageVo
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/queryRoleList", method = RequestMethod.POST)
    @ApiOperation(value = "查询角色分页，支持ID与name的模糊查询，页面上的ID是参数里的code")
    public Result queryRoleList(@RequestBody PageVo pageVo) {
        PageInfo pageInfo = roleService.queryRolesPageByCodeOrName(pageVo);
        return Result.success(pageInfo);
    }

    /**
     * 查询所有角色
     *
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/queryAllRole", method = RequestMethod.POST)
    @ApiOperation(value = "查询admin以外的所有角色")
    public Result queryAllRole() {
        List<TSysAuthRole> tSysAuthRoles = roleService.queryAllRole();
        return Result.success(tSysAuthRoles);
    }

    /**
     * 查询所有角色
     *
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/queryDidsRoles", method = RequestMethod.POST)
    @ApiOperation(value = "查询admin以外的所有角色")
    public Result queryDidsRoles() {
        List<TSysAuthRole> tSysAuthRoles = roleService.queryDidsRoles();
        return Result.success(tSysAuthRoles);
    }


    /**
     * 通过ID获取角色
     *
     * @param roleId
     * @return
     */
    @ResponseBody
    @RequestMapping("/queryByRoleId")
    @ApiOperation(value = "通过ID查询角色，此ID为数据库实际ID")
    public Result queryRoleById(@RequestBody String roleId) {
        RoleVo roleVo = roleService.queryRoleById(roleId);
        return Result.success(roleVo);
    }

    @ResponseBody
    @RequestMapping("/queryRole")
    @ApiOperation(value = "详情")
    @FuncScanAnnotation(code = "roleManagementQueryRole", name = "详情", parentCode = "roleManagement")
    @ValidateAndLogAnnotation
    public Result queryRole(@RequestBody String roleId) {
        RoleVo roleVo = roleService.queryRoleById(roleId);
        return Result.success(roleVo);
    }

    /**
     * 更新角色信息
     *
     * @return
     */
    @ResponseBody
    @RequestMapping("/setAuth")
    @ApiOperation(value = "权限设置")
    @FuncScanAnnotation(code = "roleManagementSetAuth", name = "权限设置", parentCode = "roleManagement")
    @ValidateAndLogAnnotation
    public Result setAuth(@RequestBody RoleVo roleVo) {
        roleService.upataRole(roleVo);
        return Result.success();
    }

    /**
     * 更新角色信息
     *
     * @return
     */
    @ResponseBody
    @RequestMapping("/updataRole")
    @ApiOperation(value = "更新角色信息")
    @FuncScanAnnotation(code = "roleManagementUpdataRole", name = "编辑", parentCode = "roleManagement")
    @ValidateAndLogAnnotation
    public Result updataRole(@RequestBody RoleVo roleVo) {
        roleService.upataRole(roleVo);
        return Result.success();
    }

    /**
     * 删除角色
     *
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/deleteRole", method = RequestMethod.POST)
    @ApiOperation(value = "删除角色")
    @FuncScanAnnotation(code = "roleManagementDeleteRole", name = "删除", parentCode = "roleManagement")
    @ValidateAndLogAnnotation
    public Result deleteRole(@RequestBody String roleId) {
        String msg = roleService.deleteRole(roleId);
        return Result.success(msg);
    }

    /**
     * 添加角色
     *
     * @param roleVo
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/addRole", method = RequestMethod.POST)
    @ApiOperation(value = "添加角色")
    @FuncScanAnnotation(code = "roleManagementAddRole", name = "添加", parentCode = "roleManagement")
    @ValidateAndLogAnnotation
    public Result addRole(@RequestBody RoleVo roleVo) {
        roleService.addRole(roleVo);
        return Result.success();
    }
}
