package com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.controller;

import com.code.common.spark.model.SparkColumnMeta;
import com.code.common.utils.StringUtils;
import com.code.common.utils.assertion.Assert;
import com.code.metadata.res.ddl.LogicDataObj;
import com.code.metadata.res.structured.rdb.RdbSchema;
import com.code.metaservice.ddl.ILogicDataObjService;
import com.code.metaservice.res.structured.rdb.IRdbSchemaService;
import com.dragoninfo.dfw.bean.Result;
import com.dragonsoft.cicada.datacenter.common.token.GlobalConstant;
import com.dragonsoft.cicada.datacenter.common.utils.UserContextUtil;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.service.IQuickSqlAnalysisService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.dataassets.vo.LogicDataSet.PreviewVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Wu.D.J
 * @Create : 2020.07.29
 */
@RestController
@CrossOrigin
@RequestMapping("/quickSql")
@Api(value = "QuickSqlAnalysisController|即席SQL分析")
public class QuickSqlAnalysisController {

    @Autowired
    private IQuickSqlAnalysisService quickSqlAnalysisService;

    @Autowired
    private ILogicDataObjService logicDataObjService;

    @Autowired
    private IRdbSchemaService rdbSchemaService;



    /*@GetMapping("/create")
    @ApiOperation(value = "即席SQL分析")
    @FuncScanAnnotation(code = "dataSetOperationCreateSql", name = "即席sql分析", parentCode = "dataSetOperation")
//    @ValidateAndLogAnnotation
    public Result createQuickSqlDataSet(HttpSession session,
                                        @ApiParam(value = "目录id") String classifyId,
                                        @ApiParam(value = "数据集中文名") String dataObjName,
                                        @ApiParam(value = "数据集英文名") String dataObjCode,
                                        @ApiParam(value = "数据源Id") String schemaId) {
        String userId = (String) session.getAttribute("userId");
        String dataObjId = quickSqlAnalysisService.saveQuickSqlDataObj(userId, classifyId, dataObjName, dataObjCode, schemaId);
        return Result.success(dataObjId);
    }*/

    @GetMapping("/query")
    @ApiOperation(value = "即席SQL分析编辑回显")
    public Result queryQuickSqlDatSet(@ApiParam(value = "数据集id") String dataObjId) {
        Map<String, Object> result = new HashMap<>();
        LogicDataObj dataObj = logicDataObjService.findLogicDataObjById(dataObjId);
        Assert.notNull(dataObj, "id[" + dataObjId + "]获取数据集为空!");

        RdbSchema schema = rdbSchemaService.get(dataObj.getOwnerId());

        result.put("id", dataObj.getId());
        result.put("name", dataObj.getName());
        result.put("code", dataObj.getCode());
        result.put("sql", dataObj.getSql());
        result.put("schemaId", dataObj.getOwnerId());
        result.put("schemaName", StringUtils.isNotBlank(schema.getName()) ? schema.getName() : schema.getCode());
       /* if (dataObj.getSql() != null) {
          List<SparkColumnMeta> columns = quickSqlAnalysisService.getSqlColumns(dataObjId,dataObj.getSql());
            result.put("columns", columns);
        }*/
        return Result.success(result);
    }

    @PostMapping("/columns")
    @ApiOperation(value = "字段分析")
    public Result analysisSqlColumn(@RequestBody PreviewVo previewVo) {
        List<SparkColumnMeta> columns = quickSqlAnalysisService.getSqlColumns(previewVo);
        return Result.success(columns);
    }

    @PostMapping("/preview")
    @ApiOperation(value = "数据预览")
    public Result previewSqlData(@RequestBody PreviewVo previewVo) {
        return quickSqlAnalysisService.previewSqlDataPage(previewVo);
    }

    /*@GetMapping("/save")
    @ApiOperation(value = "保存")
    public Result saveSqlDataSet(@ApiParam(value = "数据集id") String dataObjId,
                                 @ApiParam(value = "表名") String dataObjName,
                                 @ApiParam(value = "查询sql") String sql) {
        quickSqlAnalysisService.updateQuickSqlDataObj(dataObjId, dataObjName, sql);
        return Result.success();
    }*/

    @GetMapping("/registered")
    @ApiOperation(value = "数据对象信息")
    public Result getRegisteredTables(@ApiParam(value = "schemaId") String schemaId) {
//        TableRegisterVo registerVo = quickSqlAnalysisService.queryRegisteredJson(schemaId);
        return Result.success(quickSqlAnalysisService.queryRegisteredJson(schemaId));
    }

    @PostMapping("/saveQuickSql")
    @ApiOperation(value = "3.6.1数据对象信息")
    public Result getRegisteredTables(@RequestBody Map<String, Object> params, HttpSession session) {
        String userId = UserContextUtil.getUserIdByHttpSession(session);
        params.put(GlobalConstant.UserProperties.USER_ID, userId);
        Result<Map<String, Object>> result = quickSqlAnalysisService.saveQuickSqlDataObj(params);
        Map<String, Object> map = result.getData();
        return Result.success(map.get("dataObjId"));
    }

    @PostMapping("/save")
    @ApiOperation(value = "3.6.1保存和另存为")
    public Result saveSqlDataSet(@RequestBody Map<String, Object> paramsMap, HttpServletRequest request) {
        String userId = UserContextUtil.getUserIdByHttpRequest(request);
        paramsMap.put(GlobalConstant.UserProperties.USER_ID, userId);
        return quickSqlAnalysisService.saveQuickSqlDataObj(paramsMap);
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public Result updateSqlDataSet(@RequestBody Map<String, Object> paramsMap, HttpServletRequest request) {
        String userId = UserContextUtil.getUserIdByHttpRequest(request);
        paramsMap.put(GlobalConstant.UserProperties.USER_ID, userId);

        return quickSqlAnalysisService.updateQuickSqlDataObj(paramsMap);
    }

}
