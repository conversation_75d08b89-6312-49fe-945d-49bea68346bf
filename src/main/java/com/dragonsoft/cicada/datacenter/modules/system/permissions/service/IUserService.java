package com.dragonsoft.cicada.datacenter.modules.system.permissions.service;

import com.code.common.paging.PageInfo;
import com.dragoninfo.dfw.entity.TSysAuthObj;
import com.dragoninfo.dfw.entity.TSysAuthUser;
import com.dragoninfo.dfw.entity.TSysFuncBase;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.vo.GroupUserTreeVo;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.vo.PageVo;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.vo.UserVo;

import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @date 2020/5/21
 */
public interface IUserService {

    /**
     * 登录
     *
     * @return
     */
    TSysAuthUser login(UserVo userVo);


    void updataPassword(String userId,String passWord);

    /**
     * 通过ID查询用户
     *
     * @param userId
     * @return
     */
    UserVo queryUserById(String userId);

    /**
     * @param userId
     */
    List<TSysFuncBase> queryUserFunction(String userId);

    /**
     * 获取用户的随机标识ID
     *
     * @return
     */
    String getUserRandom();

    /**
     * 查询用户列表 支持条件过滤code name
     *
     * @param pageVo
     * @return
     */
    PageInfo queryUsersPageByCodeOrName(PageVo pageVo);

    /**
     * 添加用户
     *
     * @param userVo
     * @return
     */
    void addUser(UserVo userVo);

    /**
     * 添加用户
     *
     * @return
     */
    void batchAddUser(String batchUserFilePath);

    /**
     * 编辑用户
     *
     * @param userVo
     */
    void updateUser(UserVo userVo);

    /**
     * 删除用户
     *
     * @param userId
     */
    String deleteUser(String userId);

    /**
     * 修改密码
     *
     * @param userCode
     * @param oldPassword
     * @param newPassword
     */
    String updataPassword(String userCode, String oldPassword, String newPassword);

    /**
     * 更新登录次数
     */
    void updataLoginNumber(String userId);

    /**
     * 查询所有用户
     *
     * @return
     */
    List<GroupUserTreeVo> queryAllUser();

    /**
     * 获取用户的角色
     * @param userId
     * @return
     */
    TSysAuthUser getUserRole(String userId);

    /**
     * 查询所有用户,数据源使用
     *
     * @return
     */
    List<String> getAllUserAuth(String dataSetId);

    /**
     * 获取所有拥有权限的用户ID 角色ID
     * @param userId
     * @return
     */
    List<String> getAllAuthId(String userId);


    /**
     * 获取用户拥有的所有功能权限 数据权限ID
     * @param userId
     * @return
     */
    List<String> getAllAuthFunctionId(String userId);

    /**
     * 是否存在此code用户
     * @param userCode
     */
    void isExistUser(String userCode);

    /**
     * 获取用户跟某条授权数据的关系条数(包含该用户所属角色也进行判断)
     * @param userId
     * @param classifyId
     * @return
     */
    Integer userAuthDataNumber(String userId , String classifyId);

    /**
     * 获取用户是否所属天津分局用户组
     */
    Map isTianJinFenJuUser(String userId);

    /**
     * 根据角色id拿到角色所有授权的数据集
     */
    List<String> getAllAuthDatasetId(String roleId);

    /**
     * 获取用户单独与授权数据的关系条数
     */
    Integer userAuthDatasetNumber(String userId,String classifyId);

    /**
     * 根据用户名检验登录dids用户是否已经存在数据中心
     *
     */
    TSysAuthObj checkDidsUserInDC(String userCode,String objType);


    List<String> dataSetAuthFromOthers(String roleId);

    List<String> dataSetAuthFuncIdFromOthers(String roleId);
    /**
     * 添加从dids登录的用户插入到我们的库
     */
    void syncUserToDc(TSysAuthUser tSysAuthUser);

    TSysAuthObj getUserOrRole(String objCode,String objType);

    void saveUserAndRoleRelation(TSysAuthObj toObj, TSysAuthObj fromObj);

    /**
     * 查询所有用户 不包含本身，分享的地方用
     * @param userId
     * @return
     */
    List<TSysAuthUser> getAllUserNotIncludeItself(String userId);

    /**
     * 根据账号名 拿到账户信息
     * @param userCode
     * @return
     */
    String getUserIdbyUserCode(String userCode);

    /**
     * 检验用户密码
     * @param realPassWard
     * @param uncheckPassWard
     * @param loginType
     * @return
     */
    Boolean checkUserAndPassward(String userNo,String realPassWard,String uncheckPassWard,String loginType);

    void saveClassifyByCondition(String classifyName,String classifyCode,String type,String parentDirCode,String userId);

    /**
     * 判断当前用户是否为系统管理员
     * @param userId 用户id
     * @return boolean
     */
    boolean isAdmin(String userId);

    /**
     * 通过userId 获取用户名
     * @param userIds
     * @return
     */
     Map<String, String> getUserMap(List<String> userIds);
}
