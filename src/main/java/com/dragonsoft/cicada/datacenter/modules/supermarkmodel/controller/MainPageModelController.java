package com.dragonsoft.cicada.datacenter.modules.supermarkmodel.controller;

import com.code.common.utils.assertion.Assert;
import com.dragoninfo.dfw.annotation.FuncScanAnnotation;
import com.dragoninfo.dfw.annotation.LogAnnotionForSpecial;
import com.dragoninfo.dfw.bean.Result;
import com.dragonsoft.cicada.datacenter.modules.supermarkmodel.service.IMainPageModelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@CrossOrigin
@RestController
@RequestMapping("/supermarkmodel/mainpage")
@Api(value = "MAINPAGE模型市场主页")
@Slf4j
public class MainPageModelController {

    @Autowired
    private IMainPageModelService mainPageModelServiceImpl;

    @PostMapping("/initMainPageModel")
    @ApiOperation(value = "MAINPAGE模型主页信息初始化")
    @FuncScanAnnotation(code="modelMarketLogin", name="登录", parentCode = "modelMarket")
    @LogAnnotionForSpecial
    public Result initMainPageModel() {
        return Result.success();
    }

    @PostMapping("/modelCountInfo")
    @ApiOperation(value = "MAINPAGE模型信息统计")
    public Result queryModelCountInfo(@RequestBody Map<String, Object> queryCountMap) {
        Result result = null;
        try {
            result = mainPageModelServiceImpl.queryModelCountInfo(queryCountMap);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            Assert.fail("模型信息统计失败：" + e.getMessage());
        }
        return result;
    }

    @PostMapping("/modelRankingList")
    @ApiOperation(value = "MAINPAGE查询模型排行榜列表")
    public Result queryModelRankingList(@RequestBody Map<String, Object> queryModelMap) {
        Result result = null;
        try {
            result = mainPageModelServiceImpl.queryModelRankingList(queryModelMap);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            Assert.fail("查询模型排行榜列表失败：" + e.getMessage());
        }

        return result;
    }

    @PostMapping("/modelUserPublishRankingList")
    @ApiOperation(value = "MAINPAGE查询模型用户排行上架榜列表")
    public Result queryModelUserPublishRankingList(@RequestBody Map<String, Object> queryModelMap) {
        Result result = null;
        try {
            result = mainPageModelServiceImpl.queryModelUserRankingList(queryModelMap);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            Assert.fail("查询模型用户排行上架榜列表失败：" + e.getMessage());
        }

        return result;
    }

}
