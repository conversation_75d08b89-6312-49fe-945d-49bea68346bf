package com.dragonsoft.cicada.datacenter.modules.supermarkmodel.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class SupermarkModelDetailVo {

    private String id;
    private String transId;
    private String transName; //模型名称
    private String transUserId; //模型创建人id
    private String type;
    private String state;
    private String logo;
    private String version;
    private String introduction;
    private String putTime;

    private String focusType; //0关注  1未关注

    private String typeCodeName; //模型分类合并名称
    private String objectTypeCode;
    private String objectTypeName;
    private String applicationTypeCode;
    private String applicationTypeName;
    private String policeTypeCode;
    private String policeTypeName;
    private String caseTypeCode;
    private String caseTypeName;
    private String areaTypeCode;
    private String areaTypeName;
    private String controlTypeCode;
    private String controlTypeName;
    private String hyflSjTypeCode;
    private String hyflSjTypeName;
    private String yylxSjTypeCode;
    private String yylxSjTypeName;

    private BigDecimal avgScore;
    private Integer focusCount;
    private Integer browseCount;

    private List<ModelLabelVo> labels;
    private List<ModelPictureVo> pics;

    private List<ModelDataSetVo> dataSets;

    private ModelPublishUserVo publishUser;

    private boolean canApply;
    private Integer downloadCount;
    private String scoreLevel;
    private String browseLevel;
}
