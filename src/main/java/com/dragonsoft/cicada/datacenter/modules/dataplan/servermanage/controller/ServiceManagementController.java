package com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.code.common.paging.PageInfo;
import com.code.common.utils.R;
import com.code.common.utils.assertion.Assert;
import com.code.metadata.sm.EnumServiceParamDesensitization;
import com.code.metaservice.sm.model.QueryType;
import com.code.metaservice.sm.model.ServiceStatus;
import com.dragoninfo.dfw.bean.Result;
import com.dragonsoft.cicada.datacenter.ServicePublishClient;
import com.dragonsoft.cicada.datacenter.common.utils.UserContextUtil;
import com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.service.IServiceManagementService;
import com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.vo.*;
import com.dragonsoft.cicada.datacenter.modules.modeling.vo.TransClassifyVo;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.service.IFunctionService;
import com.dragonsoft.cicada.datacenter.modules.system.permissions.service.IUserService;
import com.fw.tenon.tree.Tree;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 版权所有：厦门市巨龙信息科技有限公司
 * Copyright 2018 Xiamen Dragon Information Technology Co. Ltd.
 * All right reserved.
 * ====================================================
 * 修订记录：
 * No    日期				作者(操作:具体内容)
 * 1.    2021/9/8 6:22			baijx（<EMAIL>）(创建:创建文件)
 * ====================================================
 * 类描述：
 */
@CrossOrigin
@RestController
@RequestMapping("/publishManagement")
@Slf4j
public class ServiceManagementController {

    @Autowired
    private IServiceManagementService serviceManagementService;
    @Autowired
    private ServicePublishClient servicePublishClient;

    @Autowired
    private IUserService userService;
    @Autowired
    private IFunctionService functionService;

    @RequestMapping("/queryServicePage")
    public Result queryServicePage(@RequestBody ManagementPageVo managementPage, HttpServletRequest request) {
        String userId = UserContextUtil.getUserIdByHttpRequest(request);
        String operatorId = "";
        if (userId.equals(managementPage.getOperatorId()) || CharSequenceUtil.isBlank(managementPage.getOperatorId())) {
            operatorId = userId;
        } else {
            operatorId = managementPage.getOperatorId();
        }
        if (CharSequenceUtil.isBlank(operatorId)) {
            return Result.success();
        }
        PageInfo pageInfo = serviceManagementService.queryServicePageByCondition(managementPage, operatorId);
        return Result.success(pageInfo);
    }

    /* @FuncScanAnnotation(code = "serviceManagementedit", name = "编辑api", parentCode = "serviceManagement")
     @ValidateAndLogAnnotation*/
    @GetMapping("/queryServiceDetails")
    public Result queryServiceDetails(String id) {
        ParamConfigVo paramConfigVo = null;
        try {
            paramConfigVo = serviceManagementService.queryServiceDetails(id);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return Result.success(paramConfigVo);
    }

    @GetMapping("/queryPublishedServiceMetaSelectList")
    public Result queryPublishedServiceMetaSelectList(String sourceId) {
        Result result = serviceManagementService.queryPublishedServiceMetaSelectList(sourceId);
        return result;
    }

    /**
     * 表达式算子还是常用算子
     */
    @ResponseBody
    @GetMapping("getQueryType")
    public Result getQueryType() {
        try {
            return Result.toResult(R.ok(QueryType.getEnumTypeList()));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.toResult(R.error(e.getMessage()));
        }
    }

    /**
     * 表达式算子还是常用算子
     */
    @ResponseBody
    @GetMapping("getServiceStatus")
    public Result getServiceStatus() {
        try {
            return Result.toResult(R.ok(ServiceStatus.getEnumTypeList()));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.toResult(R.error(e.getMessage()));
        }
    }

    @PostMapping("/newServiceClassify")
    public Result newServiceClassify(@RequestBody ServiceClassifyVo vo, HttpSession session) {
        String userId = session.getAttribute("userId").toString();
        String parentId = vo.getParentId();
        String name = vo.getName();
        Assert.hasLength(userId, "请先登录!");
        String id = serviceManagementService.newServiceClassify(parentId, name, null, userId);
        return Result.toResult(R.ok(id));
    }

    @PostMapping("/editServiceClassify")
    public Result editServiceClassify(@RequestBody ServiceClassifyVo vo, HttpSession session) {
        String userId = session.getAttribute("userId").toString();
        String parentId = vo.getParentId();
        String name = vo.getName();
        String id = vo.getId();
        Assert.hasLength(userId, "请先登录!");
        serviceManagementService.editServiceClassify(id, parentId, name, null, userId);
        return Result.toResult(R.ok());
    }

    @GetMapping("/deleteServiceClassify")
    public Result deleteServiceClassify(String classifyId) {
        Assert.hasLength(classifyId, "目录id不能为空!");

        serviceManagementService.deleteServiceClassify(classifyId);
        return Result.toResult(R.ok());
    }

    @GetMapping("/queryServiceClassifyTree")
    public Result queryServiceClassifyTree(HttpSession session) {
        String userId = (String) session.getAttribute("userId");
        Assert.hasLength(userId, "请先登录!");
        List<Tree> trees = serviceManagementService.queryServiceClassifyTree(userId);
        if (CollUtil.isEmpty(trees)) {
            trees = new ArrayList<>();
        }
        if (userService.isAdmin(userId)&&functionService.isAuthToAdmin()) {
            trees.addAll(serviceManagementService.getOtherApiByUser(userId));
        }
        return Result.toResult(R.ok(trees));
    }

    @PostMapping("/moveModelService")
    public Result moveModelService(@RequestBody MoveServiceVo vo) {
        String serviceId = vo.getServiceId();
        String classifyId = vo.getClassifyId();
        Assert.hasLength(serviceId, "服务id不能为空！");
        Assert.hasLength(classifyId, "目录id不能为空！");
        serviceManagementService.moveModelService(serviceId, classifyId);
        return Result.toResult(R.ok());
    }

    @GetMapping("/getDesenTypes")
    public Result getDesenTypes() {

        EnumServiceParamDesensitization[] values = EnumServiceParamDesensitization.values();
        List<Map<String, String>> maps = Arrays.stream(values).map(v -> {
            Map<String, String> desenTypeMap = new HashMap<>();
            desenTypeMap.put("name", v.getName());
            desenTypeMap.put("code", v.getCode());
            return desenTypeMap;
        }).collect(Collectors.toList());
        return Result.toResult(R.ok(maps));
    }

    @GetMapping("/getModelServiceClassify")
    public Result getModelServiceClassify() {
        return Result.toResult(R.ok(serviceManagementService.getModelServiceClassify()));
    }

    @PostMapping("/getPluginModelServiceList")
    public Result getPluginModelServiceList(@RequestBody GetPluginModelServiceListInVo vo, HttpSession session) {
        String userId = (String) session.getAttribute("userId");
        String firstClassify = vo.getFirstClassify();
        String secondClassify = vo.getSecondClassify();
        Assert.hasLength(firstClassify, "一级分类不能为空！");
        Assert.hasLength(secondClassify, "二级分类不能为空！");
        Assert.hasLength(userId, "“请先登录！");
        return Result.toResult(R.ok(serviceManagementService.getPluginModelServiceList(firstClassify, secondClassify, userId)));
    }

    @PostMapping("/checkPlugin")
    public Result checkPlugin(@RequestBody TransClassifyVo transClassifyVo) {
        return Result.success(serviceManagementService.checkPlugin(transClassifyVo));
    }

    @GetMapping("/getServiceOutput")
    public Result getServiceOutput() {
        return servicePublishClient.getServiceOutput();
    }
}
