/*==============================================================*/
/* Table: T_ETL_TRANS_LABEL      模型标签                          */
/*==============================================================*/
CREATE TABLE if not exists T_ETL_TRANS_LABEL (
   ID                   VARCHAR(32)          NOT NULL,
   TYPE                 VARCHAR(15)          NULL,
   VAL                  VARCHAR(100)         NULL,
   TRANS_ID             VARCHAR(32)          NULL,
   CONSTRAINT PK_T_ETL_TRANS_LABEL PRIMARY KEY (ID)
)
;