package com.dragonsoft.cicada.datacenter.modules.dataplan.externaldatasources.service;

import com.code.common.paging.PageInfo;
import com.code.metaservice.datawarehouse.model.DatasetTableModel;
import com.code.metaservice.datawarehouse.model.DatasetTreeModel;
import com.dragoninfo.dfw.bean.Result;
import com.dragonsoft.cicada.datacenter.modules.dataplan.externaldatasources.vo.datawarehouse.*;
import com.dragonsoft.cicada.datacenter.modules.modeling.qo.AnalysisResultLibraryQO;
import com.dragonsoft.cicada.datacenter.modules.modeling.vo.AnalysisResultLibraryVO;
import net.sf.json.JSONObject;

import java.util.List;
import java.util.Map;

/**
 * Created by leo on 2019/3/15.
 */
public interface IDataWarehousePlanService {

    /**
     * t_md_classify_element表中插入记录
     *
     * @param elementId
     * @param classifyId
     * @return
     */
    String addTreeNode(String elementId, String classifyId);

    /**
     * 查询某个ES表的所有字段
     *
     * @param objId
     * @return
     */
    List<ElasticColumnVo> queryElasticsColumns(String objId, String dbType);


    /**
     * 新增或更新数据仓库实例对象
     *
     * @param vo
     * @param ownerId
     * @return
     */
    String addDataWarehouseInstance(DataWarehouseVo vo, String ownerId, String dirId, String userId);

    String addDataWarehouseInstance(DataWarehouseVo vo, String ownerId, String dirId, String userId, String source);

    /**
     * 添加“数据仓库”对象关联的数据库
     *
     * @param res
     * @param id
     */
    void addDWRelationDb(DataWarehouseVo res, String id);

    /**
     * 更新“数据仓库”对象关联的数据库
     *
     * @param res
     */
    void editDWRelationDb(DataWarehouseVo res);

    /**
     * 检查“数据仓库”实例名是否存在
     *
     * @param name
     * @return
     */
    boolean checkDWIsExist(String name, String dirId);


    /**
     * 保存子数据仓库节点
     *
     * @param parentId
     * @param nodeName
     * @param nodeCode
     * @return
     */
    Result addClassifyTreeNode(String parentId, String nodeName, String nodeCode);


    Result addClassifyTreeNode(String parentId, String nodeName, String nodeCode, String userId);
    /**
     * 删除子数据仓库类似“天津数据仓库”
     *
     * @param classifyId
     */
    void deleteDwInstance(String classifyId);

    /**
     * 删除子数据仓库数据库实例
     *
     * @param elementId
     * @param classifyId
     */
    boolean deleteDataWarehouseDBInstance(String elementId, String classifyId);

    /**
     * 查看数据仓库数据库实例
     *
     * @param elementId
     * @return
     */
    JSONObject getDWDBInstanceForLook(String elementId);

    /**
     * 数据子仓库重命名
     *
     * @param clasifyId
     * @param classifyName
     * @return
     */
    String renameDWInstance(String clasifyId, String classifyName, String type, String id, String dirId);

    /**
     * 判断数据仓库下是否还有数据库实例对象
     *
     * @param clasifyId
     * @return
     */
    boolean isDbInstanceExist(String clasifyId);

    Result saveDWLevel(String pId, String levelName, String levelCode);

    /**
     * 添加业务库
     *
     * @param dbType
     * @param catalogId
     * @param catalogName
     * @param selectId
     * @param selectName
     * @param level
     * @param customName
     */
    DatasetTreeModel saveBusi(String busiId, String dbType, String catalogId, String catalogName, String selectId, String selectName, int level, String customName);

    /**
     * 删除业务库
     *
     * @param dwBusiId
     * @param parentIds
     */
    void deleteBusi(String dwBusiId, String[] parentIds, String dirId);

    /**
     * 删除同步表
     *
     * @param tableId
     */
    void deleteTable(String tableId, String dbType, String judgeType);

    /**
     * 查询表信息
     *
     * @param dataBaseId
     * @return
     */
    List<Map<String, Object>> queryAddTable(String dataBaseId, String content);

    /**
     * 添加表
     *
     * @param dataBaseId
     * @param dataObject
     */
    void addTable(String dataBaseId, List<String> dataObject,String userId);

    Map<String, List<DBInstancesVo>> getAllDBInstances();

    String addBusiDir(String dirName, String dirType, String usrId);

    void updateCatalog(String name, String id, String catalogType, String userId);

    void updateLevel(String name, String id);

    void moveDataOrigin(String id, String ownerId);

    void updateDataOriginName(String name, String id);

    String deleteTreeNode(String dirId, String dirType);

    void renameDataSet(String dataId, String newName);
    void renameDataObjecDataName(String dataId, String newName);

    String getSchemaId(String tableId, String dbType);

    List<String> getTableMappingIds(List<String> elementIds);

    Map<String, String> libraryRepetition(String dirId, String dbType);

    List<Map<String, String>> getElastics(String dataSetId);

    List<Map<String, String>> getAllTableMapping();

    List<ElasticColumnVo> getAllColumn(List<String> tableIds);

    DataSetVo getDataSetInfo(String id);

    DataSetInfoVo getDeployedSoftware(String id);

    Map<String, String> getDeployedSoftwareId(String id);

    void renameDataDeployedSoftware(String id, String newName);

    String getLabelName(String id);

    DataObjectVo getTableInfo(String id);

    String updateStream(String id, boolean stream,String name);

    boolean isDataWareHouse(List<String> classifierIds);

    boolean checkTable(String id);

    void setFastTable(String tableId,String isFast);

    String getIsFast(String tableId);

    DataWarehouseVo queryDBInstanceById(String id);

    String moveDataObj(String dwDbId, String dataObjId,String dataSetName,String userId);

    int checkDataSourceName(String classifyId, String dataSourceName);

    List<DatasetTreeModel> filterSameDatasourceTree( List<DatasetTreeModel> datasetTreeModels,String filterDataSetId);

    PageInfo queryTableByDadaSourceId(String dbId, String userId, PageInfo pageInfo,String name);

    void addTableNew(String dataBaseId, List<String> dataObject,String userId);

    DataSetVo getDataSetInfoNew(String id);

    List<String> getFactNameByCode(List<String> codes);


    void saveAnalysis(AnalysisResultLibraryQO qo);

    AnalysisResultLibraryVO getAnalysisResult();

    List<DatasetTableModel> querySuperDataBaseTableAll(List<DatasetTableModel> datasetTableModels, String userId);
}
