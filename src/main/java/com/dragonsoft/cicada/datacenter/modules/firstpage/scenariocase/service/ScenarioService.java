package com.dragonsoft.cicada.datacenter.modules.firstpage.scenariocase.service;

import com.code.common.paging.PageInfo;
import com.code.metadata.scenario.TScenarioCaseType;
import com.dragonsoft.cicada.datacenter.modules.firstpage.scenariocase.vo.ScenarioVo;
import com.fw.service.IService;

import java.util.List;

public interface ScenarioService extends IService {


    PageInfo getScenarioPage(ScenarioVo scenarioVo);

    PageInfo getScenarioByPage(ScenarioVo scenarioVo);

    List<TScenarioCaseType> getAllParentTypes(String userId);

    void changeToScenarioCase(String modelId,String parentCaseType,String currentCaseType,String desc);

    void deleteScenarioCases(List<String> caseIds);
}
