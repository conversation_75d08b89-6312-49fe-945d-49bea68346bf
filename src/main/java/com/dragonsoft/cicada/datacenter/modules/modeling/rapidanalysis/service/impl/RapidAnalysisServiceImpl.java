package com.dragonsoft.cicada.datacenter.modules.modeling.rapidanalysis.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.code.common.paging.PageInfo;
import com.code.common.utils.StringUtils;
import com.code.common.utils.assertion.Assert;
import com.code.metadata.business.directory.BaseBusiClassify;
import com.code.metadata.business.directory.BaseBusiDir;
import com.code.metadata.business.directory.BusiClassify;
import com.code.metadata.business.directory.EnumBusiType;
import com.code.metadata.etl.trans.TransMeta;
import com.code.metadata.model.core.ModelElement;
import com.code.metaservice.business.directory.BusiClassifyService;
import com.code.metaservice.business.directory.BusiDirService;
import com.code.metaservice.etl.trans.TransMetaService;
import com.dragoninfo.dfw.entity.TSysAuthObj;
import com.dragoninfo.dfw.service.SysAuthObjService;
import com.dragonsoft.cicada.datacenter.modules.modeling.rapidanalysis.service.RapidAnalysisService;
import com.dragonsoft.cicada.datacenter.modules.modeling.rapidanalysis.vo.ResultRapidItem;
import com.dragonsoft.cicada.datacenter.modules.modeling.rapidanalysis.vo.ResultRapidTreeNode;
import com.fw.service.BaseService;
import com.fw.service.annotation.Service;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.stream.Collectors;

import static java.util.Comparator.comparing;

@Slf4j
@Service
public class RapidAnalysisServiceImpl extends BaseService implements RapidAnalysisService {


    @Autowired
    private BusiClassifyService busiClassifyService;

    @Autowired
    private BusiDirService busiDirService;

    @Autowired
    private TransMetaService transMetaService;

    @Autowired
    private SysAuthObjService sysAuthObjService;

    @Override
    public List<ResultRapidTreeNode> getAllRapidAnaTree(String userId,String listType) {

        return getTransDirRootList(userId,listType);
    }

    @Override
    public void addRapidDir(String parentClassifyId, String classifyName, String dirType, String userId) {
        if (busiClassifyService.checkClassifyIsExistByUserId(parentClassifyId, classifyName, EnumBusiType.valueOf(dirType), userId)) {
            throw new RuntimeException("该目录已存在，请重新命名！");
        }
        BaseBusiClassify bsClassify = new BaseBusiClassify();
        bsClassify.setCode(dirType);
        bsClassify.setName(classifyName);
        bsClassify.setOperateUserId(userId);
        if (StringUtils.isNotBlank(parentClassifyId)) {
            long startTime = System.currentTimeMillis();
            BaseBusiClassify parentBc = (BaseBusiClassify) this.baseDao.load(BaseBusiClassify.class, parentClassifyId);
            long endTime = System.currentTimeMillis();
            log.info("新建目录耗时：" + (endTime - startTime));
            bsClassify.setParentBc(parentBc);
        }
        BaseBusiDir dir = busiDirService.findEasyBusiDirBy(EnumBusiType.valueOf(dirType));
        bsClassify.setBusiDir(dir);
        this.baseDao.saveOrUpdate(bsClassify);
    }

    @Override
    public void updateRapidAnaDirName(String classifyId, String newClassifyName, String userId) {
        BaseBusiClassify bc = busiClassifyService.findBusiClassifyBy(classifyId);
        String parentBcId = null;
        if (bc.getParentBc() != null) {
            parentBcId = bc.getParentBc().getId();
        }
        if (busiClassifyService.checkClassifyIsExist(parentBcId, newClassifyName, EnumBusiType.TRANS_RAPID_DIR_MF)) {
            throw new RuntimeException("该目录已存在，请重新命名！");
        }
        bc.setName(newClassifyName);
        this.baseDao.update(bc);
    }

    @Override
    public void moveRapidDir(String currentClassifyId, String newParentClassifyId) {
        BaseBusiClassify curryClassify = busiClassifyService.findBusiClassifyBy(currentClassifyId);
        BaseBusiClassify newParentClassify = busiClassifyService.findBusiClassifyBy(newParentClassifyId);

        if (busiClassifyService.checkClassifyIsExist(newParentClassifyId, curryClassify.getName(),EnumBusiType.TRANS_RAPID_DIR_MF)) {
            throw new RuntimeException("该目录已存在！");
        }
        curryClassify.setParentBc(newParentClassify);
        curryClassify.setBusiDir(newParentClassify.getBusiDir());
        this.baseDao.update(curryClassify);
    }

    @Override
    public void deleteRapidDir(String classifyId) {
        BaseBusiClassify busiClassify = busiClassifyService.findBusiClassifyBy(classifyId);
        int size = busiClassify.getBusiClassifies().size();
        if (size > 0){
            throw new RuntimeException("请按层级删除目录！");
        }

    }

    @Override
    public PageInfo getRapidAnalysisPage(String classifyId,String rapidAnaName,Integer pageNum,Integer pageSize,String userId) {
        String hql = " from BusiClassify where id = :classifyId ";
        BusiClassify busiClassify = (BusiClassify) this.baseDao.queryForObject(hql, this.addParam("classifyId", classifyId).param());
        List<String> allChildrenClassifyIds = getAllChildrenClassifyIds(busiClassify);
        String sql = "select * from t_md_classify_element where busi_classify_id in (:allChildrenClassifyIds)";
        List<Map<String,String>> elements = this.baseDao.sqlQueryForList(sql, this.addParam("allChildrenClassifyIds", allChildrenClassifyIds).param());
        List<String> elementIds = new ArrayList<>();
        for (Map<String, String> element : elements) {
            String element_id = element.get("element_id");
            elementIds.add(element_id);
        }
        Map<String,Object> map = new HashMap<>();
        map.put("elements",elementIds);
        map.put("userId",userId);
        hql = " from TransMeta where id in (:elements) and operateUserId = :userId and taskGroup = 'QUICK_SPARK' ";
        if (StrUtil.isNotEmpty(rapidAnaName)){
            hql += " and name like :rapidAnaName ";
            map.put("rapidAnaName","%"+rapidAnaName+"%");
        }
        hql += " order by operateTime desc  ";
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageSize(pageSize);
        pageInfo.setPageIndex(pageNum);
        PageInfo pageInfo1 = this.baseDao.queryForPage(hql, map, pageInfo);
        List list = setAllDirPath(pageInfo1.getDataList(), elements);
        pageInfo1.setDataList(list);
        //System.out.println(allChildrenClassifyIds);
        return pageInfo1;
    }

    //给每条数据设置全路径
    private List setAllDirPath(List<TransMeta> transMetas,List<Map<String,String>> elements){
        List<ResultRapidItem> resultRapidItems = new ArrayList<>();
        for (TransMeta transMeta : transMetas) {
            ResultRapidItem resultRapidItem = new ResultRapidItem();
            resultRapidItem.setId(transMeta.getId());
            resultRapidItem.setName(transMeta.getName());
            resultRapidItem.setTaskGroup(transMeta.getTaskGroup());
            resultRapidItem.setUpdateTime(transMeta.getOperateTime());
            Map<String,String> map = new HashMap<>();
            map.put("id",transMeta.getOperateUserId());
            TSysAuthObj tSysAuthObj = (TSysAuthObj) sysAuthObjService.query(map);
            if (tSysAuthObj == null){
               continue;
            }
            resultRapidItem.setUsername(tSysAuthObj.getObjName());
            for (Map<String, String> element : elements) {
                String elementId = element.get("element_id");
                if (Objects.equals(elementId,transMeta.getId())){
                    String busiClassifyId = element.get("busi_classify_id");
                    BaseBusiClassify busiClassify = busiClassifyService.findBusiClassifyBy(busiClassifyId);
                    resultRapidItem.setClassifyName(busiClassify.getName());
                    resultRapidItem.setParentClassifyId(busiClassifyId);
                    String allPathString = getAllPathString(busiClassify);
                    resultRapidItem.setPath(allPathString);
                    break;
                }
            }
            resultRapidItems.add(resultRapidItem);
        }
        return resultRapidItems;
    }

    private String getAllPathString(BaseBusiClassify baseBusiClassify){
        StringBuilder sb = new StringBuilder();
        sb.append(baseBusiClassify.getName());
        BaseBusiClassify parentBc = baseBusiClassify.getParentBc();
        if (parentBc != null){
            sb.insert(0,getAllPathString(parentBc)+"/");
        }
        return sb.toString();
    }

    private List<String> getAllChildrenClassifyIds(BusiClassify busiClassify){
        List<String> list = new ArrayList<>();
        list.add(busiClassify.getId());
        Set<BusiClassify> busiClassifies = busiClassify.getBusiClassifies();
        if (busiClassifies.size() > 0){
            for (BusiClassify classify : busiClassifies) {
                List<String> allChildrenClassifyIds = getAllChildrenClassifyIds(classify);
                list.addAll(allChildrenClassifyIds);
            }
        }
        return list;
    }

 /*   private List<ModelElement> getAllChildrenElements(Set<BusiClassify> busiClassifies){
        List<ModelElement>
    }*/
    /**
     *  String hql = "from BusiClassify where parentBc.id = :id";
     *         List<BusiClassify> bcs = this.baseDao.queryForList(hql, addParam("id", node.getId()).param());
     *         Stack<BusiClassify> stack = new Stack<>();
     *         Map<String, DatasetTreeModel> rtValue = new HashMap<>(10);
     *         Map<String, String> elementIds = new HashMap<>(10);
     *         BusiClassify temp = null;
     *         for (BusiClassify busiClassify : bcs) {
     *             stack.push(busiClassify);
     *             rtValue.put(busiClassify.getId(), DatasetTreeModel.builder()
     *                     .id(busiClassify.getId())
     *                     .code(busiClassify.getCode())
     *                     .name(busiClassify.getName())
     *                     .pId(node.getId())
     *                     .open(true)
     *                     .label(busiClassify.getName())
     *                     .build());
     *         }
     *         while(!stack.isEmpty()) {
     *             temp = stack.pop();
     *             for (ModelElement element : temp.getElements()) {
     *                 elementIds.put(element.getId(), temp.getId());
     *             }
     *             for (BusiClassify busiClassify : temp.getBusiClassifies()) {
     *                 stack.push(busiClassify);
     *                 DatasetTreeModel build = DatasetTreeModel.builder()
     *                         .id(busiClassify.getId())
     *                         .code(busiClassify.getCode())
     *                         .name(busiClassify.getName())
     *                         .pId(temp.getId())
     *                         .open(true)
     *                         .label(busiClassify.getName())
     *                         .build();
     *                 rtValue.put(busiClassify.getId(), build);
     *                 rtValue.get(temp.getId()).getChildren().add(build);
     *             }
     *         }
     *         if(hasLogicDataObject && elementIds.size() > 0) {
     *             List<DatasetTreeModel> dataObjs = getDataObjects(elementIds);
     *             for (DatasetTreeModel dataObj : dataObjs) {
     *                 rtValue.get(dataObj.getpId()).getChildren().add(dataObj);
     *             }
     *         }
     *         for (Map.Entry<String, DatasetTreeModel> stringDatasetTreeModelEntry : rtValue.entrySet()) {
     *             if(Objects.equals(stringDatasetTreeModelEntry.getValue().getpId(), node.getId()))
     *                 node.addChild(stringDatasetTreeModelEntry.getValue());
     *         }
     * @param
     * @param userId
     * @param listType
     * @return
     */

    private List<ResultRapidTreeNode> getTransDirRootList( String userId,String listType) {
        List<Map> busDirMapList = this.baseDao.sqlQueryForList("select * from t_md_busi_dir where busi_dir_type = 'TRANS_RAPID_DIR_MF'");
        if (ObjectUtil.isEmpty(busDirMapList)){
            Assert.fail("查无快速分析列表关联数据");
        }
        Map busDirMap = busDirMapList.get(0);
        String hql = " from BusiClassify bc where bc.busiDir.id = :busiDirId and bc.operateUserId = :userId and" +
                " (bc.parentBc.id is null or bc.parentBc.id = :parentId) ";
        if ("time".equalsIgnoreCase(listType) || StrUtil.isEmpty(listType)){
            hql += " order by bc.operateTime asc ";
        }else if ("char".equalsIgnoreCase(listType)){
            hql += " order by bc.name asc ";
        }
        Map<String,Object> map = new HashMap<>();
        map.put("busiDirId",busDirMap.get("id"));
        map.put("userId",userId);
        map.put("parentId",busDirMap.get("id"));
        List<BusiClassify> busiClassifies = this.baseDao.queryForList(hql, map);

        return setResultNodes(busiClassifies,listType);
    }


    private List<ResultRapidTreeNode> setResultNodes(List<BusiClassify> busiClassifies,String listType){
        List<ResultRapidTreeNode> resultRapidTreeNodes = new ArrayList<>();
        for (BusiClassify busiClassify : busiClassifies) {
            ResultRapidTreeNode resultRapidTreeNode = new ResultRapidTreeNode();
            resultRapidTreeNode.setDirType(busiClassify.getType());
            resultRapidTreeNode.setId(busiClassify.getId());
            resultRapidTreeNode.setOperateTime(busiClassify.getOperateTime());
            resultRapidTreeNode.setName(busiClassify.getName());
            if (busiClassify.getParentBc() != null){
                resultRapidTreeNode.setParentId(busiClassify.getParentBc().getId());
            }
            if (busiClassify.getBusiClassifies().size() <= 0){
                resultRapidTreeNode.setIsParent(false);
                resultRapidTreeNode.setChildren(null);
            }else {
                resultRapidTreeNode.setIsParent(true);
                Set<BusiClassify> classifies = busiClassify.getBusiClassifies();
                List<BusiClassify> collect;
                if ("char".equalsIgnoreCase(listType)){
                    collect = classifies.stream().sorted(comparing(ModelElement::getName)).collect(Collectors.toList());
                }else {
                    collect = classifies.stream().sorted(comparing(ModelElement::getOperateTime)).collect(Collectors.toList());
                }
                resultRapidTreeNode.setChildren(setResultNodes(collect,listType));
            }
            resultRapidTreeNodes.add(resultRapidTreeNode);
        }
        return resultRapidTreeNodes;
    }
}
