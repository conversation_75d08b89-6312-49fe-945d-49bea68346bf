package com.dragonsoft.cicada.datacenter.modules.firstpage.share.controller;

import cn.hutool.core.util.StrUtil;
import com.code.common.paging.PageInfo;
import com.code.common.utils.R;
import com.code.common.utils.assertion.Assert;
import com.code.metaservice.datawarehouse.model.DatasetTreeModel;
import com.dragoninfo.dfw.bean.Result;
import com.dragoninfo.dfw.entity.TSysAuthObj;
import com.dragonsoft.cicada.datacenter.modules.dataplan.servermanage.service.IServiceManagementService;
import com.dragonsoft.cicada.datacenter.modules.firstpage.share.service.ShareService;
import com.dragonsoft.cicada.datacenter.modules.firstpage.share.vo.*;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@CrossOrigin
@RequestMapping("/firstPageShare")
public class FirstPageShareController {

    @Resource
    private ShareService shareService;

    @Autowired
    private IServiceManagementService serviceManagementService;


    @PostMapping("/fromShare")
    @ApiOperation(value = "首页来自分享")
    public Result fromShare(@RequestBody ShareVo fromShareVo, HttpServletRequest request){
        try {
            String userId = (String) request.getSession().getAttribute("userId");
            PageInfo shares = shareService.getSharesByCondition(fromShareVo, userId);
            return Result.toResult(R.ok(shares));
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return Result.toResult(R.error(e.getMessage()));
        }
    }

    @GetMapping("/getUsersOrRoles")
    @ApiOperation(value = "获取其他用户或角色信息")
    public Result getUsersOrRoles(HttpSession session,String objType){
        try {
            String userId = (String) session.getAttribute("userId");
            List<ResultUserVo> otherUsers = shareService.getOtherUsers(userId, objType);
            return Result.toResult(R.ok(otherUsers));
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return Result.toResult(R.error(e.getMessage()));
        }
    }

    @PostMapping("/myShares")
    @ApiOperation(value = "我分享的")
    public Result myShares(HttpSession session, @RequestBody ShareVo myShareVo){
        try {
            String userId = (String) session.getAttribute("userId");
            PageInfo myShares = shareService.getMyShares(myShareVo,userId);
            return Result.toResult(R.ok(myShares));
        } catch (Exception e) {
            log.error(e.getMessage());
            log.error(e.getMessage(),e);
            return Result.toResult(R.error(e.getMessage()));
        }
    }

    @PostMapping("/searchShareResource")
    @ApiOperation(value = "我的资源")
    public Result searchShareResource(HttpSession session,@RequestBody ShareVo shareVo){
        try {
            String userId = (String) session.getAttribute("userId");
            List<ResultResource> resourceByCondition = shareService.getResourceByCondition(shareVo, userId);
            return Result.toResult(R.ok(resourceByCondition));
        } catch (Exception e) {
            log.error(e.getMessage());
            log.error(e.getMessage(),e);
            return Result.toResult(R.error(e.getMessage()));
        }
    }

    @GetMapping("/myDataSet")
    @ApiOperation(value = "我的资源数据集")
    public Result myDataSet(HttpSession session){
        try {
            String userId = (String) session.getAttribute("userId");
            List<DatasetTreeModel> myDataSetResource = shareService.getMyDataSetResource(userId);
            return Result.toResult(R.ok(myDataSetResource));
        } catch (Exception e) {
            log.error(e.getMessage());
            log.error(e.getMessage(),e);
            return Result.toResult(R.error(e.getMessage()));
        }
    }


    @PostMapping("/addShareManagement")
    @ApiOperation(value = "分享管理添加分享")
    public Result addShareManagement(HttpSession session,@RequestBody ShareVo shareVo){
        try {
            String userId = (String) session.getAttribute("userId");
            shareService.addShares(shareVo,userId);
            return Result.toResult(R.ok());
        } catch (Exception e) {
            log.error(e.getMessage());
            log.error(e.getMessage(),e);
            return Result.toResult(R.error(e.getMessage()));
        }
    }

    @PostMapping("/cancelShare")
    @ApiOperation(value = "分享管理取消分享")
    public Result cancelShare(HttpSession session,@RequestBody ShareVo shareVo){
        try {
            shareService.cancelShare(shareVo);
            return Result.toResult(R.ok());
        } catch (Exception e) {
            log.error(e.getMessage());
            log.error(e.getMessage(),e);
            return Result.toResult(R.error(e.getMessage()));
        }
    }

    @GetMapping("/getObjsByResource")
    @ApiOperation(value = "根据资源id寻找用户")
    public Result getObjsByResource(String resourceId){
        try {
            List<TSysAuthObj> objsByResourceId = shareService.getObjsByResourceId(resourceId);
            return Result.toResult(R.ok(objsByResourceId));
        } catch (Exception e) {
            log.error(e.getMessage());
            log.error(e.getMessage(),e);
            return Result.toResult(R.error(e.getMessage()));
        }
    }

    @GetMapping("/getMyModelService")
    public Result getMyModelService(String serviceType,HttpSession session){
        try {
            String userId = (String) session.getAttribute("userId");
            if (StrUtil.isEmpty(userId)){
                Assert.fail("请先登录！");
            }
            List<Map<String, Object>> mapList = serviceManagementService.queryMyServiceByType(serviceType, userId);
            return Result.toResult(R.ok(mapList));
        } catch (Exception e) {
            log.error(e.getMessage());
            log.error(e.getMessage(),e);
            return Result.toResult(R.error(e.getMessage()));
        }
    }

    @GetMapping("/getResourceTypeDir")
    public Result getResourceTypeDir(Boolean isModelShare){
        try {
            List<ResourceTypeVo> vos = new ArrayList<>();
            ResourceTypeVo dataSet = new ResourceTypeVo();
            dataSet.setName(ResourceTypeEnum.DATA_SET.getChName());
            dataSet.setCode(ResourceTypeEnum.DATA_SET.getName());
            dataSet.setFuncCode(ResourceTypeEnum.DATA_SET.getCode());
            dataSet.setIsDir(false);
            vos.add(dataSet);

            ResourceTypeVo dashboard = new ResourceTypeVo();
            dashboard.setName(ResourceTypeEnum.DASHBOARD.getChName());
            dashboard.setCode(ResourceTypeEnum.DASHBOARD.getName());
            dashboard.setFuncCode(ResourceTypeEnum.DASHBOARD.getCode());
            dashboard.setIsDir(false);
            vos.add(dashboard);

            ResourceTypeVo service = new ResourceTypeVo();
            service.setName("服务");
            service.setIsDir(true);
            List<ResourceTypeVo> serviceChildren = new ArrayList<>();
            for (ResourceTypeEnum value : ResourceTypeEnum.values()) {
                if (value == ResourceTypeEnum.DATA_SET || value == ResourceTypeEnum.DASHBOARD) {
                    continue;
                }
                if (isModelShare && (value == ResourceTypeEnum.DATA_SERVICE || value == ResourceTypeEnum.AI_SERVICE)) {
                    continue;
                }
                ResourceTypeVo serviceType = new ResourceTypeVo();
                serviceType.setName(value.getChName());
                serviceType.setCode(value.getName());
                serviceType.setFuncCode(value.getCode());
                serviceType.setIsDir(false);
                serviceChildren.add(serviceType);
            }
            service.setChildren(serviceChildren);
            vos.add(service);

            return Result.toResult(R.ok(vos));
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return Result.toResult(R.error(e.getMessage()));
        }
    }
}
