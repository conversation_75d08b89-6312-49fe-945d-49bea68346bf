package com.dragonsoft.cicada.datacenter;

import com.code.common.encrypt.KeyEncryptor;
import com.dragonsoft.cicada.datacenter.common.config.PropertiesListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.context.ConfigurableApplicationContext;

import java.net.NetworkInterface;
import java.net.SocketException;
import java.util.Enumeration;

@Slf4j
public class DataCenterWebApplication_bak {

    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(DataCenterWebApplication_bak.class);
        application.addListeners(new PropertiesListener("mlsql-engine-config.properties"));
        //SpringApplication.run(DataCenterWebApplication.class, args);
        ConfigurableApplicationContext context = application.run(args);

        // 根据配置文件的信息决定是否停止应用
        boolean checkFlag = checkLicence(context.getEnvironment().getProperty("licencekey"));
        if(!checkFlag){
            System.out.println("=========================证书错误，启动失败！");
            SpringApplication.exit(context);
        }

    }

    public static boolean checkLicence(String licenceKey){
        boolean rev = false;
        try {
            Enumeration<NetworkInterface> networkInterfaces = NetworkInterface.getNetworkInterfaces();

            while (networkInterfaces.hasMoreElements()&&!rev) {
                NetworkInterface networkInterface = networkInterfaces.nextElement();
                byte[] mac = networkInterface.getHardwareAddress();
                if (mac != null) {
                    StringBuilder sb = new StringBuilder();
                    for (int i = 0; i < mac.length; i++) {
                        sb.append(String.format("%02X%s", mac[i], (i < mac.length - 1) ? "-" : ""));
                    }
                    if(sb.toString().equalsIgnoreCase(KeyEncryptor.decrypt(licenceKey))){
                        rev=true;
                    }
                }
            }
        } catch (SocketException e) {
            log.error(e.getMessage(),e);
        }

        return rev;
    }
}
