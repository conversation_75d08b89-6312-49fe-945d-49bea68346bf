package com.dragonsoft.cicada.datacenter;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@CrossOrigin
@RequestMapping("/test")
public class TestController {

    @Value("${hdfsUploadFile.defaultLocation}")
    String hdfsUploadFile;

    @RequestMapping("hdfsUploadFile")
    public String hdfsUploadFile() {
        return hdfsUploadFile;
    }


}