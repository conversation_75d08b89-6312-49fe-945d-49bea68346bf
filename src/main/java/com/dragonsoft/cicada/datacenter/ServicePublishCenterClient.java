package com.dragonsoft.cicada.datacenter;

import com.code.common.mist.service.structure.model.ServiceClassMeta;
import lombok.Data;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

// 填入注册中心中的应用名, 也就是要调用的微服务的应用名
// 在eureka页面中可以找到
@FeignClient("service-publish-center")
public interface ServicePublishCenterClient {

    @RequestMapping("publish")
    String publish(@RequestBody ServicePublishVo servicePublishVo);

    @RequestMapping(path = "publishWithoutMeta")
    String publishWithoutMeta(@RequestBody ServicePublishVo vo);

    @RequestMapping("update")
    void update(@RequestBody ServicePublishVo servicePublishVo);

    @RequestMapping(path = "updateMeta")
    void updateMeta(@RequestBody ServicePublishVo vo);

    @RequestMapping("uninstall")
    void uninstall(@RequestParam("serviceId") String serviceId);

    @RequestMapping("offlineExport")
    void offlineExport(@RequestParam("serviceId") String serviceId);

    @RequestMapping("export")
    void export(@RequestParam("serviceId") String serviceId);

    @Data
    class ServicePublishVo {
        private ServiceClassMeta serviceClassMeta;
        private String filePath;
    }
}