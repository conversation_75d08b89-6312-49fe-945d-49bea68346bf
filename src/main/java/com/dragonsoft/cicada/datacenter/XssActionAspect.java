package com.dragonsoft.cicada.datacenter;

import com.dragonsoft.cicada.datacenter.modules.filter.XssAndSqlHttpServletRequestWrapper;
import org.aspectj.lang.annotation.Before;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

//@Aspect
@Component
public class XssActionAspect {
    private final Logger logger = LoggerFactory.getLogger(XssActionAspect.class);

    @Before(value = "execution(* com.dragonsoft.cicada.datacenter.*.*.controller..*.*(..))||" +
            "execution(* com.dragonsoft.cicada.datacenter.*.*.*.controller..*.*(..))||" +
            "execution(* com.dragoninfo.dfw.*.login..*.*(..))||" +
            "execution(* com.code.thirdplugin.controller..*.*(..))||" +
            "execution(* com.code.thirdplugin.*.*.controller..*.*(..))||" +
            "execution(* com.code.thirdplugin.*.*.*.controller..*.*(..))")
    public void service() throws IOException {
        HttpServletRequest servletRequest = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        XssAndSqlHttpServletRequestWrapper xssRequest = null;
        if (servletRequest instanceof HttpServletRequest) {
           // xssRequest = new XssAndSqlHttpServletRequestWrapper(servletRequest, whiteStr);
        }
        if (!xssRequest.checkSession(servletRequest.getMethod())) {
            throw new RuntimeException("该用户未登录或不存在，拒绝访问!");
        }
        assert xssRequest != null;
        if (xssRequest.checkParameter() || xssRequest.checkReferer()) {
            throw new RuntimeException("您所访问的页面请求中有违反安全规则元素存在，拒绝访问!");
        }
        try {
            logger.info("切面：" + servletRequest.getSession(false).getId());
            logger.info("接口方法：" + servletRequest.getServletPath());
        } catch (Exception e) {
            logger.info("该用户未登录、被禁用或所访问的页面请求中有违反安全规则元素存在");
        }

    }
}