#!/usr/bin/env bash
echo '-------------------------------------------------- dataCenter-web-ui application --------------------------------------------------------'
ROOT_PATH=$(cd $(dirname $0); pwd)
ROOT_UI_PATH=${ROOT_PATH}'/web-ui'
PID_PATH=${ROOT_PATH}'/pid'
LOG_PATH=${ROOT_PATH}'/log'
LOG_OUTPUT_PATH=${LOG_PATH}'/output'


export JAVA_OPTS="${JAVA_OPTS} -Dcom.sun.management.jmxremote"
export JAVA_OPTS="${JAVA_OPTS} -Dcom.sun.management.jmxremote.port=9001"
export JAVA_OPTS="${JAVA_OPTS} -Djava.rmi.server.hostname=x.x.x.x"
export JAVA_OPTS="${JAVA_OPTS} -Dcom.sun.management.jmxremote.ssl=false"
export JAVA_OPTS="${JAVA_OPTS} -Dcom.sun.management.jmxremote.authenticate=false"

TomcatID=$(ps -ef |grep tomcat |grep -w '/home/<USER>/dataCenter-web/web-ui'|grep -v 'grep'|awk '{print $2}')
if  [[ ! -n ${TomcatID} ]] ;then
    echo "tomcat is not running!"
else
    sh ${ROOT_UI_PATH}'/bin/shutdown.sh'
fi
sleep 2
if [[ -d ${ROOT_UI_PATH}'/webapps/dataCenter' ]]; then
    rm -rf ${ROOT_UI_PATH}'/webapps/ROOT'
    mv ${ROOT_UI_PATH}'/webapps/dataCenter' ${ROOT_UI_PATH}'/webapps/ROOT'
    cp ${ROOT_UI_PATH}'/WEB-INF'
fi
sh ${ROOT_UI_PATH}'/bin/startup.sh'
sleep 3
echo 'web-ui is running!'

echo '-------------------------------------------------------- dataCenter-web application ------------------------------------------------------------'
if [[ ! -f ${PID_PATH} ]]; then
     touch ${PID_PATH}
     echo 'pid file create success'
    else
     if [[ -s ${PID_PATH} ]]; then
         echo 'stop dataCenter application at pid : '$(cat ${PID_PATH})
         kill -9 $(cat ${PID_PATH})
        else
         echo 'dataCenter application is not running!'
     fi
fi

if [[ ! -d ${LOG_PATH} ]]; then
    mkdir ${LOG_PATH}
fi
echo '' > ${LOG_OUTPUT_PATH}
chmod -R 755 ./*

nohup java -jar cicada-dataCenter-web-3.2.0.RELEASE.jar > ${LOG_OUTPUT_PATH}  2>&1 &

echo $! > ${PID_PATH}
echo 'start dataCenter application at pid : '$(cat ${PID_PATH})

