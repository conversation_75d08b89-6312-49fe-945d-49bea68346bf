#应用代码（接入DIDS2的系统,需要在DIDS-Manage中注册，并得到一个应用代码,此处必填）
dids2.appCode=010000000000115

#是否使用单点登录统一验证
dids2.useSSO=true

#统一登录页面地址（若使用SSO登录，此处需填写DIDS-Server认证中心统一登录页面地址）http://192.168.23.130
dids2.SSOLoginUrl=http://192.168.23.86:8787/didsserver/login

#登录有效性校验地址（若使用SSO登录，此处需填写DIDS-Server认证中心统一登录页面地址）
dids2.SSOValidateUrl=http://192.168.23.86:8787/didsserver/serviceValidate

#本应用SSO初始化方法地址（若使用SSO登录，需要在本应用添加SSO初始化方法，此处为该方法访问地址）
dids2.SSOServiceUrl=http://*************:8888/dataCenter/didslogin/ssoSysInit

#本应用退出需要跳转DIDS的请求地址，需要加上userNo= 不然dids的session的清不掉
dids2.SSOLoginOutUrl=http://192.168.23.86:8787/didsserver/logout?userNo=

#信息调用方式：可选值1和2，
#1代表使用webservice接口远程向DIDS-Server请求用户信息和权限信息等，默认方式
#2代表使用数据源连接方式直接从数据库中取得用户信息和权限信息等
dids2.accessType=1

#1.webservice访问地址（若信息调用方式使用1webservice服务，此处需要填写DIDS-Server的webservice服务访问url）
dids2.webServiceUrl=http://192.168.23.86:8787/didsserver/webservices/

#2.DIDS数据源（若信息调用方式使用2数据源连接方式，则需要在本应用服务器上配置连接到DIDS数据库的数据源，此处为数据源名）
dids2.jndi=JDBC/DIDS
#过滤选择的操作(0：只过滤指定的地址,1：不过滤指定的地址，2：都不过滤.3：过滤全部。默认选择1）
dids2.filterType=0
#若匹配该地址，则进行过滤（例如XXX.do）

dids2.filterUrl=didslogin/ssoSysInit

#若匹配该地址，则无须过滤（例如XXX.do）
dids2.noFilterUrl=authorizationPage,importAuthorizationFile,license/LicenseManagerServlet,didslogin/licence,didslogin/true

#用于引导URL访问地址访问指定的文件
dids2.welcomeFile=

#用于设置统一登录页面的显示文字（例如：dids2.loginName=文章发布系统）
dids2.loginName=

#用于设置统一登陆后，不同的登录页面（1:窗口登录模式 ；不填代表使用通用的登录页面 ）
#如果是某项目特定的登录页面，则填写相应的登录页面文件名称
dids2.loginPage=

#数据中心前端访问地址
web.ip= http://*************:8080
