<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
                     http://www.springframework.org/schema/beans/spring-beans-3.2.xsd
                     http://www.springframework.org/schema/context
                     http://www.springframework.org/schema/context/spring-context-3.2.xsd"
       default-autowire="byName">
    <!--    全局配置-->

    <context:annotation-config/>
    <bean id="yamlProperties" class="com.dragonsoft.cicada.datacenter.modules.YamlPropertiesFactoryBeanAdaptor">
        <property name="decrypts">
            <set>
                <value>jdbc.datasource.password</value>
                <value>gp.external.password</value>
            </set>
        </property>
        <property name="resources" value="classpath:config-${spring.profiles.active}.yml"/>
    </bean>
    <context:property-placeholder properties-ref="yamlProperties"/>

    <import resource="classpath:application-common-access.xml"/>
    <import resource="classpath:application-dataset-step.xml"/>
    <import resource="classpath:application-dfw.xml"/>
    <import resource="classpath:application-hibernate.xml"/>
    <import resource="classpath:application-mvc.xml"/>
    <import resource="classpath:application-plugins.xml"/>
    <import resource="classpath:application-sync.xml"/>


</beans>