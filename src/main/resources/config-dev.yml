
#--------------------------------
# 配置库的地址
#--------------------------------
jdbc:
  datasource:
    driverClassName: org.postgresql.Driver
    max-total: 2000
    max-idle: 100
    password: 754052535A58
    #url: ***********************************************
    #url: *************************************************
    url: ************************************************
    #url: ***********************************************240920
    username: postgres

#--------------------------------
# 插件目录，这些插件拷到web应用所在的服务器即可
#--------------------------------
plugin:
  base:
    dir: D:\work\extensions\plugin


#--------------------------------
# 模型服务注册中心
#--------------------------------
eureka:
  client:
    service-url:
      defaultZone: http://*************:8761/eureka/


#--------------------------------
# 引擎和调度地址
#--------------------------------
mlsql:
  schedule:
    ## 调度引擎地址
    address: http://*************:8003
  #是否开启多个
  isMultiple: true
  executor:
    ## 执行引擎地址
    address: http://*************:9003
  isCollectionLog: true


#--------------------------------
# 引擎日志es地址，若任务日志需要将spark日志导入过来，需要配置，现可以不配置
#--------------------------------
log:
  log-es:
    version: 6.7.1
    port: 9200
    address: *************
  mlsql:
    index-pattern: mlsql_log_*

#--------------------------------
# HDFS地址与登录名称，用来做文件上传插件存储
#--------------------------------
hdfs:
  ## 此处配置hdfs的ip、port跟用户要跟服务发布中心，服务发布容器的一致
    ##若hdfs开启了认证
    kerberosOpen: false
    defaultFS: hdfs://*************:9000
    hdfsuploadFilePath: hdfs://*************:9000
    #若hdfs开启了认证，以下参数才需设置
    hdfsFilePath: /user/julong/mf/uploadfile/
    kererosFilePath: /home/<USER>
    userPrincipal: <EMAIL>
    userKeyTab: julong.keytab
    kbererosUrl: hdfs://mycluster
    hadoopLoginName: hdfs
    kbererosUrlTest: hdfs://mycluste/user/julong/mf/uploadfile/

#--------------------------------
# 文件上传下载HDFS位置(这里的地址与配置的hdfs地址要一致)
#--------------------------------
hdfsUploadFile:
  defaultLocation: hdfs://*************:9000/user/uploadFiles/
hdfsDownloadFile:
  defaultLocation: hdfs://*************:9000/user/downloadFiles/


#--------------------------------
#  模型服务容器所在的IP端口信息
#--------------------------------
dc:
  publish:
    path: http://***********:2001/service-publish-worker


#--------------------------------
# 帮助文档地址
#--------------------------------
help-doc:
  #后端
  ip: http://*************:8090
  #前端
  ip-front: http://*************:8070


#--------------------------------
# 定时删除gp入库产生的外部表配置 如果输出用到gp库且是用load方式时才用到
# 每天凌晨00:10执行
# 允许配置0个或多个
#--------------------------------
#gp:
#  external:
#    configs:
#      - {
#        url: "************************************************************************************",
#        user: gpadmin,
#        password: 2302000302073D,
#        schema: test22,
#        cronExpression: "0 10 0 * * ? *"
#      }


#--------------------------------
# 服务发布算子位置
#--------------------------------
ai:
  dir:
    #流程建模服务发布地址
    #此处地址与服务发布中心，服务发布容器的plugin.base.dir的地址一致
    basicPath: /hdfs/plugin/
    #流程建模服务发布地址
    aiServicePath: /hyc/test/
    #--------------------------------
    # ai建模调用http接口 ip地址与端口
    #--------------------------------
  model:
    initBuildServiceIp: *************:6002
    startAndSaveServiceIp: *************:6007
    runScriptServiceIp: *************:6008
    evaluateRstIp: *************:6009
    runOpServiceIp: *************:6003
    serviceReleaseIp: *************:6010
    #外网映射ip
    editorWebserviceIp:

