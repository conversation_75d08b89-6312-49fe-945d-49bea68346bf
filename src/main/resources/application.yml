application:
  name: dataCenter-consumer
spring:
  application: ${application.name}
  profiles:
    active: @profile.active@
  main:
    allow-bean-definition-overriding: true
  #-------------------------------
  #模型导入大小限制
  #-------------------------------
  http:
    multipart:
      max-file-size: 100Mb

server:
  context-path: /dataCenter
  port: 8888
  tomcat:
    uri-encoding: UTF-8
    max-threads: 500
    max-connections: 2000
    accept-count: 2000
dataCenter:
  lastVersion: 1.1.0,3.0.0,3.1.0,3.2.0,3.3.0,3.3.1,3.4.0,3.5.0,3.6.0,3.6.1
  #lastVersion: 1.1.0,3.0.0,3.1.0,3.2.0

#--------------------------------
# 服务发布执行耗时
#--------------------------------
service:
  publish:
    time: 60000

#-------------------------------
# 是否开启eureka注册
#-------------------------------
eureka:
  client:
    register-with-eureka: false
    fetch-registry: false
  instance:
    prefer-ip-address: true
    instance-id: ${spring.cloud.client.ip-address}:${server.port}


feign:
  client:
    config:
      default:
        connect-timeout: 10000
        read-timeout: 10000
  hystrix:
    enabled: false


#--------------------------------
# 适配华为云低版本的spark时，对一些不适用的语法糖做了处理
# 现在适配的华为云spark的版本为：2.3.2
# 这边填写使用的版本就行，例如：3.0.0 这个版本为数据中心使用的spark的主版本
#--------------------------------
spark:
  version:
    use: 3.0.0


#--------------------------------
# 增加文件输出插件数量限制
#--------------------------------
fileOutput:
  limit:
    size: 50000

#-------------------------------
# 是否开启xss校验
#-------------------------------
system:
  filterXss: false
  #是否开启管理员角色查看他人空间
  otherSpace:
    toAdmin: true

#-------------------------------
# sql插件运行限制条数
#-------------------------------
script:
  sqlLimit: 10

#-------------------------------
#是否隐藏查看sql的密码
#-------------------------------
getsql:
  hiddle: false

#-------------------------------
#模型导入否删除再导入,默认不删除
#true表示删除从新建
#false表示不删除，做merge
#-------------------------------
catalogResources:
  isDelete: false


