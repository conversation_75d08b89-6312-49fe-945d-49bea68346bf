<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="
           http://www.springframework.org/schema/context
           http://www.springframework.org/schema/context/spring-context-3.2.xsd
           http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd"
       default-autowire="byName">

    <context:annotation-config/>

    <context:component-scan base-package="com.code.common.ruleengine"/>
    <bean id="operatorBeanRegister" class="com.code.common.ruleengine.plugin.register.OperatorBeanRegister"/>

    <context:component-scan base-package="com.code.common.mist.plugin.config.service"
                            scope-resolver="com.fw.spring.sr.SingletonResolver"
                            name-generator="com.fw.spring.ng.ServiceNameGenerator"
                            use-default-filters="false">
        <context:include-filter type="annotation" expression="com.fw.service.annotation.Service"/>
        <context:exclude-filter type="regex"
                                expression="com.code.common.mist.plugin.config.service.impl.BasePluginService"/>
    </context:component-scan>

    <context:component-scan base-package="com.code.etl.plugin"
                            scope-resolver="com.fw.spring.sr.SingletonResolver"
                            name-generator="com.fw.spring.ng.ServiceNameGenerator"
                            use-default-filters="false">
        <context:include-filter type="annotation" expression="com.fw.service.annotation.Service"/>
        <context:include-filter type="annotation" expression="com.fw.service.annotation.Service"/>
        <context:exclude-filter type="regex"
                                expression="com.code.common.mist.plugin.config.service.impl.BasePluginService"/>
    </context:component-scan>

    <bean name="dataCenterInitServlet"
          class="com.dragonsoft.cicada.datacenter.modules.system.init.DataCenterInitServlet" init-method="init">
        <property name="lastVersion" value="${dataCenter.lastVersion}"/>
    </bean>

    <bean id="springBeanRegister" class="com.fw.spring.SpringBeanRegister"/>

    <bean id="beanFactory" depends-on="dataCenterInitServlet" class="com.code.plugin.PluginBeanFactory">
        <property name="pluginRegistry" ref="pluginRegistry"/>
        <property name="springBeanFactory" ref="springBeanFactory"/>
    </bean>

    <bean id="dataOperatorRouter" class="com.code.reaper.dataop.impl.DataOperatorRouterImpl">
        <property name="pluginRegistry" ref="pluginRegistry"/>
    </bean>

    <bean id="dataReaper" class="com.code.reaper.DataReaper" >
        <property name="metaResDataObjService" ref="metaResDataObjService"/>
        <property name="metaDataNormService" ref="metaDataNormService"/>
        <property name="datasourceRouter" ref="dataOperatorRouter"/>
    </bean>

    <bean id="pluginBeanRegister" class="com.code.common.mist.plugin.meta.PluginBeanRegister"/>
    <bean id="typeMappingBeanRegister" class="com.code.plugin.db.TypeMappingBeanRegister"/>
    <bean id="serviceBeanRegister" class="com.fw.service.annotation.ServiceBeanRegister"/>
    <bean id="castMetaDataSourceRegisterMethod" class="com.code.meta.utils.CastMetaDataSourceRegisterMethod"/>
    <bean id="ddlDataSourceBuilderRegisterMethod" class="com.code.meta.ddl.core.DdlDataSourceBuilderRegisterMethod"/>

    <bean id="pluginRegistry" class="com.code.plugin.StrictVersionPluginRegistry">
        <property name="baseDir" value="${plugin.base.dir}"/>
        <property name="pluginDirectory" ref="pluginDirectory"/>
        <property name="beanRegister" ref="springBeanRegister"/>
        <property name="registMethods">
            <set>
                <ref bean="pluginBeanRegister"/>
                <ref bean="typeMappingBeanRegister"/>
                <ref bean="serviceBeanRegister"/>
                <ref bean="syncHandlerRegister"/>
                <ref bean="castMetaDataSourceRegisterMethod"/>
                <ref bean="ddlDataSourceBuilderRegisterMethod"/>
                <ref bean="operatorBeanRegister"/>
            </set>
        </property>
    </bean>
    <bean id="typeMapping" class="com.code.plugin.db.DefTypeMapping">
        <property name="pluginRegistry" ref="pluginRegistry"/>
    </bean>
        <bean id="typeSystemService" class="com.code.metaservice.base.typemapping.impl.TypeSystemServiceImpl" />
        <bean id="logicDataObjService" class="com.code.metaservice.ddl.LogicDataObjServiceImpl"/>
        <bean id="logicDataColumnService" class="com.code.metaservice.ddl.LogicDataColumnServiceImpl"/>
        <bean id="stepRelationService" class="com.code.common.dataset.DataStepRelationOperator"/>
        <bean id="transVariableService" class="com.code.metaservice.variable.TransVariableServiceImpl"/>
    <bean id="transMetaService" class="com.code.metaservice.etl.trans.impl.TransMetaServiceImpl"/>
    <bean id="defaultFunctionLoader" class="com.code.common.ruleengine.aviator.DefaultFunctionLoader"/>
    <bean id="syncHandlerRegister" class="com.code.common.mist.metadata.define.SyncHandlerRegister"/>



    <context:component-scan base-package="com.code.common.hdfs"/>
    <!-- com.code.common.plugin.HDFSPluginDirectory -->
    <!-- com.code.plugin.LocalPluginDirectory -->
    <bean id="pluginDirectory" class="com.code.plugin.LocalPluginDirectory"/>

    <bean id="graphParseHelper" class="com.dragonsoft.cicada.datacenter.modules.modeling.service.impl.GraphParseHelperFactoryBean">
        <property name="beanFactory" ref="beanFactory"/>
        <property name="transMetaService" ref="transMetaService"/>
        <property name="classifierStatService" ref="classifierStatService"/>
        <property name="pluginConfigService" ref="pluginConfigService"/>
        <property name="structuralFeatureService" ref="structuralFeatureService"/>
        <property name="pluginRegistry" ref="pluginRegistry"/>
        <property name="logicDataColumnService" ref="logicDataColumnService"/>
        <property name="pyTaskService" ref="pyTaskService"/>
        <property name="typeSystemService" ref="typeSystemService"/>
        <property name="udfGraphService" ref="udfGraphService"/>
        <property name="serviceInfoService" ref="serviceInfoService"/>
        <property name="logicDataObjService" ref="logicDataObjService"/>
        <property name="stepRelationService" ref="stepRelationService"/>
        <property name="transVariableService" ref="transVariableService"/>
        <property name="serviceMetaService" ref="serviceMetaService"/>
        <property name="serviceParamsService" ref="serviceParamsService"/>

        <property name="pluginOperatorMapping">
            <map>
                <entry key="standardSqlInput" value="com.code.mlsql_2_0_version.load.LoadJDBC"/>
                <entry key="fullTextInput" value="com.code.mlsql_2_0_version.load.LoadElasticSearch"/>
                <entry key="leftOrRightJoinPlugin" value="com.code.mlsql_2_0_version.join.LeftJoin"/>
                <entry key="subtractByKeyPlugin" value="com.code.mlsql_2_0_version.join.AntiJoin"/>
                <entry key="innerJoinPlugin" value="com.code.mlsql_2_0_version.join.InnerJoin"/>
                <entry key="fullJoinPlugin" value="com.code.mlsql_2_0_version.join.FullJoin"/>
                <entry key="collisionPlugin" value="com.code.mlsql_2_0_version.join.Collision"/>
                <entry key="unionJoinPlugin" value="com.code.mlsql_2_0_version.join.UnionAll"/>
                <entry key="dataSortPlugin" value="com.code.mlsql_2_0_version.transform.OrderBy"/>
                <entry key="samplingAndShuntingPlugin" value="com.code.mlsql_2_0_version.transform.Shunt"/>
                <entry key="labelMeta" value="com.code.mlsql_2_0_version.transform.LabelPlugin"/>
                <entry key="standardSqlOutput" value="com.code.mlsql_2_0_version.save.SaveJDBC"/>
                <entry key="fullTextOutput" value="com.code.mlsql_2_0_version.save.SaveElasticSearch"/>
                <entry key="radarChartsPlugin" value="com.code.mlsql_2_0_version.charts.RadarCharts"/>
                <entry key="pieChartsPlugin" value="com.code.mlsql_2_0_version.charts.PieCharts"/>
                <entry key="scatterChartsPlugin" value="com.code.mlsql_2_0_version.charts.ScatterCharts"/>
                <entry key="lineChartsPlugin" value="com.code.mlsql_2_0_version.charts.LineCharts"/>
                <entry key="barChartsPlugin" value="com.code.mlsql_2_0_version.charts.BarCharts"/>
                <entry key="scriptMeta" value="com.code.mlsql_2_0_version.transform.MlSqlScriptTransForm"/>
                <entry key="reducePlugin" value="com.code.mlsql_2_0_version.transform.GroupBy"/>
                <entry key="zipperTablePlugin" value="com.code.mlsql_2_0_version.transform.ZipperTable"/>
                <entry key="serviceOrganization" value="com.code.mlsql_2_0_version.transform.ExpressionExcuterPlugin"/>
                <entry key="rowDenormaliserMeta" value="com.code.mlsql_2_0_version.transform.RowDenormaliserTransForm"/>
                <entry key="fieldFilteringMeta" value="com.code.mlsql_2_0_version.transform.FieldFiltering"/>
                <entry key="fileInputMeta" value="com.code.mlsql_2_0_version.load.LoadFile"/>
                <entry key="fileOutputMeta" value="com.code.mlsql_2_0_version.save.SaveFile"/>
                <entry key="dataDistinctMeta" value="com.code.mlsql_2_0_version.transform.DataDistinct"/>

                <entry key="startTaskPlugin" value="com.code.mlsql_2_0_version.transform.StartTask"/>
                <entry key="conditionFilterPlugin" value="com.code.mlsql_2_0_version.transform.ConditionFilter"/>
                <entry key="personCreatorMeta" value="com.code.mlsql_2_0_version.transform.PersonCreatorPlugin"/>
                <entry key="jsonParsingContent" value="com.code.mlsql_2_0_version.transform.JsonParing"/>
                <entry key="dlwzMapperMeta" value="com.code.mlsql_2_0_version.transform.DlwzMapperOperator"/>
                <entry key="elementLinkMeta" value="com.code.mlsql_2_0_version.transform.ElementLinkPlugin"/>
                <entry key="jwqMapperMeta" value="com.code.mlsql_2_0_version.transform.JwqMapperOperator"/>
                <entry key="effectivePolice" value="com.code.mlsql_2_0_version.transform.EffectivePoliceTransForm"/>
                <entry key="addressCleanMeta" value="com.code.mlsql_2_0_version.transform.AddressCleanOperator"/>
                <entry key="dateZipperTablePlugin" value="com.code.mlsql_2_0_version.transform.ZipperTableOperator"/>
                <entry key="numberZipperTablePlugin"
                       value="com.code.mlsql_2_0_version.transform.NumberZipperTableOperator"/>
                <entry key="normalizationMeta" value="com.code.mlsql_2_0_version.transform.Normalization"/>
                <entry key="serviceInputMeta" value="com.code.mlsql_2_0_version.transform.ServiceRequestOperator"/>
                <entry key="markingTimeMeta" value="com.code.mlsql_2_0_version.transform.MarkingTimePlugin"/>
                <entry key="peerPluginMeta" value="com.code.mlsql_2_0_version.transform.PeerPlugin"/>
                <entry key="objectToJsonMeta" value="com.code.mlsql_2_0_version.transform.ObjectToJson"/>
                <entry key="pyTaskRunnerMeta" value="com.code.mlsql_2_0_version.transform.PyTaskRunner"/>

                <!--3.0-->
                <entry key="cicadaInnerJoinPlugin" value="com.code.mlsql.join.CicadaInnerJoin"/>
                <entry key="cicadaSubtractByKeyPlugin" value="com.code.mlsql.join.CicadaAntiJoin"/>
                <entry key="cicadaCollisionPlugin" value="com.code.mlsql.join.CicadaCollision"/>
                <entry key="cicadaUnionJoinPlugin" value="com.code.mlsql.join.CicadaUnionAll"/>
                <entry key="cicadaJsonParsingContent" value="com.code.mlsql.transform.CicadaJsonParing"/>
                <entry key="cicadaObjectToJsonMeta" value="com.code.mlsql.transform.CicadaObjectToJson"/>
                <entry key="cicadaScriptMeta" value="com.code.mlsql.transform.CicadaMlSqlScriptTransForm"/>
                <entry key="cicadaFileInputMeta" value="com.code.mlsql.load.CicadaLoadFile"/>
                <entry key="cicadaStandardSqlOutput" value="com.code.mlsql.save.SaveJDBC"/>
                <entry key="cicadaZipperTablePlugin" value="com.code.mlsql.transform.ZipperTable"/>

                <entry key="cicadaDateCicadaZipperTablePlugin" value="com.code.mlsql.transform.ZipperTableOperator"/>
                <entry key="cicadaNumberCicadaZipperTablePlugin"
                       value="com.code.mlsql.transform.NumberZipperTableOperator"/>
                <entry key="cicadaDataSortPlugin" value="com.code.mlsql.transform.OrderBy"/>
                <entry key="cicadaConditionFilterPlugin" value="com.code.mlsql.transform.CicadaConditionFilter"/>
                <entry key="cicadaReducePlugin" value="com.code.mlsql.transform.CicadaGroupBy"/>

                <entry key="cicadaFileInputMeta" value="com.code.mlsql.load.CicadaLoadFile"/>
                <entry key="cicadaFieldsSettings" value="com.code.mlsql.transform.CicadaFieldsSettingsOperator"/>
                <entry key="cicadaDataDistinctMeta" value="com.code.mlsql.transform.DataDistinct"/>
                <entry key="cicadaLabelMeta" value="com.code.mlsql.transform.CicadaLabelPlugin"/>
                <entry key="cicadaJwqMapperMeta" value="com.code.mlsql.transform.JwqMapperOperator"/>
                <entry key="cicadaEffectivePolice" value="com.code.mlsql.transform.EffectivePoliceTransForm"/>
                <entry key="cicadaFieldFilteringMeta" value="com.code.mlsql.transform.CicadaFieldFiltering"/>
                <entry key="cicadaFullTextOutput" value="com.code.mlsql.save.SaveElasticSearch"/>

                <entry key="cicadaServiceInputMeta" value="com.code.mlsql.transform.ServiceRequestOperator"/>
                <entry key="cicadaRowDenormaliserMeta"
                       value="com.code.mlsql.transform.RowDenormaliserTransForm"/>
                <entry key="cicadaPyTaskRunnerMeta" value="com.code.mlsql.transform.PyTaskRunner"/>
                <entry key="cicadaNormalizationMeta" value="com.code.mlsql.transform.CicadaNormalization"/>
                <entry key="cicadaStandardSqlInput" value="com.code.mlsql.load.LoadJDBC"/>
                <entry key="cicadaServiceOrganization" value="com.code.mlsql.transform.ExpressionExcuterPlugin"/>
                <entry key="cicadaMarkingTimeMeta" value="com.code.mlsql.transform.MarkingTimePlugin"/>
                <entry key="cicadaFileOutputMeta" value="com.code.mlsql.save.CicadaSaveFile"/>
                <entry key="cicadaMetaServiceInput" value="com.code.mlsql.load.CicadaServiceLoadJson"/>
                <entry key="cicadaMetaServiceOutput" value="com.code.mlsql.transform.CicadaServiceOutput"/>
                <entry key="cicadaModelServiceMeta" value="com.code.mlsql.transform.CicadaModelService"/>
                <entry key="cicadaPeerContentMeta" value="com.code.mlsql.transform.Peer"/>
                <entry key="cicadaManyJoinMeta" value="com.code.mlsql.join.manyjoin.CicadaManyJoin"/>
                <entry key="cicadaAddFieldMeta" value="com.code.mlsql.transform.CicadaAddField"/>
                <entry key="cicadaAiModelService" value="com.code.mlsql.modelservice.impl.CicadaAiModelServiceOperator"/>
                <entry key="cicadaAnalysisModelService" value="com.code.mlsql.modelservice.impl.CicadaAnalysisModelServiceOperator"/>
                <entry key="cicadaCompareServiceModelService" value="com.code.mlsql.modelservice.impl.CicadaCompareServiceModelServiceOperator"/>
                <entry key="cicadaDataCollisionModelService" value="com.code.mlsql.modelservice.impl.CicadaDataCollisionModelServiceOperator"/>
                <entry key="cicadaInfoVerificationModelService" value="com.code.mlsql.modelservice.impl.CicadaInfoVerificationModelServiceOperator"/>
                <entry key="cicadaMetaServiceCheckOutPut" value="com.code.mlsql.transform.CicadaServiceCheckOutPut"/>
                <entry key="cicadaCodeTableConversion" value="com.code.mlsql.transform.CicadaCodeConversion"/>
                <entry key="cicadaInfoCheckModelService" value="com.code.mlsql.modelservice.impl.CicadaInfoCheckModelServiceOperator"/>

                <entry key="cicadaKafkaInputMeta" value="com.code.cicada.thirdplugin.kafka.input.meta.CicadaKafkaInputMeta"/>
                <entry key="cicadaKafkaOutputMeta" value="com.code.cicada.thirdplugin.kafka.output.meta.CicadaKafkaOutputMeta"/>
                <entry key="cicadaSpecialBusinessMeta" value="com.code.cicada.thirdplugin.special.meta.CicadaSpecialBusinessMeta"/>
                <entry key="cicadaFieldsSortMeta" value="com.code.mlsql.transform.CicadaFieldsSort"/>
               <!-- <entry key="analysisResultLibraryOutPutMeta" value="com.code.thirdplugin.cicada.sql.meta.meta.output.AnalysisResultLibraryOutPutMeta"/>-->
                <entry key="analysisResultLibraryOutPutMeta" value="com.code.mlsql.save.SaveJDBC"/>


            </map>
        </property>
    </bean>
</beans>
