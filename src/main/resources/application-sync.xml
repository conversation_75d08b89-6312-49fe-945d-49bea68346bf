<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	   xsi:schemaLocation="http://www.springframework.org/schema/beans
                     http://www.springframework.org/schema/beans/spring-beans-3.0.xsd"
	   default-autowire="byName">

	<!-- ORACLE  同步组件配置      START  -->
	<bean id="oracleResProvider" class="com.code.metaservice.res.structured.rdb.sync.oracle.OracleResProviderFactroy" scope="prototype"/>
	<bean id="oracleSchemaHandler" class="com.code.metaservice.res.structured.rdb.sync.oracle.OracleSchemaHandler" scope="prototype"/>
	<bean id="oracleTableHandler" class="com.code.metaservice.res.structured.rdb.sync.oracle.OracleTableHandler" scope="prototype"/>
	<bean id="oracleViewHandler" class="com.code.metaservice.res.structured.rdb.sync.oracle.OracleViewHandler" scope="prototype"/>
	<bean id="oracleColumnHandler" class="com.code.metaservice.res.structured.rdb.sync.oracle.OracleColumnHandler"  scope="prototype"/>
	<bean id="oracleUniqueKeyHandler" class="com.code.metaservice.res.structured.rdb.sync.oracle.OracleRdbUniqueKeyHandler" scope="prototype"/>
	<bean id="oraclePartitionHandler" class="com.code.metaservice.res.structured.rdb.sync.oracle.OraclePartitionHandler" scope="prototype"/>

	<bean id="oracleColumnSyncComparator" class="com.code.metaservice.res.sync.ResSyncComparator" scope="prototype">
		<property name="syncHandler" ref="oracleColumnHandler"/>
	</bean>

	<bean id="oracleUniqueKeySyncComparator" class="com.code.metaservice.res.sync.ResSyncComparator" scope="prototype">
		<property name="syncHandler" ref="oracleUniqueKeyHandler"/>
	</bean>

	<bean id="oraclePartitionSyncComparator" class="com.code.metaservice.res.sync.ResSyncComparator" scope="prototype">
		<property name="syncHandler" ref="oraclePartitionHandler"/>
	</bean>

	<bean id="oracleTableSyncComparator" class="com.code.metaservice.res.sync.ResSyncComparator" scope="prototype">
		<property name="syncHandler" ref="oracleTableHandler"/>
		<property name="childrenSyncHandler">
			<map>
				<entry key-ref="oracleColumnHandler" value-ref="oracleColumnSyncComparator" />
				<entry key-ref="oracleUniqueKeyHandler" value-ref="oracleUniqueKeySyncComparator" />
				<entry key-ref="oraclePartitionHandler" value-ref="oraclePartitionSyncComparator" />
			</map>
		</property>
	</bean>
	<bean id="oracleViewSyncComparator" class="com.code.metaservice.res.sync.ResSyncComparator" scope="prototype">
		<property name="syncHandler" ref="oracleViewHandler"/>
		<property name="childrenSyncHandler">
			<map>
				<entry key-ref="oracleColumnHandler" value-ref="oracleColumnSyncComparator" />
			</map>
		</property>
	</bean>
	<bean id="oracleSchemaSyncComparator" class="com.code.metaservice.res.sync.ResSyncComparator" scope="prototype">
		<property name="syncHandler" ref="oracleSchemaHandler" />
		<property name="childrenSyncHandler">
			<map>
				<entry key-ref="oracleTableHandler" value-ref="oracleTableSyncComparator" />
				<entry key-ref="oracleViewHandler" value-ref="oracleViewSyncComparator" />
			</map>
		</property>
	</bean>
	<bean id="oracleColumnConstructor" class="com.code.metaservice.res.sync.SyncResultConstructor" scope="prototype">
		<property name="syncHandler" ref="oracleColumnHandler"/>
	</bean>
	<bean id="oracleUniqueKeyConstructor" class="com.code.metaservice.res.sync.SyncResultConstructor" scope="prototype">
		<property name="syncHandler" ref="oracleUniqueKeyHandler"/>
	</bean>
	<bean id="oraclePartitionConstructor" class="com.code.metaservice.res.sync.SyncResultConstructor" scope="prototype">
		<property name="syncHandler" ref="oraclePartitionHandler"/>
	</bean>
	<bean id="oracleTableConstructor" class="com.code.metaservice.res.sync.SyncResultConstructor" scope="prototype">
		<property name="syncHandler" ref="oracleTableHandler"/>
		<property name="childSyncResultConstructor">
			<map>
				<entry key-ref="oracleColumnHandler" value-ref="oracleColumnConstructor"/>
				<entry key-ref="oracleUniqueKeyHandler" value-ref="oracleUniqueKeyConstructor"/>
				<entry key-ref="oraclePartitionHandler" value-ref="oraclePartitionConstructor"/>
			</map>
		</property>
	</bean>
	<!-- ORACLE  同步组件配置      END  -->

	<!--PostgreSql  同步组件配置      START  -->
	<bean id="postgreSqlResProvider" class="com.code.metaservice.res.structured.rdb.sync.postgresql.PostgreSqlResProviderFactory" scope="prototype"/>
	<bean id="postgreSqlSchemaHandler" class="com.code.metaservice.res.structured.rdb.sync.postgresql.PostgreSqlSchemaHandler" scope="prototype"/>
	<bean id="postgreSqlTableHandler" class="com.code.metaservice.res.structured.rdb.sync.postgresql.PostgreSqlTableHandler" scope="prototype"/>
	<bean id="postgreSqlViewHandler" class="com.code.metaservice.res.structured.rdb.sync.postgresql.PostgreSqlViewHandler" scope="prototype"/>
	<bean id="postgreSqlColumnHandler" class="com.code.metaservice.res.structured.rdb.sync.postgresql.PostgreSqlColumnHandler" scope="prototype"/>
	<bean id="postgreSqlUniqueKeyHandler" class="com.code.metaservice.res.structured.rdb.sync.postgresql.PostgreSqlRdbUniqueKeyHandler" scope="prototype"/>

	<bean id="postgreSqlUniqueKeySyncComparator" class="com.code.metaservice.res.sync.ResSyncComparator" scope="prototype">
		<property name="syncHandler" ref="postgreSqlUniqueKeyHandler" />
	</bean>
	<bean id="postgreSqlColumnSyncComparator" class="com.code.metaservice.res.sync.ResSyncComparator" scope="prototype">
		<property name="syncHandler" ref="postgreSqlColumnHandler" />
	</bean>
	<bean id="postgreSqlTableSyncComparator" class="com.code.metaservice.res.sync.ResSyncComparator" scope="prototype">
		<property name="syncHandler" ref="postgreSqlTableHandler" />
		<property name="childrenSyncHandler">
			<map>
				<entry key-ref="postgreSqlColumnHandler" value-ref="postgreSqlColumnSyncComparator" />
				<entry key-ref="postgreSqlUniqueKeyHandler" value-ref="postgreSqlUniqueKeySyncComparator" />
			</map>
		</property>
	</bean>
	<bean id="postgreSqlViewSyncComparator" class="com.code.metaservice.res.sync.ResSyncComparator" scope="prototype">
		<property name="syncHandler" ref="postgreSqlViewHandler" />
		<property name="childrenSyncHandler">
			<map>
				<entry key-ref="postgreSqlColumnHandler" value-ref="postgreSqlColumnSyncComparator" />
			</map>
		</property>
	</bean>

	<bean id="postgreSqlSchemaSyncComparator" class="com.code.metaservice.res.sync.ResSyncComparator" scope="prototype">
		<property name="syncHandler" ref="postgreSqlSchemaHandler" />
		<property name="childrenSyncHandler">
			<map>
				<entry key-ref="postgreSqlTableHandler" value-ref="postgreSqlTableSyncComparator" />
				<entry key-ref="postgreSqlViewHandler" value-ref="postgreSqlViewSyncComparator" />
			</map>
		</property>
		<property name="resProvider" ref="postgreSqlResProvider"/>
	</bean>

	<bean id="postgreSqlColumnConstructor" class="com.code.metaservice.res.sync.SyncResultConstructor" scope="prototype">
		<property name="syncHandler" ref="postgreSqlColumnHandler"/>
	</bean>
	<bean id="postgreSqlUniqueKeyConstructor" class="com.code.metaservice.res.sync.SyncResultConstructor" scope="prototype">
		<property name="syncHandler" ref="postgreSqlUniqueKeyHandler"/>
	</bean>
	<bean id="postgreSqlTableConstructor" class="com.code.metaservice.res.sync.SyncResultConstructor" scope="prototype">
		<property name="syncHandler" ref="postgreSqlTableHandler"/>
		<property name="childSyncResultConstructor">
			<map>
				<entry key-ref="postgreSqlColumnHandler" value-ref="postgreSqlColumnConstructor"/>
				<entry key-ref="postgreSqlUniqueKeyHandler" value-ref="postgreSqlUniqueKeyConstructor"/>
			</map>
		</property>
		<property name="resProvider" ref="postgreSqlResProvider"/>
	</bean>
	<!-- PostgreSql  同步组件配置      END  -->



	<!-- Mysql 同步组件配置 start-->
	<bean id="mysqlResProvider" class="com.code.metaservice.res.structured.rdb.sync.mysql.MySQLResProviderFactory" scope="prototype"/>
	<bean id="mysqlSchemaHandler" class="com.code.metaservice.res.structured.rdb.sync.mysql.MySQLSchemaHandler" scope="prototype"/>
	<bean id="mysqlTableHandler" class="com.code.metaservice.res.structured.rdb.sync.mysql.MySQLTableHandler" scope="prototype"/>
	<bean id="mysqlViewHandler" class="com.code.metaservice.res.structured.rdb.sync.mysql.MySQLViewHandler" scope="prototype"/>
	<bean id="mysqlColumnHandler" class="com.code.metaservice.res.structured.rdb.sync.mysql.MySQLColumnHandler" scope="prototype"/>
	<bean id="mysqlUniqueKeyHandler" class="com.code.metaservice.res.structured.rdb.sync.mysql.MySQLRdbUniqueKeyHandler" scope="prototype"/>

	<bean id="mysqlUniqueKeySyncComparator" class="com.code.metaservice.res.sync.ResSyncComparator" scope="prototype">
		<property name="syncHandler" ref="mysqlUniqueKeyHandler" />
	</bean>
	<bean id="mysqlColumnSyncComparator" class="com.code.metaservice.res.sync.ResSyncComparator" scope="prototype">
		<property name="syncHandler" ref="mysqlColumnHandler" />
	</bean>
	<bean id="mysqlTableSyncComparator" class="com.code.metaservice.res.sync.ResSyncComparator" scope="prototype">
		<property name="syncHandler" ref="mysqlTableHandler" />
		<property name="childrenSyncHandler">
			<map>
				<entry key-ref="mysqlColumnHandler" value-ref="mysqlColumnSyncComparator" />
				<entry key-ref="mysqlUniqueKeyHandler" value-ref="mysqlUniqueKeySyncComparator" />
			</map>
		</property>
	</bean>
	<bean id="mysqlViewSyncComparator" class="com.code.metaservice.res.sync.ResSyncComparator" scope="prototype">
		<property name="syncHandler" ref="mysqlViewHandler" />
		<property name="childrenSyncHandler">
			<map>
				<entry key-ref="mysqlColumnHandler" value-ref="mysqlColumnSyncComparator" />
			</map>
		</property>
	</bean>

	<bean id="mysqlSchemaSyncComparator" class="com.code.metaservice.res.sync.ResSyncComparator" scope="prototype">
		<property name="syncHandler" ref="mysqlSchemaHandler" />
		<property name="childrenSyncHandler">
			<map>
				<entry key-ref="mysqlTableHandler" value-ref="mysqlTableSyncComparator" />
				<entry key-ref="mysqlViewHandler" value-ref="mysqlViewSyncComparator" />
			</map>
		</property>
		<property name="resProvider" ref="mysqlResProvider"/>
	</bean>

	<bean id="mysqlColumnConstructor" class="com.code.metaservice.res.sync.SyncResultConstructor" scope="prototype">
		<property name="syncHandler" ref="mysqlColumnHandler"/>
	</bean>
	<bean id="mysqlUniqueKeyConstructor" class="com.code.metaservice.res.sync.SyncResultConstructor" scope="prototype">
		<property name="syncHandler" ref="mysqlUniqueKeyHandler"/>
	</bean>
	<bean id="mysqlTableConstructor" class="com.code.metaservice.res.sync.SyncResultConstructor" scope="prototype">
		<property name="syncHandler" ref="mysqlTableHandler"/>
		<property name="childSyncResultConstructor">
			<map>
				<entry key-ref="mysqlColumnHandler" value-ref="mysqlColumnConstructor"/>
				<entry key-ref="mysqlUniqueKeyHandler" value-ref="mysqlUniqueKeyConstructor"/>
			</map>
		</property>
		<property name="resProvider" ref="mysqlResProvider"/>
	</bean>


	<!-- MYSQL 同步组件配置 end-->



	<!--hwmpp  同步组件配置（复用postgresql）      START  -->
	<bean id="hwmppResProvider" class="com.code.metaservice.res.structured.rdb.sync.postgresql.PostgreSqlResProviderFactory" scope="prototype"/>
	<bean id="hwmppUniqueKeySyncComparator" class="com.code.metaservice.res.sync.ResSyncComparator" scope="prototype">
		<property name="syncHandler" ref="postgreSqlUniqueKeyHandler" />
	</bean>
	<bean id="hwmppColumnSyncComparator" class="com.code.metaservice.res.sync.ResSyncComparator" scope="prototype">
		<property name="syncHandler" ref="postgreSqlColumnHandler" />
	</bean>
	<bean id="hwmppTableSyncComparator" class="com.code.metaservice.res.sync.ResSyncComparator" scope="prototype">
		<property name="syncHandler" ref="postgreSqlTableHandler" />
		<property name="childrenSyncHandler">
			<map>
				<entry key-ref="postgreSqlColumnHandler" value-ref="hwmppColumnSyncComparator" />
				<entry key-ref="postgreSqlUniqueKeyHandler" value-ref="hwmppUniqueKeySyncComparator" />
			</map>
		</property>
	</bean>
	<bean id="hwmppViewSyncComparator" class="com.code.metaservice.res.sync.ResSyncComparator" scope="prototype">
		<property name="syncHandler" ref="postgreSqlViewHandler" />
		<property name="childrenSyncHandler">
			<map>
				<entry key-ref="postgreSqlColumnHandler" value-ref="hwmppColumnSyncComparator" />
			</map>
		</property>
	</bean>

	<bean id="hwmppSchemaSyncComparator" class="com.code.metaservice.res.sync.ResSyncComparator" scope="prototype">
		<property name="syncHandler" ref="postgreSqlSchemaHandler" />
		<property name="childrenSyncHandler">
			<map>
				<entry key-ref="postgreSqlTableHandler" value-ref="hwmppTableSyncComparator" />
				<entry key-ref="postgreSqlViewHandler" value-ref="hwmppViewSyncComparator" />
			</map>
		</property>
		<property name="resProvider" ref="hwmppResProvider"/>
	</bean>

	<bean id="hwmppColumnConstructor" class="com.code.metaservice.res.sync.SyncResultConstructor" scope="prototype">
		<property name="syncHandler" ref="postgreSqlColumnHandler"/>
	</bean>
	<bean id="hwmppUniqueKeyConstructor" class="com.code.metaservice.res.sync.SyncResultConstructor" scope="prototype">
		<property name="syncHandler" ref="postgreSqlUniqueKeyHandler"/>
	</bean>
	<bean id="hwmppTableConstructor" class="com.code.metaservice.res.sync.SyncResultConstructor" scope="prototype">
		<property name="syncHandler" ref="postgreSqlTableHandler"/>
		<property name="childSyncResultConstructor">
			<map>
				<entry key-ref="postgreSqlColumnHandler" value-ref="hwmppColumnConstructor"/>
				<entry key-ref="postgreSqlUniqueKeyHandler" value-ref="hwmppUniqueKeyConstructor"/>
			</map>
		</property>
		<property name="resProvider" ref="hwmppResProvider"/>
	</bean>
	<!-- hwmpp  同步组件配置      END  -->

	<!-- Hive  同步组件配置      START  -->
	<bean id="hiveResProvider" class="com.code.metaservice.res.structured.rdb.sync.hive.HiveResProviderFactory" scope="prototype"/>
	<bean id="hiveResProviderFactory" class="com.code.metaservice.res.structured.rdb.sync.hive.HiveResProviderFactory" scope="prototype"/>
	<bean id="hiveSchemaHandler" class="com.code.metaservice.res.structured.rdb.sync.hive.HiveSchemaHandler" scope="prototype"/>
	<bean id="hiveTableHandler" class="com.code.metaservice.res.structured.rdb.sync.hive.HiveTableHandler" scope="prototype"/>
	<bean id="hiveViewHandler" class="com.code.metaservice.res.structured.rdb.sync.hive.HiveViewHandler" scope="prototype"/>
	<bean id="hiveColumnHandler" class="com.code.metaservice.res.structured.rdb.sync.hive.HiveColumnHandler" scope="prototype"/>
	<bean id="hiveRdbUniqueKeyHandler" class="com.code.metaservice.res.structured.rdb.sync.hive.HiveRdbUniqueKeyHandler" scope="prototype"/>

	<bean id="hiveUniqueKeyHandler" class="com.code.metaservice.res.structured.rdb.sync.hive.HiveRdbUniqueKeyHandler" scope="prototype">
		<property name="resProvider" ref="hiveResProvider" />
	</bean>
	<bean id="hiveUniqueKeySyncComparator" class="com.code.metaservice.res.sync.ResSyncComparator" scope="prototype">
		<property name="syncHandler" ref="hiveRdbUniqueKeyHandler" />
	</bean>
	<bean id="hiveColumnSyncComparator" class="com.code.metaservice.res.sync.ResSyncComparator" scope="prototype">
		<property name="syncHandler" ref="hiveColumnHandler" />
	</bean>
	<bean id="hiveTableSyncComparator" class="com.code.metaservice.res.sync.ResSyncComparator" scope="prototype">
		<property name="syncHandler" ref="hiveTableHandler" />
		<property name="childrenSyncHandler">
			<map>
				<entry key-ref="hiveColumnHandler" value-ref="hiveColumnSyncComparator" />
				<entry key-ref="hiveRdbUniqueKeyHandler" value-ref="hiveUniqueKeySyncComparator" />
			</map>
		</property>
	</bean>
	<bean id="hiveViewSyncComparator" class="com.code.metaservice.res.sync.ResSyncComparator" scope="prototype">
		<property name="syncHandler" ref="hiveViewHandler" />
		<property name="childrenSyncHandler">
			<map>
				<entry key-ref="hiveColumnHandler" value-ref="hiveColumnSyncComparator" />
			</map>
		</property>
	</bean>

	<bean id="hiveSchemaSyncComparator" class="com.code.metaservice.res.sync.ResSyncComparator" scope="prototype">
		<property name="syncHandler" ref="hiveSchemaHandler" />
		<property name="childrenSyncHandler">
			<map>
				<entry key-ref="hiveTableHandler" value-ref="hiveTableSyncComparator" />
				<entry key-ref="hiveViewHandler" value-ref="hiveViewSyncComparator" />
			</map>
		</property>
		<property name="resProvider" ref="hiveResProviderFactory"/>
	</bean>

	<bean id="hiveColumnConstructor" class="com.code.metaservice.res.sync.SyncResultConstructor" scope="prototype">
		<property name="syncHandler" ref="hiveColumnHandler"/>
	</bean>
	<bean id="hiveUniqueKeyConstructor" class="com.code.metaservice.res.sync.SyncResultConstructor" scope="prototype">
		<property name="syncHandler" ref="hiveRdbUniqueKeyHandler"/>
	</bean>
	<bean id="hiveTableConstructor" class="com.code.metaservice.res.sync.SyncResultConstructor" scope="prototype">
		<property name="syncHandler" ref="hiveTableHandler"/>
		<property name="childSyncResultConstructor">
			<map>
				<entry key-ref="hiveColumnHandler" value-ref="hiveColumnConstructor"/>
				<entry key-ref="hiveRdbUniqueKeyHandler" value-ref="hiveUniqueKeyConstructor"/>
			</map>
		</property>
		<property name="resProvider" ref="hiveResProviderFactory"/>
	</bean>
	<!-- Hive  同步组件配置      END  -->

	<!-- Hbase  同步组件配置  -->
	<bean id="hbaseResProvider" class="com.code.metaservice.res.semistructured.hbase.sync.HbaseResProviderFactory" scope="prototype"/>
	<bean id="hbaseInstanceHandler" class="com.code.metaservice.res.semistructured.hbase.sync.HbaseInstanceHandler" scope="prototype"/>
	<bean id="hbaseTableHandler" class="com.code.metaservice.res.semistructured.hbase.sync.HbaseTableHandler" scope="prototype"/>
	<bean id="hbaseCfHandler" class="com.code.metaservice.res.semistructured.hbase.sync.HbaseCfHandler" scope="prototype"/>


	<bean id="hbaseCfSyncComparator" class="com.code.metaservice.res.sync.ResSyncComparator" scope="prototype">
		<property name="syncHandler" ref="hbaseCfHandler" />
	</bean>

	<bean id="hbaseTableSyncComparator" class="com.code.metaservice.res.sync.ResSyncComparator" scope="prototype">
		<property name="syncHandler" ref="hbaseTableHandler" />
		<property name="childrenSyncHandler">
			<map>
				<entry key-ref="hbaseCfHandler" value-ref="hbaseCfSyncComparator" />
			</map>
		</property>
	</bean>

	<bean id="hbaseCfConstructor" class="com.code.metaservice.res.sync.SyncResultConstructor" scope="prototype">
		<property name="syncHandler" ref="hbaseCfHandler"/>
	</bean>

	<bean id="hbaseTableConstructor" class="com.code.metaservice.res.sync.SyncResultConstructor" scope="prototype">
		<property name="syncHandler" ref="hbaseTableHandler"/>
		<property name="childSyncResultConstructor">
			<map>
				<entry key-ref="hbaseCfHandler" value-ref="hbaseCfConstructor"/>
			</map>
		</property>
		<property name="resProvider" ref="hbaseResProvider"/>
	</bean>

	<bean id="hbaseInstanceSyncComparator" class="com.code.metaservice.res.sync.ResSyncComparator" scope="prototype">
		<property name="syncHandler" ref="hbaseInstanceHandler" />
		<property name="childrenSyncHandler">
			<map>
				<entry key-ref="hbaseTableHandler" value-ref="hbaseTableSyncComparator" />
			</map>
		</property>
		<property name="resProvider" ref="hbaseResProvider"/>
	</bean>

	<!-- es  同步组件配置  -->
	<bean id="elasticsearchResProvider" class="com.code.metaservice.res.semistructured.elasticsearch.sync.ElasticsearchResProviderFactory"
		  scope="prototype"/>
	<bean id="elasticsearchInstanceHandler" class="com.code.metaservice.res.semistructured.elasticsearch.sync.ElasticsearchInstanceHandler"
		  scope="prototype"/>
	<bean id="elasticsearchCollectionHandler" class="com.code.metaservice.res.semistructured.elasticsearch.sync.ElasticsearchCollectionHandler"
		  scope="prototype"/>
	<bean id="elasticsearchColumnHandler" class="com.code.metaservice.res.semistructured.elasticsearch.sync.ElasticsearchColumnHandler"
		  scope="prototype"/>

	<bean id="elasticsearchColumnSyncComparator" class="com.code.metaservice.res.sync.ResSyncComparator" scope="prototype">
		<property name="syncHandler" ref="elasticsearchColumnHandler"/>
	</bean>

	<bean id="elasticsearchCollectionSyncComparator" class="com.code.metaservice.res.sync.ResSyncComparator" scope="prototype">
		<property name="syncHandler" ref="elasticsearchCollectionHandler"/>
		<property name="childrenSyncHandler">
			<map>
				<entry key-ref="elasticsearchColumnHandler" value-ref="elasticsearchColumnSyncComparator"/>
			</map>
		</property>
	</bean>

	<bean id="elasticsearchColumnConstructor" class="com.code.metaservice.res.sync.SyncResultConstructor" scope="prototype">
		<property name="syncHandler" ref="elasticsearchColumnHandler"/>
	</bean>

	<bean id="elasticsearchCollectionConstructor" class="com.code.metaservice.res.sync.SyncResultConstructor" scope="prototype">
		<property name="syncHandler" ref="elasticsearchCollectionHandler"/>
		<property name="childSyncResultConstructor">
			<map>
				<entry key-ref="elasticsearchColumnHandler" value-ref="elasticsearchColumnConstructor"/>
			</map>
		</property>
		<property name="resProvider" ref="elasticsearchResProvider"/>
	</bean>

	<bean id="elasticsearchInstanceSyncComparator" class="com.code.metaservice.res.sync.ResSyncComparator" scope="prototype">
		<property name="syncHandler" ref="elasticsearchInstanceHandler"/>
		<property name="childrenSyncHandler">
			<map>
				<entry key-ref="elasticsearchCollectionHandler" value-ref="elasticsearchCollectionSyncComparator"/>
			</map>
		</property>
		<property name="resProvider" ref="elasticsearchResProvider"/>
	</bean>


	<!-- GBASE  同步组件配置      START  -->
	<bean id="gbaseResProvider" class="com.code.metaservice.res.structured.rdb.sync.gbase.GbaseResProviderFactory" scope="prototype"/>
	<bean id="gbaseSchemaHandler" class="com.code.metaservice.res.structured.rdb.sync.gbase.GbaseSchemaHandler" scope="prototype"/>
	<bean id="gbaseTableHandler" class="com.code.metaservice.res.structured.rdb.sync.gbase.GbaseTableHandler" scope="prototype"/>
	<bean id="gbaseViewHandler" class="com.code.metaservice.res.structured.rdb.sync.gbase.GbaseViewHandler" scope="prototype"/>
	<bean id="gbaseColumnHandler" class="com.code.metaservice.res.structured.rdb.sync.gbase.GbaseColumnHandler" scope="prototype"/>
	<bean id="gbaseUniqueKeyHandler" class="com.code.metaservice.res.structured.rdb.sync.gbase.GbaseRdbUniqueKeyHandler" scope="prototype"/>

	<bean id="gbaseUniqueKeySyncComparator" class="com.code.metaservice.res.sync.ResSyncComparator" scope="prototype">
		<property name="syncHandler" ref="gbaseUniqueKeyHandler" />
	</bean>
	<bean id="gbaseColumnSyncComparator" class="com.code.metaservice.res.sync.ResSyncComparator" scope="prototype">
		<property name="syncHandler" ref="gbaseColumnHandler" />
	</bean>
	<bean id="gbaseTableSyncComparator" class="com.code.metaservice.res.sync.ResSyncComparator" scope="prototype">
		<property name="syncHandler" ref="gbaseTableHandler" />
		<property name="childrenSyncHandler">
			<map>
				<entry key-ref="gbaseColumnHandler" value-ref="gbaseColumnSyncComparator" />
				<entry key-ref="gbaseUniqueKeyHandler" value-ref="gbaseUniqueKeySyncComparator" />
			</map>
		</property>
	</bean>
	<bean id="gbaseViewSyncComparator" class="com.code.metaservice.res.sync.ResSyncComparator" scope="prototype">
		<property name="syncHandler" ref="gbaseViewHandler" />
		<property name="childrenSyncHandler">
			<map>
				<entry key-ref="gbaseColumnHandler" value-ref="gbaseColumnSyncComparator" />
			</map>
		</property>
	</bean>

	<bean id="gbaseSchemaSyncComparator" class="com.code.metaservice.res.sync.ResSyncComparator" scope="prototype">
		<property name="syncHandler" ref="gbaseSchemaHandler" />
		<property name="childrenSyncHandler">
			<map>
				<entry key-ref="gbaseTableHandler" value-ref="gbaseTableSyncComparator" />
				<entry key-ref="gbaseViewHandler" value-ref="gbaseViewSyncComparator" />
			</map>
		</property>
		<property name="resProvider" ref="gbaseResProvider"/>
	</bean>

	<bean id="gbaseColumnConstructor" class="com.code.metaservice.res.sync.SyncResultConstructor" scope="prototype">
		<property name="syncHandler" ref="gbaseColumnHandler"/>
	</bean>
	<bean id="gbaseUniqueKeyConstructor" class="com.code.metaservice.res.sync.SyncResultConstructor" scope="prototype">
		<property name="syncHandler" ref="gbaseUniqueKeyHandler"/>
	</bean>
	<bean id="gbaseTableConstructor" class="com.code.metaservice.res.sync.SyncResultConstructor" scope="prototype">
		<property name="syncHandler" ref="gbaseTableHandler"/>
		<property name="childSyncResultConstructor">
			<map>
				<entry key-ref="gbaseColumnHandler" value-ref="gbaseColumnConstructor"/>
				<entry key-ref="gbaseUniqueKeyHandler" value-ref="gbaseUniqueKeyConstructor"/>
			</map>
		</property>
		<property name="resProvider" ref="gbaseResProvider"/>
	</bean>
	<!-- GBASE  同步组件配置      END  -->

	<!-- ＧＲＥＥＮＰＬＵＭ  同步组件配置      START  -->
	<bean id="greenplumResProvider" class="com.code.metaservice.res.structured.rdb.sync.greenplum.GreenplumResProviderFactroy" scope="prototype"/>
	<!--互删 否则你会遇到无法理解的错误-->
	<bean id="greenplumResProviderFactroy" class="com.code.metaservice.res.structured.rdb.sync.greenplum.GreenplumResProviderFactroy" scope="prototype"/>
	<bean id="greenplumSchemaHandler" class="com.code.metaservice.res.structured.rdb.sync.greenplum.GreenplumSchemaHandler" scope="prototype"/>
	<bean id="greenplumTableHandler" class="com.code.metaservice.res.structured.rdb.sync.greenplum.GreenplumTableHandler" scope="prototype"/>
	<bean id="greenplumViewHandler" class="com.code.metaservice.res.structured.rdb.sync.greenplum.GreenplumViewHandler" scope="prototype"/>
	<bean id="greenplumColumnHandler" class="com.code.metaservice.res.structured.rdb.sync.greenplum.GreenplumColumnHandler"  scope="prototype"/>
	<bean id="greenplumUniqueKeyHandler" class="com.code.metaservice.res.structured.rdb.sync.greenplum.GreenplumRdbUniqueKeyHandler" scope="prototype"/>
	<!--<bean id="greenplumPartitionHandler" class="com.code.metaservice.res.structured.rdb.sync.greenplum.GreenplumPartitionHandler" scope="prototype"/>-->

	<bean id="greenplumColumnSyncComparator" class="com.code.metaservice.res.sync.ResSyncComparator" scope="prototype">
		<property name="syncHandler" ref="greenplumColumnHandler"/>
	</bean>
	<bean id="greenplumUniqueKeySyncComparator" class="com.code.metaservice.res.sync.ResSyncComparator" scope="prototype">
		<property name="syncHandler" ref="greenplumUniqueKeyHandler"/>
	</bean>
	<!--<bean id="greenplumPartitionSyncComparator" class="com.code.metaservice.res.sync.ResSyncComparator" scope="prototype">-->
	<!--<property name="syncHandler" ref="greenplumPartitionHandler"/>-->
	<!--</bean>-->
	<bean id="greenplumTableSyncComparator" class="com.code.metaservice.res.sync.ResSyncComparator" scope="prototype">
		<property name="syncHandler" ref="greenplumTableHandler"/>
		<property name="childrenSyncHandler">
			<map>
				<entry key-ref="greenplumColumnHandler" value-ref="greenplumColumnSyncComparator" />
				<entry key-ref="greenplumUniqueKeyHandler" value-ref="greenplumUniqueKeySyncComparator" />
				<!--<entry key-ref="greenplumPartitionHandler" value-ref="greenplumPartitionSyncComparator" />-->
			</map>
		</property>
	</bean>
	<bean id="greenplumViewSyncComparator" class="com.code.metaservice.res.sync.ResSyncComparator" scope="prototype">
		<property name="syncHandler" ref="greenplumViewHandler"/>
		<property name="childrenSyncHandler">
			<map>
				<entry key-ref="greenplumColumnHandler" value-ref="greenplumColumnSyncComparator" />
			</map>
		</property>
	</bean>
	<bean id="greenplumSchemaSyncComparator" class="com.code.metaservice.res.sync.ResSyncComparator" scope="prototype">
		<property name="syncHandler" ref="greenplumSchemaHandler" />
		<property name="childrenSyncHandler">
			<map>
				<entry key-ref="greenplumTableHandler" value-ref="greenplumTableSyncComparator" />
				<entry key-ref="greenplumViewHandler" value-ref="greenplumViewSyncComparator" />
			</map>
		</property>
	</bean>

	<bean id="greenplumColumnConstructor" class="com.code.metaservice.res.sync.SyncResultConstructor" scope="prototype">
		<property name="syncHandler" ref="greenplumColumnHandler"/>
	</bean>
	<bean id="greenplumUniqueKeyConstructor" class="com.code.metaservice.res.sync.SyncResultConstructor" scope="prototype">
		<property name="syncHandler" ref="greenplumUniqueKeyHandler"/>
	</bean>
	<!--<bean id="greenplumPartitionConstructor" class="com.code.metaservice.res.sync.SyncResultConstructor" scope="prototype">-->
	<!--<property name="syncHandler" ref="greenplumPartitionHandler"/>-->
	<!--</bean>-->
	<bean id="greenplumTableConstructor" class="com.code.metaservice.res.sync.SyncResultConstructor" scope="prototype">
		<property name="syncHandler" ref="greenplumTableHandler"/>
		<property name="childSyncResultConstructor">
			<map>
				<entry key-ref="greenplumColumnHandler" value-ref="greenplumColumnConstructor"/>
				<entry key-ref="greenplumUniqueKeyHandler" value-ref="greenplumUniqueKeyConstructor"/>
				<!--<entry key-ref="greenplumPartitionHandler" value-ref="greenplumPartitionConstructor"/>-->
			</map>
		</property>
	</bean>
	<!-- ＧＲＥＥＮＰＬＵＭ  同步组件配置      END  -->


	<!-- vertica 组件配置      START  -->
	<bean id="verticaResProvider" class="com.code.metaservice.res.structured.rdb.sync.vertica.VerticaResProviderFactroy" scope="prototype"/>
	<bean id="verticaResProviderFactroy" class="com.code.metaservice.res.structured.rdb.sync.vertica.VerticaResProviderFactroy" scope="prototype"/>
	<bean id="verticaSchemaHandler" class="com.code.metaservice.res.structured.rdb.sync.vertica.VerticaSchemaHandler" scope="prototype"/>
	<bean id="verticaTableHandler" class="com.code.metaservice.res.structured.rdb.sync.vertica.VerticaTableHandler" scope="prototype"/>
	<bean id="verticaViewHandler" class="com.code.metaservice.res.structured.rdb.sync.vertica.VerticaViewHandler" scope="prototype"/>
	<bean id="verticaColumnHandler" class="com.code.metaservice.res.structured.rdb.sync.vertica.VerticaColumnHandler"  scope="prototype"/>
	<bean id="verticaUniqueKeyHandler" class="com.code.metaservice.res.structured.rdb.sync.vertica.VerticaRdbUniqueKeyHandler" scope="prototype"/>

	<bean id="verticaColumnSyncComparator" class="com.code.metaservice.res.sync.ResSyncComparator" scope="prototype">
		<property name="syncHandler" ref="verticaColumnHandler"/>
	</bean>
	<bean id="verticaUniqueKeySyncComparator" class="com.code.metaservice.res.sync.ResSyncComparator" scope="prototype">
		<property name="syncHandler" ref="verticaUniqueKeyHandler"/>
	</bean>

	<bean id="verticaTableSyncComparator" class="com.code.metaservice.res.sync.ResSyncComparator" scope="prototype">
		<property name="syncHandler" ref="verticaTableHandler"/>
		<property name="childrenSyncHandler">
			<map>
				<entry key-ref="verticaColumnHandler"  value-ref="verticaColumnSyncComparator" />
				<entry key-ref="verticaUniqueKeyHandler" value-ref="verticaUniqueKeySyncComparator" />
				<!--<entry key-ref="greenplumPartitionHandler" value-ref="greenplumPartitionSyncComparator" />-->
			</map>
		</property>
	</bean>
	<bean id="verticaViewSyncComparator" class="com.code.metaservice.res.sync.ResSyncComparator" scope="prototype">
		<property name="syncHandler" ref="verticaViewHandler"/>
		<property name="childrenSyncHandler">
			<map>
				<entry key-ref="verticaColumnHandler" value-ref="verticaColumnSyncComparator" />
			</map>
		</property>
	</bean>
	<bean id="verticaSchemaSyncComparator" class="com.code.metaservice.res.sync.ResSyncComparator" scope="prototype">
		<property name="syncHandler" ref="verticaSchemaHandler" />
		<property name="childrenSyncHandler">
			<map>
				<entry key-ref="verticaTableHandler" value-ref="verticaTableSyncComparator" />
				<entry key-ref="verticaViewHandler" value-ref="verticaViewSyncComparator" />
			</map>
		</property>
	</bean>

	<bean id="verticaColumnConstructor" class="com.code.metaservice.res.sync.SyncResultConstructor" scope="prototype">
		<property name="syncHandler" ref="verticaColumnHandler"/>
	</bean>
	<bean id="verticaUniqueKeyConstructor" class="com.code.metaservice.res.sync.SyncResultConstructor" scope="prototype">
		<property name="syncHandler" ref="verticaUniqueKeyHandler"/>
	</bean>

	<bean id="verticaTableConstructor" class="com.code.metaservice.res.sync.SyncResultConstructor" scope="prototype">
		<property name="syncHandler" ref="verticaTableHandler"/>
		<property name="childSyncResultConstructor">
			<map>
				<entry key-ref="verticaColumnHandler" value-ref="verticaColumnConstructor"/>
				<entry key-ref="verticaUniqueKeyHandler" value-ref="verticaUniqueKeyConstructor"/>
				<!--<entry key-ref="greenplumPartitionHandler" value-ref="greenplumPartitionConstructor"/>-->
			</map>
		</property>
	</bean>
	<!-- vertica  同步组件配置      START  -->


	<!-- tBase  同步组件配置      START  -->
	<bean id="tBaseResProvider" class="com.code.metaservice.res.structured.rdb.sync.tbase.TBaseResProviderFactory" scope="prototype"/>
	<bean id="tBaseResProviderFactroy" class="com.code.metaservice.res.structured.rdb.sync.tbase.TBaseResProviderFactory" scope="prototype"/>
	<bean id="tBaseSchemaHandler" class="com.code.metaservice.res.structured.rdb.sync.tbase.TBaseSchemaHandler" scope="prototype"/>
	<bean id="tBaseTableHandler" class="com.code.metaservice.res.structured.rdb.sync.tbase.TBaseTableHandler" scope="prototype"/>
	<bean id="tBaseViewHandler" class="com.code.metaservice.res.structured.rdb.sync.tbase.TBaseViewHandler" scope="prototype"/>
	<bean id="tBaseColumnHandler" class="com.code.metaservice.res.structured.rdb.sync.tbase.TBaseColumnHandler"  scope="prototype"/>
	<bean id="tBaseUniqueKeyHandler" class="com.code.metaservice.res.structured.rdb.sync.tbase.TBaseRdbUniqueKeyHandler" scope="prototype"/>

	<bean id="tBaseColumnSyncComparator" class="com.code.metaservice.res.sync.ResSyncComparator" scope="prototype">
		<property name="syncHandler" ref="tBaseColumnHandler"/>
	</bean>
	<bean id="tBaseUniqueKeySyncComparator" class="com.code.metaservice.res.sync.ResSyncComparator" scope="prototype">
		<property name="syncHandler" ref="tBaseUniqueKeyHandler"/>
	</bean>

	<bean id="tBaseTableSyncComparator" class="com.code.metaservice.res.sync.ResSyncComparator" scope="prototype">
		<property name="syncHandler" ref="tBaseTableHandler"/>
		<property name="childrenSyncHandler">
			<map>
				<entry key-ref="tBaseColumnHandler" value-ref="tBaseColumnSyncComparator" />
				<entry key-ref="tBaseUniqueKeyHandler" value-ref="tBaseUniqueKeySyncComparator" />
				<!--<entry key-ref="greenplumPartitionHandler" value-ref="greenplumPartitionSyncComparator" />-->
			</map>
		</property>
	</bean>
	<bean id="tBaseViewSyncComparator" class="com.code.metaservice.res.sync.ResSyncComparator" scope="prototype">
		<property name="syncHandler" ref="tBaseViewHandler"/>
		<property name="childrenSyncHandler">
			<map>
				<entry key-ref="tBaseColumnHandler" value-ref="tBaseColumnSyncComparator" />
			</map>
		</property>
	</bean>
	<bean id="tBaseSchemaSyncComparator" class="com.code.metaservice.res.sync.ResSyncComparator" scope="prototype">
		<property name="syncHandler" ref="tBaseSchemaHandler" />
		<property name="childrenSyncHandler">
			<map>
				<entry key-ref="tBaseTableHandler" value-ref="tBaseTableSyncComparator" />
				<entry key-ref="tBaseViewHandler" value-ref="tBaseViewSyncComparator" />
			</map>
		</property>
	</bean>

	<bean id="tBaseColumnConstructor" class="com.code.metaservice.res.sync.SyncResultConstructor" scope="prototype">
		<property name="syncHandler" ref="tBaseColumnHandler"/>
	</bean>
	<bean id="tBaseUniqueKeyConstructor" class="com.code.metaservice.res.sync.SyncResultConstructor" scope="prototype">
		<property name="syncHandler" ref="tBaseUniqueKeyHandler"/>
	</bean>

	<bean id="tBaseTableConstructor" class="com.code.metaservice.res.sync.SyncResultConstructor" scope="prototype">
		<property name="syncHandler" ref="tBaseTableHandler"/>
		<property name="childSyncResultConstructor">
			<map>
				<entry key-ref="tBaseColumnHandler" value-ref="tBaseColumnConstructor"/>
				<entry key-ref="tBaseUniqueKeyHandler" value-ref="tBaseUniqueKeyConstructor"/>
				<!--<entry key-ref="greenplumPartitionHandler" value-ref="greenplumPartitionConstructor"/>-->
			</map>
		</property>
	</bean>
	<!-- tBase  同步组件配置      END  -->

	<!-- hbase sync config 勿删-->
	<bean id="globalCfg" class="com.code.metaservice.core.GlobalCfg">
		<property name="hadoopHome" value="D:\\bigdata\\hadoop-2.2.0"/>
	</bean>

</beans>
