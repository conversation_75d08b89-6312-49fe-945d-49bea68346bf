# 要素类型字段（人）
urlConfig.personStrList[0]=GMSFZH
urlConfig.personStrList[1]=GMSFZ
urlConfig.personStrList[2]=SFZH
urlConfig.personStrList[3]=SFZ
urlConfig.personStrList[4]=ZJHM
urlConfig.personStrList[5]=GMSFHM
urlConfig.personStrList[6]=证件号码
# 要素类型字段（车）
urlConfig.carStrList[0]=CPHM
urlConfig.carStrList[1]=CPH
urlConfig.carStrList[2]=车牌号
# 要素类型字段（案件）
urlConfig.ajStrList[0]=AJBH

# （人）
urlConfig.person[0].name = 超级档案
urlConfig.person[0].url = http://cjda.cd.dsj.hn/dossier/thirdLogin/zeroTrustSsoLogin?forwardUrl=%2Fperson%2F{value}&userToken={userToken}
urlConfig.person[0].mark = person
urlConfig.person[1].name = 超级布控
urlConfig.person[1].url = http://cjbk.cd.dsj.hn/ssoLogin?userToken={userToken}&type=rl&zjhm={value}&xm={value1}
urlConfig.person[1].mark = person

# （车）
urlConfig.car[0].name = 超级档案
urlConfig.car[0].url =  http://cjda.cd.dsj.hn/dossier/thirdLogin/zeroTrustSsoLogin?forwardUrl=%2Fcar%2F{value}&userToken={userToken}
urlConfig.car[0].mark = car
urlConfig.car[1].name = 超级布控
urlConfig.car[1].url = http://cjbk.cd.dsj.hn/ssoLogin?userToken={userToken}&type=cl&cph={value}
urlConfig.car[1].mark = car

# （案件）
urlConfig.aj[0].name = 超级档案
urlConfig.aj[0].url = http://cjda.cd.dsj.hn/dossier/thirdLogin/zeroTrustSsoLogin?forwardUrl=%2Fcase%2F{value}&userToken={userToken}
urlConfig.aj[0].mark = case

#模型发布类型配置
#urlConfig.mxscfxList[0] = HYFL_SJ
#urlConfig.mxscfxList[1] = YYLX_SJ
#模型发布类型配置
urlConfig.mxscfxList[0] = DXLX
urlConfig.mxscfxList[1] = YYLX
urlConfig.mxscfxList[2] = JZFL
urlConfig.mxscfxList[3] = AJLX
urlConfig.mxscfxList[4] = QYLX
urlConfig.mxscfxList[5] = DFGKSL