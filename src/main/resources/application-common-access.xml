<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
                            http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">
    <!-- 统一访问层xml配置-->

    <!--  dml bean  -->
    <bean name="dataSourceBuilder" class="com.code.meta.dml.core.DefDataSourceBuilder">
        <property name="registry" ref="pluginRegistry"/>
        <property name="metaDataService" ref="metaDataService"/>
        <property name="beanFactory" ref="beanFactory"/>
        <property name="pluginDirectory" ref="pluginDirectory"/>
    </bean>
    <bean name="typeSystemCache" class="com.code.dragonsoft.dataquery.cache.TypeSystemCache"/>

    <!--  ddl bean  -->
    <bean name="ddlDataSourceBuilderStrategy" class="com.code.meta.ddl.core.DefDdlDataSourceBuilderStrategy">
        <property name="pluginRegistry" ref="pluginRegistry"/>
        <property name="resMetaDataService" ref="resMetaDataService"/>
        <property name="beanFactory" ref="beanFactory"/>
    </bean>

    <bean name="metaSyncHandler" class="com.code.common.mist.metadata.sync.impl.SyncHandler">
        <property name="syncResultBuild" ref="syncresultBuild"/>
    </bean>

    <bean name="syncresultBuild" class="com.code.common.mist.metadata.sync.SyncResultBuild">
        <property name="beanFactory" ref="beanFactory"/>
        <property name="pluginRegistry" ref="pluginRegistry"/>
    </bean>

<!--    <context:component-scan base-package="com.code.common.mist.metadata.sync"-->
<!--                            scope-resolver="com.fw.spring.sr.SingletonResolver"-->
<!--                            name-generator="com.fw.spring.ng.ServiceNameGenerator"-->
<!--                            use-default-filters="false">-->
<!--        <context:include-filter type="annotation" expression="com.fw.service.annotation.Service"/>-->
<!--    </context:component-scan>-->

</beans>