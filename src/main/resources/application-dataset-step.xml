<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
                     http://www.springframework.org/schema/beans/spring-beans-3.2.xsd
                     http://www.springframework.org/schema/context
                     http://www.springframework.org/schema/context/spring-context-3.2.xsd"
       default-autowire="byName">

    <bean id="logicDataStepRegister" class="com.code.common.LogicDataStepRegister">
        <property name="packName" value="com.code.dataset.operator"/>
    </bean>

    <bean id="timeClicker" class="com.dragonsoft.cicada.datacenter.modules.datavisual.widget.TimerClicker">
        <property name="visSecond" value="10"/>
    </bean>
</beans>