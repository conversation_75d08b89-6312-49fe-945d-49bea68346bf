<html>
<style>
    table {
      border: 1px solid black;
      border-collapse: collapse;
      background-color: #f0f0f0;
    }
    td,
    th {
      border: 1px solid black;
      text-align: left;
      padding-left: 12px;
      padding-right: 12px;
      vertical-align: middle;
    }
  </style>
<body>
<h1 style="text-align:center"> 超级魔方算子说明文档</h1>
<#list treeList as node>
    <h2>${node.name}</h2>
    <#if node.children?has_content>
        <#list node.children as child>
        <h3>${child.name}</h3>
        <#assign obj = dataMap[child.id]>
        <div>
            <div>
                <label>说明:</label>
                <label>${obj.memo?replace("\r\n","<br>&nbsp;&nbsp;")}</label>
            </div>
            <div>
                <label>表达式:</label>
                <label>${obj.expression}</label>
            </div>
            <div>
                <label>返回值类型:</label>
                <label>${obj.returnType}</label>
            </div>
            <div>
                <label> 示例:</label>
                <label>${obj.example?replace("\r\n","<br>")}</label>
            </div>
            <#if obj.udfParameterVos?has_content>
            <div>
                <label>参数列表:</label>
                <table>
                    <thead>
                    <tr>
                        <td>名称</td>
                        <td>代码</td>
                        <td>数据类型</td>
                    </tr>
                    </thead>
                    <tbody>
                    <#list obj.udfParameterVos as udfParam >
                    <tr>
                        <td>${udfParam.name}</td>
                        <td>${udfParam.code}</td>
                        <td>${udfParam.dataType}</td>
                    </tr>
                    </#list>
                    </tbody>
                </table>
            </div>
            </#if>
        </div>
        </#list>
    </#if>
</#list>
</body>
</html>