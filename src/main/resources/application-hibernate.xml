<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:aop="http://www.springframework.org/schema/fw-aop"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
                     http://www.springframework.org/schema/beans/spring-beans-3.2.xsd
                     http://www.springframework.org/schema/context
                     http://www.springframework.org/schema/context/spring-context-3.2.xsd
                     http://www.springframework.org/schema/fw-aop
                     http://www.springframework.org/schema/fw-aop/spring-fw-aop-3.2.xsd
                     http://www.springframework.org/schema/tx
                     http://www.springframework.org/schema/tx/spring-tx-3.2.xsd"
       default-autowire="byName">

    <description>Spring JPA 全局配置</description>


    <bean class="com.code.metaservice.util.LogicDataObjectUtil" id="logicDataObjectUtil"></bean>

    <!--插件相关的service-->
    <context:component-scan base-package="com.code.etl.plugin"
                            scope-resolver="com.fw.spring.sr.SingletonResolver"
                            name-generator="com.fw.spring.ng.ServiceNameGenerator"
                            use-default-filters="false">
        <context:include-filter type="annotation" expression="com.fw.service.annotation.Service"/>
        <context:exclude-filter type="regex"
                                expression="com.code.common.mist.plugin.config.service.impl.BasePluginService"/>
    </context:component-scan>

    <context:component-scan base-package="com.code.dragonsoft.dataquery"/>

    <bean id="dataSource" class="org.apache.commons.dbcp2.BasicDataSource" destroy-method="close" primary="true">
        <property name="username" value="${jdbc.datasource.username}"></property>
        <property name="password" value="${jdbc.datasource.password}"></property>
        <property name="url" value="${jdbc.datasource.url}"></property>
        <property name="driverClassName" value="${jdbc.datasource.driverClassName}"></property>
        <property name="maxTotal" value="${jdbc.datasource.max-total}"></property>
        <property name="maxIdle" value="${jdbc.datasource.max-idle}"></property>

    </bean>
    <bean id="queryDataService" class="com.code.dragonsoft.dataquery.service.QueryServiceDataServiceImpl"/>
    <bean id="rdbQueryService" class="com.code.dragonsoft.dataquery.service.RdbQueryService"/>
    <bean id="elasticsQueryService" class="com.code.dragonsoft.dataquery.service.ElasticsQueryService"/>
    <bean id="hbaseQueryService" class="com.code.dragonsoft.dataquery.service.HbaseQueryService"/>
    <bean id="ddlOperationService" class="com.code.dragonsoft.dataquery.service.DDLOperationServiceImpl"/>
    <bean id="typeMappingService" class="com.code.dragonsoft.dataquery.service.TypeMappingServiceImpl"/>
    <bean id="labelService" class="com.code.thirdplugin.service.impl.LabelPluginServiceImpl"/>

    <bean id="mlsqlService" class="com.dragonsoft.cicada.datacenter.modules.modeling.service.impl.MLSQLServiceImpl"/>

    <context:component-scan base-package="com.code.dataset.operator"
                            scope-resolver="com.fw.spring.sr.PrototypeResolver"
                            name-generator="com.fw.spring.ng.ServiceNameGenerator"
                            use-default-filters="false">
        <context:include-filter type="annotation" expression="com.fw.service.annotation.Service"/>
    </context:component-scan>
    <context:component-scan base-package="com.code.common.dataset"
                            scope-resolver="com.fw.spring.sr.SingletonResolver"
                            name-generator="com.fw.spring.ng.ServiceNameGenerator"
                            use-default-filters="false">
        <context:include-filter type="annotation" expression="com.fw.service.annotation.Service"/>
    </context:component-scan>
    <context:component-scan base-package="com.dragonsoft.cicada.datacenter.modules"
                            scope-resolver="com.fw.spring.sr.SingletonResolver"
                            name-generator="com.fw.spring.ng.ServiceNameGenerator"
                            use-default-filters="false">
        <context:include-filter type="annotation" expression="com.fw.service.annotation.Service"/>
    </context:component-scan>

    <context:component-scan base-package="com.code.mist.builder.service"
                            scope-resolver="com.fw.spring.sr.SingletonResolver"
                            name-generator="com.fw.spring.ng.ServiceNameGenerator"
                            use-default-filters="false">
        <context:include-filter type="annotation" expression="com.fw.service.annotation.Service"/>
        <context:include-filter type="annotation" expression="org.springframework.stereotype.Service"/>
    </context:component-scan>

    <!--    <context:component-scan base-package="com.code.mist.schedule.service"
                                scope-resolver="com.fw.spring.sr.SingletonResolver"
                                name-generator="com.fw.spring.ng.ServiceNameGenerator"
                                use-default-filters="false">
            <context:include-filter type="annotation" expression="com.fw.service.annotation.Service"/>
            <context:include-filter type="annotation" expression="org.springframework.stereotype.Service"/>
        </context:component-scan>-->

    <context:component-scan base-package="com.code.metaservice"
                            scope-resolver="com.fw.spring.sr.SingletonResolver"
                            name-generator="com.fw.spring.ng.ServiceNameGenerator"
                            use-default-filters="false">
        <context:include-filter type="annotation" expression="com.fw.service.annotation.Service"/>
        <context:include-filter type="annotation" expression="org.springframework.stereotype.Service"/>
    </context:component-scan>

    <!-- 添加对Spring Service注解支持 -->
    <context:component-scan base-package="com.code.thirdplugin"
                            scope-resolver="com.fw.spring.sr.SingletonResolver"
                            name-generator="com.fw.spring.ng.ServiceNameGenerator"
                            use-default-filters="false">
        <context:include-filter type="annotation" expression="com.fw.service.annotation.Service"/>
        <context:include-filter type="annotation" expression="com.fw.service.annotation.Bean"/>
        <context:include-filter type="annotation" expression="org.springframework.stereotype.Service"/>
    </context:component-scan>

    <context:component-scan base-package="com.code.cicada.thirdplugin"
                            scope-resolver="com.fw.spring.sr.SingletonResolver"
                            name-generator="com.fw.spring.ng.ServiceNameGenerator"
                            use-default-filters="false">
        <context:include-filter type="annotation" expression="com.fw.service.annotation.Service"/>
        <context:include-filter type="annotation" expression="com.fw.service.annotation.Bean"/>
        <context:include-filter type="annotation" expression="org.springframework.stereotype.Service"/>
    </context:component-scan>

    <context:component-scan base-package="com.code.metadata"
                            scope-resolver="com.fw.spring.sr.SingletonResolver"
                            name-generator="com.fw.spring.ng.ServiceNameGenerator"
                            use-default-filters="false">
        <context:include-filter type="annotation" expression="com.fw.service.annotation.Service"/>
    </context:component-scan>

    <context:component-scan base-package="com.fw.admin.service"
                            scope-resolver="com.fw.spring.sr.SingletonResolver"
                            name-generator="com.fw.spring.ng.ServiceNameGenerator"
                            use-default-filters="false">
        <context:include-filter type="annotation" expression="com.fw.service.annotation.Service"/>
    </context:component-scan>

    <context:component-scan base-package="com.fw.extend.service"
                            scope-resolver="com.fw.spring.sr.SingletonResolver"
                            name-generator="com.fw.spring.ng.ServiceNameGenerator"
                            use-default-filters="false">
        <context:include-filter type="annotation" expression="com.fw.service.annotation.Service"/>
    </context:component-scan>

    <context:component-scan base-package="com.code.cicadas.datacenter"/>
    <context:component-scan base-package="com.code.cicadas.datacenter"
                            scope-resolver="com.fw.spring.sr.SingletonResolver"
                            name-generator="com.fw.spring.ng.ServiceNameGenerator"
                            use-default-filters="false">
        <context:include-filter type="annotation" expression="com.fw.service.annotation.Service"/>
    </context:component-scan>

    <context:component-scan base-package="com.fw.tenon.business"
                            scope-resolver="com.fw.spring.sr.SingletonResolver"
                            name-generator="com.fw.spring.ng.ServiceNameGenerator"
                            use-default-filters="false">
        <context:include-filter type="annotation" expression="com.fw.service.annotation.Service"/>
        <context:include-filter type="annotation" expression="com.fw.service.annotation.Bean"/>
        <context:include-filter type="annotation" expression="org.springframework.stereotype.Service"/>
    </context:component-scan>


    <bean id="baseDao" class="com.fw.dao.hbmimpl.BaseDaoImpl">
        <property name="sessionFactory" ref="sessionFactory"/>
        <property name="sqlSysData" value="select to_char(now(),'YYYYMMDD') SYS_TIME"/>
        <property name="sqlSysTime" value="select to_char(now(),'YYYYMMDDHH24MISS') as SYS_TIME"/>
    </bean>

    <bean id="baseService" class="com.fw.service.BaseService">
        <property name="baseDao" ref="baseDao"/>
    </bean>

    <bean id="sessionFactory"
          primary="true"
          class="com.code.metadata.spring.hibernate4.PostgreSQLLocalSessionFactoryBean">
        <property name="dataSource" ref="dataSource"/>
        <property name="hibernateProperties">
            <props>
                <prop key="hibernate.dialect">org.hibernate.dialect.PostgreSQL82Dialect</prop>
                <prop key="hibernate.show_sql">false</prop>
                <prop key="hibernate.format_sql">true</prop>
                <prop key="hibernate.bytecode.use_reflection_optimizer">true</prop>
                <!-- 很重要hibernate5，不然会报：no transaction is in progress 错误  -->
<!--                <prop key="hibernate.allow_update_outside_transaction">true</prop>-->
            </props>
        </property>

        <property name="mappingLocations">
            <list>
                <value>classpath*:/com/code/metadata/base/**/*.hbm.xml</value>
                <value>classpath*:/com/code/metadata/standard/*.hbm.xml</value>
                <value>classpath*:/com/code/metadata/res/**/*.hbm.xml</value>
                <value>classpath*:/com/code/metadata/model/core/*.hbm.xml</value>
                <value>classpath*:/com/code/metadata/etl/**/*.hbm.xml</value>
                <value>classpath*:/com/code/metadata/business/**/*.hbm.xml</value>
                <value>classpath*:/com/code/metadata/label/*.hbm.xml</value>
                <!--                <value>classpath*:/com/code/common/sch/model/*.hbm.xml</value>-->
                <value>classpath*:/com/code/metadata/datawarehouse/*.hbm.xml</value>
                <value>classpath*:/com/code/metadata/datavisual/*.hbm.xml</value>
                <value>classpath*:/com/code/metadata/sm/*.hbm.xml</value>
                <value>classpath*:/com/code/metadata/ruleengine/*.hbm.xml</value>
                <value>classpath*:/com/code/metadata/echarts/*.hbm.xml</value>
                <value>classpath*:/com/code/metadata/portal/*.hbm.xml</value>
                <value>classpath*:/com/code/metadata/python/task/*.hbm.xml</value>
                <value>classpath*:/com/code/metadata/bus/management/*.hbm.xml</value>
                <value>classpath*:/com/dragoninfo/dfw/entity/*.hbm.xml</value>
                <value>classpath*:/com/code/metadata/plugin/operate/*.hbm.xml</value>
                <value>classpath*:/com/code/metadata/service/datacenter/*.hbm.xml</value>
                <value>classpath:/com/code/metadata/udf/management/*.hbm.xml</value>
                <value>classpath:/com/code/metadata/logic/*.hbm.xml</value>
                <value>classpath:/com/code/metadata/variable/*.hbm.xml</value>
                <value>classpath:/com/code/metadata/scenario/*.hbm.xml</value>
                <value>classpath*:/com/code/metadata/modelsupermark/*.hbm.xml</value>
                <value>classpath*:/com/code/metadata/aimodel/*.hbm.xml</value>
                <value>classpath:/com/code/metadata/modelspace/*.hbm.xml</value>
                <value>classpath*:/com/code/metadata/ga/log/security/audit/platform/*.hbm.xml</value>
                <value>classpath*:/com/code/metadata/usecase/*.hbm.xml</value>
            </list>
        </property>

    </bean>

    <!-- 声明式事务管理 -->
    <bean id="transactionManager" class="org.springframework.orm.hibernate4.HibernateTransactionManager" primary="true">
        <property name="sessionFactory" ref="sessionFactory"/>
    </bean>

    <aop:aspectj-autoproxy expose-proxy="true"/>

    <tx:advice id="txAdvice">
        <tx:attributes>
            <tx:method name="_*" propagation="REQUIRES_NEW"/>
            <tx:method name="find*" read-only="true"/>
            <tx:method name="query*" read-only="true"/>
            <tx:method name="show*" read-only="true"/>
            <tx:method name="get*"/>
            <tx:method name="list*" read-only="true"/>
            <tx:method name="search*" read-only="true"/>
            <tx:method name="count*" read-only="true"/>
            <tx:method name="read*" read-only="true"/>
            <tx:method name="load*" read-only="true"/>
            <tx:method name="create*"/>
            <tx:method name="is*"/>
            <tx:method name="init*"/>
            <tx:method name="execute*"/>
            <tx:method name="save*"/>
            <tx:method name="insert*"/>
            <tx:method name="del*"/>
            <tx:method name="update*"/>
            <tx:method name="build*"/>
            <tx:method name="edit*"/>
            <tx:method name="clean*"/>
            <tx:method name="process*"/>
            <tx:method name="remove*"/>
            <tx:method name="add*"/>
            <tx:method name="cancel*"/>
            <tx:method name="change*"/>
            <tx:method name="move*"/>
            <tx:method name="*"/>
            <tx:method name="accreditDataSet" propagation="REQUIRES_NEW"/>
            <tx:method name="offlineServiceByServiceMetaIdComitSession" propagation="REQUIRES_NEW"/>
            <tx:method name="saveLogicDataObjRelation" propagation="REQUIRES_NEW"/>
        </tx:attributes>
    </tx:advice>
    <aop:config expose-proxy="true" proxy-target-class="true">
        <!--  只对业务逻辑层实施事务 -->

        <aop:pointcut id="txPointcut" expression="execution(* com.dragonsoft..*.service..*+.*(..))
    || execution(* com.code.metaservice..*.*(..))
    ||execution(* com.dragonsoft..*.service..*+.*(..))
    ||execution(* com.dragonsoft.cicada.datacenter.system..*+.*(..))
    ||bean(*Service)
    ||bean(*ServiceImpl)
     ||bean(*service*)
    "/>
        <aop:advisor id="txAdvisor" advice-ref="txAdvice" pointcut-ref="txPointcut"/>
    </aop:config>

    <!-- 定义默认事务传递机制 -->
    <tx:advice id="scheduleTXAdvice" transaction-manager="transactionManager">
        <tx:attributes>
            <tx:method name="get*" propagation="REQUIRED"/>
            <tx:method name="find*" propagation="REQUIRED"/>
            <tx:method name="query*" propagation="REQUIRED"/>
            <tx:method name="read*" propagation="REQUIRED"/>
            <tx:method name="*" propagation="REQUIRED"/>
            <tx:method name="execute" propagation="REQUIRES_NEW"/>
            <tx:method name="accreditDataSet" propagation="REQUIRES_NEW"/>
            <tx:method name="sendAlarmMessage" propagation="REQUIRES_NEW"/>
            <tx:method name="createJobMeta" propagation="REQUIRES_NEW"/>
            <tx:method name="getJobMetaByJobCode" propagation="REQUIRES_NEW"/>
        </tx:attributes>
    </tx:advice>

    <!-- 所有*Bean结尾的bean注解的类AOP默认事务传递机制 -->
    <aop:config proxy-target-class="true">
        <aop:pointcut id="ScheduleTx"
                      expression="bean(jobMetaConfiger) "/>
        <aop:advisor advice-ref="scheduleTXAdvice"
                     pointcut-ref="ScheduleTx"/>
    </aop:config>

    <bean id="springBeanFactory" class="com.fw.spring.SpringBeanFactory"/>


    <context:component-scan base-package="com.code.common.mist.metadata.sync"
                            scope-resolver="com.fw.spring.sr.SingletonResolver"
                            name-generator="com.fw.spring.ng.ServiceNameGenerator"
                            use-default-filters="false">
        <context:include-filter type="annotation" expression="com.fw.service.annotation.Service"/>
    </context:component-scan>

    <bean id="jobMetaConfiger" class="com.code.common.schedulectr.scheduleclient.schemeconfig.DefJobMetaConfiger">
        <property name="baseDao" ref="baseDao"/>
    </bean>

    <!--
        <bean id="selfCheckTask" class="com.code.mist.schedule.timer.SelfCheckTask"/>

        <bean id="appMistClientFactory" class="com.code.mist.schedule.zkutil.AppMistClientFactory" init-method="init">
            <property name="etlInstanceMetaService" ref="etlInstanceMetaService"/>
            <property name="selfCheckTask" ref="selfCheckTask" />
        </bean>

        <bean id="scheduleEngineControler"
              class="com.code.mist.schedule.controler.MistClientScheduleEngineControler" init-method="init">
            <property name="etlClusterMetaService" ref="etlClusterMetaService"/>
            <property name="etlInstanceMetaService" ref="etlInstanceMetaService" />
            <property name="selfCheckTask" ref="selfCheckTask" />
        </bean>


        &lt;!&ndash; 定义默认事务传递机制 &ndash;&gt;
        <tx:advice id="scheduleTXAdvice" transaction-manager="transactionManager">
            <tx:attributes>
                <tx:method name="insert*" propagation="REQUIRED" />
                <tx:method name="save*" propagation="REQUIRED" />
                <tx:method name="execute" propagation="REQUIRES_NEW"/>
                <tx:method name="sendAlarmMessage" propagation="REQUIRES_NEW"/>
                <tx:method name="createJobMeta" propagation="REQUIRES_NEW"/>
                <tx:method name="updateJobMeta" propagation="REQUIRES_NEW"/>
                <tx:method name="getJobMetaByJobCode" propagation="REQUIRES_NEW"/>
            </tx:attributes>
        </tx:advice>

        &lt;!&ndash; 所有*Bean结尾的bean注解的类AOP默认事务传递机制 &ndash;&gt;
        <aop:config proxy-target-class="true">
            <aop:pointcut id="ScheduleTx"
                          expression="bean(jobMetaConfiger) "/>
            <aop:advisor advice-ref="scheduleTXAdvice"
                         pointcut-ref="ScheduleTx"/>
        </aop:config>
    -->


</beans>