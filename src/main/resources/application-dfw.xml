<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
                     http://www.springframework.org/schema/beans/spring-beans-3.2.xsd
                     http://www.springframework.org/schema/context
                     http://www.springframework.org/schema/context/spring-context-3.2.xsd"
       default-autowire="byName">

    <context:annotation-config/>

    <bean id="dfwDataSource" class="org.apache.commons.dbcp2.BasicDataSource" destroy-method="close">
        <property name="username" value="${jdbc.datasource.username}"></property>
        <property name="password" value="${jdbc.datasource.password}"></property>
        <property name="url" value="${jdbc.datasource.url}"></property>
        <property name="driverClassName" value="${jdbc.datasource.driverClassName}"></property>
        <!--<property name="maxActive" value="${spring.datasource.max-active}" ></property>-->
        <property name="maxIdle" value="${jdbc.datasource.max-idle}"></property>

    </bean>

    <context:component-scan base-package="com.dragoninfo.dfw"/>

    <context:component-scan base-package="com.dragoninfo.dfw.service"
                            scope-resolver="com.fw.spring.sr.SingletonResolver"
                            name-generator="com.fw.spring.ng.ServiceNameGenerator"
                            use-default-filters="false">
        <context:include-filter type="annotation" expression="com.fw.service.annotation.Service"/>
    </context:component-scan>

    <context:component-scan base-package="com.dragoninfo.dfw.entity"
                            scope-resolver="com.fw.spring.sr.DynamicResolver"
                            name-generator="com.fw.spring.ng.BeanNameGenerator"
                            use-default-filters="false">
        <context:include-filter type="annotation" expression="com.fw.service.annotation.Bean"/>
    </context:component-scan>

    <!--    <context:component-scan base-package="com.fw.extend.service"
                                scope-resolver="com.fw.spring.sr.SingletonResolver"
                                name-generator="com.fw.spring.ng.ServiceNameGenerator"
                                use-default-filters="false">
            <context:include-filter type="annotation" expression="com.fw.service.annotation.Service"/>
        </context:component-scan>-->

    <bean id="dfwBaseDao" class="com.fw.dao.hbmimpl.DfwBaseDaoImpl">
        <property name="dfwSessionFactory" ref="dfwSessionFactory"/>
        <property name="sqlSysData" value="select to_char(now(),'YYYYMMDD') SYS_TIME"/>
        <property name="sqlSysTime" value="select to_char(now(),'YYYYMMDDHH24MISS') as SYS_TIME"/>
    </bean>

    <bean id="dfwBaseService" class="com.fw.service.DfwBaseService">
        <property name="dfwBaseDao" ref="dfwBaseDao"/>
    </bean>

    <bean id="dfwJdbcTemplate" class="org.springframework.jdbc.core.JdbcTemplate">
        <property name="dataSource" ref="dataSource"/>
    </bean>

    <bean id="dfwSessionFactory"
          class="com.code.metadata.spring.hibernate4.PostgreSQLLocalSessionFactoryBean"
          >
        <property name="dataSource" ref="dfwDataSource"/>
        <property name="hibernateProperties">
            <props>
                <prop key="hibernate.dialect">org.hibernate.dialect.PostgreSQL82Dialect</prop>
                <prop key="hibernate.show_sql">false</prop>
                <prop key="hibernate.format_sql">true</prop>
                <prop key="hibernate.bytecode.use_reflection_optimizer">true</prop>
                <!-- 很重要hibernate5，不然会报：no transaction is in progress 错误  -->
<!--                <prop key="hibernate.allow_update_outside_transaction">true</prop>-->
            </props>
        </property>

        <property name="mappingLocations">
            <list>
                <value>classpath*:/com/dragoninfo/dfw/entity/*.hbm.xml</value>
                <value>classpath*:/com/dragoninfo/dfw/entity/TSysExceptionLog.hbm.xml</value>
            </list>
        </property>

    </bean>

</beans>